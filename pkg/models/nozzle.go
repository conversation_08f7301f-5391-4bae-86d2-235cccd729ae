package models

import (
	"encoding/json"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

// NozzleStatus 喷嘴状态枚举 - 对应Wayne DART协议状态
type NozzleStatus string

const (
	NozzleStatusIdle        NozzleStatus = "idle"        // 空闲状态 - 对应pump.StatusIdle
	NozzleStatusSelected    NozzleStatus = "selected"    // 已选择 - DC3事务中选择状态
	NozzleStatusAuthorized  NozzleStatus = "authorized"  // 已授权 - 对应pump.StatusAuthorized
	NozzleStatusOut         NozzleStatus = "out"         // 油枪拔出 - DC3 NOZIO=0x1X
	NozzleStatusFilling     NozzleStatus = "filling"     // 加油中 - DC2事务活跃状态
	NozzleStatusCompleted   NozzleStatus = "completed"   // 加油完成 - 对应pump.StatusFillingCompleted
	NozzleStatusSuspended   NozzleStatus = "suspended"   // 暂停状态 - 对应pump.StatusSuspended
	NozzleStatusError       NozzleStatus = "error"       // 错误状态
	NozzleStatusMaintenance NozzleStatus = "maintenance" // 维护状态
)

// PriceStatus 价格同步状态枚举
type PriceStatus string

const (
	PriceStatusSynced  PriceStatus = "synced"  // 已同步：设备价格与目标价格一致
	PriceStatusPending PriceStatus = "pending" // 待同步：有新的目标价格等待下发
	PriceStatusSending PriceStatus = "sending" // 发送中：正在向设备发送价格
	PriceStatusFailed  PriceStatus = "failed"  // 失败：价格下发失败
	PriceStatusTimeout PriceStatus = "timeout" // 超时：价格下发超时
)

// FuelGrade 油品等级实体
type FuelGrade struct {
	// 基础标识信息
	ID          string `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	Name        string `json:"name" db:"name" gorm:"type:varchar(255);not null"`
	Type        string `json:"type" db:"type" gorm:"type:varchar(100);not null"` // gasoline, diesel, etc.
	Octane      int    `json:"octane" db:"octane" gorm:"default:0"`
	Description string `json:"description" db:"description" gorm:"type:text"`
	Color       string `json:"color" db:"color" gorm:"type:varchar(50)"`

	// 价格信息
	Price    decimal.Decimal `json:"price" db:"price" gorm:"type:decimal(8,4);not null;default:0;check:price >= 0"`
	Currency string          `json:"currency" db:"currency" gorm:"type:varchar(10);default:'CNY'"`

	// 元数据 - 修复：使用datatypes.JSON解决序列化问题
	Metadata  datatypes.JSON `json:"metadata" db:"metadata" gorm:"type:jsonb"`
	CreatedAt time.Time      `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" db:"updated_at" gorm:"autoUpdateTime"`

	// 软删除支持 - 用于油品同步功能
	DeletedAt *time.Time `json:"deleted_at,omitempty" db:"deleted_at" gorm:"index"`

	Version int `json:"version" db:"version" gorm:"default:1"`
}

// Nozzle 喷嘴实体 - 对应Wayne DART协议的Logical Nozzle
type Nozzle struct {
	// 基础标识信息
	ID       string `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	Number   byte   `json:"number" db:"number" gorm:"not null;check:number >= 1 AND number <= 15"` // Wayne协议喷嘴编号1-15
	Name     string `json:"name" db:"name" gorm:"type:varchar(255)"`
	DeviceID string `json:"device_id" db:"device_id" gorm:"type:varchar(255);not null;index"`

	// Wayne DART协议状态映射
	Status     NozzleStatus `json:"status" db:"status" gorm:"type:varchar(50);not null;default:'idle';check:status IN ('idle', 'selected', 'authorized', 'out', 'filling', 'completed', 'suspended', 'error', 'maintenance')"`
	IsOut      bool         `json:"is_out" db:"is_out" gorm:"default:false"`           // DC3 NOZIO bit 4 - 喷嘴拔出状态
	IsSelected bool         `json:"is_selected" db:"is_selected" gorm:"default:false"` // DC3 NOZIO bits 0-3 - 喷嘴选中状态

	// 油品和价格配置 (CD5事务支持)
	FuelGradeID  *string         `json:"fuel_grade_id" db:"fuel_grade_id" gorm:"type:varchar(255);index"`
	FuelGrade    *FuelGrade      `json:"fuel_grade,omitempty" gorm:"foreignKey:FuelGradeID"`
	CurrentPrice decimal.Decimal `json:"current_price" db:"current_price" gorm:"type:decimal(8,4);default:0;check:current_price >= 0"` // 当前单价，用于CD5价格更新命令

	// 新增：价格同步追踪字段
	TargetPrice      *decimal.Decimal `json:"target_price,omitempty" db:"target_price" gorm:"type:decimal(8,4)"`                // 目标价格（待调价）
	LastSentPrice    *decimal.Decimal `json:"last_sent_price,omitempty" db:"last_sent_price" gorm:"type:decimal(8,4)"`          // 上次发送的价格
	PriceStatus      PriceStatus      `json:"price_status" db:"price_status" gorm:"type:varchar(20);not null;default:'synced'"` // 价格同步状态
	PriceUpdatedAt   *time.Time       `json:"price_updated_at,omitempty" db:"price_updated_at"`                                 // 价格更新时间
	PriceSentAt      *time.Time       `json:"price_sent_at,omitempty" db:"price_sent_at"`                                       // 价格发送时间
	PriceConfirmedAt *time.Time       `json:"price_confirmed_at,omitempty" db:"price_confirmed_at"`                             // 价格确认时间

	// Wayne DART交易数据 (DC2事务) - 修复：金额字段改为3位小数
	CurrentVolume decimal.Decimal `json:"current_volume" db:"current_volume" gorm:"type:decimal(10,3);default:0;check:current_volume >= 0"` // 当前体积
	CurrentAmount decimal.Decimal `json:"current_amount" db:"current_amount" gorm:"type:decimal(10,3);default:0;check:current_amount >= 0"` // 当前金额

	// 预设值支持 (Wayne CD3/CD4事务) - 修复：金额字段改为3位小数
	PresetVolume *decimal.Decimal `json:"preset_volume,omitempty" db:"preset_volume" gorm:"type:decimal(10,3);check:preset_volume IS NULL OR preset_volume >= 0"`
	PresetAmount *decimal.Decimal `json:"preset_amount,omitempty" db:"preset_amount" gorm:"type:decimal(10,3);check:preset_amount IS NULL OR preset_amount >= 0"`

	// 🆕 预授权追踪字段
	PreAuthType      *string          `json:"preauth_type,omitempty" gorm:"column:preauth_type;type:varchar(20)"`       // 预授权类型：preset_volume 或 preset_amount
	PreAuthNumber    *decimal.Decimal `json:"preauth_number,omitempty" gorm:"column:preauth_number;type:decimal(10,3)"` // 预授权数字（体积或金额）
	PreAuthCreatedAt *time.Time       `json:"preauth_created_at,omitempty" gorm:"column:preauth_created_at"`            // 预授权创建时间
	PreAuthExpiresAt *time.Time       `json:"preauth_expires_at,omitempty" gorm:"column:preauth_expires_at"`            // 预授权过期时间

	// 累计统计数据 - 修复：金额字段改为3位小数
	TotalVolume      decimal.Decimal `json:"total_volume" db:"total_volume" gorm:"type:decimal(12,3);default:0;check:total_volume >= 0"`
	TotalAmount      decimal.Decimal `json:"total_amount" db:"total_amount" gorm:"type:decimal(12,3);default:0;check:total_amount >= 0"`
	TransactionCount int64           `json:"transaction_count" db:"transaction_count" gorm:"default:0"`

	// 物理配置
	Position   string `json:"position" db:"position" gorm:"type:varchar(100)"` // 喷嘴位置描述
	HoseLength int    `json:"hose_length" db:"hose_length" gorm:"default:0"`   // 油管长度(厘米)
	IsEnabled  bool   `json:"is_enabled" db:"is_enabled" gorm:"default:true"`  // 是否启用

	// 元数据 - 修复：使用datatypes.JSON解决序列化问题
	Metadata  datatypes.JSON `json:"metadata" db:"metadata" gorm:"type:jsonb"`
	CreatedAt time.Time      `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" db:"updated_at" gorm:"autoUpdateTime"`
	Version   int            `json:"version" db:"version" gorm:"default:1"`
}

// NozzleConfig 喷嘴配置 - 用于设备初始化
type NozzleConfig struct {
	Number      byte            `json:"number"`                // 喷嘴编号 (1-15)
	Name        string          `json:"name,omitempty"`        // 喷嘴名称
	FuelGradeID string          `json:"fuel_grade_id"`         // 油品等级ID
	Price       decimal.Decimal `json:"price"`                 // 初始价格
	Position    string          `json:"position,omitempty"`    // 物理位置
	HoseLength  int             `json:"hose_length,omitempty"` // 油管长度
	IsEnabled   bool            `json:"is_enabled"`            // 是否启用
}

// NozzleStateUpdate 喷嘴状态更新 - 用于批量操作
type NozzleStateUpdate struct {
	NozzleNumber  byte             `json:"nozzle_number"`            // 喷嘴编号
	Status        NozzleStatus     `json:"status,omitempty"`         // 状态
	IsOut         *bool            `json:"is_out,omitempty"`         // 是否拔出
	IsSelected    *bool            `json:"is_selected,omitempty"`    // 是否选中
	CurrentPrice  *decimal.Decimal `json:"current_price,omitempty"`  // 当前价格
	CurrentVolume *decimal.Decimal `json:"current_volume,omitempty"` // 当前体积
	CurrentAmount *decimal.Decimal `json:"current_amount,omitempty"` // 当前金额
}

// TableName 设置Nozzle表名
func (Nozzle) TableName() string {
	return "nozzles"
}

// TableName 设置FuelGrade表名
func (FuelGrade) TableName() string {
	return "fuel_grades"
}

// IsValidNumber 检查喷嘴编号是否有效 (Wayne协议限制1-15)
func (n *Nozzle) IsValidNumber() bool {
	return n.Number >= 1 && n.Number <= 15
}

// IsActive 检查喷嘴是否处于活跃状态
func (n *Nozzle) IsActive() bool {
	return n.Status == NozzleStatusSelected ||
		n.Status == NozzleStatusOut ||
		n.Status == NozzleStatusFilling
}

// IsTransactionInProgress 检查是否有交易进行中
func (n *Nozzle) IsTransactionInProgress() bool {
	// 修复：使用更严格的条件判断是否有交易进行中
	// 1. 首先检查状态是否为加油中
	if n.Status == NozzleStatusFilling || n.Status == NozzleStatusAuthorized {
		return true
	}

	// 2. 检查是否有显著的体积或金额数据（避免微小的噪音数据）
	// isSignificantVolume := n.CurrentVolume.GreaterThan(decimal.NewFromFloat(0.001))
	// isSignificantAmount := n.CurrentAmount.GreaterThan(decimal.NewFromFloat(0.01))

	return false
}

// CanStartTransaction 检查是否可以开始新交易
func (n *Nozzle) CanStartTransaction() bool {
	return n.IsEnabled &&
		(n.Status == NozzleStatusIdle || n.Status == NozzleStatusSelected || n.Status == NozzleStatusAuthorized) &&
		!n.IsTransactionInProgress()
}

// GetDC3NozioValue 获取DC3事务的NOZIO字节值
func (n *Nozzle) GetDC3NozioValue() byte {
	nozio := n.Number & 0x0F // 低4位：喷嘴编号
	if n.IsOut {
		nozio |= 0x10 // bit 4：拔出状态
	}
	return nozio
}

// UpdateFromDC3 根据DC3事务数据更新喷嘴状态
func (n *Nozzle) UpdateFromDC3(nozio byte, price decimal.Decimal) {
	n.Number = nozio & 0x0F
	n.IsOut = (nozio & 0x10) != 0
	n.IsSelected = true // DC3事务表明喷嘴被选中
	n.CurrentPrice = price

	// 根据拔出状态确定喷嘴状态
	if n.IsOut {
		if n.Status == NozzleStatusIdle || n.Status == NozzleStatusSelected {
			n.Status = NozzleStatusOut
		}
	} else {
		if n.Status == NozzleStatusOut || n.Status == NozzleStatusFilling {
			n.Status = NozzleStatusCompleted
		}
	}
}

// UpdateFromDC2 根据DC2事务数据更新交易数据
func (n *Nozzle) UpdateFromDC2(volume, amount decimal.Decimal) {
	n.CurrentVolume = volume
	n.CurrentAmount = amount

	// 修复：增加更严格的加油状态判断条件
	// 只有当体积或金额有显著增长（大于0.001）且喷嘴处于正确状态时，才认为是真正在加油
	isSignificantVolume := volume.GreaterThan(decimal.NewFromFloat(0.001))
	isSignificantAmount := amount.GreaterThan(decimal.NewFromFloat(0.01))
	isValidStateForFilling := n.Status == NozzleStatusOut || n.Status == NozzleStatusAuthorized

	if (isSignificantVolume || isSignificantAmount) && isValidStateForFilling {
		n.Status = NozzleStatusFilling
	} else if volume.IsZero() && amount.IsZero() && n.Status == NozzleStatusFilling {
		// 如果体积和金额都归零，且当前是加油状态，转为完成状态
		n.Status = NozzleStatusCompleted
	}
}

// ResetTransaction 重置交易数据
func (n *Nozzle) ResetTransaction() {
	n.CurrentVolume = decimal.Zero
	n.CurrentAmount = decimal.Zero
	n.PresetVolume = nil
	n.PresetAmount = nil
	n.IsOut = false
	n.IsSelected = false
	n.Status = NozzleStatusIdle
}

// CompleteTransaction 完成交易并更新累计数据
func (n *Nozzle) CompleteTransaction() {
	// 更新累计数据
	n.TotalVolume = n.TotalVolume.Add(n.CurrentVolume)
	n.TotalAmount = n.TotalAmount.Add(n.CurrentAmount)
	n.TransactionCount++

	// 重置当前交易数据
	n.ResetTransaction()
	n.Status = NozzleStatusCompleted
}

// GetStatusDescription 获取状态描述
func (n *Nozzle) GetStatusDescription() string {
	switch n.Status {
	case NozzleStatusIdle:
		return "空闲"
	case NozzleStatusSelected:
		return "已选择"
	case NozzleStatusAuthorized:
		return "已授权"
	case NozzleStatusOut:
		return "油枪拔出"
	case NozzleStatusFilling:
		return "加油中"
	case NozzleStatusCompleted:
		return "加油完成"
	case NozzleStatusSuspended:
		return "暂停"
	case NozzleStatusError:
		return "错误"
	case NozzleStatusMaintenance:
		return "维护"
	default:
		return "未知状态"
	}
}

// ValidateWayneProtocol 验证是否符合Wayne DART协议要求
func (n *Nozzle) ValidateWayneProtocol() error {
	if !n.IsValidNumber() {
		return NewValidationError("nozzle_number", "喷嘴编号必须在1-15范围内", n.Number)
	}

	if n.CurrentPrice.LessThan(decimal.Zero) {
		return NewValidationError("current_price", "价格不能为负数", n.CurrentPrice)
	}

	if n.CurrentVolume.LessThan(decimal.Zero) {
		return NewValidationError("current_volume", "体积不能为负数", n.CurrentVolume)
	}

	if n.CurrentAmount.LessThan(decimal.Zero) {
		return NewValidationError("current_amount", "金额不能为负数", n.CurrentAmount)
	}

	return nil
}

// NewValidationError 创建验证错误
func NewValidationError(field, message string, value interface{}) error {
	return &ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	}
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value"`
}

func (e *ValidationError) Error() string {
	return e.Message
}

// SetTargetPrice 设置目标价格，触发价格同步流程
func (n *Nozzle) SetTargetPrice(price decimal.Decimal) {
	n.TargetPrice = &price
	n.PriceStatus = PriceStatusPending
	now := time.Now()
	n.PriceUpdatedAt = &now
	n.UpdatedAt = now
}

// MarkPriceSending 标记价格正在发送
func (n *Nozzle) MarkPriceSending(sentPrice decimal.Decimal) {
	n.LastSentPrice = &sentPrice
	n.PriceStatus = PriceStatusSending
	now := time.Now()
	n.PriceSentAt = &now
	n.UpdatedAt = now
}

// ConfirmPriceSync 确认价格同步成功
func (n *Nozzle) ConfirmPriceSync() {
	if n.TargetPrice != nil {
		n.CurrentPrice = *n.TargetPrice
		n.TargetPrice = nil
	}
	n.PriceStatus = PriceStatusSynced
	now := time.Now()
	n.PriceConfirmedAt = &now
	n.UpdatedAt = now
}

// MarkPriceFailed 标记价格同步失败
func (n *Nozzle) MarkPriceFailed() {
	n.PriceStatus = PriceStatusFailed
	n.UpdatedAt = time.Now()
}

// MarkPriceTimeout 标记价格同步超时
func (n *Nozzle) MarkPriceTimeout() {
	n.PriceStatus = PriceStatusTimeout
	n.UpdatedAt = time.Now()
}

// NeedsPriceSync 检查是否需要价格同步
func (n *Nozzle) NeedsPriceSync() bool {
	return n.PriceStatus == PriceStatusPending
}

// IsPriceSyncing 检查是否正在同步价格
func (n *Nozzle) IsPriceSyncing() bool {
	return n.PriceStatus == PriceStatusSending
}

// IsPriceSynced 检查价格是否已同步
func (n *Nozzle) IsPriceSynced() bool {
	return n.PriceStatus == PriceStatusSynced
}

// GetPriceSyncAge 获取价格同步的等待时间
func (n *Nozzle) GetPriceSyncAge() time.Duration {
	if n.PriceUpdatedAt == nil {
		return 0
	}
	return time.Since(*n.PriceUpdatedAt)
}

// ShouldRetryPriceSync 判断是否应该重试价格同步
func (n *Nozzle) ShouldRetryPriceSync(maxAge time.Duration) bool {
	if n.PriceStatus != PriceStatusFailed && n.PriceStatus != PriceStatusTimeout {
		return false
	}
	return n.GetPriceSyncAge() > maxAge
}

// ===== FuelGrade 软删除相关方法 =====

// IsDeleted 检查油品是否已被软删除
func (f *FuelGrade) IsDeleted() bool {
	return f.DeletedAt != nil
}

// SoftDelete 软删除油品
func (f *FuelGrade) SoftDelete() {
	now := time.Now()
	f.DeletedAt = &now
	f.UpdatedAt = now
}

// Restore 恢复已软删除的油品
func (f *FuelGrade) Restore() {
	f.DeletedAt = nil
	f.UpdatedAt = time.Now()
}

// MarkAsInactive 标记油品为非活跃状态（软删除）
func (f *FuelGrade) MarkAsInactive() {
	f.SoftDelete()
}

// MarkAsActive 标记油品为活跃状态（恢复）
func (f *FuelGrade) MarkAsActive() {
	f.Restore()
}

// IsActive 检查油品是否处于活跃状态（未被软删除）
func (f *FuelGrade) IsActive() bool {
	return !f.IsDeleted()
}

// GetStatusDescription 获取油品状态描述
func (f *FuelGrade) GetStatusDescription() string {
	if f.IsDeleted() {
		return "已停用"
	}
	return "正常"
}

// UpdatePrice 更新油品价格
func (f *FuelGrade) UpdatePrice(newPrice decimal.Decimal) {
	f.Price = newPrice
	f.UpdatedAt = time.Now()
}

// Clone 克隆油品信息（用于同步时的数据比较）
func (f *FuelGrade) Clone() *FuelGrade {
	clone := &FuelGrade{
		ID:          f.ID,
		Name:        f.Name,
		Type:        f.Type,
		Octane:      f.Octane,
		Description: f.Description,
		Color:       f.Color,
		Price:       f.Price,
		Currency:    f.Currency,
		Version:     f.Version,
		CreatedAt:   f.CreatedAt,
		UpdatedAt:   f.UpdatedAt,
	}

	// 复制软删除时间
	if f.DeletedAt != nil {
		deletedAt := *f.DeletedAt
		clone.DeletedAt = &deletedAt
	}

	// 复制元数据
	if f.Metadata != nil {
		clone.Metadata = make(datatypes.JSON, len(f.Metadata))
		copy(clone.Metadata, f.Metadata)
	}

	return clone
}

// UpdateFromExternal 从外部数据更新油品信息
func (f *FuelGrade) UpdateFromExternal(name, fuelType, description string, price decimal.Decimal, metadata map[string]interface{}) {
	f.Name = name
	f.Type = fuelType
	f.Description = description
	f.Price = price

	// 更新元数据
	if metadata != nil {
		metadataJSON, _ := json.Marshal(metadata)
		f.Metadata = metadataJSON
	}

	f.UpdatedAt = time.Now()
}
