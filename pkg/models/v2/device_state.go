package v2

import (
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// DeviceState 设备状态枚举
type DeviceState string

const (
	DeviceStateOffline      DeviceState = "offline"      // 设备离线
	DeviceStateOnline       DeviceState = "online"       // 设备在线
	DeviceStateInitializing DeviceState = "initializing" // 初始化中
	DeviceStateReady        DeviceState = "ready"        // 就绪状态
	DeviceStatePolling      DeviceState = "polling"      // 轮询中
	DeviceStateError        DeviceState = "error"        // 错误状态
	DeviceStateMaintenance  DeviceState = "maintenance"  // 维护状态
	DeviceStateConfiguring  DeviceState = "configuring"  // 配置中
)

// NozzleState 喷嘴状态枚举
type NozzleState string

const (
	NozzleStateIdle        NozzleState = "idle"        // 空闲
	NozzleStateSelected    NozzleState = "selected"    // 已选择
	NozzleStateAuthorized  NozzleState = "authorized"  // 已授权
	NozzleStateOut         NozzleState = "out"         // 油枪拔出
	NozzleStateFilling     NozzleState = "filling"     // 加油中
	NozzleStateCompleted   NozzleState = "completed"   // 加油完成
	NozzleStateSuspended   NozzleState = "suspended"   // 暂停
	NozzleStateError       NozzleState = "error"       // 错误
	NozzleStateMaintenance NozzleState = "maintenance" // 维护
)

// PumpStatus Wayne DART协议泵状态映射
type PumpStatus byte

const (
	PumpStatusNotProgrammed PumpStatus = 0x00 // 未编程
	PumpStatusReset         PumpStatus = 0x01 // 重置 (数据库中对应idle状态)
	PumpStatusAuthorized    PumpStatus = 0x02 // 已授权
	PumpStatusFilling       PumpStatus = 0x04 // 加油中
	PumpStatusCompleted     PumpStatus = 0x05 // 加油完成
	PumpStatusSuspended     PumpStatus = 0x06 // 暂停
	PumpStatusError         PumpStatus = 0x07 // 错误
)

// DeviceInfo 设备基础信息
type DeviceInfo struct {
	ID           string    `json:"id"`            // 设备ID
	Name         string    `json:"name"`          // 设备名称
	Type         string    `json:"type"`          // 设备类型
	Address      byte      `json:"address"`       // DART协议地址
	ControllerID string    `json:"controller_id"` // 控制器ID
	StationID    string    `json:"station_id"`    // 加油站ID
	IslandID     string    `json:"island_id"`     // 加油岛ID
	Position     string    `json:"position"`      // 物理位置
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
}

// NozzleInfo 喷嘴基础信息
type NozzleInfo struct {
	ID           string          `json:"id"`            // 喷嘴ID
	Number       byte            `json:"number"`        // 喷嘴编号 (1-15)
	Name         string          `json:"name"`          // 喷嘴名称
	DeviceID     string          `json:"device_id"`     // 设备ID
	FuelGradeID  string          `json:"fuel_grade_id"` // 油品等级ID
	CurrentPrice decimal.Decimal `json:"current_price"` // 当前价格
	Position     string          `json:"position"`      // 喷嘴位置
	IsEnabled    bool            `json:"is_enabled"`    // 是否启用
	CreatedAt    time.Time       `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time       `json:"updated_at"`    // 更新时间
}

// DeviceStateData 设备状态数据
type DeviceStateData struct {
	DeviceInfo DeviceInfo  `json:"device_info"` // 设备信息
	State      DeviceState `json:"state"`       // 当前状态

	// DART协议相关
	TxSequence   byte       `json:"tx_sequence"`   // 发送序列号
	LastPoll     time.Time  `json:"last_poll"`     // 最后轮询时间
	LastResponse time.Time  `json:"last_response"` // 最后响应时间
	PumpStatus   PumpStatus `json:"pump_status"`   // 泵状态

	// 连接状态
	IsOnline       bool      `json:"is_online"`       // 是否在线
	ConnectTime    time.Time `json:"connect_time"`    // 连接时间
	DisconnectTime time.Time `json:"disconnect_time"` // 断连时间
	FailureCount   int       `json:"failure_count"`   // 连续失败次数
	LastError      string    `json:"last_error"`      // 最后错误信息

	// 配置状态
	IsConfigured bool      `json:"is_configured"` // 是否已配置
	ConfigTime   time.Time `json:"config_time"`   // 配置时间
	NeedsReset   bool      `json:"needs_reset"`   // 是否需要重置

	// 业务状态
	ActiveNozzles []byte `json:"active_nozzles"` // 活跃喷嘴列表
	TotalNozzles  int    `json:"total_nozzles"`  // 喷嘴总数

	// 统计信息
	PollCount    int64         `json:"poll_count"`    // 轮询计数
	ErrorCount   int64         `json:"error_count"`   // 错误计数
	ResponseTime time.Duration `json:"response_time"` // 响应时间

	mu sync.RWMutex `json:"-"` // 并发保护
}

// NozzleStateData 喷嘴状态数据
type NozzleStateData struct {
	NozzleInfo NozzleInfo  `json:"nozzle_info"` // 喷嘴信息
	State      NozzleState `json:"state"`       // 当前状态

	// 喷嘴物理状态
	IsOut      bool `json:"is_out"`      // 是否拔出
	IsSelected bool `json:"is_selected"` // 是否选中

	// 交易数据
	CurrentVolume decimal.Decimal `json:"current_volume"` // 当前体积
	CurrentAmount decimal.Decimal `json:"current_amount"` // 当前金额
	CurrentPrice  decimal.Decimal `json:"current_price"`  // 当前单价

	// 累计数据
	TotalVolume decimal.Decimal `json:"total_volume"` // 累计体积
	TotalAmount decimal.Decimal `json:"total_amount"` // 累计金额

	// 状态时间
	StateTime  time.Time `json:"state_time"`  // 状态变更时间
	LastUpdate time.Time `json:"last_update"` // 最后更新时间

	// 交易信息
	TransactionID string    `json:"transaction_id"` // 当前交易ID
	StartTime     time.Time `json:"start_time"`     // 交易开始时间
	EndTime       time.Time `json:"end_time"`       // 交易结束时间

	mu sync.RWMutex `json:"-"` // 并发保护
}

// DeviceStateMachine 设备状态机接口
type DeviceStateMachine interface {
	// 状态查询
	GetState() DeviceState
	GetStateData() *DeviceStateData

	// 状态转换
	TransitionTo(newState DeviceState) error
	CanTransitionTo(newState DeviceState) bool

	// 事件处理
	OnPollStart() error
	OnPollResponse(responseTime time.Duration) error
	OnPollTimeout() error
	OnPollError(err error) error
	OnConnect() error
	OnDisconnect() error
	OnConfigured() error
	OnReset() error

	// 协议处理
	UpdateTxSequence(txSeq byte)
	UpdatePumpStatus(status PumpStatus)

	// 统计更新
	IncrementPollCount()
	IncrementErrorCount()
	UpdateResponseTime(duration time.Duration)
}

// NozzleStateMachine 喷嘴状态机接口
type NozzleStateMachine interface {
	// 状态查询
	GetState() NozzleState
	GetStateData() *NozzleStateData

	// 状态转换
	TransitionTo(newState NozzleState) error
	CanTransitionTo(newState NozzleState) bool

	// 事件处理
	OnSelected() error
	OnAuthorized() error
	OnOut() error
	OnIn() error
	OnStartFilling() error
	OnStopFilling() error
	OnComplete() error
	OnSuspend() error
	OnResume() error
	OnError(err error) error
	OnReset() error

	// 交易数据更新
	UpdateVolume(volume decimal.Decimal) error
	UpdateAmount(amount decimal.Decimal) error
	UpdatePrice(price decimal.Decimal) error
	UpdateTotals(volume, amount decimal.Decimal) error

	// 交易管理
	StartTransaction(transactionID string) error
	EndTransaction() error
	GetCurrentTransaction() *TransactionData
}

// TransactionData 交易数据
type TransactionData struct {
	ID        string          `json:"id"`         // 交易ID
	NozzleID  string          `json:"nozzle_id"`  // 喷嘴ID
	StartTime time.Time       `json:"start_time"` // 开始时间
	EndTime   time.Time       `json:"end_time"`   // 结束时间
	Volume    decimal.Decimal `json:"volume"`     // 体积
	Amount    decimal.Decimal `json:"amount"`     // 金额
	Price     decimal.Decimal `json:"price"`      // 单价
	Status    string          `json:"status"`     // 状态

	// 泵码数据 - DC101累计计数器支持
	StartVolumeReading *decimal.Decimal `json:"start_volume_reading,omitempty"` // 起始体积泵码
	EndVolumeReading   *decimal.Decimal `json:"end_volume_reading,omitempty"`   // 结束体积泵码
	StartAmountReading *decimal.Decimal `json:"start_amount_reading,omitempty"` // 起始金额泵码
	EndAmountReading   *decimal.Decimal `json:"end_amount_reading,omitempty"`   // 结束金额泵码
	PumpReadingSource  string           `json:"pump_reading_source,omitempty"`  // 泵码数据来源
	PumpReadingQuality string           `json:"pump_reading_quality,omitempty"` // 泵码数据质量
}

// StateEvent 状态事件
type StateEvent struct {
	Type      string      `json:"type"`      // 事件类型
	DeviceID  string      `json:"device_id"` // 设备ID
	NozzleID  string      `json:"nozzle_id"` // 喷嘴ID (可选)
	OldState  interface{} `json:"old_state"` // 旧状态
	NewState  interface{} `json:"new_state"` // 新状态
	Data      interface{} `json:"data"`      // 事件数据
	Timestamp time.Time   `json:"timestamp"` // 时间戳
}

// StateEventHandler 状态事件处理器
type StateEventHandler interface {
	HandleDeviceStateChange(event StateEvent) error
	HandleNozzleStateChange(event StateEvent) error
}

// DeviceStateManager 设备状态管理器接口
type DeviceStateManager interface {
	// 设备管理
	RegisterDevice(info DeviceInfo) (DeviceStateMachine, error)
	UnregisterDevice(deviceID string) error
	GetDevice(deviceID string) (DeviceStateMachine, error)
	GetAllDevices() map[string]DeviceStateMachine

	// 喷嘴管理
	RegisterNozzle(deviceID string, info NozzleInfo) (NozzleStateMachine, error)
	UnregisterNozzle(deviceID, nozzleID string) error
	GetNozzle(deviceID, nozzleID string) (NozzleStateMachine, error)
	GetDeviceNozzles(deviceID string) map[string]NozzleStateMachine

	// 事件处理
	Subscribe(handler StateEventHandler)
	Unsubscribe(handler StateEventHandler)

	// 状态查询
	GetDeviceState(deviceID string) (*DeviceStateData, error)
	GetNozzleState(deviceID, nozzleID string) (*NozzleStateData, error)
	GetDeviceStateSummary(deviceID string) (*DeviceStateSummary, error)
}

// DeviceStateSummary 设备状态摘要
type DeviceStateSummary struct {
	DeviceID     string                 `json:"device_id"`
	DeviceState  DeviceState            `json:"device_state"`
	IsOnline     bool                   `json:"is_online"`
	NozzleCount  int                    `json:"nozzle_count"`
	ActiveCount  int                    `json:"active_count"`
	NozzleStates map[string]NozzleState `json:"nozzle_states"`
	LastUpdate   time.Time              `json:"last_update"`
}
