package v2

import (
	"fmt"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// DeviceStateMachineV2 扩展的设备状态机接口 - 集成Nozzle状态管理
type DeviceStateMachineV2 interface {
	DeviceStateMachine

	// Nozzle状态管理
	GetNozzleStates() map[byte]*NozzleStateData
	GetNozzleState(nozzleNum byte) (*NozzleStateData, error)
	UpdateNozzleState(nozzleNum byte, state *NozzleStateData) error
	GetSelectedNozzle() byte
	SetSelectedNozzle(nozzleNum byte) error
	ClearNozzleSelection()

	// Wayne协议事件处理
	ProcessDC2Transaction(volume, amount decimal.Decimal) error
	ProcessDC3Transaction(nozzleNum byte, nozio byte, price decimal.Decimal) error
	ProcessDC1Status(status PumpStatus) error

	// 状态同步
	SyncWithNozzleService(nozzleService interface{}) error
	NotifyNozzleStateChange(nozzleNum byte, oldState, newState NozzleState) error

	// 扩展事件处理
	OnNozzleOut(nozzleNum byte) error
	OnNozzleIn(nozzleNum byte) error
	OnTransactionStart(nozzleNum byte) error
	OnTransactionUpdate(nozzleNum byte, volume, amount decimal.Decimal) error
	OnTransactionComplete(nozzleNum byte) error
}

// deviceStateMachineV2 实现DeviceStateMachineV2接口
type deviceStateMachineV2 struct {
	// 设备状态数据
	deviceState  *DeviceStateData
	nozzleStates map[byte]*NozzleStateData

	// 事件处理器
	eventHandlers []StateEventHandler

	// 并发保护
	mu sync.RWMutex

	// 配置
	maxNozzles int
	deviceID   string
}

// NewDeviceStateMachineV2 创建新的设备状态机V2
func NewDeviceStateMachineV2(deviceInfo DeviceInfo, maxNozzles int) DeviceStateMachineV2 {
	return &deviceStateMachineV2{
		deviceState: &DeviceStateData{
			DeviceInfo:    deviceInfo,
			State:         DeviceStateOffline,
			TxSequence:    0,
			ActiveNozzles: make([]byte, 0),
			mu:            sync.RWMutex{},
		},
		nozzleStates:  make(map[byte]*NozzleStateData),
		eventHandlers: make([]StateEventHandler, 0),
		maxNozzles:    maxNozzles,
		deviceID:      deviceInfo.ID,
	}
}

// 实现基础DeviceStateMachine接口
func (dsm *deviceStateMachineV2) GetState() DeviceState {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()
	return dsm.deviceState.State
}

func (dsm *deviceStateMachineV2) GetStateData() *DeviceStateData {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()

	// 创建不包含锁的副本
	stateCopy := &DeviceStateData{
		DeviceInfo:     dsm.deviceState.DeviceInfo,
		State:          dsm.deviceState.State,
		TxSequence:     dsm.deviceState.TxSequence,
		LastPoll:       dsm.deviceState.LastPoll,
		LastResponse:   dsm.deviceState.LastResponse,
		PumpStatus:     dsm.deviceState.PumpStatus,
		IsOnline:       dsm.deviceState.IsOnline,
		ConnectTime:    dsm.deviceState.ConnectTime,
		DisconnectTime: dsm.deviceState.DisconnectTime,
		FailureCount:   dsm.deviceState.FailureCount,
		LastError:      dsm.deviceState.LastError,
		IsConfigured:   dsm.deviceState.IsConfigured,
		ConfigTime:     dsm.deviceState.ConfigTime,
		NeedsReset:     dsm.deviceState.NeedsReset,
		ActiveNozzles:  append([]byte(nil), dsm.deviceState.ActiveNozzles...),
		TotalNozzles:   dsm.deviceState.TotalNozzles,
		PollCount:      dsm.deviceState.PollCount,
		ErrorCount:     dsm.deviceState.ErrorCount,
		ResponseTime:   dsm.deviceState.ResponseTime,
		// 注意：不包含 mu 字段
	}
	return stateCopy
}

func (dsm *deviceStateMachineV2) TransitionTo(newState DeviceState) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	oldState := dsm.deviceState.State

	if newState == oldState {
		return nil
	}

	if !dsm.canTransitionTo(newState) {
		return fmt.Errorf("cannot transition from %s to %s", oldState, newState)
	}

	dsm.deviceState.State = newState
	dsm.deviceState.LastResponse = time.Now()

	// 触发状态变更事件
	event := StateEvent{
		Type:      "device_state_change",
		DeviceID:  dsm.deviceID,
		OldState:  oldState,
		NewState:  newState,
		Timestamp: time.Now(),
	}

	dsm.publishEvent(event)
	return nil
}

func (dsm *deviceStateMachineV2) CanTransitionTo(newState DeviceState) bool {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()
	return dsm.canTransitionTo(newState)
}

func (dsm *deviceStateMachineV2) canTransitionTo(newState DeviceState) bool {
	currentState := dsm.deviceState.State

	// 定义状态转换规则
	switch currentState {
	case DeviceStateOffline:
		return newState == DeviceStateOnline || newState == DeviceStateInitializing
	case DeviceStateOnline:
		return newState == DeviceStateOffline || newState == DeviceStateReady || newState == DeviceStateError || newState == DeviceStatePolling
	case DeviceStateInitializing:
		// 修复：允许从初始化状态转换到轮询状态和在线状态
		return newState == DeviceStateReady || newState == DeviceStateError || newState == DeviceStateOffline ||
			newState == DeviceStatePolling || newState == DeviceStateOnline
	case DeviceStateReady:
		return newState == DeviceStatePolling || newState == DeviceStateConfiguring ||
			newState == DeviceStateError || newState == DeviceStateOffline
	case DeviceStatePolling:
		return newState == DeviceStateReady || newState == DeviceStateError || newState == DeviceStateOffline ||
			newState == DeviceStateInitializing // 允许从轮询状态返回到初始化状态
	case DeviceStateError:
		return newState == DeviceStateOffline || newState == DeviceStateInitializing
	case DeviceStateMaintenance:
		return newState == DeviceStateOffline || newState == DeviceStateReady
	case DeviceStateConfiguring:
		return newState == DeviceStateReady || newState == DeviceStateError
	default:
		return false
	}
}

// 事件处理实现
func (dsm *deviceStateMachineV2) OnPollStart() error {
	return dsm.TransitionTo(DeviceStatePolling)
}

func (dsm *deviceStateMachineV2) OnPollResponse(responseTime time.Duration) error {
	dsm.mu.Lock()
	dsm.deviceState.LastResponse = time.Now()
	dsm.deviceState.ResponseTime = responseTime
	dsm.deviceState.IsOnline = true
	dsm.deviceState.FailureCount = 0
	currentState := dsm.deviceState.State
	dsm.mu.Unlock()

	// 修复状态转换逻辑：处理从错误状态的恢复
	switch currentState {
	case DeviceStateError:
		// 从错误状态恢复：先转到初始化状态，然后转到在线状态
		if err := dsm.TransitionTo(DeviceStateInitializing); err != nil {
			return fmt.Errorf("failed to recover from error to initializing: %w", err)
		}
		// 立即转到在线状态
		return dsm.TransitionTo(DeviceStateOnline)
	case DeviceStateOffline:
		// 从离线状态先转到在线状态
		return dsm.TransitionTo(DeviceStateOnline)
	case DeviceStateOnline:
		// 从在线状态转到就绪状态
		return dsm.TransitionTo(DeviceStateReady)
	case DeviceStatePolling:
		// 从轮询状态转到就绪状态
		return dsm.TransitionTo(DeviceStateReady)
	case DeviceStateInitializing:
		// 从初始化状态转到在线状态
		return dsm.TransitionTo(DeviceStateOnline)
	default:
		// 其他状态保持不变或转到就绪状态
		return dsm.TransitionTo(DeviceStateReady)
	}
}

func (dsm *deviceStateMachineV2) OnPollTimeout() error {
	dsm.mu.Lock()
	currentPumpStatus := dsm.deviceState.PumpStatus
	dsm.deviceState.FailureCount++
	dsm.deviceState.LastError = "poll timeout"
	failureCount := dsm.deviceState.FailureCount
	dsm.mu.Unlock()

	// 📖 Wayne DART协议保护：授权、加油、完成状态下提高容错性
	isBusinessCriticalState := currentPumpStatus == 0x03 || // AUTHORIZED
		currentPumpStatus == 0x04 || // FILLING
		currentPumpStatus == 0x05    // FILLING_COMPLETED

	if isBusinessCriticalState {
		// 在关键业务状态下，需要更多失败次数才触发错误状态
		if failureCount >= 10 { // 提高到10次，避免打断业务流程
			return dsm.TransitionTo(DeviceStateError)
		}
		// 在5-9次失败时，仅记录警告，不改变状态
		if failureCount >= 5 {
			dsm.publishEvent(StateEvent{
				Type:     "timeout_warning_in_business_state",
				DeviceID: dsm.deviceID,
				Data: map[string]interface{}{
					"failure_count":    failureCount,
					"pump_status":      currentPumpStatus,
					"protection_note": "业务关键状态下的超时保护，避免强制状态转换",
				},
				Timestamp: time.Now(),
			})
		}
		return nil
	}

	// 对于非关键状态，使用正常的错误处理逻辑
	if failureCount >= 3 {
		return dsm.TransitionTo(DeviceStateError)
	}

	return nil
}

func (dsm *deviceStateMachineV2) OnPollError(err error) error {
	dsm.mu.Lock()
	dsm.deviceState.FailureCount++
	// 🚀 修复：处理nil error，避免panic
	if err != nil {
		dsm.deviceState.LastError = err.Error()
	} else {
		dsm.deviceState.LastError = "unknown error (nil)"
	}
	dsm.deviceState.ErrorCount++
	failureCount := dsm.deviceState.FailureCount
	dsm.mu.Unlock()

	// 🚀 修复：提高错误状态阈值，避免过于敏感
	if failureCount >= 10 { // 从5次提高到10次
		return dsm.TransitionTo(DeviceStateError)
	} else if failureCount >= 5 {
		// 🚀 新增：5-9次失败时转为离线状态，而不是错误状态
		return dsm.TransitionTo(DeviceStateOffline)
	}

	return nil
}

func (dsm *deviceStateMachineV2) OnConnect() error {
	dsm.mu.Lock()
	dsm.deviceState.IsOnline = true
	dsm.deviceState.ConnectTime = time.Now()
	dsm.deviceState.FailureCount = 0
	dsm.mu.Unlock()

	return dsm.TransitionTo(DeviceStateOnline)
}

func (dsm *deviceStateMachineV2) OnDisconnect() error {
	dsm.mu.Lock()
	dsm.deviceState.IsOnline = false
	dsm.deviceState.DisconnectTime = time.Now()
	dsm.mu.Unlock()

	return dsm.TransitionTo(DeviceStateOffline)
}

func (dsm *deviceStateMachineV2) OnConfigured() error {
	dsm.mu.Lock()
	dsm.deviceState.IsConfigured = true
	dsm.deviceState.ConfigTime = time.Now()
	dsm.mu.Unlock()

	return dsm.TransitionTo(DeviceStateReady)
}

func (dsm *deviceStateMachineV2) OnReset() error {
	dsm.mu.Lock()
	dsm.deviceState.NeedsReset = false
	dsm.deviceState.FailureCount = 0
	dsm.deviceState.ErrorCount = 0
	dsm.mu.Unlock()

	return dsm.TransitionTo(DeviceStateInitializing)
}

// 协议处理实现
func (dsm *deviceStateMachineV2) UpdateTxSequence(txSeq byte) {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	dsm.deviceState.TxSequence = txSeq
}

func (dsm *deviceStateMachineV2) UpdatePumpStatus(status PumpStatus) {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	dsm.deviceState.PumpStatus = status
}

// 统计更新实现
func (dsm *deviceStateMachineV2) IncrementPollCount() {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	dsm.deviceState.PollCount++
}

func (dsm *deviceStateMachineV2) IncrementErrorCount() {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	dsm.deviceState.ErrorCount++
}

func (dsm *deviceStateMachineV2) UpdateResponseTime(duration time.Duration) {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	dsm.deviceState.ResponseTime = duration
}

// Nozzle状态管理实现
func (dsm *deviceStateMachineV2) GetNozzleStates() map[byte]*NozzleStateData {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()

	// 创建状态副本
	states := make(map[byte]*NozzleStateData)
	for num, state := range dsm.nozzleStates {
		// 创建不包含锁的副本
		stateCopy := &NozzleStateData{
			NozzleInfo:    state.NozzleInfo,
			State:         state.State,
			IsOut:         state.IsOut,
			IsSelected:    state.IsSelected,
			CurrentVolume: state.CurrentVolume,
			CurrentAmount: state.CurrentAmount,
			CurrentPrice:  state.CurrentPrice,
			TotalVolume:   state.TotalVolume,
			TotalAmount:   state.TotalAmount,
			StateTime:     state.StateTime,
			LastUpdate:    state.LastUpdate,
			TransactionID: state.TransactionID,
			StartTime:     state.StartTime,
			EndTime:       state.EndTime,
			// 注意：不包含 mu 字段
		}
		states[num] = stateCopy
	}

	return states
}

func (dsm *deviceStateMachineV2) GetNozzleState(nozzleNum byte) (*NozzleStateData, error) {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()

	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		return nil, fmt.Errorf("nozzle %d not found", nozzleNum)
	}

	// 创建不包含锁的副本
	stateCopy := &NozzleStateData{
		NozzleInfo:    state.NozzleInfo,
		State:         state.State,
		IsOut:         state.IsOut,
		IsSelected:    state.IsSelected,
		CurrentVolume: state.CurrentVolume,
		CurrentAmount: state.CurrentAmount,
		CurrentPrice:  state.CurrentPrice,
		TotalVolume:   state.TotalVolume,
		TotalAmount:   state.TotalAmount,
		StateTime:     state.StateTime,
		LastUpdate:    state.LastUpdate,
		TransactionID: state.TransactionID,
		StartTime:     state.StartTime,
		EndTime:       state.EndTime,
		// 注意：不包含 mu 字段
	}
	return stateCopy, nil
}

func (dsm *deviceStateMachineV2) UpdateNozzleState(nozzleNum byte, state *NozzleStateData) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	// 根据Wayne DART协议，喷嘴编号0-15都是合法的
	// 0表示"无喷嘴选中"状态，1-15为正常喷嘴编号
	if nozzleNum > 15 {
		return fmt.Errorf("invalid nozzle number: %d", nozzleNum)
	}

	oldState := dsm.nozzleStates[nozzleNum]
	dsm.nozzleStates[nozzleNum] = state

	// 触发状态变更事件
	if oldState != nil {
		dsm.notifyNozzleStateChange(nozzleNum, oldState.State, state.State)
	}

	return nil
}

func (dsm *deviceStateMachineV2) GetSelectedNozzle() byte {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()

	for num, state := range dsm.nozzleStates {
		if state.IsSelected {
			return num
		}
	}

	return 0 // 没有选中的喷嘴
}

func (dsm *deviceStateMachineV2) SetSelectedNozzle(nozzleNum byte) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	// 清除其他喷嘴的选中状态
	for _, state := range dsm.nozzleStates {
		state.IsSelected = false
	}

	// 设置指定喷嘴为选中
	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		// 创建新的喷嘴状态
		state = &NozzleStateData{
			NozzleInfo: NozzleInfo{
				Number:   nozzleNum,
				DeviceID: dsm.deviceID,
			},
			State:      NozzleStateSelected,
			IsSelected: true,
			StateTime:  time.Now(),
			LastUpdate: time.Now(),
		}
		dsm.nozzleStates[nozzleNum] = state
	} else {
		state.IsSelected = true
		state.State = NozzleStateSelected
		state.StateTime = time.Now()
		state.LastUpdate = time.Now()
	}

	// 更新设备的活跃喷嘴列表
	dsm.updateActiveNozzles(nozzleNum, true)

	return nil
}

func (dsm *deviceStateMachineV2) ClearNozzleSelection() {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	for _, state := range dsm.nozzleStates {
		if state.IsSelected {
			state.IsSelected = false
			state.State = NozzleStateIdle
			state.StateTime = time.Now()
			state.LastUpdate = time.Now()
		}
	}

	dsm.deviceState.ActiveNozzles = make([]byte, 0)
}

// Wayne协议事件处理实现
func (dsm *deviceStateMachineV2) ProcessDC2Transaction(volume, amount decimal.Decimal) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	selectedNozzle := dsm.getSelectedNozzleUnsafe()
	if selectedNozzle == 0 {
		return fmt.Errorf("no nozzle selected for DC2 transaction")
	}

	state, exists := dsm.nozzleStates[selectedNozzle]
	if !exists {
		return fmt.Errorf("selected nozzle %d not found", selectedNozzle)
	}

	oldState := state.State
	state.CurrentVolume = volume
	state.CurrentAmount = amount

	// 修复：增加更严格的加油状态判断条件
	// 只有当体积或金额有显著增长（大于0.001）且喷嘴处于正确状态时，才认为是真正在加油
	isSignificantVolume := volume.GreaterThan(decimal.NewFromFloat(0.001))
	isSignificantAmount := amount.GreaterThan(decimal.NewFromFloat(0.01))
	isValidStateForFilling := state.State == NozzleStateOut || state.State == NozzleStateAuthorized
	
	if (isSignificantVolume || isSignificantAmount) && isValidStateForFilling {
		state.State = NozzleStateFilling
	} else if volume.IsZero() && amount.IsZero() && state.State == NozzleStateFilling {
		// 如果体积和金额都归零，且当前是加油状态，转为完成状态
		state.State = NozzleStateCompleted
	}

	state.LastUpdate = time.Now()

	dsm.notifyNozzleStateChange(selectedNozzle, oldState, state.State)
	return nil
}

func (dsm *deviceStateMachineV2) ProcessDC3Transaction(nozzleNum byte, nozio byte, price decimal.Decimal) error {
	// 根据Wayne DART协议，喷嘴编号0-15都是合法的
	// 0表示"无喷嘴选中"状态，1-15为正常喷嘴编号
	if nozzleNum > 15 {
		return fmt.Errorf("invalid nozzle number: %d", nozzleNum)
	}

	// 修复死锁：特殊处理喷嘴编号0，先处理再加锁
	if nozzleNum == 0 {
		dsm.ClearNozzleSelection()
		return nil
	}

	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	isOut := (nozio & 0x10) != 0

	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		// 创建新的喷嘴状态
		state = &NozzleStateData{
			NozzleInfo: NozzleInfo{
				Number:   nozzleNum,
				DeviceID: dsm.deviceID,
			},
			State:      NozzleStateIdle,
			StateTime:  time.Now(),
			LastUpdate: time.Now(),
		}
		dsm.nozzleStates[nozzleNum] = state
	}

	oldState := state.State
	state.CurrentPrice = price
	state.IsOut = isOut
	state.IsSelected = true

	// 根据拔出状态确定喷嘴状态
	if isOut {
		if state.State == NozzleStateIdle || state.State == NozzleStateSelected {
			state.State = NozzleStateOut
		}
	} else {
		if state.State == NozzleStateOut || state.State == NozzleStateFilling {
			state.State = NozzleStateCompleted
		}
	}

	state.StateTime = time.Now()
	state.LastUpdate = time.Now()

	// 更新设备的活跃喷嘴列表
	dsm.updateActiveNozzles(nozzleNum, state.IsSelected)

	dsm.notifyNozzleStateChange(nozzleNum, oldState, state.State)
	return nil
}

func (dsm *deviceStateMachineV2) ProcessDC1Status(status PumpStatus) error {
	// 修复死锁：先获取当前状态，然后在一个锁周期内完成所有操作
	dsm.mu.Lock()
	currentState := dsm.deviceState.State
	dsm.deviceState.PumpStatus = status
	dsm.mu.Unlock()

	// 特殊处理 PUMP_NOT_PROGRAMMED 状态
	if status == PumpStatusNotProgrammed {
		// 触发自动价格配置事件
		event := StateEvent{
			Type:     "auto_price_config_needed",
			DeviceID: dsm.deviceID,
			Data: map[string]interface{}{
				"pump_status":   status,
				"reason":        "PUMP_NOT_PROGRAMMED detected, automatic price configuration required per Wayne DART protocol",
				"action":        "send_price_update",
				"protocol_note": "Reset commands are not allowed in PUMP_NOT_PROGRAMMED state, must configure prices first",
			},
			Timestamp: time.Now(),
		}

		dsm.publishEvent(event)

		// 如果当前处于错误状态，先恢复到初始化状态
		if currentState == DeviceStateError {
			if err := dsm.TransitionTo(DeviceStateInitializing); err != nil {
				return fmt.Errorf("failed to recover from error to initializing for PUMP_NOT_PROGRAMMED: %w", err)
			}
		}
		// 保持在初始化状态，等待价格配置完成
		return dsm.TransitionTo(DeviceStateInitializing)
	}

	// 根据泵状态更新设备状态
	switch status {
	case PumpStatusReset: // RESET状态对应数据库中的idle状态
		// 如果当前处于错误状态，需要先恢复
		if currentState == DeviceStateError {
			// 先转到初始化状态
			if err := dsm.TransitionTo(DeviceStateInitializing); err != nil {
				return fmt.Errorf("failed to recover from error to initializing: %w", err)
			}
			// 然后转到在线状态
			if err := dsm.TransitionTo(DeviceStateOnline); err != nil {
				return fmt.Errorf("failed to transition from initializing to online: %w", err)
			}
		}
		// 最后转到就绪状态
		return dsm.TransitionTo(DeviceStateReady)
	case PumpStatusError:
		return dsm.TransitionTo(DeviceStateError)
	default:
		// 对于其他状态，如果当前处于错误状态，尝试恢复到在线状态
		if currentState == DeviceStateError {
			if err := dsm.TransitionTo(DeviceStateInitializing); err != nil {
				return fmt.Errorf("failed to recover from error to initializing: %w", err)
			}
			return dsm.TransitionTo(DeviceStateOnline)
		}
		return nil
	}
}

// 扩展事件处理实现
func (dsm *deviceStateMachineV2) OnNozzleOut(nozzleNum byte) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		return fmt.Errorf("nozzle %d not found", nozzleNum)
	}

	oldState := state.State
	state.IsOut = true
	state.State = NozzleStateOut
	state.StateTime = time.Now()
	state.LastUpdate = time.Now()

	dsm.notifyNozzleStateChange(nozzleNum, oldState, state.State)
	return nil
}

func (dsm *deviceStateMachineV2) OnNozzleIn(nozzleNum byte) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		return fmt.Errorf("nozzle %d not found", nozzleNum)
	}

	oldState := state.State
	state.IsOut = false

	// 根据当前状态决定新状态
	if state.State == NozzleStateOut || state.State == NozzleStateFilling {
		state.State = NozzleStateCompleted
	}

	state.StateTime = time.Now()
	state.LastUpdate = time.Now()

	dsm.notifyNozzleStateChange(nozzleNum, oldState, state.State)
	return nil
}

func (dsm *deviceStateMachineV2) OnTransactionStart(nozzleNum byte) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		return fmt.Errorf("nozzle %d not found", nozzleNum)
	}

	oldState := state.State
	state.State = NozzleStateAuthorized
	state.StartTime = time.Now()
	state.StateTime = time.Now()
	state.LastUpdate = time.Now()

	dsm.notifyNozzleStateChange(nozzleNum, oldState, state.State)
	return nil
}

func (dsm *deviceStateMachineV2) OnTransactionUpdate(nozzleNum byte, volume, amount decimal.Decimal) error {
	return dsm.ProcessDC2Transaction(volume, amount)
}

func (dsm *deviceStateMachineV2) OnTransactionComplete(nozzleNum byte) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	state, exists := dsm.nozzleStates[nozzleNum]
	if !exists {
		return fmt.Errorf("nozzle %d not found", nozzleNum)
	}

	oldState := state.State

	// 更新累计数据
	state.TotalVolume = state.TotalVolume.Add(state.CurrentVolume)
	state.TotalAmount = state.TotalAmount.Add(state.CurrentAmount)

	// 重置当前交易数据
	state.CurrentVolume = decimal.Zero
	state.CurrentAmount = decimal.Zero
	state.IsOut = false
	state.IsSelected = false
	state.State = NozzleStateCompleted
	state.EndTime = time.Now()
	state.StateTime = time.Now()
	state.LastUpdate = time.Now()

	dsm.notifyNozzleStateChange(nozzleNum, oldState, state.State)
	return nil
}

// 状态同步实现
func (dsm *deviceStateMachineV2) SyncWithNozzleService(nozzleService interface{}) error {
	// 这里可以实现与NozzleService的同步逻辑
	// 由于接口类型问题，这里先返回nil
	return nil
}

func (dsm *deviceStateMachineV2) NotifyNozzleStateChange(nozzleNum byte, oldState, newState NozzleState) error {
	dsm.notifyNozzleStateChange(nozzleNum, oldState, newState)
	return nil
}

// 私有辅助方法
func (dsm *deviceStateMachineV2) getSelectedNozzleUnsafe() byte {
	for num, state := range dsm.nozzleStates {
		if state.IsSelected {
			return num
		}
	}
	return 0
}

func (dsm *deviceStateMachineV2) updateActiveNozzles(nozzleNum byte, isActive bool) {
	activeNozzles := make([]byte, 0)

	for num, state := range dsm.nozzleStates {
		if state.IsSelected || state.State == NozzleStateFilling || state.State == NozzleStateOut {
			activeNozzles = append(activeNozzles, num)
		}
	}

	dsm.deviceState.ActiveNozzles = activeNozzles
}

func (dsm *deviceStateMachineV2) notifyNozzleStateChange(nozzleNum byte, oldState, newState NozzleState) {
	event := StateEvent{
		Type:      "nozzle_state_change",
		DeviceID:  dsm.deviceID,
		NozzleID:  fmt.Sprintf("%s-nozzle-%d", dsm.deviceID, nozzleNum),
		OldState:  oldState,
		NewState:  newState,
		Data:      map[string]interface{}{"nozzle_number": nozzleNum},
		Timestamp: time.Now(),
	}

	dsm.publishEvent(event)
}

func (dsm *deviceStateMachineV2) publishEvent(event StateEvent) {
	for _, handler := range dsm.eventHandlers {
		go func(h StateEventHandler, e StateEvent) {
			if e.Type == "device_state_change" {
				h.HandleDeviceStateChange(e)
			} else if e.Type == "nozzle_state_change" {
				h.HandleNozzleStateChange(e)
			}
		}(handler, event)
	}
}

// AddEventHandler 添加事件处理器
func (dsm *deviceStateMachineV2) AddEventHandler(handler StateEventHandler) {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	dsm.eventHandlers = append(dsm.eventHandlers, handler)
}

// RemoveEventHandler 移除事件处理器
func (dsm *deviceStateMachineV2) RemoveEventHandler(handler StateEventHandler) {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	for i, h := range dsm.eventHandlers {
		if h == handler {
			dsm.eventHandlers = append(dsm.eventHandlers[:i], dsm.eventHandlers[i+1:]...)
			break
		}
	}
}
