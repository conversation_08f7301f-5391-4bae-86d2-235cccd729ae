package models

import (
	"github.com/shopspring/decimal"
)

// NewVolume 创建体积值
func NewVolume(value float64, unit string) decimal.Decimal {
	return decimal.NewFromFloat(value)
}

// NewPrice 创建价格值
func NewPrice(value float64) decimal.Decimal {
	return decimal.NewFromFloat(value)
}

// NewDecimalFromString 从字符串创建decimal
func NewDecimalFromString(value string) (decimal.Decimal, error) {
	return decimal.NewFromString(value)
}

// IsDecimalZero 检查decimal是否为零
func IsDecimalZero(d decimal.Decimal) bool {
	return d.Equal(decimal.Zero)
}
 