package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFuelGrade_SoftDelete(t *testing.T) {
	// 创建测试油品
	fuelGrade := &FuelGrade{
		ID:          "test_grade_1",
		Name:        "测试汽油",
		Type:        "gasoline",
		Octane:      92,
		Description: "测试用汽油",
		Color:       "red",
		Price:       decimal.NewFromFloat(7.50),
		Currency:    "CNY",
		Version:     1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 测试初始状态
	assert.False(t, fuelGrade.IsDeleted(), "新创建的油品不应该被标记为删除")
	assert.True(t, fuelGrade.IsActive(), "新创建的油品应该处于活跃状态")
	assert.Equal(t, "正常", fuelGrade.GetStatusDescription(), "状态描述应该是'正常'")

	// 测试软删除
	beforeDelete := time.Now()
	fuelGrade.SoftDelete()
	afterDelete := time.Now()

	assert.True(t, fuelGrade.IsDeleted(), "油品应该被标记为删除")
	assert.False(t, fuelGrade.IsActive(), "油品应该处于非活跃状态")
	assert.Equal(t, "已停用", fuelGrade.GetStatusDescription(), "状态描述应该是'已停用'")
	assert.NotNil(t, fuelGrade.DeletedAt, "DeletedAt字段不应该为nil")
	assert.True(t, fuelGrade.DeletedAt.After(beforeDelete) && fuelGrade.DeletedAt.Before(afterDelete),
		"DeletedAt时间应该在软删除操作的时间范围内")

	// 测试恢复
	beforeRestore := time.Now()
	time.Sleep(1 * time.Millisecond) // 确保时间差异
	fuelGrade.Restore()
	afterRestore := time.Now()

	assert.False(t, fuelGrade.IsDeleted(), "恢复后油品不应该被标记为删除")
	assert.True(t, fuelGrade.IsActive(), "恢复后油品应该处于活跃状态")
	assert.Equal(t, "正常", fuelGrade.GetStatusDescription(), "恢复后状态描述应该是'正常'")
	assert.Nil(t, fuelGrade.DeletedAt, "恢复后DeletedAt字段应该为nil")
	assert.True(t, fuelGrade.UpdatedAt.After(beforeRestore) && fuelGrade.UpdatedAt.Before(afterRestore),
		"UpdatedAt时间应该在恢复操作的时间范围内")
}

func TestFuelGrade_MarkAsInactiveAndActive(t *testing.T) {
	fuelGrade := &FuelGrade{
		ID:        "test_grade_2",
		Name:      "测试柴油",
		Type:      "diesel",
		Price:     decimal.NewFromFloat(6.80),
		Currency:  "CNY",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 测试标记为非活跃
	fuelGrade.MarkAsInactive()
	assert.True(t, fuelGrade.IsDeleted(), "MarkAsInactive应该软删除油品")
	assert.False(t, fuelGrade.IsActive(), "MarkAsInactive后油品应该非活跃")

	// 测试标记为活跃
	fuelGrade.MarkAsActive()
	assert.False(t, fuelGrade.IsDeleted(), "MarkAsActive应该恢复油品")
	assert.True(t, fuelGrade.IsActive(), "MarkAsActive后油品应该活跃")
}

func TestFuelGrade_UpdatePrice(t *testing.T) {
	fuelGrade := &FuelGrade{
		ID:        "test_grade_3",
		Name:      "测试汽油",
		Type:      "gasoline",
		Price:     decimal.NewFromFloat(7.50),
		Currency:  "CNY",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	originalUpdateTime := fuelGrade.UpdatedAt
	time.Sleep(10 * time.Millisecond) // 确保时间差异

	newPrice := decimal.NewFromFloat(8.00)
	fuelGrade.UpdatePrice(newPrice)

	assert.Equal(t, newPrice, fuelGrade.Price, "价格应该被更新")
	assert.True(t, fuelGrade.UpdatedAt.After(originalUpdateTime), "UpdatedAt时间应该被更新")
}

func TestFuelGrade_Clone(t *testing.T) {
	// 创建原始油品
	original := &FuelGrade{
		ID:          "test_grade_4",
		Name:        "测试汽油",
		Type:        "gasoline",
		Octane:      95,
		Description: "测试用95号汽油",
		Color:       "blue",
		Price:       decimal.NewFromFloat(8.10),
		Currency:    "CNY",
		Version:     2,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 添加元数据
	metadata := map[string]interface{}{
		"grade":  "premium",
		"source": "test",
	}
	metadataJSON, _ := json.Marshal(metadata)
	original.Metadata = metadataJSON

	// 软删除原始油品
	original.SoftDelete()

	// 克隆
	clone := original.Clone()

	// 验证克隆结果
	assert.Equal(t, original.ID, clone.ID, "ID应该相同")
	assert.Equal(t, original.Name, clone.Name, "Name应该相同")
	assert.Equal(t, original.Type, clone.Type, "Type应该相同")
	assert.Equal(t, original.Octane, clone.Octane, "Octane应该相同")
	assert.Equal(t, original.Description, clone.Description, "Description应该相同")
	assert.Equal(t, original.Color, clone.Color, "Color应该相同")
	assert.True(t, original.Price.Equal(clone.Price), "Price应该相同")
	assert.Equal(t, original.Currency, clone.Currency, "Currency应该相同")
	assert.Equal(t, original.Version, clone.Version, "Version应该相同")
	assert.Equal(t, original.CreatedAt, clone.CreatedAt, "CreatedAt应该相同")
	assert.Equal(t, original.UpdatedAt, clone.UpdatedAt, "UpdatedAt应该相同")

	// 验证软删除状态被正确克隆
	assert.NotNil(t, clone.DeletedAt, "DeletedAt应该被克隆")
	assert.Equal(t, *original.DeletedAt, *clone.DeletedAt, "DeletedAt时间应该相同")
	assert.True(t, clone.IsDeleted(), "克隆的油品应该保持删除状态")

	// 验证元数据被正确克隆
	assert.Equal(t, original.Metadata, clone.Metadata, "Metadata应该相同")

	// 验证是深拷贝而不是浅拷贝
	clone.Name = "修改后的名称"
	assert.NotEqual(t, original.Name, clone.Name, "修改克隆不应该影响原始对象")
}

func TestFuelGrade_UpdateFromExternal(t *testing.T) {
	fuelGrade := &FuelGrade{
		ID:        "test_grade_5",
		Name:      "旧名称",
		Type:      "gasoline",
		Price:     decimal.NewFromFloat(7.50),
		Currency:  "CNY",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	originalUpdateTime := fuelGrade.UpdatedAt
	time.Sleep(10 * time.Millisecond) // 确保时间差异

	// 更新数据
	newName := "新名称"
	newType := "diesel"
	newDescription := "新描述"
	newPrice := decimal.NewFromFloat(6.80)
	newMetadata := map[string]interface{}{
		"code":   "NEW-001",
		"source": "external_api",
	}

	fuelGrade.UpdateFromExternal(newName, newType, newDescription, newPrice, newMetadata)

	// 验证更新结果
	assert.Equal(t, newName, fuelGrade.Name, "名称应该被更新")
	assert.Equal(t, newType, fuelGrade.Type, "类型应该被更新")
	assert.Equal(t, newDescription, fuelGrade.Description, "描述应该被更新")
	assert.True(t, newPrice.Equal(fuelGrade.Price), "价格应该被更新")
	assert.True(t, fuelGrade.UpdatedAt.After(originalUpdateTime), "UpdatedAt时间应该被更新")

	// 验证元数据
	var actualMetadata map[string]interface{}
	err := json.Unmarshal(fuelGrade.Metadata, &actualMetadata)
	require.NoError(t, err, "元数据应该能够正确反序列化")
	assert.Equal(t, "NEW-001", actualMetadata["code"], "元数据中的code应该正确")
	assert.Equal(t, "external_api", actualMetadata["source"], "元数据中的source应该正确")
}

func TestFuelGrade_TableName(t *testing.T) {
	fuelGrade := &FuelGrade{}
	assert.Equal(t, "fuel_grades", fuelGrade.TableName(), "表名应该是fuel_grades")
}

func TestFuelGrade_SoftDeleteEdgeCases(t *testing.T) {
	t.Run("重复软删除", func(t *testing.T) {
		fuelGrade := &FuelGrade{
			ID:        "test_grade_6",
			Name:      "测试油品",
			Type:      "gasoline",
			Price:     decimal.NewFromFloat(7.50),
			Currency:  "CNY",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// 第一次软删除
		fuelGrade.SoftDelete()
		firstDeleteTime := *fuelGrade.DeletedAt

		time.Sleep(10 * time.Millisecond)

		// 第二次软删除
		fuelGrade.SoftDelete()
		secondDeleteTime := *fuelGrade.DeletedAt

		assert.True(t, secondDeleteTime.After(firstDeleteTime), "重复软删除应该更新删除时间")
	})

	t.Run("重复恢复", func(t *testing.T) {
		fuelGrade := &FuelGrade{
			ID:        "test_grade_7",
			Name:      "测试油品",
			Type:      "gasoline",
			Price:     decimal.NewFromFloat(7.50),
			Currency:  "CNY",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// 软删除
		fuelGrade.SoftDelete()
		assert.True(t, fuelGrade.IsDeleted(), "应该被软删除")

		// 第一次恢复
		fuelGrade.Restore()
		assert.False(t, fuelGrade.IsDeleted(), "应该被恢复")

		// 第二次恢复（对未删除的油品）
		firstRestoreTime := fuelGrade.UpdatedAt
		time.Sleep(10 * time.Millisecond)
		fuelGrade.Restore()

		assert.False(t, fuelGrade.IsDeleted(), "应该仍然处于活跃状态")
		assert.True(t, fuelGrade.UpdatedAt.After(firstRestoreTime), "UpdatedAt应该被更新")
	})
}
