package models

import (
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

// CommandType 命令类型枚举
type CommandType string

const (
	// 设备控制命令
	CommandTypeStart    CommandType = "start"    // 启动设备
	CommandTypeStop     CommandType = "stop"     // 停止设备
	CommandTypePause    CommandType = "pause"    // 暂停设备
	CommandTypeResume   CommandType = "resume"   // 恢复设备
	CommandTypeReset    CommandType = "reset"    // 重置设备
	CommandTypeShutdown CommandType = "shutdown" // 关闭设备

	// 泵设备专用命令
	CommandTypeAuthorize CommandType = "authorize"  // 授权加油
	CommandTypePreset    CommandType = "preset"     // 预设金额/升数
	CommandTypePumpStart CommandType = "pump_start" // 开始加油
	CommandTypePumpStop  CommandType = "pump_stop"  // 停止加油
	CommandTypeSetPrice  CommandType = "set_price"  // 设置价格

	// ATG设备专用命令
	CommandTypeRead       CommandType = "read"        // 读取数据
	CommandTypeInventory  CommandType = "inventory"   // 库存查询
	CommandTypeAlarmReset CommandType = "alarm_reset" // 告警复位
	CommandTypeCalibrate  CommandType = "calibrate"   // 校准

	// 显示屏设备专用命令
	CommandTypeDisplay     CommandType = "display"      // 显示内容
	CommandTypeClearScreen CommandType = "clear_screen" // 清屏
	CommandTypeSetBright   CommandType = "set_bright"   // 设置亮度

	// 系统命令
	CommandTypeStatus     CommandType = "status"     // 状态查询
	CommandTypeConfig     CommandType = "config"     // 配置命令
	CommandTypeDiagnostic CommandType = "diagnostic" // 诊断命令
	CommandTypeUpdate     CommandType = "update"     // 更新命令

	// DART协议专用命令
	CommandTypeRequestStatus        CommandType = "request_status"         // CD1 00H - 请求状态
	CommandTypeRequestTotalCounters CommandType = "request_total_counters" // CD101 - 请求总计数器
	CommandTypeRequestFillingInfo   CommandType = "request_filling_info"   // CD1 04H - 请求加油信息
	CommandTypeRequestPumpIdentity  CommandType = "request_pump_identity"  // CD1 03H - 请求泵身份
	CommandTypeRequestPumpParams    CommandType = "request_pump_params"    // CD1 02H - 请求泵参数
	CommandTypeAllowedNozzles       CommandType = "allowed_nozzles"        // CD2 - 允许的喷嘴
	CommandTypePresetVolume         CommandType = "preset_volume"          // CD3 - 预设体积
	CommandTypePresetAmount         CommandType = "preset_amount"          // CD4 - 预设金额
	CommandTypePriceUpdate          CommandType = "price_update"           // CD5 - 价格更新
)

// CommandStatus 命令状态枚举
type CommandStatus string

const (
	CommandStatusPending   CommandStatus = "pending"   // 等待执行
	CommandStatusSending   CommandStatus = "sending"   // 发送中
	CommandStatusExecuting CommandStatus = "executing" // 执行中
	CommandStatusCompleted CommandStatus = "completed" // 执行完成
	CommandStatusFailed    CommandStatus = "failed"    // 执行失败
	CommandStatusTimeout   CommandStatus = "timeout"   // 执行超时
	CommandStatusCancelled CommandStatus = "cancelled" // 已取消
)

// Priority 优先级枚举
type Priority int

const (
	PriorityLow    Priority = 1  // 低优先级
	PriorityNormal Priority = 5  // 普通优先级
	PriorityHigh   Priority = 8  // 高优先级
	PriorityUrgent Priority = 10 // 紧急优先级
)

// Command 命令实体 - 向设备发送的指令
type Command struct {
	// 基础标识信息
	ID   string      `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	Type CommandType `json:"type" db:"type" gorm:"type:varchar(100);not null;index"`
	Name string      `json:"name" db:"name" gorm:"type:varchar(255);not null"`

	// 目标设备信息
	DeviceID     string `json:"device_id" db:"device_id" gorm:"type:varchar(255);not null;index"`
	ControllerID string `json:"controller_id" db:"controller_id" gorm:"type:varchar(255);not null;index"`

	// 命令参数和数据
	Parameters CommandParameters `json:"parameters" db:"parameters" gorm:"type:jsonb"`
	Payload    CommandPayload    `json:"payload" db:"payload" gorm:"type:jsonb"`

	// 执行控制
	Priority    Priority   `json:"priority" db:"priority" gorm:"not null;default:5"`
	ScheduledAt *time.Time `json:"scheduled_at" db:"scheduled_at"` // 计划执行时间
	TimeoutSecs int        `json:"timeout_secs" db:"timeout_secs"` // 超时时间(秒)
	MaxRetries  int        `json:"max_retries" db:"max_retries"`   // 最大重试次数
	RetryCount  int        `json:"retry_count" db:"retry_count"`   // 当前重试次数

	// 状态信息
	Status       CommandStatus `json:"status" db:"status" gorm:"type:varchar(50);not null;index"`
	Result       CommandResult `json:"result" db:"result" gorm:"type:jsonb"`
	ErrorMessage string        `json:"error_message" db:"error_message" gorm:"type:text"`

	// 关联事务
	TransactionID *string `json:"transaction_id" db:"transaction_id" gorm:"type:varchar(255);index"`

	// 用户信息
	UserID    string `json:"user_id" db:"user_id" gorm:"type:varchar(255)"`
	UserAgent string `json:"user_agent" db:"user_agent" gorm:"type:varchar(500)"`

	// 时间戳
	CreatedAt   time.Time  `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
	StartedAt   *time.Time `json:"started_at" db:"started_at"`
	CompletedAt *time.Time `json:"completed_at" db:"completed_at"`

	// 元数据
	Version int `json:"version" db:"version" gorm:"default:1"`
}

// CommandParameters 命令参数
type CommandParameters struct {
	// 通用参数
	Amount   *decimal.Decimal `json:"amount,omitempty"`    // 金额(用于预设等)
	Volume   *decimal.Decimal `json:"volume,omitempty"`    // 体积(用于预设等)
	Price    *decimal.Decimal `json:"price,omitempty"`     // 价格(用于设置价格等)
	FuelType string           `json:"fuel_type,omitempty"` // 油品类型
	NozzleID string           `json:"nozzle_id,omitempty"` // 喷嘴ID

	// 显示相关参数
	Message    string `json:"message,omitempty"`    // 显示消息
	Brightness int    `json:"brightness,omitempty"` // 亮度(0-100)
	Duration   int    `json:"duration,omitempty"`   // 显示时长(秒)

	// ATG相关参数
	TankID    string   `json:"tank_id,omitempty"`    // 油罐ID
	DataTypes []string `json:"data_types,omitempty"` // 数据类型
	Addresses []int    `json:"addresses,omitempty"`  // 设备地址列表

	// DART协议特有参数
	DeviceAddress int    `json:"device_address,omitempty"` // 设备地址
	CommandCode   string `json:"command_code,omitempty"`   // 命令代码
	DataLength    int    `json:"data_length,omitempty"`    // 数据长度

	// 扩展参数
	ExtraParams map[string]interface{} `json:"extra_params,omitempty"`
}

// CommandPayload 命令载荷数据
type CommandPayload struct {
	// 原始数据
	RawData []byte `json:"raw_data,omitempty"` // 原始字节数据

	// DART协议数据
	DARTFrame *DARTFrame `json:"dart_frame,omitempty"` // DART协议帧

	// JSON数据
	JSONData map[string]interface{} `json:"json_data,omitempty"` // JSON格式数据

	// 文件数据
	FileData *FileData `json:"file_data,omitempty"` // 文件数据
}

// DARTFrame DART协议帧结构
type DARTFrame struct {
	STX        byte   `json:"stx"`         // 帧头(0x02)
	Address    byte   `json:"address"`     // 设备地址
	Command    byte   `json:"command"`     // 命令代码
	DataLength byte   `json:"data_length"` // 数据长度
	Data       []byte `json:"data"`        // 数据域
	CRC        uint16 `json:"crc"`         // CRC校验码
	ETX        byte   `json:"etx"`         // 帧尾(0x03)
}

// FileData 文件数据
type FileData struct {
	Filename string `json:"filename"`  // 文件名
	Content  []byte `json:"content"`   // 文件内容
	MimeType string `json:"mime_type"` // MIME类型
	Size     int64  `json:"size"`      // 文件大小
}

// CommandResult 命令执行结果
type CommandResult struct {
	// 执行结果
	Success   bool                   `json:"success"`              // 是否成功
	Data      map[string]interface{} `json:"data"`                 // 返回数据
	ErrorCode string                 `json:"error_code,omitempty"` // 错误代码
	ErrorMsg  string                 `json:"error_msg,omitempty"`  // 错误消息

	// 响应数据
	Response     *ResponseData `json:"response,omitempty"`      // 设备响应
	ResponseTime int64         `json:"response_time,omitempty"` // 响应时间(毫秒)

	// 执行统计
	ExecutionTime int64 `json:"execution_time,omitempty"` // 执行时间(毫秒)
	RetryCount    int   `json:"retry_count,omitempty"`    // 重试次数
}

// ResponseData 设备响应数据
type ResponseData struct {
	// 原始响应
	RawResponse []byte `json:"raw_response,omitempty"` // 原始响应数据

	// DART协议响应
	DARTResponse *DARTFrame `json:"dart_response,omitempty"` // DART协议响应帧

	// 解析后的数据
	ParsedData map[string]interface{} `json:"parsed_data,omitempty"` // 解析后的数据

	// 状态信息
	DeviceStatus map[string]interface{} `json:"device_status,omitempty"` // 设备状态
}

// TransactionType 交易类型枚举
type TransactionType string

const (
	TransactionTypeFuel    TransactionType = "fuel"    // 加油交易
	TransactionTypePayment TransactionType = "payment" // 支付交易
	TransactionTypeRefund  TransactionType = "refund"  // 退款交易
	TransactionTypeVoid    TransactionType = "void"    // 作废交易
)

// TransactionStatus 交易状态枚举
type TransactionStatus string

const (
	TransactionStatusPending   TransactionStatus = "pending"   // 等待中
	TransactionStatusStarted   TransactionStatus = "started"   // 已开始
	TransactionStatusActive    TransactionStatus = "active"    // 进行中
	TransactionStatusCompleted TransactionStatus = "completed" // 已完成
	TransactionStatusCancelled TransactionStatus = "cancelled" // 已取消
	TransactionStatusFailed    TransactionStatus = "failed"    // 失败
	TransactionStatusRefunded  TransactionStatus = "refunded"  // 已退款

	// 新增状态 - 直接持久化方案
	TransactionStatusInitiated TransactionStatus = "initiated" // DC3 Nozzle Out创建Transaction
	TransactionStatusFilling   TransactionStatus = "filling"   // DC1 FILLING状态

	// 🆕 智能完成机制状态
	TransactionStatusAwaitingFinalDC2 TransactionStatus = "awaiting_final_dc2" // DC1 FILLING_COMPLETED后等待最终DC2数据
)

// TransactionStateChange 状态转换记录
type TransactionStateChange struct {
	FromStatus TransactionStatus `json:"from_status"`
	ToStatus   TransactionStatus `json:"to_status"`
	Timestamp  time.Time         `json:"timestamp"`
	Reason     string            `json:"reason"`
	Source     string            `json:"source"` // DC1/DC2/DC3/manual
}

// Transaction 交易实体 - 加油等业务交易
type Transaction struct {
	// 基础标识信息
	ID     string            `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	Type   TransactionType   `json:"type" db:"type" gorm:"type:varchar(100);not null;index"`
	Status TransactionStatus `json:"status" db:"status" gorm:"type:varchar(50);not null;index"`

	// 设备信息
	DeviceID     string `json:"device_id" db:"device_id" gorm:"type:varchar(255);not null;index"`
	ControllerID string `json:"controller_id" db:"controller_id" gorm:"type:varchar(255);not null;index"`
	NozzleID     string `json:"nozzle_id" db:"nozzle_id" gorm:"type:varchar(255);index"`

	// 交易数据 - 修复：所有金额字段改为3位小数
	FuelType      string          `json:"fuel_type" db:"fuel_type" gorm:"type:varchar(100)"`
	FuelGradeID   *string         `json:"fuel_grade_id" db:"fuel_grade_id" gorm:"type:varchar(255);index"`
	FuelGradeName string          `json:"fuel_grade_name" db:"fuel_grade_name" gorm:"type:varchar(255)"`
	UnitPrice     decimal.Decimal `json:"unit_price" db:"unit_price" gorm:"type:decimal(10,3)"`
	PresetAmount  decimal.Decimal `json:"preset_amount" db:"preset_amount" gorm:"type:decimal(10,3)"`
	PresetVolume  decimal.Decimal `json:"preset_volume" db:"preset_volume" gorm:"type:decimal(10,3)"`
	ActualAmount  decimal.Decimal `json:"actual_amount" db:"actual_amount" gorm:"type:decimal(10,3)"`
	ActualVolume  decimal.Decimal `json:"actual_volume" db:"actual_volume" gorm:"type:decimal(10,3)"`
	TotalAmount   decimal.Decimal `json:"total_amount" db:"total_amount" gorm:"type:decimal(10,3)"`

	// 泵码数据 - Wayne DART协议DC101累计计数器支持 - 修复：金额泵码改为3位小数
	StartPumpVolumeReading *decimal.Decimal `json:"start_pump_volume_reading" db:"start_pump_volume_reading" gorm:"type:decimal(15,3);comment:起始泵码-体积读数(升)，来源于DC101累计计数器"`
	EndPumpVolumeReading   *decimal.Decimal `json:"end_pump_volume_reading" db:"end_pump_volume_reading" gorm:"type:decimal(15,3);comment:结束泵码-体积读数(升)，来源于DC101累计计数器"`
	StartPumpAmountReading *decimal.Decimal `json:"start_pump_amount_reading" db:"start_pump_amount_reading" gorm:"type:decimal(15,3);comment:起始泵码-金额读数(元)，来源于DC101累计计数器"`
	EndPumpAmountReading   *decimal.Decimal `json:"end_pump_amount_reading" db:"end_pump_amount_reading" gorm:"type:decimal(15,3);comment:结束泵码-金额读数(元)，来源于DC101累计计数器"`
	PumpReadingSource      string           `json:"pump_reading_source" db:"pump_reading_source" gorm:"type:varchar(50);default:DC101;comment:泵码数据来源(DC101/MANUAL/ESTIMATE)"`
	PumpReadingQuality     string           `json:"pump_reading_quality" db:"pump_reading_quality" gorm:"type:varchar(20);default:good;comment:泵码数据质量(good/poor/bad/missing)"`
	PumpReadingValidated   bool             `json:"pump_reading_validated" db:"pump_reading_validated" gorm:"default:false;comment:泵码数据是否已验证"`
	PumpReadingDiscrepancy *decimal.Decimal `json:"pump_reading_discrepancy" db:"pump_reading_discrepancy" gorm:"type:decimal(10,3);comment:泵码计算与实际的差异值(升)"`

	// 客户信息
	CustomerID   string `json:"customer_id" db:"customer_id" gorm:"type:varchar(255);index"`
	CustomerCard string `json:"customer_card" db:"customer_card" gorm:"type:varchar(255)"`
	VehicleID    string `json:"vehicle_id" db:"vehicle_id" gorm:"type:varchar(255)"`

	// 支付信息 - 修复：支付金额字段改为3位小数
	PaymentMethod string          `json:"payment_method" db:"payment_method" gorm:"type:varchar(100)"`
	PaymentRef    string          `json:"payment_ref" db:"payment_ref" gorm:"type:varchar(255)"`
	PaidAmount    decimal.Decimal `json:"paid_amount" db:"paid_amount" gorm:"type:decimal(10,3)"`
	ChangeAmount  decimal.Decimal `json:"change_amount" db:"change_amount" gorm:"type:decimal(10,3)"`

	// 操作员信息
	OperatorID   string `json:"operator_id" db:"operator_id" gorm:"type:varchar(255)"`
	OperatorName string `json:"operator_name" db:"operator_name" gorm:"type:varchar(255)"`

	// 时间信息
	StartedAt   *time.Time `json:"started_at" db:"started_at"`
	CompletedAt *time.Time `json:"completed_at" db:"completed_at"`
	CancelledAt *time.Time `json:"cancelled_at" db:"cancelled_at"`

	// 元数据
	CreatedAt time.Time `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at" gorm:"autoUpdateTime"`
	Version   int       `json:"version" db:"version" gorm:"default:1"`

	// 扩展数据 - 修复：使用datatypes.JSON解决序列化问题
	ExtraData datatypes.JSON `json:"extra_data" db:"extra_data" gorm:"type:jsonb"`

	// 🎯 外部系统同步字段
	ExternalTransactionID *string `json:"external_transaction_id" db:"external_transaction_id" gorm:"type:varchar(255);index;comment:外部系统返回的交易ID，用于后续更新操作"`
	SyncStatus            string  `json:"sync_status" db:"sync_status" gorm:"type:varchar(50);default:'pending';comment:同步状态(pending/synced/failed/needs_sync)"`

	// 直接持久化方案扩展字段
	InitiatedAt       *time.Time `json:"initiated_at" db:"initiated_at" gorm:"comment:DC3 Nozzle Out创建时间"`
	FillingAt         *time.Time `json:"filling_at" db:"filling_at" gorm:"comment:DC1 FILLING开始时间"`
	PendingCountersAt *time.Time `json:"pending_counters_at" db:"pending_counters_at" gorm:"comment:DC1 FILLING_COMPLETED进入pending_counters状态时间，用于超时处理"`

	// 🆕 智能完成机制时间戳字段
	AwaitingFinalDC2At  *time.Time     `json:"awaiting_final_dc2_at" db:"awaiting_final_dc2_at" gorm:"comment:进入awaiting_final_dc2状态时间，用于500ms超时检查"`
	SmartWaitingAt      *time.Time     `json:"smart_waiting_at" db:"smart_waiting_at" gorm:"comment:进入smart_waiting状态时间，用于200ms超时检查"`
	CounterUpdateWindow time.Duration  `json:"counter_update_window" db:"counter_update_window" gorm:"comment:允许泵码更新的时间窗口(秒)"`
	DataSource          string         `json:"data_source" db:"data_source" gorm:"type:varchar(50);default:'direct_persistence';comment:数据来源"`
	CreatedBy           string         `json:"created_by" db:"created_by" gorm:"type:varchar(100);comment:创建者"`
	StateHistory        datatypes.JSON `json:"state_history" db:"state_history" gorm:"type:jsonb;comment:状态转换历史"`

	// 关联命令
	Commands []Command `json:"commands,omitempty" gorm:"foreignKey:TransactionID"`
}

// DeviceStatusType 设备状态类型枚举
type DeviceStatusType string

const (
	DeviceStatusTypeGeneral    DeviceStatusType = "general"    // 通用状态
	DeviceStatusTypePump       DeviceStatusType = "pump"       // 泵状态
	DeviceStatusTypeATG        DeviceStatusType = "atg"        // ATG状态
	DeviceStatusTypeDisplay    DeviceStatusType = "display"    // 显示屏状态
	DeviceStatusTypeController DeviceStatusType = "controller" // 控制器状态
)

// DeviceStatusEntry 设备状态条目 - 设备实时状态数据
type DeviceStatusEntry struct {
	// 基础标识信息
	ID         string           `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	DeviceID   string           `json:"device_id" db:"device_id" gorm:"type:varchar(255);not null;index"`
	StatusType DeviceStatusType `json:"status_type" db:"status_type" gorm:"type:varchar(100);not null;index"`

	// 状态数据
	StatusData  map[string]interface{} `json:"status_data" db:"status_data" gorm:"type:jsonb"`
	Timestamp   time.Time              `json:"timestamp" db:"timestamp" gorm:"not null;index"`
	SequenceNum int64                  `json:"sequence_num" db:"sequence_num" gorm:"not null"`

	// 数据质量
	DataQuality string `json:"data_quality" db:"data_quality" gorm:"type:varchar(50)"` // good, poor, bad
	Source      string `json:"source" db:"source" gorm:"type:varchar(100)"`            // 数据来源

	// 元数据
	CreatedAt time.Time `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
}

// TableName 设置Command表名
func (Command) TableName() string {
	return "commands"
}

// TableName 设置Transaction表名
func (Transaction) TableName() string {
	return "transactions"
}

// TableName 设置DeviceStatusEntry表名
func (DeviceStatusEntry) TableName() string {
	return "device_status_entries"
}

// IsPending 检查命令是否为等待状态
func (c *Command) IsPending() bool {
	return c.Status == CommandStatusPending
}

// IsExecuting 检查命令是否在执行中
func (c *Command) IsExecuting() bool {
	return c.Status == CommandStatusExecuting || c.Status == CommandStatusSending
}

// IsCompleted 检查命令是否已完成
func (c *Command) IsCompleted() bool {
	return c.Status == CommandStatusCompleted
}

// IsFailed 检查命令是否失败
func (c *Command) IsFailed() bool {
	return c.Status == CommandStatusFailed || c.Status == CommandStatusTimeout
}

// CanRetry 检查命令是否可以重试
func (c *Command) CanRetry() bool {
	return c.IsFailed() && c.RetryCount < c.MaxRetries
}

// IsScheduled 检查命令是否已计划
func (c *Command) IsScheduled() bool {
	return c.ScheduledAt != nil && c.ScheduledAt.After(time.Now())
}

// ShouldExecute 检查命令是否应该执行
func (c *Command) ShouldExecute() bool {
	if c.ScheduledAt == nil {
		return c.IsPending()
	}
	return c.IsPending() && !c.ScheduledAt.After(time.Now())
}

// IsActive 检查交易是否活跃
func (t *Transaction) IsActive() bool {
	return t.Status == TransactionStatusStarted || t.Status == TransactionStatusActive
}

// IsCompleted 检查交易是否已完成
func (t *Transaction) IsCompleted() bool {
	return t.Status == TransactionStatusCompleted
}

// IsCancelled 检查交易是否已取消
func (t *Transaction) IsCancelled() bool {
	return t.Status == TransactionStatusCancelled
}

// CanCancel 检查交易是否可以取消
func (t *Transaction) CanCancel() bool {
	return t.Status == TransactionStatusPending ||
		t.Status == TransactionStatusStarted ||
		t.Status == TransactionStatusActive
}

// GetDuration 获取交易持续时间
func (t *Transaction) GetDuration() time.Duration {
	if t.StartedAt == nil {
		return 0
	}

	if t.CompletedAt != nil {
		return t.CompletedAt.Sub(*t.StartedAt)
	}

	if t.CancelledAt != nil {
		return t.CancelledAt.Sub(*t.StartedAt)
	}

	return time.Since(*t.StartedAt)
}

// IsFuelTransaction 检查是否为加油交易
func (t *Transaction) IsFuelTransaction() bool {
	return t.Type == TransactionTypeFuel
}

// CalculateDiscount 计算折扣金额
func (t *Transaction) CalculateDiscount() decimal.Decimal {
	if t.TotalAmount.IsZero() || t.PaidAmount.IsZero() {
		return decimal.Zero
	}

	discount := t.TotalAmount.Sub(t.PaidAmount)
	if discount.IsNegative() {
		return decimal.Zero
	}

	return discount
}

// CalculatedVolume 基于泵码计算交易体积
func (t *Transaction) CalculatedVolume() decimal.Decimal {
	if t.StartPumpVolumeReading == nil || t.EndPumpVolumeReading == nil {
		return decimal.Zero
	}

	if t.EndPumpVolumeReading.LessThan(*t.StartPumpVolumeReading) {
		return decimal.Zero
	}

	return t.EndPumpVolumeReading.Sub(*t.StartPumpVolumeReading)
}

// CalculatedAmount 基于泵码计算交易金额
func (t *Transaction) CalculatedAmount() decimal.Decimal {
	if t.StartPumpAmountReading == nil || t.EndPumpAmountReading == nil {
		return decimal.Zero
	}

	if t.EndPumpAmountReading.LessThan(*t.StartPumpAmountReading) {
		return decimal.Zero
	}

	return t.EndPumpAmountReading.Sub(*t.StartPumpAmountReading)
}

// GetVolumeDiscrepancy 获取泵码计算体积与实际体积的差异
func (t *Transaction) GetVolumeDiscrepancy() decimal.Decimal {
	calculatedVolume := t.CalculatedVolume()
	if calculatedVolume.IsZero() {
		return decimal.Zero
	}

	return calculatedVolume.Sub(t.ActualVolume).Abs()
}

// GetAmountDiscrepancy 获取泵码计算金额与实际金额的差异
func (t *Transaction) GetAmountDiscrepancy() decimal.Decimal {
	calculatedAmount := t.CalculatedAmount()
	if calculatedAmount.IsZero() {
		return decimal.Zero
	}

	return calculatedAmount.Sub(t.ActualAmount).Abs()
}

// ValidatePumpReadings 验证泵码数据的一致性
func (t *Transaction) ValidatePumpReadings() error {
	// 检查泵码字段的基本完整性
	if t.StartPumpVolumeReading != nil || t.EndPumpVolumeReading != nil {
		if t.StartPumpVolumeReading == nil {
			return fmt.Errorf("缺少起始体积泵码")
		}
		if t.EndPumpVolumeReading == nil {
			return fmt.Errorf("缺少结束体积泵码")
		}
		if t.EndPumpVolumeReading.LessThan(*t.StartPumpVolumeReading) {
			return fmt.Errorf("结束体积泵码不能小于起始体积泵码")
		}
	}

	if t.StartPumpAmountReading != nil || t.EndPumpAmountReading != nil {
		if t.StartPumpAmountReading == nil {
			return fmt.Errorf("缺少起始金额泵码")
		}
		if t.EndPumpAmountReading == nil {
			return fmt.Errorf("缺少结束金额泵码")
		}
		if t.EndPumpAmountReading.LessThan(*t.StartPumpAmountReading) {
			return fmt.Errorf("结束金额泵码不能小于起始金额泵码")
		}
	}

	// 如果有泵码数据，检查数据一致性（容差范围内）
	tolerance := decimal.NewFromFloat(0.01) // 10ml容差
	volumeDiscrepancy := t.GetVolumeDiscrepancy()

	if volumeDiscrepancy.GreaterThan(tolerance) {
		return fmt.Errorf("泵码计算体积与实际体积差异过大: %s升", volumeDiscrepancy.String())
	}

	return nil
}

// HasPumpReadings 检查是否有完整的泵码数据
func (t *Transaction) HasPumpReadings() bool {
	return t.StartPumpVolumeReading != nil &&
		t.EndPumpVolumeReading != nil &&
		t.StartPumpAmountReading != nil &&
		t.EndPumpAmountReading != nil
}

// GetPumpReadingQuality 获取泵码数据质量级别
func (t *Transaction) GetPumpReadingQuality() string {
	if t.PumpReadingQuality == "" {
		if t.HasPumpReadings() {
			return "good"
		}
		return "missing"
	}
	return t.PumpReadingQuality
}

// IsPumpReadingValidated 检查泵码数据是否已验证
func (t *Transaction) IsPumpReadingValidated() bool {
	return t.PumpReadingValidated
}

// BuildDARTFrame 构建DART协议帧
func (cmd *Command) BuildDARTFrame() (*DARTFrame, error) {
	if cmd.Payload.DARTFrame != nil {
		return cmd.Payload.DARTFrame, nil
	}

	// 根据命令类型和参数构建DART帧
	frame := &DARTFrame{
		STX: 0x02,
		ETX: 0x03,
	}

	// 设置设备地址
	if cmd.Parameters.DeviceAddress > 0 {
		frame.Address = byte(cmd.Parameters.DeviceAddress)
	}

	// 根据命令类型设置命令代码
	switch cmd.Type {
	case CommandTypeStatus:
		frame.Command = 0x53 // 'S' - Status
	case CommandTypeStart:
		frame.Command = 0x31 // '1' - Start
	case CommandTypeStop:
		frame.Command = 0x30 // '0' - Stop
	case CommandTypeRead:
		frame.Command = 0x52 // 'R' - Read
	default:
		frame.Command = 0x00
	}

	// 设置数据
	if len(cmd.Payload.RawData) > 0 {
		frame.Data = cmd.Payload.RawData
		frame.DataLength = byte(len(frame.Data))
	}

	return frame, nil
}

// HasFuelGrade 检查是否有油品等级信息
func (t *Transaction) HasFuelGrade() bool {
	return t.FuelGradeID != nil && *t.FuelGradeID != ""
}

// SetFuelGrade 设置油品等级信息
func (t *Transaction) SetFuelGrade(fuelGradeID, fuelGradeName string) {
	t.FuelGradeID = &fuelGradeID
	t.FuelGradeName = fuelGradeName
}

// GetFuelGradeDisplayName 获取显示用的油品等级名称
func (t *Transaction) GetFuelGradeDisplayName() string {
	if t.FuelGradeName != "" {
		return t.FuelGradeName
	}
	if t.HasFuelGrade() {
		return *t.FuelGradeID
	}
	return t.FuelType
}

// IsFuelGradeValid 检查油品等级信息是否有效
func (t *Transaction) IsFuelGradeValid() bool {
	return t.HasFuelGrade() && t.FuelGradeName != ""
}

// GetFuelGradeID 安全地获取油品等级ID
func (t *Transaction) GetFuelGradeID() string {
	if t.FuelGradeID != nil {
		return *t.FuelGradeID
	}
	return ""
}

// CanUpdateCounters 检查是否可以更新泵码
func (t *Transaction) CanUpdateCounters(timestamp time.Time) bool {
	// 只有在完成状态后的一定时间窗口内才允许更新泵码
	if t.Status != TransactionStatusCompleted {
		return false
	}

	// 检查时间窗口（默认30秒）
	window := 30 * time.Second
	if t.CounterUpdateWindow > 0 {
		window = t.CounterUpdateWindow
	}

	return timestamp.Sub(t.UpdatedAt) <= window
}
