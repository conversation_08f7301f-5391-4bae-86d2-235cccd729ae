package models

import (
	"time"

	"github.com/shopspring/decimal"
)

// NozzleCounters 喷嘴累计泵码变更流水表 - 记录每一次DC101事务的累计计数器变更
// 这是一个流水表，每次DC101事务都会插入一条新记录
type NozzleCounters struct {
	// 流水表主键
	ID int64 `json:"id" db:"id" gorm:"primaryKey;autoIncrement;comment:流水记录主键"`

	// 设备和喷嘴标识
	DeviceID string `json:"device_id" db:"device_id" gorm:"type:varchar(255);not null;index;comment:设备ID"`
	NozzleID string `json:"nozzle_id" db:"nozzle_id" gorm:"type:varchar(255);not null;index;comment:喷嘴ID"`

	// DC101事务信息
	CounterType  int16           `json:"counter_type" db:"counter_type" gorm:"not null;comment:DC101计数器类型(0x01-0x08体积,0x11-0x18金额)"`
	CounterValue decimal.Decimal `json:"counter_value" db:"counter_value" gorm:"type:decimal(15,3);not null;comment:当前累计计数器值"`

	// 变更信息
	PreviousValue  *decimal.Decimal `json:"previous_value" db:"previous_value" gorm:"type:decimal(15,3);comment:上一次记录的累计值"`
	IncrementValue *decimal.Decimal `json:"increment_value" db:"increment_value" gorm:"type:decimal(15,3);comment:本次增量值"`

	// 数据来源和质量
	DC101TransactionData []byte `json:"dc101_transaction_data" db:"dc101_transaction_data" gorm:"type:bytea;comment:原始DC101事务数据"`
	IsValid              bool   `json:"is_valid" db:"is_valid" gorm:"default:true;comment:数据是否有效"`
	IsAnomaly            bool   `json:"is_anomaly" db:"is_anomaly" gorm:"default:false;comment:是否为异常数据"`
	AnomalyReason        string `json:"anomaly_reason" db:"anomaly_reason" gorm:"type:varchar(255);comment:异常原因描述"`

	// 业务上下文
	TransactionID string `json:"transaction_id" db:"transaction_id" gorm:"type:varchar(255);index;comment:关联的业务交易ID"`
	OperatorID    string `json:"operator_id" db:"operator_id" gorm:"type:varchar(255);comment:操作员ID"`

	// 时间戳
	RecordedAt time.Time `json:"recorded_at" db:"recorded_at" gorm:"not null;default:CURRENT_TIMESTAMP;index;comment:DC101事务发生时间"`
	CreatedAt  time.Time `json:"created_at" db:"created_at" gorm:"autoCreateTime;comment:记录创建时间"`
}

// TableName 设置表名
func (NozzleCounters) TableName() string {
	return "nozzle_counters"
}

// IsVolumeCounter 判断是否为体积计数器
func (nc *NozzleCounters) IsVolumeCounter() bool {
	return nc.CounterType >= 1 && nc.CounterType <= 8
}

// IsAmountCounter 判断是否为金额计数器
func (nc *NozzleCounters) IsAmountCounter() bool {
	return nc.CounterType >= 17 && nc.CounterType <= 24
}

// SetIncrement 设置增量值
func (nc *NozzleCounters) SetIncrement(previousValue *decimal.Decimal) {
	nc.PreviousValue = previousValue
	if previousValue != nil {
		increment := nc.CounterValue.Sub(*previousValue)
		nc.IncrementValue = &increment

		// 检查异常：计数器回退
		if increment.LessThan(decimal.Zero) {
			nc.IsAnomaly = true
			nc.AnomalyReason = "counter_rollback"
		}
		// 检查异常：单次增量过大
		if increment.GreaterThan(decimal.NewFromInt(1000)) {
			nc.IsAnomaly = true
			nc.AnomalyReason = "excessive_increment"
		}
	} else {
		// 首次记录
		nc.IncrementValue = &nc.CounterValue
	}
}

// GetCounterTypeName 获取计数器类型名称
func (nc *NozzleCounters) GetCounterTypeName() string {
	if nc.IsVolumeCounter() {
		return "volume"
	} else if nc.IsAmountCounter() {
		return "amount"
	}
	return "unknown"
}

// CounterUpdateEvent 计数器更新事件（用于事件通知）
type CounterUpdateEvent struct {
	DeviceID       string          `json:"device_id"`
	NozzleID       string          `json:"nozzle_id"`
	CounterType    int16           `json:"counter_type"`
	OldValue       decimal.Decimal `json:"old_value"`
	NewValue       decimal.Decimal `json:"new_value"`
	IncrementValue decimal.Decimal `json:"increment_value"`
	Timestamp      time.Time       `json:"timestamp"`
	IsAnomaly      bool            `json:"is_anomaly"`
	AnomalyReason  string          `json:"anomaly_reason,omitempty"`
	TransactionID  string          `json:"transaction_id,omitempty"`
}
