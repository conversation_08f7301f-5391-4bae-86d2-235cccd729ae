package models

import (
	"encoding/json"
)

// MarshalConfig 序列化控制器配置
func MarshalConfig(config ControllerConfig) ([]byte, error) {
	return json.Marshal(config)
}

// UnmarshalConfig 反序列化控制器配置
func UnmarshalConfig(data []byte) (ControllerConfig, error) {
	var config ControllerConfig
	err := json.Unmarshal(data, &config)
	return config, err
}

// MarshalDeviceConfig 序列化设备配置
func MarshalDeviceConfig(config DeviceConfig) ([]byte, error) {
	return json.Marshal(config)
}

// UnmarshalDeviceConfig 反序列化设备配置
func UnmarshalDeviceConfig(data []byte) (DeviceConfig, error) {
	var config DeviceConfig
	err := json.Unmarshal(data, &config)
	return config, err
}

// MarshalMetadata 序列化元数据
func MarshalMetadata(metadata map[string]interface{}) ([]byte, error) {
	if metadata == nil {
		return []byte("{}"), nil
	}
	return json.Marshal(metadata)
}

// UnmarshalMetadata 反序列化元数据
func UnmarshalMetadata(data []byte) (map[string]interface{}, error) {
	var metadata map[string]interface{}
	err := json.Unmarshal(data, &metadata)
	return metadata, err
}
