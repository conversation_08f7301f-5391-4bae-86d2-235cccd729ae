package models

import (
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

// DeviceType 设备类型枚举
type DeviceType string

const (
	// 下位机类型
	DeviceTypeController     DeviceType = "controller"
	DeviceTypeFuelController DeviceType = "fuel_controller"
	DeviceTypePayController  DeviceType = "payment_controller"
	DeviceTypeMonitorCtrl    DeviceType = "monitor_controller"

	// 控制器类型
	ControllerTypeDARTLine   DeviceType = "dart_line_controller"
	ControllerTypeTCPDevice  DeviceType = "tcp_device_controller"
	ControllerTypeSerialPort DeviceType = "serial_port_controller"

	// 终端设备类型 - 标准设备
	DeviceTypeFuelPump   DeviceType = "fuel_pump"
	DeviceTypeFuelNozzle DeviceType = "fuel_nozzle"
	DeviceTypeATG        DeviceType = "atg"
	DeviceTypeDisplay    DeviceType = "display"
	DeviceTypePOS        DeviceType = "pos"
	DeviceTypeEDC        DeviceType = "edc"
	DeviceTypePump       DeviceType = "pump"

	// 终端设备类型 - DART协议设备
	DeviceTypeDARTPump    DeviceType = "dart_pump"
	DeviceTypeDARTTank    DeviceType = "dart_tank"
	DeviceTypeDARTDisplay DeviceType = "dart_display"
)

// DeviceStatus 设备状态枚举
type DeviceStatus string

const (
	DeviceStatusOnline      DeviceStatus = "online"
	DeviceStatusOffline     DeviceStatus = "offline"
	DeviceStatusError       DeviceStatus = "error"
	DeviceStatusMaintenance DeviceStatus = "maintenance"
	DeviceStatusUnknown     DeviceStatus = "unknown"
)

// DeviceHealth 设备健康状态枚举
type DeviceHealth string

const (
	DeviceHealthHealthy  DeviceHealth = "healthy"
	DeviceHealthGood     DeviceHealth = "good"
	DeviceHealthWarning  DeviceHealth = "warning"
	DeviceHealthCritical DeviceHealth = "critical"
	DeviceHealthError    DeviceHealth = "error"
	DeviceHealthUnknown  DeviceHealth = "unknown"
)

// ControllerStatus 控制器状态枚举 (alias for DeviceStatus)
type ControllerStatus = DeviceStatus

// ControllerType 控制器类型枚举 (alias for DeviceType)
type ControllerType = DeviceType

// ProtocolType 通信协议类型枚举
type ProtocolType string

const (
	ProtocolTypeTCP       ProtocolType = "tcp"
	ProtocolTypeUDP       ProtocolType = "udp"
	ProtocolTypeSerial    ProtocolType = "serial"
	ProtocolTypeModbus    ProtocolType = "modbus"
	ProtocolTypeDART      ProtocolType = "dart"
	ProtocolTypeWayneDart ProtocolType = "wayne_dart"
)

// Controller 下位机控制器实体 - FCC连接的底层控制设备
type Controller struct {
	// 基础标识信息
	ID     string     `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	Name   string     `json:"name" db:"name" gorm:"type:varchar(255);not null"`
	Type   DeviceType `json:"type" db:"type" gorm:"type:varchar(100);not null"`
	Model  string     `json:"model" db:"model" gorm:"type:varchar(255)"`
	Vendor string     `json:"vendor" db:"vendor" gorm:"type:varchar(255)"`

	// 连接配置信息
	Protocol ProtocolType     `json:"protocol" db:"protocol" gorm:"type:varchar(50);not null"`
	Address  string           `json:"address" db:"address" gorm:"type:varchar(255);not null"`
	Config   ControllerConfig `json:"config" db:"config" gorm:"type:jsonb"`

	// 物理位置信息
	StationID string `json:"station_id" db:"station_id" gorm:"type:varchar(255);not null;index"`
	Location  string `json:"location" db:"location" gorm:"type:varchar(255)"`

	// 状态信息
	Status   DeviceStatus `json:"status" db:"status" gorm:"type:varchar(50);not null;index"`
	Health   DeviceHealth `json:"health" db:"health" gorm:"type:varchar(50);not null"`
	LastSeen *time.Time   `json:"last_seen" db:"last_seen"`

	// 元数据
	Metadata  map[string]interface{} `json:"metadata" db:"metadata" gorm:"type:jsonb"`
	CreatedAt time.Time              `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time              `json:"updated_at" db:"updated_at" gorm:"autoUpdateTime"`
	Version   int                    `json:"version" db:"version" gorm:"default:1"`

	// 关联设备
	Devices []Device `json:"devices,omitempty" gorm:"foreignKey:ControllerID"`
}

// ControllerConfig 下位机连接配置
type ControllerConfig struct {
	// TCP/UDP配置
	Host string `json:"host,omitempty"`
	Port int    `json:"port,omitempty"`

	// 串口配置
	SerialPort string `json:"serial_port,omitempty"`
	BaudRate   int    `json:"baud_rate,omitempty"`
	DataBits   int    `json:"data_bits,omitempty"`
	StopBits   int    `json:"stop_bits,omitempty"`
	Parity     string `json:"parity,omitempty"`

	// 协议参数
	Timeout      int `json:"timeout,omitempty"`       // 连接超时(毫秒)
	ReadTimeout  int `json:"read_timeout,omitempty"`  // 读超时(毫秒)
	WriteTimeout int `json:"write_timeout,omitempty"` // 写超时(毫秒)
	MaxRetries   int `json:"max_retries,omitempty"`   // 最大重试次数

	// DART协议特有配置
	AddressRange    *AddressRange `json:"address_range,omitempty"`     // 设备地址范围
	DLEEnabled      bool          `json:"dle_enabled,omitempty"`       // DLE透明处理
	CRCEnabled      bool          `json:"crc_enabled,omitempty"`       // CRC校验
	TXSequenceStart int           `json:"tx_sequence_start,omitempty"` // TX序列起始值

	// 扩展配置
	ExtraConfig map[string]interface{} `json:"extra_config,omitempty"`
}

// AddressRange 设备地址范围
type AddressRange struct {
	Min int `json:"min"` // 最小地址
	Max int `json:"max"` // 最大地址
}

// Device 终端设备实体 - 具体的业务设备
type Device struct {
	// 基础标识信息
	ID           string     `json:"id" db:"id" gorm:"primaryKey;type:varchar(255)"`
	Name         string     `json:"name" db:"name" gorm:"type:varchar(255);not null"`
	Type         DeviceType `json:"type" db:"type" gorm:"type:varchar(100);not null;index"`
	Model        string     `json:"model" db:"model" gorm:"type:varchar(255)"`
	Vendor       string     `json:"vendor" db:"vendor" gorm:"type:varchar(255)"`
	SerialNumber string     `json:"serial_number" db:"serial_number" gorm:"type:varchar(255)"`

	// 层级关系
	ControllerID string     `json:"controller_id" db:"controller_id" gorm:"type:varchar(255);not null;index"`
	Controller   Controller `json:"controller,omitempty" gorm:"foreignKey:ControllerID"`

	// 设备配置
	DeviceAddress int          `json:"device_address" db:"device_address" gorm:"not null"` // 设备地址(DART协议中的地址)
	Config        DeviceConfig `json:"config" db:"config" gorm:"type:jsonb"`

	// 物理位置信息
	StationID string `json:"station_id" db:"station_id" gorm:"type:varchar(255);not null;index"`
	IslandID  string `json:"island_id" db:"island_id" gorm:"type:varchar(255);index"`
	Position  string `json:"position" db:"position" gorm:"type:varchar(255)"` // 具体位置描述

	// 状态信息
	Status   DeviceStatus `json:"status" db:"status" gorm:"type:varchar(50);not null;index"`
	Health   DeviceHealth `json:"health" db:"health" gorm:"type:varchar(50);not null"`
	LastSeen *time.Time   `json:"last_seen" db:"last_seen"`

	// 能力信息
	Capabilities DeviceCapabilities `json:"capabilities" db:"capabilities" gorm:"type:jsonb"`

	// Nozzle关联 - Device作为Wayne Pump的映射
	Nozzles     []Nozzle `json:"nozzles,omitempty" gorm:"foreignKey:DeviceID"`
	NozzleCount int      `json:"nozzle_count" gorm:"-"` // 计算字段，不存储到数据库

	// Wayne协议特有配置
	MaxNozzles      int      `json:"max_nozzles" db:"max_nozzles" gorm:"default:15"`           // 最大喷嘴数(Wayne限制1-15)
	SupportedGrades []string `json:"supported_grades" db:"supported_grades" gorm:"type:jsonb"` // 支持的油品等级

	// 元数据
	Metadata  map[string]interface{} `json:"metadata" db:"metadata" gorm:"type:jsonb"`
	CreatedAt time.Time              `json:"created_at" db:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time              `json:"updated_at" db:"updated_at" gorm:"autoUpdateTime"`
	Version   int                    `json:"version" db:"version" gorm:"default:1"`
}

// DeviceConfig 设备配置
type DeviceConfig struct {
	// 通用配置
	Enabled     bool   `json:"enabled"`
	Description string `json:"description,omitempty"`

	// 泵设备特有配置
	PumpConfig *PumpConfig `json:"pump_config,omitempty"`

	// ATG设备特有配置
	ATGConfig *ATGConfig `json:"atg_config,omitempty"`

	// 显示屏设备特有配置
	DisplayConfig *DisplayConfig `json:"display_config,omitempty"`

	// 扩展配置
	ExtraConfig map[string]interface{} `json:"extra_config,omitempty"`
}

// PumpConfig 泵设备配置
type PumpConfig struct {
	NozzleCount    int                    `json:"nozzle_count"`            // 喷嘴数量
	SupportedFuels []string               `json:"supported_fuels"`         // 支持的油品类型
	MaxFlowRate    decimal.Decimal        `json:"max_flow_rate"`           // 最大流量(升/分钟)
	Precision      int                    `json:"precision"`               // 计量精度(小数位数)
	PriceConfig    map[string]PriceConfig `json:"price_config,omitempty"`  // 价格配置
	SafetyLimits   SafetyLimits           `json:"safety_limits,omitempty"` // 安全限制
}

// PriceConfig 价格配置
type PriceConfig struct {
	FuelType string          `json:"fuel_type"` // 油品类型
	Price    decimal.Decimal `json:"price"`     // 单价
	Currency string          `json:"currency"`  // 货币单位
}

// SafetyLimits 安全限制配置
type SafetyLimits struct {
	MaxAmount          decimal.Decimal `json:"max_amount"`           // 最大金额
	MaxVolume          decimal.Decimal `json:"max_volume"`           // 最大体积
	MaxTransactionTime int             `json:"max_transaction_time"` // 最大交易时间(秒)
}

// ATGConfig ATG设备配置
type ATGConfig struct {
	TankCount    int                    `json:"tank_count"`    // 油罐数量
	TankMappings map[string]TankMapping `json:"tank_mappings"` // 油罐映射
	ProbeTypes   []string               `json:"probe_types"`   // 探头类型
	AlarmLimits  map[string]AlarmLimit  `json:"alarm_limits"`  // 告警限制
	ReportConfig ReportConfig           `json:"report_config"` // 报告配置
}

// TankMapping 油罐映射
type TankMapping struct {
	TankID     string          `json:"tank_id"`     // 油罐ID
	FuelType   string          `json:"fuel_type"`   // 油品类型
	Capacity   decimal.Decimal `json:"capacity"`    // 容量
	ProbeDepth decimal.Decimal `json:"probe_depth"` // 探头深度
}

// AlarmLimit 告警限制
type AlarmLimit struct {
	LowLevel      decimal.Decimal `json:"low_level"`      // 低液位
	HighLevel     decimal.Decimal `json:"high_level"`     // 高液位
	CriticalLevel decimal.Decimal `json:"critical_level"` // 临界液位
}

// ReportConfig 报告配置
type ReportConfig struct {
	ReportInterval int      `json:"report_interval"` // 报告间隔(分钟)
	ReportTypes    []string `json:"report_types"`    // 报告类型
}

// DisplayConfig 显示屏配置
type DisplayConfig struct {
	ScreenSize   string            `json:"screen_size"`   // 屏幕尺寸
	Resolution   string            `json:"resolution"`    // 分辨率
	Languages    []string          `json:"languages"`     // 支持的语言
	DefaultTheme string            `json:"default_theme"` // 默认主题
	Layout       map[string]string `json:"layout"`        // 布局配置
}

// DeviceCapabilities 设备能力信息
type DeviceCapabilities struct {
	// 支持的命令类型
	SupportedCommands []string `json:"supported_commands"`

	// 支持的数据类型
	SupportedDataTypes []string `json:"supported_data_types"`

	// 协议版本
	ProtocolVersion string `json:"protocol_version,omitempty"`

	// 固件版本
	FirmwareVersion string `json:"firmware_version,omitempty"`

	// 硬件版本
	HardwareVersion string `json:"hardware_version,omitempty"`

	// 特性支持
	Features map[string]bool `json:"features,omitempty"`

	// 性能指标
	Performance map[string]interface{} `json:"performance,omitempty"`
}

// TableName 设置Controller表名
func (Controller) TableName() string {
	return "controllers"
}

// TableName 设置Device表名
func (Device) TableName() string {
	return "devices"
}

// IsOnline 检查设备是否在线
func (d *Device) IsOnline() bool {
	return d.Status == DeviceStatusOnline
}

// IsHealthy 检查设备是否健康
func (d *Device) IsHealthy() bool {
	return d.Health == DeviceHealthHealthy
}

// IsPump 检查是否为泵设备
func (d *Device) IsPump() bool {
	return d.Type == DeviceTypeFuelPump || d.Type == DeviceTypeDARTPump
}

// IsATG 检查是否为ATG设备
func (d *Device) IsATG() bool {
	return d.Type == DeviceTypeATG || d.Type == DeviceTypeDARTTank
}

// GetPumpConfig 获取泵配置
func (d *Device) GetPumpConfig() *PumpConfig {
	if d.Config.PumpConfig != nil {
		return d.Config.PumpConfig
	}
	return nil
}

// GetATGConfig 获取ATG配置
func (d *Device) GetATGConfig() *ATGConfig {
	if d.Config.ATGConfig != nil {
		return d.Config.ATGConfig
	}
	return nil
}

// SupportsCommand 检查设备是否支持指定命令
func (d *Device) SupportsCommand(command string) bool {
	for _, cmd := range d.Capabilities.SupportedCommands {
		if cmd == command {
			return true
		}
	}
	return false
}

// HasFeature 检查设备是否支持指定特性
func (d *Device) HasFeature(feature string) bool {
	if d.Capabilities.Features == nil {
		return false
	}
	return d.Capabilities.Features[feature]
}

// IsController 检查是否为下位机设备
func (c *Controller) IsController() bool {
	return c.Type == DeviceTypeController ||
		c.Type == DeviceTypeFuelController ||
		c.Type == DeviceTypePayController ||
		c.Type == DeviceTypeMonitorCtrl
}

// IsDARTProtocol 检查是否使用DART协议
func (c *Controller) IsDARTProtocol() bool {
	return c.Protocol == ProtocolTypeDART
}

// GetDeviceAddressRange 获取设备地址范围
func (c *Controller) GetDeviceAddressRange() *AddressRange {
	if c.Config.AddressRange != nil {
		return c.Config.AddressRange
	}
	// DART协议默认地址范围
	if c.IsDARTProtocol() {
		return &AddressRange{
			Min: 0x50, // 80 decimal
			Max: 0x6F, // 111 decimal
		}
	}
	return nil
}

// IsAddressInRange 检查设备地址是否在范围内
func (c *Controller) IsAddressInRange(address int) bool {
	addressRange := c.GetDeviceAddressRange()
	if addressRange == nil {
		return true
	}
	return address >= addressRange.Min && address <= addressRange.Max
}

// GetActiveNozzles 获取活跃的喷嘴列表
func (d *Device) GetActiveNozzles() []Nozzle {
	var activeNozzles []Nozzle
	for _, nozzle := range d.Nozzles {
		if nozzle.IsActive() {
			activeNozzles = append(activeNozzles, nozzle)
		}
	}
	return activeNozzles
}

// GetNozzleByNumber 根据喷嘴编号获取喷嘴
func (d *Device) GetNozzleByNumber(number byte) *Nozzle {
	for i := range d.Nozzles {
		if d.Nozzles[i].Number == number {
			return &d.Nozzles[i]
		}
	}
	return nil
}

// IsNozzleNumberValid 检查喷嘴编号是否有效 (Wayne协议限制1-15)
func (d *Device) IsNozzleNumberValid(number byte) bool {
	return number >= 1 && number <= 15 && number <= byte(d.MaxNozzles)
}

// GetNozzleCount 获取实际喷嘴数量
func (d *Device) GetNozzleCount() int {
	return len(d.Nozzles)
}

// GetEnabledNozzles 获取启用的喷嘴列表
func (d *Device) GetEnabledNozzles() []Nozzle {
	var enabledNozzles []Nozzle
	for _, nozzle := range d.Nozzles {
		if nozzle.IsEnabled {
			enabledNozzles = append(enabledNozzles, nozzle)
		}
	}
	return enabledNozzles
}

// HasActiveTransaction 检查是否有活跃的交易
func (d *Device) HasActiveTransaction() bool {
	for _, nozzle := range d.Nozzles {
		if nozzle.IsTransactionInProgress() {
			return true
		}
	}
	return false
}

// GetSelectedNozzle 获取当前选中的喷嘴
func (d *Device) GetSelectedNozzle() *Nozzle {
	for i := range d.Nozzles {
		if d.Nozzles[i].IsSelected {
			return &d.Nozzles[i]
		}
	}
	return nil
}

// IsWaynePump 检查是否为Wayne DART泵设备
func (d *Device) IsWaynePump() bool {
	return d.Type == DeviceTypeDARTPump &&
		d.Controller.Protocol == ProtocolTypeDART
}

// ValidateNozzleConfiguration 验证喷嘴配置是否符合Wayne协议
func (d *Device) ValidateNozzleConfiguration() error {
	if !d.IsWaynePump() {
		return nil // 非Wayne设备不需要验证
	}

	nozzleCount := d.GetNozzleCount()
	if nozzleCount > d.MaxNozzles {
		return NewValidationError("nozzle_count",
			fmt.Sprintf("喷嘴数量(%d)超过最大限制(%d)", nozzleCount, d.MaxNozzles),
			nozzleCount)
	}

	// 检查喷嘴编号唯一性
	numberSet := make(map[byte]bool)
	for _, nozzle := range d.Nozzles {
		if !nozzle.IsValidNumber() {
			return NewValidationError("nozzle_number",
				fmt.Sprintf("喷嘴编号%d不在有效范围(1-15)内", nozzle.Number),
				nozzle.Number)
		}

		if numberSet[nozzle.Number] {
			return NewValidationError("nozzle_number",
				fmt.Sprintf("喷嘴编号%d重复", nozzle.Number),
				nozzle.Number)
		}
		numberSet[nozzle.Number] = true

		// 验证单个喷嘴
		if err := nozzle.ValidateWayneProtocol(); err != nil {
			return err
		}
	}

	return nil
}
