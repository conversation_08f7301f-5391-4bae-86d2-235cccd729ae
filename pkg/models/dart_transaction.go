package models

import (
	"time"

	"github.com/shopspring/decimal"
)

/*
// DARTTransaction DART协议专用的交易模型
// 用于协议层和业务层之间的数据传输，避免循环依赖
// 标准 Transaction 模型包含大量业务字段，对于协议层来说是不必要的负担

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Protocol Layer │    │  Service Layer  │    │ Database Layer  │
│   (DART)        │    │   (Business)    │    │   (Persistent)  │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ DARTTransaction │◄──►│ TransactionConverter││ Transaction     │
│ (Protocol-specific)│ │ (Business logic)│    │ (Full model)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
*/
type DARTTransaction struct {
	// 基础标识
	ID               string                `json:"id"`
	Type             DARTTransactionType   `json:"type"`
	Status           DARTTransactionStatus `json:"status"`
	DeviceID         string                `json:"device_id"`
	NozzleID         string                `json:"nozzle_id"`
	OperatorID       string                `json:"operator_id,omitempty"`

	// 时间戳
	StartTime        time.Time  `json:"start_time"`
	EndTime          *time.Time `json:"end_time,omitempty"`
	LastUpdateTime   time.Time  `json:"last_update_time"`

	// 交易数据
	UnitPrice        decimal.Decimal `json:"unit_price"`
	Volume           decimal.Decimal `json:"volume"`
	Amount           decimal.Decimal `json:"amount"`
	FuelGradeID      string          `json:"fuel_grade_id,omitempty"`

	// DART协议特有字段
	PumpVolumeReading *decimal.Decimal `json:"pump_volume_reading,omitempty"`
	PumpAmountReading *decimal.Decimal `json:"pump_amount_reading,omitempty"`
	PumpReadingSource string           `json:"pump_reading_source,omitempty"`
	PumpReadingTime   *time.Time       `json:"pump_reading_time,omitempty"`

	// 协议状态
	ProtocolVersion   string            `json:"protocol_version,omitempty"`
	DeviceAddress     byte              `json:"device_address"`
	LastDCTransaction string            `json:"last_dc_transaction,omitempty"`
	ErrorCode         string            `json:"error_code,omitempty"`
	ErrorMessage      string            `json:"error_message,omitempty"`

	// 扩展数据
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// DARTTransactionType DART交易类型
type DARTTransactionType string

const (
	DARTTransactionTypeFuel    DARTTransactionType = "fuel"
	DARTTransactionTypeTest    DARTTransactionType = "test"
	DARTTransactionTypeService DARTTransactionType = "service"
)

// DARTTransactionStatus DART交易状态
type DARTTransactionStatus string

const (
	DARTTransactionStatusInitializing DARTTransactionStatus = "initializing"
	DARTTransactionStatusActive       DARTTransactionStatus = "active"
	DARTTransactionStatusCompleted    DARTTransactionStatus = "completed"
	DARTTransactionStatusCancelled    DARTTransactionStatus = "cancelled"
	DARTTransactionStatusError        DARTTransactionStatus = "error"
)

// IsCompleted 检查交易是否已完成
func (dt *DARTTransaction) IsCompleted() bool {
	return dt.Status == DARTTransactionStatusCompleted
}

// IsCancelled 检查交易是否已取消
func (dt *DARTTransaction) IsCancelled() bool {
	return dt.Status == DARTTransactionStatusCancelled
}

// IsActive 检查交易是否活跃
func (dt *DARTTransaction) IsActive() bool {
	return dt.Status == DARTTransactionStatusActive
}

// HasPumpReadings 检查是否有泵码数据
func (dt *DARTTransaction) HasPumpReadings() bool {
	return dt.PumpVolumeReading != nil || dt.PumpAmountReading != nil
}

// GetTotalAmount 获取总金额（优先使用泵码，否则使用计算值）
func (dt *DARTTransaction) GetTotalAmount() decimal.Decimal {
	if dt.PumpAmountReading != nil && !dt.PumpAmountReading.IsZero() {
		return *dt.PumpAmountReading
	}
	return dt.Amount
}

// GetTotalVolume 获取总体积（优先使用泵码，否则使用计算值）
func (dt *DARTTransaction) GetTotalVolume() decimal.Decimal {
	if dt.PumpVolumeReading != nil && !dt.PumpVolumeReading.IsZero() {
		return *dt.PumpVolumeReading
	}
	return dt.Volume
}

// ToStandardTransaction 转换为标准Transaction模型
func (dt *DARTTransaction) ToStandardTransaction() *Transaction {
	var fuelGradeID *string
	if dt.FuelGradeID != "" {
		fuelGradeID = &dt.FuelGradeID
	}

	tx := &Transaction{
		ID:            dt.ID,
		Type:          TransactionType(dt.Type),
		DeviceID:      dt.DeviceID,
		NozzleID:      dt.NozzleID,
		OperatorID:    dt.OperatorID,
		UnitPrice:     dt.UnitPrice,
		ActualVolume:  dt.GetTotalVolume(),
		ActualAmount:  dt.GetTotalAmount(),
		FuelGradeID:   fuelGradeID,
		CreatedAt:     dt.StartTime,
		UpdatedAt:     dt.LastUpdateTime,
	}

	// 状态转换
	switch dt.Status {
	case DARTTransactionStatusInitializing:
		tx.Status = TransactionStatusInitiated
	case DARTTransactionStatusActive:
		tx.Status = TransactionStatusFilling
	case DARTTransactionStatusCompleted:
		tx.Status = TransactionStatusCompleted
	case DARTTransactionStatusCancelled:
		tx.Status = TransactionStatusCancelled
	case DARTTransactionStatusError:
		tx.Status = TransactionStatusFailed
	default:
		tx.Status = TransactionStatusInitiated
	}

	// 设置结束时间
	if dt.EndTime != nil {
		tx.CompletedAt = dt.EndTime
	}

	// 设置泵码数据
	if dt.HasPumpReadings() {
		tx.StartPumpVolumeReading = dt.PumpVolumeReading
		tx.StartPumpAmountReading = dt.PumpAmountReading
		tx.PumpReadingSource = dt.PumpReadingSource
	}

	return tx
}

// FromStandardTransaction 从标准Transaction模型创建
func FromStandardTransaction(tx *Transaction) *DARTTransaction {
	var fuelGradeID string
	if tx.FuelGradeID != nil {
		fuelGradeID = *tx.FuelGradeID
	}

	dt := &DARTTransaction{
		ID:             tx.ID,
		Type:           DARTTransactionType(tx.Type),
		DeviceID:       tx.DeviceID,
		NozzleID:       tx.NozzleID,
		OperatorID:     tx.OperatorID,
		UnitPrice:      tx.UnitPrice,
		Volume:         tx.ActualVolume,
		Amount:         tx.ActualAmount,
		FuelGradeID:    fuelGradeID,
		StartTime:      tx.CreatedAt,
		LastUpdateTime: tx.UpdatedAt,
	}

	// 状态转换
	switch tx.Status {
	case TransactionStatusInitiated:
		dt.Status = DARTTransactionStatusInitializing
	case TransactionStatusFilling:
		dt.Status = DARTTransactionStatusActive
	case TransactionStatusCompleted:
		dt.Status = DARTTransactionStatusCompleted
	case TransactionStatusCancelled:
		dt.Status = DARTTransactionStatusCancelled
	case TransactionStatusFailed:
		dt.Status = DARTTransactionStatusError
	default:
		dt.Status = DARTTransactionStatusInitializing
	}

	// 设置结束时间
	if tx.CompletedAt != nil {
		dt.EndTime = tx.CompletedAt
	}

	// 设置泵码数据
	if tx.StartPumpVolumeReading != nil {
		dt.PumpVolumeReading = tx.StartPumpVolumeReading
	}
	if tx.StartPumpAmountReading != nil {
		dt.PumpAmountReading = tx.StartPumpAmountReading
	}
	if tx.PumpReadingSource != "" {
		dt.PumpReadingSource = tx.PumpReadingSource
	}

	return dt
} 