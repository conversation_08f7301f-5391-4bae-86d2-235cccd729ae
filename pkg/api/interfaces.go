package api

import (
	"context"
	"errors"
	"time"

	"fcc-service/pkg/models"
)

// DeviceManager 设备管理器接口 - 管理设备生命周期
type DeviceManager interface {
	// 设备注册和管理
	RegisterController(ctx context.Context, controller *models.Controller) error
	UnregisterController(ctx context.Context, controllerID string) error
	RegisterDevice(ctx context.Context, device *models.Device) error
	UnregisterDevice(ctx context.Context, deviceID string) error

	// 设备查询
	GetController(ctx context.Context, controllerID string) (*models.Controller, error)
	GetDevice(ctx context.Context, deviceID string) (*models.Device, error)
	ListControllers(ctx context.Context, filter ControllerFilter) ([]*models.Controller, error)
	ListDevices(ctx context.Context, filter DeviceFilter) ([]*models.Device, error)

	// 设备更新
	UpdateController(ctx context.Context, controller *models.Controller) error
	UpdateDevice(ctx context.Context, device *models.Device) error

	// 设备状态管理
	GetDeviceStatus(ctx context.Context, deviceID string) (*DeviceStatusInfo, error)
	UpdateDeviceStatus(ctx context.Context, deviceID string, status models.DeviceStatus) error
	UpdateDeviceHealth(ctx context.Context, deviceID string, health models.DeviceHealth) error
	GetControllerStatus(ctx context.Context, controllerID string) (*DeviceStatusInfo, error)

	// 控制器设备管理
	AddDeviceToController(ctx context.Context, controllerID, deviceID string) error
	RemoveDeviceFromController(ctx context.Context, controllerID, deviceID string) error

	// 设备命令
	SendCommand(ctx context.Context, command *models.Command) (*CommandResult, error)

	// Wayne设备相关方法
	ListWayneDevices(ctx context.Context) ([]*models.Device, error)
	GetWayneDevice(ctx context.Context, deviceID string) (*models.Device, error)
	GetPumpStatus(ctx context.Context, deviceID string) (interface{}, error)
	GetPumpTotals(ctx context.Context, deviceID string) (interface{}, error)

	// 设备发现
	DiscoverDevices(ctx context.Context, controllerID string) ([]*models.Device, error)
	RefreshDeviceTopology(ctx context.Context) error
}

// CommandExecutor 命令执行器接口 - 执行设备控制命令
type CommandExecutor interface {
	// 同步命令执行
	ExecuteCommand(ctx context.Context, req *CommandRequest) (*CommandResponse, error)

	// 异步命令执行
	ExecuteCommandAsync(ctx context.Context, req *CommandRequest) (*AsyncCommandResponse, error)
	GetCommandStatus(ctx context.Context, commandID string) (*CommandStatus, error)
	CancelCommand(ctx context.Context, commandID string) error

	// 批量命令执行
	ExecuteBatchCommands(ctx context.Context, requests []*CommandRequest) ([]*CommandResponse, error)
}

// StatusMonitor 状态监控器接口 - 监控设备状态和事件
type StatusMonitor interface {
	// 状态查询
	GetDeviceStatus(ctx context.Context, deviceID string) (*DeviceStatusInfo, error)
	GetDevicesStatus(ctx context.Context, deviceIDs []string) ([]*DeviceStatusInfo, error)

	// 状态订阅
	SubscribeDeviceStatus(ctx context.Context, deviceID string) (<-chan *StatusEvent, error)
	SubscribeDevicesStatus(ctx context.Context, deviceIDs []string) (<-chan *StatusEvent, error)
	UnsubscribeDeviceStatus(ctx context.Context, deviceID string) error

	// 事件通知
	PublishStatusEvent(ctx context.Context, event *StatusEvent) error

	// 历史数据
	GetStatusHistory(ctx context.Context, req *StatusHistoryRequest) ([]*DeviceStatusInfo, error)
}

// DeviceAdapter 设备适配器接口 - 抽象设备通信协议
type DeviceAdapter interface {
	// 连接管理
	Connect(ctx context.Context, config AdapterConfig) error
	Disconnect(ctx context.Context) error
	IsConnected() bool

	// 设备发现
	DiscoverDevices(ctx context.Context) ([]*models.Device, error)

	// 命令执行
	ExecuteCommand(ctx context.Context, deviceID string, command Command) (*CommandResult, error)

	// 状态获取
	GetDeviceStatus(ctx context.Context, deviceID string) (*DeviceStatusInfo, error)

	// 协议信息
	GetProtocolInfo() ProtocolInfo
	GetSupportedCommands() []string
}

// AdapterFactory 适配器工厂接口 - 创建设备适配器
type AdapterFactory interface {
	CreateAdapter(protocolType models.ProtocolType, config AdapterConfig) (DeviceAdapter, error)
	GetSupportedProtocols() []models.ProtocolType
	RegisterAdapter(protocolType models.ProtocolType, creator AdapterCreator) error
}

// AdapterCreator 适配器创建函数类型
type AdapterCreator func(config AdapterConfig) (DeviceAdapter, error)

// 数据类型定义

// DeviceFilter 设备过滤条件
type DeviceFilter struct {
	StationID    string              `json:"station_id,omitempty"`
	DeviceType   models.DeviceType   `json:"device_type,omitempty"`
	Status       models.DeviceStatus `json:"status,omitempty"`
	Health       models.DeviceHealth `json:"health,omitempty"`
	ControllerID string              `json:"controller_id,omitempty"`
	Limit        int                 `json:"limit,omitempty"`
	Offset       int                 `json:"offset,omitempty"`
}

// ControllerFilter 控制器过滤条件
type ControllerFilter struct {
	StationID      string              `json:"station_id,omitempty"`
	ControllerType models.DeviceType   `json:"controller_type,omitempty"`
	Status         models.DeviceStatus `json:"status,omitempty"`
	Health         models.DeviceHealth `json:"health,omitempty"`
	Protocol       models.ProtocolType `json:"protocol,omitempty"`
	Limit          int                 `json:"limit,omitempty"`
	Offset         int                 `json:"offset,omitempty"`
}

// CommandRequest 命令请求
type CommandRequest struct {
	CommandID   string                 `json:"command_id"`
	DeviceID    string                 `json:"device_id"`
	CommandType string                 `json:"command_type"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Timeout     time.Duration          `json:"timeout,omitempty"`
	Priority    CommandPriority        `json:"priority,omitempty"`
	Metadata    map[string]string      `json:"metadata,omitempty"`
}

// CommandResponse 命令响应
type CommandResponse struct {
	CommandID  string                 `json:"command_id"`
	DeviceID   string                 `json:"device_id"`
	Success    bool                   `json:"success"`
	Result     map[string]interface{} `json:"result,omitempty"`
	Error      string                 `json:"error,omitempty"`
	ExecutedAt time.Time              `json:"executed_at"`
	Duration   time.Duration          `json:"duration"`
}

// AsyncCommandResponse 异步命令响应
type AsyncCommandResponse struct {
	CommandID string        `json:"command_id"`
	Status    CommandStatus `json:"status"`
	QueuedAt  time.Time     `json:"queued_at"`
}

// CommandStatus 命令状态
type CommandStatus struct {
	CommandID   string       `json:"command_id"`
	DeviceID    string       `json:"device_id"`
	State       CommandState `json:"state"`
	Progress    int          `json:"progress"` // 0-100
	Result      interface{}  `json:"result,omitempty"`
	Error       string       `json:"error,omitempty"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	CompletedAt *time.Time   `json:"completed_at,omitempty"`
}

// CommandPriority 命令优先级
type CommandPriority string

const (
	CommandPriorityLow    CommandPriority = "low"
	CommandPriorityNormal CommandPriority = "normal"
	CommandPriorityHigh   CommandPriority = "high"
	CommandPriorityUrgent CommandPriority = "urgent"
)

// CommandState 命令状态枚举
type CommandState string

const (
	CommandStatePending   CommandState = "pending"
	CommandStateQueued    CommandState = "queued"
	CommandStateExecuting CommandState = "executing"
	CommandStateCompleted CommandState = "completed"
	CommandStateFailed    CommandState = "failed"
	CommandStateCancelled CommandState = "cancelled"
	CommandStateTimeout   CommandState = "timeout"
)

// DeviceStatusInfo 设备状态信息
type DeviceStatusInfo struct {
	DeviceID   string                 `json:"device_id"`
	DeviceType models.DeviceType      `json:"device_type"`
	Status     models.DeviceStatus    `json:"status"`
	Health     models.DeviceHealth    `json:"health"`
	Properties map[string]interface{} `json:"properties,omitempty"`
	LastUpdate time.Time              `json:"last_update"`
	LastSeen   *time.Time             `json:"last_seen,omitempty"`
}

// StatusEvent 状态事件
type StatusEvent struct {
	EventID   string                 `json:"event_id"`
	DeviceID  string                 `json:"device_id"`
	EventType StatusEventType        `json:"event_type"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source,omitempty"`
}

// StatusEventType 状态事件类型
type StatusEventType string

const (
	StatusEventTypeStatusChange   StatusEventType = "status_change"
	StatusEventTypeHealthChange   StatusEventType = "health_change"
	StatusEventTypePropertyChange StatusEventType = "property_change"
	StatusEventTypeConnected      StatusEventType = "connected"
	StatusEventTypeDisconnected   StatusEventType = "disconnected"
	StatusEventTypeError          StatusEventType = "error"
	StatusEventTypeWarning        StatusEventType = "warning"

	// 业务事件类型
	StatusEventTypeDeviceRestart StatusEventType = "device_restart"
	StatusEventTypeTransaction   StatusEventType = "transaction"
	StatusEventTypeFrameReceived StatusEventType = "frame_received"
	StatusEventTypePriceMismatch StatusEventType = "price_mismatch"
)

// StatusHistoryRequest 状态历史查询请求
type StatusHistoryRequest struct {
	DeviceID  string    `json:"device_id"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Limit     int       `json:"limit,omitempty"`
	Offset    int       `json:"offset,omitempty"`
}

// AdapterConfig 适配器配置
type AdapterConfig struct {
	Protocol   models.ProtocolType    `json:"protocol"`
	Connection ConnectionConfig       `json:"connection"`
	Options    map[string]interface{} `json:"options,omitempty"`
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	// TCP/UDP配置
	Host string `json:"host,omitempty"`
	Port int    `json:"port,omitempty"`

	// 串口配置
	SerialPort string `json:"serial_port,omitempty"`
	BaudRate   int    `json:"baud_rate,omitempty"`
	DataBits   int    `json:"data_bits,omitempty"`
	StopBits   int    `json:"stop_bits,omitempty"`
	Parity     string `json:"parity,omitempty"`

	// 通用配置
	Timeout      time.Duration `json:"timeout,omitempty"`
	ReadTimeout  time.Duration `json:"read_timeout,omitempty"`
	WriteTimeout time.Duration `json:"write_timeout,omitempty"`
	MaxRetries   int           `json:"max_retries,omitempty"`
}

// Command 设备命令
type Command struct {
	Type       string                 `json:"type"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
	Timeout    time.Duration          `json:"timeout,omitempty"`
}

// CommandResult 命令结果
type CommandResult struct {
	Success   bool                   `json:"success"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// ProtocolInfo 协议信息
type ProtocolInfo struct {
	Name        string   `json:"name"`
	Version     string   `json:"version"`
	Description string   `json:"description"`
	Features    []string `json:"features,omitempty"`
}

// 错误定义
var (
	ErrDeviceNotFound          = errors.New("device not found")
	ErrControllerNotFound      = errors.New("controller not found")
	ErrDeviceAlreadyExists     = errors.New("device already exists")
	ErrControllerAlreadyExists = errors.New("controller already exists")
)
