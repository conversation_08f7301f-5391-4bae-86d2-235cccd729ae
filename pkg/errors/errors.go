package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误码类型
type ErrorCode string

// 通用错误码
const (
	// 系统错误 (1000-1999)
	ErrCodeInternalError      ErrorCode = "INTERNAL_ERROR"
	ErrCodeNotImplemented     ErrorCode = "NOT_IMPLEMENTED"
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeTimeout            ErrorCode = "TIMEOUT"
	ErrCodeInvalidConfig      ErrorCode = "INVALID_CONFIG"

	// 请求错误 (2000-2999)
	ErrCodeInvalidRequest   ErrorCode = "INVALID_REQUEST"
	ErrCodeInvalidParameter ErrorCode = "INVALID_PARAMETER"
	ErrCodeMissingParameter ErrorCode = "MISSING_PARAMETER"
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"
	ErrCodeUnauthorized     ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden        ErrorCode = "FORBIDDEN"

	// 设备错误 (3000-3999)
	ErrCodeDeviceNotFound      ErrorCode = "DEVICE_NOT_FOUND"
	ErrCodeDeviceOffline       ErrorCode = "DEVICE_OFFLINE"
	ErrCodeDeviceUnavailable   ErrorCode = "DEVICE_UNAVAILABLE"
	ErrCodeDeviceNotSupported  ErrorCode = "DEVICE_NOT_SUPPORTED"
	ErrCodeDeviceConnectFailed ErrorCode = "DEVICE_CONNECT_FAILED"
	ErrCodeDeviceBusy          ErrorCode = "DEVICE_BUSY"
	ErrCodeDeviceError         ErrorCode = "DEVICE_ERROR"

	// 控制器错误 (4000-4999)
	ErrCodeControllerNotFound      ErrorCode = "CONTROLLER_NOT_FOUND"
	ErrCodeControllerOffline       ErrorCode = "CONTROLLER_OFFLINE"
	ErrCodeControllerUnavailable   ErrorCode = "CONTROLLER_UNAVAILABLE"
	ErrCodeControllerConnectFailed ErrorCode = "CONTROLLER_CONNECT_FAILED"

	// 命令错误 (5000-5999)
	ErrCodeCommandNotFound        ErrorCode = "COMMAND_NOT_FOUND"
	ErrCodeCommandNotSupported    ErrorCode = "COMMAND_NOT_SUPPORTED"
	ErrCodeCommandExecutionFailed ErrorCode = "COMMAND_EXECUTION_FAILED"
	ErrCodeCommandTimeout         ErrorCode = "COMMAND_TIMEOUT"
	ErrCodeCommandCancelled       ErrorCode = "COMMAND_CANCELLED"
	ErrCodeCommandInvalidParams   ErrorCode = "COMMAND_INVALID_PARAMS"

	// 协议错误 (6000-6999)
	ErrCodeProtocolNotSupported ErrorCode = "PROTOCOL_NOT_SUPPORTED"
	ErrCodeProtocolError        ErrorCode = "PROTOCOL_ERROR"
	ErrCodeProtocolTimeout      ErrorCode = "PROTOCOL_TIMEOUT"
	ErrCodeProtocolChecksum     ErrorCode = "PROTOCOL_CHECKSUM_ERROR"
	ErrCodeProtocolFormat       ErrorCode = "PROTOCOL_FORMAT_ERROR"

	// 适配器错误 (7000-7999)
	ErrCodeAdapterNotFound      ErrorCode = "ADAPTER_NOT_FOUND"
	ErrCodeAdapterCreateFailed  ErrorCode = "ADAPTER_CREATE_FAILED"
	ErrCodeAdapterNotConnected  ErrorCode = "ADAPTER_NOT_CONNECTED"
	ErrCodeAdapterConnectFailed ErrorCode = "ADAPTER_CONNECT_FAILED"

	// 数据错误 (8000-8999)
	ErrCodeDataNotFound  ErrorCode = "DATA_NOT_FOUND"
	ErrCodeDataConflict  ErrorCode = "DATA_CONFLICT"
	ErrCodeDataCorrupted ErrorCode = "DATA_CORRUPTED"
	ErrCodeDatabaseError ErrorCode = "DATABASE_ERROR"
	ErrCodeCacheError    ErrorCode = "CACHE_ERROR"

	// 连接错误 (9000-9999)
	ErrCodeConnectionFailed  ErrorCode = "CONNECTION_FAILED"
	ErrCodeConnectionTimeout ErrorCode = "CONNECTION_TIMEOUT"
	ErrCodeConnectionClosed  ErrorCode = "CONNECTION_CLOSED"
	ErrCodeResourceExhausted ErrorCode = "RESOURCE_EXHAUSTED"
)

// ErrorSeverity 错误严重程度
type ErrorSeverity string

const (
	SeverityInfo     ErrorSeverity = "info"
	SeverityWarning  ErrorSeverity = "warning"
	SeverityError    ErrorSeverity = "error"
	SeverityCritical ErrorSeverity = "critical"
)

// FCCError FCC统一错误类型
type FCCError struct {
	Code       ErrorCode              `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	Severity   ErrorSeverity          `json:"severity"`
	StatusCode int                    `json:"status_code"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	Cause      error                  `json:"-"` // 原始错误，不序列化
}

// Error 实现error接口
func (e *FCCError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap 返回原始错误
func (e *FCCError) Unwrap() error {
	return e.Cause
}

// WithMetadata 添加元数据
func (e *FCCError) WithMetadata(key string, value interface{}) *FCCError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
	return e
}

// WithCause 设置原始错误
func (e *FCCError) WithCause(cause error) *FCCError {
	e.Cause = cause
	return e
}

// NewInternalError 创建内部错误
func NewInternalError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeInternalError,
		Message:    message,
		Severity:   SeverityError,
		StatusCode: http.StatusInternalServerError,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewDeviceNotFoundError 创建设备未找到错误
func NewDeviceNotFoundError(deviceID string) *FCCError {
	return &FCCError{
		Code:       ErrCodeDeviceNotFound,
		Message:    "Device not found",
		Details:    fmt.Sprintf("Device ID: %s", deviceID),
		Severity:   SeverityWarning,
		StatusCode: http.StatusNotFound,
		Metadata:   map[string]interface{}{"device_id": deviceID},
	}
}

// NewValidationError 创建验证错误
func NewValidationError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeValidationFailed,
		Message:    message,
		Severity:   SeverityWarning,
		StatusCode: http.StatusBadRequest,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewDatabaseError 创建数据库错误
func NewDatabaseError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeDatabaseError,
		Message:    message,
		Severity:   SeverityError,
		StatusCode: http.StatusInternalServerError,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewNotFoundError 创建资源未找到错误
func NewNotFoundError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeDataNotFound,
		Message:    message,
		Severity:   SeverityWarning,
		StatusCode: http.StatusNotFound,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewConnectionError 创建连接错误
func NewConnectionError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeConnectionFailed,
		Message:    message,
		Severity:   SeverityError,
		StatusCode: http.StatusServiceUnavailable,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewTimeoutError 创建超时错误
func NewTimeoutError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeTimeout,
		Message:    message,
		Severity:   SeverityWarning,
		StatusCode: http.StatusRequestTimeout,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewResourceError 创建资源错误
func NewResourceError(message string, details ...string) *FCCError {
	err := &FCCError{
		Code:       ErrCodeResourceExhausted,
		Message:    message,
		Severity:   SeverityError,
		StatusCode: http.StatusTooManyRequests,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// IsConnectionError 检查是否为连接错误
func IsConnectionError(err error) bool {
	if fccErr, ok := err.(*FCCError); ok {
		return fccErr.Code == ErrCodeConnectionFailed ||
			fccErr.Code == ErrCodeConnectionTimeout ||
			fccErr.Code == ErrCodeConnectionClosed
	}
	return false
}

// IsTimeoutError 检查是否为超时错误
func IsTimeoutError(err error) bool {
	if fccErr, ok := err.(*FCCError); ok {
		return fccErr.Code == ErrCodeTimeout ||
			fccErr.Code == ErrCodeConnectionTimeout ||
			fccErr.Code == ErrCodeCommandTimeout ||
			fccErr.Code == ErrCodeProtocolTimeout
	}
	return false
}

// AsFCCError 转换为FCC错误
func AsFCCError(err error) *FCCError {
	if err == nil {
		return nil
	}

	if fccErr, ok := err.(*FCCError); ok {
		return fccErr
	}

	// 包装为内部错误
	return &FCCError{
		Code:       ErrCodeInternalError,
		Message:    "Internal system error",
		Details:    err.Error(),
		Severity:   SeverityError,
		StatusCode: http.StatusInternalServerError,
		Cause:      err,
	}
}
