### 3.1.10 Suspend Request (\*)

Transaction: CD14

Format:

<html><body><table><tr><td>MNEMONIC NUMBER</td><td colspan="2">OF BYTES</td></tr><tr><td>TRANS</td><td>1</td><td>0EH</td></tr><tr><td>LNG</td><td>1</td><td>Number of data bytes in the transaction</td></tr><tr><td>NOZ</td><td>1</td><td>Nozzle number (0-0FH)</td></tr></table></body></html>

NOZ specifies the logical nozzle number that is suspended from filling. The number is only used for satellite pumps and should normally be 0.

This transaction is supported only by SAT pumps with full implementation of Dart.

### 3.1.11 Resume Request (\*)

Transaction: CD15

Format:

TRANS 0FH LNG Number of data bytes in the transaction NOZ Nozzle number (0-0FH)

NOZ specifies the logical nozzle number that is resumed for filling. The number is only used for satellite pumps and should normally be 0.

This transaction is supported only by SAT pumps with full implementation of Dart.

### 3.1.12 Request Total Counters (\*)

Transaction: CD101

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 65H   
LNG 1 Number of data bytes in the transaction   
COUN 1 Total Counter number, where COUN has meaning as described in table below. (Also see specification of DC101)

<html><body><table><tr><td>COUN</td><td>01H</td><td>02H</td><td>03H</td><td>04H</td><td>05H</td><td>06H</td><td>07H</td><td>08H</td><td>09H</td></tr><tr><td>Volume</td><td>LN1</td><td>LN2</td><td>LN3</td><td>LN4</td><td>LN5</td><td>LN6</td><td>LN7</td><td>LN8</td><td>ELN1-8</td></tr><tr><td>Vol. 1$ meter</td><td>LN1</td><td>LN2</td><td>LN3</td><td>LN4</td><td>LN5</td><td>LN6</td><td>LN7</td><td>LN8</td><td>0</td></tr><tr><td>Vol. 2$ meter</td><td>LN1</td><td>LN2</td><td>LN3</td><td>LN4</td><td>LN5</td><td>LN6</td><td>LN7</td><td>LN8</td><td>0</td></tr><tr><td>COUN</td><td>11H</td><td>12H</td><td>13H</td><td>14H</td><td>15H</td><td>16H</td><td>17H</td><td>18H</td><td>19H</td></tr><tr><td>Amount</td><td>LN1</td><td>LN2</td><td>LN3</td><td>LN4</td><td>LN5</td><td>LN6</td><td>LN7</td><td>LN8</td><td> LN1-8</td></tr><tr><td>No. of Fill.</td><td>LN1</td><td>LN2</td><td>LN3</td><td>LN4</td><td>LN5</td><td>LN6</td><td>LN7</td><td>LN8</td><td>LN1-8</td></tr><tr><td>Not used</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0 </td><td></td><td>0</td><td>0</td><td>0</td></tr></table></body></html>

LN \(=\) Logical Nozzle.

This transaction is supported only by pumps with full implementation of Dart.

## 3.2 Transactions from pump to site controller

### 3.2.1 Pump status

Transaction: DC1

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 01H   
LNG 1 Number of data bytes in the transaction   
STATUS 1 Pump status

This transaction is sent by the pump if the status is changed or if the pump receives the command 'RETURN STATUS’.

The pump can have the following status:

0 PUMP NOT PROGRAMMED   
1 RESET   
2 AUTHORIZED   
4 FILLING   
5 FILLING COMPLETED   
6 MAX AMOUNT/VOLUME REACHED   
7 SWITCHED OFF   
8 SUSPENDED \* (fuelling point suspended)

### 3.2.2 Filled volume and amount

Transaction: DC2

Format:

MNEMONIC NUMBER OF BYTES   
TRANS 1 02H   
LNG 1 Number of data bytes in the transaction   
VOL 4 Filled volume   
AMO 4 Filled amount

VOL and AMO are sent in packed BCD. MSB is sent in the first byte.

This transaction is sent by the pump at change of a value or if the pump receives the command RETURN FILLING INFORMATION.

### 3.2.3 Nozzle status and filling price

Transaction: DC3

Format:

<html><body><table><tr><td>MNEMONIC NUMBER</td><td colspan="2">OF BYTES</td></tr><tr><td>TRANS</td><td>1</td><td>03H</td></tr><tr><td>LNG</td><td>1</td><td>Number of data bytes in the transaction</td></tr><tr><td>PRI</td><td>3</td><td>Filling price</td></tr><tr><td>NOZIO</td><td>1</td><td></td></tr></table></body></html>

PRI is the price used by the pump for calculation of filled amount. It is sent in packed BCD, MSB first.

NOZIO bits 0-3 contain selected nozzle number.

NOZIO bit 4 contains nozzle in/out information.

\[
\begin{array} { l } { 0 = \mathrm { i n } } \\ { 1 = \mathrm { o u t } } \end{array}
\]

This transaction is sent by the pump if the status is changed or if the pump receives the command 'RETURN STATUS' or ‘RETURN FILLING INFORMATION’.

Example:

NOZIO Meaning 02H Nozzle 2 selected, nozzle in 12H Nozzle 2 selected, nozzle out 10H No nozzle selected, nozzle out. (This is possible for a blending pump.)

There must never be more than one selected logical nozzle number. If two nozzles are taken out simultaneously, the pump determines which logical nozzle number is selected. The site controller will use the last received logical nozzle number.

### 3.2.4 Alarm Code (\*)

Transaction: DC5

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 05H   
LNG 1 Number of data bytes in the transaction   
ALARM 1 Alarm code 01H CPU reset 03H RAM error 04H PROM checksum error 06H Pulser error 07H Pulser current error 09H Emergency stop 0AH Power failure 0BH Pressure lost 0CH Blend ratio error 0DH Low leak error 0EH High leak error 0FH Hose leak error 10H VR monitor, error reset 11H VR monitor, 10 consecutive errors 12H VR monitor, shut down pump 13H VR monitor, internal error 14H Low Tank level detected\*\* 15H Low Tank level Ok \*\* 16H RESERVED 17H RESERVED 18H RESERVED 19H VR control error 20H VR control error consecutive

This transaction is sent by the pump if an alarm is generated or if the pump receives the command 'RETURN STATUS'.

This transaction is supported only by pumps with full implementation of Dart.

\*\* The Tank low level is alarm is only available on the Italian Market, and it requires special hardware in the pumps. Error codes 14H and 15H are also used for application specific errors on Asian markets.

### 3.2.5 Pump Parameters (\*)

Transaction: DC7

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 07H   
LNG 1 Number of data bytes in the transaction   
RES 22 Not used   
DPVOL 1 Number of decimals in volume (0-8)   
DPAMO 1 Number of decimals in amount (0-8)   
DPUNP 1 Number of decimals in unit price (0-4)   
RES 5 Not used   
MAMO 4 Maximum amount   
RES 2 Not used   
GRADE 15 Existing grade per nozzle number

MAMO is sent in packed BCD with MSB in first byte.

This transaction is sent by the pump if SET PUMP PARAMETERS is received or if the pump receives the command 'RETURN PUMP PARAMETERS

This transaction is supported only by pumps with full implementation of Dart.

![](images/a50aeef33af1b42779b71a4e9cea5d3a1a9d1a8a9673be0832fd5651d3d23ee2.jpg)

PID is sent in packed BCD, MSB first.

The pump sends this transaction if command ‘RETURN PUMP IDENTITY’ is received.

### 3.2.7 Suspend Reply (\*)

Transaction: DC14

Format:

MNEMONIC NUMBER OF BYTES

<html><body><table><tr><td>TRANS 1</td><td>0EH</td></tr><tr><td>LNG 1</td><td>Number of data bytes in the transaction</td></tr><tr><td>NOZ 1</td><td>Nozzle number (from request).</td></tr></table></body></html>

The pump sends this transaction if SUSPEND REQUEST is received.

This transaction is supported only by SAT pumps with full implementation of Dart.

### 3.2.8 Resume Reply (\*)

Transaction: DC15

Format:

TRANS 1 0FH LNG 1 Number of data bytes in the transaction NOZ 1 Nozzle number (from request).

The pump sends this transaction if RESUME REQUEST is received.

This transaction is supported only by SAT pumps with full implementation of Dart.

### 3.2.9 Total Counters (\*)

Transaction: DC101

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 65H   
LNG 1 Number of data bytes in the transaction   
COUN 1 Logical Nozzle number 01H-09H and 11H-19H   
TOTVAL 5 \(\mathrm { C O U N } = 0 1 \mathrm { H } { \cdot } 0 8 \mathrm { H }\) : Volume Totals for logical nozzle. \(\mathrm { C O U N } = 0 9 \mathrm { H }\) : ΣLN1-8 Volume (sum of all logical nozzle volume totals). \(\mathrm { C O U N } = 1 1 \mathrm { H } { - } 1 8 \mathrm { H }\) : Amount Totals for logical nozzle. \(\mathrm { C O U N } = 1 9 \mathrm { H }\) : ΣLN1-8 Amount (sum of all logical nozzle amount totals) .   
TOTM1 / 5 \(\mathrm { C O U N } = 0 1 \mathrm { H } { \cdot } 0 8 \mathrm { H }\) : Primary meter Volume totals for logi  
NOFILL cal nozzle. \(\mathrm { C O U N } = 0 9 \mathrm { H }\) : Not used shall be zero (0). \(\mathrm { C O U N } = 1 1 \mathrm { H } { - } 1 8 \mathrm { H }\) : Number of fillings for logical nozzle. \(\mathrm { C O U N } { = } 1 9 \mathrm { H }\) : Total no of fillings for whole pump.   
TOTM2 5 COUN \(\neqq\) 01H-08H: Secondary meter Volume totals for logical nozzle. COUN \(\uplus\) 09H: Not used shall be zero (0). COUN = 11H-19H: Not used and shall be zero (0).

Total counters are sent as packed BCD format with MSB first.

The pump sends this transaction if Request Total Counters is received.

This transaction is supported only by pumps with full implementation of Dart.

### 3.2.10 IFSF Stand Alone Mode (\*)

Transaction: DC102

Format:

MNEMONIC NUMBER OF BYTES

<html><body><table><tr><td>TRANS 1 1</td><td>66H</td></tr><tr><td>LNG MODE</td><td>Number of data bytes in the transaction IFSF mode.</td></tr><tr><td>1</td><td>00H = Stand Alone Disabled</td></tr><tr><td>LOCA 1</td><td>01H = Stand Alone Enabled Local Authorise 00H = Button has not been pressed</td></tr></table></body></html>

The message is used to inform ISFS-converter if pump is operating in Stand Alone or not and if the Local Authorise button has been pressed.

Pump will send this message only if configured to do so (parameter in pump). If the pump is configured to send this message it sends it on status change of any of the two parameters (MODE and LOCA). The message will also be sent when system requests pump parameters \(( \mathrm { D C l } = 0 2 \mathrm { H } )\) ).

This transaction is supported only by pumps with full implementation of Dart.

### 3.2.11 Pump Unit Prices (\*)

Transaction: DC103

Format:

<html><body><table><tr><td>MNEMONIC  NUMBER</td><td colspan="2">OF BYTES</td></tr><tr><td>TRANS</td><td>1</td><td>67H</td></tr><tr><td>LNG</td><td>1</td><td>Number of data bytes in the transaction</td></tr><tr><td>PRI1 ..</td><td>3</td><td>Price</td></tr><tr><td>PRIn</td><td>3</td><td></td></tr></table></body></html>

The message is used to inform ISFS-converter of current pump prices when operating in IFSF Stand Alone Mode.

Pump will send this message only if configured to do so (parameter in pump). The pump send this message as response to command 0FH (RETURN PRICES OF ALL CURRENT GRADES) in CD1.

This transaction is supported only by pumps with full implementation of Dart.

![](images/fa5f7957a32ff504ef576b6906e69f16d6389948b3325e26004b55d3ac0991bd.jpg)

# EXAMPLES

## 4.1 Clear display when customer pays

Pump display is cleared when the customer pays the filling. Authorize is made when next customer takes out a nozzle or selects a grade.

Transaction   
To disp. To cen- Comment tral The pump is in status FILLING COMPLETE.   
CD1 Command RESET, the customer has paid. DC1 Status RESET. Next customer arrives. DC3 Grade selected.   
CD2 Allowed nozzle numbers. Only needed if different from previous filling.   
CD3 Preset volume. Note that preset values are set to default by the command RESET.   
CD1 Command AUTHORIZE. DC1 Status AUTHORIZED. DC3 Nozzle out. DC1 Status FILLING. DC2 Filled volume and amount. : DC2 DC3 Nozzle in. DC1 Status FILLING COMPLETE.

## 4.2 Clear display at start of filling

Pump display is cleared when a filling is started. Authorize is made when the customer pays the filling.

Transaction To disp. To central Comment

The pump is in status FILLING COMPLETE Next customer arrives. DC3 Nozzle out.   
CD1 Command RESET. The pump clears the display. DC1 Status RESET.   
CD1 Command AUTHORIZE. DC1 Status AUTHORIZED. DC1 Status FILLING. DC2 Filled volume and amount. : DC2 DC3 Nozzle in. DC1 Status FILLING COMPLETE.

![](images/e0bbd0794bbd1759cc498921a39b2f5715a026c566fa6a8aaf1302eeb0dd1d7c.jpg)

## 4.3 Price change

If a pump only is working with one filling type, it is not necessary to send out prices for each filling. In this example is the price known first then the nozzle is taken out.

Transaction   
To disp. To cen- Comment tral The pump is in status FILLING COMPLETE. Next customer arrives. DC3 Nozzle out.   
CD5 Price updating.   
CD2 Allowed nozzle numbers.   
CD1 Command RESET. DC1 Status RESET. DC3 Grade selected.   
CD2 Allowed nozzle numbers. Only needed if different from previous filling.   
CD3 Preset volume. Note that preset values must be sent after the command RESET.   
CD1 Command AUTHORIZE. DC1 Status AUTHORIZED. DC1 Status FILLING. DC2 Filled volume and amount. : DC2 DC3 Nozzle in. DC1 Status FILLING COMPLETE.

# 5 LIMITATIONS

Maximum 32 pumps

Volume 99999999   
Amount 99999999   
Filling price 999999   
Nozzle number 1-0FH   
Blended products 6   
Number of grades on one pump 15

![](images/5418bd06cee131473e0c5eff8bb1c6ebc6cae68da74b39ee5b50d10ad8c69e77.jpg)

<html><body><table><tr><td> Revision</td><td>Changes</td><td>WM041550 Rev</td></tr><tr><td>2.10</td><td>Initial check-in to PDM system</td><td>01</td></tr><tr><td>2.11</td><td>Part 1. - Added description on RESERVED and note with reference to separate application specifications. Par 3. - Added note on older versions - Added RESERVED application specific messages</td><td>02</td></tr></table></body></html>

![](images/7ba3aa3fe0069206247e9cbe02b299f431340b53874f079fec402a9dabc4587d.jpg)