# PROTOCOL SPECIFICATION

DART PUMP INTERFACE

2.1 2010-12-20

This document is the property of <PERSON><PERSON><PERSON>. It is not to be used or duplicated without written permission of the owner, and is not to be used in any way inconsistent with the purpose for which it is loaned. Dresser Wayne AB shall not be liable for technical or editorial errors or omissions which may appear in this document. It also retains the right to make changes to this specification at any time without prior notice.

TABLE OF CONTENTS

1 GENERAL   
1.1 Protocol levels .. 1   
2 PRINCIPLES. 2   
2.1 Control of pumps.. 2   
2.1.1 Commands to a pump... 2   
2.1.2 Pump status... 3   
2.1.3 Command and status flow ... . 4   
2.2 Data from the pump.. 7   
2.3 Handling volume and amount . 7   
2.4 Handling prices... .. 8   
2.5 Grades and nozzle numbers... . 8   
2.6 Preset amount/volume . .. 8   
2.7 Pump identity... .. 9   
3 TRANSACTIONS... .. 10   
3.1 Transactions from site controller to pump... . 13   
3.1.1 Command to pump ... .... 13   
3.1.2 Allowed nozzle numbers ... .. 14   
3.1.3 Preset volume .. .. 14   
3.1.4 Preset amount .. .. 15   
3.1.5 Price update .. .. 15   
3.1.6 Command to Output (\*)..... .. 16   
3.1.7 Request Total Counters (\*) .. 16   
3.1.8 Set pump parameters (\*) . 17   
3.1.9 Set filling type (\*). . 17   
3.1.10 Suspend Request (\*) . . 18   
3.1.11 Resume Request (\*). . 18   
3.1.12 Request Total Counters (\*).. . 19   
3.2 Transactions from pump to site controller. . 20   
3.2.1 Pump status.. .. 20   
3.2.2 Filled volume and amount. . 20   
3.2.3 Nozzle status and filling price .. . 21   
3.2.4 Alarm Code (\*)... 22   
3.2.5 Pump Parameters (\*) . . 23   
3.2.6 Pump identity.. . 23   
3.2.7 Suspend Reply (\*) . . 24   
3.2.8 Resume Reply (\*) . . 24   
3.2.9 Total Counters (\*).. 25   
3.2.10 IFSF Stand Alone Mode (\*) . . 26   
3.2.11 Pump Unit Prices (\*) ... . 27   
4 EXAMPLES. . 28   
4.1 Clear display when customer pays .. . 28   
4.2 Clear display at start of filling . . 29   
4.3 Price change.. 30   
5 LIMITATIONS .. . 31

This document describes the protocol between a DRESSER WAYNE pump and a site controller at a petrol station. Only the protocol above the line protocol is described. From the line protocol, a device address and a buffer with data are received. All error handling e.g. CRC and parity check is made in the line protocol.

Each pump has a device address (50H-6FH). A pump shall handle a block if the block is addressed to the pump. The test of device address is made in the line protocol.

The line protocol is described in the document "DART-LINE PROTOCOL SPECIFICATION".

The electronic interface is described in the document "DART-LINE PROTOCOL SPECIFICATION" and in the flow chart "DART CONFIGURATION".

Application specific messages and data, listed as “RESERVED” in this specification, are described in separate application specifications.

## 1.1 Protocol levels

The protocol is divided into 3 levels.

Level 3 Application level.   
Level 2 Line protocol level.   
Level 1 Electronic level.

![](images/848ed4258efaa9fc5eab852c3acccc63aeaca41cfb742b37a52dad3c2dcb8827.jpg)

Level 2 is handling is polling of devices and transport of blocks that are created by level 3. Level 2 cheeks that a block is transmitted correctly. The check is made with CRC, parity and block sequence number. If an error occurs, retransmission is handled by level 2.

At level 3 blocks are transmitted between site controller and pumps. A block can contain one or more transactions that are specified in this document.

Example of a block sent on the communication line.

<html><body><table><tr><td colspan="4"></td><td>TRANS</td><td colspan="9">DATA</td></tr><tr><td>ADR</td><td></td><td>CTRL</td><td>NO</td><td>LNG</td><td>1</td><td></td><td>2</td><td></td><td>CRC-1CRC-2</td><td></td><td>ETX</td><td>SF</td></tr><tr><td></td><td colspan="2"></td><td colspan="9"></td></tr><tr><td colspan="3">Created and sent by level 2.</td><td colspan="9">Created by level 3. Sent by level 2. Created and sent by level 2..</td></tr></table></body></html>

# PRINCIPLES

## 2.1 Control of pumps

### 2.1.1 Commands to a pump

The site controller can send the following commands to a pump.

RETURN STATUS   
RETURN PUMP IDENTITY   
RETURN FILLING INFORMATION   
RESET   
AUTHORIZE   
SUSPEND   
RESUME   
STOP   
SWITCH OFF

#### RETURN STATUS:

The pump shall return status information. This is a global request that will cause the pump to return transaction "Pump status" and "Nozzle status and filling price". The site controller uses this command when a pump is connected the first time or after a communication fault. During normal operation the pump sends transaction "Pump status" and transaction "Nozzle status and filling price" at change of status.

#### RETURN PUMP IDENTITY:

The pump shall return pump identity in transaction "Pump identity". The site controller will only handle pumps that are tested together with the site controller. It is not necessary to use this command in normal operation.

### RESET:

The pump clears the display and variables such as filled volume and filled amount.

### AUTHORIZE:

Authorize of the pump. The pump shall now be ready for start of filling. The command can come before or after a nozzle is taken out.

### SUSPEND:

This command is used to temporarily stop a filling e.g. when contact with a Vehicle Identification Device is lost. It is a command that will stop the filling, but the main pump status will still be AUTHORIZED or FILLING.

### RESUME:

This command is used to reactivate the pump again e.g. when the contact is re-established with a Vehicle Identification Device.

STOP:

The pump goes to filling completed. If a filling was going on, it is stopped.

#### SWITCH OFF:

Switch off the pump. The command is used when the station is closing or if there is an error in the pump.

### 2.1.2 Pump status

Pump status is sent to the site controller with transaction "Pump status".

The pump has the following status:

PUMP NOT PROGRAMMED   
RESET   
AUTHORIZED (SUSPENDED)   
FILLING (SUSPENDED)   
FILLING COMPLETED   
MAX AMOUNT/VOLUME REACHED   
SWITCHED OFF

A status change is caused by a command from the site controller or by an action in the pump e.g. nozzle in.

![](images/b53ef377b0b6cd02ddbab09450be882be68e6d82a4ed07be25894307578e2020.jpg)

### 2.1.3 Command and status flow

In the following table the possible commands in different pump status are listed.

#### Command

#### Normally received in status

#### Action

RETURN STATUS

All

Return status

RETURN FILLING INFORMATION

RESET   
AUTHORIZED (SUSPENDED)   
FILLING (SUSPENDED)   
FILLING COMPLETED   
MAX AMOUNT/VOLUME   
REACHED

Return filling information

RESET

FILLING COMPLETED MAX AMOUNT/VOLUME REACHED

Amount, volume and alarm are cleared. The light is switched on.

AUTHORIZE

RESET

If a grade is selected and allowed, then the pump motor is turned on.

SUSPEND

AUTHORIZED FILLING

The pump valve is closed.

RESUME

SUSPENDED

The pump valve is opened.

STOP

RESET   
AUTHORIZED (SUSPENDED)   
FILLING (SUSPENDED)   
MAX AMOUNT/VOLUME   
REACHED

The pump motor is turned off.

The light and pump motor are turned off.

In the following table the possible status changes are listed.

![](images/8383adf2a1959672b12d5f81a0ca34a56976fb6447a3c4d88dc6834ad02c80ba.jpg)

![](images/846a60634230f6b9b3c76868ce78ad647c076cf874009eacc43292abe1d2ed26.jpg)

## 2.2 Data from the pump

Data is sent spontaneously at a change or at a request from the site controller.

Spontaneous data:

Status change   
Nozzle in/out   
Select logical nozzle number   
Change of filled volume or amount

Pump status

Pump status is sent to the site controller with transaction "Pump status".

The pump has the following status:

PUMP NOT PROGRAMMED   
RESET   
AUTHORIZED (SUSPENDED)   
FILLING (SUSPENDED)   
FILLING COMPLETED   
MAX AMOUNT/VOLUME REACHED   
SWITCHED OFF

A status change is caused by a command from the site controller or by an action in the pump e.g. nozzle in.

## 2.3 Handling volume and amount

It is important that the display at the site controller has the same value as the display at the pump. The site controller does not make any calculations, it takes the volume and amount that is received from the pump and put it on the display. The pump sends the data only when a value is changed or if the site controller does request it. The data is sent in transaction "Filled volume and amount". The change normally occurs during a filling. Note that at reset of volume and amount counters, the transaction is sent with value \(\scriptstyle \longmapsto 0\) .

For safety the site controller may request the pump to send transaction "Filled volume and amount" at end of filling.

## 2.4 Handling prices

The unit price that shall be used for a filling is sent from the site controller with transaction "Price update". In this document it is called filling price. The pump receives one filling price for each nozzle number. If the pump has more than one logical nozzle number, there must be prices for all nozzles, otherwise the whole transaction is ignored. If there are more prices than nozzles, the prices are accepted but the prices without nozzle are ignored.

The filling price is sent only when the price is changed or if a pump is powered on or zeroized. A zeroized pump stays in status “pump not programmed” until a unit price has been received.

The pump does not accept a new unit price after a filling has started.

## 2.5 Grades and nozzle numbers

The pump shall work only with nozzle number. The site controller handles the connection grade-nozzle number.

In this document 'nozzle number' means logical nozzle number and not a physical nozzle. A blending pump e.g. with one nozzle and three grade select buttons has three logical nozzle numbers.

With transaction "Allowed nozzle numbers" the site controller can select nozzle numbers in a pump that is allowed to use for filling. An authorize command is only authorizing allowed nozzle numbers. Normally all existing nozzles are allowed. If the same nozzles are allowed at every filling from a pump, it is not necessary to send a new transaction for each authorization. The pump shall use the last received information about allowed nozzles.

## 2.6 Preset amount/volume

The site controller sends preset volume in transaction "Preset volume" or preset amount in transaction "Preset amount". The pump stops the filling when the preset value is reached.

A preset value is used only during one filling. The value is reset when the pump has done a reset caused by a reset command.

It is possible to send a new preset value during a filling. The pump always use the last received value. If the site controller first sends preset volume 100 and then during filling sends preset amount 50, the pump stops the filling at amount 50. If the amount already is over 50, the filling is stopped immediately.

The preset value for amount and volume are set back to a default value when the pump receives the command RESET.

If a filling is stopped because of reached preset value, the status sent in transaction "Pump status" will be "max amount/volume reached” instead of “filling completed”.

## 2.7 Pump identity

With a command the site controller can request an identity from the pump. The pump returns identity in transaction "Pump identity". The identity consists of 10 digits.

Digit 1-4: Manufacturer identity. The identity has to be received from Wayne/Dresser for use in the pump program.   
Digit 5-6: Pump type within one manufacturer. Also this number has to be received from Wayne/Dresser.

Digit 7-10: Program version. Free to use by manufacturers.

Digit 1 is MSB.

![](images/b66ea2ae3da9e41bb000e7f0f0714c95b8aea84e9a78aded847997917083b0c0.jpg)

A block to or from a pump can have one or more transactions. A transaction can have fixed or variable length.

A transaction always starts with a byte containing transaction number and a byte containing the length of the data in the transaction. If a transaction has fixed length it does not seem necessary to have the length byte. But the length byte makes it possible for a program to skip transactions that the program does not recognize.

The length of the data block is received from the line protocol. All transactions are handled when the sum of handled transactions length is equal to the length of the data block.

Example:

TRANS Transaction with fix length 1.   
LNG Number of data bytes in the transaction (1). DATA 1   
TRANS Transaction with variable length n.   
LNG Number of data bytes in the transaction (n). DATA 1   
:   
DATA n

In the following section also optional transactions are included for completeness. Transactions marked with an asterisk \(( ^ { * } )\) are not needed to interface a standard Dart pump and may not even be supported by the pump. They are however used in a full Dart implementation for Wayne pumps.

Older versions of dispenser firmware might only support some of the data/messages. Consult Dresser Wayne to determine which data is useful/available in YOUR application.

The following table contains existing transactions.

From site controller to pump.

![](images/8a5d54e29fbd7454f1001794fcccc9c3db3dfdef8621c6eeef0b716e27f71b72.jpg)

From pump to site controller.

![](images/62ebf0c4c66dd50d50d934f319d8630e11c5fd4f083dfcccbe9b73f4d3f67ad0.jpg)

## 3.1 Transactions from site controller to pump

### 3.1.1 Command to pump

Transaction: CD1

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 01H LNG 1 Number of data bytes in the transaction DCC 1 Pump control command

DCC 0H-0EH

Following commands can be sent:

00H RETURN STATUS   
02H RETURN PUMP PARAMETERS (\*)   
03H RETURN PUMP IDENTITY   
04H RETURN FILLING INFORMATION   
05H RESET   
06H AUTHORIZE   
08H STOP   
0AH SWITCH OFF   
0DH SUSPEND FUELLING POINT (\*, alternatively CD14)   
0EH RESUME FUELLING POINT (\*, alternatively CD 15)   
0FH RETURN PRICES OF ALL CURRENT GRADES (\*)

#### 3.1.2 Allowed nozzle numbers

Transaction: CD2

Format:

MNEMONIC NUMBER OF BYTES   
TRANS 1 02H   
LNG 1 Number of data bytes in the transaction   
NOZ1 1 Nozzle number   
NOZn 1

LNG number of nozzle numbers in the transaction.

#### NOZ 1-0FH

NOZ1-n specifies the logical nozzle numbers that is allowed for filling. The transaction is used when a pump is authorized. E.g. if nozzle 1-3 is allowed, the transaction contains 1, 2, 3.

![](images/910f9c36bd2e99481ba3ec112f5bce091852129b9b73e9dcd71bae8fc7e2d570.jpg)

The volume is sent in packed BCD with MSB in first byte. The pump shall stop automatically when filled volume = VOL.

#### 3.1.4 Preset amount

Transaction: CD4

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 04H   
LNG 1 Number of data bytes in the transaction   
AMO 4 Amount

The amount is sent in packed BCD with MSB in first byte. The pump shall stop automatically when filled amount \(: = \mathbf { A M O }\) .

#### 3.1.5 Price update

Transaction: CD5

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 05H   
LNG 1 Number of data bytes in the transaction   
PRI1 3 Price   
PRIn 3

LNG = 3 \* number of prices.

PRI is the price in packet BCD with MSB in first byte. PRI1 is the price for logical nozzle number 1. PRI2 is the price for logical nozzle number 2 and so on.

#### 3.1.6 Command to Output (\*)

Transaction: CD7

Format:

MNEMONIC NUMBER OF BYTES

TRANS 1 07H   
LNG 1 Number of data bytes in the transaction   
NUM 1 Number of output function   
COMD 1 Command

NUM = 0 - 0FFH \(\mathrm { C O M D } = 0 - 0 \mathrm { F F H }\)

Used to control outputs (NUM) of the pump where:

0 – 09H Reserved   
0AH Master Red/Green functionality   
0BH Master SB/BT functionality   
0CH Back Light Control functionality   
0DH Speak to Pump functionality   
0EH Hose Leak Test functionality

Each of the Outputs above can be controlled with the following commands (COMD):

00H Switch OFF   
01H Switch ON   
02 – 09H Reserved

This transaction is supported only by pumps with full implementation of Dart.

#### 3.1.7 Request Total Counters (\*)

Transaction: CD8

Handle the same way as CD101.   
This transaction is supported only by pumps with full implementation of Dart.

#### 3.1.8 Set pump parameters (\*)

Transaction: CD9

Format:

MNEMONIC NUMBER OF BYTES

<html><body><table><tr><td>TRANS 1</td><td>09H Number of data bytes in the transaction</td></tr><tr><td>LNG 1 RES 22</td><td>Not used</td></tr><tr><td>DPVOL 1</td><td>Number of decimals in volume (0-8)</td></tr><tr><td>DPAMO 1</td><td>Number of decimals in amount (0-8)</td></tr><tr><td>DPUNP 1</td><td>Number of decimals in unit price (0-4)</td></tr><tr><td>RES 5 4</td><td>Not used</td></tr><tr><td>MAMO</td><td>Maximum amount</td></tr><tr><td>RES 17</td><td>Not used</td></tr></table></body></html>

MAMO is sent in packed BCD with MSB in first byte. The amount is used for delivery limit if CD 3 or CD 4 is not used.

This transaction is supported only by pumps with full implementation of Dart.

![](images/68e2263cc44a4c66fe57c0f2e2621da1116ce1f8a08a4028e5e074e9206701c9.jpg)

This transaction is supported only by pumps with full implementation of Dart.

#### 3.1.10 Suspend Request (\*)

Transaction: CD14

Format:

<html><body><table><tr><td>MNEMONIC NUMBER</td><td colspan="2">OF BYTES</td></tr><tr><td>TRANS</td><td>1</td><td>0EH</td></tr><tr><td>LNG</td><td>1</td><td>Number of data bytes in the transaction</td></tr><tr><td>NOZ</td><td>1</td><td>Nozzle number (0-0FH)</td></tr></table></body></html>

NOZ specifies the logical nozzle number that is suspended from filling. The number is only used for satellite pumps and should normally be 0.

This transaction is supported only by SAT pumps with full implementation of Dart.

#### 3.1.11 Resume Request (\*)

Transaction: CD15

Format:

TRANS 0FH LNG Number of data bytes in the transaction NOZ Nozzle number (0-0FH)

NOZ specifies the logical nozzle number that is resumed for filling. The number is only used for satellite pumps and should normally be 0.

This transaction is supported only by SAT pumps with full implementation of Dart.