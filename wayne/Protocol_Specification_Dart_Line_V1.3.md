# PROTOCOL SPECIFICATION

DART-LINE

Revision 1.3

2002-08-22

This document is the property of <PERSON><PERSON><PERSON>. It is not to be used or duplicated without written permission of the owner, and is not to be used in any way inconsistent with the purpose for which it is loaned. Dresser Wayne AB shall not be liable for technical or editorial errors or omissions which may appear in this document. It also retains the right to make changes to this specification at any time without prior notice.

TABLE OF CONTENTS

1 General . 1   
2 Data format.. 1   
3 Physical Requirements . 1   
4 Galvanic Isolation... 2   
5 Data Rate . 2   
6 Code Transparence .. . 2   
7 Reliable Data Transfer. 2   
8 Operational Specifics 2   
9 Buffer Size. . 2   
10 Control Characters in the Protocol .. 3   
11 Messages from Master to Slave.. 4   
12 Messages from Slave to Master.. 5   
13 Flow chart Tx-Interrupt . 6   
14 Flow chart Rx-Interrupt. 7   
15 Possible answer alternatives.. .. 8   
16 Error Recovery .. 9   
17 Timing 9   
18 Hardware .. 9

## General

The standard data link is based on a master/slave relationship, where the master polls the slaves. If the master wants to send data to a slave, it sends a block with data instead of a poll. If the slave wants to send data it answers with data on a poll. Half duplex is used. The protocol is code transparent and byte oriented. The protocol required for DART implementation shall provide for variable length messages.

2

### Data format

Asynchronous communication

9600 (Default) or 19 200 baud optional

1 start bit, 8 data bits, 1 parity bit (odd), 1 stop bit

3

## Physical Requirements

It shall be possible to transmit over DART to slave units wired up to 300 meters distant from the master using special wiring. DART shall provide for operation using standard wiring in conduit. See Table 1 below for application data.

The wiring for implementation of DART shall be two wire. A fibre optical link shall be an option.

Table 1   

<html><body><table><tr><td colspan="3">DART Application Data</td></tr><tr><td>Baud Rate</td><td>Wiring</td><td>Maximum Separation (meters)</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>19200 Shielded Twisted Pair</td><td>300</td></tr><tr><td></td><td>19200 Standard Wiring Dry Conduit</td><td>300</td></tr><tr><td></td><td>19200 Standard Wiring Wet Conduit</td><td>50</td></tr><tr><td></td><td>9600 Standard Wiring Wet Conduit.</td><td>100</td></tr><tr><td></td><td>19200 Fibre Optic Link</td><td>300</td></tr><tr><td></td><td>19200 Special Cable-Wet Conduit</td><td>300</td></tr></table></body></html>

### Galvanic Isolation

Galvanic isolation may be required in each unit.

### Data Rate

DART shall not be baud-rate dependent. A baud rate of 19200 shall be possible, default 9600, if distance and type of wiring are restrictive.

6

### Code Transparency

Code transparency for 8 bit data is achieved by Data Link escape (DLE) insertion. DLE insertion is needed when any data byte or CRC has the value SF (stop flag). Transmitting device transmit DLE before SF is transmitted in the data field. Receiving device checks when receiving SF, if previous received character was DLE. If so DLE is over written by the received SF in the line buffer. Inserted DLE:s are not included in the CRC-calculation.

### Reliable Data Transfer

DART must provide for reliable data transfer. Error checking is to be implemented by CRC-16. Parity checking is required on each byte.

### Operational Specifics

DART shall operate half-duplex and transmit data in asynchronous start-stop format.

### Buffer Size

Buffer size is application dependent. However, maximum is 256 bytes including control characters. Different slaves can have different buffer size.

ETX 03H End of text DLE 10H Data link escape SF FAH Stop flag

Some abbreviation explanations:

ADR Slave device address for message 00H-FFH

CRC-1 LSB of CRC-16 word. CRC is calculated from the first byte in the message (ADR) to the last data byte. CRC is initialised to 0000H.

CRC-2 MSB of CRC-16 word.

CTRL Control character and block sequence number

Bit

![](images/2b7b5047a6b1935ce6cb5602ec86d098a5ae3bc55d9d9f24bf7cbd25f10ed138.jpg)

Control character Different control characters

POLL 20H   
DATA 30H-3FH   
ACK C0H-CFH   
NAK 50H-5FH   
EOT 70H-7FH   
ACKPOLL E0H-EFH Optional. Normally not used since ACK followed by POLL is almost as efficient.

The master has one independent TX# for each slave. Each slave has one TX#. When data is sent from the master or the slave a new \(\mathrm { T X } \#\) is generated for each new data block. The TX# is then returned from the master or the slave in ACK, NAK, EOT or ACKPOLL. Slave answering EOT at POLL contains 0 in TX#. \(\mathrm { T X } \#\) is initiated to 0 after restart of protocol and then incremented by one for each successfully transmitted data block. TX# wraps around to 1 after FH.

Poll

ADR CTRL SF

Data

ADR CTRL DATA

DATA CRC-1 CRC-2 ETX SF

ACK

ADR CTRL SF

NAK

ADR CTRL SF

EOT

ADR CTRL SF

Data ADR CTRL DATA DATA CRC-1 CRC-2 ETX SF

ACK

ADR CTRL SF

NAK

ADR CTRL SF

![](images/a50dacd6e6afa1306c5ec8ddc834d31d24874708b96555a088e90f3aabd47f81.jpg)  
DRESSER WAYNE - Protocol Specification Dart-Line Page 6

![](images/026f914570d0a4d304e31fa7b983c6c99f26c2ff7eca2c5a8802b149a8d49616.jpg)

![](images/ca255be9a23aa8aefbe3fe34e94ad0e441313858e9275aac1514bfb59ef7878d.jpg)

Error recovery is done when the expected block sequence number does not match the real one. It is done by both the master and the slave. This is a listing of all different situations when error recovery should be done. The TX#-check should be done in the following sequence:

1. \(\mathrm { T X } \# =\) Last received TX#. The transmitting unit did not get my last ACK. Skip data and answer ACK. Note that this must be done also when \(\mathrm { T X } \# = 0\) .   
2. \(\mathrm { T X } \# = 0\) The transmitting unit has been restarted. Initiate the expected TX# to   
0, accept data and send ACK.   
3. TX# \(< >\) expected. If expected \(\mathrm { T X } \# = 0\) this means that actual unit has been restarted and the expected TX# should be sent to the one just received. If so accept data and send ACK. Otherwise another error has occurred and the block should be answered with NAK.

NAK is sent if \(\mathrm { T X } \#\) -error is found in received data. The slave shall not answer at parity error or CRC-error.

The unit transmitting data has the responsibility to restart the communication procedure when NAK has been received 3 times for identical message.

17

## Timing

Each unit must be capable to receive characters at 19200/9600 baud without delays between characters. The master controls the timing. The slave must respond to a poll or data within \(2 5 ~ \mathrm { m s }\) , i.e. transmit the first character after a complete poll or data. The slave must be capable to receive an ACK and a poll transmitted from the master as one continuous byte stream (two lines 3 bytes). In this case ACK and Poll are for two different device addresses. If ACK and poll are for the same device then ACKPOLL is sent as one message (3 bytes).

### Hardware

RS485 with or without galvanic isolation.

DRESSER WAYNE - Protocol Specification Dart-Line Page 9

![](images/be50558b15f8b68618efd2448026db9ab40c362db4ddb54ef786b1fda946d1a1.jpg)