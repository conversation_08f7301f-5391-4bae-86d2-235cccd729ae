# Redis配置文件 - FCC Service
# 针对开发和测试环境优化

# 基本配置
port 6379
bind 0.0.0.0
protected-mode no

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置 (开发环境关闭以提升性能)
save ""
appendonly no

# 日志配置
loglevel notice
logfile ""

# 客户端配置
timeout 300
tcp-keepalive 300
tcp-backlog 511

# 数据库配置
databases 16

# 性能优化
hz 10
dynamic-hz yes

# 安全配置 (开发环境简化)
# requirepass your_password_here

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60 