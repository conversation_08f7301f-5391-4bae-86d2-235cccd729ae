#!/bin/bash

echo "🔍 分析SharedConnection机制与单设备工作的差异"

echo "=== 问题诊断：为什么单个pump工作正常，但多个pump失败？ ==="

echo
echo "1️⃣ 单设备工作模式分析："
echo "   - 每个设备有独立的SerialManager实例"
echo "   - 每个设备有独立的串口连接"
echo "   - 设备间完全隔离，不存在竞争条件"

echo
echo "2️⃣ SharedConnection模式分析："
echo "   - 创建共享的'shared_bus_COM7'设备"
echo "   - 多个pump共享同一个SerialManager"
echo "   - 可能存在的问题：DeviceID不匹配！"

echo
echo "3️⃣ 关键问题分析："
echo "   问题很可能是：SharedConnection创建了'shared_bus_COM7'设备"
echo "   但DART协议通信仍然使用原始deviceID (device_com7_pump01/pump02)"
echo "   导致设备配置和通信接口不匹配！"

echo
echo "🔍 验证理论：检查日志中的设备ID使用情况"

# 检查最新的日志文件
LOG_FILE=$(find /home/<USER>"*.log" -type f -newer $(find /home/<USER>"*.log" -type f | head -1) 2>/dev/null | head -1)

if [ -z "$LOG_FILE" ]; then
    echo "❌ 未找到日志文件"
    exit 1
fi

echo "📋 使用日志文件: $LOG_FILE"

echo
echo "=== 分析设备ID在通信过程中的使用 ==="

echo
echo "🔍 1. 查找shared_bus设备创建："
grep -n "shared_bus" "$LOG_FILE" | head -5

echo
echo "🔍 2. 查找实际设备轮询："
grep -n "ExecuteSinglePoll" "$LOG_FILE" | head -5

echo
echo "🔍 3. 查找设备地址配置："
grep -n "device_address.*0x5[01]" "$LOG_FILE" | head -5

echo
echo "🔍 4. 查找SerialTransport创建："
grep -n "NewSerialTransport" "$LOG_FILE" | head -3

echo
echo "=== 理论验证结果 ==="
echo
echo "如果我的分析正确，你应该看到："
echo "1. SharedConnection创建了'shared_bus_COM7'设备"
echo "2. 但ExecuteSinglePoll仍然使用'device_com7_pump01'等原始ID"
echo "3. 这导致设备配置查找失败或配置不匹配"

echo
echo "=== 解决方案建议 ==="
echo "1. 确保SharedConnection正确传递原始deviceID到底层通信"
echo "2. 或者修改通信层接受总线级别的设备配置"
echo "3. 验证设备地址(0x50/0x51)是否正确传递到DART协议层"

echo
echo "🔧 下一步：检查设备配置查找逻辑"
echo "   重点关注：WayneAdapter.ExecuteCommand中的设备配置获取" 