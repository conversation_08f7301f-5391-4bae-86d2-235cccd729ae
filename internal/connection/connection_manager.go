package connection

import (
	"context"
	"fmt"
	"sync"
	"time"

	wayneConn "fcc-service/internal/adapters/wayne/connection"
	"fcc-service/pkg/errors"
)

// ConnectionManager Wayne设备连接管理器
// 负责管理与Wayne DART设备的连接，包括连接池、健康检查等
type ConnectionManager struct {
	// 连接池
	connections map[string]*SerialConnection
	mutex       sync.RWMutex

	// 配置
	config ConnectionManagerConfig

	// 生命周期控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// ConnectionManagerConfig 连接管理器配置
type ConnectionManagerConfig struct {
	// 连接池配置
	MaxConnections      int           `json:"max_connections"`       // 最大连接数
	ConnectionTimeout   time.Duration `json:"connection_timeout"`    // 连接超时
	IdleTimeout         time.Duration `json:"idle_timeout"`          // 空闲超时
	HealthCheckInterval time.Duration `json:"health_check_interval"` // 健康检查间隔

	// DART协议配置
	DARTConfig DARTProtocolConfig `json:"dart_config"`

	// 重试配置
	MaxRetries    int           `json:"max_retries"`    // 最大重试次数
	RetryInterval time.Duration `json:"retry_interval"` // 重试间隔
}

// DARTProtocolConfig DART协议配置
type DARTProtocolConfig struct {
	// 基本协议参数
	AddressRangeMin byte          `json:"address_range_min"` // 最小地址 (0x50)
	AddressRangeMax byte          `json:"address_range_max"` // 最大地址 (0x6F)
	ResponseTimeout time.Duration `json:"response_timeout"`  // 响应超时 (≤25ms)
	MaxFrameSize    int           `json:"max_frame_size"`    // 最大帧长度 (256字节)

	// 串口配置
	SerialConfig wayneConn.SerialConfig `json:"serial_config"`

	// 协议特性
	DLEEnabled bool `json:"dle_enabled"` // 启用DLE透明处理
	CRCEnabled bool `json:"crc_enabled"` // 启用CRC校验
}

// DataReceivedCallback 数据接收回调函数类型
type DataReceivedCallback func(data []byte)

// SerialConnection 串口连接封装
type SerialConnection struct {
	// 连接标识
	ID         string    `json:"id"`
	Port       string    `json:"port"`
	CreatedAt  time.Time `json:"created_at"`
	LastUsedAt time.Time `json:"last_used_at"`

	// 连接状态
	Status    wayneConn.ConnectionStatus `json:"status"`
	IsHealthy bool                       `json:"is_healthy"`

	// 串口管理器
	SerialManager SerialManagerInterface

	// 连接统计
	UsageCount int64 `json:"usage_count"`

	// 互斥锁
	mutex sync.RWMutex
}

// NewConnectionManager 创建新的连接管理器
func NewConnectionManager(config ConnectionManagerConfig) (*ConnectionManager, error) {
	// 验证配置
	if err := validateConnectionManagerConfig(config); err != nil {
		return nil, fmt.Errorf("无效的连接管理器配置: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &ConnectionManager{
		connections: make(map[string]*SerialConnection),
		config:      config,
		ctx:         ctx,
		cancel:      cancel,
	}

	// 启动健康检查
	manager.startHealthCheck()

	return manager, nil
}

// validateConnectionManagerConfig 验证连接管理器配置
func validateConnectionManagerConfig(config ConnectionManagerConfig) error {
	if config.MaxConnections <= 0 {
		return errors.NewValidationError("最大连接数必须大于0")
	}

	if config.ConnectionTimeout <= 0 {
		config.ConnectionTimeout = 30 * time.Second
	}

	if config.HealthCheckInterval <= 0 {
		config.HealthCheckInterval = 30 * time.Second
	}

	// 验证DART协议配置
	dartConfig := config.DARTConfig
	if dartConfig.AddressRangeMin < 0x50 || dartConfig.AddressRangeMin > 0x6F {
		return errors.NewValidationError("DART地址范围最小值必须在0x50-0x6F之间")
	}

	if dartConfig.AddressRangeMax < 0x50 || dartConfig.AddressRangeMax > 0x6F {
		return errors.NewValidationError("DART地址范围最大值必须在0x50-0x6F之间")
	}

	if dartConfig.AddressRangeMin > dartConfig.AddressRangeMax {
		return errors.NewValidationError("DART地址范围最小值不能大于最大值")
	}

	if dartConfig.ResponseTimeout > 25*time.Millisecond {
		return errors.NewValidationError("DART响应超时不能超过25ms，当前配置: " + dartConfig.ResponseTimeout.String())
	}

	// 强制设置为25ms如果未配置或为0
	if dartConfig.ResponseTimeout <= 0 {
		dartConfig.ResponseTimeout = 25 * time.Millisecond
	}

	if dartConfig.MaxFrameSize <= 0 || dartConfig.MaxFrameSize > 256 {
		dartConfig.MaxFrameSize = 256 // DART协议最大帧长度
	}

	return nil
}

// GetConnection 获取或创建串口连接
func (cm *ConnectionManager) GetConnection(ctx context.Context, port string) (*SerialConnection, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 检查是否已存在连接
	if conn, exists := cm.connections[port]; exists {
		if conn.IsHealthy && conn.Status == wayneConn.ConnectionStatusConnected {
			conn.updateLastUsed()
			return conn, nil
		}
		// 连接不健康，移除并重新创建
		delete(cm.connections, port)
		conn.close()
	}

	// 检查连接数限制
	if len(cm.connections) >= cm.config.MaxConnections {
		return nil, errors.NewResourceError("达到最大连接数限制")
	}

	// 创建新连接
	conn, err := cm.createConnection(ctx, port)
	if err != nil {
		return nil, fmt.Errorf("创建连接失败: %w", err)
	}

	cm.connections[port] = conn
	return conn, nil
}

// createConnection 创建新的串口连接
func (cm *ConnectionManager) createConnection(ctx context.Context, port string) (*SerialConnection, error) {
	// 配置串口参数
	serialConfig := cm.config.DARTConfig.SerialConfig
	serialConfig.Port = port

	// 创建串口管理器
	serialManager, err := wayneConn.NewSerialManager(serialConfig)
	if err != nil {
		return nil, fmt.Errorf("创建串口管理器失败: %w", err)
	}

	// 建立连接
	connectCtx, cancel := context.WithTimeout(ctx, cm.config.ConnectionTimeout)
	defer cancel()

	err = serialManager.Connect(connectCtx)
	if err != nil {
		return nil, fmt.Errorf("连接串口失败: %w", err)
	}

	// 创建连接对象
	conn := &SerialConnection{
		ID:            fmt.Sprintf("%s_%d", port, time.Now().Unix()),
		Port:          port,
		CreatedAt:     time.Now(),
		LastUsedAt:    time.Now(),
		Status:        wayneConn.ConnectionStatusConnected,
		IsHealthy:     true,
		SerialManager: serialManager,
		UsageCount:    0,
	}

	return conn, nil
}

// ReleaseConnection 释放连接
func (cm *ConnectionManager) ReleaseConnection(port string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[port]; exists {
		delete(cm.connections, port)
		return conn.close()
	}

	return nil
}

// GetConnectionStats 获取连接统计信息
func (cm *ConnectionManager) GetConnectionStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_connections":   len(cm.connections),
		"max_connections":     cm.config.MaxConnections,
		"healthy_connections": 0,
		"connections":         make([]map[string]interface{}, 0, len(cm.connections)),
	}

	healthyCount := 0
	for _, conn := range cm.connections {
		if conn.IsHealthy {
			healthyCount++
		}

		connStats := map[string]interface{}{
			"id":           conn.ID,
			"port":         conn.Port,
			"status":       conn.Status,
			"is_healthy":   conn.IsHealthy,
			"created_at":   conn.CreatedAt,
			"last_used_at": conn.LastUsedAt,
			"usage_count":  conn.UsageCount,
		}

		if conn.SerialManager != nil {
			connStats["serial_stats"] = conn.SerialManager.GetStatistics()
		}

		stats["connections"] = append(stats["connections"].([]map[string]interface{}), connStats)
	}

	stats["healthy_connections"] = healthyCount
	return stats
}

// Close 关闭连接管理器
func (cm *ConnectionManager) Close() error {
	cm.cancel()
	cm.wg.Wait()

	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	var lastErr error
	for port, conn := range cm.connections {
		if err := conn.close(); err != nil {
			lastErr = err
		}
		delete(cm.connections, port)
	}

	return lastErr
}

// startHealthCheck 启动健康检查
func (cm *ConnectionManager) startHealthCheck() {
	cm.wg.Add(1)
	go func() {
		defer cm.wg.Done()

		ticker := time.NewTicker(cm.config.HealthCheckInterval)
		defer ticker.Stop()

		for {
			select {
			case <-cm.ctx.Done():
				return
			case <-ticker.C:
				cm.performHealthCheck()
			}
		}
	}()
}

// performHealthCheck 执行健康检查
func (cm *ConnectionManager) performHealthCheck() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	for port, conn := range cm.connections {
		// 检查连接是否超时
		if time.Since(conn.LastUsedAt) > cm.config.IdleTimeout {
			delete(cm.connections, port)
			conn.close()
			continue
		}

		// 检查连接健康状态
		if !conn.checkHealth() {
			conn.IsHealthy = false
		}
	}
}

// SerialConnection methods

// updateLastUsed 更新最后使用时间
func (sc *SerialConnection) updateLastUsed() {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	sc.LastUsedAt = time.Now()
	sc.UsageCount++
}

// checkHealth 检查连接健康状态
func (sc *SerialConnection) checkHealth() bool {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	if sc.SerialManager == nil {
		return false
	}

	return sc.SerialManager.IsConnected()
}

// SendFrame 发送DART协议帧
func (sc *SerialConnection) SendFrame(ctx context.Context, frame []byte) error {
	sc.updateLastUsed()

	if sc.SerialManager == nil {
		return errors.NewConnectionError("串口管理器未初始化")
	}

	return sc.SerialManager.SendFrame(ctx, frame)
}

// ReceiveFrame 接收DART协议帧
func (sc *SerialConnection) ReceiveFrame(ctx context.Context) ([]byte, error) {
	sc.updateLastUsed()

	if sc.SerialManager == nil {
		return nil, errors.NewConnectionError("串口管理器未初始化")
	}

	return sc.SerialManager.ReceiveFrame(ctx)
}

// close 关闭连接
func (sc *SerialConnection) close() error {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	if sc.SerialManager != nil {
		err := sc.SerialManager.Close()
		sc.SerialManager = nil
		return err
	}

	return nil
}

// 默认配置创建函数

// NewDefaultConnectionManagerConfig 创建默认连接管理器配置
func NewDefaultConnectionManagerConfig() ConnectionManagerConfig {
	return ConnectionManagerConfig{
		MaxConnections:      10,
		ConnectionTimeout:   30 * time.Second,
		IdleTimeout:         5 * time.Minute,
		HealthCheckInterval: 30 * time.Second,
		MaxRetries:          3,
		RetryInterval:       100 * time.Millisecond,
		DARTConfig: DARTProtocolConfig{
			AddressRangeMin: 0x50,                  // DART协议最小地址
			AddressRangeMax: 0x6F,                  // DART协议最大地址
			ResponseTimeout: 25 * time.Millisecond, // DART协议响应超时限制
			MaxFrameSize:    256,                   // DART协议最大帧长度
			DLEEnabled:      true,                  // 启用DLE透明处理
			CRCEnabled:      true,                  // 启用CRC校验
			SerialConfig: wayneConn.SerialConfig{
				BaudRate:        9600,                  // DART协议默认波特率
				DataBits:        8,                     // DART协议数据位
				StopBits:        1,                     // DART协议停止位
				Parity:          wayneConn.ParityOdd,   // DART协议奇校验
				Timeout:         25 * time.Millisecond, // DART协议响应超时
				ReadTimeout:     50 * time.Millisecond, // 读取超时
				WriteTimeout:    50 * time.Millisecond, // 写入超时
				MaxRetries:      3,                     // 最大重试次数
				RetryInterval:   10 * time.Millisecond, // 重试间隔
				ReadBufferSize:  256,                   // DART协议最大缓冲区
				WriteBufferSize: 256,                   // 写缓冲区大小
			},
		},
	}
}

// SerialManagerInterface 接口定义（用于解耦）
type SerialManagerInterface interface {
	Connect(ctx context.Context) error
	Disconnect(ctx context.Context) error
	IsConnected() bool
	SendFrame(ctx context.Context, frame []byte) error
	ReceiveFrame(ctx context.Context) ([]byte, error)
	Close() error
	GetStatistics() interface{} // 返回interface{}以支持不同的统计类型
	SetDataReceivedCallback(callback wayneConn.DataReceivedCallback)
}
