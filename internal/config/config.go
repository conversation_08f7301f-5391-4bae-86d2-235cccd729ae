package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Config FCC服务配置结构
type Config struct {
	HTTP            HTTPConfig            `mapstructure:"http" yaml:"http"`
	Database        DatabaseConfig        `mapstructure:"database" yaml:"database"`
	Redis           RedisConfig           `mapstructure:"redis" yaml:"redis"`
	DART            DARTConfig            `mapstructure:"dart" yaml:"dart"`
	FCC             FCCConfig             `mapstructure:"fcc" yaml:"fcc"`
	Monitoring      MonitoringConfig      `mapstructure:"monitoring" yaml:"monitoring"`
	Logging         LoggingConfig         `mapstructure:"logging" yaml:"logging"`
	Business        BusinessConfig        `mapstructure:"business" yaml:"business"`
	Security        SecurityConfig        `mapstructure:"security" yaml:"security"`
	ErrorHandle     ErrorHandleConfig     `mapstructure:"error_handling" yaml:"error_handling"`
	Adapters        AdaptersConfig        `mapstructure:"adapters" yaml:"adapters"`
	Watchdog        WatchdogConfig        `mapstructure:"watchdog" yaml:"watchdog" json:"watchdog"`
	SerialScheduler SerialSchedulerConfig `mapstructure:"serial_scheduler" yaml:"serial_scheduler" json:"serial_scheduler"`
	PreAuth         PreAuthConfig         `mapstructure:"preauth" yaml:"preauth"`
	AutoAuth        AutoAuthConfig        `mapstructure:"autoauth" yaml:"autoauth"`
}

// HTTPConfig HTTP服务器配置
type HTTPConfig struct {
	Addr         string        `mapstructure:"addr" yaml:"addr"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout" yaml:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout" yaml:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout" yaml:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string `mapstructure:"host" yaml:"host"`
	Port         int    `mapstructure:"port" yaml:"port"`
	Database     string `mapstructure:"database" yaml:"database"`
	Username     string `mapstructure:"username" yaml:"username"`
	Password     string `mapstructure:"password" yaml:"password"`
	MaxOpenConns int    `mapstructure:"max_open_conns" yaml:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns" yaml:"max_idle_conns"`
	SSLMode      string `mapstructure:"ssl_mode" yaml:"ssl_mode"`
	TimeZone     string `mapstructure:"timezone" yaml:"timezone"` // 🆕 数据库时区配置
}

// GetTimezone 获取配置的时区名称
func (c *DatabaseConfig) GetTimezone() string {
	if c.TimeZone == "" {
		return "UTC" // 默认时区
	}
	return c.TimeZone
}

// GetTimezoneOrDefault 获取配置的时区，如果为空则返回默认值
func (c *DatabaseConfig) GetTimezoneOrDefault(defaultTZ string) string {
	if c.TimeZone == "" {
		return defaultTZ
	}
	return c.TimeZone
}

// GetConfiguredTime 获取使用配置时区的当前时间
func (c *DatabaseConfig) GetConfiguredTime() time.Time {
	location, err := time.LoadLocation(c.GetTimezone())
	if err != nil {
		// 如果加载失败，使用系统时间
		return time.Now()
	}
	return time.Now().In(location)
}

// RedisConfig Redis配置
type RedisConfig struct {
	Enabled      bool          `yaml:"enabled" mapstructure:"enabled"` // 新增：Redis 开关
	Host         string        `yaml:"host" mapstructure:"host"`
	Port         int           `yaml:"port" mapstructure:"port"`
	Password     string        `yaml:"password" mapstructure:"password"`
	DB           int           `yaml:"db" mapstructure:"db"`
	PoolSize     int           `yaml:"pool_size" mapstructure:"pool_size"`
	MinIdleConns int           `yaml:"min_idle_conns" mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `yaml:"dial_timeout" mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `yaml:"read_timeout" mapstructure:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout" mapstructure:"write_timeout"`
	PoolTimeout  time.Duration `yaml:"pool_timeout" mapstructure:"pool_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout" mapstructure:"idle_timeout"`
}

// DARTConfig DART协议配置
type DARTConfig struct {
	Serial    SerialConfig    `mapstructure:"serial" yaml:"serial"`
	Discovery DiscoveryConfig `mapstructure:"discovery" yaml:"discovery"`
	Protocol  ProtocolConfig  `mapstructure:"protocol" yaml:"protocol"`
}

// SerialConfig 串口配置
type SerialConfig struct {
	// 现有配置
	DevicePatterns []string      `mapstructure:"device_patterns" yaml:"device_patterns"`
	BaudRate       int           `mapstructure:"baud_rate" yaml:"baud_rate"`
	DataBits       int           `mapstructure:"data_bits" yaml:"data_bits"`
	StopBits       int           `mapstructure:"stop_bits" yaml:"stop_bits"`
	Parity         string        `mapstructure:"parity" yaml:"parity"`
	Timeout        time.Duration `mapstructure:"timeout" yaml:"timeout"`

	// ✅ 新增：批量读取优化配置
	EnableBatchReading bool          `mapstructure:"enable_batch_reading" yaml:"enable_batch_reading"` // 启用批量读取
	BatchTimeout       time.Duration `mapstructure:"batch_timeout" yaml:"batch_timeout"`               // 批量窗口时间
	BatchSizeThreshold int           `mapstructure:"batch_size_threshold" yaml:"batch_size_threshold"` // 批量大小阈值

	// 现有配置
	EnableRawDataLogging bool `mapstructure:"enable_raw_data_logging" yaml:"enable_raw_data_logging"`
}

// DiscoveryConfig 设备发现配置
type DiscoveryConfig struct {
	ScanInterval    time.Duration `mapstructure:"scan_interval" yaml:"scan_interval"`
	AddressRangeMin int           `mapstructure:"address_range_min" yaml:"address_range_min"`
	AddressRangeMax int           `mapstructure:"address_range_max" yaml:"address_range_max"`
	ProbeTimeout    time.Duration `mapstructure:"probe_timeout" yaml:"probe_timeout"`
	RetryCount      int           `mapstructure:"retry_count" yaml:"retry_count"`
}

// ProtocolConfig 协议参数配置
type ProtocolConfig struct {
	ResponseTimeout time.Duration `mapstructure:"response_timeout" yaml:"response_timeout"`
	MaxRetries      int           `mapstructure:"max_retries" yaml:"max_retries"`
	DLEEnabled      bool          `mapstructure:"dle_enabled" yaml:"dle_enabled"`
	CRCEnabled      bool          `mapstructure:"crc_enabled" yaml:"crc_enabled"`
}

// FCCConfig FCC架构集成配置
type FCCConfig struct {
	DeviceManagement DeviceManagementConfig `mapstructure:"device_management" yaml:"device_management"`
	BusinessServices BusinessServicesConfig `mapstructure:"business_services" yaml:"business_services"`
	APIGateway       APIGatewayConfig       `mapstructure:"api_gateway" yaml:"api_gateway"`
}

// DeviceManagementConfig 设备管理服务集成配置
type DeviceManagementConfig struct {
	Endpoint   string        `mapstructure:"endpoint" yaml:"endpoint"`
	Timeout    time.Duration `mapstructure:"timeout" yaml:"timeout"`
	RetryCount int           `mapstructure:"retry_count" yaml:"retry_count"`
	AuthToken  string        `mapstructure:"auth_token" yaml:"auth_token"`
}

// BusinessServicesConfig 业务服务集成配置
type BusinessServicesConfig struct {
	Endpoint   string        `mapstructure:"endpoint" yaml:"endpoint"`
	Timeout    time.Duration `mapstructure:"timeout" yaml:"timeout"`
	RetryCount int           `mapstructure:"retry_count" yaml:"retry_count"`
	AuthToken  string        `mapstructure:"auth_token" yaml:"auth_token"`
}

// APIGatewayConfig API网关配置
type APIGatewayConfig struct {
	Endpoint  string        `mapstructure:"endpoint" yaml:"endpoint"`
	Timeout   time.Duration `mapstructure:"timeout" yaml:"timeout"`
	AuthToken string        `mapstructure:"auth_token" yaml:"auth_token"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled     bool   `mapstructure:"enabled" yaml:"enabled"`
	MetricsPort int    `mapstructure:"metrics_port" yaml:"metrics_port"`
	MetricsPath string `mapstructure:"metrics_path" yaml:"metrics_path"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level   string           `mapstructure:"level" yaml:"level"`
	Format  string           `mapstructure:"format" yaml:"format"`
	File    LogFileConfig    `mapstructure:"file" yaml:"file"`
	Console LogConsoleConfig `mapstructure:"console" yaml:"console"`
}

// LogFileConfig 文件日志配置
type LogFileConfig struct {
	Enabled    bool   `mapstructure:"enabled" yaml:"enabled"`
	Path       string `mapstructure:"path" yaml:"path"`
	MaxSize    int    `mapstructure:"max_size" yaml:"max_size"`       // MB
	MaxBackups int    `mapstructure:"max_backups" yaml:"max_backups"` // 保留备份数
	MaxAge     int    `mapstructure:"max_age" yaml:"max_age"`         // 天数
	Compress   bool   `mapstructure:"compress" yaml:"compress"`       // 是否压缩
}

// LogConsoleConfig 控制台日志配置
type LogConsoleConfig struct {
	Enabled bool `mapstructure:"enabled" yaml:"enabled"`
}

// BusinessConfig 业务配置
type BusinessConfig struct {
	Pump            PumpConfig            `mapstructure:"pump" yaml:"pump"`
	Sync            SyncConfig            `mapstructure:"sync" yaml:"sync"`
	Cache           CacheConfig           `mapstructure:"cache" yaml:"cache"`
	TransactionSync TransactionSyncConfig `mapstructure:"transaction_sync" yaml:"transaction_sync"`
}

// PumpConfig 泵控制参数
type PumpConfig struct {
	MaxConcurrentOperations int           `mapstructure:"max_concurrent_operations" yaml:"max_concurrent_operations"`
	AuthorizationTimeout    time.Duration `mapstructure:"authorization_timeout" yaml:"authorization_timeout"`
	TransactionTimeout      time.Duration `mapstructure:"transaction_timeout" yaml:"transaction_timeout"`
}

// SyncConfig 数据同步配置
type SyncConfig struct {
	StatusInterval time.Duration `mapstructure:"status_interval" yaml:"status_interval"`
	BatchSize      int           `mapstructure:"batch_size" yaml:"batch_size"`
	RetryInterval  time.Duration `mapstructure:"retry_interval" yaml:"retry_interval"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	DeviceStatusTTL int    `mapstructure:"device_status_ttl" yaml:"device_status_ttl"`
	TransactionTTL  int    `mapstructure:"transaction_ttl" yaml:"transaction_ttl"`
	MaxMemory       string `mapstructure:"max_memory" yaml:"max_memory"`
}

// TransactionSyncConfig 交易同步配置
type TransactionSyncConfig struct {
	Enabled  bool          `mapstructure:"enabled" yaml:"enabled"`
	Endpoint string        `mapstructure:"endpoint" yaml:"endpoint"`
	Timeout  time.Duration `mapstructure:"timeout" yaml:"timeout"`
	APIKey   string        `mapstructure:"api_key" yaml:"api_key"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	API    APISecurityConfig    `mapstructure:"api" yaml:"api"`
	Device DeviceSecurityConfig `mapstructure:"device" yaml:"device"`
}

// APISecurityConfig API访问控制
type APISecurityConfig struct {
	RateLimit      int `mapstructure:"rate_limit" yaml:"rate_limit"`
	MaxConnections int `mapstructure:"max_connections" yaml:"max_connections"`
}

// DeviceSecurityConfig 设备访问控制
type DeviceSecurityConfig struct {
	AllowedAddresses       []string `mapstructure:"allowed_addresses" yaml:"allowed_addresses"`
	AuthenticationRequired bool     `mapstructure:"authentication_required" yaml:"authentication_required"`
}

// ErrorHandleConfig 错误处理配置
type ErrorHandleConfig struct {
	Retry          RetryConfig          `mapstructure:"retry" yaml:"retry"`
	CircuitBreaker CircuitBreakerConfig `mapstructure:"circuit_breaker" yaml:"circuit_breaker"`
}

// RetryConfig 重试策略
type RetryConfig struct {
	MaxAttempts int           `mapstructure:"max_attempts" yaml:"max_attempts"`
	BaseDelay   time.Duration `mapstructure:"base_delay" yaml:"base_delay"`
	MaxDelay    time.Duration `mapstructure:"max_delay" yaml:"max_delay"`
	Multiplier  float64       `mapstructure:"multiplier" yaml:"multiplier"`
}

// CircuitBreakerConfig 断路器配置
type CircuitBreakerConfig struct {
	FailureThreshold int           `mapstructure:"failure_threshold" yaml:"failure_threshold"`
	RecoveryTimeout  time.Duration `mapstructure:"recovery_timeout" yaml:"recovery_timeout"`
	HalfOpenMaxCalls int           `mapstructure:"half_open_max_calls" yaml:"half_open_max_calls"`
}

// AdaptersConfig 适配器配置
type AdaptersConfig struct {
	Wayne WayneAdapterConfig `mapstructure:"wayne" yaml:"wayne"`
}

// WayneAdapterConfig Wayne适配器配置
type WayneAdapterConfig struct {
	Type       string                `mapstructure:"type" yaml:"type"`
	Connection WayneConnectionConfig `mapstructure:"connection" yaml:"connection"`
	Options    WayneAdapterOptions   `mapstructure:"options" yaml:"options"`
}

// WayneConnectionConfig Wayne连接配置
type WayneConnectionConfig struct {
	SerialPort string        `mapstructure:"serial_port" yaml:"serial_port"`
	BaudRate   int           `mapstructure:"baud_rate" yaml:"baud_rate"`
	Timeout    time.Duration `mapstructure:"timeout" yaml:"timeout"`
	MaxRetries int           `mapstructure:"max_retries" yaml:"max_retries"`
}

// WayneAdapterOptions Wayne适配器选项
type WayneAdapterOptions struct {
	DiscoveryBatchSize         int `mapstructure:"discovery_batch_size" yaml:"discovery_batch_size"`
	DiscoveryBatchDelayMs      int `mapstructure:"discovery_batch_delay_ms" yaml:"discovery_batch_delay_ms"`
	DiscoveryAddressDelayMs    int `mapstructure:"discovery_address_delay_ms" yaml:"discovery_address_delay_ms"`
	DiscoveryResponseTimeoutMs int `mapstructure:"discovery_response_timeout_ms" yaml:"discovery_response_timeout_ms"`
	// 🚀 增强轮询功能配置 (MVP)
	EnhancedPollingEnabled bool `mapstructure:"enhanced_polling_enabled" yaml:"enhanced_polling_enabled"`
}

// WatchdogConfig 看门狗配置
type WatchdogConfig struct {
	// Enabled 是否启用看门狗
	Enabled bool `mapstructure:"enabled" yaml:"enabled" json:"enabled"`

	// TimeSliceMs 每个设备的时间片长度（毫秒）
	TimeSliceMs int `mapstructure:"time_slice_ms" yaml:"time_slice_ms" json:"time_slice_ms"`

	// SwitchDelayMs 设备切换间隔（毫秒）
	SwitchDelayMs int `mapstructure:"switch_delay_ms" yaml:"switch_delay_ms" json:"switch_delay_ms"`

	// MaxWaitTimeoutMs 最大等待调度超时（毫秒）
	MaxWaitTimeoutMs int `mapstructure:"max_wait_timeout_ms" yaml:"max_wait_timeout_ms" json:"max_wait_timeout_ms"`
}

// SerialSchedulerConfig 串口调度器配置
type SerialSchedulerConfig struct {
	// Enabled 是否启用串口调度
	Enabled bool `mapstructure:"enabled" yaml:"enabled" json:"enabled"`

	// TimeSliceMs 每个设备的时间片长度（毫秒）
	TimeSliceMs int `mapstructure:"time_slice_ms" yaml:"time_slice_ms" json:"time_slice_ms"`

	// SwitchDelayMs 设备切换间隔（毫秒）
	SwitchDelayMs int `mapstructure:"switch_delay_ms" yaml:"switch_delay_ms" json:"switch_delay_ms"`

	// MaxWaitTimeoutMs 最大等待调度超时（毫秒）
	MaxWaitTimeoutMs int `mapstructure:"max_wait_timeout_ms" yaml:"max_wait_timeout_ms" json:"max_wait_timeout_ms"`
}

// PreAuthConfig 预授权配置
type PreAuthConfig struct {
	// Enabled 是否启用预授权模式
	Enabled bool `mapstructure:"enabled" yaml:"enabled" json:"enabled"`

	// DefaultTTL 预授权缓存默认过期时间
	DefaultTTL time.Duration `mapstructure:"default_ttl" yaml:"default_ttl" json:"default_ttl"`
}

// AutoAuthConfig 自动授权配置
type AutoAuthConfig struct {
	// Enabled 是否启用自动授权模式
	Enabled bool `mapstructure:"enabled" yaml:"enabled" json:"enabled"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置环境变量前缀
	viper.SetEnvPrefix("FCC")
	viper.AutomaticEnv()

	// 从环境变量中读取敏感信息
	viper.BindEnv("database.host", "FCC_DATABASE_HOST")
	viper.BindEnv("database.port", "FCC_DATABASE_PORT")
	viper.BindEnv("database.database", "FCC_DATABASE_NAME")
	viper.BindEnv("database.username", "FCC_DATABASE_USER")
	viper.BindEnv("database.password", "FCC_DATABASE_PASSWORD")
	viper.BindEnv("database.ssl_mode", "FCC_DATABASE_SSLMODE")
	viper.BindEnv("redis.password", "FCC_REDIS_PASSWORD")
	viper.BindEnv("fcc.device_management.auth_token", "FCC_DEVICE_MANAGEMENT_AUTH_TOKEN")
	viper.BindEnv("fcc.business_services.auth_token", "FCC_BUSINESS_SERVICES_AUTH_TOKEN")
	viper.BindEnv("fcc.api_gateway.auth_token", "FCC_API_GATEWAY_AUTH_TOKEN")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// validateConfig 验证配置参数
func validateConfig(config *Config) error {
	// HTTP配置验证
	if config.HTTP.Addr == "" {
		return fmt.Errorf("http.addr is required")
	}

	// 数据库配置验证
	if config.Database.Host == "" {
		return fmt.Errorf("database.host is required")
	}
	if config.Database.Database == "" {
		return fmt.Errorf("database.database is required")
	}
	if config.Database.Username == "" {
		return fmt.Errorf("database.username is required")
	}

	// 时区配置验证和默认值设置
	if config.Database.TimeZone == "" {
		config.Database.TimeZone = "Asia/Jakarta" // 默认时区
	}
	// 验证时区有效性
	if _, err := time.LoadLocation(config.Database.TimeZone); err != nil {
		return fmt.Errorf("database.timezone '%s' is invalid: %w", config.Database.TimeZone, err)
	}

	// DART协议配置验证
	if config.DART.Discovery.AddressRangeMin < 0x50 || config.DART.Discovery.AddressRangeMin > 0x6F {
		return fmt.Errorf("dart.discovery.address_range_min must be between 0x50 and 0x6F")
	}
	if config.DART.Discovery.AddressRangeMax < 0x50 || config.DART.Discovery.AddressRangeMax > 0x6F {
		return fmt.Errorf("dart.discovery.address_range_max must be between 0x50 and 0x6F")
	}
	if config.DART.Discovery.AddressRangeMin > config.DART.Discovery.AddressRangeMax {
		return fmt.Errorf("dart.discovery.address_range_min cannot be greater than address_range_max")
	}

	// DART协议标准验证
	// if config.DART.Protocol.ResponseTimeout > 25*time.Millisecond {
	// 	return fmt.Errorf("dart.protocol.response_timeout cannot exceed 25ms (DART standard)")
	// }

	// 串口配置验证
	if len(config.DART.Serial.DevicePatterns) == 0 {
		return fmt.Errorf("dart.serial.device_patterns cannot be empty")
	}
	if config.DART.Serial.BaudRate != 9600 && config.DART.Serial.BaudRate != 19200 {
		return fmt.Errorf("dart.serial.baud_rate must be 9600 or 19200")
	}

	return nil
}

// GetDatabaseDSN 获取数据库连接字符串
func (c *Config) GetDatabaseDSN() string {
	password := c.Database.Password
	if password == "" {
		password = os.Getenv("FCC_DATABASE_PASSWORD")
	}

	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.Username,
		password,
		c.Database.Database,
		c.Database.SSLMode,
		c.Database.TimeZone,
	)
}

// GetRedisAddr 获取Redis连接地址
func (c *Config) GetRedisAddr() string {
	return c.Redis.Host + ":" + fmt.Sprintf("%d", c.Redis.Port)
}

// GetRedisPassword 获取Redis密码
func (c *Config) GetRedisPassword() string {
	password := c.Redis.Password
	if password == "" {
		password = os.Getenv("FCC_REDIS_PASSWORD")
	}
	return password
}

// GetTimezone 获取配置的时区对象
func (c *Config) GetTimezone() (*time.Location, error) {
	return time.LoadLocation(c.Database.TimeZone)
}

// GetTimezoneOrDefault 获取配置的时区对象，失败时返回默认时区
func (c *Config) GetTimezoneOrDefault(defaultTimezone string) *time.Location {
	if location, err := time.LoadLocation(c.Database.TimeZone); err == nil {
		return location
	}

	if defaultTimezone != "" {
		if location, err := time.LoadLocation(defaultTimezone); err == nil {
			return location
		}
	}

	// 最后降级到UTC
	return time.UTC
}

// GetLogger 根据配置创建日志记录器
func (c *Config) GetLogger() (*zap.Logger, error) {
	// 确定日志级别
	level := zap.InfoLevel
	switch c.Logging.Level {
	case "debug":
		level = zap.DebugLevel
	case "info":
		level = zap.InfoLevel
	case "warn":
		level = zap.WarnLevel
	case "error":
		level = zap.ErrorLevel
	}

	// 创建编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	if c.Logging.Level == "debug" {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
	}

	// 设置时间格式
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建编码器
	var encoder zapcore.Encoder
	if c.Logging.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 收集所有的写入目标
	var cores []zapcore.Core

	// 控制台输出
	if c.Logging.Console.Enabled {
		consoleCore := zapcore.NewCore(
			encoder,
			zapcore.AddSync(os.Stdout),
			level,
		)
		cores = append(cores, consoleCore)
	}

	// 文件输出
	if c.Logging.File.Enabled && c.Logging.File.Path != "" {
		// 确保日志目录存在
		logDir := filepath.Dir(c.Logging.File.Path)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}

		// 配置日志轮转
		fileWriter := &lumberjack.Logger{
			Filename:   c.Logging.File.Path,
			MaxSize:    c.Logging.File.MaxSize,    // MB
			MaxBackups: c.Logging.File.MaxBackups, // 保留备份数
			MaxAge:     c.Logging.File.MaxAge,     // 天数
			Compress:   c.Logging.File.Compress,   // 压缩旧文件
		}

		fileCore := zapcore.NewCore(
			encoder,
			zapcore.AddSync(fileWriter),
			level,
		)
		cores = append(cores, fileCore)
	}

	// 如果没有启用任何输出，默认使用控制台输出
	if len(cores) == 0 {
		consoleCore := zapcore.NewCore(
			encoder,
			zapcore.AddSync(os.Stdout),
			level,
		)
		cores = append(cores, consoleCore)
	}

	// 合并所有核心
	core := zapcore.NewTee(cores...)

	// 创建 logger
	logger := zap.New(core)

	// 在开发模式下添加调用者信息
	if c.Logging.Level == "debug" {
		logger = logger.WithOptions(zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	}

	return logger, nil
}

// IsDevelopment 判断是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.Logging.Level == "debug"
}

// IsProduction 判断是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Logging.Level == "info" || c.Logging.Level == "warn" || c.Logging.Level == "error"
}
