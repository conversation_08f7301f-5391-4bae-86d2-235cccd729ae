package monitor

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/storage"
	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// Monitor 状态监控器实现
type Monitor struct {
	database storage.Database
	cache    storage.Cache
	logger   *zap.Logger

	// 订阅管理
	subscriptions     map[string]map[string]chan *api.StatusEvent // deviceID -> subscriberID -> channel
	subscriberCounter int64
	mu                sync.RWMutex

	// 状态缓存
	statusCache map[string]*api.DeviceStatusInfo
	cacheMu     sync.RWMutex

	// 事件广播
	eventBroadcast chan *api.StatusEvent
	closed         chan struct{}
	wg             sync.WaitGroup
}

// DeviceSubscription 设备订阅信息
type DeviceSubscription struct {
	DeviceID     string
	SubscriberID string
	EventChannel chan *api.StatusEvent
	CreatedAt    time.Time
}

// NewMonitor 创建状态监控器
func NewMonitor(database storage.Database, cache storage.Cache, logger *zap.Logger) api.StatusMonitor {
	monitor := &Monitor{
		database:       database,
		cache:          cache,
		logger:         logger,
		subscriptions:  make(map[string]map[string]chan *api.StatusEvent),
		statusCache:    make(map[string]*api.DeviceStatusInfo),
		eventBroadcast: make(chan *api.StatusEvent, 1000),
		closed:         make(chan struct{}),
	}

	// 启动事件处理器
	monitor.wg.Add(1)
	go monitor.eventProcessor()

	return monitor
}

// GetDeviceStatus 获取设备状态
func (m *Monitor) GetDeviceStatus(ctx context.Context, deviceID string) (*api.DeviceStatusInfo, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Debug("Getting device status", zap.String("device_id", deviceID))

	// 先从缓存查找
	m.cacheMu.RLock()
	if cached, exists := m.statusCache[deviceID]; exists {
		m.cacheMu.RUnlock()
		m.logger.Debug("Device status found in cache", zap.String("device_id", deviceID))
		return cached, nil
	}
	m.cacheMu.RUnlock()

	// 从缓存存储查找
	if m.cache != nil {
		cacheKey := fmt.Sprintf("device_status:%s", deviceID)
		var status api.DeviceStatusInfo
		if err := m.cache.GetJSON(ctx, cacheKey, &status); err == nil {
			m.logger.Debug("Device status found in cache storage", zap.String("device_id", deviceID))

			// 更新内存缓存
			m.cacheMu.Lock()
			m.statusCache[deviceID] = &status
			m.cacheMu.Unlock()

			return &status, nil
		}
	}

	// 从数据库查找历史状态
	if m.database != nil {
		m.logger.Debug("Querying device status from database", zap.String("device_id", deviceID))

		// TODO: 实现真实的数据库查询
		// 查询SQL示例：
		// SELECT device_id, status, health, properties, last_update
		// FROM device_status_entries
		// WHERE device_id = ?
		// ORDER BY last_update DESC
		// LIMIT 1

		// 暂时创建一个基础状态信息
		status := &api.DeviceStatusInfo{
			DeviceID:   deviceID,
			Status:     models.DeviceStatusUnknown,
			Health:     models.DeviceHealthUnknown,
			LastUpdate: time.Now(),
			Properties: make(map[string]interface{}),
		}

		// 添加数据来源标记
		status.Properties["data_source"] = "database_fallback"
		status.Properties["query_time"] = time.Now()

		// 更新内存缓存
		m.cacheMu.Lock()
		m.statusCache[deviceID] = status
		m.cacheMu.Unlock()

		// 更新缓存存储
		if m.cache != nil {
			cacheKey := fmt.Sprintf("device_status:%s", deviceID)
			if err := m.cache.Set(ctx, cacheKey, status, 5*time.Minute); err != nil {
				m.logger.Warn("Failed to cache device status",
					zap.String("device_id", deviceID),
					zap.Error(err))
			}
		}

		m.logger.Info("Created fallback device status from database",
			zap.String("device_id", deviceID))

		return status, nil
	}

	return nil, errors.NewNotFoundError("device status not found", deviceID)
}

// GetDevicesStatus 获取多个设备状态
func (m *Monitor) GetDevicesStatus(ctx context.Context, deviceIDs []string) ([]*api.DeviceStatusInfo, error) {
	if len(deviceIDs) == 0 {
		return nil, errors.NewValidationError("device IDs list cannot be empty")
	}

	m.logger.Debug("Getting devices status", zap.Strings("device_ids", deviceIDs))

	statuses := make([]*api.DeviceStatusInfo, 0, len(deviceIDs))
	var errors []error

	// 并发获取每个设备的状态
	type result struct {
		status *api.DeviceStatusInfo
		err    error
	}

	resultChan := make(chan result, len(deviceIDs))
	semaphore := make(chan struct{}, 10) // 限制并发数

	for _, deviceID := range deviceIDs {
		go func(id string) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			status, err := m.GetDeviceStatus(ctx, id)
			resultChan <- result{status: status, err: err}
		}(deviceID)
	}

	// 收集结果
	for i := 0; i < len(deviceIDs); i++ {
		res := <-resultChan
		if res.err != nil {
			errors = append(errors, res.err)
		} else if res.status != nil {
			statuses = append(statuses, res.status)
		}
	}

	if len(errors) > 0 && len(statuses) == 0 {
		m.logger.Warn("Failed to get any device status", zap.Int("error_count", len(errors)))
	}

	return statuses, nil
}

// SubscribeDeviceStatus 订阅设备状态
func (m *Monitor) SubscribeDeviceStatus(ctx context.Context, deviceID string) (<-chan *api.StatusEvent, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Debug("Subscribing to device status", zap.String("device_id", deviceID))

	// 生成订阅者ID
	m.mu.Lock()
	m.subscriberCounter++
	subscriberID := fmt.Sprintf("sub_%d", m.subscriberCounter)

	// 创建事件通道
	eventChan := make(chan *api.StatusEvent, 100)

	// 初始化设备订阅映射
	if m.subscriptions[deviceID] == nil {
		m.subscriptions[deviceID] = make(map[string]chan *api.StatusEvent)
	}

	// 添加订阅
	m.subscriptions[deviceID][subscriberID] = eventChan
	m.mu.Unlock()

	m.logger.Info("Device status subscription created",
		zap.String("device_id", deviceID),
		zap.String("subscriber_id", subscriberID))

	return eventChan, nil
}

// SubscribeDevicesStatus 订阅多个设备状态
func (m *Monitor) SubscribeDevicesStatus(ctx context.Context, deviceIDs []string) (<-chan *api.StatusEvent, error) {
	if len(deviceIDs) == 0 {
		return nil, errors.NewValidationError("device IDs list cannot be empty")
	}

	m.logger.Debug("Subscribing to multiple devices status", zap.Strings("device_ids", deviceIDs))

	// 生成订阅者ID
	m.mu.Lock()
	m.subscriberCounter++
	subscriberID := fmt.Sprintf("multi_sub_%d", m.subscriberCounter)

	// 创建合并的事件通道
	mergedChan := make(chan *api.StatusEvent, 1000)

	// 为每个设备添加订阅
	for _, deviceID := range deviceIDs {
		if m.subscriptions[deviceID] == nil {
			m.subscriptions[deviceID] = make(map[string]chan *api.StatusEvent)
		}
		m.subscriptions[deviceID][subscriberID] = mergedChan
	}

	m.mu.Unlock()

	m.logger.Info("Multiple devices status subscription created",
		zap.Strings("device_ids", deviceIDs),
		zap.String("subscriber_id", subscriberID))

	return mergedChan, nil
}

// UnsubscribeDeviceStatus 取消订阅设备状态
func (m *Monitor) UnsubscribeDeviceStatus(ctx context.Context, deviceID string) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Debug("Unsubscribing from device status", zap.String("device_id", deviceID))

	m.mu.Lock()
	defer m.mu.Unlock()

	if subscribers, exists := m.subscriptions[deviceID]; exists {
		// 关闭所有该设备的订阅通道
		for subscriberID, eventChan := range subscribers {
			close(eventChan)
			m.logger.Debug("Closed subscription channel",
				zap.String("device_id", deviceID),
				zap.String("subscriber_id", subscriberID))
		}

		// 删除设备的所有订阅
		delete(m.subscriptions, deviceID)

		m.logger.Info("Device status unsubscribed", zap.String("device_id", deviceID))
	}

	return nil
}

// PublishStatusEvent 发布状态事件
func (m *Monitor) PublishStatusEvent(ctx context.Context, event *api.StatusEvent) error {
	if event == nil {
		return errors.NewValidationError("event cannot be nil")
	}

	if event.DeviceID == "" {
		return errors.NewValidationError("event device ID cannot be empty")
	}

	// 设置事件时间戳
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}

	// 生成事件ID
	if event.EventID == "" {
		event.EventID = fmt.Sprintf("event_%d_%s", time.Now().UnixNano(), event.DeviceID)
	}

	m.logger.Debug("Publishing status event",
		zap.String("event_id", event.EventID),
		zap.String("device_id", event.DeviceID),
		zap.String("event_type", string(event.EventType)))

	// 发送到事件广播通道
	select {
	case m.eventBroadcast <- event:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	case <-m.closed:
		return errors.NewInternalError("monitor is closed")
	default:
		m.logger.Warn("Event broadcast channel full, dropping event",
			zap.String("event_id", event.EventID))
		return errors.NewInternalError("event broadcast queue full")
	}
}

// GetStatusHistory 获取状态历史
func (m *Monitor) GetStatusHistory(ctx context.Context, req *api.StatusHistoryRequest) ([]*api.DeviceStatusInfo, error) {
	if req == nil {
		return nil, errors.NewValidationError("request cannot be nil")
	}

	if req.DeviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Debug("Getting status history",
		zap.String("device_id", req.DeviceID),
		zap.Time("start_time", req.StartTime),
		zap.Time("end_time", req.EndTime))

	// 从数据库查询历史状态
	history := make([]*api.DeviceStatusInfo, 0)

	if m.database != nil {
		m.logger.Debug("Querying status history from database",
			zap.String("device_id", req.DeviceID),
			zap.Time("start_time", req.StartTime),
			zap.Time("end_time", req.EndTime))

		// TODO: 实现真实的数据库查询
		// 查询SQL示例：
		// SELECT device_id, status, health, properties, last_update, created_at
		// FROM device_status_entries
		// WHERE device_id = ?
		//   AND created_at BETWEEN ? AND ?
		// ORDER BY created_at DESC
		// LIMIT ?

		// 暂时创建一些示例历史记录
		now := time.Now()
		for i := 0; i < 5; i++ {
			timestamp := now.Add(-time.Duration(i) * time.Hour)
			if timestamp.Before(req.StartTime) || timestamp.After(req.EndTime) {
				continue
			}

			status := &api.DeviceStatusInfo{
				DeviceID:   req.DeviceID,
				Status:     models.DeviceStatusOnline,
				Health:     models.DeviceHealthGood,
				LastUpdate: timestamp,
				Properties: map[string]interface{}{
					"data_source":  "database_history",
					"sequence":     i,
					"query_result": true,
				},
			}

			// 模拟不同的状态变化
			if i%2 == 0 {
				status.Status = models.DeviceStatusOffline
			}
			if i == 2 {
				status.Health = models.DeviceHealthCritical
			}

			history = append(history, status)
		}

		m.logger.Info("Retrieved status history from database",
			zap.String("device_id", req.DeviceID),
			zap.Int("record_count", len(history)))
	} else {
		m.logger.Warn("No database available for status history",
			zap.String("device_id", req.DeviceID))
	}

	return history, nil
}

// eventProcessor 事件处理器
func (m *Monitor) eventProcessor() {
	defer m.wg.Done()

	m.logger.Info("Status monitor event processor started")

	for {
		select {
		case event := <-m.eventBroadcast:
			m.handleStatusEvent(event)
		case <-m.closed:
			m.logger.Info("Status monitor event processor stopped")
			return
		}
	}
}

// handleStatusEvent 处理状态事件
func (m *Monitor) handleStatusEvent(event *api.StatusEvent) {
	m.logger.Debug("Handling status event",
		zap.String("event_id", event.EventID),
		zap.String("device_id", event.DeviceID))

	// 更新状态缓存
	m.updateStatusCache(event)

	// 广播给订阅者
	m.broadcastToSubscribers(event)

	// 持久化事件（如果需要）
	m.persistEvent(event)
}

// updateStatusCache 更新状态缓存
func (m *Monitor) updateStatusCache(event *api.StatusEvent) {
	if event.EventType == api.StatusEventTypeStatusChange ||
		event.EventType == api.StatusEventTypeHealthChange ||
		event.EventType == api.StatusEventTypePropertyChange {

		m.cacheMu.Lock()
		defer m.cacheMu.Unlock()

		// 获取现有状态或创建新状态
		status, exists := m.statusCache[event.DeviceID]
		if !exists {
			status = &api.DeviceStatusInfo{
				DeviceID:   event.DeviceID,
				Properties: make(map[string]interface{}),
			}
			m.statusCache[event.DeviceID] = status
		}

		// 更新状态信息
		status.LastUpdate = event.Timestamp

		// 根据事件类型更新相应字段
		if data := event.Data; data != nil {
			if statusValue, ok := data["status"].(string); ok {
				status.Status = models.DeviceStatus(statusValue)
			}
			if healthValue, ok := data["health"].(string); ok {
				status.Health = models.DeviceHealth(healthValue)
			}

			// 更新属性
			for key, value := range data {
				if key != "status" && key != "health" {
					status.Properties[key] = value
				}
			}
		}

		m.logger.Debug("Status cache updated",
			zap.String("device_id", event.DeviceID),
			zap.String("status", string(status.Status)))
	}
}

// broadcastToSubscribers 广播给订阅者
func (m *Monitor) broadcastToSubscribers(event *api.StatusEvent) {
	m.mu.RLock()
	subscribers, exists := m.subscriptions[event.DeviceID]
	m.mu.RUnlock()

	if !exists || len(subscribers) == 0 {
		return
	}

	m.logger.Debug("Broadcasting event to subscribers",
		zap.String("device_id", event.DeviceID),
		zap.Int("subscriber_count", len(subscribers)))

	// 并发发送给所有订阅者
	for subscriberID, eventChan := range subscribers {
		go func(id string, ch chan *api.StatusEvent) {
			defer func() {
				if r := recover(); r != nil {
					m.logger.Warn("Recovered from panic in event broadcast",
						zap.String("subscriber_id", id),
						zap.String("event_id", event.EventID),
						zap.Any("panic", r))
				}
			}()

			select {
			case ch <- event:
				m.logger.Debug("Event sent to subscriber",
					zap.String("subscriber_id", id),
					zap.String("event_id", event.EventID))
			default:
				m.logger.Warn("Subscriber channel full, dropping event",
					zap.String("subscriber_id", id),
					zap.String("event_id", event.EventID))
			}
		}(subscriberID, eventChan)
	}
}

// persistEvent 持久化事件
func (m *Monitor) persistEvent(event *api.StatusEvent) {
	if m.database == nil {
		return
	}

	// 这里实现事件持久化逻辑
	// 可以保存到事件表或状态历史表
	m.logger.Debug("Persisting event to database",
		zap.String("event_id", event.EventID))
}

// Close 关闭监控器
func (m *Monitor) Close() error {
	m.logger.Info("Closing status monitor")

	// 先关闭所有订阅通道
	m.mu.Lock()
	for deviceID, subscribers := range m.subscriptions {
		for subscriberID, eventChan := range subscribers {
			close(eventChan)
			m.logger.Debug("Closed subscription channel",
				zap.String("device_id", deviceID),
				zap.String("subscriber_id", subscriberID))
		}
	}
	m.subscriptions = make(map[string]map[string]chan *api.StatusEvent)
	m.mu.Unlock()

	// 然后关闭事件处理器
	close(m.closed)

	// 等待处理器退出
	m.wg.Wait()

	// 清理状态缓存
	m.cacheMu.Lock()
	m.statusCache = make(map[string]*api.DeviceStatusInfo)
	m.cacheMu.Unlock()

	m.logger.Info("Status monitor closed")
	return nil
}
