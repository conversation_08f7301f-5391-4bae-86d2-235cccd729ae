package monitor

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/services/transaction"
)

// MonitorState 监控状态
type MonitorState string

const (
	StateOK       MonitorState = "OK"
	StateAlerting MonitorState = "ALERTING"
)

// SyncMonitorConfig 同步监控配置
type SyncMonitorConfig struct {
	// 事件驱动部分
	FailureThreshold int64 `json:"failure_threshold"` // 触发快速告警的失败次数

	// 轮询保底部分
	PollingInterval time.Duration `json:"polling_interval"` // 轮询周期
	PollingEnabled  bool          `json:"polling_enabled"`  // 是否启用轮询

	// 熔断机制
	CooldownPeriod time.Duration `json:"cooldown_period"` // 告警冷却时间

	// 轮询检查关键指标阈值
	MaxFailedCount   int64 `json:"max_failed_count"`   // 最大失败数阈值
	MaxRetryingCount int64 `json:"max_retrying_count"` // 最大重试数阈值
	MaxPendingCount  int64 `json:"max_pending_count"`  // 最大待处理数阈值
	MaxPendingAge    int64 `json:"max_pending_age"`    // 最长未处理时间（小时）

	// 功能开关
	AlertingEnabled bool `json:"alerting_enabled"` // 是否启用告警
}

// TransactionSummaryProvider 交易汇总提供者接口
type TransactionSummaryProvider interface {
	GetUnuploadedTransactionsSummary(ctx context.Context) (*transaction.UnuploadedTransactionSummary, error)
}

// SyncMonitor 同步监控服务
type SyncMonitor struct {
	transactionService TransactionSummaryProvider
	logger             *zap.Logger

	// 状态与熔断机制
	mu            sync.RWMutex
	state         MonitorState
	cooldownUntil time.Time

	// 事件驱动部分
	failureCounter atomic.Int64  // 原子失败计数器
	failureChan    chan struct{} // 失败信号通道

	// 轮询保底部分
	ticker *time.Ticker
	done   chan bool

	// 配置
	config SyncMonitorConfig

	// 统计信息
	alertCount        atomic.Int64 // 告警计数
	pollingCheckCount atomic.Int64 // 轮询检查计数

	// 生命周期管理
	started atomic.Bool
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewSyncMonitor 创建并初始化监视器
func NewSyncMonitor(transactionService TransactionSummaryProvider, config SyncMonitorConfig, logger *zap.Logger) *SyncMonitor {
	// 验证和设置默认配置
	config = validateAndSetDefaults(config)

	monitor := &SyncMonitor{
		transactionService: transactionService,
		logger:             logger,
		state:              StateOK,
		failureChan:        make(chan struct{}, 1000), // 缓冲通道避免阻塞
		done:               make(chan bool, 1),
		config:             config,
	}

	logger.Info("SyncMonitor created",
		zap.Int64("failure_threshold", config.FailureThreshold),
		zap.Duration("polling_interval", config.PollingInterval),
		zap.Duration("cooldown_period", config.CooldownPeriod),
		zap.Bool("alerting_enabled", config.AlertingEnabled),
		zap.Bool("polling_enabled", config.PollingEnabled))

	return monitor
}

// validateAndSetDefaults 验证配置并设置默认值
func validateAndSetDefaults(config SyncMonitorConfig) SyncMonitorConfig {
	if config.FailureThreshold <= 0 {
		config.FailureThreshold = 10 // 默认1分钟内失败10次
	}
	if config.PollingInterval <= 0 {
		config.PollingInterval = 10 * time.Minute // 默认10分钟轮询
	}
	if config.CooldownPeriod <= 0 {
		config.CooldownPeriod = 30 * time.Minute // 默认30分钟冷却期
	}
	if config.MaxFailedCount <= 0 {
		config.MaxFailedCount = 100 // 默认失败数阈值
	}
	if config.MaxRetryingCount <= 0 {
		config.MaxRetryingCount = 50 // 默认重试数阈值
	}
	if config.MaxPendingCount <= 0 {
		config.MaxPendingCount = 200 // 默认待处理数阈值
	}
	if config.MaxPendingAge <= 0 {
		config.MaxPendingAge = 24 // 默认24小时
	}
	return config
}

// Start 启动监控服务
func (sm *SyncMonitor) Start(ctx context.Context) error {
	if !sm.started.CompareAndSwap(false, true) {
		return fmt.Errorf("sync monitor is already started")
	}

	sm.ctx, sm.cancel = context.WithCancel(ctx)

	sm.logger.Info("Starting SyncMonitor",
		zap.String("initial_state", string(sm.state)),
		zap.Duration("polling_interval", sm.config.PollingInterval),
		zap.Int64("failure_threshold", sm.config.FailureThreshold))

	// 启动主监控循环
	go sm.monitorLoop()

	// 启动轮询检查（如果启用）
	if sm.config.PollingEnabled {
		go sm.pollingLoop()
	}

	// 启动冷却重置循环
	go sm.cooldownResetLoop()

	sm.logger.Info("SyncMonitor started successfully")
	return nil
}

// Stop 停止监控服务
func (sm *SyncMonitor) Stop() {
	if !sm.started.CompareAndSwap(true, false) {
		return
	}

	sm.logger.Info("Stopping SyncMonitor")

	if sm.cancel != nil {
		sm.cancel()
	}

	if sm.ticker != nil {
		sm.ticker.Stop()
	}

	select {
	case sm.done <- true:
	default:
	}

	sm.logger.Info("SyncMonitor stopped",
		zap.Int64("total_alerts", sm.alertCount.Load()),
		zap.Int64("total_polling_checks", sm.pollingCheckCount.Load()))
}

// NotifyFailure 通知失败事件
func (sm *SyncMonitor) NotifyFailure() {
	currentCount := sm.failureCounter.Add(1)

	sm.logger.Debug("Failure notification received",
		zap.Int64("current_failure_count", currentCount),
		zap.Int64("threshold", sm.config.FailureThreshold))

	// 检查是否达到阈值
	if currentCount >= sm.config.FailureThreshold {
		sm.logger.Warn("Failure threshold reached, attempting to trip breaker",
			zap.Int64("failure_count", currentCount),
			zap.Int64("threshold", sm.config.FailureThreshold))

		sm.tripBreaker("failure_rate")

		// 重置计数器
		sm.failureCounter.Store(0)
	}

	// 发送信号到失败通道（非阻塞）
	select {
	case sm.failureChan <- struct{}{}:
	default:
		sm.logger.Debug("Failure channel is full, skipping signal")
	}
}

// GetState 获取当前状态
func (sm *SyncMonitor) GetState() MonitorState {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.state
}

// IsInCooldown 检查是否在冷却期
func (sm *SyncMonitor) IsInCooldown() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return time.Now().Before(sm.cooldownUntil)
}

// GetAlertCount 获取告警计数
func (sm *SyncMonitor) GetAlertCount() int64 {
	return sm.alertCount.Load()
}

// GetPollingCheckCount 获取轮询检查计数
func (sm *SyncMonitor) GetPollingCheckCount() int64 {
	return sm.pollingCheckCount.Load()
}

// GetConfig 获取配置
func (sm *SyncMonitor) GetConfig() SyncMonitorConfig {
	return sm.config
}

// CheckAndResetCooldown 公共方法：检查并重置冷却状态（主要用于测试）
func (sm *SyncMonitor) CheckAndResetCooldown() {
	sm.checkAndResetCooldown()
}

// monitorLoop 主监控循环
func (sm *SyncMonitor) monitorLoop() {
	sm.logger.Info("Monitor loop started")

	for {
		select {
		case <-sm.ctx.Done():
			sm.logger.Info("Monitor loop stopped due to context cancellation")
			return

		case <-sm.done:
			sm.logger.Info("Monitor loop stopped due to done signal")
			return

		case <-sm.failureChan:
			sm.logger.Debug("Received failure signal in monitor loop")
			// 失败信号已经在 NotifyFailure 中处理
		}
	}
}

// pollingLoop 轮询检查循环
func (sm *SyncMonitor) pollingLoop() {
	sm.ticker = time.NewTicker(sm.config.PollingInterval)
	defer sm.ticker.Stop()

	sm.logger.Info("Polling loop started",
		zap.Duration("interval", sm.config.PollingInterval))

	for {
		select {
		case <-sm.ctx.Done():
			sm.logger.Info("Polling loop stopped due to context cancellation")
			return

		case <-sm.done:
			sm.logger.Info("Polling loop stopped due to done signal")
			return

		case <-sm.ticker.C:
			sm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (sm *SyncMonitor) performHealthCheck() {
	sm.pollingCheckCount.Add(1)

	sm.logger.Debug("Performing polling health check",
		zap.Int64("check_count", sm.pollingCheckCount.Load()))

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	summary, err := sm.transactionService.GetUnuploadedTransactionsSummary(ctx)
	if err != nil {
		sm.logger.Error("Failed to get transaction summary during health check",
			zap.Error(err))
		sm.tripBreaker("health_check_error")
		return
	}

	// 检查各项指标
	var issues []string
	if summary.TotalFailed > sm.config.MaxFailedCount {
		issues = append(issues, fmt.Sprintf("high_failed_count:%d", summary.TotalFailed))
	}
	if summary.TotalRetrying > sm.config.MaxRetryingCount {
		issues = append(issues, fmt.Sprintf("high_retrying_count:%d", summary.TotalRetrying))
	}
	if summary.TotalPending > sm.config.MaxPendingCount {
		issues = append(issues, fmt.Sprintf("high_pending_count:%d", summary.TotalPending))
	}

	// 检查最老的待处理交易
	if summary.OldestPending != nil {
		age := time.Since(*summary.OldestPending)
		maxAge := time.Duration(sm.config.MaxPendingAge) * time.Hour
		if age > maxAge {
			issues = append(issues, fmt.Sprintf("old_pending_transaction:%s", age.String()))
		}
	}

	if len(issues) > 0 {
		sm.logger.Warn("Health check detected issues",
			zap.Strings("issues", issues),
			zap.Int64("total_failed", summary.TotalFailed),
			zap.Int64("total_retrying", summary.TotalRetrying),
			zap.Int64("total_pending", summary.TotalPending))

		sm.tripBreaker("polling_check")
	} else {
		sm.logger.Debug("Health check passed",
			zap.Int64("total_failed", summary.TotalFailed),
			zap.Int64("total_retrying", summary.TotalRetrying),
			zap.Int64("total_pending", summary.TotalPending))
	}
}

// tripBreaker 触发熔断器
func (sm *SyncMonitor) tripBreaker(reason string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// 检查是否在冷却期
	if time.Now().Before(sm.cooldownUntil) {
		sm.logger.Info("Alerting is currently snoozed, new trigger ignored",
			zap.String("reason", reason),
			zap.Duration("cooldown_remaining", time.Until(sm.cooldownUntil)))
		return
	}

	// 检查当前状态
	if sm.state == StateOK {
		// 从OK状态切换到ALERTING
		sm.state = StateAlerting
		sm.cooldownUntil = time.Now().Add(sm.config.CooldownPeriod)
		sm.alertCount.Add(1)

		sm.logger.Warn("Sync failures exceeded threshold, circuit breaker opened",
			zap.String("reason", reason),
			zap.String("previous_state", "OK"),
			zap.String("new_state", "ALERTING"),
			zap.Duration("cooldown_period", sm.config.CooldownPeriod),
			zap.Time("cooldown_until", sm.cooldownUntil),
			zap.Int64("alert_count", sm.alertCount.Load()))

		// 这里可以集成实际的告警系统（邮件、短信、Webhook等）
		if sm.config.AlertingEnabled {
			sm.sendAlert(reason)
		}
	} else {
		sm.logger.Debug("Circuit breaker already in ALERTING state, ignoring trigger",
			zap.String("reason", reason),
			zap.Duration("cooldown_remaining", time.Until(sm.cooldownUntil)))
	}
}

// cooldownResetLoop 冷却重置循环
func (sm *SyncMonitor) cooldownResetLoop() {
	sm.logger.Info("Cooldown reset loop started")

	ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-sm.ctx.Done():
			sm.logger.Info("Cooldown reset loop stopped due to context cancellation")
			return

		case <-sm.done:
			sm.logger.Info("Cooldown reset loop stopped due to done signal")
			return

		case <-ticker.C:
			sm.checkAndResetCooldown()
		}
	}
}

// checkAndResetCooldown 检查并重置冷却状态
func (sm *SyncMonitor) checkAndResetCooldown() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if sm.state == StateAlerting && time.Now().After(sm.cooldownUntil) {
		sm.state = StateOK
		sm.logger.Info("Circuit breaker closed, monitoring resumed",
			zap.String("previous_state", "ALERTING"),
			zap.String("new_state", "OK"),
			zap.Int64("total_alerts", sm.alertCount.Load()))
	}
}

// sendAlert 发送告警（可扩展集成外部告警系统）
func (sm *SyncMonitor) sendAlert(reason string) {
	sm.logger.Error("🚨 SYNC ALERT: Transaction sync system requires attention",
		zap.String("alert_reason", reason),
		zap.String("monitor_state", string(sm.state)),
		zap.Int64("alert_count", sm.alertCount.Load()),
		zap.Duration("cooldown_period", sm.config.CooldownPeriod),
		zap.String("action_required", "Check transaction sync status and resolve issues"))

	// TODO: 集成外部告警系统
	// - 发送邮件通知
	// - 发送短信告警
	// - 调用Webhook
	// - 集成Slack/Teams通知
	// - 写入告警数据库
}
