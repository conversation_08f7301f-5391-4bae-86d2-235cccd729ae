package nozzle_counters

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"fcc-service/pkg/models"
)

// gormRepository GORM数据库实现
type gormRepository struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewGormRepository 创建GORM仓储实现
func NewGormRepository(db *gorm.DB, logger *zap.Logger) Repository {
	return &gormRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建计数器记录
func (r *gormRepository) Create(ctx context.Context, record *models.NozzleCounters) error {
	if err := r.db.WithContext(ctx).Create(record).Error; err != nil {
		return fmt.Errorf("failed to create nozzle counter record: %w", err)
	}
	return nil
}

// GetLatestByType 根据设备ID、喷嘴ID和计数器类型获取最新记录
func (r *gormRepository) GetLatestByType(ctx context.Context, deviceID string, nozzleID string, counterType int16) (*models.NozzleCounters, error) {
	var record models.NozzleCounters
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND nozzle_id = ? AND counter_type = ?", deviceID, nozzleID, counterType).
		Order("recorded_at DESC").
		First(&record).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有记录是正常情况
		}
		return nil, fmt.Errorf("failed to get latest counter record: %w", err)
	}

	return &record, nil
}

// GetHistoryByType 获取指定类型的历史记录
func (r *gormRepository) GetHistoryByType(ctx context.Context, deviceID string, nozzleID string, counterType int16, limit int) ([]*models.NozzleCounters, error) {
	var records []*models.NozzleCounters

	query := r.db.WithContext(ctx).
		Where("device_id = ? AND nozzle_id = ? AND counter_type = ?", deviceID, nozzleID, counterType).
		Order("recorded_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get counter history: %w", err)
	}

	return records, nil
}

// GetUnassociatedEventsSince 获取指定时间之后的所有未关联交易的计数器事件
func (r *gormRepository) GetUnassociatedEventsSince(ctx context.Context, deviceID string, nozzleID string, since time.Time, limit int) ([]*models.NozzleCounters, error) {
	var records []*models.NozzleCounters

	query := r.db.WithContext(ctx).
		Where("device_id = ? AND nozzle_id = ? AND recorded_at >= ? AND (transaction_id = ? OR transaction_id IS NULL)",
			deviceID, nozzleID, since, "").
		Order("recorded_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get unassociated events since time: %w", err)
	}

	r.logger.Debug("Found unassociated counter events",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Time("since", since),
		zap.Int("count", len(records)),
		zap.Int("limit", limit))

	return records, nil
}

// UpdateTransactionID 更新指定事件的TransactionID
func (r *gormRepository) UpdateTransactionID(ctx context.Context, eventID int64, transactionID string) error {
	result := r.db.WithContext(ctx).
		Model(&models.NozzleCounters{}).
		Where("id = ?", eventID).
		Update("transaction_id", transactionID)

	if result.Error != nil {
		return fmt.Errorf("failed to update transaction_id for event %d: %w", eventID, result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("event with ID %d not found", eventID)
	}

	r.logger.Debug("Updated transaction_id for counter event",
		zap.Int64("event_id", eventID),
		zap.String("transaction_id", transactionID))

	return nil
}
