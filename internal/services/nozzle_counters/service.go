package nozzle_counters

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/lib/pq"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"fcc-service/internal/storage"
	"fcc-service/pkg/models"
)

// Service 喷嘴计数器服务接口 - 简化版，专注于DC101事件记录
type Service interface {
	// DC101事务处理 - 核心功能
	RecordDC101Event(ctx context.Context, deviceID string, nozzleID string, counterType int16, value decimal.Decimal, timestamp time.Time, dc101Data []byte) (*models.NozzleCounters, error)
	// 🆕 新增：带TransactionID关联的DC101事件记录，用于正确关联泵码与交易
	RecordDC101EventWithTransaction(ctx context.Context, deviceID string, nozzleID string, counterType int16, value decimal.Decimal, timestamp time.Time, dc101Data []byte, transactionID string) (*models.NozzleCounters, error)

	// 基础查询
	GetLatestCounterValue(ctx context.Context, deviceID string, nozzleID string, counterType int16) (decimal.Decimal, error)
	GetCounterHistory(ctx context.Context, deviceID string, nozzleID string, counterType int16, limit int) ([]*models.NozzleCounters, error)

	// 重构V2.1 - 反向匹配逻辑支持
	// 🆕 新增：获取未关联交易的DC101事件，用于反向匹配
	GetRecentUnassociatedCounterEvents(ctx context.Context, deviceID string, nozzleID string, since time.Time, limit int) ([]*models.NozzleCounters, error)
	// 🆕 新增：将已存在的DC101事件关联到特定交易
	AssociateEventWithTransaction(ctx context.Context, eventID int64, transactionID string) error

	// 事件通知
	RegisterUpdateListener(listener CounterUpdateListener)
	UnregisterUpdateListener(listener CounterUpdateListener)
}

// CounterUpdateListener 计数器更新监听器
type CounterUpdateListener interface {
	OnCounterUpdated(event *models.CounterUpdateEvent) error
	GetListenerID() string
}

// Repository 数据访问接口 - 简化版
type Repository interface {
	// 基础操作
	Create(ctx context.Context, record *models.NozzleCounters) error
	GetLatestByType(ctx context.Context, deviceID string, nozzleID string, counterType int16) (*models.NozzleCounters, error)
	GetHistoryByType(ctx context.Context, deviceID string, nozzleID string, counterType int16, limit int) ([]*models.NozzleCounters, error)

	// 重构V2.1 - 反向匹配逻辑支持
	// GetUnassociatedEventsSince 获取指定时间之后的所有未关联交易的计数器事件
	GetUnassociatedEventsSince(ctx context.Context, deviceID string, nozzleID string, since time.Time, limit int) ([]*models.NozzleCounters, error)
	// UpdateTransactionID 更新指定事件的TransactionID
	UpdateTransactionID(ctx context.Context, eventID int64, transactionID string) error
}

// service 服务实现
type service struct {
	repository Repository
	logger     *zap.Logger
	listeners  []CounterUpdateListener
	cache      storage.Cache
}

// NewService 创建喷嘴计数器服务
func NewService(repository Repository, logger *zap.Logger, cache storage.Cache) Service {
	return &service{
		repository: repository,
		logger:     logger,
		listeners:  make([]CounterUpdateListener, 0),
		cache:      cache,
	}
}

// RecordDC101Event 记录DC101事件（插入新的流水记录）
func (s *service) RecordDC101Event(ctx context.Context, deviceID string, nozzleID string, counterType int16, value decimal.Decimal, timestamp time.Time, dc101Data []byte) (*models.NozzleCounters, error) {
	return s.RecordDC101EventWithTransaction(ctx, deviceID, nozzleID, counterType, value, timestamp, dc101Data, "")
}

// RecordDC101EventWithTransaction 记录DC101事件（插入新的流水记录）并关联TransactionID
func (s *service) RecordDC101EventWithTransaction(ctx context.Context, deviceID string, nozzleID string, counterType int16, value decimal.Decimal, timestamp time.Time, dc101Data []byte, transactionID string) (*models.NozzleCounters, error) {
	// L1 - 高性能事件缓存 (The Fast Lane)
	// 目的: 拦截最高频的、由设备轮询产生的短期重复事件，避免访问数据库。
	cacheKey := fmt.Sprintf("event-dedup:%s:%s:%d:%s", deviceID, nozzleID, counterType, value.String())
	isNew, err := s.cache.SetNX(ctx, cacheKey, 1, 30*time.Minute)

	if err != nil {
		// 如果缓存操作失败（例如Redis连接问题），记录警告并继续执行，让L2和L3来兜底。
		s.logger.Warn("L1 cache SetNX for deduplication failed, proceeding to L2 logic",
			zap.String("cache_key", cacheKey),
			zap.Error(err))
	}

	if err == nil && !isNew {
		// 缓存命中，是短期重复事件，直接成功返回。
		s.logger.Debug("L1 cache hit: duplicate event skipped",
			zap.String("cache_key", cacheKey),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil, nil
	}

	// L2 - 有状态业务逻辑核心 (The Logic Core)
	previousRecord, err := s.repository.GetLatestByType(ctx, deviceID, nozzleID, counterType)
	// 🎯 关键修复：优雅地处理"记录未找到"的错误，这是首次记录时的正常情况。
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 只有在不是 "record not found" 错误时，才将其视为需要中断的严重错误。
		s.logger.Error("L2 failed to get previous counter value with unexpected error",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Int16("counter_type", counterType),
			zap.Error(err))
		return nil, fmt.Errorf("L2 failed to get previous counter value: %w", err)
	}
	// 如果错误是 gorm.ErrRecordNotFound，我们会忽略它，此时 previousRecord 为 nil，流程将自然地继续下去。

	if previousRecord != nil {
		// 场景1: 状态未变 (新值等于旧值)
		if value.Equal(previousRecord.CounterValue) {
			s.logger.Debug("L2 duplicate state detected: new value equals previous value",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.String("value", value.String()))
			return nil, nil
		}
		// 场景2: 乱序事件 (新事件的时间戳早于或等于旧事件)
		// 🔧 修复时区问题：统一转换为UTC时间进行比较
		newTimestampUTC := timestamp.UTC()
		previousTimestampUTC := previousRecord.RecordedAt.UTC()

		if !newTimestampUTC.After(previousTimestampUTC) {
			s.logger.Warn("L2 out-of-order event detected",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Time("new_timestamp", timestamp),
				zap.Time("new_timestamp_utc", newTimestampUTC),
				zap.Time("previous_timestamp", previousRecord.RecordedAt),
				zap.Time("previous_timestamp_utc", previousTimestampUTC))
			return nil, nil
		}
	}

	// 状态已改变，创建新的流水记录
	record := &models.NozzleCounters{
		DeviceID:             deviceID,
		NozzleID:             nozzleID,
		CounterType:          counterType,
		CounterValue:         value,
		DC101TransactionData: dc101Data,
		RecordedAt:           timestamp.UTC(), // 🔧 修复时区问题：统一存储为UTC时间
		IsValid:              true,
		IsAnomaly:            false,
		TransactionID:        transactionID, // 🎯 关键修复：正确设置TransactionID
	}

	// 计算增量值 (此方法会处理归零场景)
	var previousValue *decimal.Decimal
	if previousRecord != nil {
		previousValue = &previousRecord.CounterValue
	}
	record.SetIncrement(previousValue)

	// L3 - 数据库安全网 (The Safety Net)
	if err := s.repository.Create(ctx, record); err != nil {
		// 优雅地处理唯一性约束冲突，这在并发或极端情况下是预期的
		if isUniqueConstraintViolation(err) {
			s.logger.Warn("L3 unique constraint violation: concurrent insert detected and handled",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Time("recorded_at", timestamp),
				zap.Error(err))
			return nil, nil // 静默处理，因为另一条记录已成功插入
		}

		s.logger.Error("L3 failed to create counter record",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to create counter record: %w", err)
	}

	s.logger.Debug("DC101 event recorded successfully (L1, L2, L3 checks passed)",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Int16("counter_type", counterType),
		zap.String("value", value.String()),
		zap.String("increment", record.IncrementValue.String()),
		zap.Bool("is_anomaly", record.IsAnomaly),
		zap.String("transaction_id", transactionID)) // 🎯 关键修复：记录TransactionID

	// 发送更新事件
	oldValue := decimal.Zero
	if previousValue != nil {
		oldValue = *previousValue
	}

	s.notifyUpdateListeners(&models.CounterUpdateEvent{
		DeviceID:       deviceID,
		NozzleID:       nozzleID,
		CounterType:    counterType,
		OldValue:       oldValue,
		NewValue:       value,
		IncrementValue: *record.IncrementValue,
		Timestamp:      timestamp,
		IsAnomaly:      record.IsAnomaly,
		AnomalyReason:  record.AnomalyReason,
		TransactionID:  transactionID, // 🎯 关键修复：传递TransactionID
	})

	return record, nil
}

// GetLatestCounterValue 获取指定类型的最新计数器值
func (s *service) GetLatestCounterValue(ctx context.Context, deviceID string, nozzleID string, counterType int16) (decimal.Decimal, error) {
	record, err := s.repository.GetLatestByType(ctx, deviceID, nozzleID, counterType)
	if err != nil {
		return decimal.Zero, err
	}
	if record == nil {
		return decimal.Zero, nil
	}
	return record.CounterValue, nil
}

// GetCounterHistory 获取计数器历史记录
func (s *service) GetCounterHistory(ctx context.Context, deviceID string, nozzleID string, counterType int16, limit int) ([]*models.NozzleCounters, error) {
	return s.repository.GetHistoryByType(ctx, deviceID, nozzleID, counterType, limit)
}

// GetRecentUnassociatedCounterEvents 获取指定时间窗口内未关联交易的DC101事件，用于反向匹配
// 返回指定喷嘴在给定时间之后的所有未关联交易的计数器事件
func (s *service) GetRecentUnassociatedCounterEvents(ctx context.Context, deviceID string, nozzleID string, since time.Time, limit int) ([]*models.NozzleCounters, error) {
	s.logger.Debug("Getting recent unassociated counter events for reverse matching",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Time("since", since),
		zap.Int("limit", limit))

	// 调用Repository的新方法来获取未关联交易的事件
	return s.repository.GetUnassociatedEventsSince(ctx, deviceID, nozzleID, since, limit)
}

// AssociateEventWithTransaction 将已存在的DC101事件关联到特定交易
func (s *service) AssociateEventWithTransaction(ctx context.Context, eventID int64, transactionID string) error {
	s.logger.Debug("Associating DC101 event with transaction",
		zap.Int64("event_id", eventID),
		zap.String("transaction_id", transactionID))

	// 直接更新数据库中的TransactionID字段
	if err := s.repository.UpdateTransactionID(ctx, eventID, transactionID); err != nil {
		s.logger.Error("Failed to associate event with transaction",
			zap.Int64("event_id", eventID),
			zap.String("transaction_id", transactionID),
			zap.Error(err))
		return fmt.Errorf("failed to associate event with transaction: %w", err)
	}

	s.logger.Debug("DC101 event associated with transaction successfully",
		zap.Int64("event_id", eventID),
		zap.String("transaction_id", transactionID))

	return nil
}

// RegisterUpdateListener 注册更新监听器
func (s *service) RegisterUpdateListener(listener CounterUpdateListener) {
	s.listeners = append(s.listeners, listener)
	s.logger.Info("Counter update listener registered", zap.String("listener_id", listener.GetListenerID()))
}

// UnregisterUpdateListener 移除更新监听器
func (s *service) UnregisterUpdateListener(listener CounterUpdateListener) {
	for i, l := range s.listeners {
		if l.GetListenerID() == listener.GetListenerID() {
			s.listeners = append(s.listeners[:i], s.listeners[i+1:]...)
			s.logger.Info("Counter update listener unregistered", zap.String("listener_id", listener.GetListenerID()))
			break
		}
	}
}

// notifyUpdateListeners 通知所有监听器
func (s *service) notifyUpdateListeners(event *models.CounterUpdateEvent) {
	for _, listener := range s.listeners {
		if err := listener.OnCounterUpdated(event); err != nil {
			s.logger.Error("Failed to notify counter update listener",
				zap.String("listener_id", listener.GetListenerID()),
				zap.Error(err))
		}
	}
}

// isUniqueConstraintViolation 检查错误是否为PostgreSQL的唯一性约束违反
func isUniqueConstraintViolation(err error) bool {
	// "23505" 是 PostgreSQL 中 unique_violation 的 SQLSTATE code
	// 我们需要检查错误链
	if pqErr, ok := err.(*pq.Error); ok {
		return pqErr.Code == "23505"
	}

	// 备用方案：检查错误消息字符串
	// 这不如检查错误码健壮，但可以作为后备
	if strings.Contains(err.Error(), "duplicate key value violates unique constraint") ||
		strings.Contains(err.Error(), "violates unique constraint") {
		return true
	}

	return false
}
