package watchdog

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// WatchdogConfig 看门狗配置
type WatchdogConfig struct {
	Name      string        `json:"name"`    // 看门狗名称
	Timeout   time.Duration `json:"timeout"` // 超时时间
	OnTimeout func()        `json:"-"`       // 超时回调函数
	Logger    *zap.Logger   `json:"-"`       // 日志器
}

// Watchdog 看门狗实现
type Watchdog struct {
	config WatchdogConfig
	logger *zap.Logger

	// 状态管理
	isRunning    bool
	lastPing     time.Time
	startTime    time.Time
	timeoutCount int64

	// 并发控制
	mu       sync.RWMutex
	stopChan chan struct{}
	doneChan chan struct{}
	stopped  bool // 新增：防止重复关闭channel
}

// NewWatchdog 创建看门狗实例
func NewWatchdog(config WatchdogConfig) *Watchdog {
	if config.Logger == nil {
		config.Logger = zap.NewNop()
	}

	if config.Timeout <= 0 {
		config.Timeout = 30 * time.Second // 默认30秒超时
	}

	return &Watchdog{
		config:   config,
		logger:   config.Logger,
		lastPing: time.Now(),
		stop<PERSON>han: make(chan struct{}),
		doneChan: make(chan struct{}),
		stopped:  false, // 初始化为false
	}
}

// Start 启动看门狗
func (w *Watchdog) Start(ctx context.Context) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.isRunning {
		return nil
	}

	// 如果之前已经停止过，重新创建channels
	if w.stopped {
		w.stopChan = make(chan struct{})
		w.doneChan = make(chan struct{})
			// 重新创建看门狗通道
	}

	w.isRunning = true
	w.startTime = time.Now()
	w.lastPing = time.Now()
	w.timeoutCount = 0
	w.stopped = false // 重置停止状态

	go w.watchLoop(ctx)

	w.logger.Info("Watchdog started",
		zap.String("name", w.config.Name),
		zap.Duration("timeout", w.config.Timeout))

	return nil
}

// Stop 停止看门狗
func (w *Watchdog) Stop() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.isRunning || w.stopped {
		return nil
	}

	w.isRunning = false
	w.stopped = true // 标记为已停止，防止重复关闭

	// 安全关闭channel：检查channel是否已关闭
	select {
	case <-w.stopChan:
		// channel已经关闭，无需再次关闭
	default:
		// channel未关闭，安全关闭
		close(w.stopChan)
	}

	// 等待监控循环结束（使用超时防止死锁）
	select {
	case <-w.doneChan:
			// 看门狗循环完成
	case <-time.After(2 * time.Second):
		w.logger.Warn("Watchdog stop timeout, watch loop may not have completed",
			zap.String("name", w.config.Name))
	}

	w.logger.Info("Watchdog stopped",
		zap.String("name", w.config.Name),
		zap.Int64("timeout_count", w.timeoutCount),
		zap.Duration("uptime", time.Since(w.startTime)))

	return nil
}

// Ping 喂狗 - 重置超时计时器
func (w *Watchdog) Ping() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.isRunning {
		return
	}

	w.lastPing = time.Now()
}

// IsRunning 检查是否正在运行
func (w *Watchdog) IsRunning() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.isRunning
}

// GetLastPing 获取最后一次ping时间
func (w *Watchdog) GetLastPing() time.Time {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.lastPing
}

// GetTimeoutCount 获取超时次数
func (w *Watchdog) GetTimeoutCount() int64 {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.timeoutCount
}

// GetUptime 获取运行时间
func (w *Watchdog) GetUptime() time.Duration {
	w.mu.RLock()
	defer w.mu.RUnlock()
	if !w.isRunning {
		return 0
	}
	return time.Since(w.startTime)
}

// watchLoop 监控循环
func (w *Watchdog) watchLoop(ctx context.Context) {
	defer close(w.doneChan)

	ticker := time.NewTicker(w.config.Timeout / 2) // 以超时时间的一半作为检查间隔
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-w.stopChan:
			return
		case <-ticker.C:
			if w.checkTimeout() {
				w.handleTimeout()
			}
		}
	}
}

// checkTimeout 检查是否超时
func (w *Watchdog) checkTimeout() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()

	if !w.isRunning {
		return false
	}

	timeSinceLastPing := time.Since(w.lastPing)
	return timeSinceLastPing >= w.config.Timeout
}

// handleTimeout 处理超时
func (w *Watchdog) handleTimeout() {
	w.mu.Lock()
	w.timeoutCount++
	lastPing := w.lastPing
	timeoutCount := w.timeoutCount
	w.mu.Unlock()

	w.logger.Warn("Watchdog timeout detected",
		zap.String("name", w.config.Name),
		zap.Time("last_ping", lastPing),
		zap.Duration("timeout_duration", w.config.Timeout),
		zap.Int64("timeout_count", timeoutCount))

	// 调用超时回调
	if w.config.OnTimeout != nil {
		go w.config.OnTimeout()
	}
}

// WatchdogManager 看门狗管理器
type WatchdogManager struct {
	watchdogs map[string]*Watchdog
	mu        sync.RWMutex
	logger    *zap.Logger
}

// NewWatchdogManager 创建看门狗管理器
func NewWatchdogManager(logger *zap.Logger) *WatchdogManager {
	if logger == nil {
		logger = zap.NewNop()
	}

	return &WatchdogManager{
		watchdogs: make(map[string]*Watchdog),
		logger:    logger,
	}
}

// Register 注册看门狗
func (wm *WatchdogManager) Register(name string, config WatchdogConfig) error {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	if _, exists := wm.watchdogs[name]; exists {
		return nil // 已存在，不重复注册
	}

	config.Name = name
	config.Logger = wm.logger

	watchdog := NewWatchdog(config)
	wm.watchdogs[name] = watchdog

	wm.logger.Info("Watchdog registered",
		zap.String("name", name),
		zap.Duration("timeout", config.Timeout))

	return nil
}

// Unregister 注销看门狗
func (wm *WatchdogManager) Unregister(name string) error {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	watchdog, exists := wm.watchdogs[name]
	if !exists {
		return nil
	}

	// 停止看门狗
	if err := watchdog.Stop(); err != nil {
		wm.logger.Warn("Failed to stop watchdog",
			zap.String("name", name),
			zap.Error(err))
	}

	delete(wm.watchdogs, name)

	wm.logger.Info("Watchdog unregistered",
		zap.String("name", name))

	return nil
}

// Start 启动指定看门狗
func (wm *WatchdogManager) Start(ctx context.Context, name string) error {
	wm.mu.RLock()
	watchdog, exists := wm.watchdogs[name]
	wm.mu.RUnlock()

	if !exists {
		return NewWatchdogError("watchdog not found: " + name)
	}

	return watchdog.Start(ctx)
}

// Stop 停止指定看门狗
func (wm *WatchdogManager) Stop(name string) error {
	wm.mu.RLock()
	watchdog, exists := wm.watchdogs[name]
	wm.mu.RUnlock()

	if !exists {
		return NewWatchdogError("watchdog not found: " + name)
	}

	return watchdog.Stop()
}

// StartAll 启动所有看门狗
func (wm *WatchdogManager) StartAll(ctx context.Context) error {
	wm.mu.RLock()
	watchdogs := make(map[string]*Watchdog)
	for name, watchdog := range wm.watchdogs {
		watchdogs[name] = watchdog
	}
	wm.mu.RUnlock()

	for name, watchdog := range watchdogs {
		if err := watchdog.Start(ctx); err != nil {
			wm.logger.Error("Failed to start watchdog",
				zap.String("name", name),
				zap.Error(err))
		}
	}

	return nil
}

// StopAll 停止所有看门狗
func (wm *WatchdogManager) StopAll() error {
	wm.mu.RLock()
	watchdogs := make(map[string]*Watchdog)
	for name, watchdog := range wm.watchdogs {
		watchdogs[name] = watchdog
	}
	wm.mu.RUnlock()

	for name, watchdog := range watchdogs {
		if err := watchdog.Stop(); err != nil {
			wm.logger.Error("Failed to stop watchdog",
				zap.String("name", name),
				zap.Error(err))
		}
	}

	return nil
}

// Ping 向指定看门狗发送ping
func (wm *WatchdogManager) Ping(name string) error {
	wm.mu.RLock()
	watchdog, exists := wm.watchdogs[name]
	wm.mu.RUnlock()

	if !exists {
		return NewWatchdogError("watchdog not found: " + name)
	}

	watchdog.Ping()
	return nil
}

// PingAll 向所有看门狗发送ping
func (wm *WatchdogManager) PingAll() {
	wm.mu.RLock()
	watchdogs := make(map[string]*Watchdog)
	for name, watchdog := range wm.watchdogs {
		watchdogs[name] = watchdog
	}
	wm.mu.RUnlock()

	for _, watchdog := range watchdogs {
		watchdog.Ping()
	}
}

// GetWatchdog 获取指定看门狗
func (wm *WatchdogManager) GetWatchdog(name string) (*Watchdog, error) {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	watchdog, exists := wm.watchdogs[name]
	if !exists {
		return nil, NewWatchdogError("watchdog not found: " + name)
	}

	return watchdog, nil
}

// GetAllWatchdogs 获取所有看门狗
func (wm *WatchdogManager) GetAllWatchdogs() map[string]*Watchdog {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	result := make(map[string]*Watchdog)
	for name, watchdog := range wm.watchdogs {
		result[name] = watchdog
	}

	return result
}

// GetStatus 获取看门狗状态
func (wm *WatchdogManager) GetStatus() map[string]WatchdogStatus {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	result := make(map[string]WatchdogStatus)
	for name, watchdog := range wm.watchdogs {
		result[name] = WatchdogStatus{
			Name:         name,
			IsRunning:    watchdog.IsRunning(),
			LastPing:     watchdog.GetLastPing(),
			TimeoutCount: watchdog.GetTimeoutCount(),
			Uptime:       watchdog.GetUptime(),
		}
	}

	return result
}

// WatchdogStatus 看门狗状态
type WatchdogStatus struct {
	Name         string        `json:"name"`
	IsRunning    bool          `json:"is_running"`
	LastPing     time.Time     `json:"last_ping"`
	TimeoutCount int64         `json:"timeout_count"`
	Uptime       time.Duration `json:"uptime"`
}

// WatchdogError 看门狗错误
type WatchdogError struct {
	Message string
}

func (e *WatchdogError) Error() string {
	return e.Message
}

func NewWatchdogError(message string) *WatchdogError {
	return &WatchdogError{Message: message}
}
