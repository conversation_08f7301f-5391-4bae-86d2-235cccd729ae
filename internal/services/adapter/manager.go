package adapter

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// Manager 适配器管理器
type Manager struct {
	factory api.AdapterFactory
	logger  *zap.Logger

	// 活跃适配器实例
	adapters map[string]api.DeviceAdapter // key: controllerID
	mu       sync.RWMutex

	// 适配器配置缓存
	configs map[string]api.AdapterConfig // key: controllerID
}

// NewManager 创建适配器管理器
func NewManager(factory api.AdapterFactory, logger *zap.Logger) *Manager {
	return &Manager{
		factory:  factory,
		logger:   logger,
		adapters: make(map[string]api.DeviceAdapter),
		configs:  make(map[string]api.AdapterConfig),
	}
}

// GetOrCreateAdapter 获取或创建适配器（只负责连接，不执行发现）
func (m *Manager) GetOrCreateAdapter(ctx context.Context, controller *models.Controller) (api.DeviceAdapter, error) {
	m.mu.RLock()
	adapter, exists := m.adapters[controller.ID]
	m.mu.RUnlock()

	if exists && adapter.IsConnected() {
		return adapter, nil
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 双重检查
	adapter, exists = m.adapters[controller.ID]
	if exists && adapter.IsConnected() {
		return adapter, nil
	}

	// 创建适配器配置
	config := m.createAdapterConfig(controller)

	// 创建新适配器
	newAdapter, err := m.factory.CreateAdapter(controller.Protocol, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create adapter for controller %s: %w", controller.ID, err)
	}

	// 为Wayne适配器设置日志器
	if wayneAdapter, ok := newAdapter.(interface{ SetLogger(*zap.Logger) }); ok {
		wayneAdapter.SetLogger(m.logger)
	}

	// 为Wayne适配器设置控制器ID
	if wayneAdapter, ok := newAdapter.(interface{ SetControllerID(string) }); ok {
		wayneAdapter.SetControllerID(controller.ID)
	}

	m.logger.Info("正在连接适配器",
		zap.String("控制器ID", controller.ID),
		zap.String("协议类型", string(controller.Protocol)),
		zap.String("地址", controller.Address),
		zap.String("串口", config.Connection.SerialPort))

	// 连接适配器
	if err := newAdapter.Connect(ctx, config); err != nil {
		m.logger.Error("适配器连接失败",
			zap.String("控制器ID", controller.ID),
			zap.String("协议", string(controller.Protocol)),
			zap.Error(err))
		return nil, fmt.Errorf("failed to connect adapter for controller %s: %w", controller.ID, err)
	}

	// 适配器连接成功，设备管理基于数据库配置
	if controller.Protocol == models.ProtocolTypeWayneDart {
		m.logger.Info("Wayne适配器连接成功，设备基于数据库配置管理",
			zap.String("控制器ID", controller.ID),
			zap.String("串口", controller.Config.SerialPort))
	}

	// 缓存适配器和配置
	m.adapters[controller.ID] = newAdapter
	m.configs[controller.ID] = config

	m.logger.Info("Adapter created and connected",
		zap.String("controller_id", controller.ID),
		zap.String("protocol", string(controller.Protocol)),
		zap.String("address", controller.Address))

	return newAdapter, nil
}

// GetAdapter 获取已存在的适配器
func (m *Manager) GetAdapter(controllerID string) (api.DeviceAdapter, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	adapter, exists := m.adapters[controllerID]
	if !exists {
		return nil, errors.NewNotFoundError("adapter not found for controller " + controllerID)
	}

	if !adapter.IsConnected() {
		return nil, errors.NewConnectionError("adapter not connected for controller " + controllerID)
	}

	return adapter, nil
}

// RemoveAdapter 移除适配器
func (m *Manager) RemoveAdapter(ctx context.Context, controllerID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	adapter, exists := m.adapters[controllerID]
	if !exists {
		return nil // 适配器不存在，无需移除
	}

	// 断开连接
	if err := adapter.Disconnect(ctx); err != nil {
		m.logger.Warn("Failed to disconnect adapter",
			zap.String("controller_id", controllerID),
			zap.Error(err))
	}

	// 从缓存中移除
	delete(m.adapters, controllerID)
	delete(m.configs, controllerID)

	m.logger.Info("Adapter removed", zap.String("controller_id", controllerID))
	return nil
}

// DiscoverDevices 已废弃：通过适配器发现设备（改为基于数据库配置）
func (m *Manager) DiscoverDevices(ctx context.Context, controller *models.Controller) ([]*models.Device, error) {
	m.logger.Info("Device discovery is disabled, using database configuration instead",
		zap.String("controller_id", controller.ID))

	// 返回空列表，表示不执行发现
	return []*models.Device{}, nil
}

// ExecuteCommand 通过适配器执行命令
func (m *Manager) ExecuteCommand(ctx context.Context, controller *models.Controller, deviceID string, command api.Command) (*api.CommandResult, error) {
	adapter, err := m.GetOrCreateAdapter(ctx, controller)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter for command execution: %w", err)
	}

	result, err := adapter.ExecuteCommand(ctx, deviceID, command)
	if err != nil {
		return nil, fmt.Errorf("command execution failed for device %s: %w", deviceID, err)
	}

	return result, nil
}

// GetDeviceStatus 通过适配器获取设备状态
func (m *Manager) GetDeviceStatus(ctx context.Context, controller *models.Controller, deviceID string) (*api.DeviceStatusInfo, error) {
	adapter, err := m.GetOrCreateAdapter(ctx, controller)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter for status query: %w", err)
	}

	status, err := adapter.GetDeviceStatus(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("status query failed for device %s: %w", deviceID, err)
	}

	return status, nil
}

// GetSupportedProtocols 获取支持的协议类型
func (m *Manager) GetSupportedProtocols() []models.ProtocolType {
	return m.factory.GetSupportedProtocols()
}

// HealthCheck 检查所有适配器的健康状态
func (m *Manager) HealthCheck(ctx context.Context) map[string]bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := make(map[string]bool)
	for controllerID, adapter := range m.adapters {
		health[controllerID] = adapter.IsConnected()
	}

	return health
}

// ReconnectAdapter 重新连接适配器
func (m *Manager) ReconnectAdapter(ctx context.Context, controllerID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	adapter, exists := m.adapters[controllerID]
	if !exists {
		return errors.NewNotFoundError("adapter not found for controller " + controllerID)
	}

	config, exists := m.configs[controllerID]
	if !exists {
		return errors.NewInternalError("adapter config not found for controller " + controllerID)
	}

	// 先断开现有连接
	if adapter.IsConnected() {
		if err := adapter.Disconnect(ctx); err != nil {
			m.logger.Warn("Failed to disconnect adapter before reconnection",
				zap.String("controller_id", controllerID),
				zap.Error(err))
		}
	}

	// 重新连接
	if err := adapter.Connect(ctx, config); err != nil {
		return fmt.Errorf("failed to reconnect adapter for controller %s: %w", controllerID, err)
	}

	m.logger.Info("Adapter reconnected", zap.String("controller_id", controllerID))
	return nil
}

// Close 关闭适配器管理器
func (m *Manager) Close(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	for controllerID, adapter := range m.adapters {
		if err := adapter.Disconnect(ctx); err != nil {
			m.logger.Error("Failed to disconnect adapter during shutdown",
				zap.String("controller_id", controllerID),
				zap.Error(err))
		}
	}

	// 清空缓存
	m.adapters = make(map[string]api.DeviceAdapter)
	m.configs = make(map[string]api.AdapterConfig)

	m.logger.Info("Adapter manager closed")
	return nil
}

// createAdapterConfig 根据控制器创建适配器配置
func (m *Manager) createAdapterConfig(controller *models.Controller) api.AdapterConfig {
	config := api.AdapterConfig{
		Protocol:   controller.Protocol,
		Connection: api.ConnectionConfig{
			// 根据协议类型设置连接参数
		},
	}

	// 根据控制器配置设置连接参数
	if controller.Config.SerialPort != "" {
		config.Connection.SerialPort = controller.Config.SerialPort
		config.Connection.BaudRate = controller.Config.BaudRate
		config.Connection.DataBits = controller.Config.DataBits
		config.Connection.StopBits = controller.Config.StopBits
		config.Connection.Parity = controller.Config.Parity
	}

	if controller.Config.Host != "" {
		config.Connection.Host = controller.Config.Host
		config.Connection.Port = controller.Config.Port
	}

	// 设置超时参数
	if controller.Config.Timeout > 0 {
		config.Connection.Timeout = time.Duration(controller.Config.Timeout) * time.Millisecond
	}
	if controller.Config.ReadTimeout > 0 {
		config.Connection.ReadTimeout = time.Duration(controller.Config.ReadTimeout) * time.Millisecond
	}
	if controller.Config.WriteTimeout > 0 {
		config.Connection.WriteTimeout = time.Duration(controller.Config.WriteTimeout) * time.Millisecond
	}
	if controller.Config.MaxRetries > 0 {
		config.Connection.MaxRetries = controller.Config.MaxRetries
	}

	// 设置协议特定选项
	if controller.Config.ExtraConfig != nil {
		config.Options = controller.Config.ExtraConfig
	}

	return config
}
