package device_runtime_state

import (
	"testing"
	"time"

	"go.uber.org/zap"
)

func TestDeviceRuntimeStateService(t *testing.T) {
	logger := zap.NewNop()
	service := NewService(logger)

	deviceID := "test_device_001"

	// 测试设备注册
	t.Run("RegisterDevice", func(t *testing.T) {
		err := service.RegisterDevice(deviceID)
		if err != nil {
			t.Fatalf("Failed to register device: %v", err)
		}

		if !service.IsDeviceRegistered(deviceID) {
			t.Fatal("Device should be registered")
		}

		if service.GetDeviceCount() != 1 {
			t.Fatalf("Expected device count 1, got %d", service.GetDeviceCount())
		}
	})

	// 测试 TX 重置时间
	t.Run("TxResetTime", func(t *testing.T) {
		now := time.Now()

		// 设置 TX 重置时间
		err := service.SetTxResetTime(deviceID, now)
		if err != nil {
			t.Fatalf("Failed to set TX reset time: %v", err)
		}

		// 获取 TX 重置时间
		resetTime, err := service.GetTxResetTime(deviceID)
		if err != nil {
			t.Fatalf("Failed to get TX reset time: %v", err)
		}

		if !resetTime.Equal(now) {
			t.Fatalf("Expected reset time %v, got %v", now, resetTime)
		}
	})

	// 测试通用运行时状态
	t.Run("RuntimeState", func(t *testing.T) {
		key := "test_key"
		value := "test_value"

		// 设置状态
		err := service.SetRuntimeState(deviceID, key, value)
		if err != nil {
			t.Fatalf("Failed to set runtime state: %v", err)
		}

		// 获取状态
		retrievedValue, err := service.GetRuntimeState(deviceID, key)
		if err != nil {
			t.Fatalf("Failed to get runtime state: %v", err)
		}

		if retrievedValue != value {
			t.Fatalf("Expected value %v, got %v", value, retrievedValue)
		}
	})

	// 测试获取所有状态
	t.Run("GetAllRuntimeStates", func(t *testing.T) {
		states, err := service.GetAllRuntimeStates(deviceID)
		if err != nil {
			t.Fatalf("Failed to get all runtime states: %v", err)
		}

		if len(states) < 2 { // 至少应该有 TX 重置时间和测试键
			t.Fatalf("Expected at least 2 states, got %d", len(states))
		}

		if service.GetStateCount(deviceID) != len(states) {
			t.Fatalf("State count mismatch: %d vs %d", service.GetStateCount(deviceID), len(states))
		}
	})

	// 测试计数器状态
	t.Run("CounterState", func(t *testing.T) {
		counterType := "volume"
		counterValue := 123.45

		err := service.SetCounterState(deviceID, counterType, counterValue)
		if err != nil {
			t.Fatalf("Failed to set counter state: %v", err)
		}

		retrievedValue, err := service.GetCounterState(deviceID, counterType)
		if err != nil {
			t.Fatalf("Failed to get counter state: %v", err)
		}

		if retrievedValue != counterValue {
			t.Fatalf("Expected counter value %v, got %v", counterValue, retrievedValue)
		}
	})

	// 测试清除设备状态
	t.Run("ClearDeviceStates", func(t *testing.T) {
		err := service.ClearDeviceStates(deviceID)
		if err != nil {
			t.Fatalf("Failed to clear device states: %v", err)
		}

		if service.IsDeviceRegistered(deviceID) {
			t.Fatal("Device should not be registered after clearing")
		}

		if service.GetDeviceCount() != 0 {
			t.Fatalf("Expected device count 0, got %d", service.GetDeviceCount())
		}

		// 验证状态已清除
		resetTime, err := service.GetTxResetTime(deviceID)
		if err != nil {
			t.Fatalf("Failed to get TX reset time after clear: %v", err)
		}

		if !resetTime.IsZero() {
			t.Fatal("TX reset time should be zero after clearing")
		}
	})

	// 测试错误情况
	t.Run("ErrorCases", func(t *testing.T) {
		// 空设备 ID
		err := service.SetTxResetTime("", time.Now())
		if err == nil {
			t.Fatal("Expected error for empty device ID")
		}

		// 空键
		err = service.SetRuntimeState("test", "", "value")
		if err == nil {
			t.Fatal("Expected error for empty key")
		}
	})
}

// 测试监听器
type testListener struct {
	id     string
	events []*RuntimeStateEvent
}

func (l *testListener) OnStateChanged(event *RuntimeStateEvent) error {
	l.events = append(l.events, event)
	return nil
}

func (l *testListener) GetListenerID() string {
	return l.id
}

func TestRuntimeStateListener(t *testing.T) {
	logger := zap.NewNop()
	service := NewService(logger)

	deviceID := "test_device_002"
	listener := &testListener{id: "test_listener", events: make([]*RuntimeStateEvent, 0)}

	// 添加监听器
	err := service.AddListener(listener)
	if err != nil {
		t.Fatalf("Failed to add listener: %v", err)
	}

	// 注册设备并设置状态
	service.RegisterDevice(deviceID)
	service.SetTxResetTime(deviceID, time.Now())

	// 等待异步事件处理
	time.Sleep(10 * time.Millisecond)

	// 验证事件
	if len(listener.events) == 0 {
		t.Fatal("Expected at least one event")
	}

	event := listener.events[len(listener.events)-1] // 获取最后一个事件
	if event.DeviceID != deviceID {
		t.Fatalf("Expected device ID %s, got %s", deviceID, event.DeviceID)
	}

	if event.Key != string(KeyTxResetTime) {
		t.Fatalf("Expected key %s, got %s", KeyTxResetTime, event.Key)
	}

	// 移除监听器
	err = service.RemoveListener(listener.GetListenerID())
	if err != nil {
		t.Fatalf("Failed to remove listener: %v", err)
	}
}
