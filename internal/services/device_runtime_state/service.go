package device_runtime_state

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// service 设备运行时状态服务实现
// 🚀 无锁设计：使用 sync.Map 实现高并发访问
type service struct {
	// 设备状态存储：deviceID -> *deviceState
	deviceStates sync.Map
	// 事件监听器：listenerID -> RuntimeStateListener
	listeners sync.Map
	// 日志记录器
	logger *zap.Logger
}

// deviceState 单个设备的状态数据
type deviceState struct {
	// 状态数据：key -> value
	states sync.Map
	// 注册时间
	registeredAt time.Time
}

// NewService 创建设备运行时状态服务
func NewService(logger *zap.Logger) DeviceRuntimeStateServiceWithEvents {
	return &service{
		logger: logger,
		// sync.Map 无需初始化
	}
}

// SetTxResetTime 设置 TX 重置时间
func (s *service) SetTxResetTime(deviceID string, timestamp time.Time) error {
	return s.SetRuntimeState(deviceID, string(KeyTxResetTime), timestamp)
}

// GetTxResetTime 获取 TX 重置时间
func (s *service) GetTxResetTime(deviceID string) (time.Time, error) {
	value, err := s.GetRuntimeState(deviceID, string(KeyTxResetTime))
	if err != nil {
		return time.Time{}, err
	}

	if value == nil {
		return time.Time{}, nil // 未设置时返回零值
	}

	if timestamp, ok := value.(time.Time); ok {
		return timestamp, nil
	}

	return time.Time{}, fmt.Errorf("invalid timestamp type for device %s", deviceID)
}

// SetCounterState 设置计数器状态
func (s *service) SetCounterState(deviceID string, counterType string, value interface{}) error {
	key := fmt.Sprintf("counter_%s", counterType)
	return s.SetRuntimeState(deviceID, key, value)
}

// GetCounterState 获取计数器状态
func (s *service) GetCounterState(deviceID string, counterType string) (interface{}, error) {
	key := fmt.Sprintf("counter_%s", counterType)
	return s.GetRuntimeState(deviceID, key)
}

// SetRuntimeState 设置运行时状态
func (s *service) SetRuntimeState(deviceID string, key string, value interface{}) error {
	if deviceID == "" {
		return fmt.Errorf("device ID cannot be empty")
	}
	if key == "" {
		return fmt.Errorf("state key cannot be empty")
	}

	// 🚀 无锁设计：获取或创建设备状态
	deviceStateValue, _ := s.deviceStates.LoadOrStore(deviceID, &deviceState{
		registeredAt: time.Now(),
	})
	devState := deviceStateValue.(*deviceState)

	// 获取旧值用于事件通知
	oldValue, _ := devState.states.Load(key)

	// 设置新值
	devState.states.Store(key, value)

	s.logger.Debug("设置设备运行时状态",
		zap.String("device_id", deviceID),
		zap.String("key", key),
		zap.Any("old_value", oldValue),
		zap.Any("new_value", value))

	// 🚀 无锁异步通知状态变更
	go s.NotifyStateChange(deviceID, key, oldValue, value)

	return nil
}

// GetRuntimeState 获取运行时状态
func (s *service) GetRuntimeState(deviceID string, key string) (interface{}, error) {
	if deviceID == "" {
		return nil, fmt.Errorf("device ID cannot be empty")
	}
	if key == "" {
		return nil, fmt.Errorf("state key cannot be empty")
	}

	// 🚀 无锁设计：直接从 sync.Map 获取
	if deviceStateValue, exists := s.deviceStates.Load(deviceID); exists {
		devState := deviceStateValue.(*deviceState)
		if value, exists := devState.states.Load(key); exists {
			return value, nil
		}
	}

	return nil, nil // 未找到时返回 nil
}

// GetAllRuntimeStates 获取设备的所有运行时状态
func (s *service) GetAllRuntimeStates(deviceID string) (map[string]interface{}, error) {
	if deviceID == "" {
		return nil, fmt.Errorf("device ID cannot be empty")
	}

	// 🚀 无锁设计：遍历 sync.Map
	if deviceStateValue, exists := s.deviceStates.Load(deviceID); exists {
		devState := deviceStateValue.(*deviceState)
		result := make(map[string]interface{})

		devState.states.Range(func(key, value interface{}) bool {
			result[key.(string)] = value
			return true
		})

		return result, nil
	}

	return make(map[string]interface{}), nil
}

// ClearDeviceStates 清除设备的所有运行时状态
func (s *service) ClearDeviceStates(deviceID string) error {
	if deviceID == "" {
		return fmt.Errorf("device ID cannot be empty")
	}

	// 🚀 无锁设计：直接删除
	s.deviceStates.Delete(deviceID)

	s.logger.Info("清除设备运行时状态",
		zap.String("device_id", deviceID))

	return nil
}

// RegisterDevice 注册设备
func (s *service) RegisterDevice(deviceID string) error {
	if deviceID == "" {
		return fmt.Errorf("device ID cannot be empty")
	}

	// 🚀 无锁设计：LoadOrStore 保证原子性
	_, loaded := s.deviceStates.LoadOrStore(deviceID, &deviceState{
		registeredAt: time.Now(),
	})

	if loaded {
		s.logger.Debug("设备已注册，跳过重复注册",
			zap.String("device_id", deviceID))
	} else {
		s.logger.Info("注册设备到运行时状态服务",
			zap.String("device_id", deviceID))
	}

	return nil
}

// UnregisterDevice 注销设备
func (s *service) UnregisterDevice(deviceID string) error {
	return s.ClearDeviceStates(deviceID)
}

// IsDeviceRegistered 检查设备是否已注册
func (s *service) IsDeviceRegistered(deviceID string) bool {
	// 🚀 无锁设计：直接检查是否存在
	_, exists := s.deviceStates.Load(deviceID)
	return exists
}

// GetDeviceCount 获取已注册设备数量
func (s *service) GetDeviceCount() int {
	// 🚀 无锁设计：遍历计数
	count := 0
	s.deviceStates.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// GetStateCount 获取设备的状态数量
func (s *service) GetStateCount(deviceID string) int {
	// 🚀 无锁设计：遍历设备状态计数
	if deviceStateValue, exists := s.deviceStates.Load(deviceID); exists {
		devState := deviceStateValue.(*deviceState)
		count := 0
		devState.states.Range(func(key, value interface{}) bool {
			count++
			return true
		})
		return count
	}
	return 0
}

// AddListener 添加状态变更监听器
func (s *service) AddListener(listener RuntimeStateListener) error {
	if listener == nil {
		return fmt.Errorf("listener cannot be nil")
	}

	// 🚀 无锁设计：直接存储到 sync.Map
	listenerID := listener.GetListenerID()
	s.listeners.Store(listenerID, listener)

	s.logger.Info("添加运行时状态监听器",
		zap.String("listener_id", listenerID))

	return nil
}

// RemoveListener 移除状态变更监听器
func (s *service) RemoveListener(listenerID string) error {
	// 🚀 无锁设计：直接从 sync.Map 删除
	s.listeners.Delete(listenerID)

	s.logger.Info("移除运行时状态监听器",
		zap.String("listener_id", listenerID))

	return nil
}

// NotifyStateChange 通知状态变更
func (s *service) NotifyStateChange(deviceID string, key string, oldValue, newValue interface{}) error {
	// 🚀 无锁设计：遍历 sync.Map 中的监听器
	event := &RuntimeStateEvent{
		DeviceID:  deviceID,
		Key:       key,
		OldValue:  oldValue,
		NewValue:  newValue,
		Timestamp: time.Now(),
	}

	s.listeners.Range(func(key, value interface{}) bool {
		listener := value.(RuntimeStateListener)
		if err := listener.OnStateChanged(event); err != nil {
			s.logger.Error("状态变更监听器处理失败",
				zap.String("listener_id", listener.GetListenerID()),
				zap.String("device_id", deviceID),
				zap.String("key", key.(string)),
				zap.Error(err))
		}
		return true
	})

	return nil
}
