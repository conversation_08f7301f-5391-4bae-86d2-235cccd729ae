package device_runtime_state

import (
	"time"
)

// DeviceRuntimeStateService 设备运行时状态服务接口
// 用于管理设备的运行时状态信息，如 TX 重置时间、计数器状态等
// 解决 DevicePoller 和 TransactionLifecycle 之间的循环依赖问题
type DeviceRuntimeStateService interface {
	// TX 重置时间管理
	SetTxResetTime(deviceID string, timestamp time.Time) error
	GetTxResetTime(deviceID string) (time.Time, error)

	// 计数器状态管理
	SetCounterState(deviceID string, counterType string, value interface{}) error
	GetCounterState(deviceID string, counterType string) (interface{}, error)

	// 通用运行时状态管理
	SetRuntimeState(deviceID string, key string, value interface{}) error
	GetRuntimeState(deviceID string, key string) (interface{}, error)

	// 批量操作
	GetAllRuntimeStates(deviceID string) (map[string]interface{}, error)
	ClearDeviceStates(deviceID string) error

	// 设备生命周期管理
	RegisterDevice(deviceID string) error
	UnregisterDevice(deviceID string) error
	IsDeviceRegistered(deviceID string) bool

	// 统计和监控
	GetDeviceCount() int
	GetStateCount(deviceID string) int
}

// RuntimeStateKey 运行时状态键常量
type RuntimeStateKey string

const (
	// TX 重置时间键
	KeyTxResetTime RuntimeStateKey = "tx_reset_time"

	// 计数器相关键
	KeyCounterVolume RuntimeStateKey = "counter_volume"
	KeyCounterAmount RuntimeStateKey = "counter_amount"

	// 配置相关键
	KeyNozzleConfig RuntimeStateKey = "nozzle_config"
	KeyPriceConfig  RuntimeStateKey = "price_config"

	// 状态相关键
	KeyLastActivity   RuntimeStateKey = "last_activity"
	KeyPollingStatus  RuntimeStateKey = "polling_status"
	KeyConnectionInfo RuntimeStateKey = "connection_info"
)

// DeviceRuntimeState 设备运行时状态结构
type DeviceRuntimeState struct {
	DeviceID     string                 `json:"device_id"`
	States       map[string]interface{} `json:"states"`
	LastUpdated  time.Time              `json:"last_updated"`
	RegisteredAt time.Time              `json:"registered_at"`
}

// RuntimeStateEvent 运行时状态变更事件
type RuntimeStateEvent struct {
	DeviceID  string      `json:"device_id"`
	Key       string      `json:"key"`
	OldValue  interface{} `json:"old_value"`
	NewValue  interface{} `json:"new_value"`
	Timestamp time.Time   `json:"timestamp"`
}

// RuntimeStateListener 运行时状态监听器接口
type RuntimeStateListener interface {
	OnStateChanged(event *RuntimeStateEvent) error
	GetListenerID() string
}

// DeviceRuntimeStateServiceWithEvents 带事件功能的设备运行时状态服务接口
type DeviceRuntimeStateServiceWithEvents interface {
	DeviceRuntimeStateService

	// 事件监听
	AddListener(listener RuntimeStateListener) error
	RemoveListener(listenerID string) error
	NotifyStateChange(deviceID string, key string, oldValue, newValue interface{}) error
}
