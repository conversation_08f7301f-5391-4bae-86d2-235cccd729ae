# 授权时泵码完整性检查功能

## 🎯 功能概述

在授权油抢时，系统会检查对应喷嘴是否已有有效的体积和金额泵码数据。如果任一泵码缺失，则拒绝授权并自动发送CD101请求获取缺失的泵码数据。

## 🚀 核心特性

### 1. 高性能SQL查询
- 使用带索引的SQL查询实现泵码完整性检查
- 适合高频调用的授权场景
- 一次查询检查两个计数器类型的完整性

### 2. 智能拒绝机制
- 体积泵码缺失时拒绝授权
- 金额泵码缺失时拒绝授权  
- 返回明确的错误信息说明缺失的泵码类型

### 3. 自动CD101请求
- 检测到泵码缺失时，自动发送CD101请求
- 异步发送，不阻塞响应
- 同时请求体积和金额两种计数器

## 📊 实现细节

### CheckPumpReadingIntegrity 方法

**位置**: `internal/services/nozzle/service_v2.go`

**功能**: 
- 根据喷嘴编号计算对应的计数器类型
- 使用高性能SQL查询检查泵码是否存在
- 返回详细的检查结果

**SQL查询优化**:
```sql
SELECT 
    counter_type,
    counter_value,
    recorded_at
FROM nozzle_counters 
WHERE device_id = ? 
    AND nozzle_id = ? 
    AND counter_type IN (?, ?) 
    AND is_valid = true
    AND counter_value > 0
ORDER BY counter_type, recorded_at DESC
```

**索引利用**:
- `device_id` 索引
- `nozzle_id` 索引  
- `counter_type` 索引
- `recorded_at` 索引

### 授权流程集成

**位置**: `internal/server/handlers/v2/wayne_command_handler.go`

**集成点**: `ExecuteAuthorize` 方法

**流程**:
1. 检查设备和喷嘴状态
2. **🆕 检查泵码完整性**
3. 如果检查通过，继续授权
4. 如果检查失败：
   - 拒绝授权
   - 异步发送CD101请求
   - 返回HTTP 412状态码

## 🔧 技术实现

### 泵码类型映射
- **体积计数器**: 喷嘴编号 (0x01-0x08)
- **金额计数器**: 喷嘴编号 + 16 (0x11-0x18)

### 异步CD101请求
```go
go func() {
    // 获取设备轮询器
    devicePoller, err := h.dispatchTask.GetDevice(req.DeviceID)
    if err == nil {
        // 发送体积计数器请求
        devicePoller.RequestCounters(v2.CounterTypeEnum(volumeCounterType))
        // 发送金额计数器请求  
        devicePoller.RequestCounters(v2.CounterTypeEnum(amountCounterType))
    }
}()
```

## 📈 性能优势

### 1. 索引优化查询
- 复合索引快速定位记录
- 避免全表扫描
- 支持高并发授权请求

### 2. 一次查询双重检查
- 单次SQL查询检查两种计数器
- 减少数据库往返次数
- 降低查询延迟

### 3. 异步CD101请求
- 不阻塞授权响应
- 后台自动补充缺失数据
- 提升用户体验

## 🛡️ 错误处理

### HTTP状态码
- **200**: 授权成功（泵码完整）
- **412**: 授权失败（泵码不完整）
- **409**: 授权失败（设备/喷嘴状态冲突）

### 错误信息
```json
{
  "error": "Authorize rejected: incomplete pump readings. CD101 request sent to retrieve missing data. Please retry after a few seconds.",
  "missing_counters": ["volume(0x01)", "amount(0x11)"]
}
```

## 🔄 完整授权流程

```mermaid
graph TD
    A[收到授权请求] --> B[检查设备状态]
    B --> C[检查喷嘴状态]
    C --> D[🆕 检查泵码完整性]
    D --> E{泵码完整?}
    E -->|是| F[继续授权流程]
    E -->|否| G[拒绝授权]
    G --> H[异步发送CD101]
    H --> I[返回412错误]
    F --> J[发送CD1授权命令]
    J --> K[返回授权成功]
```

## 📝 使用示例

### 成功授权
```bash
curl -X POST /api/v2/wayne/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "nozzle_id": "nozzle_001_01", 
    "employee_id": "001"
  }'

# 响应: 200 OK
{
  "success": true,
  "message": "Device authorized successfully"
}
```

### 泵码不完整
```bash
curl -X POST /api/v2/wayne/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "nozzle_id": "nozzle_001_01",
    "employee_id": "001"
  }'

# 响应: 412 Precondition Failed  
{
  "error": "Authorize rejected: incomplete pump readings. CD101 request sent to retrieve missing data. Please retry after a few seconds."
}
```

## 🎯 解决的问题

1. **数据准确性**: 确保授权前已有准确的起始泵码
2. **交易完整性**: 避免由于缺失起始泵码导致的交易数据错误
3. **系统稳定性**: 防止数值溢出等异常情况
4. **用户体验**: 自动补充缺失数据，减少人工干预

## 🔮 后续改进

1. **缓存优化**: 增加泵码数据缓存，减少数据库查询
2. **重试机制**: 智能重试逻辑，自动处理临时性故障
3. **监控告警**: 添加泵码缺失监控和告警机制
4. **统计分析**: 收集泵码完整性统计数据，优化系统配置 