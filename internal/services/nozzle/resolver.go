package nozzle

import (
	"context"
	"fmt"

	"fcc-service/pkg/models"

	"go.uber.org/zap"
)

// NozzleResolver Nozzle 解析器 - 统一处理 Nozzle 查找逻辑
type NozzleResolver interface {
	// ResolveNozzle 根据设备ID和喷嘴编号解析到完整的 Nozzle 对象
	ResolveNozzle(ctx context.Context, deviceID string, nozzleNumber byte) (*models.Nozzle, error)

	// GetNozzleByID 根据完整的 nozzle ID 直接获取 Nozzle 对象
	GetNozzleByID(ctx context.Context, nozzleID string) (*models.Nozzle, error)

	// ResolveNozzles 批量解析 Nozzles
	ResolveNozzles(ctx context.Context, deviceID string, nozzleNumbers []byte) ([]*models.Nozzle, error)

	// ValidateNozzleExists 验证 Nozzle 是否存在
	ValidateNozzleExists(ctx context.Context, deviceID string, nozzleNumber byte) error
}

// nozzleResolver Nozzle 解析器实现
type nozzleResolver struct {
	nozzleService ServiceV2
	logger        *zap.Logger
}

// NewNozzleResolver 创建 Nozzle 解析器
func NewNozzleResolver(nozzleService ServiceV2, logger *zap.Logger) NozzleResolver {
	return &nozzleResolver{
		nozzleService: nozzleService,
		logger:        logger,
	}
}

// ResolveNozzle 根据设备ID和喷嘴编号解析到完整的 Nozzle 对象
func (r *nozzleResolver) ResolveNozzle(ctx context.Context, deviceID string, nozzleNumber byte) (*models.Nozzle, error) {
	r.logger.Debug("Resolving nozzle",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", nozzleNumber))

	// 通过 nozzle service 查找
	nozzle, err := r.nozzleService.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		r.logger.Error("Failed to resolve nozzle",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.Error(err))
		return nil, fmt.Errorf("failed to resolve nozzle %d for device %s: %w", nozzleNumber, deviceID, err)
	}

	if nozzle == nil {
		return nil, fmt.Errorf("nozzle %d not found for device %s", nozzleNumber, deviceID)
	}

	r.logger.Debug("Nozzle resolved successfully",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("nozzle_id", nozzle.ID),
		zap.String("nozzle_name", nozzle.Name))

	return nozzle, nil
}

// GetNozzleByID 根据完整的 nozzle ID 直接获取 Nozzle 对象
func (r *nozzleResolver) GetNozzleByID(ctx context.Context, nozzleID string) (*models.Nozzle, error) {
	r.logger.Debug("Getting nozzle by ID",
		zap.String("nozzle_id", nozzleID))

	// 直接通过 nozzle service 查找
	nozzle, err := r.nozzleService.GetNozzleByID(ctx, nozzleID)
	if err != nil {
		r.logger.Error("Failed to get nozzle by ID",
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get nozzle by ID %s: %w", nozzleID, err)
	}

	if nozzle == nil {
		return nil, fmt.Errorf("nozzle not found: %s", nozzleID)
	}

	r.logger.Debug("Nozzle found successfully by ID",
		zap.String("nozzle_id", nozzleID),
		zap.String("device_id", nozzle.DeviceID),
		zap.Uint8("nozzle_number", nozzle.Number),
		zap.String("nozzle_name", nozzle.Name))

	return nozzle, nil
}

// ResolveNozzles 批量解析 Nozzles
func (r *nozzleResolver) ResolveNozzles(ctx context.Context, deviceID string, nozzleNumbers []byte) ([]*models.Nozzle, error) {
	if len(nozzleNumbers) == 0 {
		return []*models.Nozzle{}, nil
	}

	r.logger.Debug("Resolving multiple nozzles",
		zap.String("device_id", deviceID),
		zap.Int("count", len(nozzleNumbers)))

	var nozzles []*models.Nozzle
	var errors []error

	for _, nozzleNumber := range nozzleNumbers {
		nozzle, err := r.ResolveNozzle(ctx, deviceID, nozzleNumber)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		nozzles = append(nozzles, nozzle)
	}

	if len(errors) > 0 {
		return nozzles, fmt.Errorf("failed to resolve some nozzles: %d errors occurred", len(errors))
	}

	r.logger.Debug("All nozzles resolved successfully",
		zap.String("device_id", deviceID),
		zap.Int("resolved_count", len(nozzles)))

	return nozzles, nil
}

// ValidateNozzleExists 验证 Nozzle 是否存在
func (r *nozzleResolver) ValidateNozzleExists(ctx context.Context, deviceID string, nozzleNumber byte) error {
	_, err := r.ResolveNozzle(ctx, deviceID, nozzleNumber)
	return err
}
