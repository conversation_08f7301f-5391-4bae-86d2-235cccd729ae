package nozzle

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fcc-service/internal/adapters/wayne/pump"
	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// NozzlePriceInfo 喷嘴价格信息
type NozzlePriceInfo struct {
	NozzleNumber byte            `json:"nozzle_number" validate:"required,min=1,max=8"` // 喷嘴编号
	Price        decimal.Decimal `json:"price" validate:"required,gte=0"`               // 价格
	Decimals     int             `json:"decimals" validate:"min=0,max=3"`               // 小数位数，默认3
}

// NozzleTargetPriceUpdate 喷嘴目标价格更新信息
type NozzleTargetPriceUpdate struct {
	NozzleNumber byte            `json:"nozzle_number"`
	TargetPrice  decimal.Decimal `json:"target_price"`
}

// ServiceV2 Nozzle服务接口V2 - 专为Wayne DART协议设计
type ServiceV2 interface {
	// 基础CRUD操作
	CreateNozzle(ctx context.Context, nozzle *models.Nozzle) error
	GetNozzle(ctx context.Context, deviceID string, number byte) (*models.Nozzle, error)
	GetNozzleByID(ctx context.Context, nozzleID string) (*models.Nozzle, error)
	GetDeviceNozzles(ctx context.Context, deviceID string) ([]*models.Nozzle, error)
	UpdateNozzle(ctx context.Context, nozzle *models.Nozzle) error
	DeleteNozzle(ctx context.Context, deviceID string, number byte) error

	// Wayne DART协议专用方法（保留用于自动配置场景）
	GetNozzlePricesForCD5(ctx context.Context, deviceID string) ([][]byte, error)
	// 新增：构建完整价格数组，用于单个喷嘴价格更新时填充其他喷嘴的当前价格
	BuildCompleteNozzlePrices(ctx context.Context, deviceID string, targetNozzle byte, targetPrice decimal.Decimal) ([]NozzlePriceInfo, error)
	UpdateFromDC2Transaction(ctx context.Context, deviceID string, nozzleNum byte, volume, amount decimal.Decimal) error
	UpdateFromDC3Transaction(ctx context.Context, deviceID string, nozzleNum byte, nozio byte, price decimal.Decimal) error

	// 新增：异步价格同步管理
	SetNozzleTargetPrice(ctx context.Context, deviceID string, nozzleNumber byte, targetPrice decimal.Decimal) error
	GetNozzlesNeedingPriceSync(ctx context.Context, deviceID string) ([]*models.Nozzle, error)
	GetNozzlesPriceSyncing(ctx context.Context, deviceID string) ([]*models.Nozzle, error)
	MarkNozzlePriceSending(ctx context.Context, deviceID string, nozzleNumber byte, sentPrice decimal.Decimal) error
	ConfirmNozzlePriceSync(ctx context.Context, deviceID string, nozzleNumber byte) error
	MarkNozzlePriceFailed(ctx context.Context, deviceID string, nozzleNumber byte, reason string) error
	CheckPriceSyncTimeout(ctx context.Context, deviceID string, timeout time.Duration) error
	GetPriceSyncStatus(ctx context.Context, deviceID string) (map[string]interface{}, error)

	// 新增：原子性批量价格更新操作
	BatchSetNozzleTargetPrices(ctx context.Context, deviceID string, priceUpdates []NozzleTargetPriceUpdate) error
	BatchConfirmPriceSync(ctx context.Context, deviceID string, nozzleNumbers []byte) error

	// 批量操作
	BatchUpdateNozzleStates(ctx context.Context, deviceID string, updates []models.NozzleStateUpdate) error
	SyncDeviceNozzles(ctx context.Context, deviceID string) error

	// 设备初始化支持
	InitializeDeviceNozzles(ctx context.Context, deviceID string, configs []models.NozzleConfig) error

	// 业务操作
	StartTransaction(ctx context.Context, deviceID string, nozzleNum byte, presetVolume, presetAmount *decimal.Decimal) error
	CompleteTransaction(ctx context.Context, deviceID string, nozzleNum byte) error
	ResetTransaction(ctx context.Context, deviceID string, nozzleNum byte) error

	// 设备状态检查
	HasFillingNozzles(ctx context.Context, deviceID string) (bool, error)
	CheckFillingStatusAndLockNozzle(ctx context.Context, deviceID string, nozzleID string) error

	// 🚀 新增：简化的直接数据库更新方法

	// UpdateNozzleStatusDirect 直接更新喷嘴状态到数据库
	UpdateNozzleStatusDirect(ctx context.Context, deviceID string, nozzleID string, status models.NozzleStatus) error

	// UpdateNozzleDataDirect 直接更新喷嘴体积金额数据到数据库
	UpdateNozzleDataDirect(ctx context.Context, deviceID string, nozzleID string, volume, amount decimal.Decimal) error

	// UpdateNozzleDC3Direct 直接更新DC3相关字段到数据库
	UpdateNozzleDC3Direct(ctx context.Context, deviceID string, nozzleID string, price decimal.Decimal, isOut, isSelected bool) error

	// ResetDeviceNozzlesDirect 直接重置设备所有喷嘴到数据库
	ResetDeviceNozzlesDirect(ctx context.Context, deviceID string) error

	// UpdateAllNozzlesStatusDirect 直接更新设备所有喷嘴状态到数据库
	UpdateAllNozzlesStatusDirect(ctx context.Context, deviceID string, status models.NozzleStatus) error

	// UpdateActiveNozzlesStatusDirect 直接更新设备所有启用的喷嘴状态到数据库
	UpdateActiveNozzlesStatusDirect(ctx context.Context, deviceID string, status models.NozzleStatus) error

	// UpdateMultipleNozzlesStatusByNumberDirect 批量更新多个指定喷嘴编号的状态到数据库
	UpdateMultipleNozzlesStatusByNumberDirect(ctx context.Context, deviceID string, nozzleNumbers []byte, status models.NozzleStatus) error

	// UpdateNozzleFillingWithOthersIdle 将指定喷嘴设为filling状态，同时将其他喷嘴设为idle状态
	// UpdateNozzleFillingWithOthersIdle(ctx context.Context, deviceID string, workingnozzleID string) error

	// QueryCurrentNozzleStatus 查询当前喷嘴状态
	QueryCurrentNozzleStatus(ctx context.Context, deviceID string, nozzleID string) (models.NozzleStatus, error)

	// QueryCurrentNozzleData 查询当前喷嘴体积金额数据
	QueryCurrentNozzleData(ctx context.Context, deviceID string, nozzleID string) (decimal.Decimal, decimal.Decimal, error)

	// QueryCurrentNozzleIsOut 查询当前喷嘴是否拔出状态
	QueryCurrentNozzleIsOut(ctx context.Context, nozzleID string) (bool, error)

	// 🆕 检查泵码完整性 - 确保有体积和金额两个泵码才允许授权
	CheckPumpReadingIntegrity(ctx context.Context, deviceID string, nozzleID string) error

	// 🆕 预授权相关方法
	UpdateNozzlePreAuth(ctx context.Context, nozzleID string, authType string, preAuthNumber decimal.Decimal, expiresAt time.Time) error
	ClearNozzlePreAuth(ctx context.Context, nozzleID string) error

	// 🆕 检查设备上是否有有效的预授权
	CheckActivePreAuthOnDevice(ctx context.Context, deviceID string) (bool, error)
}

// serviceV2 NozzleService的V2实现
type serviceV2 struct {
	db           *gorm.DB
	logger       *zap.Logger
	bcdConverter pump.BCDConverter

	// 🚀 新增：状态防重复机制
	lastStatusCache  map[string]models.NozzleStatus // key: "deviceID-nozzleID"
	lastStatusTime   map[string]time.Time           // key: "deviceID-nozzleID"
	statusCacheMutex sync.RWMutex
}

// NewServiceV2 创建新的NozzleService V2实例
func NewServiceV2(db *gorm.DB, logger *zap.Logger) ServiceV2 {
	return &serviceV2{
		db:           db,
		logger:       logger,
		bcdConverter: pump.NewBCDConverter(),

		// 🚀 初始化状态防重复机制
		lastStatusCache:  make(map[string]models.NozzleStatus),
		lastStatusTime:   make(map[string]time.Time),
		statusCacheMutex: sync.RWMutex{},
	}
}

// CreateNozzle 创建新喷嘴
func (s *serviceV2) CreateNozzle(ctx context.Context, nozzle *models.Nozzle) error {
	// 验证喷嘴配置
	if err := nozzle.ValidateWayneProtocol(); err != nil {
		return fmt.Errorf("nozzle validation failed: %w", err)
	}

	// 修复：直接存储API传入的金额数据，不乘以1000，保持与API数据一致
	// nozzle.CurrentPrice 和 nozzle.CurrentAmount 直接使用传入值

	// 检查设备是否存在
	var device models.Device
	if err := s.db.WithContext(ctx).First(&device, "id = ?", nozzle.DeviceID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("device not found: %s", nozzle.DeviceID)
		}
		return fmt.Errorf("failed to check device: %w", err)
	}

	// 检查喷嘴编号是否有效
	if !device.IsNozzleNumberValid(nozzle.Number) {
		return fmt.Errorf("invalid nozzle number %d for device %s", nozzle.Number, nozzle.DeviceID)
	}

	// 检查喷嘴编号是否已存在
	var existingCount int64
	if err := s.db.WithContext(ctx).Model(&models.Nozzle{}).
		Where("device_id = ? AND number = ?", nozzle.DeviceID, nozzle.Number).
		Count(&existingCount).Error; err != nil {
		return fmt.Errorf("failed to check existing nozzle: %w", err)
	}

	if existingCount > 0 {
		return fmt.Errorf("nozzle with number %d already exists for device %s", nozzle.Number, nozzle.DeviceID)
	}

	// 生成ID
	if nozzle.ID == "" {
		nozzle.ID = fmt.Sprintf("%s-nozzle-%d", nozzle.DeviceID, nozzle.Number)
	}

	// 创建喷嘴
	if err := s.db.WithContext(ctx).Create(nozzle).Error; err != nil {
		return fmt.Errorf("failed to create nozzle: %w", err)
	}

	s.logger.Info("Nozzle created successfully",
		zap.String("nozzle_id", nozzle.ID),
		zap.String("device_id", nozzle.DeviceID),
		zap.Uint8("number", nozzle.Number))

	return nil
}

// GetNozzle 根据设备ID和喷嘴编号获取喷嘴
func (s *serviceV2) GetNozzle(ctx context.Context, deviceID string, number byte) (*models.Nozzle, error) {
	var nozzle models.Nozzle

	if err := s.db.WithContext(ctx).
		Preload("FuelGrade").
		Where("device_id = ? AND number = ?", deviceID, number).
		First(&nozzle).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("nozzle not found: device=%s, number=%d", deviceID, number)
		}
		return nil, fmt.Errorf("failed to get nozzle: %w", err)
	}

	// 修复：API返回数据库原始金额，不除以1000，保持与数据库一致
	// nozzle.CurrentPrice 和 nozzle.CurrentAmount 直接返回数据库值

	return &nozzle, nil
}

// GetNozzleByID 根据ID获取喷嘴
func (s *serviceV2) GetNozzleByID(ctx context.Context, nozzleID string) (*models.Nozzle, error) {
	s.logger.Info("GetNozzleByID called", zap.String("nozzle_id", nozzleID))

	var nozzle models.Nozzle

	s.logger.Debug("Executing database query for nozzle",
		zap.String("nozzle_id", nozzleID),
		zap.String("query", "SELECT * FROM nozzles WHERE id = ?"))

	if err := s.db.WithContext(ctx).
		Preload("FuelGrade").
		First(&nozzle, "id = ?", nozzleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Error("Nozzle not found in database",
				zap.String("nozzle_id", nozzleID),
				zap.Error(err))
			return nil, fmt.Errorf("nozzle not found: %s", nozzleID)
		}
		s.logger.Error("Database error while getting nozzle",
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get nozzle: %w", err)
	}

	s.logger.Info("Nozzle found successfully",
		zap.String("nozzle_id", nozzleID),
		zap.String("device_id", nozzle.DeviceID),
		zap.Uint8("nozzle_number", nozzle.Number),
		zap.Bool("is_enabled", nozzle.IsEnabled),
		zap.String("status", string(nozzle.Status)),
		zap.String("current_price", nozzle.CurrentPrice.String()))

	// 修复：API返回数据库原始金额，不除以1000，保持与数据库一致
	// nozzle.CurrentPrice 和 nozzle.CurrentAmount 直接返回数据库值

	return &nozzle, nil
}

// GetDeviceNozzles 获取设备的所有喷嘴
func (s *serviceV2) GetDeviceNozzles(ctx context.Context, deviceID string) ([]*models.Nozzle, error) {
	var nozzles []*models.Nozzle

	if err := s.db.WithContext(ctx).
		Preload("FuelGrade").
		Where("device_id = ?", deviceID).
		Order("number ASC").
		Find(&nozzles).Error; err != nil {
		return nil, fmt.Errorf("failed to get device nozzles: %w", err)
	}

	// 修复：API返回数据库原始金额，不除以1000，保持与数据库一致
	// 直接返回数据库中的nozzles数据，不做除法处理

	return nozzles, nil
}

// UpdateNozzle 更新喷嘴
func (s *serviceV2) UpdateNozzle(ctx context.Context, nozzle *models.Nozzle) error {
	// 更新喷嘴信息

	// 验证喷嘴配置
	if err := nozzle.ValidateWayneProtocol(); err != nil {
		return fmt.Errorf("nozzle validation failed: %w", err)
	}

	// 执行优化的数据库更新

	// 性能优化：只更新必要字段，避免全字段UPDATE
	updateFields := map[string]interface{}{
		"status":         nozzle.Status,
		"is_out":         nozzle.IsOut,
		"is_selected":    nozzle.IsSelected,
		"current_volume": nozzle.CurrentVolume,
		"updated_at":     time.Now(),

		// 修复：直接存储API传入的金额数据，不乘以1000，保持与API数据一致
		"current_price":  nozzle.CurrentPrice,
		"current_amount": nozzle.CurrentAmount,
	}

	// 只在有预设值时更新
	if nozzle.PresetVolume != nil {
		updateFields["preset_volume"] = nozzle.PresetVolume
	}
	if nozzle.PresetAmount != nil {
		// 修复：直接存储API传入的金额数据，不乘以1000
		updateFields["preset_amount"] = nozzle.PresetAmount
	}

	// 执行优化的UPDATE
	if err := s.db.WithContext(ctx).Model(nozzle).Updates(updateFields).Error; err != nil {
		s.logger.Error("Failed to update nozzle",
			zap.String("nozzle_id", nozzle.ID),
			zap.Error(err))
		return fmt.Errorf("failed to update nozzle: %w", err)
	}

	s.logger.Info("Nozzle updated successfully",
		zap.String("nozzle_id", nozzle.ID))

	return nil
}

// DeleteNozzle 删除喷嘴
func (s *serviceV2) DeleteNozzle(ctx context.Context, deviceID string, number byte) error {
	// 删除喷嘴

	result := s.db.WithContext(ctx).
		Where("device_id = ? AND number = ?", deviceID, number).
		Delete(&models.Nozzle{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete nozzle: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("nozzle not found: device=%s, number=%d", deviceID, number)
	}

	s.logger.Info("Nozzle deleted successfully",
		zap.String("device_id", deviceID),
		zap.Uint8("number", number))

	return nil
}

// GetNozzlePricesForCD5 获取设备所有喷嘴的价格数据，用于CD5命令
// 注意：此方法主要用于自动价格配置场景，从数据库获取所有启用喷嘴的当前价格
// 对于接口调价场景（单个或指定喷嘴调价），应直接构建价格参数，不使用此方法
func (s *serviceV2) GetNozzlePricesForCD5(ctx context.Context, deviceID string) ([][]byte, error) {
	// 获取CD5自动配置场景的喷嘴价格

	// 获取设备所有启用的喷嘴
	nozzles, err := s.GetDeviceNozzles(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device nozzles: %w", err)
	}

	// 检查是否有启用的喷嘴
	if len(nozzles) == 0 {
		return nil, fmt.Errorf("no nozzles found for device %s", deviceID)
	}

	// 只为实际存在且启用的喷嘴生成价格数据
	var pricesBCD [][]byte
	for _, nozzle := range nozzles {
		if nozzle.IsEnabled {
			// 价格验证：检查价格是否有效
			if nozzle.CurrentPrice.IsZero() || nozzle.CurrentPrice.IsNegative() {
				return nil, fmt.Errorf("invalid price for nozzle %d on device %s: price must be positive", nozzle.Number, deviceID)
			}

			// 直接使用价格值，不再乘以1000
			priceFloat, _ := nozzle.CurrentPrice.Float64()

			priceBCD := s.bcdConverter.EncodePrice(priceFloat, 3, 3)
			pricesBCD = append(pricesBCD, priceBCD)

			s.logger.Debug("Generated price for CD5",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzle.Number),
				zap.String("price", nozzle.CurrentPrice.String()),
				zap.Float64("price_float", priceFloat),
				zap.String("price_bcd", fmt.Sprintf("%X", priceBCD)))
		}
	}

	// 如果没有启用的喷嘴，返回错误而不是使用默认价格
	if len(pricesBCD) == 0 {
		return nil, fmt.Errorf("no enabled nozzles found for device %s", deviceID)
	}

	s.logger.Info("Generated prices for CD5 auto-configuration",
		zap.String("device_id", deviceID),
		zap.Int("nozzle_count", len(nozzles)),
		zap.Int("price_count", len(pricesBCD)),
		zap.String("scenario", "auto_config_from_database"))

	return pricesBCD, nil
}

// UpdateFromDC2Transaction 根据DC2事务更新喷嘴的体积和金额数据
func (s *serviceV2) UpdateFromDC2Transaction(ctx context.Context, deviceID string, nozzleNum byte, volume, amount decimal.Decimal) error {
	// 根据DC2事务更新喷嘴

	// 获取喷嘴
	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNum)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	// 修复：直接使用真实金额，不做倍数处理，保证数据一致性
	nozzle.UpdateFromDC2(volume, amount)

	// 保存更新
	if err := s.UpdateNozzle(ctx, nozzle); err != nil {
		return fmt.Errorf("failed to update nozzle: %w", err)
	}

	s.logger.Info("Nozzle updated from DC2 transaction",
		zap.String("nozzle_id", nozzle.ID),
		zap.String("status", string(nozzle.Status)))

	return nil
}

// UpdateFromDC3Transaction 根据DC3事务更新喷嘴的价格和状态数据
func (s *serviceV2) UpdateFromDC3Transaction(ctx context.Context, deviceID string, nozzleNum byte, nozio byte, price decimal.Decimal) error {
	// 根据DC3事务更新喷嘴

	// 获取喷嘴
	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNum)
	if err != nil {
		s.logger.Error("Failed to get nozzle in UpdateFromDC3Transaction",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_num", nozzleNum),
			zap.Error(err))
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	// 修复：直接使用真实价格，不做倍数处理，保证数据一致性
	nozzle.UpdateFromDC3(nozio, price)

	// 保存更新
	if err := s.UpdateNozzle(ctx, nozzle); err != nil {
		s.logger.Error("Failed to update nozzle in UpdateFromDC3Transaction",
			zap.String("nozzle_id", nozzle.ID),
			zap.Error(err))
		return fmt.Errorf("failed to update nozzle: %w", err)
	}

	s.logger.Info("Nozzle updated from DC3 transaction",
		zap.String("nozzle_id", nozzle.ID),
		zap.String("status", string(nozzle.Status)),
		zap.Bool("is_out", nozzle.IsOut),
		zap.String("price", price.String()))

	return nil
}

// BatchUpdateNozzleStates 批量更新喷嘴状态
func (s *serviceV2) BatchUpdateNozzleStates(ctx context.Context, deviceID string, updates []models.NozzleStateUpdate) error {
	// 批量更新喷嘴状态

	// 在事务中执行批量更新
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			nozzle, err := s.GetNozzle(ctx, deviceID, update.NozzleNumber)
			if err != nil {
				s.logger.Warn("Failed to get nozzle for batch update",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_num", update.NozzleNumber),
					zap.Error(err))
				continue
			}

			// 应用更新
			if update.Status != "" {
				nozzle.Status = update.Status
			}
			if update.IsOut != nil {
				nozzle.IsOut = *update.IsOut
			}
			if update.IsSelected != nil {
				nozzle.IsSelected = *update.IsSelected
			}
			if update.CurrentPrice != nil {
				// 修复：直接使用真实价格值，不做倍数处理
				nozzle.CurrentPrice = *update.CurrentPrice
			}
			if update.CurrentVolume != nil {
				nozzle.CurrentVolume = *update.CurrentVolume
			}
			if update.CurrentAmount != nil {
				// 修复：直接使用真实金额值，不做倍数处理
				nozzle.CurrentAmount = *update.CurrentAmount
			}

			// 更新时间戳
			nozzle.UpdatedAt = time.Now()

			// 保存更新
			if err := tx.Save(nozzle).Error; err != nil {
				return fmt.Errorf("failed to update nozzle %d: %w", update.NozzleNumber, err)
			}
		}

		return nil
	})
}

// SyncDeviceNozzles 同步设备喷嘴状态
func (s *serviceV2) SyncDeviceNozzles(ctx context.Context, deviceID string) error {
	// 同步设备喷嘴

	// 获取设备
	var device models.Device
	if err := s.db.WithContext(ctx).Preload("Nozzles").First(&device, "id = ?", deviceID).Error; err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	// 更新设备的喷嘴计数
	device.NozzleCount = len(device.Nozzles)

	// 验证喷嘴配置
	if err := device.ValidateNozzleConfiguration(); err != nil {
		return fmt.Errorf("nozzle configuration validation failed: %w", err)
	}

	s.logger.Info("Device nozzles synced successfully",
		zap.String("device_id", deviceID),
		zap.Int("nozzle_count", device.NozzleCount))

	return nil
}

// InitializeDeviceNozzles 初始化设备喷嘴
func (s *serviceV2) InitializeDeviceNozzles(ctx context.Context, deviceID string, configs []models.NozzleConfig) error {
	// 初始化设备喷嘴

	// 在事务中执行初始化
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除现有喷嘴
		if err := tx.Where("device_id = ?", deviceID).Delete(&models.Nozzle{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing nozzles: %w", err)
		}

		// 创建新喷嘴
		for _, config := range configs {
			nozzle := &models.Nozzle{
				ID:          fmt.Sprintf("%s-nozzle-%d", deviceID, config.Number),
				Number:      config.Number,
				Name:        config.Name,
				DeviceID:    deviceID,
				Status:      models.NozzleStatusIdle,
				FuelGradeID: &config.FuelGradeID,
				// 修复：直接使用配置中的真实价格值，不做倍数处理
				CurrentPrice: config.Price,
				Position:     config.Position,
				HoseLength:   config.HoseLength,
				IsEnabled:    config.IsEnabled,
			}

			if err := tx.Create(nozzle).Error; err != nil {
				return fmt.Errorf("failed to create nozzle %d: %w", config.Number, err)
			}
		}

		return nil
	})
}

// StartTransaction 开始交易
func (s *serviceV2) StartTransaction(ctx context.Context, deviceID string, nozzleNum byte, presetVolume, presetAmount *decimal.Decimal) error {
	// 开始交易

	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNum)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	if !nozzle.CanStartTransaction() {
		return fmt.Errorf("nozzle cannot start transaction: status=%s, enabled=%t", nozzle.Status, nozzle.IsEnabled)
	}

	// 设置预设值
	nozzle.PresetVolume = presetVolume
	nozzle.PresetAmount = presetAmount
	nozzle.Status = models.NozzleStatusAuthorized
	nozzle.IsSelected = true

	return s.UpdateNozzle(ctx, nozzle)
}

// CompleteTransaction 完成交易
func (s *serviceV2) CompleteTransaction(ctx context.Context, deviceID string, nozzleNum byte) error {
	// 完成交易

	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNum)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	// 完成交易并更新累计数据
	nozzle.CompleteTransaction()

	return s.UpdateNozzle(ctx, nozzle)
}

// ResetTransaction 重置交易
func (s *serviceV2) ResetTransaction(ctx context.Context, deviceID string, nozzleNum byte) error {
	// 重置交易

	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNum)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	// 重置交易数据
	nozzle.ResetTransaction()

	return s.UpdateNozzle(ctx, nozzle)
}

// HasFillingNozzles 检查设备下是否有加注中的喷嘴
func (s *serviceV2) HasFillingNozzles(ctx context.Context, deviceID string) (bool, error) {
	s.logger.Debug("Checking for filling nozzles", zap.String("device_id", deviceID))

	// model.NozzleStatusAuthorized 也不行
	var count int64
	err := s.db.WithContext(ctx).Model(&models.Nozzle{}).
		Where("device_id = ? AND is_enabled = true AND (status = ? OR status = ?)",
			deviceID, models.NozzleStatusFilling, models.NozzleStatusAuthorized).
		Count(&count).Error

	if err != nil {
		s.logger.Error("Failed to check filling nozzles",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return false, fmt.Errorf("failed to check filling nozzles: %w", err)
	}

	hasFillingNozzles := count > 0
	s.logger.Info("Filling nozzles check result",
		zap.String("device_id", deviceID),
		zap.Bool("has_filling_nozzles", hasFillingNozzles),
		zap.Int64("count", count))

	return hasFillingNozzles, nil
}

// CheckPumpReadingIntegrity 检查喷嘴泵码完整性
// 🆕 在授权时检查是否能获取到体积和金额两个泵码，如果不能则拒绝授权
// 🚀 高性能SQL实现：使用索引查询，适合高频调用
func (s *serviceV2) CheckPumpReadingIntegrity(ctx context.Context, deviceID string, nozzleID string) error {
	// 获取喷嘴信息以确定计数器类型
	nozzle, err := s.GetNozzleByID(ctx, nozzleID)
	if err != nil {
		s.logger.Error("Failed to get nozzle for pump reading check",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	nozzleNumber := nozzle.Number
	volumeCounterType := int16(nozzleNumber)      // 0x01-0x08
	amountCounterType := int16(nozzleNumber + 16) // 0x11-0x18

	// 🚀 高性能SQL查询：使用索引 (device_id, nozzle_id, counter_type, recorded_at)
	// 一次查询检查两个计数器类型是否都有有效数据
	query := `
		SELECT 
			counter_type,
			counter_value,
			recorded_at
		FROM nozzle_counters 
		WHERE device_id = ? 
			AND nozzle_id = ? 
			AND counter_type IN (?, ?) 
			AND is_valid = true
			AND counter_value > 0
		ORDER BY counter_type, recorded_at DESC
	`

	rows, err := s.db.WithContext(ctx).Raw(query, deviceID, nozzleID, volumeCounterType, amountCounterType).Rows()
	if err != nil {
		s.logger.Error("Failed to query pump readings for integrity check",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return fmt.Errorf("failed to query pump readings: %w", err)
	}
	defer rows.Close()

	// 记录找到的计数器类型
	foundCounters := make(map[int16]struct {
		Value      string
		RecordedAt time.Time
	})

	for rows.Next() {
		var counterType int16
		var counterValue string
		var recordedAt time.Time

		if err := rows.Scan(&counterType, &counterValue, &recordedAt); err != nil {
			s.logger.Error("Failed to scan pump reading row",
				zap.Error(err))
			continue
		}

		// 只保留每种类型的最新记录（由于ORDER BY，第一条就是最新的）
		if _, exists := foundCounters[counterType]; !exists {
			foundCounters[counterType] = struct {
				Value      string
				RecordedAt time.Time
			}{
				Value:      counterValue,
				RecordedAt: recordedAt,
			}
		}
	}

	if err := rows.Err(); err != nil {
		s.logger.Error("Error iterating pump reading rows",
			zap.Error(err))
		return fmt.Errorf("error reading pump reading results: %w", err)
	}

	// 检查是否找到了两个必需的计数器
	var missingCounters []string
	var hasVolumeReading, hasAmountReading bool

	if volumeData, found := foundCounters[volumeCounterType]; found {
		hasVolumeReading = true
		s.logger.Debug("Volume pump reading found",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("volume_reading", volumeData.Value),
			zap.Time("recorded_at", volumeData.RecordedAt))
	} else {
		missingCounters = append(missingCounters, fmt.Sprintf("volume(0x%02x)", volumeCounterType))
		s.logger.Debug("Volume pump reading missing",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Uint8("nozzle_number", nozzleNumber))
	}

	if amountData, found := foundCounters[amountCounterType]; found {
		hasAmountReading = true
		s.logger.Debug("Amount pump reading found",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("amount_reading", amountData.Value),
			zap.Time("recorded_at", amountData.RecordedAt))
	} else {
		missingCounters = append(missingCounters, fmt.Sprintf("amount(0x%02x)", amountCounterType))
		s.logger.Debug("Amount pump reading missing",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Uint8("nozzle_number", nozzleNumber))
	}

	// 如果缺少任何一个泵码，拒绝授权
	if !hasVolumeReading || !hasAmountReading {
		s.logger.Warn("Authorization rejected: incomplete pump readings",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.Strings("missing_counters", missingCounters),
			zap.Bool("has_volume_reading", hasVolumeReading),
			zap.Bool("has_amount_reading", hasAmountReading))

		return fmt.Errorf("incomplete pump readings: missing %v. Need both volume and amount readings for authorization", missingCounters)
	}

	s.logger.Info("Pump reading integrity check passed",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.Bool("has_volume_reading", hasVolumeReading),
		zap.Bool("has_amount_reading", hasAmountReading),
		zap.Int("total_counters_found", len(foundCounters)))

	return nil
}

// CheckFillingStatusAndLockNozzle 检查喷嘴状态并锁定喷嘴（用于授权前检查）
func (s *serviceV2) CheckFillingStatusAndLockNozzle(ctx context.Context, deviceID string, nozzleID string) error {
	// 检查设备是否有加注中的喷嘴
	hasFillingNozzles, err := s.HasFillingNozzles(ctx, deviceID)
	if err != nil {
		s.logger.Error("Failed to check filling nozzles",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return fmt.Errorf("failed to check filling nozzles: %w", err)
	}

	if hasFillingNozzles {
		s.logger.Warn("Authorization rejected: device has filling nozzles",
			zap.String("device_id", deviceID))
		return fmt.Errorf("device has filling nozzles")
	}

	// 检查当前 Nozzle 是否正在工作
	nozzle, err := s.GetNozzleByID(ctx, nozzleID)
	if err != nil {
		s.logger.Error("Failed to get nozzle",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	if nozzle.IsTransactionInProgress() {
		s.logger.Warn("Authorization rejected2: nozzle is in transaction",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzle.ID),
			zap.String("status", string(nozzle.Status)))
		return fmt.Errorf("nozzle is in transaction")
	}

	s.logger.Info("Nozzle locked successfully",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzle.ID))

	return nil
}

// SetNozzleTargetPrice 设置喷嘴目标价格，触发异步调价流程
func (s *serviceV2) SetNozzleTargetPrice(ctx context.Context, deviceID string, nozzleNumber byte, targetPrice decimal.Decimal) error {
	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	// 验证价格范围
	priceFloat, _ := targetPrice.Float64()
	if priceFloat <= 0 || priceFloat > 999999.999 {
		return fmt.Errorf("invalid target price %.3f: must be between 0.001 and 999999.999", priceFloat)
	}

	// 设置目标价格并标记为待同步
	nozzle.SetTargetPrice(targetPrice)

	if err := s.UpdateNozzle(ctx, nozzle); err != nil {
		return fmt.Errorf("failed to update nozzle target price: %w", err)
	}

	s.logger.Info("Target price set for nozzle",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("current_price", nozzle.CurrentPrice.String()),
		zap.String("target_price", targetPrice.String()),
		zap.String("status", string(nozzle.PriceStatus)))

	return nil
}

// GetNozzlesNeedingPriceSync 获取需要价格同步的喷嘴列表
func (s *serviceV2) GetNozzlesNeedingPriceSync(ctx context.Context, deviceID string) ([]*models.Nozzle, error) {
	var nozzles []*models.Nozzle

	err := s.db.WithContext(ctx).
		Where("device_id = ? AND price_status = ?", deviceID, models.PriceStatusPending).
		Find(&nozzles).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get nozzles needing price sync: %w", err)
	}

	// 找到需要价格同步的喷嘴

	return nozzles, nil
}

// GetNozzlesPriceSyncing 获取正在同步价格的喷嘴列表
func (s *serviceV2) GetNozzlesPriceSyncing(ctx context.Context, deviceID string) ([]*models.Nozzle, error) {
	var nozzles []*models.Nozzle

	err := s.db.WithContext(ctx).
		Where("device_id = ? AND price_status = ?", deviceID, models.PriceStatusSending).
		Find(&nozzles).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get nozzles price syncing: %w", err)
	}

	return nozzles, nil
}

// MarkNozzlePriceSending 标记喷嘴价格正在发送
func (s *serviceV2) MarkNozzlePriceSending(ctx context.Context, deviceID string, nozzleNumber byte, sentPrice decimal.Decimal) error {
	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	nozzle.MarkPriceSending(sentPrice)

	if err := s.UpdateNozzle(ctx, nozzle); err != nil {
		return fmt.Errorf("failed to mark nozzle price sending: %w", err)
	}

	// 标记喷嘴价格为发送中

	return nil
}

// ConfirmNozzlePriceSync 确认喷嘴价格同步成功
func (s *serviceV2) ConfirmNozzlePriceSync(ctx context.Context, deviceID string, nozzleNumber byte) error {
	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	oldPrice := nozzle.CurrentPrice
	nozzle.ConfirmPriceSync()

	if err := s.UpdateNozzle(ctx, nozzle); err != nil {
		return fmt.Errorf("failed to confirm nozzle price sync: %w", err)
	}

	s.logger.Info("Nozzle price sync confirmed",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("old_price", oldPrice.String()),
		zap.String("new_price", nozzle.CurrentPrice.String()))

	return nil
}

// MarkNozzlePriceFailed 标记喷嘴价格同步失败
func (s *serviceV2) MarkNozzlePriceFailed(ctx context.Context, deviceID string, nozzleNumber byte, reason string) error {
	nozzle, err := s.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		return fmt.Errorf("failed to get nozzle: %w", err)
	}

	nozzle.MarkPriceFailed()

	if err := s.UpdateNozzle(ctx, nozzle); err != nil {
		return fmt.Errorf("failed to mark nozzle price failed: %w", err)
	}

	s.logger.Warn("Nozzle price sync failed",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("reason", reason))

	return nil
}

// CheckPriceSyncTimeout 检查价格同步超时
func (s *serviceV2) CheckPriceSyncTimeout(ctx context.Context, deviceID string, timeout time.Duration) error {
	var nozzles []*models.Nozzle

	err := s.db.WithContext(ctx).
		Where("device_id = ? AND price_status = ? AND price_sent_at < ?",
			deviceID, models.PriceStatusSending, time.Now().Add(-timeout)).
		Find(&nozzles).Error

	if err != nil {
		return fmt.Errorf("failed to check price sync timeout: %w", err)
	}

	for _, nozzle := range nozzles {
		nozzle.MarkPriceTimeout()
		if err := s.UpdateNozzle(ctx, nozzle); err != nil {
			s.logger.Error("Failed to mark nozzle price timeout",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzle.Number),
				zap.Error(err))
			continue
		}

		s.logger.Warn("Nozzle price sync timeout",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", nozzle.Number),
			zap.Duration("timeout", timeout))
	}

	return nil
}

// GetPriceSyncStatus 获取设备价格同步状态
func (s *serviceV2) GetPriceSyncStatus(ctx context.Context, deviceID string) (map[string]interface{}, error) {
	var stats struct {
		Synced  int64 `gorm:"column:synced"`
		Pending int64 `gorm:"column:pending"`
		Sending int64 `gorm:"column:sending"`
		Failed  int64 `gorm:"column:failed"`
		Timeout int64 `gorm:"column:timeout"`
	}

	query := `
		SELECT 
			COUNT(CASE WHEN price_status = 'synced' THEN 1 END) as synced,
			COUNT(CASE WHEN price_status = 'pending' THEN 1 END) as pending,
			COUNT(CASE WHEN price_status = 'sending' THEN 1 END) as sending,
			COUNT(CASE WHEN price_status = 'failed' THEN 1 END) as failed,
			COUNT(CASE WHEN price_status = 'timeout' THEN 1 END) as timeout
		FROM nozzles WHERE device_id = ? AND is_enabled = true
	`

	err := s.db.WithContext(ctx).Raw(query, deviceID).Scan(&stats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get price sync status: %w", err)
	}

	status := map[string]interface{}{
		"device_id": deviceID,
		"synced":    stats.Synced,
		"pending":   stats.Pending,
		"sending":   stats.Sending,
		"failed":    stats.Failed,
		"timeout":   stats.Timeout,
		"total":     stats.Synced + stats.Pending + stats.Sending + stats.Failed + stats.Timeout,
	}

	// 获取价格同步状态

	return status, nil
}

// BuildCompleteNozzlePrices 构建完整的喷嘴价格数组，用于CD5命令
// 解决Wayne DART协议要求按序传递所有喷嘴价格的问题
// 当更新单个喷嘴价格时，需要包含所有喷嘴的当前价格，确保CD5命令格式正确
func (s *serviceV2) BuildCompleteNozzlePrices(ctx context.Context, deviceID string, targetNozzle byte, targetPrice decimal.Decimal) ([]NozzlePriceInfo, error) {
	// 构建CD5命令的完整喷嘴价格

	// 获取设备所有喷嘴
	nozzles, err := s.GetDeviceNozzles(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device nozzles: %w", err)
	}

	if len(nozzles) == 0 {
		return nil, fmt.Errorf("no nozzles found for device %s", deviceID)
	}

	// 创建编号到喷嘴的映射，方便查找
	nozzleMap := make(map[byte]*models.Nozzle)
	var maxNozzleNumber byte

	for _, nozzle := range nozzles {
		// 修复：添加空指针检查
		if nozzle == nil {
			s.logger.Warn("Found nil nozzle in device nozzles list",
				zap.String("device_id", deviceID))
			continue
		}

		if nozzle.IsEnabled {
			nozzleMap[nozzle.Number] = nozzle
			if nozzle.Number > maxNozzleNumber {
				maxNozzleNumber = nozzle.Number
			}
		}
	}

	// 构建价格数组，严格按照喷嘴编号顺序：1, 2, 3, ..., maxNozzleNumber
	// 关键修复：确保数组索引与Wayne DART协议的PRI位置完全对应
	// PRI1 (数组[0]) = 喷嘴1, PRI2 (数组[1]) = 喷嘴2, 依此类推
	var prices []NozzlePriceInfo

	for nozzleNum := byte(1); nozzleNum <= maxNozzleNumber; nozzleNum++ {
		nozzle := nozzleMap[nozzleNum]

		var price decimal.Decimal

		if nozzle == nil {
			// 喷嘴不存在 - 使用安全默认价格，保持数组连续性
			s.logger.Debug("Nozzle not found, using default price for protocol compliance",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleNum))
			price = decimal.NewFromFloat(6.333) // 安全默认价格
		} else {
			// 喷嘴存在 - 检查是否为目标喷嘴
			if nozzleNum == targetNozzle {
				// 使用目标价格（API传入的新价格）
				price = targetPrice
				s.logger.Debug("Using target price for updated nozzle",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_number", nozzleNum),
					zap.String("target_price", targetPrice.String()))
			} else {
				// 使用当前价格
				price = nozzle.CurrentPrice
				s.logger.Debug("Using current price for existing nozzle",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_number", nozzleNum),
					zap.String("current_price", nozzle.CurrentPrice.String()))
			}
		}

		// 验证价格范围（真实价格值范围）
		priceFloat, _ := price.Float64()
		if priceFloat < 0.001 || priceFloat > 999999.999 {
			s.logger.Warn("Price out of Wayne DART BCD range, using safe default",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleNum),
				zap.Float64("invalid_price", priceFloat))
			price = decimal.NewFromFloat(6.333) // 安全默认价格(6.333元真实值)
		}

		// 关键修复：严格按喷嘴编号顺序添加到数组
		// 确保 prices[0] = 喷嘴1, prices[1] = 喷嘴2, 等等
		prices = append(prices, NozzlePriceInfo{
			NozzleNumber: nozzleNum, // 保持原始喷嘴编号
			Price:        price,
			Decimals:     3, // Wayne DART协议标准：3位小数
		})

		s.logger.Debug("Added nozzle price to CD5 array",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", nozzleNum),
			zap.Int("array_index", len(prices)-1),
			zap.String("price", price.String()),
			zap.String("protocol_position", fmt.Sprintf("PRI%d", nozzleNum)))
	}

	if len(prices) == 0 {
		s.logger.Error("No nozzles found or no prices built",
			zap.String("device_id", deviceID),
			zap.Uint8("target_nozzle", targetNozzle),
			zap.Int("total_nozzles_from_db", len(nozzles)),
			zap.Uint8("max_nozzle_number", maxNozzleNumber))
		return nil, fmt.Errorf("no enabled nozzles found for device %s", deviceID)
	}

	s.logger.Info("Built complete nozzle prices for CD5 command with FIXED protocol compliance",
		zap.String("device_id", deviceID),
		zap.Uint8("target_nozzle", targetNozzle),
		zap.String("target_price", targetPrice.String()),
		zap.Int("total_nozzles", len(prices)),
		zap.Uint8("max_nozzle_number", maxNozzleNumber),
		zap.String("protocol_note", "Array index FIXED: prices[0]=Nozzle1/PRI1, prices[1]=Nozzle2/PRI2, etc."),
		zap.String("price_array_order", fmt.Sprintf("Nozzle1-8: %v", prices)))

	return prices, nil
}

// BatchSetNozzleTargetPrices 原子性批量设置喷嘴目标价格
// 确保所有价格更新在同一个数据库事务中完成，避免部分成功部分失败的情况
func (s *serviceV2) BatchSetNozzleTargetPrices(ctx context.Context, deviceID string, priceUpdates []NozzleTargetPriceUpdate) error {
	if len(priceUpdates) == 0 {
		return fmt.Errorf("no price updates provided")
	}

	s.logger.Info("Starting batch target price update",
		zap.String("device_id", deviceID),
		zap.Int("update_count", len(priceUpdates)))

	// 在数据库事务中执行所有价格更新
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range priceUpdates {
			// 验证价格范围
			priceFloat, _ := update.TargetPrice.Float64()
			if priceFloat <= 0 || priceFloat > 999999.999 {
				return fmt.Errorf("invalid target price %.3f for nozzle %d: must be between 0.001 and 999999.999",
					priceFloat, update.NozzleNumber)
			}

			// 获取喷嘴（在事务内）
			var nozzle models.Nozzle
			if err := tx.Where("device_id = ? AND number = ?", deviceID, update.NozzleNumber).First(&nozzle).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					return fmt.Errorf("nozzle %d not found for device %s", update.NozzleNumber, deviceID)
				}
				return fmt.Errorf("failed to get nozzle %d: %w", update.NozzleNumber, err)
			}

			// 验证喷嘴是否启用
			if !nozzle.IsEnabled {
				return fmt.Errorf("nozzle %d is disabled", update.NozzleNumber)
			}

			// 设置目标价格并标记为待同步
			nozzle.SetTargetPrice(update.TargetPrice)

			// 保存更新（在事务内）
			if err := tx.Save(&nozzle).Error; err != nil {
				return fmt.Errorf("failed to update target price for nozzle %d: %w", update.NozzleNumber, err)
			}

			s.logger.Debug("Target price set for nozzle in transaction",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", update.NozzleNumber),
				zap.String("current_price", nozzle.CurrentPrice.String()),
				zap.String("target_price", update.TargetPrice.String()))
		}

		return nil
	})
}

// BatchConfirmPriceSync 原子性批量确认价格同步
// 在同一个事务中确认多个喷嘴的价格同步状态
func (s *serviceV2) BatchConfirmPriceSync(ctx context.Context, deviceID string, nozzleNumbers []byte) error {
	if len(nozzleNumbers) == 0 {
		return fmt.Errorf("no nozzle numbers provided")
	}

	s.logger.Info("Starting batch price sync confirmation",
		zap.String("device_id", deviceID),
		zap.Int("nozzle_count", len(nozzleNumbers)))

	// 在数据库事务中执行所有确认操作
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, nozzleNumber := range nozzleNumbers {
			// 获取喷嘴（在事务内）
			var nozzle models.Nozzle
			if err := tx.Where("device_id = ? AND number = ?", deviceID, nozzleNumber).First(&nozzle).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					s.logger.Warn("Nozzle not found for price sync confirmation",
						zap.String("device_id", deviceID),
						zap.Uint8("nozzle_number", nozzleNumber))
					continue
				}
				return fmt.Errorf("failed to get nozzle %d: %w", nozzleNumber, err)
			}

			// 记录旧价格用于日志
			oldPrice := nozzle.CurrentPrice

			// 确认价格同步
			nozzle.ConfirmPriceSync()

			// 保存更新（在事务内）
			if err := tx.Save(&nozzle).Error; err != nil {
				return fmt.Errorf("failed to confirm price sync for nozzle %d: %w", nozzleNumber, err)
			}

			s.logger.Debug("Price sync confirmed for nozzle in transaction",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.String("old_price", oldPrice.String()),
				zap.String("new_price", nozzle.CurrentPrice.String()))
		}

		return nil
	})
}

// 🚀 新增：简化的直接数据库更新方法

// UpdateNozzleStatusDirect 直接更新喷嘴状态到数据库
func (s *serviceV2) UpdateNozzleStatusDirect(ctx context.Context, deviceID string, nozzleID string, status models.NozzleStatus) error {
	// 🚀 防重复机制：检查是否需要更新
	cacheKey := fmt.Sprintf("%s-%s", deviceID, nozzleID)
	now := time.Now()

	s.statusCacheMutex.RLock()
	lastStatus, hasLastStatus := s.lastStatusCache[cacheKey]
	lastTime, hasLastTime := s.lastStatusTime[cacheKey]
	s.statusCacheMutex.RUnlock()

	// 如果在1秒内设置了相同的状态，跳过更新
	if hasLastStatus && hasLastTime && lastStatus == status && now.Sub(lastTime) < time.Second {
		s.logger.Debug("Skipping duplicate nozzle status update",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("status", string(status)),
			zap.Duration("time_since_last", now.Sub(lastTime)))
		return nil
	}

	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("id = ?", nozzleID).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": now,
		})

	if result.Error != nil {
		s.logger.Error("Failed to update nozzle status directly",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("status", string(status)),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update nozzle status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("nozzle not found: device=%s, nozzle_id=%s", deviceID, nozzleID)
	}

	// 🚀 更新缓存
	s.statusCacheMutex.Lock()
	s.lastStatusCache[cacheKey] = status
	s.lastStatusTime[cacheKey] = now
	s.statusCacheMutex.Unlock()

	s.logger.Info("Nozzle status updated directly",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("status", string(status)),
		zap.String("previous_status", string(lastStatus)))

	return nil
}

// UpdateNozzleDataDirect 直接更新喷嘴体积金额数据到数据库
func (s *serviceV2) UpdateNozzleDataDirect(ctx context.Context, deviceID string, nozzleID string, volume, amount decimal.Decimal) error {
	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("id = ?", nozzleID).
		Updates(map[string]interface{}{
			"current_volume": volume,
			"current_amount": amount,
			"updated_at":     time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to update nozzle data directly",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("volume", volume.String()),
			zap.String("amount", amount.String()),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update nozzle data: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("nozzle not found: device=%s, nozzle_id=%s", deviceID, nozzleID)
	}

	s.logger.Debug("Nozzle data updated directly",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("volume", volume.String()),
		zap.String("amount", amount.String()))

	return nil
}

// UpdateNozzleDC3Direct 直接更新DC3相关字段到数据库
func (s *serviceV2) UpdateNozzleDC3Direct(ctx context.Context, deviceID string, nozzleID string, price decimal.Decimal, isOut, isSelected bool) error {
	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("id = ?", nozzleID).
		Updates(map[string]interface{}{
			"current_price": price,
			"is_out":        isOut,
			"is_selected":   isSelected,
			"updated_at":    time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to update nozzle DC3 data directly",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("price", price.String()),
			zap.Bool("is_out", isOut),
			zap.Bool("is_selected", isSelected),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update nozzle DC3 data: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("nozzle not found: device=%s, nozzle_id=%s", deviceID, nozzleID)
	}

	s.logger.Debug("Nozzle DC3 data updated directly",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("price", price.String()),
		zap.Bool("is_out", isOut),
		zap.Bool("is_selected", isSelected))

	return nil
}

// ResetDeviceNozzlesDirect 直接重置设备所有喷嘴到数据库
func (s *serviceV2) ResetDeviceNozzlesDirect(ctx context.Context, deviceID string) error {
	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("device_id = ?", deviceID).
		Updates(map[string]interface{}{
			"status":         models.NozzleStatusIdle,
			"current_volume": decimal.Zero,
			"current_amount": decimal.Zero,
			"preset_volume":  nil,
			"preset_amount":  nil,
			"is_out":         false,
			"is_selected":    false,
			"updated_at":     time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to reset device nozzles directly",
			zap.String("device_id", deviceID),
			zap.Error(result.Error))
		return fmt.Errorf("failed to reset device nozzles: %w", result.Error)
	}

	s.logger.Info("Device nozzles reset directly",
		zap.String("device_id", deviceID),
		zap.Int64("affected_rows", result.RowsAffected))

	return nil
}

// UpdateAllNozzlesStatusDirect 直接更新设备所有喷嘴状态到数据库
func (s *serviceV2) UpdateAllNozzlesStatusDirect(ctx context.Context, deviceID string, status models.NozzleStatus) error {
	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("device_id = ?", deviceID).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to update all nozzles status directly",
			zap.String("device_id", deviceID),
			zap.String("status", string(status)),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update all nozzles status: %w", result.Error)
	}

	s.logger.Info("All device nozzles status updated directly",
		zap.String("device_id", deviceID),
		zap.String("status", string(status)),
		zap.Int64("affected_rows", result.RowsAffected))

	return nil
}

// UpdateActiveNozzlesStatusDirect 直接更新设备所有启用的喷嘴状态到数据库
func (s *serviceV2) UpdateActiveNozzlesStatusDirect(ctx context.Context, deviceID string, status models.NozzleStatus) error {
	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("device_id = ? AND is_enabled = ?", deviceID, true).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to update active nozzles status directly",
			zap.String("device_id", deviceID),
			zap.String("status", string(status)),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update active nozzles status: %w", result.Error)
	}

	s.logger.Info("Active device nozzles status updated directly",
		zap.String("device_id", deviceID),
		zap.String("status", string(status)),
		zap.Int64("affected_rows", result.RowsAffected))

	return nil
}

// UpdateMultipleNozzlesStatusByNumberDirect 批量更新多个指定喷嘴编号的状态到数据库
func (s *serviceV2) UpdateMultipleNozzlesStatusByNumberDirect(ctx context.Context, deviceID string, nozzleNumbers []byte, status models.NozzleStatus) error {
	if len(nozzleNumbers) == 0 {
		return fmt.Errorf("no nozzle numbers provided for batch update")
	}

	s.logger.Info("Starting batch nozzle status update by number",
		zap.String("device_id", deviceID),
		zap.Int("nozzle_count", len(nozzleNumbers)),
		zap.String("status", string(status)))

	// 在数据库事务中执行批量更新
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, nozzleNumber := range nozzleNumbers {
			// 🚀 防重复机制：检查是否需要更新
			cacheKey := fmt.Sprintf("%s-%d", deviceID, nozzleNumber)
			now := time.Now()

			s.statusCacheMutex.RLock()
			lastStatus, hasLastStatus := s.lastStatusCache[cacheKey]
			lastTime, hasLastTime := s.lastStatusTime[cacheKey]
			s.statusCacheMutex.RUnlock()

			// 如果在1秒内设置了相同的状态，跳过更新
			if hasLastStatus && hasLastTime && lastStatus == status && now.Sub(lastTime) < time.Second {
				s.logger.Debug("Skipping duplicate nozzle status update by number in batch",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_number", nozzleNumber),
					zap.String("status", string(status)),
					zap.Duration("time_since_last", now.Sub(lastTime)))
				continue
			}

			result := tx.
				Model(&models.Nozzle{}).
				Where("device_id = ? AND number = ?", deviceID, nozzleNumber).
				Updates(map[string]interface{}{
					"status":     status,
					"updated_at": now,
				})

			if result.Error != nil {
				s.logger.Error("Failed to update nozzle status by number in batch",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_number", nozzleNumber),
					zap.String("status", string(status)),
					zap.Error(result.Error))
				return fmt.Errorf("failed to update nozzle status by number in batch: %w", result.Error)
			}

			if result.RowsAffected == 0 {
				s.logger.Warn("Nozzle not found for batch status update by number",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_number", nozzleNumber))
				continue
			}

			// 🚀 更新缓存
			s.statusCacheMutex.Lock()
			s.lastStatusCache[cacheKey] = status
			s.lastStatusTime[cacheKey] = now
			s.statusCacheMutex.Unlock()

			s.logger.Debug("Nozzle status updated by number in batch",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.String("status", string(status)),
				zap.String("previous_status", string(lastStatus)))
		}

		return nil
	})
}

// UpdateNozzleFillingWithOthersIdle 将指定喷嘴设为filling状态，同时将其他喷嘴设为idle状态
// func (s *serviceV2) UpdateNozzleFillingWithOthersIdle(ctx context.Context, deviceID string, workingnozzleID string) error {
// 	// 使用事务确保数据一致性
// 	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
// 		// 1. 将其他喷嘴设为idle状态
// 		result1 := tx.Model(&models.Nozzle{}).
// 			Where("device_id = ? AND number != ?", deviceID, workingNozzleID).
// 			Updates(map[string]interface{}{
// 				"status":     models.NozzleStatusIdle,
// 				"updated_at": time.Now(),
// 			})

// 		if result1.Error != nil {
// 			s.logger.Error("Failed to set other nozzles to idle",
// 				zap.String("device_id", deviceID),
// 				zap.Uint8("working_nozzle_id", workingNozzleID),
// 				zap.Error(result1.Error))
// 			return fmt.Errorf("failed to set other nozzles to idle: %w", result1.Error)
// 		}

// 		// 2. 将工作喷嘴设为filling状态
// 		result2 := tx.Model(&models.Nozzle{}).
// 			Where("device_id = ? AND number = ?", deviceID, workingNozzleID).
// 			Updates(map[string]interface{}{
// 				"status":     models.NozzleStatusFilling,
// 				"updated_at": time.Now(),
// 			})

// 		if result2.Error != nil {
// 			s.logger.Error("Failed to set working nozzle to filling",
// 				zap.String("device_id", deviceID),
// 				zap.Uint8("working_nozzle_id", workingNozzleID),
// 				zap.Error(result2.Error))
// 			return fmt.Errorf("failed to set working nozzle to filling: %w", result2.Error)
// 		}

// 		if result2.RowsAffected == 0 {
// 			return fmt.Errorf("working nozzle not found: device=%s, number=%d", deviceID, workingNozzleID)
// 		}

// 		s.logger.Info("Nozzle filling status updated with others set to idle",
// 			zap.String("device_id", deviceID),
// 			zap.Uint8("working_nozzle_id", workingNozzleID),
// 			zap.Int64("idle_nozzles_affected", result1.RowsAffected),
// 			zap.Int64("filling_nozzles_affected", result2.RowsAffected))

// 		return nil
// 	})
// }

// QueryCurrentNozzleStatus 查询当前喷嘴状态
func (s *serviceV2) QueryCurrentNozzleStatus(ctx context.Context, deviceID string, nozzleID string) (models.NozzleStatus, error) {
	var status models.NozzleStatus
	err := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Select("status").
		Where("id = ?", nozzleID).
		Scan(&status).Error

	if err != nil {
		return "", fmt.Errorf("failed to query nozzle status: %w", err)
	}

	return status, nil
}

// QueryCurrentNozzleData 查询当前喷嘴体积金额数据
func (s *serviceV2) QueryCurrentNozzleData(ctx context.Context, deviceID string, nozzleID string) (decimal.Decimal, decimal.Decimal, error) {
	var result struct {
		CurrentVolume decimal.Decimal `gorm:"column:current_volume"`
		CurrentAmount decimal.Decimal `gorm:"column:current_amount"`
	}

	err := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Select("current_volume, current_amount").
		Where("id = ?", nozzleID).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to query nozzle data: %w", err)
	}

	return result.CurrentVolume, result.CurrentAmount, nil
}

// QueryCurrentNozzleIsOut 查询当前喷嘴是否拔出状态
func (s *serviceV2) QueryCurrentNozzleIsOut(ctx context.Context, nozzleID string) (bool, error) {
	var isOut bool
	err := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Select("is_out").
		Where("id = ?", nozzleID).
		Scan(&isOut).Error

	if err != nil {
		return false, fmt.Errorf("failed to query nozzle is_out status: %w", err)
	}

	return isOut, nil
}

// 🆕 预授权相关方法实现

// UpdateNozzlePreAuth 更新喷嘴预授权信息
func (s *serviceV2) UpdateNozzlePreAuth(ctx context.Context, nozzleID string, authType string, preAuthNumber decimal.Decimal, expiresAt time.Time) error {
	now := time.Now()

	updateFields := map[string]interface{}{
		"preauth_type":       authType,
		"preauth_number":     preAuthNumber,
		"preauth_created_at": now,
		"preauth_expires_at": expiresAt,
		"updated_at":         now,
	}

	// 根据授权类型设置预设值
	if authType == "preset_volume" || authType == "preset_full" {
		updateFields["preset_volume"] = preAuthNumber
		updateFields["preset_amount"] = nil // 清空金额预设
	} else if authType == "preset_amount" {
		updateFields["preset_amount"] = preAuthNumber
		updateFields["preset_volume"] = nil // 清空体积预设
	}

	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("id = ?", nozzleID).
		Updates(updateFields)

	if result.Error != nil {
		s.logger.Error("Failed to update nozzle preauth",
			zap.String("nozzle_id", nozzleID),
			zap.String("auth_type", authType),
			zap.String("preauth_number", preAuthNumber.String()),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update nozzle preauth: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("nozzle not found: %s", nozzleID)
	}

	s.logger.Info("Nozzle preauth updated successfully",
		zap.String("nozzle_id", nozzleID),
		zap.String("auth_type", authType),
		zap.String("preauth_number", preAuthNumber.String()),
		zap.Time("expires_at", expiresAt))

	return nil
}

// ClearNozzlePreAuth 清除喷嘴预授权信息
func (s *serviceV2) ClearNozzlePreAuth(ctx context.Context, nozzleID string) error {
	updateFields := map[string]interface{}{
		"preauth_type":       nil,
		"preauth_number":     nil,
		"preauth_created_at": nil,
		"preauth_expires_at": nil,
		"preset_volume":      nil,
		"preset_amount":      nil,
		"updated_at":         time.Now(),
	}

	result := s.db.WithContext(ctx).
		Model(&models.Nozzle{}).
		Where("id = ?", nozzleID).
		Updates(updateFields)

	if result.Error != nil {
		s.logger.Error("Failed to clear nozzle preauth",
			zap.String("nozzle_id", nozzleID),
			zap.Error(result.Error))
		return fmt.Errorf("failed to clear nozzle preauth: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("nozzle not found: %s", nozzleID)
	}

	s.logger.Info("Nozzle preauth cleared successfully",
		zap.String("nozzle_id", nozzleID))

	return nil
}

// CheckActivePreAuthOnDevice 检查设备上是否有有效的预授权
// 🚀 高性能SQL实现：使用索引查询检查设备上是否存在未过期的预授权
func (s *serviceV2) CheckActivePreAuthOnDevice(ctx context.Context, deviceID string) (bool, error) {
	s.logger.Debug("Checking for active preauth on device", zap.String("device_id", deviceID))

	// 🚀 高性能SQL查询：使用索引 (device_id, preauth_expires_at)
	// 检查设备上是否有未过期的预授权
	query := `
		SELECT COUNT(*)
		FROM nozzles
		WHERE device_id = ?
			AND preauth_type IS NOT NULL
			AND preauth_expires_at IS NOT NULL
			AND preauth_expires_at > NOW()
			AND is_enabled = true
	`

	var count int64
	err := s.db.WithContext(ctx).Raw(query, deviceID).Scan(&count).Error
	if err != nil {
		s.logger.Error("Failed to check active preauth on device",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return false, fmt.Errorf("failed to check active preauth: %w", err)
	}

	hasActivePreauth := count > 0
	s.logger.Info("Active preauth check result",
		zap.String("device_id", deviceID),
		zap.Bool("has_active_preauth", hasActivePreauth),
		zap.Int64("active_preauth_count", count))

	return hasActivePreauth, nil
}
