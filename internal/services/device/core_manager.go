package device

import (
	"context"
	"time"

	"go.uber.org/zap"

	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// CoreManager 核心设备管理器 - 只负责基础CRUD操作
type CoreManager struct {
	repository DeviceRepository
	cache      DeviceCache
	logger     *zap.Logger
}

// DeviceRepository 设备数据访问接口
type DeviceRepository interface {
	SaveController(ctx context.Context, controller *models.Controller) error
	LoadController(ctx context.Context, controllerID string) (*models.Controller, error)
	DeleteController(ctx context.Context, controllerID string) error

	SaveDevice(ctx context.Context, device *models.Device) error
	LoadDevice(ctx context.Context, deviceID string) (*models.Device, error)
	DeleteDevice(ctx context.Context, deviceID string) error

	ListControllers(ctx context.Context, filter api.ControllerFilter) ([]*models.Controller, error)
	ListDevices(ctx context.Context, filter api.DeviceFilter) ([]*models.Device, error)
}

// DeviceCache 设备缓存接口
type DeviceCache interface {
	GetController(ctx context.Context, controllerID string) (*models.Controller, error)
	SetController(ctx context.Context, controller *models.Controller) error
	DeleteController(ctx context.Context, controllerID string) error

	GetDevice(ctx context.Context, deviceID string) (*models.Device, error)
	SetDevice(ctx context.Context, device *models.Device) error
	DeleteDevice(ctx context.Context, deviceID string) error
}

// NewCoreManager 创建核心设备管理器
func NewCoreManager(repository DeviceRepository, cache DeviceCache, logger *zap.Logger) *CoreManager {
	return &CoreManager{
		repository: repository,
		cache:      cache,
		logger:     logger,
	}
}

// RegisterController 注册控制器
func (m *CoreManager) RegisterController(ctx context.Context, controller *models.Controller) error {
	if controller == nil {
		return errors.NewValidationError("controller cannot be nil")
	}

	if controller.ID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}

	// 验证控制器配置 TODO
	// if err := m.validateController(controller); err != nil {
	// 	return err
	// }

	// 设置时间戳
	now := time.Now()
	if controller.CreatedAt.IsZero() {
		controller.CreatedAt = now
	}
	controller.UpdatedAt = now
	controller.LastSeen = &now

	// 保存到数据库
	if err := m.repository.SaveController(ctx, controller); err != nil {
		return err
	}

	// 更新缓存
	if err := m.cache.SetController(ctx, controller); err != nil {
		m.logger.Warn("Failed to cache controller", zap.String("controller_id", controller.ID), zap.Error(err))
	}

	m.logger.Info("Controller registered successfully", zap.String("controller_id", controller.ID))
	return nil
}

// RegisterDevice 注册设备
func (m *CoreManager) RegisterDevice(ctx context.Context, device *models.Device) error {
	if device == nil {
		return errors.NewValidationError("device cannot be nil")
	}

	if device.ID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	// 验证设备配置，TODO
	// if err := validateDevice(device); err != nil {
	// 	return err
	// }

	// 设置时间戳
	now := time.Now()
	if device.CreatedAt.IsZero() {
		device.CreatedAt = now
	}
	device.UpdatedAt = now
	device.LastSeen = &now

	// 保存到数据库
	if err := m.repository.SaveDevice(ctx, device); err != nil {
		return err
	}

	// 更新缓存
	if err := m.cache.SetDevice(ctx, device); err != nil {
		m.logger.Warn("Failed to cache device", zap.String("device_id", device.ID), zap.Error(err))
	}

	m.logger.Info("Device registered successfully", zap.String("device_id", device.ID))
	return nil
}

// GetController 获取控制器
func (m *CoreManager) GetController(ctx context.Context, controllerID string) (*models.Controller, error) {
	if controllerID == "" {
		return nil, errors.NewValidationError("controller ID cannot be empty")
	}

	// 先从缓存查找
	controller, err := m.cache.GetController(ctx, controllerID)
	if err == nil {
		return controller, nil
	}

	// 从数据库查找
	controller, err = m.repository.LoadController(ctx, controllerID)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	if err := m.cache.SetController(ctx, controller); err != nil {
		m.logger.Warn("Failed to cache controller", zap.String("controller_id", controllerID), zap.Error(err))
	}

	return controller, nil
}

// GetDevice 获取设备
func (m *CoreManager) GetDevice(ctx context.Context, deviceID string) (*models.Device, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	// 先从缓存查找
	device, err := m.cache.GetDevice(ctx, deviceID)
	if err == nil {
		return device, nil
	}

	// 从数据库查找
	device, err = m.repository.LoadDevice(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	if err := m.cache.SetDevice(ctx, device); err != nil {
		m.logger.Warn("Failed to cache device", zap.String("device_id", deviceID), zap.Error(err))
	}

	return device, nil
}

// ListControllers 列出控制器
func (m *CoreManager) ListControllers(ctx context.Context, filter api.ControllerFilter) ([]*models.Controller, error) {
	return m.repository.ListControllers(ctx, filter)
}

// ListDevices 列出设备
func (m *CoreManager) ListDevices(ctx context.Context, filter api.DeviceFilter) ([]*models.Device, error) {
	return m.repository.ListDevices(ctx, filter)
}

// UpdateDeviceStatus 更新设备状态
func (m *CoreManager) UpdateDeviceStatus(ctx context.Context, deviceID string, status models.DeviceStatus) error {
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return err
	}

	if device.Status == status {
		return nil // 状态未变化
	}

	device.Status = status
	device.UpdatedAt = time.Now()
	device.LastSeen = &device.UpdatedAt

	// 保存到数据库
	if err := m.repository.SaveDevice(ctx, device); err != nil {
		return err
	}

	// 更新缓存
	if err := m.cache.SetDevice(ctx, device); err != nil {
		m.logger.Warn("Failed to update device cache", zap.String("device_id", deviceID), zap.Error(err))
	}

	return nil
}
