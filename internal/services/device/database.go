package device

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"

	"go.uber.org/zap"
)

// saveControllerToDB 保存控制器到数据库
func (m *Manager) saveControllerToDB(ctx context.Context, controller *models.Controller) error {
	query := `
		INSERT INTO controllers (
			id, name, type, protocol, address, station_id, status, health,
			config, metadata, version, created_at, updated_at, last_seen
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		ON CONFLICT (id) DO UPDATE SET
			name = $2, type = $3, protocol = $4, address = $5, station_id = $6,
			status = $7, health = $8, config = $9, metadata = $10, version = $11,
			updated_at = $13, last_seen = $14
	`

	configJSON, err := models.MarshalConfig(controller.Config)
	if err != nil {
		return fmt.Errorf("failed to marshal controller config: %w", err)
	}

	metadataJSON, err := models.MarshalMetadata(controller.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal controller metadata: %w", err)
	}

	_, err = m.database.Exec(ctx, query,
		controller.ID,
		controller.Name,
		controller.Type,
		controller.Protocol,
		controller.Address,
		controller.StationID,
		controller.Status,
		controller.Health,
		configJSON,
		metadataJSON,
		controller.Version,
		controller.CreatedAt,
		controller.UpdatedAt,
		controller.LastSeen,
	)

	if err != nil {
		return errors.NewDatabaseError("failed to save controller", err.Error()).WithCause(err)
	}

	return nil
}

// loadControllerFromDB 从数据库加载控制器
func (m *Manager) loadControllerFromDB(ctx context.Context, controllerID string) (*models.Controller, error) {
	query := `
		SELECT id, name, type, protocol, address, station_id, status, health,
			   config, metadata, version, created_at, updated_at, last_seen
		FROM controllers
		WHERE id = $1
	`

	row := m.database.QueryRow(ctx, query, controllerID)

	var controller models.Controller
	var configJSON, metadataJSON []byte
	var lastSeen sql.NullTime

	err := row.Scan(
		&controller.ID,
		&controller.Name,
		&controller.Type,
		&controller.Protocol,
		&controller.Address,
		&controller.StationID,
		&controller.Status,
		&controller.Health,
		&configJSON,
		&metadataJSON,
		&controller.Version,
		&controller.CreatedAt,
		&controller.UpdatedAt,
		&lastSeen,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewDeviceNotFoundError(controllerID)
		}
		return nil, errors.NewDatabaseError("failed to load controller", err.Error()).WithCause(err)
	}

	// 反序列化配置
	if len(configJSON) > 0 {
		config, err := models.UnmarshalConfig(configJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal controller config: %w", err)
		}
		controller.Config = config
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		metadata, err := models.UnmarshalMetadata(metadataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal controller metadata: %w", err)
		}
		controller.Metadata = metadata
	}

	// 处理LastSeen
	if lastSeen.Valid {
		controller.LastSeen = &lastSeen.Time
	}

	return &controller, nil
}

// deleteControllerFromDB 从数据库删除控制器
func (m *Manager) deleteControllerFromDB(ctx context.Context, controllerID string) error {
	query := `DELETE FROM controllers WHERE id = $1`

	result, err := m.database.Exec(ctx, query, controllerID)
	if err != nil {
		return errors.NewDatabaseError("failed to delete controller", err.Error()).WithCause(err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("failed to get affected rows", err.Error()).WithCause(err)
	}

	if rowsAffected == 0 {
		return errors.NewDeviceNotFoundError(controllerID)
	}

	return nil
}

// saveDeviceToDB 保存设备到数据库
func (m *Manager) saveDeviceToDB(ctx context.Context, device *models.Device) error {
	query := `
		INSERT INTO devices (
			id, name, type, controller_id, device_address, station_id, status, health,
			config, metadata, version, created_at, updated_at, last_seen
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		ON CONFLICT (id) DO UPDATE SET
			name = $2, type = $3, controller_id = $4, device_address = $5, station_id = $6,
			status = $7, health = $8, config = $9, metadata = $10, version = $11,
			updated_at = $13, last_seen = $14
	`

	configJSON, err := models.MarshalDeviceConfig(device.Config)
	if err != nil {
		return fmt.Errorf("failed to marshal device config: %w", err)
	}

	metadataJSON, err := models.MarshalMetadata(device.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal device metadata: %w", err)
	}

	_, err = m.database.Exec(ctx, query,
		device.ID,
		device.Name,
		device.Type,
		device.ControllerID,
		device.DeviceAddress,
		device.StationID,
		device.Status,
		device.Health,
		configJSON,
		metadataJSON,
		device.Version,
		device.CreatedAt,
		device.UpdatedAt,
		device.LastSeen,
	)

	if err != nil {
		return errors.NewDatabaseError("failed to save device", err.Error()).WithCause(err)
	}

	return nil
}

// loadDeviceFromDB 从数据库加载设备
func (m *Manager) loadDeviceFromDB(ctx context.Context, deviceID string) (*models.Device, error) {
	query := `
		SELECT id, name, type, controller_id, device_address, station_id, status, health,
			   config, metadata, version, created_at, updated_at, last_seen
		FROM devices
		WHERE id = $1
	`

	row := m.database.QueryRow(ctx, query, deviceID)

	var device models.Device
	var configJSON, metadataJSON []byte
	var lastSeen sql.NullTime

	err := row.Scan(
		&device.ID,
		&device.Name,
		&device.Type,
		&device.ControllerID,
		&device.DeviceAddress,
		&device.StationID,
		&device.Status,
		&device.Health,
		&configJSON,
		&metadataJSON,
		&device.Version,
		&device.CreatedAt,
		&device.UpdatedAt,
		&lastSeen,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewDeviceNotFoundError(deviceID)
		}
		return nil, errors.NewDatabaseError("failed to load device", err.Error()).WithCause(err)
	}

	// 反序列化配置
	if len(configJSON) > 0 {
		config, err := models.UnmarshalDeviceConfig(configJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal device config: %w", err)
		}
		device.Config = config
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		metadata, err := models.UnmarshalMetadata(metadataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal device metadata: %w", err)
		}
		device.Metadata = metadata
	}

	// 处理LastSeen
	if lastSeen.Valid {
		device.LastSeen = &lastSeen.Time
	}

	return &device, nil
}

// deleteDeviceFromDB 从数据库删除设备
func (m *Manager) deleteDeviceFromDB(ctx context.Context, deviceID string) error {
	query := `DELETE FROM devices WHERE id = $1`

	result, err := m.database.Exec(ctx, query, deviceID)
	if err != nil {
		return errors.NewDatabaseError("failed to delete device", err.Error()).WithCause(err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.NewDatabaseError("failed to get affected rows", err.Error()).WithCause(err)
	}

	if rowsAffected == 0 {
		return errors.NewDeviceNotFoundError(deviceID)
	}

	return nil
}

// updateControllerDevicesCache 更新控制器设备列表缓存
func (m *Manager) updateControllerDevicesCache(ctx context.Context, controllerID string) {
	cacheKey := fmt.Sprintf("controller:%s:devices", controllerID)

	// 获取该控制器下的所有设备
	m.mu.RLock()
	devices := make([]*models.Device, 0)
	for _, device := range m.devices {
		if device.ControllerID == controllerID {
			devices = append(devices, device)
		}
	}
	m.mu.RUnlock()

	// 更新缓存
	if err := m.cache.Set(ctx, cacheKey, devices, 15*time.Minute); err != nil {
		m.logger.Warn("Failed to update controller devices cache",
			zap.String("controller_id", controllerID),
			zap.Error(err))
	}
}

// BatchLoadControllersFromDB 批量从数据库加载控制器
func (m *Manager) BatchLoadControllersFromDB(ctx context.Context) error {
	query := `
		SELECT id, name, type, protocol, address, station_id, status, health,
			   config, metadata, version, created_at, updated_at, last_seen
		FROM controllers
		ORDER BY created_at
	`

	rows, err := m.database.Query(ctx, query)
	if err != nil {
		return errors.NewDatabaseError("failed to load controllers", err.Error()).WithCause(err)
	}
	defer rows.Close()

	m.mu.Lock()
	defer m.mu.Unlock()

	var loadedCount int
	for rows.Next() {
		var controller models.Controller
		var configJSON, metadataJSON []byte
		var lastSeen sql.NullTime

		err := rows.Scan(
			&controller.ID,
			&controller.Name,
			&controller.Type,
			&controller.Protocol,
			&controller.Address,
			&controller.StationID,
			&controller.Status,
			&controller.Health,
			&configJSON,
			&metadataJSON,
			&controller.Version,
			&controller.CreatedAt,
			&controller.UpdatedAt,
			&lastSeen,
		)
		if err != nil {
			m.logger.Error("Failed to scan controller row", zap.Error(err))
			continue
		}

		// 反序列化配置
		if len(configJSON) > 0 {
			config, err := models.UnmarshalConfig(configJSON)
			if err != nil {
				m.logger.Error("Failed to unmarshal controller config",
					zap.String("controller_id", controller.ID),
					zap.Error(err))
				continue
			}
			controller.Config = config
		}

		// 反序列化元数据
		if len(metadataJSON) > 0 {
			metadata, err := models.UnmarshalMetadata(metadataJSON)
			if err != nil {
				m.logger.Error("Failed to unmarshal controller metadata",
					zap.String("controller_id", controller.ID),
					zap.Error(err))
				continue
			}
			controller.Metadata = metadata
		}

		// 处理LastSeen
		if lastSeen.Valid {
			controller.LastSeen = &lastSeen.Time
		}

		m.controllers[controller.ID] = &controller
		loadedCount++
	}

	if err := rows.Err(); err != nil {
		return errors.NewDatabaseError("error iterating controller rows", err.Error()).WithCause(err)
	}

	m.logger.Info("Loaded controllers from database", zap.Int("count", loadedCount))
	return nil
}

// BatchLoadDevicesFromDB 批量从数据库加载设备
func (m *Manager) BatchLoadDevicesFromDB(ctx context.Context) error {
	query := `
		SELECT id, name, type, controller_id, device_address, station_id, status, health,
			   config, metadata, version, created_at, updated_at, last_seen
		FROM devices
		ORDER BY created_at
	`

	rows, err := m.database.Query(ctx, query)
	if err != nil {
		return errors.NewDatabaseError("failed to load devices", err.Error()).WithCause(err)
	}
	defer rows.Close()

	m.mu.Lock()
	defer m.mu.Unlock()

	var loadedCount int
	for rows.Next() {
		var device models.Device
		var configJSON, metadataJSON []byte
		var lastSeen sql.NullTime

		err := rows.Scan(
			&device.ID,
			&device.Name,
			&device.Type,
			&device.ControllerID,
			&device.DeviceAddress,
			&device.StationID,
			&device.Status,
			&device.Health,
			&configJSON,
			&metadataJSON,
			&device.Version,
			&device.CreatedAt,
			&device.UpdatedAt,
			&lastSeen,
		)
		if err != nil {
			m.logger.Error("Failed to scan device row", zap.Error(err))
			continue
		}

		// 反序列化配置
		if len(configJSON) > 0 {
			config, err := models.UnmarshalDeviceConfig(configJSON)
			if err != nil {
				m.logger.Error("Failed to unmarshal device config",
					zap.String("device_id", device.ID),
					zap.Error(err))
				continue
			}
			device.Config = config
		}

		// 反序列化元数据
		if len(metadataJSON) > 0 {
			metadata, err := models.UnmarshalMetadata(metadataJSON)
			if err != nil {
				m.logger.Error("Failed to unmarshal device metadata",
					zap.String("device_id", device.ID),
					zap.Error(err))
				continue
			}
			device.Metadata = metadata
		}

		// 处理LastSeen
		if lastSeen.Valid {
			device.LastSeen = &lastSeen.Time
		}

		m.devices[device.ID] = &device
		loadedCount++
	}

	if err := rows.Err(); err != nil {
		return errors.NewDatabaseError("error iterating device rows", err.Error()).WithCause(err)
	}

	m.logger.Info("Loaded devices from database", zap.Int("count", loadedCount))
	return nil
}
