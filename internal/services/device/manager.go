package device

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/services/adapter"
	"fcc-service/internal/storage"
	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// Manager 设备管理器实现
type Manager struct {
	database   storage.Database
	cache      storage.Cache
	adapterMgr *adapter.Manager
	logger     *zap.Logger

	// 内存设备注册表
	controllers map[string]*models.Controller
	devices     map[string]*models.Device
	mu          sync.RWMutex

	// 设备状态变更通道
	statusChan chan *DeviceStatusUpdate
}

// DeviceStatusUpdate 设备状态更新事件
type DeviceStatusUpdate struct {
	DeviceID  string              `json:"device_id"`
	OldStatus models.DeviceStatus `json:"old_status"`
	NewStatus models.DeviceStatus `json:"new_status"`
	OldHealth models.DeviceHealth `json:"old_health"`
	NewHealth models.DeviceHealth `json:"new_health"`
	Timestamp time.Time           `json:"timestamp"`
	EventType string              `json:"event_type"`
}

// NewManager 创建设备管理器
func NewManager(database storage.Database, cache storage.Cache, adapterMgr *adapter.Manager, logger *zap.Logger) api.DeviceManager {
	return &Manager{
		database:    database,
		cache:       cache,
		adapterMgr:  adapterMgr,
		logger:      logger,
		controllers: make(map[string]*models.Controller),
		devices:     make(map[string]*models.Device),
		statusChan:  make(chan *DeviceStatusUpdate, 100),
	}
}

// RegisterController 注册控制器
func (m *Manager) RegisterController(ctx context.Context, controller *models.Controller) error {
	if controller == nil {
		return errors.NewValidationError("controller cannot be nil")
	}

	if controller.ID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}

	m.logger.Info("Registering controller",
		zap.String("controller_id", controller.ID),
		zap.String("controller_type", string(controller.Type)),
		zap.String("address", controller.Address))

	// 验证控制器配置
	if err := m.validateController(controller); err != nil {
		return err
	}

	// 检查是否已存在
	m.mu.RLock()
	existing, exists := m.controllers[controller.ID]
	m.mu.RUnlock()

	if exists {
		m.logger.Warn("Controller already exists, updating",
			zap.String("controller_id", controller.ID),
			zap.String("existing_version", fmt.Sprintf("%d", existing.Version)))
	}

	// 设置时间戳
	now := time.Now()
	if controller.CreatedAt.IsZero() {
		controller.CreatedAt = now
	}
	controller.UpdatedAt = now
	controller.LastSeen = &now

	// 保存到数据库
	if err := m.saveControllerToDB(ctx, controller); err != nil {
		return fmt.Errorf("failed to save controller to database: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.controllers[controller.ID] = controller
	m.mu.Unlock()

	// 更新缓存
	cacheKey := fmt.Sprintf("controller:%s", controller.ID)
	if err := m.cache.Set(ctx, cacheKey, controller, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to cache controller", zap.String("controller_id", controller.ID), zap.Error(err))
	}

	m.logger.Info("Controller registered successfully", zap.String("controller_id", controller.ID))
	return nil
}

// UnregisterController 注销控制器
func (m *Manager) UnregisterController(ctx context.Context, controllerID string) error {
	if controllerID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}

	m.logger.Info("Unregistering controller", zap.String("controller_id", controllerID))

	// 检查控制器是否存在
	m.mu.RLock()
	_, exists := m.controllers[controllerID]
	m.mu.RUnlock()

	if !exists {
		return errors.NewDeviceNotFoundError(controllerID)
	}

	// 先注销该控制器下的所有设备
	deviceIDs := make([]string, 0)
	m.mu.RLock()
	for _, device := range m.devices {
		if device.ControllerID == controllerID {
			deviceIDs = append(deviceIDs, device.ID)
		}
	}
	m.mu.RUnlock()

	// 注销设备
	for _, deviceID := range deviceIDs {
		if err := m.UnregisterDevice(ctx, deviceID); err != nil {
			m.logger.Error("Failed to unregister device during controller cleanup",
				zap.String("device_id", deviceID),
				zap.String("controller_id", controllerID),
				zap.Error(err))
		}
	}

	// 从数据库删除
	if err := m.deleteControllerFromDB(ctx, controllerID); err != nil {
		return fmt.Errorf("failed to delete controller from database: %w", err)
	}

	// 从内存注册表移除
	m.mu.Lock()
	delete(m.controllers, controllerID)
	m.mu.Unlock()

	// 从缓存删除
	cacheKey := fmt.Sprintf("controller:%s", controllerID)
	if err := m.cache.Del(ctx, cacheKey); err != nil {
		m.logger.Warn("Failed to delete controller from cache", zap.String("controller_id", controllerID), zap.Error(err))
	}

	m.logger.Info("Controller unregistered successfully",
		zap.String("controller_id", controllerID),
		zap.Int("devices_unregistered", len(deviceIDs)))
	return nil
}

// RegisterDevice 注册设备
func (m *Manager) RegisterDevice(ctx context.Context, device *models.Device) error {
	if device == nil {
		return errors.NewValidationError("device cannot be nil")
	}

	if device.ID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	if device.ControllerID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}

	m.logger.Info("Registering device",
		zap.String("device_id", device.ID),
		zap.String("device_type", string(device.Type)),
		zap.String("controller_id", device.ControllerID))

	// 验证设备配置
	if err := m.validateDevice(device); err != nil {
		return err
	}

	// 验证控制器存在
	m.mu.RLock()
	controller, exists := m.controllers[device.ControllerID]
	m.mu.RUnlock()

	if !exists {
		return errors.NewValidationError(fmt.Sprintf("controller %s not found", device.ControllerID))
	}

	// 验证设备地址在控制器地址范围内
	if controller.IsDARTProtocol() {
		if !controller.IsAddressInRange(device.DeviceAddress) {
			return errors.NewValidationError(fmt.Sprintf("device address %d not in controller address range", device.DeviceAddress))
		}
	}

	// 检查是否已存在
	m.mu.RLock()
	existing, exists := m.devices[device.ID]
	m.mu.RUnlock()

	if exists {
		m.logger.Warn("Device already exists, updating",
			zap.String("device_id", device.ID),
			zap.String("existing_version", fmt.Sprintf("%d", existing.Version)))
	}

	// 设置时间戳
	now := time.Now()
	if device.CreatedAt.IsZero() {
		device.CreatedAt = now
	}
	device.UpdatedAt = now
	device.LastSeen = &now

	// 保存到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to save device to database: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.devices[device.ID] = device
	m.mu.Unlock()

	// 更新缓存
	cacheKey := fmt.Sprintf("device:%s", device.ID)
	if err := m.cache.Set(ctx, cacheKey, device, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to cache device", zap.String("device_id", device.ID), zap.Error(err))
	}

	// 更新控制器的设备列表缓存
	m.updateControllerDevicesCache(ctx, device.ControllerID)

	m.logger.Info("Device registered successfully", zap.String("device_id", device.ID))
	return nil
}

// UnregisterDevice 注销设备
func (m *Manager) UnregisterDevice(ctx context.Context, deviceID string) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Unregistering device", zap.String("device_id", deviceID))

	// 检查设备是否存在
	m.mu.RLock()
	device, exists := m.devices[deviceID]
	m.mu.RUnlock()

	if !exists {
		return errors.NewDeviceNotFoundError(deviceID)
	}

	controllerID := device.ControllerID

	// 从数据库删除
	if err := m.deleteDeviceFromDB(ctx, deviceID); err != nil {
		return fmt.Errorf("failed to delete device from database: %w", err)
	}

	// 从内存注册表移除
	m.mu.Lock()
	delete(m.devices, deviceID)
	m.mu.Unlock()

	// 从缓存删除
	cacheKey := fmt.Sprintf("device:%s", deviceID)
	if err := m.cache.Del(ctx, cacheKey); err != nil {
		m.logger.Warn("Failed to delete device from cache", zap.String("device_id", deviceID), zap.Error(err))
	}

	// 更新控制器的设备列表缓存
	m.updateControllerDevicesCache(ctx, controllerID)

	m.logger.Info("Device unregistered successfully", zap.String("device_id", deviceID))
	return nil
}

// GetController 获取控制器
func (m *Manager) GetController(ctx context.Context, controllerID string) (*models.Controller, error) {
	if controllerID == "" {
		return nil, errors.NewValidationError("controller ID cannot be empty")
	}

	// 先从内存查找
	m.mu.RLock()
	controller, exists := m.controllers[controllerID]
	m.mu.RUnlock()

	if exists {
		return controller, nil
	}

	// 从缓存查找
	cacheKey := fmt.Sprintf("controller:%s", controllerID)
	var cachedController models.Controller
	if err := m.cache.GetJSON(ctx, cacheKey, &cachedController); err == nil {
		// 更新内存注册表
		m.mu.Lock()
		m.controllers[controllerID] = &cachedController
		m.mu.Unlock()
		return &cachedController, nil
	}

	// 从数据库查找
	controller, err := m.loadControllerFromDB(ctx, controllerID)
	if err != nil {
		return nil, err
	}

	// 更新缓存和内存注册表
	m.mu.Lock()
	m.controllers[controllerID] = controller
	m.mu.Unlock()

	if err := m.cache.Set(ctx, cacheKey, controller, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to cache controller", zap.String("controller_id", controllerID), zap.Error(err))
	}

	return controller, nil
}

// GetDevice 获取设备
func (m *Manager) GetDevice(ctx context.Context, deviceID string) (*models.Device, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	// 先从内存查找
	m.mu.RLock()
	device, exists := m.devices[deviceID]
	m.mu.RUnlock()

	if exists {
		return device, nil
	}

	// 从缓存查找
	cacheKey := fmt.Sprintf("device:%s", deviceID)
	var cachedDevice models.Device
	if err := m.cache.GetJSON(ctx, cacheKey, &cachedDevice); err == nil {
		// 更新内存注册表
		m.mu.Lock()
		m.devices[deviceID] = &cachedDevice
		m.mu.Unlock()
		return &cachedDevice, nil
	}

	// 从数据库查找
	device, err := m.loadDeviceFromDB(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 更新缓存和内存注册表
	m.mu.Lock()
	m.devices[deviceID] = device
	m.mu.Unlock()

	if err := m.cache.Set(ctx, cacheKey, device, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to cache device", zap.String("device_id", deviceID), zap.Error(err))
	}

	return device, nil
}

// ListControllers 列出控制器
func (m *Manager) ListControllers(ctx context.Context, filter api.ControllerFilter) ([]*models.Controller, error) {
	// 查询控制器列表

	// 从内存获取所有控制器
	m.mu.RLock()
	allControllers := make([]*models.Controller, 0, len(m.controllers))
	for _, controller := range m.controllers {
		allControllers = append(allControllers, controller)
	}
	m.mu.RUnlock()

	// 应用过滤器
	filtered := m.filterControllers(allControllers, filter)

	// 应用分页
	start := filter.Offset
	if start > len(filtered) {
		start = len(filtered)
	}

	end := start + filter.Limit
	if filter.Limit == 0 || end > len(filtered) {
		end = len(filtered)
	}

	result := filtered[start:end]
	// 控制器列表查询完成

	return result, nil
}

// ListDevices 列出设备
func (m *Manager) ListDevices(ctx context.Context, filter api.DeviceFilter) ([]*models.Device, error) {
	// 查询设备列表

	// 从内存获取所有设备
	m.mu.RLock()
	allDevices := make([]*models.Device, 0, len(m.devices))
	for _, device := range m.devices {
		allDevices = append(allDevices, device)
	}
	m.mu.RUnlock()

	// 应用过滤器
	filtered := m.filterDevices(allDevices, filter)

	// 应用分页
	start := filter.Offset
	if start > len(filtered) {
		start = len(filtered)
	}

	end := start + filter.Limit
	if filter.Limit == 0 || end > len(filtered) {
		end = len(filtered)
	}

	result := filtered[start:end]
	// 设备列表查询完成

	return result, nil
}

// UpdateDeviceStatus 更新设备状态
func (m *Manager) UpdateDeviceStatus(ctx context.Context, deviceID string, status models.DeviceStatus) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Debug("Updating device status",
		zap.String("device_id", deviceID),
		zap.String("new_status", string(status)))

	// 获取当前设备
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return err
	}

	oldStatus := device.Status
	if oldStatus == status {
		// 状态未变化，无需更新
		return nil
	}

	// 更新设备状态
	device.Status = status
	device.UpdatedAt = time.Now()
	device.LastSeen = &device.UpdatedAt

	// 保存到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to update device status in database: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.devices[deviceID] = device
	m.mu.Unlock()

	// 更新缓存
	cacheKey := fmt.Sprintf("device:%s", deviceID)
	if err := m.cache.Set(ctx, cacheKey, device, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to update device cache", zap.String("device_id", deviceID), zap.Error(err))
	}

	// 发送状态变更事件
	select {
	case m.statusChan <- &DeviceStatusUpdate{
		DeviceID:  deviceID,
		OldStatus: oldStatus,
		NewStatus: status,
		OldHealth: device.Health,
		NewHealth: device.Health,
		Timestamp: device.UpdatedAt,
	}:
	default:
		m.logger.Warn("Status update channel full, dropping event", zap.String("device_id", deviceID))
	}

	m.logger.Info("Device status updated",
		zap.String("device_id", deviceID),
		zap.String("old_status", string(oldStatus)),
		zap.String("new_status", string(status)))

	return nil
}

// UpdateDeviceHealth 更新设备健康状态
func (m *Manager) UpdateDeviceHealth(ctx context.Context, deviceID string, health models.DeviceHealth) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Debug("Updating device health",
		zap.String("device_id", deviceID),
		zap.String("new_health", string(health)))

	// 获取当前设备
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return err
	}

	oldHealth := device.Health
	if oldHealth == health {
		// 健康状态未变化，无需更新
		return nil
	}

	// 更新设备健康状态
	device.Health = health
	device.UpdatedAt = time.Now()
	device.LastSeen = &device.UpdatedAt

	// 保存到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to update device health in database: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.devices[deviceID] = device
	m.mu.Unlock()

	// 更新缓存
	cacheKey := fmt.Sprintf("device:%s", deviceID)
	if err := m.cache.Set(ctx, cacheKey, device, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to update device cache", zap.String("device_id", deviceID), zap.Error(err))
	}

	// 发送状态变更事件
	select {
	case m.statusChan <- &DeviceStatusUpdate{
		DeviceID:  deviceID,
		OldStatus: device.Status,
		NewStatus: device.Status,
		OldHealth: oldHealth,
		NewHealth: health,
		Timestamp: device.UpdatedAt,
	}:
	default:
		m.logger.Warn("Status update channel full, dropping event", zap.String("device_id", deviceID))
	}

	m.logger.Info("Device health updated",
		zap.String("device_id", deviceID),
		zap.String("old_health", string(oldHealth)),
		zap.String("new_health", string(health)))

	return nil
}

// DiscoverDevices 发现设备
func (m *Manager) DiscoverDevices(ctx context.Context, controllerID string) ([]*models.Device, error) {
	if controllerID == "" {
		return nil, errors.NewValidationError("controller ID cannot be empty")
	}

	m.logger.Info("Discovering devices", zap.String("controller_id", controllerID))

	// 验证控制器存在
	controller, err := m.GetController(ctx, controllerID)
	if err != nil {
		return nil, err
	}

	// 使用适配器管理器进行设备发现
	discoveredDevices, err := m.adapterMgr.DiscoverDevices(ctx, controller)
	if err != nil {
		m.logger.Error("Device discovery failed",
			zap.String("controller_id", controllerID),
			zap.Error(err))
		return nil, fmt.Errorf("device discovery failed: %w", err)
	}

	// 注册发现的设备
	registeredDevices := make([]*models.Device, 0, len(discoveredDevices))
	for _, device := range discoveredDevices {
		// 检查设备是否已存在
		m.mu.RLock()
		existing, exists := m.devices[device.ID]
		m.mu.RUnlock()

		if exists {
			// 设备已存在，更新状态和时间戳
			existing.Status = device.Status
			existing.Health = device.Health
			existing.UpdatedAt = time.Now()
			existing.LastSeen = &existing.UpdatedAt

			// 保存到数据库
			if err := m.saveDeviceToDB(ctx, existing); err != nil {
				m.logger.Error("Failed to update existing device",
					zap.String("device_id", device.ID),
					zap.Error(err))
				continue
			}

			// 更新内存注册表
			m.mu.Lock()
			m.devices[device.ID] = existing
			m.mu.Unlock()

			registeredDevices = append(registeredDevices, existing)
		} else {
			// 新设备，注册到系统
			if err := m.RegisterDevice(ctx, device); err != nil {
				m.logger.Error("Failed to register discovered device",
					zap.String("device_id", device.ID),
					zap.Error(err))
				continue
			}

			registeredDevices = append(registeredDevices, device)
		}
	}

	m.logger.Info("Device discovery completed",
		zap.String("controller_id", controllerID),
		zap.String("controller_type", string(controller.Type)),
		zap.Int("devices_discovered", len(discoveredDevices)),
		zap.Int("devices_registered", len(registeredDevices)))

	return registeredDevices, nil
}

// RefreshDeviceTopology 刷新设备拓扑
func (m *Manager) RefreshDeviceTopology(ctx context.Context) error {
	m.logger.Info("Refreshing device topology")

	// 获取所有控制器
	m.mu.RLock()
	controllers := make([]*models.Controller, 0, len(m.controllers))
	for _, controller := range m.controllers {
		controllers = append(controllers, controller)
	}
	m.mu.RUnlock()

	// 对每个控制器执行设备发现
	var totalDevices int
	for _, controller := range controllers {
		devices, err := m.DiscoverDevices(ctx, controller.ID)
		if err != nil {
			m.logger.Error("Failed to discover devices for controller",
				zap.String("controller_id", controller.ID),
				zap.Error(err))
			continue
		}
		totalDevices += len(devices)
	}

	m.logger.Info("Device topology refresh completed",
		zap.Int("controllers", len(controllers)),
		zap.Int("total_devices", totalDevices))

	return nil
}

// GetStatusChannel 获取状态变更通道
func (m *Manager) GetStatusChannel() <-chan *DeviceStatusUpdate {
	return m.statusChan
}

// Close 关闭设备管理器
func (m *Manager) Close() error {
	m.logger.Info("Closing device manager")
	close(m.statusChan)
	return nil
}

// UpdateController 更新控制器
func (m *Manager) UpdateController(ctx context.Context, controller *models.Controller) error {
	if controller == nil {
		return errors.NewValidationError("controller cannot be nil")
	}

	if controller.ID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}

	m.logger.Info("Updating controller", zap.String("controller_id", controller.ID))

	// 检查控制器是否存在
	m.mu.RLock()
	existing, exists := m.controllers[controller.ID]
	m.mu.RUnlock()

	if !exists {
		return api.ErrControllerNotFound
	}

	// 更新时间戳和版本
	controller.UpdatedAt = time.Now()
	controller.Version = existing.Version + 1
	if controller.CreatedAt.IsZero() {
		controller.CreatedAt = existing.CreatedAt
	}

	// 保存到数据库
	if err := m.saveControllerToDB(ctx, controller); err != nil {
		return fmt.Errorf("failed to update controller in database: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.controllers[controller.ID] = controller
	m.mu.Unlock()

	// 更新缓存
	cacheKey := fmt.Sprintf("controller:%s", controller.ID)
	if err := m.cache.Set(ctx, cacheKey, controller, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to update controller cache", zap.String("controller_id", controller.ID), zap.Error(err))
	}

	m.logger.Info("Controller updated successfully", zap.String("controller_id", controller.ID))
	return nil
}

// UpdateDevice 更新设备
func (m *Manager) UpdateDevice(ctx context.Context, device *models.Device) error {
	if device == nil {
		return errors.NewValidationError("device cannot be nil")
	}

	if device.ID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Updating device", zap.String("device_id", device.ID))

	// 检查设备是否存在
	m.mu.RLock()
	existing, exists := m.devices[device.ID]
	m.mu.RUnlock()

	if !exists {
		return api.ErrDeviceNotFound
	}

	// 更新时间戳和版本
	device.UpdatedAt = time.Now()
	device.Version = existing.Version + 1
	if device.CreatedAt.IsZero() {
		device.CreatedAt = existing.CreatedAt
	}

	// 保存到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to update device in database: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.devices[device.ID] = device
	m.mu.Unlock()

	// 更新缓存
	cacheKey := fmt.Sprintf("device:%s", device.ID)
	if err := m.cache.Set(ctx, cacheKey, device, 30*time.Minute); err != nil {
		m.logger.Warn("Failed to update device cache", zap.String("device_id", device.ID), zap.Error(err))
	}

	m.logger.Info("Device updated successfully", zap.String("device_id", device.ID))
	return nil
}

// GetDeviceStatus 获取设备状态
func (m *Manager) GetDeviceStatus(ctx context.Context, deviceID string) (*api.DeviceStatusInfo, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	// 获取设备
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 构建状态信息
	statusInfo := &api.DeviceStatusInfo{
		DeviceID:   deviceID,
		DeviceType: device.Type,
		Status:     device.Status,
		Health:     device.Health,
		LastUpdate: device.UpdatedAt,
		LastSeen:   device.LastSeen,
		Properties: make(map[string]interface{}),
	}

	// 添加设备特有属性
	if device.Metadata != nil {
		statusInfo.Properties = device.Metadata
	}

	return statusInfo, nil
}

// GetControllerStatus 获取控制器状态
func (m *Manager) GetControllerStatus(ctx context.Context, controllerID string) (*api.DeviceStatusInfo, error) {
	if controllerID == "" {
		return nil, errors.NewValidationError("controller ID cannot be empty")
	}

	// 获取控制器
	controller, err := m.GetController(ctx, controllerID)
	if err != nil {
		return nil, err
	}

	// 构建状态信息
	statusInfo := &api.DeviceStatusInfo{
		DeviceID:   controllerID,
		DeviceType: controller.Type,
		Status:     controller.Status,
		Health:     controller.Health,
		LastUpdate: controller.UpdatedAt,
		LastSeen:   controller.LastSeen,
		Properties: make(map[string]interface{}),
	}

	// 添加控制器特有属性
	if controller.Metadata != nil {
		statusInfo.Properties = controller.Metadata
	}

	return statusInfo, nil
}

// AddDeviceToController 添加设备到控制器
func (m *Manager) AddDeviceToController(ctx context.Context, controllerID, deviceID string) error {
	if controllerID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Adding device to controller",
		zap.String("controller_id", controllerID),
		zap.String("device_id", deviceID))

	// 检查控制器是否存在
	m.mu.RLock()
	_, controllerExists := m.controllers[controllerID]
	device, deviceExists := m.devices[deviceID]
	m.mu.RUnlock()

	if !controllerExists {
		return api.ErrControllerNotFound
	}

	if !deviceExists {
		return api.ErrDeviceNotFound
	}

	// 更新设备的控制器ID
	device.ControllerID = controllerID
	device.UpdatedAt = time.Now()

	// 保存到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to update device controller assignment: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.devices[deviceID] = device
	m.mu.Unlock()

	m.logger.Info("Device added to controller successfully",
		zap.String("controller_id", controllerID),
		zap.String("device_id", deviceID))

	return nil
}

// RemoveDeviceFromController 从控制器移除设备
func (m *Manager) RemoveDeviceFromController(ctx context.Context, controllerID, deviceID string) error {
	if controllerID == "" {
		return errors.NewValidationError("controller ID cannot be empty")
	}
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Removing device from controller",
		zap.String("controller_id", controllerID),
		zap.String("device_id", deviceID))

	// 检查设备是否存在且属于该控制器
	m.mu.RLock()
	device, exists := m.devices[deviceID]
	m.mu.RUnlock()

	if !exists {
		return api.ErrDeviceNotFound
	}

	if device.ControllerID != controllerID {
		return errors.NewValidationError("device does not belong to the specified controller")
	}

	// 清空设备的控制器ID
	device.ControllerID = ""
	device.UpdatedAt = time.Now()

	// 保存到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to remove device from controller: %w", err)
	}

	// 更新内存注册表
	m.mu.Lock()
	m.devices[deviceID] = device
	m.mu.Unlock()

	m.logger.Info("Device removed from controller successfully",
		zap.String("controller_id", controllerID),
		zap.String("device_id", deviceID))

	return nil
}

// SendCommand 发送设备命令
func (m *Manager) SendCommand(ctx context.Context, command *models.Command) (*api.CommandResult, error) {
	if command == nil {
		return nil, errors.NewValidationError("command cannot be nil")
	}

	if command.DeviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Sending command to device",
		zap.String("device_id", command.DeviceID),
		zap.String("command_type", string(command.Type)))

	// 检查设备是否存在
	device, err := m.GetDevice(ctx, command.DeviceID)
	if err != nil {
		return nil, err
	}

	// 获取控制器信息
	controller, err := m.GetController(ctx, device.ControllerID)
	if err != nil {
		m.logger.Error("Failed to get controller for device",
			zap.String("device_id", command.DeviceID),
			zap.String("controller_id", device.ControllerID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get controller: %w", err)
	}

	// 修复：真正调用适配器执行命令
	// 构造API命令对象
	apiCommand := api.Command{
		Type:       string(command.Type),
		Parameters: command.Parameters.ExtraParams,
		Timeout:    time.Duration(command.TimeoutSecs) * time.Second,
	}

	// 如果没有设置超时，使用默认值
	if apiCommand.Timeout == 0 {
		apiCommand.Timeout = 30 * time.Second
	}

	// 通过适配器管理器执行命令
	result, err := m.adapterMgr.ExecuteCommand(ctx, controller, command.DeviceID, apiCommand)
	if err != nil {
		m.logger.Error("Failed to execute command via adapter",
			zap.String("device_id", command.DeviceID),
			zap.String("command_type", string(command.Type)),
			zap.Error(err))

		// 返回失败结果而不是错误，保持API一致性
		return &api.CommandResult{
			Success: false,
			Data: map[string]interface{}{
				"error":        err.Error(),
				"device_id":    device.ID,
				"command_type": string(command.Type),
			},
			Timestamp: time.Now(),
		}, nil
	}

	// 记录成功执行
	m.logger.Info("Command executed successfully",
		zap.String("device_id", command.DeviceID),
		zap.String("command_type", string(command.Type)),
		zap.Bool("success", result.Success))

	// 对于特定命令类型，可能需要更新设备状态
	switch command.Type {
	case models.CommandTypeAuthorize:
		// 授权命令成功后，可能需要更新设备状态
		if result.Success {
			m.logger.Debug("Authorize command succeeded, device ready for operation",
				zap.String("device_id", command.DeviceID))
		}
	case models.CommandTypeStop:
		// 停止命令成功后，可能需要更新设备状态
		if result.Success {
			m.logger.Debug("Stop command succeeded, device operation stopped",
				zap.String("device_id", command.DeviceID))
		}
	}

	return result, nil
}

// ListWayneDevices 列出Wayne设备
func (m *Manager) ListWayneDevices(ctx context.Context) ([]*models.Device, error) {
	m.logger.Info("Listing Wayne devices")

	// 使用过滤器查询Wayne设备
	filter := api.DeviceFilter{
		DeviceType: models.DeviceTypeDARTPump, // 假设Wayne设备是DART泵
	}

	devices, err := m.ListDevices(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list Wayne devices: %w", err)
	}

	m.logger.Info("Wayne devices listed successfully", zap.Int("count", len(devices)))
	return devices, nil
}

// GetWayneDevice 获取Wayne设备
func (m *Manager) GetWayneDevice(ctx context.Context, deviceID string) (*models.Device, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Getting Wayne device", zap.String("device_id", deviceID))

	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 验证是否是Wayne设备
	if device.Type != models.DeviceTypeDARTPump {
		return nil, errors.NewValidationError("device is not a Wayne device")
	}

	return device, nil
}

// GetPumpStatus 获取泵状态
func (m *Manager) GetPumpStatus(ctx context.Context, deviceID string) (interface{}, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Getting pump status", zap.String("device_id", deviceID))

	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 尝试从适配器获取真实状态
	adapter, err := m.adapterMgr.GetAdapter(device.ControllerID)
	if err != nil {
		m.logger.Warn("Failed to get adapter for pump status, using cached data",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	var status map[string]interface{}

	if adapter != nil && adapter.IsConnected() {
		// 尝试获取真实设备状态
		deviceStatus, err := adapter.GetDeviceStatus(ctx, deviceID)
		if err != nil {
			m.logger.Warn("Failed to get real device status, using cached data",
				zap.String("device_id", deviceID),
				zap.Error(err))
		} else {
			// 使用真实设备状态
			status = map[string]interface{}{
				"device_id":    deviceID,
				"pump_status":  string(deviceStatus.Status),
				"health":       string(deviceStatus.Health),
				"last_updated": time.Now(),
				"data_source":  "real_device",
				"adapter_type": adapter.GetProtocolInfo().Name,
			}

			m.logger.Debug("Retrieved real pump status",
				zap.String("device_id", deviceID),
				zap.String("status", string(deviceStatus.Status)))

			return status, nil
		}
	}

	// 回退到缓存或模拟数据
	status = map[string]interface{}{
		"device_id":         deviceID,
		"pump_status":       string(device.Status),
		"health":            string(device.Health),
		"last_updated":      time.Now(),
		"data_source":       "cached",
		"adapter_available": adapter != nil,
		"adapter_connected": adapter != nil && adapter.IsConnected(),
	}

	// 从设备配置中获取额外信息
	if device.IsPump() {
		pumpConfig := device.GetPumpConfig()
		if pumpConfig != nil {
			// 添加价格信息
			if len(pumpConfig.PriceConfig) > 0 {
				prices := make(map[string]interface{})
				for _, priceConfig := range pumpConfig.PriceConfig {
					price, _ := priceConfig.Price.Float64()
					prices[priceConfig.FuelType] = price
				}
				status["prices"] = prices
				status["price_count"] = len(pumpConfig.PriceConfig)
			}
		}
	}

	m.logger.Debug("Using cached pump status",
		zap.String("device_id", deviceID),
		zap.String("status", string(device.Status)))

	return status, nil
}

// GetPumpTotals 获取泵累计数据 - 通过DART协议
func (m *Manager) GetPumpTotals(ctx context.Context, deviceID string) (interface{}, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Getting pump totals via DART protocol", zap.String("device_id", deviceID))

	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 1. 优先通过适配器发送协议命令获取实时数据
	adapter, err := m.adapterMgr.GetAdapter(device.ControllerID)
	if err != nil {
		m.logger.Warn("Failed to get adapter, falling back to cache",
			zap.String("device_id", deviceID), zap.Error(err))
		return m.getTotalsFromCache(ctx, deviceID, device)
	}

	if !adapter.IsConnected() {
		m.logger.Warn("Adapter not connected, falling back to cache",
			zap.String("device_id", deviceID))
		return m.getTotalsFromCache(ctx, deviceID, device)
	}

	// 2. 发送Request Total Counters命令 (CD101)
	cmd := &models.Command{
		ID:       fmt.Sprintf("totals_%s_%d", deviceID, time.Now().UnixNano()),
		Type:     models.CommandTypeRequestTotalCounters,
		DeviceID: deviceID,
		Parameters: models.CommandParameters{
			ExtraParams: map[string]interface{}{
				"counter_type": "all", // 请求所有计数器 (体积+金额+次数)
			},
		},
		TimeoutSecs: 5, // DART协议要求25ms响应，设置5秒超时
		MaxRetries:  2,
	}

	// 3. 通过适配器发送命令
	apiCommand := api.Command{
		Type:       string(cmd.Type),
		Parameters: cmd.Parameters.ExtraParams,
		Timeout:    time.Duration(cmd.TimeoutSecs) * time.Second,
	}

	response, err := adapter.ExecuteCommand(ctx, deviceID, apiCommand)
	if err != nil {
		m.logger.Warn("Failed to get totals via protocol, falling back to cache",
			zap.String("device_id", deviceID), zap.Error(err))
		return m.getTotalsFromCache(ctx, deviceID, device)
	}

	if !response.Success {
		m.logger.Warn("Protocol command failed, falling back to cache",
			zap.String("device_id", deviceID),
			zap.String("error", response.Error))
		return m.getTotalsFromCache(ctx, deviceID, device)
	}

	// 4. 解析协议响应
	totals := response.Data
	if totals == nil {
		m.logger.Warn("Empty response data, falling back to cache",
			zap.String("device_id", deviceID))
		return m.getTotalsFromCache(ctx, deviceID, device)
	}

	// 5. 异步更新缓存
	go m.updateTotalsCache(ctx, deviceID, totals)

	m.logger.Info("Successfully retrieved pump totals via protocol",
		zap.String("device_id", deviceID),
		zap.String("data_source", "dart_protocol"))

	return totals, nil
}

// getTotalsFromCache 从缓存或数据库获取总计数据作为降级方案
func (m *Manager) getTotalsFromCache(ctx context.Context, deviceID string, device *models.Device) (interface{}, error) {
	// 创建基础返回数据
	totals := map[string]interface{}{
		"device_id":   deviceID,
		"device_name": device.Name,
		"device_type": string(device.Type),
		"timestamp":   time.Now(),
	}

	// 1. 先尝试Redis缓存
	cacheKey := fmt.Sprintf("pump_totals:%s", deviceID)
	if m.cache != nil {
		var cachedTotals map[string]interface{}
		if err := m.cache.GetJSON(ctx, cacheKey, &cachedTotals); err == nil {
			cachedTotals["data_source"] = "redis_cache"
			m.logger.Debug("Retrieved totals from Redis cache", zap.String("device_id", deviceID))
			return cachedTotals, nil
		}
	}

	// 2. 再尝试数据库
	if m.database != nil {
		row := m.database.QueryRow(ctx,
			"SELECT volume_total, amount_total, fill_count, last_reset, updated_at FROM pump_totals WHERE device_id = $1",
			deviceID)

		var volumeTotal, amountTotal float64
		var fillCount int
		var lastReset, updatedAt time.Time

		if err := row.Scan(&volumeTotal, &amountTotal, &fillCount, &lastReset, &updatedAt); err == nil {
			totals["grand_total_volume"] = volumeTotal
			totals["grand_total_amount"] = amountTotal
			totals["fill_count"] = fillCount
			totals["last_reset"] = lastReset
			totals["updated_at"] = updatedAt
			totals["data_source"] = "database_cache"

			m.logger.Debug("Retrieved totals from database", zap.String("device_id", deviceID))
			return totals, nil
		}
	}

	// 3. 最后返回默认值
	totals["grand_total_volume"] = 0.0
	totals["grand_total_amount"] = 0.0
	totals["fill_count"] = 0
	totals["last_reset"] = time.Now().Add(-24 * time.Hour)
	totals["data_source"] = "default"
	totals["error"] = "no_data_available"

	// 添加设备基本信息
	totals["device_status"] = string(device.Status)
	totals["device_health"] = string(device.Health)

	m.logger.Warn("Using default totals data", zap.String("device_id", deviceID))
	return totals, nil
}

// updateTotalsCache 异步更新总计数据缓存
func (m *Manager) updateTotalsCache(ctx context.Context, deviceID string, totals map[string]interface{}) {
	// 更新Redis缓存
	if m.cache != nil {
		cacheKey := fmt.Sprintf("pump_totals:%s", deviceID)
		if err := m.cache.Set(ctx, cacheKey, totals, time.Hour); err != nil {
			m.logger.Warn("Failed to update totals cache",
				zap.String("device_id", deviceID), zap.Error(err))
		}
	}

	// 更新PostgreSQL持久化
	if m.database != nil {
		volumeTotal, _ := totals["grand_total_volume"].(float64)
		amountTotal, _ := totals["grand_total_amount"].(float64)
		fillCount, _ := totals["fill_count"].(int)

		_, err := m.database.Exec(ctx,
			"INSERT INTO pump_totals (device_id, volume_total, amount_total, fill_count, updated_at) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (device_id) DO UPDATE SET volume_total = $2, amount_total = $3, fill_count = $4, updated_at = $5",
			deviceID, volumeTotal, amountTotal, fillCount, time.Now())

		if err != nil {
			m.logger.Warn("Failed to update totals in database",
				zap.String("device_id", deviceID), zap.Error(err))
		}
	}

	m.logger.Debug("Updated totals cache successfully", zap.String("device_id", deviceID))
}

// HandleDeviceRestart 处理设备重启事件
func (m *Manager) HandleDeviceRestart(ctx context.Context, deviceID string) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	m.logger.Info("Handling device restart event", zap.String("device_id", deviceID))

	// 获取设备信息
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return fmt.Errorf("failed to get device for restart handling: %w", err)
	}

	// 记录设备重启事件
	m.logger.Warn("Device restart detected",
		zap.String("device_id", deviceID),
		zap.String("device_type", string(device.Type)),
		zap.Time("restart_time", time.Now()))

	// 1. 标记设备重启状态
	oldStatus := device.Status
	device.Status = models.DeviceStatusOnline // 重启后设为在线
	device.UpdatedAt = time.Now()
	device.LastSeen = &device.UpdatedAt

	// 2. 清除设备缓存状态
	if err := m.clearDeviceCache(ctx, deviceID); err != nil {
		m.logger.Warn("Failed to clear device cache after restart",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 3. 生成设备恢复命令序列
	recoveryCommands, err := m.CreateDeviceRecoverySequence(ctx, deviceID)
	if err != nil {
		m.logger.Error("Failed to create device recovery sequence",
			zap.String("device_id", deviceID),
			zap.Error(err))
		// 不阻塞主流程，继续处理
	} else {
		m.logger.Info("Created device recovery sequence",
			zap.String("device_id", deviceID),
			zap.Int("command_count", len(recoveryCommands)))
	}

	// 4. 保存设备状态到数据库
	if err := m.saveDeviceToDB(ctx, device); err != nil {
		return fmt.Errorf("failed to save device restart status: %w", err)
	}

	// 5. 更新内存状态
	m.mu.Lock()
	m.devices[deviceID] = device
	m.mu.Unlock()

	// 6. 发布设备重启事件
	select {
	case m.statusChan <- &DeviceStatusUpdate{
		DeviceID:  deviceID,
		OldStatus: oldStatus,
		NewStatus: device.Status,
		OldHealth: device.Health,
		NewHealth: device.Health,
		Timestamp: device.UpdatedAt,
		EventType: "device_restart",
	}:
	default:
		m.logger.Warn("Status update channel full, dropping restart event",
			zap.String("device_id", deviceID))
	}

	m.logger.Info("Device restart handled successfully",
		zap.String("device_id", deviceID),
		zap.String("old_status", string(oldStatus)),
		zap.String("new_status", string(device.Status)))

	return nil
}

// CreateDeviceRecoverySequence 创建设备恢复命令序列
func (m *Manager) CreateDeviceRecoverySequence(ctx context.Context, deviceID string) ([]*models.Command, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %w", err)
	}

	var commands []*models.Command

	// 1. CD1 - 状态查询命令
	statusCommand := &models.Command{
		ID:       fmt.Sprintf("%s_recovery_status_%d", deviceID, time.Now().Unix()),
		DeviceID: deviceID,
		Type:     models.CommandTypeStatus,
		Parameters: models.CommandParameters{
			DeviceAddress: device.DeviceAddress,
			ExtraParams: map[string]interface{}{
				"command_code": "CD1",
				"operation":    "status_query",
			},
		},
		Priority:  models.PriorityHigh,
		Status:    models.CommandStatusPending,
		CreatedAt: time.Now(),
	}
	commands = append(commands, statusCommand)

	// 2. CD5 - 价格设置命令（如果设备是泵）
	if device.IsPump() {
		priceCommand, err := m.createPriceSyncCommand(ctx, device)
		if err != nil {
			m.logger.Warn("Failed to create price sync command",
				zap.String("device_id", deviceID),
				zap.Error(err))
		} else {
			commands = append(commands, priceCommand)
		}
	}

	// 3. CD9 - 设备配置同步命令
	configCommand := &models.Command{
		ID:       fmt.Sprintf("%s_recovery_config_%d", deviceID, time.Now().Unix()),
		DeviceID: deviceID,
		Type:     models.CommandTypeConfig,
		Parameters: models.CommandParameters{
			DeviceAddress: device.DeviceAddress,
			ExtraParams: map[string]interface{}{
				"command_code": "CD9",
				"operation":    "sync_configuration",
			},
		},
		Priority:  models.PriorityNormal,
		Status:    models.CommandStatusPending,
		CreatedAt: time.Now(),
	}
	commands = append(commands, configCommand)

	return commands, nil
}

// ValidateDeviceConnection 验证设备连接状态
func (m *Manager) ValidateDeviceConnection(ctx context.Context, deviceID string) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	// 检查设备是否在线
	if device.Status != models.DeviceStatusOnline {
		return fmt.Errorf("device is not online, current status: %s", device.Status)
	}

	// 检查最后心跳时间
	if device.LastSeen != nil {
		timeSinceLastSeen := time.Since(*device.LastSeen)
		if timeSinceLastSeen > 5*time.Minute { // 5分钟无心跳认为连接异常
			return fmt.Errorf("device connection timeout, last seen: %v ago", timeSinceLastSeen)
		}
	}

	// 检查设备健康状态
	if device.Health == models.DeviceHealthCritical {
		return fmt.Errorf("device health is critical")
	}

	return nil
}

// clearDeviceCache 清除设备相关的缓存数据
func (m *Manager) clearDeviceCache(ctx context.Context, deviceID string) error {
	if m.cache == nil {
		return nil // 如果没有缓存，直接返回
	}

	// 清除设备基本信息缓存
	deviceCacheKey := fmt.Sprintf("device:%s", deviceID)
	if err := m.cache.Del(ctx, deviceCacheKey); err != nil {
		return fmt.Errorf("failed to clear device cache: %w", err)
	}

	// 清除设备状态缓存
	statusCacheKey := fmt.Sprintf("device_status:%s", deviceID)
	if err := m.cache.Del(ctx, statusCacheKey); err != nil {
		return fmt.Errorf("failed to clear device status cache: %w", err)
	}

	// 清除设备统计数据缓存
	statsCacheKey := fmt.Sprintf("device_stats:%s", deviceID)
	if err := m.cache.Del(ctx, statsCacheKey); err != nil {
		return fmt.Errorf("failed to clear device stats cache: %w", err)
	}

	return nil
}

// createPriceSyncCommand 创建价格同步命令
func (m *Manager) createPriceSyncCommand(ctx context.Context, device *models.Device) (*models.Command, error) {
	// 获取系统价格
	systemPrice, err := m.GetSystemPrice(ctx, device.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get system price: %w", err)
	}

	return &models.Command{
		ID:       fmt.Sprintf("%s_recovery_price_%d", device.ID, time.Now().Unix()),
		DeviceID: device.ID,
		Type:     models.CommandTypeSetPrice,
		Parameters: models.CommandParameters{
			DeviceAddress: device.DeviceAddress,
			ExtraParams: map[string]interface{}{
				"command_code": "CD5",
				"operation":    "set_price",
				"price":        systemPrice,
			},
		},
		Priority:  models.PriorityHigh,
		Status:    models.CommandStatusPending,
		CreatedAt: time.Now(),
	}, nil
}

// GetSystemPrice 获取系统价格
func (m *Manager) GetSystemPrice(ctx context.Context, deviceID string) (float64, error) {
	// 这里应该从配置或数据库获取系统价格
	// 暂时返回默认价格，后续可以完善
	return 1.259, nil // 默认价格 1.259 元/升
}

// ValidateDevicePrice 验证设备价格与系统价格的一致性
func (m *Manager) ValidateDevicePrice(ctx context.Context, deviceID string, devicePrice float64) error {
	if deviceID == "" {
		return errors.NewValidationError("device ID cannot be empty")
	}

	if devicePrice < 0 {
		return errors.NewValidationError("device price cannot be negative")
	}

	m.logger.Debug("Validating device price",
		zap.String("device_id", deviceID),
		zap.Float64("device_price", devicePrice))

	// 获取设备信息
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return fmt.Errorf("failed to get device for price validation: %w", err)
	}

	// 从设备配置中获取系统价格
	var systemPrice float64
	var priceFound bool

	if device.GetPumpConfig() != nil {
		for _, priceConfig := range device.GetPumpConfig().PriceConfig {
			price, _ := priceConfig.Price.Float64()
			systemPrice = price
			priceFound = true
			break // 暂时使用第一个价格配置
		}
	}

	if !priceFound {
		m.logger.Warn("No system price configured for device",
			zap.String("device_id", deviceID))
		return errors.NewValidationError("no system price configured for device")
	}

	// 价格比较（允许0.01的误差）
	priceDiff := math.Abs(devicePrice - systemPrice)
	tolerance := 0.01

	if priceDiff > tolerance {
		m.logger.Warn("Device price mismatch detected",
			zap.String("device_id", deviceID),
			zap.Float64("device_price", devicePrice),
			zap.Float64("system_price", systemPrice),
			zap.Float64("difference", priceDiff))

		// 记录价格不一致事件
		if err := m.recordPriceMismatchEvent(ctx, deviceID, devicePrice, systemPrice); err != nil {
			m.logger.Error("Failed to record price mismatch event", zap.Error(err))
		}

		return fmt.Errorf("device price %.3f does not match system price %.3f (difference: %.3f)",
			devicePrice, systemPrice, priceDiff)
	}

	m.logger.Debug("Device price validation passed",
		zap.String("device_id", deviceID),
		zap.Float64("price", devicePrice))

	return nil
}

// recordPriceMismatchEvent 记录价格不一致事件
func (m *Manager) recordPriceMismatchEvent(ctx context.Context, deviceID string, devicePrice, systemPrice float64) error {
	// TODO: 实现价格不一致事件记录到数据库
	// 这里可以记录到审计日志或事件表

	m.logger.Info("Recording price mismatch event",
		zap.String("device_id", deviceID),
		zap.Float64("device_price", devicePrice),
		zap.Float64("system_price", systemPrice),
		zap.Time("timestamp", time.Now()))

	return nil
}

// CreatePriceSyncCommand 创建价格同步命令
func (m *Manager) CreatePriceSyncCommand(ctx context.Context, deviceID string, targetPrice float64) (*models.Command, error) {
	if deviceID == "" {
		return nil, errors.NewValidationError("device ID cannot be empty")
	}

	if targetPrice < 0 {
		return nil, errors.NewValidationError("target price cannot be negative")
	}

	// 获取设备信息
	device, err := m.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device for price sync: %w", err)
	}

	m.logger.Info("Creating price sync command",
		zap.String("device_id", deviceID),
		zap.Float64("target_price", targetPrice))

	// 创建CD5价格设置命令
	command := &models.Command{
		ID:       fmt.Sprintf("%s_price_sync_%d", deviceID, time.Now().Unix()),
		DeviceID: deviceID,
		Type:     models.CommandTypeSetPrice,
		Parameters: models.CommandParameters{
			DeviceAddress: device.DeviceAddress,
			ExtraParams: map[string]interface{}{
				"command_code": "CD5",
				"operation":    "set_price",
				"price":        targetPrice,
				"fuel_grade":   1, // 默认油品等级
			},
		},
		Priority:  models.PriorityHigh,
		Status:    models.CommandStatusPending,
		CreatedAt: time.Now(),
	}

	return command, nil
}
