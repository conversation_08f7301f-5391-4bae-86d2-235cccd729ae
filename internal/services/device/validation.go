package device

import (
	"fmt"

	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// validateController 验证控制器配置
func (m *Manager) validateController(controller *models.Controller) error {
	// 验证基本字段
	if controller.Name == "" {
		return errors.NewValidationError("controller name cannot be empty")
	}

	if controller.Type == "" {
		return errors.NewValidationError("controller type cannot be empty")
	}

	if controller.Protocol == "" {
		return errors.NewValidationError("controller protocol cannot be empty")
	}

	if controller.Address == "" {
		return errors.NewValidationError("controller address cannot be empty")
	}

	if controller.StationID == "" {
		return errors.NewValidationError("station ID cannot be empty")
	}

	// 验证协议特定配置
	switch controller.Protocol {
	case models.ProtocolTypeDART:
		return m.validateDARTController(controller)
	case models.ProtocolTypeTCP:
		return m.validateTCPController(controller)
	case models.ProtocolTypeUDP:
		return m.validateUDPController(controller)
	case models.ProtocolTypeSerial:
		return m.validateSerialController(controller)
	case models.ProtocolTypeModbus:
		return m.validateModbusController(controller)
	default:
		return errors.NewValidationError(fmt.Sprintf("unsupported protocol type: %s", controller.Protocol))
	}
}

// validateDARTController 验证DART协议控制器
func (m *Manager) validateDARTController(controller *models.Controller) error {
	// 验证地址范围
	if controller.Config.AddressRange == nil {
		return errors.NewValidationError("DART controller must have address range configured")
	}

	addressRange := controller.Config.AddressRange
	if addressRange.Min < 0x50 || addressRange.Max > 0x6F {
		return errors.NewValidationError("DART device addresses must be in range 0x50-0x6F")
	}

	if addressRange.Min > addressRange.Max {
		return errors.NewValidationError("address range min cannot be greater than max")
	}

	// 验证串口配置
	if controller.Config.SerialPort == "" {
		return errors.NewValidationError("DART controller must have serial port configured")
	}

	if controller.Config.BaudRate != 9600 && controller.Config.BaudRate != 19200 {
		return errors.NewValidationError("DART protocol only supports 9600 or 19200 baud rate")
	}

	// 验证超时配置
	if controller.Config.Timeout == 0 {
		controller.Config.Timeout = 25 // 默认25ms
	}

	if controller.Config.Timeout > 25 {
		return errors.NewValidationError("DART protocol response timeout must not exceed 25ms")
	}

	return nil
}

// validateTCPController 验证TCP协议控制器
func (m *Manager) validateTCPController(controller *models.Controller) error {
	if controller.Config.Host == "" {
		return errors.NewValidationError("TCP controller must have host configured")
	}

	if controller.Config.Port <= 0 || controller.Config.Port > 65535 {
		return errors.NewValidationError("TCP controller must have valid port configured")
	}

	// 设置默认超时
	if controller.Config.Timeout == 0 {
		controller.Config.Timeout = 5000 // 默认5秒
	}

	return nil
}

// validateUDPController 验证UDP协议控制器
func (m *Manager) validateUDPController(controller *models.Controller) error {
	if controller.Config.Host == "" {
		return errors.NewValidationError("UDP controller must have host configured")
	}

	if controller.Config.Port <= 0 || controller.Config.Port > 65535 {
		return errors.NewValidationError("UDP controller must have valid port configured")
	}

	// 设置默认超时
	if controller.Config.Timeout == 0 {
		controller.Config.Timeout = 3000 // 默认3秒
	}

	return nil
}

// validateSerialController 验证串口协议控制器
func (m *Manager) validateSerialController(controller *models.Controller) error {
	if controller.Config.SerialPort == "" {
		return errors.NewValidationError("Serial controller must have serial port configured")
	}

	if controller.Config.BaudRate <= 0 {
		return errors.NewValidationError("Serial controller must have valid baud rate configured")
	}

	// 验证数据位
	if controller.Config.DataBits < 5 || controller.Config.DataBits > 8 {
		return errors.NewValidationError("Serial controller data bits must be between 5 and 8")
	}

	// 验证停止位
	if controller.Config.StopBits < 1 || controller.Config.StopBits > 2 {
		return errors.NewValidationError("Serial controller stop bits must be 1 or 2")
	}

	// 验证校验位
	if controller.Config.Parity != "" {
		switch controller.Config.Parity {
		case "none", "odd", "even", "mark", "space":
			// 有效的校验位设置
		default:
			return errors.NewValidationError("Invalid parity setting")
		}
	}

	return nil
}

// validateModbusController 验证Modbus协议控制器
func (m *Manager) validateModbusController(controller *models.Controller) error {
	// Modbus可以基于TCP或串口
	if controller.Config.Host != "" {
		// Modbus TCP
		return m.validateTCPController(controller)
	} else if controller.Config.SerialPort != "" {
		// Modbus RTU
		return m.validateSerialController(controller)
	} else {
		return errors.NewValidationError("Modbus controller must have either TCP or serial configuration")
	}
}

// validateDevice 验证设备配置
func (m *Manager) validateDevice(device *models.Device) error {
	// 验证基本字段
	if device.Name == "" {
		return errors.NewValidationError("device name cannot be empty")
	}

	if device.Type == "" {
		return errors.NewValidationError("device type cannot be empty")
	}

	if device.StationID == "" {
		return errors.NewValidationError("station ID cannot be empty")
	}

	// 验证设备地址
	if device.DeviceAddress < 0 {
		return errors.NewValidationError("device address cannot be negative")
	}

	// 验证设备类型特定配置
	switch device.Type {
	case models.DeviceTypeFuelPump, models.DeviceTypeDARTPump:
		return m.validatePumpDevice(device)
	case models.DeviceTypeATG, models.DeviceTypeDARTTank:
		return m.validateATGDevice(device)
	case models.DeviceTypeDisplay, models.DeviceTypeDARTDisplay:
		return m.validateDisplayDevice(device)
	case models.DeviceTypeFuelNozzle:
		return m.validateNozzleDevice(device)
	case models.DeviceTypePOS:
		return m.validatePOSDevice(device)
	case models.DeviceTypeEDC:
		return m.validateEDCDevice(device)
	default:
		// 未知设备类型，进行基本验证
		return nil
	}
}

// validatePumpDevice 验证泵设备
func (m *Manager) validatePumpDevice(device *models.Device) error {
	pumpConfig := device.GetPumpConfig()
	if pumpConfig == nil {
		return errors.NewValidationError("pump device must have pump configuration")
	}

	if pumpConfig.NozzleCount <= 0 {
		return errors.NewValidationError("pump must have at least one nozzle")
	}

	if len(pumpConfig.SupportedFuels) == 0 {
		return errors.NewValidationError("pump must support at least one fuel type")
	}

	if models.IsDecimalZero(pumpConfig.MaxFlowRate) {
		return errors.NewValidationError("pump must have maximum flow rate configured")
	}

	if pumpConfig.Precision < 0 || pumpConfig.Precision > 6 {
		return errors.NewValidationError("pump precision must be between 0 and 6")
	}

	return nil
}

// validateATGDevice 验证ATG设备
func (m *Manager) validateATGDevice(device *models.Device) error {
	atgConfig := device.GetATGConfig()
	if atgConfig == nil {
		return errors.NewValidationError("ATG device must have ATG configuration")
	}

	if atgConfig.TankCount <= 0 {
		return errors.NewValidationError("ATG must monitor at least one tank")
	}

	if len(atgConfig.TankMappings) == 0 {
		return errors.NewValidationError("ATG must have tank mappings configured")
	}

	// 验证油罐映射
	for tankID, mapping := range atgConfig.TankMappings {
		if mapping.TankID == "" {
			return errors.NewValidationError(fmt.Sprintf("tank mapping %s must have tank ID", tankID))
		}

		if mapping.FuelType == "" {
			return errors.NewValidationError(fmt.Sprintf("tank mapping %s must have fuel type", tankID))
		}

		if models.IsDecimalZero(mapping.Capacity) {
			return errors.NewValidationError(fmt.Sprintf("tank mapping %s must have capacity", tankID))
		}
	}

	return nil
}

// validateDisplayDevice 验证显示设备
func (m *Manager) validateDisplayDevice(device *models.Device) error {
	// 显示设备的基本验证已在validateDevice中完成
	// 这里可以添加显示设备特有的验证逻辑
	return nil
}

// validateNozzleDevice 验证油枪设备
func (m *Manager) validateNozzleDevice(device *models.Device) error {
	// 油枪设备通常关联到泵，这里可以验证关联关系
	return nil
}

// validatePOSDevice 验证POS设备
func (m *Manager) validatePOSDevice(device *models.Device) error {
	// POS设备的验证逻辑
	return nil
}

// validateEDCDevice 验证EDC设备
func (m *Manager) validateEDCDevice(device *models.Device) error {
	// EDC设备的验证逻辑
	return nil
}

// filterControllers 过滤控制器列表
func (m *Manager) filterControllers(controllers []*models.Controller, filter api.ControllerFilter) []*models.Controller {
	var filtered []*models.Controller

	for _, controller := range controllers {
		// 应用过滤条件
		if filter.StationID != "" && controller.StationID != filter.StationID {
			continue
		}

		// 控制器使用ControllerType字段存储控制器类型
		if filter.ControllerType != "" && controller.Type != filter.ControllerType {
			continue
		}

		if filter.Status != "" && controller.Status != filter.Status {
			continue
		}

		if filter.Health != "" && controller.Health != filter.Health {
			continue
		}

		if filter.Protocol != "" && controller.Protocol != filter.Protocol {
			continue
		}

		filtered = append(filtered, controller)
	}

	return filtered
}

// filterDevices 过滤设备列表
func (m *Manager) filterDevices(devices []*models.Device, filter api.DeviceFilter) []*models.Device {
	var filtered []*models.Device

	for _, device := range devices {
		// 应用过滤条件
		if filter.StationID != "" && device.StationID != filter.StationID {
			continue
		}

		if filter.DeviceType != "" && device.Type != filter.DeviceType {
			continue
		}

		if filter.Status != "" && device.Status != filter.Status {
			continue
		}

		if filter.Health != "" && device.Health != filter.Health {
			continue
		}

		if filter.ControllerID != "" && device.ControllerID != filter.ControllerID {
			continue
		}

		filtered = append(filtered, device)
	}

	return filtered
}
