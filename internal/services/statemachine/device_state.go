package statemachine

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DeviceState 设备状态枚举
type DeviceState string

const (
	// 基础状态
	DeviceStateUnknown      DeviceState = "unknown"      // 未知状态
	DeviceStateInitializing DeviceState = "initializing" // 初始化中
	DeviceStateOnline       DeviceState = "online"       // 在线
	DeviceStateOffline      DeviceState = "offline"      // 离线
	DeviceStateError        DeviceState = "error"        // 错误状态
	DeviceStateMaintenance  DeviceState = "maintenance"  // 维护状态

	// 泵特定状态
	DeviceStateIdle        DeviceState = "idle"        // 空闲
	DeviceStateDispensing  DeviceState = "dispensing"  // 加油中
	DeviceStateAuthorizing DeviceState = "authorizing" // 授权中
	DeviceStateCompleting  DeviceState = "completing"  // 完成中
	DeviceStateFaulted     DeviceState = "faulted"     // 故障状态
)

// DeviceEvent 设备事件枚举
type DeviceEvent string

const (
	// 连接事件
	EventConnect    DeviceEvent = "connect"    // 连接
	EventDisconnect DeviceEvent = "disconnect" // 断开连接
	EventTimeout    DeviceEvent = "timeout"    // 超时
	EventReconnect  DeviceEvent = "reconnect"  // 重连

	// 业务事件
	EventStartTransaction    DeviceEvent = "start_transaction"    // 开始交易
	EventAuthorize           DeviceEvent = "authorize"            // 授权
	EventStartDispensing     DeviceEvent = "start_dispensing"     // 开始加油
	EventStopDispensing      DeviceEvent = "stop_dispensing"      // 停止加油
	EventCompleteTransaction DeviceEvent = "complete_transaction" // 完成交易

	// 错误事件
	EventError       DeviceEvent = "error"       // 错误
	EventFault       DeviceEvent = "fault"       // 故障
	EventRecover     DeviceEvent = "recover"     // 恢复
	EventMaintenance DeviceEvent = "maintenance" // 维护

	// 系统事件
	EventInitialize DeviceEvent = "initialize" // 初始化
	EventShutdown   DeviceEvent = "shutdown"   // 关闭
)

// StateTransition 状态转换定义
type StateTransition struct {
	From   DeviceState `json:"from"`   // 源状态
	Event  DeviceEvent `json:"event"`  // 触发事件
	To     DeviceState `json:"to"`     // 目标状态
	Guard  string      `json:"guard"`  // 守卫条件（可选）
	Action string      `json:"action"` // 转换动作（可选）
}

// StateContext 状态上下文
type StateContext struct {
	DeviceID          string                 `json:"device_id"`          // 设备ID
	CurrentState      DeviceState            `json:"current_state"`      // 当前状态
	PreviousState     DeviceState            `json:"previous_state"`     // 前一状态
	LastTransition    time.Time              `json:"last_transition"`    // 最后转换时间
	StateData         map[string]interface{} `json:"state_data"`         // 状态数据
	TransitionHistory []TransitionRecord     `json:"transition_history"` // 转换历史
}

// TransitionRecord 转换记录
type TransitionRecord struct {
	From      DeviceState   `json:"from"`      // 源状态
	To        DeviceState   `json:"to"`        // 目标状态
	Event     DeviceEvent   `json:"event"`     // 触发事件
	Timestamp time.Time     `json:"timestamp"` // 转换时间
	Success   bool          `json:"success"`   // 是否成功
	Error     string        `json:"error"`     // 错误信息
	Duration  time.Duration `json:"duration"`  // 转换耗时
}

// DeviceStateMachine 设备状态机
type DeviceStateMachine struct {
	deviceID     string
	currentState DeviceState
	transitions  map[string][]StateTransition // key: from_state, value: possible transitions
	context      *StateContext
	guards       map[string]GuardFunc
	actions      map[string]ActionFunc
	listeners    []StateChangeListener
	logger       *zap.Logger
	mu           sync.RWMutex
}

// GuardFunc 守卫函数类型
type GuardFunc func(ctx context.Context, stateCtx *StateContext, event DeviceEvent) bool

// ActionFunc 动作函数类型
type ActionFunc func(ctx context.Context, stateCtx *StateContext, event DeviceEvent) error

// StateChangeListener 状态变更监听器
type StateChangeListener interface {
	OnStateChange(deviceID string, from, to DeviceState, event DeviceEvent) error
}

// NewDeviceStateMachine 创建设备状态机
func NewDeviceStateMachine(deviceID string, logger *zap.Logger) *DeviceStateMachine {
	sm := &DeviceStateMachine{
		deviceID:     deviceID,
		currentState: DeviceStateUnknown,
		transitions:  make(map[string][]StateTransition),
		context: &StateContext{
			DeviceID:          deviceID,
			CurrentState:      DeviceStateUnknown,
			PreviousState:     DeviceStateUnknown,
			LastTransition:    time.Now(),
			StateData:         make(map[string]interface{}),
			TransitionHistory: make([]TransitionRecord, 0),
		},
		guards:    make(map[string]GuardFunc),
		actions:   make(map[string]ActionFunc),
		listeners: make([]StateChangeListener, 0),
		logger:    logger,
	}

	// 初始化默认状态转换规则
	sm.initializeTransitions()

	return sm
}

// initializeTransitions 初始化状态转换规则
func (sm *DeviceStateMachine) initializeTransitions() {
	// ✅ 基础连接状态转换 - 更宽松的规则
	sm.addTransition(DeviceStateUnknown, EventInitialize, DeviceStateInitializing)
	sm.addTransition(DeviceStateInitializing, EventConnect, DeviceStateOnline)
	sm.addTransition(DeviceStateInitializing, EventError, DeviceStateError)

	// ✅ 修复：允许从 initializing 直接进入 online（轮询成功）
	sm.addTransition(DeviceStateInitializing, EventRecover, DeviceStateOnline)
	sm.addTransition(DeviceStateInitializing, EventConnect, DeviceStateOnline)

	// ✅ 修复：允许 error 状态回到 initializing
	sm.addTransition(DeviceStateError, EventInitialize, DeviceStateInitializing)
	sm.addTransition(DeviceStateError, EventConnect, DeviceStateInitializing)

	sm.addTransition(DeviceStateOnline, EventDisconnect, DeviceStateOffline)
	sm.addTransition(DeviceStateOffline, EventReconnect, DeviceStateOnline)
	sm.addTransition(DeviceStateOffline, EventTimeout, DeviceStateError)

	// ✅ 业务状态转换（泵设备）- 增加更多转换路径
	sm.addTransition(DeviceStateOnline, EventStartTransaction, DeviceStateAuthorizing)
	sm.addTransition(DeviceStateIdle, EventStartTransaction, DeviceStateAuthorizing)
	sm.addTransition(DeviceStateAuthorizing, EventAuthorize, DeviceStateIdle)
	sm.addTransition(DeviceStateIdle, EventStartDispensing, DeviceStateDispensing)
	sm.addTransition(DeviceStateDispensing, EventStopDispensing, DeviceStateCompleting)
	sm.addTransition(DeviceStateCompleting, EventCompleteTransaction, DeviceStateIdle)

	// ✅ 修复：允许从 online 直接进入 idle（设备就绪）
	sm.addTransition(DeviceStateOnline, EventRecover, DeviceStateIdle)
	sm.addTransition(DeviceStateInitializing, EventRecover, DeviceStateIdle)

	// ✅ 修复：允许从任何状态恢复到在线状态
	sm.addTransition(DeviceStateUnknown, EventConnect, DeviceStateOnline)
	sm.addTransition(DeviceStateUnknown, EventRecover, DeviceStateInitializing)

	// ✅ 错误处理状态转换 - 更宽松
	sm.addTransition(DeviceStateOnline, EventError, DeviceStateError)
	sm.addTransition(DeviceStateIdle, EventFault, DeviceStateFaulted)
	sm.addTransition(DeviceStateDispensing, EventFault, DeviceStateFaulted)
	sm.addTransition(DeviceStateError, EventRecover, DeviceStateOnline)
	sm.addTransition(DeviceStateFaulted, EventRecover, DeviceStateIdle)

	// ✅ 修复：允许任何状态的轮询恢复
	sm.addTransition(DeviceStateError, EventConnect, DeviceStateOnline)
	sm.addTransition(DeviceStateFaulted, EventConnect, DeviceStateOnline)

	// 维护状态转换
	sm.addTransition(DeviceStateOnline, EventMaintenance, DeviceStateMaintenance)
	sm.addTransition(DeviceStateIdle, EventMaintenance, DeviceStateMaintenance)
	sm.addTransition(DeviceStateMaintenance, EventRecover, DeviceStateOnline)
}

// addTransition 添加状态转换规则
func (sm *DeviceStateMachine) addTransition(from DeviceState, event DeviceEvent, to DeviceState) {
	transition := StateTransition{
		From:  from,
		Event: event,
		To:    to,
	}

	key := string(from)
	sm.transitions[key] = append(sm.transitions[key], transition)
}

// AddTransitionWithGuard 添加带守卫的状态转换
func (sm *DeviceStateMachine) AddTransitionWithGuard(from DeviceState, event DeviceEvent, to DeviceState, guard string) {
	transition := StateTransition{
		From:  from,
		Event: event,
		To:    to,
		Guard: guard,
	}

	key := string(from)
	sm.transitions[key] = append(sm.transitions[key], transition)
}

// AddTransitionWithAction 添加带动作的状态转换
func (sm *DeviceStateMachine) AddTransitionWithAction(from DeviceState, event DeviceEvent, to DeviceState, action string) {
	transition := StateTransition{
		From:   from,
		Event:  event,
		To:     to,
		Action: action,
	}

	key := string(from)
	sm.transitions[key] = append(sm.transitions[key], transition)
}

// RegisterGuard 注册守卫函数
func (sm *DeviceStateMachine) RegisterGuard(name string, guard GuardFunc) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.guards[name] = guard
}

// RegisterAction 注册动作函数
func (sm *DeviceStateMachine) RegisterAction(name string, action ActionFunc) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.actions[name] = action
}

// AddStateChangeListener 添加状态变更监听器
func (sm *DeviceStateMachine) AddStateChangeListener(listener StateChangeListener) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.listeners = append(sm.listeners, listener)
}

// GetCurrentState 获取当前状态
func (sm *DeviceStateMachine) GetCurrentState() DeviceState {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.currentState
}

// GetStateContext 获取状态上下文
func (sm *DeviceStateMachine) GetStateContext() *StateContext {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	// 返回副本
	contextCopy := *sm.context
	contextCopy.StateData = make(map[string]interface{})
	for k, v := range sm.context.StateData {
		contextCopy.StateData[k] = v
	}

	return &contextCopy
}

// SetStateData 设置状态数据
func (sm *DeviceStateMachine) SetStateData(key string, value interface{}) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.context.StateData[key] = value
}

// GetStateData 获取状态数据
func (sm *DeviceStateMachine) GetStateData(key string) (interface{}, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	value, exists := sm.context.StateData[key]
	return value, exists
}

// SendEvent 发送事件触发状态转换
func (sm *DeviceStateMachine) SendEvent(ctx context.Context, event DeviceEvent) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	startTime := time.Now()

	sm.logger.Debug("Processing state machine event",
		zap.String("device_id", sm.deviceID),
		zap.String("current_state", string(sm.currentState)),
		zap.String("event", string(event)))

	// 查找可能的状态转换
	possibleTransitions := sm.transitions[string(sm.currentState)]
	var targetTransition *StateTransition

	for _, transition := range possibleTransitions {
		if transition.Event == event {
			// 检查守卫条件
			if transition.Guard != "" {
				if guard, exists := sm.guards[transition.Guard]; exists {
					if !guard(ctx, sm.context, event) {
						sm.logger.Debug("Guard condition failed",
							zap.String("guard", transition.Guard),
							zap.String("event", string(event)))
						continue
					}
				}
			}
			targetTransition = &transition
			break
		}
	}

	// 如果没有找到有效转换
	if targetTransition == nil {
		err := fmt.Errorf("no valid transition for event %s from state %s", event, sm.currentState)
		sm.recordTransition(sm.currentState, sm.currentState, event, false, err.Error(), time.Since(startTime))
		sm.logger.Warn("Invalid state transition",
			zap.String("device_id", sm.deviceID),
			zap.String("current_state", string(sm.currentState)),
			zap.String("event", string(event)),
			zap.Error(err))
		return err
	}

	// 执行状态转换
	return sm.executeTransition(ctx, *targetTransition, event, startTime)
}

// executeTransition 执行状态转换
func (sm *DeviceStateMachine) executeTransition(ctx context.Context, transition StateTransition, event DeviceEvent, startTime time.Time) error {
	oldState := sm.currentState
	newState := transition.To

	sm.logger.Info("执行设备状态转换",
		zap.String("设备ID", sm.deviceID),
		zap.String("从状态", string(oldState)),
		zap.String("到状态", string(newState)),
		zap.String("触发事件", string(event)))

	// 执行转换动作（如果有）
	if transition.Action != "" {
		if action, exists := sm.actions[transition.Action]; exists {
			sm.logger.Debug("执行状态转换动作",
				zap.String("设备ID", sm.deviceID),
				zap.String("动作名称", transition.Action))

			if err := action(ctx, sm.context, event); err != nil {
				sm.logger.Error("状态转换动作执行失败",
					zap.String("设备ID", sm.deviceID),
					zap.String("动作名称", transition.Action),
					zap.Error(err))
				sm.recordTransition(oldState, oldState, event, false, err.Error(), time.Since(startTime))
				return fmt.Errorf("转换动作失败: %w", err)
			}
		}
	}

	// 更新状态
	sm.context.PreviousState = sm.currentState
	sm.context.CurrentState = newState
	sm.context.LastTransition = time.Now()
	sm.currentState = newState

	// 记录成功转换
	sm.recordTransition(oldState, newState, event, true, "", time.Since(startTime))

	// 通知监听器
	sm.notifyListeners(oldState, newState, event)

	sm.logger.Info("设备状态转换完成",
		zap.String("设备ID", sm.deviceID),
		zap.String("从状态", string(oldState)),
		zap.String("到状态", string(newState)),
		zap.Duration("转换耗时", time.Since(startTime)))

	return nil
}

// recordTransition 记录状态转换
func (sm *DeviceStateMachine) recordTransition(from, to DeviceState, event DeviceEvent, success bool, errorMsg string, duration time.Duration) {
	record := TransitionRecord{
		From:      from,
		To:        to,
		Event:     event,
		Timestamp: time.Now(),
		Success:   success,
		Error:     errorMsg,
		Duration:  duration,
	}

	sm.context.TransitionHistory = append(sm.context.TransitionHistory, record)

	// 限制历史记录数量（最多保留100条）
	if len(sm.context.TransitionHistory) > 100 {
		sm.context.TransitionHistory = sm.context.TransitionHistory[1:]
	}
}

// notifyListeners 通知状态变更监听器
func (sm *DeviceStateMachine) notifyListeners(from, to DeviceState, event DeviceEvent) {
	for _, listener := range sm.listeners {
		go func(l StateChangeListener) {
			if err := l.OnStateChange(sm.deviceID, from, to, event); err != nil {
				sm.logger.Error("State change listener failed",
					zap.String("device_id", sm.deviceID),
					zap.Error(err))
			}
		}(listener)
	}
}

// CanTransition 检查是否可以进行某个状态转换
func (sm *DeviceStateMachine) CanTransition(event DeviceEvent) bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	possibleTransitions := sm.transitions[string(sm.currentState)]
	for _, transition := range possibleTransitions {
		if transition.Event == event {
			return true
		}
	}
	return false
}

// GetPossibleEvents 获取当前状态下可能的事件
func (sm *DeviceStateMachine) GetPossibleEvents() []DeviceEvent {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	var events []DeviceEvent
	possibleTransitions := sm.transitions[string(sm.currentState)]

	for _, transition := range possibleTransitions {
		events = append(events, transition.Event)
	}

	return events
}

// GetTransitionHistory 获取转换历史
func (sm *DeviceStateMachine) GetTransitionHistory(limit int) []TransitionRecord {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	history := sm.context.TransitionHistory
	if limit > 0 && len(history) > limit {
		return history[len(history)-limit:]
	}

	// 返回副本
	result := make([]TransitionRecord, len(history))
	copy(result, history)
	return result
}

// Reset 重置状态机到初始状态
func (sm *DeviceStateMachine) Reset() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.currentState = DeviceStateUnknown
	sm.context = &StateContext{
		DeviceID:          sm.deviceID,
		CurrentState:      DeviceStateUnknown,
		PreviousState:     DeviceStateUnknown,
		LastTransition:    time.Now(),
		StateData:         make(map[string]interface{}),
		TransitionHistory: make([]TransitionRecord, 0),
	}

	sm.logger.Info("State machine reset",
		zap.String("device_id", sm.deviceID))
}

// GetStatistics 获取状态机统计信息
func (sm *DeviceStateMachine) GetStatistics() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	totalTransitions := len(sm.context.TransitionHistory)
	successfulTransitions := 0

	for _, record := range sm.context.TransitionHistory {
		if record.Success {
			successfulTransitions++
		}
	}

	var avgDuration time.Duration
	if totalTransitions > 0 {
		var totalDuration time.Duration
		for _, record := range sm.context.TransitionHistory {
			totalDuration += record.Duration
		}
		avgDuration = totalDuration / time.Duration(totalTransitions)
	}

	return map[string]interface{}{
		"device_id":              sm.deviceID,
		"current_state":          sm.currentState,
		"previous_state":         sm.context.PreviousState,
		"last_transition":        sm.context.LastTransition,
		"total_transitions":      totalTransitions,
		"successful_transitions": successfulTransitions,
		"success_rate":           float64(successfulTransitions) / float64(totalTransitions),
		"average_duration":       avgDuration,
		"uptime":                 time.Since(sm.context.LastTransition),
	}
}
