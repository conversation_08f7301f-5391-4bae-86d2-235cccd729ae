package statemachine

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/services/polling"
	"fcc-service/pkg/models"
)

// DeviceManagerInterface 设备管理器接口（用于状态同步）
type DeviceManagerInterface interface {
	UpdateDeviceStatus(ctx context.Context, deviceID string, status models.DeviceStatus) error
	UpdateDeviceHealth(ctx context.Context, deviceID string, health models.DeviceHealth) error
}

// DatabaseStateListener 数据库状态同步监听器
type DatabaseStateListener struct {
	deviceManager DeviceManagerInterface
	logger        *zap.Logger
}

// OnStateChange 状态变更回调，同步到数据库
func (dsl *DatabaseStateListener) OnStateChange(deviceID string, from, to DeviceState, event DeviceEvent) error {
	if dsl.deviceManager == nil {
		dsl.logger.Warn("设备管理器未配置，跳过数据库状态同步",
			zap.String("设备ID", deviceID))
		return nil
	}

	dsl.logger.Info("同步设备状态到数据库",
		zap.String("设备ID", deviceID),
		zap.String("从状态", string(from)),
		zap.String("到状态", string(to)),
		zap.String("触发事件", string(event)))

	ctx := context.Background()

	// 将状态机状态映射到设备状态
	var deviceStatus models.DeviceStatus
	var deviceHealth models.DeviceHealth

	switch to {
	case DeviceStateOnline, DeviceStateIdle:
		deviceStatus = models.DeviceStatusOnline
		deviceHealth = models.DeviceHealthGood
	case DeviceStateOffline:
		deviceStatus = models.DeviceStatusOffline
		deviceHealth = models.DeviceHealthUnknown
	case DeviceStateError, DeviceStateFaulted:
		deviceStatus = models.DeviceStatusOffline
		deviceHealth = models.DeviceHealthError
	case DeviceStateInitializing:
		deviceStatus = models.DeviceStatusOnline // 初始化中认为在线
		deviceHealth = models.DeviceHealthGood
	case DeviceStateMaintenance:
		deviceStatus = models.DeviceStatusMaintenance
		deviceHealth = models.DeviceHealthGood
	default:
		deviceStatus = models.DeviceStatusOffline
		deviceHealth = models.DeviceHealthUnknown
	}

	// 更新设备状态
	if err := dsl.deviceManager.UpdateDeviceStatus(ctx, deviceID, deviceStatus); err != nil {
		dsl.logger.Error("更新设备状态到数据库失败",
			zap.String("设备ID", deviceID),
			zap.String("状态", string(deviceStatus)),
			zap.Error(err))
		return err
	}

	// 更新设备健康状态
	if err := dsl.deviceManager.UpdateDeviceHealth(ctx, deviceID, deviceHealth); err != nil {
		dsl.logger.Error("更新设备健康状态到数据库失败",
			zap.String("设备ID", deviceID),
			zap.String("健康状态", string(deviceHealth)),
			zap.Error(err))
		return err
	}

	dsl.logger.Info("设备状态已同步到数据库",
		zap.String("设备ID", deviceID),
		zap.String("设备状态", string(deviceStatus)),
		zap.String("健康状态", string(deviceHealth)))

	return nil
}

// PollingIntegration 轮询系统与状态机的集成器
type PollingIntegration struct {
	businessEngine *BusinessEngine
	pollScheduler  *polling.PollScheduler
	deviceManager  DeviceManagerInterface
	logger         *zap.Logger

	// 轮询状态跟踪
	devicePollStates map[string]*DevicePollState
	pollMutex        sync.RWMutex

	// 配置
	config *PollingIntegrationConfig

	// 事件处理
	stopEventListener chan struct{}
	eventWG           sync.WaitGroup
}

// PollingIntegrationConfig 轮询集成配置
type PollingIntegrationConfig struct {
	MaxConsecutiveFailures int           `json:"max_consecutive_failures"` // 最大连续失败次数
	FailureThreshold       int           `json:"failure_threshold"`        // 故障阈值
	RecoveryCheckInterval  time.Duration `json:"recovery_check_interval"`  // 恢复检查间隔
	StateTransitionDelay   time.Duration `json:"state_transition_delay"`   // 状态转换延迟
	EnableHealthCheck      bool          `json:"enable_health_check"`      // 启用健康检查
}

// DevicePollState 设备轮询状态
type DevicePollState struct {
	DeviceID            string          `json:"device_id"`
	LastPollTime        time.Time       `json:"last_poll_time"`
	LastSuccessTime     time.Time       `json:"last_success_time"`
	ConsecutiveFailures int             `json:"consecutive_failures"`
	TotalPolls          int64           `json:"total_polls"`
	SuccessfulPolls     int64           `json:"successful_polls"`
	FailedPolls         int64           `json:"failed_polls"`
	LastError           string          `json:"last_error"`
	CurrentState        DeviceState     `json:"current_state"`
	HealthStatus        string          `json:"health_status"`      // healthy, warning, critical
	ResponseTimes       []time.Duration `json:"response_times"`     // 最近的响应时间
	LastResponseTime    time.Duration   `json:"last_response_time"` // 最后响应时间
}

// NewPollingIntegration 创建轮询集成器
func NewPollingIntegration(
	businessEngine *BusinessEngine,
	pollScheduler *polling.PollScheduler,
	deviceManager DeviceManagerInterface,
	config *PollingIntegrationConfig,
	logger *zap.Logger,
) *PollingIntegration {
	if config == nil {
		config = &PollingIntegrationConfig{
			MaxConsecutiveFailures: 3,
			FailureThreshold:       5,
			RecoveryCheckInterval:  30 * time.Second,
			StateTransitionDelay:   100 * time.Millisecond,
			EnableHealthCheck:      true,
		}
	}

	integration := &PollingIntegration{
		businessEngine:    businessEngine,
		pollScheduler:     pollScheduler,
		deviceManager:     deviceManager,
		logger:            logger,
		devicePollStates:  make(map[string]*DevicePollState),
		config:            config,
		stopEventListener: make(chan struct{}),
	}

	// 注册轮询事件监听器
	integration.registerPollingListeners()

	return integration
}

// StartIntegration 启动集成服务
func (pi *PollingIntegration) StartIntegration(ctx context.Context) error {
	pi.logger.Info("Starting polling integration service")

	// 启动轮询事件监听器
	pi.eventWG.Add(1)
	go pi.pollEventListener(ctx)

	// 启动健康检查（如果启用）
	if pi.config.EnableHealthCheck {
		go pi.healthCheckRoutine(ctx)
	}

	return nil
}

// StopIntegration 停止集成服务
func (pi *PollingIntegration) StopIntegration() error {
	pi.logger.Info("Stopping polling integration service")

	// 停止事件监听器
	close(pi.stopEventListener)
	pi.eventWG.Wait()

	return nil
}

// RegisterDevice 注册设备到轮询集成
func (pi *PollingIntegration) RegisterDevice(device *models.Device) error {
	pi.pollMutex.Lock()
	defer pi.pollMutex.Unlock()

	// 创建设备轮询状态
	pollState := &DevicePollState{
		DeviceID:        device.ID,
		LastPollTime:    time.Time{},
		LastSuccessTime: time.Time{},
		CurrentState:    DeviceStateUnknown,
		HealthStatus:    "unknown",
		ResponseTimes:   make([]time.Duration, 0, 100), // 保留最近100次响应时间
	}

	pi.devicePollStates[device.ID] = pollState

	// 注册设备到业务引擎
	if err := pi.businessEngine.RegisterDevice(device); err != nil {
		return fmt.Errorf("failed to register device to business engine: %w", err)
	}

	// 添加数据库状态同步监听器
	if pi.deviceManager != nil {
		stateMachine, err := pi.businessEngine.GetDeviceStateMachine(device.ID)
		if err != nil {
			pi.logger.Error("获取设备状态机失败，无法添加数据库同步监听器",
				zap.String("设备ID", device.ID),
				zap.Error(err))
		} else {
			// 创建数据库状态监听器
			dbListener := &DatabaseStateListener{
				deviceManager: pi.deviceManager,
				logger:        pi.logger,
			}

			// 添加监听器到状态机
			stateMachine.AddStateChangeListener(dbListener)

			pi.logger.Info("数据库状态同步监听器已添加",
				zap.String("设备ID", device.ID))

			// 确保初始状态同步：同步状态机的当前状态到数据库
			currentState := stateMachine.GetCurrentState()
			pi.logger.Info("执行设备注册时的初始状态同步",
				zap.String("设备ID", device.ID),
				zap.String("状态机状态", string(currentState)),
				zap.String("数据库状态", string(device.Status)))

			// 将状态机状态映射到设备状态并同步到数据库
			var deviceStatus models.DeviceStatus
			var deviceHealth models.DeviceHealth

			switch currentState {
			case DeviceStateOnline, DeviceStateIdle:
				deviceStatus = models.DeviceStatusOnline
				deviceHealth = models.DeviceHealthGood
			case DeviceStateOffline:
				deviceStatus = models.DeviceStatusOffline
				deviceHealth = models.DeviceHealthUnknown
			case DeviceStateError, DeviceStateFaulted:
				deviceStatus = models.DeviceStatusOffline
				deviceHealth = models.DeviceHealthError
			case DeviceStateInitializing:
				deviceStatus = models.DeviceStatusOnline // 初始化中认为在线
				deviceHealth = models.DeviceHealthGood
			case DeviceStateMaintenance:
				deviceStatus = models.DeviceStatusMaintenance
				deviceHealth = models.DeviceHealthGood
			default:
				deviceStatus = models.DeviceStatusOffline
				deviceHealth = models.DeviceHealthUnknown
			}

			// 更新设备状态到数据库
			ctx := context.Background()
			if err := pi.deviceManager.UpdateDeviceStatus(ctx, device.ID, deviceStatus); err != nil {
				pi.logger.Error("设备注册时同步状态到数据库失败",
					zap.String("设备ID", device.ID),
					zap.String("目标状态", string(deviceStatus)),
					zap.Error(err))
			} else {
				pi.logger.Info("设备注册时状态已同步到数据库",
					zap.String("设备ID", device.ID),
					zap.String("同步状态", string(deviceStatus)))
			}

			// 更新设备健康状态到数据库
			if err := pi.deviceManager.UpdateDeviceHealth(ctx, device.ID, deviceHealth); err != nil {
				pi.logger.Error("设备注册时同步健康状态到数据库失败",
					zap.String("设备ID", device.ID),
					zap.String("目标健康状态", string(deviceHealth)),
					zap.Error(err))
			}
		}
	} else {
		pi.logger.Warn("设备管理器未配置，无法启用数据库状态同步",
			zap.String("设备ID", device.ID))
	}

	// 发送设备连接事件
	event := &BusinessEvent{
		DeviceID:  device.ID,
		EventType: "device_connected",
		Data: map[string]interface{}{
			"device_type": string(device.Type),
			"device_name": device.Name,
		},
		Source: "polling_integration",
	}

	if err := pi.businessEngine.SendEvent(event); err != nil {
		pi.logger.Error("Failed to send device connected event",
			zap.String("device_id", device.ID),
			zap.Error(err))
	}

	pi.logger.Info("Device registered to polling integration",
		zap.String("device_id", device.ID),
		zap.String("device_type", string(device.Type)))

	return nil
}

// UnregisterDevice 从轮询集成注销设备
func (pi *PollingIntegration) UnregisterDevice(deviceID string) error {
	pi.pollMutex.Lock()
	defer pi.pollMutex.Unlock()

	delete(pi.devicePollStates, deviceID)

	// 从业务引擎注销设备
	if err := pi.businessEngine.UnregisterDevice(deviceID); err != nil {
		return fmt.Errorf("failed to unregister device from business engine: %w", err)
	}

	pi.logger.Info("Device unregistered from polling integration",
		zap.String("device_id", deviceID))

	return nil
}

// GetDevicePollState 获取设备轮询状态
func (pi *PollingIntegration) GetDevicePollState(deviceID string) (*DevicePollState, error) {
	pi.pollMutex.RLock()
	defer pi.pollMutex.RUnlock()

	pollState, exists := pi.devicePollStates[deviceID]
	if !exists {
		return nil, fmt.Errorf("device poll state not found: %s", deviceID)
	}

	// 返回副本
	stateCopy := *pollState
	stateCopy.ResponseTimes = make([]time.Duration, len(pollState.ResponseTimes))
	copy(stateCopy.ResponseTimes, pollState.ResponseTimes)

	return &stateCopy, nil
}

// GetAllDevicePollStates 获取所有设备轮询状态
func (pi *PollingIntegration) GetAllDevicePollStates() map[string]*DevicePollState {
	pi.pollMutex.RLock()
	defer pi.pollMutex.RUnlock()

	states := make(map[string]*DevicePollState)
	for deviceID, pollState := range pi.devicePollStates {
		// 创建副本
		stateCopy := *pollState
		stateCopy.ResponseTimes = make([]time.Duration, len(pollState.ResponseTimes))
		copy(stateCopy.ResponseTimes, pollState.ResponseTimes)
		states[deviceID] = &stateCopy
	}

	return states
}

// registerPollingListeners 注册轮询事件监听器
func (pi *PollingIntegration) registerPollingListeners() {
	// 添加轮询成功处理的业务规则
	pi.businessEngine.AddBusinessRule(BusinessRule{
		Name:        "polling_success_handler",
		Description: "Handle successful polling results",
		Priority:    10,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "poll_success"
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return pi.handlePollSuccess(ctx, event, stateMachine)
		},
	})

	// 添加轮询失败处理的业务规则
	pi.businessEngine.AddBusinessRule(BusinessRule{
		Name:        "polling_failure_handler",
		Description: "Handle polling failures",
		Priority:    15,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "poll_failure"
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return pi.handlePollFailure(ctx, event, stateMachine)
		},
	})

	// 添加设备恢复处理的业务规则
	pi.businessEngine.AddBusinessRule(BusinessRule{
		Name:        "device_recovery_handler",
		Description: "Handle device recovery after failures",
		Priority:    20,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "device_recovery" &&
				(deviceState == DeviceStateError || deviceState == DeviceStateFaulted)
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return pi.handleDeviceRecovery(ctx, event, stateMachine)
		},
	})
}

// handlePollSuccess 处理轮询成功
func (pi *PollingIntegration) handlePollSuccess(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
	deviceID := event.DeviceID

	// 更新轮询状态
	pi.updatePollState(deviceID, true, "", event.Data)

	// 根据当前状态进行状态转换
	currentState := stateMachine.GetCurrentState()

	pi.logger.Info("处理轮询成功事件",
		zap.String("设备ID", deviceID),
		zap.String("当前状态", string(currentState)))

	switch currentState {
	case DeviceStateUnknown:
		// 修复：未知状态下轮询成功，初始化并连接到在线状态
		pi.logger.Info("设备从未知状态转为初始化", zap.String("设备ID", deviceID))
		if err := stateMachine.SendEvent(ctx, EventInitialize); err != nil {
			return fmt.Errorf("初始化事件发送失败: %w", err)
		}
		// 立即发送连接事件
		pi.logger.Info("设备从初始化转为在线", zap.String("设备ID", deviceID))
		return stateMachine.SendEvent(ctx, EventConnect)

	case DeviceStateInitializing:
		// 初始化状态下轮询成功，转到在线状态
		pi.logger.Info("设备从初始化转为在线", zap.String("设备ID", deviceID))
		return stateMachine.SendEvent(ctx, EventConnect)

	case DeviceStateOffline:
		// 离线状态下轮询成功，重连到在线状态
		pi.logger.Info("设备从离线转为在线", zap.String("设备ID", deviceID))
		return stateMachine.SendEvent(ctx, EventReconnect)

	case DeviceStateError:
		// 错误状态下轮询成功，尝试恢复
		if pi.shouldAttemptRecovery(deviceID) {
			pi.logger.Info("设备从错误状态恢复", zap.String("设备ID", deviceID))
			return stateMachine.SendEvent(ctx, EventRecover)
		}

	case DeviceStateOnline, DeviceStateIdle:
		// 已在线状态，保持当前状态并更新最后成功时间
		pi.logger.Debug("设备保持在线状态",
			zap.String("设备ID", deviceID),
			zap.String("当前状态", string(currentState)))

		// 修复状态同步问题：即使状态未变化，也要确保数据库状态一致
		if pi.deviceManager != nil {
			pi.logger.Info("强制同步设备在线状态到数据库",
				zap.String("设备ID", deviceID),
				zap.String("状态机状态", string(currentState)))

			// 直接调用设备管理器更新状态，确保数据库状态一致
			if err := pi.deviceManager.UpdateDeviceStatus(ctx, deviceID, models.DeviceStatusOnline); err != nil {
				pi.logger.Error("强制同步设备状态到数据库失败",
					zap.String("设备ID", deviceID),
					zap.Error(err))
				// 不返回错误，避免影响轮询流程
			} else {
				pi.logger.Info("设备在线状态已强制同步到数据库",
					zap.String("设备ID", deviceID))
			}

			// 同时更新设备健康状态
			if err := pi.deviceManager.UpdateDeviceHealth(ctx, deviceID, models.DeviceHealthGood); err != nil {
				pi.logger.Error("强制同步设备健康状态到数据库失败",
					zap.String("设备ID", deviceID),
					zap.Error(err))
			}
		}

	default:
		// 其他状态下的轮询成功，记录警告
		pi.logger.Warn("未处理的设备状态",
			zap.String("设备ID", deviceID),
			zap.String("当前状态", string(currentState)))
	}

	return nil
}

// handlePollFailure 处理轮询失败
func (pi *PollingIntegration) handlePollFailure(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
	deviceID := event.DeviceID

	// 获取错误信息
	errorMsg := ""
	if errData, exists := event.Data["error"]; exists {
		if errStr, ok := errData.(string); ok {
			errorMsg = errStr
		}
	}

	// 更新轮询状态
	pi.updatePollState(deviceID, false, errorMsg, event.Data)

	// 获取连续失败次数
	pollState, err := pi.GetDevicePollState(deviceID)
	if err != nil {
		return err
	}

	currentState := stateMachine.GetCurrentState()

	// 根据失败次数决定状态转换
	if pollState.ConsecutiveFailures >= pi.config.MaxConsecutiveFailures {
		switch currentState {
		case DeviceStateOnline, DeviceStateIdle:
			// 在线状态下连续失败，转到离线状态
			return stateMachine.SendEvent(ctx, EventDisconnect)

		case DeviceStateOffline:
			// 离线状态下持续失败，转到错误状态
			if pollState.ConsecutiveFailures >= pi.config.FailureThreshold {
				return stateMachine.SendEvent(ctx, EventTimeout)
			}

		case DeviceStateInitializing:
			// 初始化状态下失败，转到错误状态
			return stateMachine.SendEvent(ctx, EventError)
		}
	}

	return nil
}

// handleDeviceRecovery 处理设备恢复
func (pi *PollingIntegration) handleDeviceRecovery(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
	deviceID := event.DeviceID

	// 重置失败计数
	pi.pollMutex.Lock()
	if pollState, exists := pi.devicePollStates[deviceID]; exists {
		pollState.ConsecutiveFailures = 0
		pollState.HealthStatus = "healthy"
		pollState.LastError = ""
	}
	pi.pollMutex.Unlock()

	// 执行恢复状态转换
	return stateMachine.SendEvent(ctx, EventRecover)
}

// updatePollState 更新设备轮询状态
func (pi *PollingIntegration) updatePollState(deviceID string, success bool, errorMsg string, eventData map[string]interface{}) {
	pi.pollMutex.Lock()
	defer pi.pollMutex.Unlock()

	pollState, exists := pi.devicePollStates[deviceID]
	if !exists {
		return
	}

	now := time.Now()
	pollState.LastPollTime = now
	pollState.TotalPolls++

	if success {
		pollState.LastSuccessTime = now
		pollState.SuccessfulPolls++
		pollState.ConsecutiveFailures = 0
		pollState.LastError = ""
		pollState.HealthStatus = "healthy"
	} else {
		pollState.FailedPolls++
		pollState.ConsecutiveFailures++
		pollState.LastError = errorMsg

		// 更新健康状态
		if pollState.ConsecutiveFailures >= pi.config.FailureThreshold {
			pollState.HealthStatus = "critical"
		} else if pollState.ConsecutiveFailures >= pi.config.MaxConsecutiveFailures {
			pollState.HealthStatus = "warning"
		}
	}

	// 更新响应时间（如果有）
	if responseTime, exists := eventData["response_time"]; exists {
		if duration, ok := responseTime.(time.Duration); ok {
			pollState.LastResponseTime = duration
			pollState.ResponseTimes = append(pollState.ResponseTimes, duration)

			// 限制响应时间历史数量
			if len(pollState.ResponseTimes) > 100 {
				pollState.ResponseTimes = pollState.ResponseTimes[1:]
			}
		}
	}

	// 更新当前状态
	if deviceState, exists := eventData["device_state"]; exists {
		if state, ok := deviceState.(DeviceState); ok {
			pollState.CurrentState = state
		}
	}
}

// shouldAttemptRecovery 判断是否应该尝试恢复
func (pi *PollingIntegration) shouldAttemptRecovery(deviceID string) bool {
	pi.pollMutex.RLock()
	defer pi.pollMutex.RUnlock()

	pollState, exists := pi.devicePollStates[deviceID]
	if !exists {
		return false
	}

	// 如果连续失败次数较低且最近有成功轮询，则尝试恢复
	return pollState.ConsecutiveFailures <= 1 &&
		time.Since(pollState.LastSuccessTime) < pi.config.RecoveryCheckInterval
}

// healthCheckRoutine 健康检查例程
func (pi *PollingIntegration) healthCheckRoutine(ctx context.Context) {
	ticker := time.NewTicker(pi.config.RecoveryCheckInterval)
	defer ticker.Stop()

	pi.logger.Info("Health check routine started")

	for {
		select {
		case <-ctx.Done():
			pi.logger.Info("Health check routine stopped")
			return
		case <-ticker.C:
			pi.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 执行健康检查
func (pi *PollingIntegration) performHealthCheck(ctx context.Context) {
	pi.pollMutex.RLock()
	deviceStates := make(map[string]*DevicePollState)
	for deviceID, state := range pi.devicePollStates {
		stateCopy := *state
		deviceStates[deviceID] = &stateCopy
	}
	pi.pollMutex.RUnlock()

	for deviceID, pollState := range deviceStates {
		// 检查设备是否长时间没有轮询
		if time.Since(pollState.LastPollTime) > pi.config.RecoveryCheckInterval*2 {
			pi.logger.Warn("Device has not been polled recently",
				zap.String("device_id", deviceID),
				zap.Time("last_poll", pollState.LastPollTime))

			// 发送设备检查事件
			event := &BusinessEvent{
				DeviceID:  deviceID,
				EventType: "device_health_check",
				Data: map[string]interface{}{
					"last_poll_time":       pollState.LastPollTime,
					"consecutive_failures": pollState.ConsecutiveFailures,
					"health_status":        pollState.HealthStatus,
				},
				Source: "health_check",
			}

			if err := pi.businessEngine.SendEvent(event); err != nil {
				pi.logger.Error("Failed to send health check event",
					zap.String("device_id", deviceID),
					zap.Error(err))
			}
		}

		// 检查是否需要自动恢复
		if pollState.HealthStatus == "warning" &&
			time.Since(pollState.LastSuccessTime) < pi.config.RecoveryCheckInterval &&
			pollState.ConsecutiveFailures < pi.config.MaxConsecutiveFailures {

			pi.logger.Info("Attempting automatic device recovery",
				zap.String("device_id", deviceID))

			event := &BusinessEvent{
				DeviceID:  deviceID,
				EventType: "device_recovery",
				Data: map[string]interface{}{
					"recovery_type": "automatic",
					"trigger":       "health_check",
				},
				Source: "health_check",
			}

			if err := pi.businessEngine.SendEvent(event); err != nil {
				pi.logger.Error("Failed to send recovery event",
					zap.String("device_id", deviceID),
					zap.Error(err))
			}
		}
	}
}

// pollEventListener 监听轮询事件并转发给业务引擎
func (pi *PollingIntegration) pollEventListener(ctx context.Context) {
	defer pi.eventWG.Done()

	pi.logger.Info("Starting poll event listener")

	// 获取轮询调度器的事件总线（需要添加到轮询调度器的接口）
	eventBus := pi.getPollingEventBus()
	if eventBus == nil {
		pi.logger.Error("Cannot get polling event bus")
		return
	}

	for {
		select {
		case <-ctx.Done():
			pi.logger.Info("Poll event listener stopped by context")
			return
		case <-pi.stopEventListener:
			pi.logger.Info("Poll event listener stopped")
			return
		case pollEvent := <-eventBus:
			pi.handlePollingEvent(ctx, pollEvent)
		}
	}
}

// getPollingEventBus 获取轮询调度器的事件总线
func (pi *PollingIntegration) getPollingEventBus() <-chan *polling.PollEvent {
	// 调用轮询调度器的GetEventBus方法
	if pi.pollScheduler != nil {
		return pi.pollScheduler.GetEventBus()
	}
	return nil
}

// handlePollingEvent 处理轮询事件
func (pi *PollingIntegration) handlePollingEvent(ctx context.Context, pollEvent *polling.PollEvent) {
	if pollEvent == nil {
		return
	}

	pi.logger.Info("接收到轮询事件",
		zap.String("设备ID", pollEvent.DeviceID),
		zap.String("事件类型", pollEvent.EventType),
		zap.Time("事件时间", pollEvent.Timestamp))

	// 转换轮询事件为业务事件
	businessEvent := &BusinessEvent{
		DeviceID:  pollEvent.DeviceID,
		EventType: pollEvent.EventType, // "poll_success" 或 "poll_failure"
		Data:      pollEvent.Data,
		Timestamp: pollEvent.Timestamp,
		Source:    "poll_scheduler",
	}

	pi.logger.Debug("转发轮询事件到业务引擎",
		zap.String("设备ID", pollEvent.DeviceID),
		zap.String("事件类型", pollEvent.EventType),
		zap.Any("事件数据", pollEvent.Data))

	// 发送到业务引擎
	if err := pi.businessEngine.SendEvent(businessEvent); err != nil {
		pi.logger.Error("转发轮询事件到业务引擎失败",
			zap.String("设备ID", pollEvent.DeviceID),
			zap.String("事件类型", pollEvent.EventType),
			zap.Error(err))
	} else {
		pi.logger.Debug("轮询事件已成功转发到业务引擎",
			zap.String("设备ID", pollEvent.DeviceID),
			zap.String("事件类型", pollEvent.EventType))
	}
}
