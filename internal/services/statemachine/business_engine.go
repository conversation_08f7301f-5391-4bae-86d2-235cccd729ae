package statemachine

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/pkg/models"
)

// BusinessEvent 业务事件类型
type BusinessEvent struct {
	DeviceID  string                 `json:"device_id"`  // 设备ID
	EventType string                 `json:"event_type"` // 事件类型
	Data      map[string]interface{} `json:"data"`       // 事件数据
	Timestamp time.Time              `json:"timestamp"`  // 时间戳
	Source    string                 `json:"source"`     // 事件源
}

// BusinessRule 业务规则定义
type BusinessRule struct {
	Name        string                `json:"name"`        // 规则名称
	Description string                `json:"description"` // 规则描述
	Condition   BusinessConditionFunc `json:"-"`           // 条件函数
	Action      BusinessActionFunc    `json:"-"`           // 动作函数
	Priority    int                   `json:"priority"`    // 优先级
	Enabled     bool                  `json:"enabled"`     // 是否启用
}

// BusinessConditionFunc 业务条件函数
type BusinessConditionFunc func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool

// BusinessActionFunc 业务动作函数
type BusinessActionFunc func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error

// BusinessEngineConfig 业务引擎配置
type BusinessEngineConfig struct {
	MaxDevices          int           `json:"max_devices"`           // 最大设备数
	EventBufferSize     int           `json:"event_buffer_size"`     // 事件缓冲区大小
	ProcessingTimeout   time.Duration `json:"processing_timeout"`    // 处理超时
	StateMachineTimeout time.Duration `json:"statemachine_timeout"`  // 状态机超时
	RuleProcessingDelay time.Duration `json:"rule_processing_delay"` // 规则处理延迟
	EnableMetrics       bool          `json:"enable_metrics"`        // 启用指标
	EnableAuditLog      bool          `json:"enable_audit_log"`      // 启用审计日志
}

// BusinessEngine 业务流程引擎
type BusinessEngine struct {
	config *BusinessEngineConfig
	logger *zap.Logger

	// 状态机管理
	stateMachines map[string]*DeviceStateMachine
	smMutex       sync.RWMutex

	// 业务规则
	rules      []BusinessRule
	rulesMutex sync.RWMutex

	// 事件处理
	eventQueue   chan *BusinessEvent
	stopChan     chan struct{}
	processingWG sync.WaitGroup

	// 统计信息
	stats      *BusinessEngineStats
	statsMutex sync.RWMutex

	// 运行状态
	running bool
	mu      sync.RWMutex
}

// BusinessEngineStats 业务引擎统计信息
type BusinessEngineStats struct {
	TotalEvents        int64         `json:"total_events"`         // 总事件数
	ProcessedEvents    int64         `json:"processed_events"`     // 已处理事件数
	FailedEvents       int64         `json:"failed_events"`        // 失败事件数
	TotalRulesFired    int64         `json:"total_rules_fired"`    // 触发规则总数
	ActiveDevices      int           `json:"active_devices"`       // 活跃设备数
	AverageProcessTime time.Duration `json:"average_process_time"` // 平均处理时间
	Uptime             time.Duration `json:"uptime"`               // 运行时间
	StartTime          time.Time     `json:"start_time"`           // 启动时间
}

// NewBusinessEngine 创建业务流程引擎
func NewBusinessEngine(config *BusinessEngineConfig, logger *zap.Logger) *BusinessEngine {
	if config == nil {
		config = &BusinessEngineConfig{
			MaxDevices:          1000,
			EventBufferSize:     10000,
			ProcessingTimeout:   30 * time.Second,
			StateMachineTimeout: 5 * time.Second,
			RuleProcessingDelay: 100 * time.Millisecond,
			EnableMetrics:       true,
			EnableAuditLog:      true,
		}
	}

	engine := &BusinessEngine{
		config:        config,
		logger:        logger,
		stateMachines: make(map[string]*DeviceStateMachine),
		rules:         make([]BusinessRule, 0),
		eventQueue:    make(chan *BusinessEvent, config.EventBufferSize),
		stopChan:      make(chan struct{}),
		stats: &BusinessEngineStats{
			StartTime: time.Now(),
		},
	}

	// 注册默认业务规则
	engine.registerDefaultRules()

	return engine
}

// Start 启动业务引擎
func (be *BusinessEngine) Start(ctx context.Context) error {
	be.mu.Lock()
	defer be.mu.Unlock()

	if be.running {
		return nil // 已经运行中
	}

	be.running = true
	be.stats.StartTime = time.Now()

	// 启动事件处理协程
	be.processingWG.Add(1)
	go be.eventProcessor(ctx)

	be.logger.Info("Business engine started",
		zap.Int("max_devices", be.config.MaxDevices),
		zap.Int("event_buffer_size", be.config.EventBufferSize))

	return nil
}

// Stop 停止业务引擎
func (be *BusinessEngine) Stop() error {
	be.mu.Lock()
	defer be.mu.Unlock()

	if !be.running {
		return nil // 已经停止
	}

	be.running = false

	// 发送停止信号
	close(be.stopChan)

	// 等待处理协程结束
	be.processingWG.Wait()

	// 更新统计信息
	be.statsMutex.Lock()
	be.stats.Uptime = time.Since(be.stats.StartTime)
	be.statsMutex.Unlock()

	be.logger.Info("Business engine stopped",
		zap.Duration("uptime", be.stats.Uptime))

	return nil
}

// RegisterDevice 注册设备到业务引擎
func (be *BusinessEngine) RegisterDevice(device *models.Device) error {
	be.smMutex.Lock()
	defer be.smMutex.Unlock()

	// 检查设备数量限制
	if len(be.stateMachines) >= be.config.MaxDevices {
		return fmt.Errorf("maximum devices limit reached: %d", be.config.MaxDevices)
	}

	// 检查设备是否已注册
	if _, exists := be.stateMachines[device.ID]; exists {
		return fmt.Errorf("device already registered: %s", device.ID)
	}

	// 创建设备状态机
	stateMachine := NewDeviceStateMachine(device.ID, be.logger)

	// 注册状态变更监听器
	stateMachine.AddStateChangeListener(&BusinessStateListener{
		engine: be,
	})

	// 注册设备特定的守卫和动作
	be.registerDeviceHandlers(stateMachine, device)

	be.stateMachines[device.ID] = stateMachine

	// 更新统计信息
	be.statsMutex.Lock()
	be.stats.ActiveDevices = len(be.stateMachines)
	be.statsMutex.Unlock()

	be.logger.Info("Device registered to business engine",
		zap.String("device_id", device.ID),
		zap.String("device_type", string(device.Type)))

	return nil
}

// UnregisterDevice 从业务引擎注销设备
func (be *BusinessEngine) UnregisterDevice(deviceID string) error {
	be.smMutex.Lock()
	defer be.smMutex.Unlock()

	if _, exists := be.stateMachines[deviceID]; !exists {
		return fmt.Errorf("device not found: %s", deviceID)
	}

	delete(be.stateMachines, deviceID)

	// 更新统计信息
	be.statsMutex.Lock()
	be.stats.ActiveDevices = len(be.stateMachines)
	be.statsMutex.Unlock()

	be.logger.Info("Device unregistered from business engine",
		zap.String("device_id", deviceID))

	return nil
}

// SendEvent 发送业务事件
func (be *BusinessEngine) SendEvent(event *BusinessEvent) error {
	if !be.isRunning() {
		return fmt.Errorf("business engine is not running")
	}

	// 验证事件
	if err := be.validateEvent(event); err != nil {
		return fmt.Errorf("invalid event: %w", err)
	}

	// 设置时间戳
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}

	// 发送到事件队列
	select {
	case be.eventQueue <- event:
		be.statsMutex.Lock()
		be.stats.TotalEvents++
		be.statsMutex.Unlock()
		return nil
	default:
		be.statsMutex.Lock()
		be.stats.FailedEvents++
		be.statsMutex.Unlock()
		return fmt.Errorf("event queue is full")
	}
}

// GetDeviceStateMachine 获取设备状态机
func (be *BusinessEngine) GetDeviceStateMachine(deviceID string) (*DeviceStateMachine, error) {
	be.smMutex.RLock()
	defer be.smMutex.RUnlock()

	stateMachine, exists := be.stateMachines[deviceID]
	if !exists {
		return nil, fmt.Errorf("device not found: %s", deviceID)
	}

	return stateMachine, nil
}

// GetDeviceState 获取设备当前状态
func (be *BusinessEngine) GetDeviceState(deviceID string) (DeviceState, error) {
	stateMachine, err := be.GetDeviceStateMachine(deviceID)
	if err != nil {
		return DeviceStateUnknown, err
	}

	return stateMachine.GetCurrentState(), nil
}

// AddBusinessRule 添加业务规则
func (be *BusinessEngine) AddBusinessRule(rule BusinessRule) {
	be.rulesMutex.Lock()
	defer be.rulesMutex.Unlock()

	be.rules = append(be.rules, rule)

	be.logger.Info("Business rule added",
		zap.String("rule_name", rule.Name),
		zap.Int("priority", rule.Priority))
}

// GetStatistics 获取引擎统计信息
func (be *BusinessEngine) GetStatistics() *BusinessEngineStats {
	be.statsMutex.RLock()
	defer be.statsMutex.RUnlock()

	// 返回副本
	stats := *be.stats
	if be.running {
		stats.Uptime = time.Since(be.stats.StartTime)
	}

	return &stats
}

// isRunning 检查引擎是否在运行
func (be *BusinessEngine) isRunning() bool {
	be.mu.RLock()
	defer be.mu.RUnlock()
	return be.running
}

// eventProcessor 事件处理器
func (be *BusinessEngine) eventProcessor(ctx context.Context) {
	defer be.processingWG.Done()

	be.logger.Info("Event processor started")

	for {
		select {
		case <-be.stopChan:
			be.logger.Info("Event processor stopping")
			return
		case event := <-be.eventQueue:
			be.processEvent(ctx, event)
		}
	}
}

// processEvent 处理单个事件
func (be *BusinessEngine) processEvent(ctx context.Context, event *BusinessEvent) {
	startTime := time.Now()

	be.logger.Info("处理业务事件",
		zap.String("设备ID", event.DeviceID),
		zap.String("事件类型", event.EventType),
		zap.String("事件源", event.Source),
		zap.Time("事件时间", event.Timestamp))

	// 获取设备状态机
	stateMachine, err := be.GetDeviceStateMachine(event.DeviceID)
	if err != nil {
		be.logger.Error("获取设备状态机失败",
			zap.String("设备ID", event.DeviceID),
			zap.Error(err))
		be.statsMutex.Lock()
		be.stats.FailedEvents++
		be.statsMutex.Unlock()
		return
	}

	// 获取当前设备状态
	currentState := stateMachine.GetCurrentState()

	be.logger.Debug("业务事件详情",
		zap.String("设备ID", event.DeviceID),
		zap.String("事件类型", event.EventType),
		zap.String("当前状态", string(currentState)),
		zap.Any("事件数据", event.Data))

	// 处理业务规则
	rulesFired := be.processBusinessRules(ctx, event, currentState, stateMachine)

	// 更新统计信息
	be.statsMutex.Lock()
	be.stats.ProcessedEvents++
	be.stats.TotalRulesFired += int64(rulesFired)

	// 更新平均处理时间
	processingTime := time.Since(startTime)
	if be.stats.ProcessedEvents == 1 {
		be.stats.AverageProcessTime = processingTime
	} else {
		// 滚动平均
		be.stats.AverageProcessTime = (be.stats.AverageProcessTime*time.Duration(be.stats.ProcessedEvents-1) + processingTime) / time.Duration(be.stats.ProcessedEvents)
	}
	be.statsMutex.Unlock()

	be.logger.Info("业务事件处理完成",
		zap.String("设备ID", event.DeviceID),
		zap.String("事件类型", event.EventType),
		zap.Int("触发规则数", rulesFired),
		zap.Duration("处理耗时", processingTime))
}

// processBusinessRules 处理业务规则
func (be *BusinessEngine) processBusinessRules(ctx context.Context, event *BusinessEvent, deviceState DeviceState, stateMachine *DeviceStateMachine) int {
	be.rulesMutex.RLock()
	rules := make([]BusinessRule, len(be.rules))
	copy(rules, be.rules)
	be.rulesMutex.RUnlock()

	rulesFired := 0

	// 按优先级排序规则（优先级数字越小越优先）
	for i := 0; i < len(rules); i++ {
		for j := i + 1; j < len(rules); j++ {
			if rules[i].Priority > rules[j].Priority {
				rules[i], rules[j] = rules[j], rules[i]
			}
		}
	}

	// 执行规则
	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		// 检查条件
		if rule.Condition != nil && rule.Condition(ctx, event, deviceState) {
			be.logger.Debug("Business rule condition met",
				zap.String("rule_name", rule.Name),
				zap.String("device_id", event.DeviceID))

			// 执行动作
			if rule.Action != nil {
				if err := rule.Action(ctx, event, stateMachine); err != nil {
					be.logger.Error("Business rule action failed",
						zap.String("rule_name", rule.Name),
						zap.String("device_id", event.DeviceID),
						zap.Error(err))
				} else {
					rulesFired++
					be.logger.Info("Business rule fired",
						zap.String("rule_name", rule.Name),
						zap.String("device_id", event.DeviceID))
				}
			}

			// 添加规则处理延迟
			if be.config.RuleProcessingDelay > 0 {
				time.Sleep(be.config.RuleProcessingDelay)
			}
		}
	}

	return rulesFired
}

// validateEvent 验证事件
func (be *BusinessEngine) validateEvent(event *BusinessEvent) error {
	if event == nil {
		return fmt.Errorf("event is nil")
	}

	if event.DeviceID == "" {
		return fmt.Errorf("device_id is required")
	}

	if event.EventType == "" {
		return fmt.Errorf("event_type is required")
	}

	return nil
}

// registerDeviceHandlers 注册设备特定的守卫和动作
func (be *BusinessEngine) registerDeviceHandlers(stateMachine *DeviceStateMachine, device *models.Device) {
	// 注册泵设备特定的守卫
	if device.Type == models.DeviceTypePump {
		stateMachine.RegisterGuard("pump_ready", func(ctx context.Context, stateCtx *StateContext, event DeviceEvent) bool {
			// 检查泵是否准备好进行交易
			if ready, exists := stateCtx.StateData["pump_ready"]; exists {
				return ready.(bool)
			}
			return true // 默认认为准备好
		})

		stateMachine.RegisterAction("log_transaction", func(ctx context.Context, stateCtx *StateContext, event DeviceEvent) error {
			// 记录交易日志
			be.logger.Info("Transaction event logged",
				zap.String("device_id", stateCtx.DeviceID),
				zap.String("event", string(event)))
			return nil
		})
	}
}

// registerDefaultRules 注册默认业务规则
func (be *BusinessEngine) registerDefaultRules() {
	// 规则1: 设备连接时初始化
	be.AddBusinessRule(BusinessRule{
		Name:        "device_connection_init",
		Description: "Initialize device when connected",
		Priority:    10,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "device_connected" && deviceState == DeviceStateUnknown
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return stateMachine.SendEvent(ctx, EventInitialize)
		},
	})

	// 规则2: 轮询成功时连接设备
	be.AddBusinessRule(BusinessRule{
		Name:        "poll_success_connect",
		Description: "Connect device when poll succeeds",
		Priority:    20,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "poll_success" && deviceState == DeviceStateInitializing
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return stateMachine.SendEvent(ctx, EventConnect)
		},
	})

	// 规则3: 轮询失败时处理错误
	be.AddBusinessRule(BusinessRule{
		Name:        "poll_failure_error",
		Description: "Handle error when poll fails",
		Priority:    30,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "poll_failure"
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			// 检查失败次数
			if failureCount, exists := event.Data["failure_count"]; exists {
				if count, ok := failureCount.(int); ok && count >= 3 {
					return stateMachine.SendEvent(ctx, EventError)
				}
			}
			return nil
		},
	})

	// 规则4: 交易请求处理
	be.AddBusinessRule(BusinessRule{
		Name:        "transaction_request",
		Description: "Handle transaction request",
		Priority:    40,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "transaction_request" &&
				(deviceState == DeviceStateOnline || deviceState == DeviceStateIdle)
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return stateMachine.SendEvent(ctx, EventStartTransaction)
		},
	})

	// 规则5: 设备故障恢复
	be.AddBusinessRule(BusinessRule{
		Name:        "device_recovery",
		Description: "Recover device from error state",
		Priority:    50,
		Enabled:     true,
		Condition: func(ctx context.Context, event *BusinessEvent, deviceState DeviceState) bool {
			return event.EventType == "device_recovery" &&
				(deviceState == DeviceStateError || deviceState == DeviceStateFaulted)
		},
		Action: func(ctx context.Context, event *BusinessEvent, stateMachine *DeviceStateMachine) error {
			return stateMachine.SendEvent(ctx, EventRecover)
		},
	})
}

// BusinessStateListener 业务状态监听器
type BusinessStateListener struct {
	engine *BusinessEngine
}

// OnStateChange 状态变更回调
func (bsl *BusinessStateListener) OnStateChange(deviceID string, from, to DeviceState, event DeviceEvent) error {
	// 创建状态变更事件
	stateEvent := &BusinessEvent{
		DeviceID:  deviceID,
		EventType: "state_change",
		Data: map[string]interface{}{
			"from_state": string(from),
			"to_state":   string(to),
			"event":      string(event),
		},
		Timestamp: time.Now(),
		Source:    "state_machine",
	}

	// 发送状态变更事件到业务引擎
	if err := bsl.engine.SendEvent(stateEvent); err != nil {
		bsl.engine.logger.Error("Failed to send state change event",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return err
	}

	return nil
}
