package statemachine

import (
	"context"
	"time"

	"fcc-service/pkg/models"
)

// StateMachineService 状态机服务接口
type StateMachineService interface {
	// 业务引擎管理
	StartBusinessEngine(ctx context.Context) error
	StopBusinessEngine() error
	IsBusinessEngineRunning() bool

	// 设备管理
	RegisterDevice(device *models.Device) error
	UnregisterDevice(deviceID string) error
	GetDeviceState(deviceID string) (DeviceState, error)
	GetDeviceStateMachine(deviceID string) (*DeviceStateMachine, error)

	// 事件处理
	SendBusinessEvent(event *BusinessEvent) error
	AddBusinessRule(rule BusinessRule)

	// 状态查询
	GetAllDeviceStates() map[string]DeviceState
	GetDeviceStateHistory(deviceID string, limit int) ([]TransitionRecord, error)
	GetDeviceStatistics(deviceID string) (map[string]interface{}, error)

	// 引擎统计
	GetEngineStatistics() *BusinessEngineStats
}

// EventPublisher 事件发布器接口
type EventPublisher interface {
	PublishEvent(event *BusinessEvent) error
	Subscribe(eventType string, handler func(*BusinessEvent) error) error
	Unsubscribe(eventType string, handlerID string) error
}

// StateChangeNotifier 状态变更通知器接口
type StateChangeNotifier interface {
	NotifyStateChange(deviceID string, from, to DeviceState, event DeviceEvent) error
	AddStateChangeListener(listener StateChangeListener)
	RemoveStateChangeListener(listener StateChangeListener)
}

// DeviceHealthMonitor 设备健康监控接口
type DeviceHealthMonitor interface {
	GetDeviceHealth(deviceID string) (*DeviceHealthStatus, error)
	GetAllDeviceHealth() map[string]*DeviceHealthStatus
	SetHealthThreshold(deviceID string, threshold *HealthThreshold) error
}

// DeviceHealthStatus 设备健康状态
type DeviceHealthStatus struct {
	DeviceID            string        `json:"device_id"`
	Status              string        `json:"status"` // healthy, warning, critical, unknown
	LastHealthCheck     time.Time     `json:"last_health_check"`
	ConsecutiveFailures int           `json:"consecutive_failures"`
	SuccessRate         float64       `json:"success_rate"`
	AverageResponseTime time.Duration `json:"average_response_time"`
	Uptime              time.Duration `json:"uptime"`
	Issues              []string      `json:"issues"`
}

// HealthThreshold 健康阈值
type HealthThreshold struct {
	MaxConsecutiveFailures int           `json:"max_consecutive_failures"`
	MinSuccessRate         float64       `json:"min_success_rate"`
	MaxResponseTime        time.Duration `json:"max_response_time"`
	CheckInterval          time.Duration `json:"check_interval"`
}

// StateMachineConfig 状态机配置
type StateMachineConfig struct {
	// 业务引擎配置
	BusinessEngine *BusinessEngineConfig `json:"business_engine"`

	// 轮询集成配置
	PollingIntegration *PollingIntegrationConfig `json:"polling_integration"`

	// 监控配置
	HealthMonitoring *HealthMonitoringConfig `json:"health_monitoring"`

	// 日志配置
	EnableDebugLogging bool `json:"enable_debug_logging"`
	EnableAuditLog     bool `json:"enable_audit_log"`
}

// HealthMonitoringConfig 健康监控配置
type HealthMonitoringConfig struct {
	EnableHealthMonitoring bool             `json:"enable_health_monitoring"`
	HealthCheckInterval    time.Duration    `json:"health_check_interval"`
	DefaultThreshold       *HealthThreshold `json:"default_threshold"`
	AlertOnCritical        bool             `json:"alert_on_critical"`
	AlertOnWarning         bool             `json:"alert_on_warning"`
}

// StateMachineMetrics 状态机指标
type StateMachineMetrics struct {
	// 设备指标
	TotalDevices    int                 `json:"total_devices"`
	DevicesByState  map[DeviceState]int `json:"devices_by_state"`
	DevicesByHealth map[string]int      `json:"devices_by_health"`

	// 事件指标
	TotalEvents     int64            `json:"total_events"`
	EventsByType    map[string]int64 `json:"events_by_type"`
	EventsPerSecond float64          `json:"events_per_second"`

	// 状态转换指标
	TotalTransitions      int64         `json:"total_transitions"`
	SuccessfulTransitions int64         `json:"successful_transitions"`
	FailedTransitions     int64         `json:"failed_transitions"`
	AverageTransitionTime time.Duration `json:"average_transition_time"`

	// 业务规则指标
	TotalRulesFired int64            `json:"total_rules_fired"`
	RulesByName     map[string]int64 `json:"rules_by_name"`

	// 系统指标
	Uptime         time.Duration `json:"uptime"`
	MemoryUsage    int64         `json:"memory_usage"`
	GoroutineCount int           `json:"goroutine_count"`
}

// DeviceStateQuery 设备状态查询参数
type DeviceStateQuery struct {
	DeviceIDs    []string      `json:"device_ids,omitempty"`
	States       []DeviceState `json:"states,omitempty"`
	HealthStatus []string      `json:"health_status,omitempty"`
	TimeRange    *TimeRange    `json:"time_range,omitempty"`
	Limit        int           `json:"limit,omitempty"`
	Offset       int           `json:"offset,omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// StateTransitionQuery 状态转换查询参数
type StateTransitionQuery struct {
	DeviceID    string      `json:"device_id,omitempty"`
	FromState   DeviceState `json:"from_state,omitempty"`
	ToState     DeviceState `json:"to_state,omitempty"`
	Event       DeviceEvent `json:"event,omitempty"`
	TimeRange   *TimeRange  `json:"time_range,omitempty"`
	SuccessOnly bool        `json:"success_only"`
	Limit       int         `json:"limit,omitempty"`
	Offset      int         `json:"offset,omitempty"`
}

// BusinessRuleQuery 业务规则查询参数
type BusinessRuleQuery struct {
	RuleName  string     `json:"rule_name,omitempty"`
	Priority  int        `json:"priority,omitempty"`
	Enabled   *bool      `json:"enabled,omitempty"`
	TimeRange *TimeRange `json:"time_range,omitempty"`
	Limit     int        `json:"limit,omitempty"`
	Offset    int        `json:"offset,omitempty"`
}
