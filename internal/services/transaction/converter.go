package transaction

import (
	"encoding/json"
	"fmt"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/datatypes"

	"fcc-service/pkg/models"
)

// DARTTransactionConverter DART交易转换器
type DARTTransactionConverter struct {
	logger *zap.Logger
}

// NewDARTTransactionConverter 创建DART交易转换器
func NewDARTTransactionConverter(logger *zap.Logger) *DARTTransactionConverter {
	return &DARTTransactionConverter{
		logger: logger,
	}
}

// decimalToPointer 将decimal.Decimal转换为*decimal.Decimal（任务6新增）
func (c *DARTTransactionConverter) decimalToPointer(d decimal.Decimal) *decimal.Decimal {
	if d.<PERSON>() {
		return nil // 零值使用nil表示，节省存储空间
	}
	return &d
}

// pointerToDecimal 将*decimal.Decimal转换为decimal.Decimal（任务6新增）
func (c *DARTTransactionConverter) pointerToDecimal(d *decimal.Decimal) decimal.Decimal {
	if d == nil {
		return decimal.Zero
	}
	return *d
}

// ToStandardTransaction 将DART交易转换为标准交易模型
func (c *DARTTransactionConverter) ToStandardTransaction(dartTx *models.DARTTransaction, nozzle *models.Nozzle, context *TransactionContext) (*models.Transaction, error) {
	if dartTx == nil {
		return nil, fmt.Errorf("DART transaction is nil")
	}

	if nozzle == nil {
		return nil, fmt.Errorf("nozzle is nil")
	}

	// 创建扩展数据
	extraData := map[string]interface{}{
		"dart_metadata": map[string]interface{}{
			"last_dc_transaction": dartTx.LastDCTransaction,
			"protocol_version":    dartTx.ProtocolVersion,
			"device_address":      dartTx.DeviceAddress,
			"error_code":          dartTx.ErrorCode,
			"error_message":       dartTx.ErrorMessage,
		},
		// 任务6新增：泵码元数据
		"pump_reading_metadata": map[string]interface{}{
			"pump_reading_source": dartTx.PumpReadingSource,
			"pump_reading_time":   dartTx.PumpReadingTime,
		},
	}

	// 序列化为 JSON
	extraDataJSON, err := json.Marshal(extraData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal extra data: %w", err)
	}

	// 创建标准交易模型
	standardTx := &models.Transaction{
		ID:   dartTx.ID,
		Type: models.TransactionTypeFuel, // Wayne DART主要用于加油交易

		// 设备信息 - 🚀 使用真实的 Nozzle 对象
		DeviceID: dartTx.DeviceID,
		NozzleID: nozzle.ID, // 直接使用数据库中的真实 Nozzle ID

		// 🚀 基础油品信息：优先从Nozzle中提取，确保基本信息可用
		FuelType: c.extractBasicFuelType(nozzle),

		// 交易数据
		UnitPrice:    dartTx.UnitPrice,
		ActualVolume: dartTx.Volume,
		ActualAmount: dartTx.Amount,
		TotalAmount:  dartTx.Amount, // 对于加油交易，实际金额就是总金额

		// 时间信息
		StartedAt:   &dartTx.StartTime,
		CompletedAt: dartTx.EndTime,
		CreatedAt:   dartTx.StartTime,
		UpdatedAt:   dartTx.LastUpdateTime,

		// 任务6新增：泵码数据映射
		StartPumpVolumeReading: dartTx.PumpVolumeReading,
		EndPumpVolumeReading:   dartTx.PumpVolumeReading,
		StartPumpAmountReading: dartTx.PumpAmountReading,
		EndPumpAmountReading:   dartTx.PumpAmountReading,
		PumpReadingSource:      dartTx.PumpReadingSource,
		PumpReadingQuality:     "good", // Default quality
		PumpReadingValidated:   false,  // Default validation status

		// 状态转换
		Status: c.convertTransactionStatus(dartTx.Status),

		// 版本信息
		Version: 1,

		// 扩展数据
		ExtraData: datatypes.JSON(extraDataJSON),
	}

	// 🚀 基础油品等级信息：从Nozzle中提取基础FuelGrade信息
	if nozzle.FuelGradeID != nil && *nozzle.FuelGradeID != "" {
		fuelGradeName := ""
		if nozzle.FuelGrade != nil {
			fuelGradeName = nozzle.FuelGrade.Name
		}
		if fuelGradeName == "" {
			// 生成基于ID的默认名称
			fuelGradeName = fmt.Sprintf("Fuel Grade %s", *nozzle.FuelGradeID)
		}
		standardTx.SetFuelGrade(*nozzle.FuelGradeID, fuelGradeName)

		c.logger.Debug("Set basic fuel grade info from nozzle",
			zap.String("transaction_id", dartTx.ID),
			zap.String("fuel_grade_id", *nozzle.FuelGradeID),
			zap.String("fuel_grade_name", fuelGradeName))
	}

	// 设置ControllerID（必需字段）
	if context != nil && context.Device != nil {
		standardTx.ControllerID = context.Device.ControllerID
	} else {
		// 如果没有上下文信息，设置默认值
		standardTx.ControllerID = "unknown-controller"
		c.logger.Warn("No controller context available, using default controller ID",
			zap.String("transaction_id", dartTx.ID),
			zap.String("device_id", dartTx.DeviceID))
	}

	// 如果有结束时间，设置结束时间
	if dartTx.EndTime != nil {
		standardTx.CompletedAt = dartTx.EndTime
		if dartTx.Status == models.DARTTransactionStatusCancelled {
			standardTx.CancelledAt = dartTx.EndTime
		}
	}

	// 填充上下文信息
	if context != nil {
		if err := c.fillContextInfo(standardTx, context); err != nil {
			c.logger.Error("Failed to fill context info", zap.Error(err))
			// 不阻断处理，继续进行
		}
	}

	// 验证必要字段
	if err := c.validateTransaction(standardTx); err != nil {
		return nil, fmt.Errorf("invalid transaction after conversion: %w", err)
	}

	c.logger.Debug("DART transaction converted successfully",
		zap.String("dart_tx_id", dartTx.ID),
		zap.String("standard_tx_id", standardTx.ID),
		zap.String("device_id", standardTx.DeviceID),
		zap.String("nozzle_id", standardTx.NozzleID),
		zap.String("nozzle_name", nozzle.Name),
		zap.Uint8("nozzle_number", nozzle.Number))

	return standardTx, nil
}

// convertTransactionStatus 转换交易状态
func (c *DARTTransactionConverter) convertTransactionStatus(dartStatus models.DARTTransactionStatus) models.TransactionStatus {
	switch dartStatus {
	case models.DARTTransactionStatusInitializing:
		return models.TransactionStatusPending
	case models.DARTTransactionStatusActive:
		return models.TransactionStatusActive
	case models.DARTTransactionStatusCompleted:
		return models.TransactionStatusCompleted
	case models.DARTTransactionStatusCancelled:
		return models.TransactionStatusCancelled
	case models.DARTTransactionStatusError:
		return models.TransactionStatusFailed
	default:
		c.logger.Warn("Unknown DART transaction status, defaulting to pending",
			zap.String("dart_status", string(dartStatus)))
		return models.TransactionStatusPending
	}
}

// fillContextInfo 填充上下文信息到标准交易
func (c *DARTTransactionConverter) fillContextInfo(tx *models.Transaction, context *TransactionContext) error {
	// 解析现有的 ExtraData
	var extraData map[string]interface{}
	if len(tx.ExtraData) > 0 {
		if err := json.Unmarshal(tx.ExtraData, &extraData); err != nil {
			return fmt.Errorf("failed to unmarshal existing extra data: %w", err)
		}
	} else {
		extraData = make(map[string]interface{})
	}

	// 设备上下文
	if context.Device != nil {
		tx.ControllerID = context.Device.ControllerID

		extraData["device_context"] = map[string]interface{}{
			"device_name":   context.Device.DeviceName,
			"device_type":   context.Device.DeviceType,
			"serial_number": context.Device.SerialNumber,
			"model":         context.Device.Model,
			"vendor":        context.Device.Vendor,
			"position":      context.Device.Position,
			"island_id":     context.Device.IslandID,
		}
	}

	// 站点上下文
	if context.Station != nil {
		extraData["station_context"] = map[string]interface{}{
			"station_id":   context.Station.StationID,
			"station_name": context.Station.StationName,
			"station_code": context.Station.StationCode,
			"region":       context.Station.Region,
			"address":      context.Station.Address,
			"company_id":   context.Station.CompanyID,
			"company_name": context.Station.CompanyName,
		}
	}

	// 操作员上下文
	if context.Operator != nil {
		tx.OperatorID = context.Operator.OperatorID
		tx.OperatorName = context.Operator.OperatorName

		extraData["operator_context"] = map[string]interface{}{
			"operator_code":  context.Operator.OperatorCode,
			"department":     context.Operator.Department,
			"role":           context.Operator.Role,
			"shift_id":       context.Operator.ShiftID,
			"workstation_id": context.Operator.WorkstationID,
		}
	}

	// 油品上下文 - 🚀 更新：完善FuelGrade信息转换，填充所有油品字段
	if context.FuelGrade != nil {
		// 🚀 核心字段：填充Transaction模型的新字段
		tx.FuelType = context.FuelGrade.FuelType
		if context.FuelGrade.FuelGradeID != "" {
			tx.SetFuelGrade(context.FuelGrade.FuelGradeID, context.FuelGrade.FuelGradeName)
		}

		// 🚀 扩展数据：保存完整的油品信息供其他用途
		extraData["fuel_grade_context"] = map[string]interface{}{
			"fuel_grade_id":   context.FuelGrade.FuelGradeID,
			"fuel_grade_name": context.FuelGrade.FuelGradeName,
			"fuel_type":       context.FuelGrade.FuelType,
			"octane_rating":   context.FuelGrade.OctaneRating,
		}

		c.logger.Debug("Applied fuel grade context to transaction",
			zap.String("transaction_id", tx.ID),
			zap.String("fuel_grade_id", context.FuelGrade.FuelGradeID),
			zap.String("fuel_grade_name", context.FuelGrade.FuelGradeName),
			zap.String("fuel_type", context.FuelGrade.FuelType))
	} else {
		// 🚀 增强：当没有FuelGrade上下文时，尝试从Nozzle信息推断
		c.logger.Warn("No fuel grade context available, using fallback logic",
			zap.String("transaction_id", tx.ID))

		extraData["fuel_grade_context"] = map[string]interface{}{
			"fuel_grade_id":   "unknown",
			"fuel_grade_name": "Unknown Fuel Grade",
			"fuel_type":       tx.FuelType, // 保持现有的FuelType
			"fallback_used":   true,
		}
	}

	// 支付上下文（预留）
	if context.Payment != nil {
		tx.PaymentMethod = context.Payment.PaymentMethod
		tx.PaymentRef = context.Payment.ReferenceNumber
		tx.PaidAmount = context.Payment.Amount

		extraData["payment_context"] = map[string]interface{}{
			"payment_provider": context.Payment.PaymentProvider,
			"card_type":        context.Payment.CardType,
			"card_mask":        context.Payment.CardMask,
			"auth_code":        context.Payment.AuthCode,
		}
	}

	// 合并扩展数据
	if context.ExtraData != nil {
		for key, value := range context.ExtraData {
			extraData[key] = value
		}
	}

	// 序列化更新后的 ExtraData
	extraDataJSON, err := json.Marshal(extraData)
	if err != nil {
		return fmt.Errorf("failed to marshal updated extra data: %w", err)
	}

	tx.ExtraData = datatypes.JSON(extraDataJSON)
	return nil
}

// validateTransaction 验证交易数据
func (c *DARTTransactionConverter) validateTransaction(tx *models.Transaction) error {
	if tx.ID == "" {
		return fmt.Errorf("transaction ID is required")
	}

	if tx.DeviceID == "" {
		return fmt.Errorf("device ID is required")
	}

	if tx.NozzleID == "" {
		return fmt.Errorf("nozzle ID is required")
	}

	if tx.Type == "" {
		return fmt.Errorf("transaction type is required")
	}

	if tx.Status == "" {
		return fmt.Errorf("transaction status is required")
	}

	// 验证金额和体积的合理性
	if tx.ActualAmount.IsNegative() {
		return fmt.Errorf("actual amount cannot be negative")
	}

	if tx.ActualVolume.IsNegative() {
		return fmt.Errorf("actual volume cannot be negative")
	}

	if tx.UnitPrice.IsNegative() {
		return fmt.Errorf("unit price cannot be negative")
	}

	// 验证时间逻辑
	if tx.StartedAt != nil && tx.CompletedAt != nil {
		if tx.CompletedAt.Before(*tx.StartedAt) {
			return fmt.Errorf("completed time cannot be before started time")
		}
	}

	// 任务6新增：验证泵码数据一致性
	if err := c.validatePumpReadings(tx); err != nil {
		return fmt.Errorf("pump reading validation failed: %w", err)
	}

	return nil
}

// validatePumpReadings 验证泵码相关字段 - 任务6修复：支持两阶段策略
func (c *DARTTransactionConverter) validatePumpReadings(tx *models.Transaction) error {
	// ✅ 修复：支持两阶段策略，允许部分泵码数据

	// 验证泵码数据质量字段
	if tx.PumpReadingQuality != "" {
		// ✅ 修复：添加"timeout"和"timeout_calculated"作为有效的泵码质量值
		validQualities := []string{"good", "poor", "bad", "missing", "timeout", "timeout_calculated"}
		isValid := false
		for _, quality := range validQualities {
			if tx.PumpReadingQuality == quality {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("invalid pump reading quality: %s", tx.PumpReadingQuality)
		}
	}

	// 验证泵码数据来源字段
	if tx.PumpReadingSource != "" {
		// ✅ 修复：添加更多有效的泵码来源
		validSources := []string{"DC101", "DC2_INITIAL", "MANUAL", "CALCULATED", "ESTIMATED", "TIMEOUT", "TIMEOUT_CALCULATION"}
		isValid := false
		for _, source := range validSources {
			if tx.PumpReadingSource == source {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("invalid pump reading source: %s", tx.PumpReadingSource)
		}
	}

	// ✅ 宽松验证：允许部分泵码数据存在
	// 第一阶段：只有基础交易数据，泵码可能为空
	// 第二阶段：有DC101泵码数据，质量会提升

	// 检查体积泵码逻辑合理性（如果都存在的话）
	if tx.StartPumpVolumeReading != nil && tx.EndPumpVolumeReading != nil {
		if tx.EndPumpVolumeReading.LessThan(*tx.StartPumpVolumeReading) {
			return fmt.Errorf("end volume reading (%v) cannot be less than start volume reading (%v)",
				tx.EndPumpVolumeReading.String(), tx.StartPumpVolumeReading.String())
		}
	}

	// 检查金额泵码逻辑合理性（如果都存在的话）
	if tx.StartPumpAmountReading != nil && tx.EndPumpAmountReading != nil {
		if tx.EndPumpAmountReading.LessThan(*tx.StartPumpAmountReading) {
			return fmt.Errorf("end amount reading (%v) cannot be less than start amount reading (%v)",
				tx.EndPumpAmountReading.String(), tx.StartPumpAmountReading.String())
		}
	}

	// ✅ 对于第一阶段的交易（DC2_INITIAL），不强制要求完整的泵码数据
	if tx.PumpReadingSource == "DC2_INITIAL" {
		// 第一阶段交易，允许泵码为空
		return nil
	}

	return nil
}

// FromStandardTransaction 将标准交易转换为DART交易（可选，用于反向转换）
func (c *DARTTransactionConverter) FromStandardTransaction(standardTx *models.Transaction) (*models.DARTTransaction, error) {
	if standardTx == nil {
		return nil, fmt.Errorf("standard transaction is nil")
	}

	// 解析喷嘴ID
	var nozzleID byte = 1 // 默认值
	if standardTx.NozzleID != "" {
		if parsed, err := fmt.Sscanf(standardTx.NozzleID, "%d", &nozzleID); err != nil || parsed != 1 {
			c.logger.Warn("Failed to parse nozzle ID, using default",
				zap.String("nozzle_id", standardTx.NozzleID),
				zap.Error(err))
		}
	}

	// Extract fuel grade ID
	var fuelGradeID string
	if standardTx.FuelGradeID != nil {
		fuelGradeID = *standardTx.FuelGradeID
	}

	// 创建DART交易
	dartTx := &models.DARTTransaction{
		ID:               standardTx.ID,
		DeviceID:         standardTx.DeviceID,
		NozzleID:         standardTx.NozzleID,
		OperatorID:       standardTx.OperatorID,
		StartTime:        standardTx.CreatedAt,
		LastUpdateTime:   standardTx.UpdatedAt,

		// 交易数据
		Volume:      standardTx.ActualVolume,
		Amount:      standardTx.ActualAmount,
		UnitPrice:   standardTx.UnitPrice,
		FuelGradeID: fuelGradeID,

		// 任务6新增：泵码数据反向映射
		PumpVolumeReading: standardTx.StartPumpVolumeReading,
		PumpAmountReading: standardTx.StartPumpAmountReading,
		PumpReadingSource: standardTx.PumpReadingSource,

		// 状态转换
		Status: c.convertFromStandardStatus(standardTx.Status),
		Type:   models.DARTTransactionType(standardTx.Type),
	}

	// 填充时间信息
	if standardTx.StartedAt != nil {
		dartTx.StartTime = *standardTx.StartedAt
	}

	if standardTx.CompletedAt != nil {
		dartTx.EndTime = standardTx.CompletedAt
	}

	// 从扩展数据中提取DART元数据
	if len(standardTx.ExtraData) > 0 {
		var extraData map[string]interface{}
		if err := json.Unmarshal(standardTx.ExtraData, &extraData); err != nil {
			c.logger.Warn("Failed to unmarshal extra data", zap.Error(err))
		} else {
			if dartMetadata, ok := extraData["dart_metadata"].(map[string]interface{}); ok {
				if errMsg, ok := dartMetadata["error_message"].(string); ok {
					dartTx.ErrorMessage = errMsg
				}
			}
		}
	}

	return dartTx, nil
}

// convertFromStandardStatus 从标准状态转换为DART状态
func (c *DARTTransactionConverter) convertFromStandardStatus(standardStatus models.TransactionStatus) models.DARTTransactionStatus {
	switch standardStatus {
	case models.TransactionStatusPending:
		return models.DARTTransactionStatusInitializing
	case models.TransactionStatusStarted, models.TransactionStatusActive:
		return models.DARTTransactionStatusActive
	case models.TransactionStatusCompleted:
		return models.DARTTransactionStatusCompleted
	case models.TransactionStatusCancelled:
		return models.DARTTransactionStatusCancelled
	case models.TransactionStatusFailed:
		return models.DARTTransactionStatusError
	default:
		c.logger.Warn("Unknown standard transaction status, defaulting to initializing",
			zap.String("standard_status", string(standardStatus)))
		return models.DARTTransactionStatusInitializing
	}
}

// extractBasicFuelType 从Nozzle中提取基础油品类型 - 只使用真实数据
func (c *DARTTransactionConverter) extractBasicFuelType(nozzle *models.Nozzle) string {
	// 只使用Nozzle关联的FuelGrade的真实Type，不允许推断
	if nozzle.FuelGrade != nil && nozzle.FuelGrade.Type != "" {
		c.logger.Debug("Extracted fuel type from nozzle's FuelGrade",
			zap.String("nozzle_id", nozzle.ID),
			zap.String("fuel_type", nozzle.FuelGrade.Type))
		return nozzle.FuelGrade.Type
	}

	// 如果没有真实数据，返回空字符串
	c.logger.Warn("No fuel type available from nozzle's FuelGrade",
		zap.String("nozzle_id", nozzle.ID))
	return ""
}


