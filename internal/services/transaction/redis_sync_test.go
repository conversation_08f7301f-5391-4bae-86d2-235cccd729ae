package transaction

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// TestRedisSyncStatusStore 测试Redis同步状态存储
func TestRedisSyncStatusStore(t *testing.T) {
	// 跳过测试如果没有Redis连接
	if testing.Short() {
		t.<PERSON><PERSON>("Skipping Redis integration test in short mode")
	}

	// 创建Redis客户端（测试用）
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15, // 使用测试数据库
	})

	ctx := context.Background()
	
	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		t.Skipf("Redis not available: %v", err)
	}

	// 清理测试数据
	defer func() {
		client.FlushDB(ctx)
		client.Close()
	}()

	logger := zap.NewNop()
	store := NewRedisSyncStatusStoreFromClient(client, logger)

	// 测试保存状态
	status := &SyncStatus{
		SystemID:      "test_system",
		TransactionID: "test_tx_123",
		Status:        "pending",
		AttemptCount:  1,
		LastAttempt:   time.Now(),
	}

	err := store.Save(ctx, status)
	if err != nil {
		t.Fatalf("Failed to save status: %v", err)
	}

	// 测试获取状态
	retrieved, err := store.Get(ctx, "test_tx_123", "test_system")
	if err != nil {
		t.Fatalf("Failed to get status: %v", err)
	}

	if retrieved.SystemID != status.SystemID {
		t.Errorf("Expected SystemID %s, got %s", status.SystemID, retrieved.SystemID)
	}

	if retrieved.TransactionID != status.TransactionID {
		t.Errorf("Expected TransactionID %s, got %s", status.TransactionID, retrieved.TransactionID)
	}

	if retrieved.Status != status.Status {
		t.Errorf("Expected Status %s, got %s", status.Status, retrieved.Status)
	}

	// 测试列出状态
	statuses, err := store.List(ctx, "test_tx_123")
	if err != nil {
		t.Fatalf("Failed to list statuses: %v", err)
	}

	if len(statuses) != 1 {
		t.Errorf("Expected 1 status, got %d", len(statuses))
	}

	// 测试更新状态
	status.Status = "success"
	status.AttemptCount = 2
	err = store.Update(ctx, status)
	if err != nil {
		t.Fatalf("Failed to update status: %v", err)
	}

	// 验证更新
	updated, err := store.Get(ctx, "test_tx_123", "test_system")
	if err != nil {
		t.Fatalf("Failed to get updated status: %v", err)
	}

	if updated.Status != "success" {
		t.Errorf("Expected Status success, got %s", updated.Status)
	}

	if updated.AttemptCount != 2 {
		t.Errorf("Expected AttemptCount 2, got %d", updated.AttemptCount)
	}

	// 测试删除状态
	err = store.Delete(ctx, "test_tx_123", "test_system")
	if err != nil {
		t.Fatalf("Failed to delete status: %v", err)
	}

	// 验证删除
	_, err = store.Get(ctx, "test_tx_123", "test_system")
	if err == nil {
		t.Error("Expected error when getting deleted status, got nil")
	}
}

// TestSyncPublisherWithRedis 测试带Redis的同步发布器
func TestSyncPublisherWithRedis(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping Redis integration test in short mode")
	}

	// 创建Redis客户端
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})

	ctx := context.Background()
	
	if err := client.Ping(ctx).Err(); err != nil {
		t.Skipf("Redis not available: %v", err)
	}

	defer func() {
		client.FlushDB(ctx)
		client.Close()
	}()

	logger := zap.NewNop()
	
	// 创建Redis状态存储
	statusStore := NewRedisSyncStatusStoreFromClient(client, logger)
	
	// 创建同步发布器配置
	config := SyncPublisherConfig{
		MaxConcurrency:    2,
		SyncTimeout:       10 * time.Second,
		EnableRetry:       true,
		DefaultMaxRetries: 2,
		DefaultRetryDelay: 1 * time.Second,
		EnableAsync:       false, // 测试中使用同步模式
		WorkerCount:       1,
		QueueSize:         10,
	}

	// 创建同步发布器
	publisher := NewSyncPublisher(config, statusStore, logger)

	// 验证发布器创建成功
	if publisher == nil {
		t.Fatal("Failed to create sync publisher")
	}

	// 测试回调设置
	callbackCalled := false
	publisher.SetExternalIDUpdateCallback(func(ctx context.Context, transactionID, externalID string) error {
		callbackCalled = true
		return nil
	})

	// 测试回调调用
	err := publisher.UpdateExternalTransactionID(ctx, "test_tx", "ext_123")
	if err != nil {
		t.Fatalf("Failed to call external ID update: %v", err)
	}

	if !callbackCalled {
		t.Error("Expected callback to be called, but it wasn't")
	}

	t.Log("Redis sync publisher test completed successfully")
}

// BenchmarkRedisSyncStatusStore 性能测试
func BenchmarkRedisSyncStatusStore(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping Redis benchmark in short mode")
	}

	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})

	ctx := context.Background()
	
	if err := client.Ping(ctx).Err(); err != nil {
		b.Skipf("Redis not available: %v", err)
	}

	defer client.Close()

	logger := zap.NewNop()
	store := NewRedisSyncStatusStoreFromClient(client, logger)

	status := &SyncStatus{
		SystemID:      "bench_system",
		TransactionID: "bench_tx",
		Status:        "pending",
		AttemptCount:  1,
		LastAttempt:   time.Now(),
	}

	b.ResetTimer()

	b.Run("Save", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			status.TransactionID = "bench_tx_" + string(rune(i))
			store.Save(ctx, status)
		}
	})

	b.Run("Get", func(b *testing.B) {
		// 先保存一个状态
		store.Save(ctx, status)
		
		for i := 0; i < b.N; i++ {
			store.Get(ctx, status.TransactionID, status.SystemID)
		}
	})
}
