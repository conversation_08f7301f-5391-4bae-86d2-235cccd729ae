package transaction

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/services/nozzle"
	v2 "fcc-service/internal/services/polling/v2"
	"fcc-service/pkg/models"
)

// TransactionEventBridge 交易事件桥接器
// 实现TransactionLifecycleListenerInterface接口，将TransactionLifecycleService的事件
// 转发到TransactionSyncPublisher进行上游系统发布
type TransactionEventBridge struct {
	syncPublisher  TransactionSyncPublisher
	dataCollector  TransactionDataCollector
	nozzleResolver nozzle.NozzleResolver
	repository     TransactionRepository // 🎯 添加Repository用于数据库更新
	logger         *zap.Logger
}

// NewTransactionEventBridge 创建交易事件桥接器
func NewTransactionEventBridge(
	syncPublisher TransactionSyncPublisher,
	dataCollector TransactionDataCollector,
	nozzleResolver nozzle.NozzleResolver,
	repository TransactionRepository, // 🎯 添加Repository参数
	logger *zap.Logger,
) v2.TransactionLifecycleListenerInterface {
	return &TransactionEventBridge{
		syncPublisher:  syncPublisher,
		dataCollector:  dataCollector,
		nozzleResolver: nozzleResolver,
		repository:     repository, // 🎯 注入Repository
		logger:         logger,
	}
}

// GetListenerID 返回监听器唯一标识
func (b *TransactionEventBridge) GetListenerID() string {
	return "transaction_event_bridge"
}

// OnTransactionCreated 处理交易创建事件
func (b *TransactionEventBridge) OnTransactionCreated(tx *models.Transaction) error {
	b.logger.Info("🌉 Bridge: Transaction created event received",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("status", string(tx.Status)))

	// 收集上下文并发布事件
	return b.publishTransactionEventWithContext(tx, TransactionEventCreated)
}

// OnTransactionUpdated 处理交易更新事件
// 🎯 优化：update 事件直接调用 publisher，不需要重新组装交易数据
func (b *TransactionEventBridge) OnTransactionUpdated(tx *models.Transaction) error {
	b.logger.Debug("🌉 Bridge: Transaction updated event received",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("status", string(tx.Status)))

	// 🎯 直接创建 TransactionEvent 并调用 publisher，避免重新组装
	event := &TransactionEvent{
		EventID:       fmt.Sprintf("%s-%s-%d", tx.ID, TransactionEventUpdated, time.Now().Unix()),
		EventType:     TransactionEventUpdated,
		TransactionID: tx.ID,
		Transaction:   tx,
		Timestamp:     time.Now(),
	}

	// 直接调用 publisher
	ctx := context.Background()
	if err := b.syncPublisher.PublishTransactionEvent(ctx, event); err != nil {
		b.logger.Error("Failed to publish transaction update event",
			zap.String("transaction_id", tx.ID),
			zap.Error(err))
		return err
	}

	b.logger.Debug("Successfully published transaction update event",
		zap.String("transaction_id", tx.ID),
		zap.String("event_id", event.EventID))

	return nil
}

// OnTransactionCompleted 处理交易完成事件
func (b *TransactionEventBridge) OnTransactionCompleted(tx *models.Transaction) error {
	b.logger.Info("🌉 [Bridge]: Transaction completed event received",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("status", string(tx.Status)),
		zap.String("actual_volume", tx.ActualVolume.String()),
		zap.String("actual_amount", tx.ActualAmount.String()))

	// 收集上下文并发布事件
	return b.publishTransactionEventWithContext(tx, TransactionEventCompleted)
}

// OnTransactionCancelled 处理交易取消事件
func (b *TransactionEventBridge) OnTransactionCancelled(tx *models.Transaction) error {
	b.logger.Info("🌉 Bridge: Transaction cancelled event received",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("status", string(tx.Status)))

	// 收集上下文并发布事件
	return b.publishTransactionEventWithContext(tx, TransactionEventCancelled)
}

// OnTransactionPending 处理交易进入等待泵码状态事件
func (b *TransactionEventBridge) OnTransactionPending(tx *models.Transaction) error {
	b.logger.Info("🌉 Bridge: Transaction pending event received",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("status", string(tx.Status)),
		zap.String("note", "Transaction waiting for DC101 pump counter verification"))

	// 对于 pending 状态，暂时不发布到上游系统，只记录日志
	// 这是一个内部状态，上游系统不需要感知这个中间状态
	b.logger.Debug("🌉 Bridge: Pending event logged but not published to upstream",
		zap.String("transaction_id", tx.ID),
		zap.String("reason", "Internal state - not relevant for upstream systems"))

	return nil
}

// publishTransactionEventWithContext 收集上下文并发布交易事件
func (b *TransactionEventBridge) publishTransactionEventWithContext(tx *models.Transaction, eventType TransactionEventType) error {
	ctx := context.Background()

	b.logger.Info("🌉 [Bridge] Starting transaction event bridge processing",
		zap.String("transaction_id", tx.ID),
		zap.String("event_type", string(eventType)),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("status", string(tx.Status)),
		zap.String("volume", tx.ActualVolume.String()),
		zap.String("amount", tx.ActualAmount.String()),
		// 🚀 迁移：添加泵码相关字段日志
		zap.String("pump_reading_source", tx.PumpReadingSource),
		zap.String("pump_reading_quality", tx.PumpReadingQuality),
		zap.Bool("pump_reading_validated", tx.PumpReadingValidated))

	// 🚀 Step 1: 解析 Nozzle 对象 - 迁移详细日志格式
	b.logger.Info("📍 [Bridge] Step 1: Resolving nozzle for transaction event",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID))

	// 🔧 修复：tx.NozzleID 已经是完整的 nozzle ID，直接使用 GetNozzleByID 查询
	// 不再需要解析为 nozzle number，因为 tx.NozzleID 格式为 "device_com7_pump01-nozzle-2"
	var nozzle *models.Nozzle
	var err error

	if tx.NozzleID != "" {
		// 直接通过完整的 nozzle ID 查询，避免复杂的字符串解析
		nozzle, err = b.nozzleResolver.GetNozzleByID(ctx, tx.NozzleID)
		if err != nil {
			b.logger.Warn("Failed to get nozzle by ID, this might indicate a missing nozzle record",
				zap.String("nozzle_id", tx.NozzleID),
				zap.String("device_id", tx.DeviceID),
				zap.Error(err))
			return err
		}
	} else {
		// 如果 nozzle ID 为空，使用默认值
		b.logger.Warn("Empty nozzle ID, using default nozzle number 1",
			zap.String("device_id", tx.DeviceID))
		nozzle, err = b.nozzleResolver.ResolveNozzle(ctx, tx.DeviceID, 1)
		if err != nil {
			return err
		}
	}

	b.logger.Info("✅ [Bridge] Step 1 SUCCESS: Nozzle resolved for transaction event",
		zap.String("transaction_id", tx.ID),
		zap.String("nozzle_id", nozzle.ID),
		zap.String("nozzle_name", nozzle.Name))

	// 🚀 Step 2: 收集交易上下文（容错性收集，始终返回有效上下文）
	b.logger.Info("📍 [Bridge] Step 2: Collecting transaction context data",
		zap.String("transaction_id", tx.ID))

	txContext := b.collectTransactionContextWithDefaults(ctx, tx, nozzle)

	b.logger.Info("✅ [Bridge] Step 2 SUCCESS: Transaction context collected",
		zap.String("transaction_id", tx.ID),
		zap.Bool("has_complete_context", txContext.HasCompleteContext()),
		zap.Int("defaults_used_count", txContext.GetDefaultsUsedCount()))

	// 🚀 Step 3: 异步发布事件到上游系统
	b.logger.Info("📍 [Bridge] Step 3: Publishing transaction event asynchronously",
		zap.String("transaction_id", tx.ID))

	go func() {
		eventCtx := context.Background()

		b.logger.Info("🌉 [Bridge] Publishing transaction event to upstream",
			zap.String("transaction_id", tx.ID),
			zap.String("event_type", string(eventType)),
			zap.String("device_id", tx.DeviceID),
			zap.String("nozzle_id", tx.NozzleID))

		// 创建事件 - 🚀 迁移：采用与PersistDARTTransaction相同的事件创建格式
		event := &TransactionEvent{
			EventID:       fmt.Sprintf("%s-%s-%d", tx.ID, eventType, time.Now().Unix()),
			EventType:     eventType,
			TransactionID: tx.ID,
			Transaction:   tx,
			Context:       txContext,
			Timestamp:     time.Now(),
			Source:        "fcc-service-bridge", // 保持桥接器的标识
			Version:       "1.0",
		}

		// 直接使用 TransactionSyncPublisher 发布事件
		if err := b.syncPublisher.PublishTransactionEvent(eventCtx, event); err != nil {
			b.logger.Error("❌ [Bridge] Step 3 FAILED: Failed to publish transaction event",
				zap.String("transaction_id", tx.ID),
				zap.String("event_type", string(eventType)),
				zap.String("event_id", event.EventID),
				zap.String("publisher_type", fmt.Sprintf("%T", b.syncPublisher)),
				zap.Error(err))
		} else {
			b.logger.Info("✅ [Bridge] Step 3 SUCCESS: Transaction event published successfully",
				zap.String("transaction_id", tx.ID),
				zap.String("event_type", string(eventType)),
				zap.String("event_id", event.EventID))
		}
	}()

	// 🚀 迁移：添加完整的成功总结日志（类似PersistDARTTransaction）
	b.logger.Info("🎉 [Bridge] Transaction event bridge processing completed successfully",
		zap.String("transaction_id", tx.ID),
		zap.String("event_type", string(eventType)),
		zap.String("nozzle_id", nozzle.ID),
		zap.String("controller_id", tx.ControllerID),
		zap.Bool("has_complete_context", txContext.HasCompleteContext()),
		zap.Int("defaults_used_count", txContext.GetDefaultsUsedCount()),
		// 🚀 迁移：泵码持久化日志字段
		zap.String("pump_reading_source", tx.PumpReadingSource),
		zap.String("pump_reading_quality", tx.PumpReadingQuality),
		zap.Bool("pump_reading_validated", tx.PumpReadingValidated))

	return nil
}

// collectTransactionContextWithDefaults 容错性收集交易上下文信息
// 复用TransactionService中的逻辑，但适配标准Transaction模型
func (b *TransactionEventBridge) collectTransactionContextWithDefaults(ctx context.Context, tx *models.Transaction, nozzle *models.Nozzle) *TransactionContext {
	txContext := &TransactionContext{
		ExtraData: make(map[string]interface{}),
	}

	// 记录哪些上下文信息使用了默认值
	defaultsUsed := make(map[string]string)

	// 1. 收集设备信息
	deviceCtx, err := b.dataCollector.CollectDeviceInfo(ctx, tx.DeviceID)
	if err != nil {
		b.logger.Warn("🌉 Bridge: Failed to collect device info, using defaults",
			zap.String("device_id", tx.DeviceID),
			zap.Error(err))

		deviceCtx = &DeviceContext{
			DeviceID:     tx.DeviceID,
			DeviceName:   fmt.Sprintf("Device-%s", tx.DeviceID),
			DeviceType:   "fuel_pump",
			ControllerID: tx.ControllerID,
			StationID:    "default-station",
			IslandID:     "",
			Position:     "unknown",
			SerialNumber: "unknown",
			Model:        "unknown",
			Vendor:       "unknown",
		}
		defaultsUsed["device_context"] = "device collection failed"
	}
	txContext.Device = deviceCtx

	// 2. 收集站点信息
	var stationCtx *StationContext
	if deviceCtx.StationID != "" {
		stationCtx, err = b.dataCollector.CollectStationInfo(ctx, deviceCtx.StationID)
		if err != nil {
			b.logger.Warn("🌉 Bridge: Failed to collect station info, using defaults",
				zap.String("station_id", deviceCtx.StationID),
				zap.Error(err))

			stationCtx = &StationContext{
				StationID:   deviceCtx.StationID,
				StationName: fmt.Sprintf("Station-%s", deviceCtx.StationID),
				StationCode: deviceCtx.StationID,
				Region:      "unknown",
				Address:     "",
				CompanyID:   "default-company",
				CompanyName: "Unknown Company",
			}
			defaultsUsed["station_context"] = "station collection failed"
		}
		txContext.Station = stationCtx
	}

	// 3. 收集油品信息
	fuelCtx := b.collectEnhancedFuelGradeInfo(ctx, nozzle, tx, &defaultsUsed)
	txContext.FuelGrade = fuelCtx

	// 4. 收集操作员信息
	if tx.OperatorID != "" {
		operatorCtx, err := b.dataCollector.CollectOperatorInfo(ctx, tx.OperatorID)
		if err != nil {
			b.logger.Warn("🌉 Bridge: Failed to collect operator info, using defaults",
				zap.String("operator_id", tx.OperatorID),
				zap.Error(err))

			operatorCtx = &OperatorContext{
				OperatorID:    tx.OperatorID,
				OperatorName:  fmt.Sprintf("Employee-%s", tx.OperatorID),
				OperatorCode:  tx.OperatorID,
				Department:    "unknown",
				Role:          "operator",
				ShiftID:       "",
				WorkstationID: "",
			}
			defaultsUsed["operator_context"] = "operator collection failed"
		}
		txContext.Operator = operatorCtx
	} else {
		txContext.Operator = &OperatorContext{
			OperatorID:    "system",
			OperatorName:  "System Auto",
			OperatorCode:  "SYS",
			Department:    "system",
			Role:          "auto",
			ShiftID:       "",
			WorkstationID: "",
		}
		defaultsUsed["operator_context"] = "no operator ID provided"
	}

	// 5. 记录默认值使用情况
	if len(defaultsUsed) > 0 {
		txContext.ExtraData["context_defaults_used"] = defaultsUsed
	}

	// 6. 添加收集时间戳和状态
	txContext.ExtraData["context_collection"] = map[string]interface{}{
		"collected_at":    time.Now(),
		"collection_mode": "bridge_fault_tolerant",
		"defaults_count":  len(defaultsUsed),
	}

	return txContext
}

// collectEnhancedFuelGradeInfo 收集油品信息 - 适配标准Transaction模型
func (b *TransactionEventBridge) collectEnhancedFuelGradeInfo(ctx context.Context, nozzle *models.Nozzle, tx *models.Transaction, defaultsUsed *map[string]string) *FuelGradeContext {
	b.logger.Debug("🌉 Bridge: Collecting fuel grade info",
		zap.String("nozzle_id", nozzle.ID),
		zap.String("transaction_fuel_grade_id", b.safeGetTransactionFuelGradeID(tx)))

	// 策略1：优先使用Transaction中的油品信息
	if tx.FuelGradeID != nil && *tx.FuelGradeID != "" {
		b.logger.Debug("🌉 Bridge: Using fuel grade info from transaction",
			zap.String("nozzle_id", nozzle.ID),
			zap.String("fuel_grade_id", *tx.FuelGradeID),
			zap.String("fuel_grade_name", tx.FuelGradeName))

		return &FuelGradeContext{
			FuelGradeID:   *tx.FuelGradeID,
			FuelGradeName: tx.FuelGradeName,
			FuelType:      tx.FuelType,
			OctaneRating:  0, // Transaction模型中没有octane字段
		}
	}

	// 策略2：使用Nozzle中的完整FuelGrade信息
	if nozzle.FuelGrade != nil {
		b.logger.Debug("🌉 Bridge: Using fuel grade info from nozzle",
			zap.String("nozzle_id", nozzle.ID),
			zap.String("fuel_grade_id", nozzle.FuelGrade.ID),
			zap.String("fuel_grade_name", nozzle.FuelGrade.Name))

		return &FuelGradeContext{
			FuelGradeID:   nozzle.FuelGrade.ID,
			FuelGradeName: nozzle.FuelGrade.Name,
			FuelType:      nozzle.FuelGrade.Type,
			OctaneRating:  nozzle.FuelGrade.Octane,
		}
	}

	// 策略3：使用DataCollector尝试获取
	fuelCtx, err := b.dataCollector.CollectFuelGradeInfo(ctx, nozzle.ID)
	if err == nil {
		b.logger.Debug("🌉 Bridge: Successfully collected fuel grade info via DataCollector",
			zap.String("nozzle_id", nozzle.ID),
			zap.String("fuel_grade_id", fuelCtx.FuelGradeID))
		return fuelCtx
	}

	// 策略4：返回空值
	b.logger.Warn("🌉 Bridge: No fuel grade information available",
		zap.String("nozzle_id", nozzle.ID),
		zap.Error(err))

	(*defaultsUsed)["fuel_grade_context"] = "no fuel grade information available"

	return &FuelGradeContext{
		FuelGradeID:   "",
		FuelGradeName: "",
		FuelType:      "",
		OctaneRating:  0,
	}
}

// safeGetTransactionFuelGradeID 安全获取交易中的油品等级ID
func (b *TransactionEventBridge) safeGetTransactionFuelGradeID(tx *models.Transaction) string {
	if tx.FuelGradeID != nil {
		return *tx.FuelGradeID
	}
	return ""
}

// 🆕 DC101优先验证机制：处理泵码质量改进事件
func (b *TransactionEventBridge) OnPumpReadingQualityImproved(tx *models.Transaction, oldQuality, newQuality string) error {
	b.logger.Info("🌉 Bridge: Pump reading quality improved event received",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("old_quality", oldQuality),
		zap.String("new_quality", newQuality),
		zap.String("status", string(tx.Status)))

	// 对于泵码质量改进事件，我们可以选择：
	// 1. 发布一个专门的质量改进事件
	// 2. 或者作为交易更新事件处理
	// 这里选择作为更新事件处理，因为本质上是交易数据的更新
	return b.publishTransactionEventWithContext(tx, TransactionEventUpdated)
}

// 🎯 新增：处理外部交易ID更新的方法
// UpdateExternalTransactionID 更新交易的外部系统ID和同步状态
func (b *TransactionEventBridge) UpdateExternalTransactionID(ctx context.Context, transactionID, externalID string) error {
	b.logger.Info("🌉 Bridge: Updating external transaction ID",
		zap.String("transaction_id", transactionID),
		zap.String("external_transaction_id", externalID))

	// 获取现有交易
	tx, err := b.repository.GetByID(ctx, transactionID)
	if err != nil {
		b.logger.Error("Failed to get transaction for external ID update",
			zap.String("transaction_id", transactionID),
			zap.Error(err))
		return fmt.Errorf("failed to get transaction: %w", err)
	}

	// 更新外部交易ID和同步状态
	tx.ExternalTransactionID = &externalID
	tx.SyncStatus = "synced"

	// 保存更新
	if err := b.repository.Update(ctx, tx); err != nil {
		b.logger.Error("Failed to update transaction with external ID",
			zap.String("transaction_id", transactionID),
			zap.String("external_transaction_id", externalID),
			zap.Error(err))
		return fmt.Errorf("failed to update transaction: %w", err)
	}

	b.logger.Info("✅ Bridge: External transaction ID updated successfully",
		zap.String("transaction_id", transactionID),
		zap.String("external_transaction_id", externalID),
		zap.String("sync_status", "synced"))

	return nil
}

// 🎯 新增：标记交易为需要同步状态
// MarkTransactionAsNeedsSync 标记交易为需要重新同步
func (b *TransactionEventBridge) MarkTransactionAsNeedsSync(ctx context.Context, transactionID string) error {
	b.logger.Info("🌉 Bridge: Marking transaction as needs_sync",
		zap.String("transaction_id", transactionID))

	// 获取现有交易
	tx, err := b.repository.GetByID(ctx, transactionID)
	if err != nil {
		b.logger.Error("Failed to get transaction for sync status update",
			zap.String("transaction_id", transactionID),
			zap.Error(err))
		return fmt.Errorf("failed to get transaction: %w", err)
	}

	// 更新同步状态
	tx.SyncStatus = "needs_sync"

	// 保存更新
	if err := b.repository.Update(ctx, tx); err != nil {
		b.logger.Error("Failed to update transaction sync status",
			zap.String("transaction_id", transactionID),
			zap.Error(err))
		return fmt.Errorf("failed to update transaction: %w", err)
	}

	b.logger.Info("✅ Bridge: Transaction marked as needs_sync",
		zap.String("transaction_id", transactionID),
		zap.String("sync_status", "needs_sync"))

	return nil
}
