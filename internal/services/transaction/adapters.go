package transaction

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	"fcc-service/internal/services/nozzle"
	"fcc-service/pkg/api"
	"fcc-service/pkg/models"
)

// DeviceManagerAdapter 设备管理器适配器 - 解决mutex值传递问题
type DeviceManagerAdapter struct {
	deviceManager api.DeviceManager // 使用接口，避免直接引用包含mutex的实现
}

// NewDeviceManagerAdapter 创建设备管理器适配器
func NewDeviceManagerAdapter(deviceManager api.DeviceManager) DeviceInfoProvider {
	return &DeviceManagerAdapter{
		deviceManager: deviceManager,
	}
}

// GetDevice 获取设备信息
func (dma *DeviceManagerAdapter) GetDevice(ctx context.Context, deviceID string) (*models.Device, error) {
	return dma.deviceManager.GetDevice(ctx, deviceID)
}

// GetController 获取控制器信息
func (dma *DeviceManagerAdapter) GetController(ctx context.Context, controllerID string) (*models.Controller, error) {
	return dma.deviceManager.GetController(ctx, controllerID)
}

// NozzleServiceAdapter 喷嘴服务适配器
type NozzleServiceAdapter struct {
	nozzleService nozzle.ServiceV2 // 🚀 使用正确的接口类型
}

// NewNozzleServiceAdapter 创建喷嘴服务适配器
func NewNozzleServiceAdapter(nozzleService nozzle.ServiceV2) NozzleInfoProvider {
	return &NozzleServiceAdapter{
		nozzleService: nozzleService,
	}
}

// GetNozzle 获取喷嘴信息
func (nsa *NozzleServiceAdapter) GetNozzle(ctx context.Context, deviceID string, number byte) (*models.Nozzle, error) {
	return nsa.nozzleService.GetNozzle(ctx, deviceID, number)
}

// TransactionServiceFactory 交易服务工厂 - 处理复杂的依赖注入
type TransactionServiceFactory struct{}

// NewTransactionServiceFactory 创建交易服务工厂
func NewTransactionServiceFactory() *TransactionServiceFactory {
	return &TransactionServiceFactory{}
}

// CreateTransactionService 创建完整的交易服务
func (tsf *TransactionServiceFactory) CreateTransactionService(
	repository TransactionRepository,
	deviceManager api.DeviceManager,
	nozzleService nozzle.ServiceV2, // 🚀 使用正确的接口类型
	logger *zap.Logger,
) TransactionService {
	// 创建适配器，避免直接传递包含mutex的结构
	deviceAdapter := NewDeviceManagerAdapter(deviceManager)
	nozzleAdapter := NewNozzleServiceAdapter(nozzleService)

	// 创建数据收集器
	dataCollector := NewDataCollector(deviceAdapter, nozzleAdapter, logger)

	// 🎯 创建生产级同步发布器（支持完整的回调功能）
	syncConfig := SyncPublisherConfig{
		MaxConcurrency:    5,
		SyncTimeout:       30 * time.Second,
		EnableRetry:       true,
		DefaultMaxRetries: 3,
		DefaultRetryDelay: 5 * time.Second,
		EnableAsync:       true,
		WorkerCount:       3,
		QueueSize:         100,
	}
	statusStore := NewMemorySyncStatusStore()
	syncPublisher := NewSyncPublisher(syncConfig, statusStore, logger)

	// 创建交易服务
	// 🚀 需要创建 NozzleResolver
	nozzleResolver := nozzle.NewNozzleResolver(nozzleService, logger)

	return NewTransactionService(repository, dataCollector, syncPublisher, nozzleResolver, logger)
}

// CreateTransactionServiceWithRedis 创建带Redis支持的完整交易服务
func (tsf *TransactionServiceFactory) CreateTransactionServiceWithRedis(
	repository TransactionRepository,
	deviceManager api.DeviceManager,
	nozzleService nozzle.ServiceV2,
	redisClient *redis.Client,
	logger *zap.Logger,
) TransactionService {
	// 创建适配器，避免直接传递包含mutex的结构
	deviceAdapter := NewDeviceManagerAdapter(deviceManager)
	nozzleAdapter := NewNozzleServiceAdapter(nozzleService)

	// 创建数据收集器
	dataCollector := NewDataCollector(deviceAdapter, nozzleAdapter, logger)

	// 🎯 创建Redis版本的同步发布器
	syncConfig := SyncPublisherConfig{
		MaxConcurrency:    5,
		SyncTimeout:       30 * time.Second,
		EnableRetry:       true,
		DefaultMaxRetries: 3,
		DefaultRetryDelay: 5 * time.Second,
		EnableAsync:       true,
		WorkerCount:       3,
		QueueSize:         100,
	}

	// 使用Redis作为状态存储
	statusStore := NewRedisSyncStatusStoreFromClient(redisClient, logger)
	syncPublisher := NewSyncPublisher(syncConfig, statusStore, logger)

	// 创建交易服务
	nozzleResolver := nozzle.NewNozzleResolver(nozzleService, logger)

	return NewTransactionService(repository, dataCollector, syncPublisher, nozzleResolver, logger)
}
