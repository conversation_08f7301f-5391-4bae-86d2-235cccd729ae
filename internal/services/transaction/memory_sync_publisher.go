package transaction

import (
	"context"
	"fmt"

	"go.uber.org/zap"
)

// memorySyncPublisher 内存版本的同步发布器 - 用于开发和简单场景
type memorySyncPublisher struct {
	logger         *zap.Logger
	syncStrategies map[string]SyncStrategy
	statusStore    map[string][]*SyncStatus // transactionID -> syncStatuses
}

// NewMemorySyncPublisher 创建内存同步发布器
func NewMemorySyncPublisher(logger *zap.Logger) TransactionSyncPublisher {
	return &memorySyncPublisher{
		logger:         logger,
		syncStrategies: make(map[string]SyncStrategy),
		statusStore:    make(map[string][]*SyncStatus),
	}
}

// PublishTransactionEvent 发布交易事件
func (msp *memorySyncPublisher) PublishTransactionEvent(ctx context.Context, event *TransactionEvent) error {
	if event == nil {
		return fmt.Errorf("transaction event is nil")
	}

	msp.logger.Info("Publishing transaction event",
		zap.String("event_id", event.EventID),
		zap.String("event_type", string(event.EventType)),
		zap.String("transaction_id", event.TransactionID))

	// 异步处理同步策略（避免阻塞主流程）
	go msp.processEventAsync(ctx, event)

	return nil
}

// RegisterSyncStrategy 注册同步策略
func (msp *memorySyncPublisher) RegisterSyncStrategy(systemID string, strategy SyncStrategy) {
	msp.syncStrategies[systemID] = strategy
	msp.logger.Info("Sync strategy registered", zap.String("system_id", systemID))
}

// GetSyncStatus 获取同步状态
func (msp *memorySyncPublisher) GetSyncStatus(ctx context.Context, transactionID string) ([]*SyncStatus, error) {
	if statuses, exists := msp.statusStore[transactionID]; exists {
		return statuses, nil
	}
	return []*SyncStatus{}, nil
}

// processEventAsync 异步处理事件
func (msp *memorySyncPublisher) processEventAsync(ctx context.Context, event *TransactionEvent) {
	for systemID, strategy := range msp.syncStrategies {
		// 检查策略是否支持该事件类型
		if !msp.supportsEvent(strategy, event.EventType) {
			continue
		}

		// 创建同步状态
		status := &SyncStatus{
			SystemID:      systemID,
			TransactionID: event.TransactionID,
			Status:        "pending",
			AttemptCount:  0,
		}

		// 尝试同步
		if err := strategy.Sync(ctx, event); err != nil {
			status.Status = "failed"
			status.LastError = err.Error()
			msp.logger.Warn("Sync failed",
				zap.String("system_id", systemID),
				zap.String("transaction_id", event.TransactionID),
				zap.Error(err))
		} else {
			status.Status = "success"
			msp.logger.Info("Sync successful",
				zap.String("system_id", systemID),
				zap.String("transaction_id", event.TransactionID))
		}

		// 存储状态
		msp.storeStatus(event.TransactionID, status)
	}
}

// supportsEvent 检查策略是否支持事件类型
func (msp *memorySyncPublisher) supportsEvent(strategy SyncStrategy, eventType TransactionEventType) bool {
	supportedEvents := strategy.SupportedEvents()
	for _, supported := range supportedEvents {
		if supported == eventType {
			return true
		}
	}
	return false
}

// storeStatus 存储同步状态
func (msp *memorySyncPublisher) storeStatus(transactionID string, status *SyncStatus) {
	if _, exists := msp.statusStore[transactionID]; !exists {
		msp.statusStore[transactionID] = make([]*SyncStatus, 0)
	}
	msp.statusStore[transactionID] = append(msp.statusStore[transactionID], status)
}

// 🎯 设置外部ID更新回调 - 内存版本简单实现
func (msp *memorySyncPublisher) SetExternalIDUpdateCallback(callback func(ctx context.Context, transactionID, externalID string) error) {
	// 内存版本暂时不需要实现，只记录日志
	msp.logger.Info("External ID update callback set for memory sync publisher")
}

// 🎯 设置同步状态更新回调 - 内存版本简单实现
func (msp *memorySyncPublisher) SetSyncStatusUpdateCallback(callback func(ctx context.Context, transactionID, status string) error) {
	// 内存版本暂时不需要实现，只记录日志
	msp.logger.Info("Sync status update callback set for memory sync publisher")
}

// 🎯 调用外部ID更新回调 - 内存版本简单实现
func (msp *memorySyncPublisher) UpdateExternalTransactionID(ctx context.Context, transactionID, externalID string) error {
	// 内存版本只记录日志，不实际更新数据库
	msp.logger.Info("External transaction ID update requested",
		zap.String("transaction_id", transactionID),
		zap.String("external_id", externalID))
	return nil
}

// 🎯 调用同步状态更新回调 - 内存版本简单实现
func (msp *memorySyncPublisher) UpdateSyncStatus(ctx context.Context, transactionID, status string) error {
	// 内存版本只记录日志，不实际更新数据库
	msp.logger.Info("Sync status update requested",
		zap.String("transaction_id", transactionID),
		zap.String("status", status))
	return nil
}
