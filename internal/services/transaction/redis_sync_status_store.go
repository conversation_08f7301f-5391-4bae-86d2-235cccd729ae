package transaction

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// redisSyncStatusStore Redis实现的同步状态存储
type redisSyncStatusStore struct {
	client    redis.UniversalClient
	logger    *zap.Logger
	keyPrefix string
	ttl       time.Duration
}

// NewRedisSyncStatusStore 创建Redis同步状态存储
func NewRedisSyncStatusStore(client redis.UniversalClient, logger *zap.Logger) SyncStatusStore {
	return &redisSyncStatusStore{
		client:    client,
		logger:    logger,
		keyPrefix: "fcc:sync_status:",
		ttl:       24 * time.Hour, // 状态保留24小时
	}
}

// NewRedisSyncStatusStoreFromClient 从普通Redis客户端创建状态存储
func NewRedisSyncStatusStoreFromClient(client *redis.Client, logger *zap.Logger) SyncStatusStore {
	return &redisSyncStatusStore{
		client:    client, // *redis.Client 实现了 redis.UniversalClient 接口
		logger:    logger,
		keyPrefix: "fcc:sync_status:",
		ttl:       24 * time.Hour,
	}
}

// NewRedisSyncStatusStoreWithConfig 创建带配置的Redis同步状态存储
func NewRedisSyncStatusStoreWithConfig(client redis.UniversalClient, logger *zap.Logger, keyPrefix string, ttl time.Duration) SyncStatusStore {
	return &redisSyncStatusStore{
		client:    client,
		logger:    logger,
		keyPrefix: keyPrefix,
		ttl:       ttl,
	}
}

// Save 保存同步状态
func (r *redisSyncStatusStore) Save(ctx context.Context, status *SyncStatus) error {
	key := r.getStatusKey(status.TransactionID, status.SystemID)

	// 序列化状态
	data, err := json.Marshal(status)
	if err != nil {
		r.logger.Error("Failed to marshal sync status",
			zap.String("transaction_id", status.TransactionID),
			zap.String("system_id", status.SystemID),
			zap.Error(err))
		return fmt.Errorf("failed to marshal sync status: %w", err)
	}

	// 保存到Redis
	if err := r.client.Set(ctx, key, data, r.ttl).Err(); err != nil {
		r.logger.Error("Failed to save sync status to Redis",
			zap.String("key", key),
			zap.String("transaction_id", status.TransactionID),
			zap.String("system_id", status.SystemID),
			zap.Error(err))
		return fmt.Errorf("failed to save sync status to Redis: %w", err)
	}

	// 添加到交易的状态列表
	listKey := r.getTransactionListKey(status.TransactionID)
	if err := r.client.SAdd(ctx, listKey, status.SystemID).Err(); err != nil {
		r.logger.Warn("Failed to add to transaction status list",
			zap.String("list_key", listKey),
			zap.String("system_id", status.SystemID),
			zap.Error(err))
	}

	// 设置列表的TTL
	r.client.Expire(ctx, listKey, r.ttl)

	r.logger.Debug("Sync status saved to Redis",
		zap.String("transaction_id", status.TransactionID),
		zap.String("system_id", status.SystemID),
		zap.String("status", status.Status))

	return nil
}

// Get 获取指定交易和系统的同步状态
func (r *redisSyncStatusStore) Get(ctx context.Context, transactionID, systemID string) (*SyncStatus, error) {
	key := r.getStatusKey(transactionID, systemID)

	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("sync status not found for transaction %s, system %s", transactionID, systemID)
		}
		r.logger.Error("Failed to get sync status from Redis",
			zap.String("key", key),
			zap.String("transaction_id", transactionID),
			zap.String("system_id", systemID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get sync status from Redis: %w", err)
	}

	var status SyncStatus
	if err := json.Unmarshal([]byte(data), &status); err != nil {
		r.logger.Error("Failed to unmarshal sync status",
			zap.String("transaction_id", transactionID),
			zap.String("system_id", systemID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to unmarshal sync status: %w", err)
	}

	return &status, nil
}

// List 获取指定交易的所有同步状态
func (r *redisSyncStatusStore) List(ctx context.Context, transactionID string) ([]*SyncStatus, error) {
	listKey := r.getTransactionListKey(transactionID)

	// 获取所有系统ID
	systemIDs, err := r.client.SMembers(ctx, listKey).Result()
	if err != nil {
		if err == redis.Nil {
			return []*SyncStatus{}, nil
		}
		r.logger.Error("Failed to get system IDs from Redis",
			zap.String("list_key", listKey),
			zap.String("transaction_id", transactionID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get system IDs from Redis: %w", err)
	}

	if len(systemIDs) == 0 {
		return []*SyncStatus{}, nil
	}

	// 批量获取状态
	var statuses []*SyncStatus
	for _, systemID := range systemIDs {
		status, err := r.Get(ctx, transactionID, systemID)
		if err != nil {
			r.logger.Warn("Failed to get sync status for system",
				zap.String("transaction_id", transactionID),
				zap.String("system_id", systemID),
				zap.Error(err))
			continue
		}
		statuses = append(statuses, status)
	}

	r.logger.Debug("Listed sync statuses from Redis",
		zap.String("transaction_id", transactionID),
		zap.Int("count", len(statuses)))

	return statuses, nil
}

// Update 更新同步状态
func (r *redisSyncStatusStore) Update(ctx context.Context, status *SyncStatus) error {
	// Redis中的更新就是重新保存
	return r.Save(ctx, status)
}

// Delete 删除同步状态
func (r *redisSyncStatusStore) Delete(ctx context.Context, transactionID, systemID string) error {
	key := r.getStatusKey(transactionID, systemID)
	listKey := r.getTransactionListKey(transactionID)

	// 删除状态
	if err := r.client.Del(ctx, key).Err(); err != nil {
		r.logger.Error("Failed to delete sync status from Redis",
			zap.String("key", key),
			zap.String("transaction_id", transactionID),
			zap.String("system_id", systemID),
			zap.Error(err))
		return fmt.Errorf("failed to delete sync status from Redis: %w", err)
	}

	// 从列表中移除
	if err := r.client.SRem(ctx, listKey, systemID).Err(); err != nil {
		r.logger.Warn("Failed to remove from transaction status list",
			zap.String("list_key", listKey),
			zap.String("system_id", systemID),
			zap.Error(err))
	}

	r.logger.Debug("Sync status deleted from Redis",
		zap.String("transaction_id", transactionID),
		zap.String("system_id", systemID))

	return nil
}

// getStatusKey 生成状态存储的Redis key
func (r *redisSyncStatusStore) getStatusKey(transactionID, systemID string) string {
	return fmt.Sprintf("%s%s:%s", r.keyPrefix, transactionID, systemID)
}

// getTransactionListKey 生成交易状态列表的Redis key
func (r *redisSyncStatusStore) getTransactionListKey(transactionID string) string {
	return fmt.Sprintf("%slist:%s", r.keyPrefix, transactionID)
}

// Ping 检查Redis连接
func (r *redisSyncStatusStore) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭Redis连接
func (r *redisSyncStatusStore) Close() error {
	return r.client.Close()
}
