package transaction

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// syncPublisher 同步发布器实现
type syncPublisher struct {
	strategies  map[string]SyncStrategy
	statusStore SyncStatusStore
	logger      *zap.Logger

	// 并发控制
	mu sync.RWMutex

	// 配置
	config SyncPublisherConfig

	// 🎯 外部ID更新回调
	externalIDUpdateCallback func(ctx context.Context, transactionID, externalID string) error
	syncStatusUpdateCallback func(ctx context.Context, transactionID, status string) error
}

// SyncPublisherConfig 同步发布器配置
type SyncPublisherConfig struct {
	// 并发设置
	MaxConcurrency int `json:"max_concurrency"` // 最大并发同步数

	// 超时设置
	SyncTimeout time.Duration `json:"sync_timeout"` // 单个同步超时

	// 重试设置
	EnableRetry       bool          `json:"enable_retry"`
	DefaultMaxRetries int           `json:"default_max_retries"`
	DefaultRetryDelay time.Duration `json:"default_retry_delay"`

	// 异步处理
	EnableAsync bool `json:"enable_async"` // 是否启用异步同步
	WorkerCount int  `json:"worker_count"` // 异步工作协程数
	QueueSize   int  `json:"queue_size"`   // 异步队列大小
}

// SyncStatusStore 同步状态存储接口
type SyncStatusStore interface {
	Save(ctx context.Context, status *SyncStatus) error
	Get(ctx context.Context, transactionID, systemID string) (*SyncStatus, error)
	List(ctx context.Context, transactionID string) ([]*SyncStatus, error)
	Update(ctx context.Context, status *SyncStatus) error
	Delete(ctx context.Context, transactionID, systemID string) error
}

// NewSyncPublisher 创建同步发布器
func NewSyncPublisher(config SyncPublisherConfig, statusStore SyncStatusStore, logger *zap.Logger) TransactionSyncPublisher {
	publisher := &syncPublisher{
		strategies:  make(map[string]SyncStrategy),
		statusStore: statusStore,
		logger:      logger,
		config:      config,
	}

	// 启动异步工作协程
	if config.EnableAsync {
		publisher.startAsyncWorkers()
	}

	return publisher
}

// RegisterSyncStrategy 注册同步策略
func (sp *syncPublisher) RegisterSyncStrategy(systemID string, strategy SyncStrategy) {
	sp.mu.Lock()
	defer sp.mu.Unlock()

	sp.strategies[systemID] = strategy

	// 🎯 如果是HTTPSyncStrategy，设置SyncPublisher引用
	if httpStrategy, ok := strategy.(*HTTPSyncStrategy); ok {
		httpStrategy.SetSyncPublisher(sp)
		sp.logger.Info("HTTPSyncStrategy configured with SyncPublisher reference",
			zap.String("system_id", systemID))
	}

	sp.logger.Info("Sync strategy registered",
		zap.String("system_id", systemID),
		zap.Strings("supported_events", eventTypesToStrings(strategy.SupportedEvents())))
}

// PublishTransactionEvent 发布交易事件
func (sp *syncPublisher) PublishTransactionEvent(ctx context.Context, event *TransactionEvent) error {
	sp.logger.Info("Publishing transaction event",
		zap.String("event_id", event.EventID),
		zap.String("event_type", string(event.EventType)),
		zap.String("transaction_id", event.TransactionID))

	sp.mu.RLock()
	strategies := make(map[string]SyncStrategy)
	for systemID, strategy := range sp.strategies {
		strategies[systemID] = strategy
	}
	sp.mu.RUnlock()

	// 过滤支持该事件类型的策略
	applicableStrategies := sp.filterStrategiesByEvent(strategies, event.EventType)

	if len(applicableStrategies) == 0 {
		sp.logger.Debug("No applicable sync strategies for event",
			zap.String("event_type", string(event.EventType)))
		return nil
	}

	// 根据配置选择同步方式
	if sp.config.EnableAsync {
		return sp.publishAsync(ctx, event, applicableStrategies)
	} else {
		return sp.publishSync(ctx, event, applicableStrategies)
	}
}

// GetSyncStatus 获取同步状态
func (sp *syncPublisher) GetSyncStatus(ctx context.Context, transactionID string) ([]*SyncStatus, error) {
	return sp.statusStore.List(ctx, transactionID)
}

// 🎯 设置外部ID更新回调
func (sp *syncPublisher) SetExternalIDUpdateCallback(callback func(ctx context.Context, transactionID, externalID string) error) {
	sp.mu.Lock()
	defer sp.mu.Unlock()
	sp.externalIDUpdateCallback = callback
}

// 🎯 设置同步状态更新回调
func (sp *syncPublisher) SetSyncStatusUpdateCallback(callback func(ctx context.Context, transactionID, status string) error) {
	sp.mu.Lock()
	defer sp.mu.Unlock()
	sp.syncStatusUpdateCallback = callback
}

// 🎯 调用外部ID更新回调
func (sp *syncPublisher) UpdateExternalTransactionID(ctx context.Context, transactionID, externalID string) error {
	sp.mu.RLock()
	callback := sp.externalIDUpdateCallback
	sp.mu.RUnlock()

	if callback != nil {
		return callback(ctx, transactionID, externalID)
	}

	sp.logger.Warn("No external ID update callback registered",
		zap.String("transaction_id", transactionID),
		zap.String("external_id", externalID))
	return nil
}

// 🎯 调用同步状态更新回调
func (sp *syncPublisher) UpdateSyncStatus(ctx context.Context, transactionID, status string) error {
	sp.mu.RLock()
	callback := sp.syncStatusUpdateCallback
	sp.mu.RUnlock()

	if callback != nil {
		return callback(ctx, transactionID, status)
	}

	sp.logger.Warn("No sync status update callback registered",
		zap.String("transaction_id", transactionID),
		zap.String("status", status))
	return nil
}

// publishSync 同步发布
func (sp *syncPublisher) publishSync(ctx context.Context, event *TransactionEvent, strategies map[string]SyncStrategy) error {
	var errors []error

	// 创建超时上下文
	if sp.config.SyncTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, sp.config.SyncTimeout)
		defer cancel()
	}

	// 并发同步到各个系统
	if sp.config.MaxConcurrency > 1 {
		errors = sp.syncConcurrently(ctx, event, strategies)
	} else {
		errors = sp.syncSequentially(ctx, event, strategies)
	}

	// 处理错误
	if len(errors) > 0 {
		sp.logger.Error("Some sync operations failed",
			zap.String("transaction_id", event.TransactionID),
			zap.Int("error_count", len(errors)))

		// 返回第一个错误
		return errors[0]
	}

	return nil
}

// syncConcurrently 并发同步
func (sp *syncPublisher) syncConcurrently(ctx context.Context, event *TransactionEvent, strategies map[string]SyncStrategy) []error {
	type syncResult struct {
		systemID string
		error    error
	}

	resultChan := make(chan syncResult, len(strategies))
	semaphore := make(chan struct{}, sp.config.MaxConcurrency)

	// 启动同步协程
	for systemID, strategy := range strategies {
		go func(sysID string, strat SyncStrategy) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			err := sp.syncToSystem(ctx, event, sysID, strat)
			resultChan <- syncResult{systemID: sysID, error: err}
		}(systemID, strategy)
	}

	// 收集结果
	var errors []error
	for i := 0; i < len(strategies); i++ {
		result := <-resultChan
		if result.error != nil {
			errors = append(errors, result.error)
		}
	}

	return errors
}

// syncSequentially 顺序同步
func (sp *syncPublisher) syncSequentially(ctx context.Context, event *TransactionEvent, strategies map[string]SyncStrategy) []error {
	var errors []error

	for systemID, strategy := range strategies {
		if err := sp.syncToSystem(ctx, event, systemID, strategy); err != nil {
			errors = append(errors, err)
		}
	}

	return errors
}

// syncToSystem 同步到单个系统
func (sp *syncPublisher) syncToSystem(ctx context.Context, event *TransactionEvent, systemID string, strategy SyncStrategy) error {
	// 创建同步状态记录
	status := &SyncStatus{
		SystemID:      systemID,
		TransactionID: event.TransactionID,
		Status:        "pending",
		AttemptCount:  0,
		LastAttempt:   time.Now(),
	}

	// 保存初始状态
	if err := sp.statusStore.Save(ctx, status); err != nil {
		sp.logger.Warn("Failed to save initial sync status",
			zap.String("system_id", systemID),
			zap.String("transaction_id", event.TransactionID),
			zap.Error(err))
	}

	// 执行同步
	return sp.executeSync(ctx, event, strategy, status)
}

// executeSync 执行同步（包含重试逻辑）
func (sp *syncPublisher) executeSync(ctx context.Context, event *TransactionEvent, strategy SyncStrategy, status *SyncStatus) error {
	maxRetries := strategy.MaxRetries()
	if maxRetries <= 0 {
		maxRetries = sp.config.DefaultMaxRetries
	}

	var lastError error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		status.AttemptCount = attempt + 1
		status.LastAttempt = time.Now()

		// 执行同步
		if err := strategy.Sync(ctx, event); err != nil {
			lastError = err
			status.Status = "failed"
			status.LastError = err.Error()

			sp.logger.Warn("Sync attempt failed",
				zap.String("system_id", strategy.SystemID()),
				zap.String("transaction_id", event.TransactionID),
				zap.Int("attempt", attempt+1),
				zap.Error(err))

			// 检查是否应该重试
			if attempt < maxRetries && strategy.ShouldRetry(err) {
				status.Status = "retrying"

				// 计算下次重试时间
				retryDelay := strategy.RetryDelay()
				if retryDelay <= 0 {
					retryDelay = sp.config.DefaultRetryDelay
				}
				status.NextRetry = &[]time.Time{time.Now().Add(retryDelay)}[0]

				// 更新状态
				sp.statusStore.Update(ctx, status)

				// 等待重试延迟
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(retryDelay):
					continue
				}
			}
		} else {
			// 同步成功
			status.Status = "success"
			status.LastError = ""
			status.NextRetry = nil

			sp.logger.Info("Sync completed successfully",
				zap.String("system_id", strategy.SystemID()),
				zap.String("transaction_id", event.TransactionID),
				zap.Int("attempts", attempt+1))

			// 更新状态
			sp.statusStore.Update(ctx, status)
			return nil
		}
	}

	// 所有重试都失败了
	sp.logger.Error("Sync failed after all retries",
		zap.String("system_id", strategy.SystemID()),
		zap.String("transaction_id", event.TransactionID),
		zap.Int("max_retries", maxRetries),
		zap.Error(lastError))

	// 更新最终状态
	status.Status = "failed"
	sp.statusStore.Update(ctx, status)

	return lastError
}

// publishAsync 异步发布（预留实现）
func (sp *syncPublisher) publishAsync(ctx context.Context, event *TransactionEvent, strategies map[string]SyncStrategy) error {
	// TODO: 实现异步队列机制
	// 这里可以使用channel、消息队列等方式实现异步处理
	sp.logger.Debug("Async publish not implemented, falling back to sync",
		zap.String("transaction_id", event.TransactionID))

	return sp.publishSync(ctx, event, strategies)
}

// startAsyncWorkers 启动异步工作协程（预留实现）
func (sp *syncPublisher) startAsyncWorkers() {
	// TODO: 实现异步工作协程
	sp.logger.Debug("Async workers not implemented")
}

// filterStrategiesByEvent 根据事件类型过滤策略
func (sp *syncPublisher) filterStrategiesByEvent(strategies map[string]SyncStrategy, eventType TransactionEventType) map[string]SyncStrategy {
	filtered := make(map[string]SyncStrategy)

	for systemID, strategy := range strategies {
		supportedEvents := strategy.SupportedEvents()
		for _, supportedEvent := range supportedEvents {
			if supportedEvent == eventType {
				filtered[systemID] = strategy
				break
			}
		}
	}

	return filtered
}

// 辅助函数
func eventTypesToStrings(eventTypes []TransactionEventType) []string {
	strings := make([]string, len(eventTypes))
	for i, eventType := range eventTypes {
		strings[i] = string(eventType)
	}
	return strings
}

// 内存同步状态存储实现（用于开发和测试）
type memorySyncStatusStore struct {
	data map[string][]*SyncStatus // key: transactionID, value: statuses
	mu   sync.RWMutex
}

// NewMemorySyncStatusStore 创建内存同步状态存储
func NewMemorySyncStatusStore() SyncStatusStore {
	return &memorySyncStatusStore{
		data: make(map[string][]*SyncStatus),
	}
}

func (m *memorySyncStatusStore) Save(ctx context.Context, status *SyncStatus) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.data[status.TransactionID] == nil {
		m.data[status.TransactionID] = make([]*SyncStatus, 0)
	}

	m.data[status.TransactionID] = append(m.data[status.TransactionID], status)
	return nil
}

func (m *memorySyncStatusStore) Get(ctx context.Context, transactionID, systemID string) (*SyncStatus, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	statuses := m.data[transactionID]
	for _, status := range statuses {
		if status.SystemID == systemID {
			return status, nil
		}
	}

	return nil, fmt.Errorf("sync status not found")
}

func (m *memorySyncStatusStore) List(ctx context.Context, transactionID string) ([]*SyncStatus, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	statuses := m.data[transactionID]
	if statuses == nil {
		return []*SyncStatus{}, nil
	}

	// 返回副本
	result := make([]*SyncStatus, len(statuses))
	copy(result, statuses)
	return result, nil
}

func (m *memorySyncStatusStore) Update(ctx context.Context, status *SyncStatus) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	statuses := m.data[status.TransactionID]
	for i, s := range statuses {
		if s.SystemID == status.SystemID {
			statuses[i] = status
			return nil
		}
	}

	return fmt.Errorf("sync status not found for update")
}

func (m *memorySyncStatusStore) Delete(ctx context.Context, transactionID, systemID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	statuses := m.data[transactionID]
	for i, status := range statuses {
		if status.SystemID == systemID {
			// 删除该元素
			m.data[transactionID] = append(statuses[:i], statuses[i+1:]...)
			return nil
		}
	}

	return fmt.Errorf("sync status not found for deletion")
}

// 示例同步策略实现

// ExternalTransactionPayload 外部API交易数据格式 - 完整匹配 CreateFuelTransactionRequest
type ExternalTransactionPayload struct {
	// 必须字段
	TransactionNumber string  `json:"transaction_number" validate:"required"`
	PumpID            string  `json:"pump_id" validate:"required"`
	NozzleID          string  `json:"nozzle_id" validate:"required"`
	FuelType          string  `json:"fuel_type" validate:"required"`
	FuelGrade         string  `json:"fuel_grade" validate:"required"`
	Tank              int     `json:"tank" validate:"required"`
	UnitPrice         float64 `json:"unit_price" validate:"required,gt=0"`
	Volume            float64 `json:"volume" validate:"required,gt=0"`
	Amount            float64 `json:"amount" validate:"required,gt=0"`
	TotalVolume       float64 `json:"total_volume" validate:"required,gt=0"`
	TotalAmount       float64 `json:"total_amount" validate:"required,gt=0"`

	// 可选字段
	MemberCardID     *string                `json:"member_card_id,omitempty"`
	MemberID         *int64                 `json:"member_id,omitempty"`
	EmployeeID       *string                `json:"employee_id,omitempty"`
	FCCTransactionID *string                `json:"fcc_transaction_id,omitempty"`
	POSTerminalID    *string                `json:"pos_terminal_id,omitempty"`
	StartTotalizer   *float64               `json:"start_totalizer,omitempty"`
	EndTotalizer     *float64               `json:"end_totalizer,omitempty"`
	NozzleStartTime  *time.Time             `json:"nozzle_start_time,omitempty"`
	NozzleEndTime    *time.Time             `json:"nozzle_end_time,omitempty"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
}

// 移除回调机制，使用现有的事件系统

// HTTPSyncStrategy HTTP同步策略
type HTTPSyncStrategy struct {
	systemID        string
	endpoint        string
	timeout         time.Duration
	maxRetries      int
	retryDelay      time.Duration
	supportedEvents []TransactionEventType
	headers         map[string]string
	apiKey          string                   // 新增：API密钥
	client          *http.Client             // 🎯 复用的HTTP客户端
	syncPublisher   TransactionSyncPublisher // 🎯 添加SyncPublisher引用用于回调
	logger          *zap.Logger
}

// NewHTTPSyncStrategy 创建HTTP同步策略
func NewHTTPSyncStrategy(systemID, endpoint string, timeout time.Duration, apiKey string, logger *zap.Logger) SyncStrategy {
	headers := map[string]string{
		"Content-Type": "application/json",
		"User-Agent":   "FCC-Service-Transaction-Sync/1.0",
	}

	// 添加API密钥认证头
	if apiKey != "" {
		headers["X-API-Key"] = apiKey
	}

	// 🎯 创建复用的HTTP客户端，避免每次请求重复创建
	client := &http.Client{
		Timeout: timeout,
		// 可以在这里添加更多的客户端配置，如连接池、代理等
	}

	return &HTTPSyncStrategy{
		systemID:   systemID,
		endpoint:   endpoint,
		timeout:    timeout,
		maxRetries: 3,
		retryDelay: 5 * time.Second,
		supportedEvents: []TransactionEventType{
			TransactionEventUpdated, // 🚀 新增：支持更新事件
			TransactionEventCompleted,
			TransactionEventCancelled,
			TransactionEventFailed,
		},
		headers:       headers,
		apiKey:        apiKey,
		client:        client, // 🎯 设置复用的HTTP客户端
		syncPublisher: nil,    // 🎯 初始化为nil，通过SetSyncPublisher设置
		logger:        logger,
	}
}

// SetSyncPublisher 设置SyncPublisher引用，用于回调数据库更新
func (h *HTTPSyncStrategy) SetSyncPublisher(publisher TransactionSyncPublisher) {
	h.syncPublisher = publisher
}

func (h *HTTPSyncStrategy) SystemID() string {
	return h.systemID
}

func (h *HTTPSyncStrategy) SupportedEvents() []TransactionEventType {
	return h.supportedEvents
}

func (h *HTTPSyncStrategy) Sync(ctx context.Context, event *TransactionEvent) error {
	// 🎯 根据事件类型选择不同的接口和方法
	if event.EventType == TransactionEventUpdated {
		return h.syncPumpReadingUpdate(ctx, event)
	}

	// 其他事件类型使用原有的完整交易同步逻辑
	return h.syncFullTransaction(ctx, event)
}

// syncPumpReadingUpdate 专门处理泵码更新的同步
func (h *HTTPSyncStrategy) syncPumpReadingUpdate(ctx context.Context, event *TransactionEvent) error {
	tx := event.Transaction
	if tx == nil {
		return fmt.Errorf("transaction is nil in update event")
	}

	h.logger.Info("[SyncPublisher] Handling pump reading update event",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID))

	// 🎯 检查是否有外部交易ID，如果没有则标记为需要同步
	if tx.ExternalTransactionID == nil || *tx.ExternalTransactionID == "" {
		h.logger.Warn("No external transaction ID found, marking transaction as needs_sync",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", tx.DeviceID),
			zap.String("nozzle_id", tx.NozzleID))

		// 标记为需要同步
		return h.markTransactionAsNeedsSync(ctx, tx)
	}

	// 构建 pump-readings 更新数据
	updateData := map[string]interface{}{}

	// 添加 end_totalizer（体积读数）
	if tx.EndPumpVolumeReading != nil {
		endTotalizer, _ := tx.EndPumpVolumeReading.Float64()
		updateData["end_totalizer"] = endTotalizer
	}

	// 添加 end_amount_reading（金额读数）如果有的话
	if tx.EndPumpAmountReading != nil {
		endAmount, _ := tx.EndPumpAmountReading.Float64()
		updateData["end_amount_reading"] = endAmount
	}

	// 如果没有要更新的数据，跳过
	if len(updateData) == 0 {
		h.logger.Debug("No pump reading data to update, skipping",
			zap.String("transaction_id", tx.ID))
		return nil
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("failed to marshal pump reading data: %w", err)
	}

	// 🎯 使用外部交易ID构建 pump-readings 接口 URL
	baseURL := h.getBaseURLFromEndpoint()
	pumpReadingsURL := fmt.Sprintf("%s/api/v1/fuel-transactions/%s/pump-readings",
		baseURL, *tx.ExternalTransactionID)

	h.logger.Info("[SyncPublisher] Updating pump readings via dedicated API",
		zap.String("system_id", h.systemID),
		zap.String("url", pumpReadingsURL),
		zap.String("transaction_id", tx.ID),
		zap.String("external_transaction_id", *tx.ExternalTransactionID),
		zap.String("json_data", string(jsonData)))

	// 🎯 复用 HTTP client 和请求发送逻辑
	return h.sendHTTPRequest(ctx, "PATCH", pumpReadingsURL, jsonData, "pump reading update")
}

// syncFullTransaction 处理完整交易同步（原有逻辑）
func (h *HTTPSyncStrategy) syncFullTransaction(ctx context.Context, event *TransactionEvent) error {
	// 转换数据
	data, err := h.TransformData(event)
	if err != nil {
		return fmt.Errorf("failed to transform data: %w", err)
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	// 🚀 调试：打印实际发送的JSON数据
	h.logger.Info("[SyncPublisher] Sending JSON data to external API",
		zap.String("system_id", h.systemID),
		zap.String("endpoint", h.endpoint),
		zap.String("transaction_id", event.TransactionID),
		zap.String("json_data", string(jsonData)))

	h.logger.Info("[SyncPublisher]Syncing transaction to HTTP endpoint",
		zap.String("system_id", h.systemID),
		zap.String("endpoint", h.endpoint),
		zap.String("transaction_id", event.TransactionID))

	// 🎯 发送请求并处理响应，保存外部系统返回的ID
	return h.sendHTTPRequestWithResponse(ctx, "POST", h.endpoint, jsonData, "full transaction sync", event.Transaction)
}

func (h *HTTPSyncStrategy) TransformData(event *TransactionEvent) (interface{}, error) {
	if event == nil || event.Transaction == nil {
		return nil, fmt.Errorf("event or transaction is nil")
	}

	tx := event.Transaction

	// 🚀 数据验证：只有当交易有实际数据时才同步
	transactionData := h.extractTransactionData(tx)
	// todo: 暂时关闭数据验证
	// if err := h.validateTransactionData(tx.ID, transactionData); err != nil {
	// 	return nil, err
	// }

	// 创建外部API格式的数据
	payload := &ExternalTransactionPayload{
		// 必须字段
		TransactionNumber: tx.ID,
		PumpID:            tx.DeviceID,
		NozzleID:          tx.NozzleID,
		FuelType:          tx.GetFuelGradeID(),
		FuelGrade:         h.getFuelGrade(tx),
		Tank:              h.getTankNumber(tx),
		UnitPrice:         transactionData.UnitPrice,
		Volume:            transactionData.Volume,
		Amount:            transactionData.Amount,
		TotalVolume:       transactionData.TotalVolume,
		TotalAmount:       transactionData.TotalAmount,

		// 可选字段
		FCCTransactionID: &tx.ID,
	}

	// 设置可选字段
	h.setOptionalFields(tx, payload)

	// 设置泵码和时间数据
	h.setPumpAndTimeData(tx, payload)

	// 设置元数据
	h.setMetadata(tx, payload)

	h.logger.Debug("Transformed transaction data",
		zap.String("fcc_transaction_id", *payload.FCCTransactionID),
		zap.String("fuel_type", payload.FuelType),
		zap.String("nozzle_id", payload.NozzleID),
		zap.String("pump_id", payload.PumpID),
		zap.Float64("total_amount", payload.TotalAmount),
		zap.Float64("total_volume", payload.TotalVolume),
		zap.String("transaction_number", payload.TransactionNumber),
		zap.Float64("unit_price", payload.UnitPrice))

	return payload, nil
}

// TransactionData 存储提取和转换后的交易数据
type TransactionData struct {
	Volume      float64
	Amount      float64
	UnitPrice   float64
	TotalVolume float64
	TotalAmount float64
}

// extractTransactionData 提取并转换交易数据
func (h *HTTPSyncStrategy) extractTransactionData(tx *models.Transaction) *TransactionData {
	return &TransactionData{
		Volume:      h.safeFloat64FromDecimal(tx.ActualVolume, 0.0),
		Amount:      h.safeFloat64FromDecimal(tx.ActualAmount, 0.0),
		UnitPrice:   h.safeFloat64FromDecimal(tx.UnitPrice, 0.0),
		TotalVolume: h.safeFloat64FromPtrDecimal(tx.EndPumpVolumeReading, 0.0),
		TotalAmount: h.safeFloat64FromPtrDecimal(tx.EndPumpAmountReading, 0.0),
	}
}

// validateTransactionData 验证交易数据是否有效
func (h *HTTPSyncStrategy) validateTransactionData(transactionID string, data *TransactionData) error {
	if data.Volume <= 0 || data.Amount <= 0 || data.UnitPrice <= 0 {
		h.logger.Info("Skipping sync for transaction with insufficient data",
			zap.String("transaction_id", transactionID),
			zap.Float64("volume", data.Volume),
			zap.Float64("amount", data.Amount),
			zap.Float64("unit_price", data.UnitPrice))
		return fmt.Errorf("transaction has insufficient data for sync (volume: %.2f, amount: %.2f, unit_price: %.2f)",
			data.Volume, data.Amount, data.UnitPrice)
	}
	return nil
}

// setOptionalFields 设置可选字段
func (h *HTTPSyncStrategy) setOptionalFields(tx *models.Transaction, payload *ExternalTransactionPayload) {
	// 设置客户卡信息
	if tx.CustomerCard != "" {
		payload.MemberCardID = &tx.CustomerCard
	}

	// 设置操作员信息
	if tx.OperatorID != "" {
		payload.EmployeeID = &tx.OperatorID
	}
}

// setPumpAndTimeData 设置泵码和时间数据
func (h *HTTPSyncStrategy) setPumpAndTimeData(tx *models.Transaction, payload *ExternalTransactionPayload) {
	// 设置泵码数据
	if tx.StartPumpVolumeReading != nil {
		startTotalizer := h.safeFloat64FromPtrDecimal(tx.StartPumpVolumeReading, 0.0)
		payload.StartTotalizer = &startTotalizer
	}
	if tx.EndPumpVolumeReading != nil {
		endTotalizer := h.safeFloat64FromPtrDecimal(tx.EndPumpVolumeReading, 0.0)
		payload.EndTotalizer = &endTotalizer
	}

	// 设置时间信息
	if tx.StartedAt != nil {
		payload.NozzleStartTime = tx.StartedAt // auth 时间
	}
	if tx.CompletedAt != nil {
		payload.NozzleEndTime = tx.CompletedAt // 完成交易(DC101)时间
	}
}

// setMetadata 设置元数据
func (h *HTTPSyncStrategy) setMetadata(tx *models.Transaction, payload *ExternalTransactionPayload) {
	// if tx.ExtraData == nil {
	// 	return
	// }

	payload.Metadata = map[string]interface{}{
		"fcc_device_id":     tx.DeviceID,
		"fcc_controller_id": tx.ControllerID,
		"fcc_status":        string(tx.Status),
		"fcc_type":          string(tx.Type),
		"fcc_created_at":    tx.CreatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// 添加时间信息到元数据（格式化为字符串用于存储）
	h.addTimeMetadata(tx, payload.Metadata)

	// 添加泵码数据到元数据（保留原始精度）
	h.addPumpMetadata(tx, payload.Metadata)
}

// addTimeMetadata 添加时间元数据
func (h *HTTPSyncStrategy) addTimeMetadata(tx *models.Transaction, metadata map[string]interface{}) {
	if tx.StartedAt != nil {
		metadata["fcc_started_at"] = tx.StartedAt.Format("2006-01-02T15:04:05Z")
	}
	if tx.CompletedAt != nil {
		metadata["fcc_completed_at"] = tx.CompletedAt.Format("2006-01-02T15:04:05Z")
	}
}

// addPumpMetadata 添加泵码元数据
func (h *HTTPSyncStrategy) addPumpMetadata(tx *models.Transaction, metadata map[string]interface{}) {
	if tx.HasPumpReadings() {
		metadata["pump_readings"] = map[string]interface{}{
			"start_volume": h.safePtrDecimalString(tx.StartPumpVolumeReading),
			"end_volume":   h.safePtrDecimalString(tx.EndPumpVolumeReading),
			"start_amount": h.safePtrDecimalString(tx.StartPumpAmountReading),
			"end_amount":   h.safePtrDecimalString(tx.EndPumpAmountReading),
			"source":       tx.PumpReadingSource,
			"quality":      tx.PumpReadingQuality,
			"validated":    tx.PumpReadingValidated,
		}
	}
}

func (h *HTTPSyncStrategy) ShouldRetry(err error) bool {
	// 这里可以实现更复杂的重试逻辑
	// 例如：网络错误重试，业务错误不重试
	return true
}

func (h *HTTPSyncStrategy) MaxRetries() int {
	return h.maxRetries
}

func (h *HTTPSyncStrategy) RetryDelay() time.Duration {
	return h.retryDelay
}

// 辅助方法：获取油品等级
func (h *HTTPSyncStrategy) getFuelGrade(tx *models.Transaction) string {
	// 优先从Transaction的FuelGradeName获取
	if tx.FuelGradeName != "" {
		return tx.FuelGradeName
	}

	// 如果没有，根据FuelType推断
	switch tx.FuelType {
	case "gasoline":
		return "92" // 默认汽油等级
	case "diesel":
		return "0" // 默认柴油等级
	default:
		return "92" // 默认等级
	}
}

// 辅助方法：获取油罐编号
func (h *HTTPSyncStrategy) getTankNumber(tx *models.Transaction) int {
	// TODO: 实际实现需要从nozzles表中查询tank信息
	// 这里先返回默认值
	return 1
}

// 🚀 新增：安全的decimal转float64方法，处理decimal.Decimal类型
func (h *HTTPSyncStrategy) safeFloat64FromDecimal(dec decimal.Decimal, defaultValue float64) float64 {
	f, exact := dec.Float64()
	if !exact {
		h.logger.Warn("Decimal to float64 conversion lost precision",
			zap.String("system_id", h.systemID),
			zap.String("decimal_value", dec.String()),
			zap.Float64("converted_value", f))
	}
	return f
}

// 🚀 新增：安全的decimal转float64方法，处理*decimal.Decimal类型
func (h *HTTPSyncStrategy) safeFloat64FromPtrDecimal(dec *decimal.Decimal, defaultValue float64) float64 {
	if dec == nil {
		return defaultValue
	}

	f, exact := dec.Float64()
	if !exact {
		h.logger.Warn("Decimal to float64 conversion lost precision",
			zap.String("system_id", h.systemID),
			zap.String("decimal_value", dec.String()),
			zap.Float64("converted_value", f))
	}

	return f
}

// 🚀 新增：安全的decimal转string方法，处理空指针
func (h *HTTPSyncStrategy) safePtrDecimalString(dec *decimal.Decimal) string {
	if dec == nil {
		return "0"
	}

	return dec.String()
}

// getBaseURLFromEndpoint 从 endpoint 中提取 base URL
func (h *HTTPSyncStrategy) getBaseURLFromEndpoint() string {
	// 假设 endpoint 格式为 http://host:port/path
	// 我们需要提取 http://host:port 部分
	if h.endpoint == "" {
		return "http://localhost:8080" // 默认值
	}

	// 简单的 URL 解析
	if strings.HasPrefix(h.endpoint, "http://") || strings.HasPrefix(h.endpoint, "https://") {
		parts := strings.Split(h.endpoint, "/")
		if len(parts) >= 3 {
			return strings.Join(parts[:3], "/")
		}
	}

	// 如果解析失败，返回默认值
	return "http://localhost:8080"
}

// sendHTTPRequest 发送HTTP请求的通用方法
// 🎯 复用HTTP client和请求处理逻辑，避免代码重复
func (h *HTTPSyncStrategy) sendHTTPRequest(ctx context.Context, method, url string, jsonData []byte, operationType string) error {
	// 🎯 使用复用的HTTP客户端，避免每次请求重复创建
	// 这样可以复用连接池，提高性能

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create %s request: %w", operationType, err)
	}

	// 设置请求头（复用同一套headers）
	for key, value := range h.headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := h.client.Do(req)
	if err != nil {
		h.logger.Error("HTTP request failed",
			zap.String("system_id", h.systemID),
			zap.String("url", url),
			zap.String("method", method),
			zap.String("operation", operationType),
			zap.Error(err))
		return fmt.Errorf("%s request failed: %w", operationType, err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		h.logger.Error("HTTP request returned error status",
			zap.String("system_id", h.systemID),
			zap.String("url", url),
			zap.String("method", method),
			zap.String("operation", operationType),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)))
		return fmt.Errorf("%s failed with status %d: %s", operationType, resp.StatusCode, string(body))
	}

	h.logger.Info("HTTP request successful",
		zap.String("system_id", h.systemID),
		zap.String("url", url),
		zap.String("method", method),
		zap.String("operation", operationType),
		zap.Int("status_code", resp.StatusCode))

	return nil
}

// sendHTTPRequestWithResponse 发送HTTP请求并处理响应的方法
// 🎯 专门用于完整交易同步，处理外部系统返回的ID
func (h *HTTPSyncStrategy) sendHTTPRequestWithResponse(ctx context.Context, method, url string, jsonData []byte, operationType string, tx *models.Transaction) error {
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create %s request: %w", operationType, err)
	}

	// 设置请求头
	for key, value := range h.headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := h.client.Do(req)
	if err != nil {
		h.logger.Error("HTTP request failed",
			zap.String("system_id", h.systemID),
			zap.String("url", url),
			zap.String("method", method),
			zap.String("operation", operationType),
			zap.Error(err))
		return fmt.Errorf("%s request failed: %w", operationType, err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		h.logger.Error("Failed to read response body",
			zap.String("system_id", h.systemID),
			zap.String("url", url),
			zap.Error(err))
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 🎯 打印返回内容
	h.logger.Info("[SyncPublisher] External API response received",
		zap.String("system_id", h.systemID),
		zap.String("url", url),
		zap.String("method", method),
		zap.String("operation", operationType),
		zap.String("transaction_id", tx.ID),
		zap.Int("status_code", resp.StatusCode),
		zap.String("response_body", string(body)))

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		h.logger.Error("HTTP request returned error status",
			zap.String("system_id", h.systemID),
			zap.String("url", url),
			zap.String("method", method),
			zap.String("operation", operationType),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)))
		return fmt.Errorf("%s failed with status %d: %s", operationType, resp.StatusCode, string(body))
	}

	// 🎯 解析响应并提取外部交易ID
	if err := h.extractAndSaveExternalID(ctx, body, tx); err != nil {
		h.logger.Warn("Failed to extract external transaction ID from response",
			zap.String("transaction_id", tx.ID),
			zap.String("response_body", string(body)),
			zap.Error(err))
		// 不返回错误，因为同步本身是成功的
	} else {
		// 🎯 同步成功，记录日志（数据库更新由事件系统处理）
		h.logger.Info("Transaction sync successful, external ID extracted",
			zap.String("transaction_id", tx.ID))
	}

	h.logger.Info("HTTP request successful",
		zap.String("system_id", h.systemID),
		zap.String("url", url),
		zap.String("method", method),
		zap.String("operation", operationType),
		zap.Int("status_code", resp.StatusCode))

	return nil
}

// extractAndSaveExternalID 从响应中提取外部交易ID并保存到数据库
func (h *HTTPSyncStrategy) extractAndSaveExternalID(ctx context.Context, responseBody []byte, tx *models.Transaction) error {
	// 解析JSON响应
	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 尝试提取ID字段（可能的字段名：id, transaction_id, external_id）
	var externalID string
	if id, ok := response["id"]; ok {
		if idStr, ok := id.(string); ok {
			externalID = idStr
		}
	} else if txID, ok := response["transaction_id"]; ok {
		if txIDStr, ok := txID.(string); ok {
			externalID = txIDStr
		}
	} else if extID, ok := response["external_id"]; ok {
		if extIDStr, ok := extID.(string); ok {
			externalID = extIDStr
		}
	}

	if externalID == "" {
		return fmt.Errorf("no valid external ID found in response")
	}

	// 🎯 记录外部交易ID，并通过SyncPublisher回调更新数据库
	h.logger.Info("External transaction ID extracted from response",
		zap.String("transaction_id", tx.ID),
		zap.String("external_transaction_id", externalID))

	// 🎯 通过SyncPublisher回调更新数据库
	if h.syncPublisher != nil {
		if err := h.syncPublisher.UpdateExternalTransactionID(ctx, tx.ID, externalID); err != nil {
			h.logger.Error("Failed to update external transaction ID via callback",
				zap.String("transaction_id", tx.ID),
				zap.String("external_transaction_id", externalID),
				zap.Error(err))
			return fmt.Errorf("failed to update external transaction ID: %w", err)
		}
		h.logger.Info("External transaction ID updated successfully via callback",
			zap.String("transaction_id", tx.ID),
			zap.String("external_transaction_id", externalID))
	} else {
		h.logger.Warn("No SyncPublisher available for external ID update",
			zap.String("transaction_id", tx.ID),
			zap.String("external_transaction_id", externalID))
	}

	return nil
}

// markTransactionAsNeedsSync 标记交易为需要同步状态
func (h *HTTPSyncStrategy) markTransactionAsNeedsSync(ctx context.Context, tx *models.Transaction) error {
	h.logger.Info("Marking transaction as needs_sync due to missing external ID",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID))

	// 🎯 记录需要同步状态，并通过SyncPublisher回调更新数据库
	h.logger.Info("Transaction marked as needs_sync due to missing external ID",
		zap.String("transaction_id", tx.ID))

	// 🎯 通过SyncPublisher回调更新数据库
	if h.syncPublisher != nil {
		if err := h.syncPublisher.UpdateSyncStatus(ctx, tx.ID, "needs_sync"); err != nil {
			h.logger.Error("Failed to update sync status via callback",
				zap.String("transaction_id", tx.ID),
				zap.String("status", "needs_sync"),
				zap.Error(err))
			return fmt.Errorf("failed to update sync status: %w", err)
		}
		h.logger.Info("Transaction sync status updated successfully via callback",
			zap.String("transaction_id", tx.ID),
			zap.String("status", "needs_sync"))
	} else {
		h.logger.Warn("No SyncPublisher available for sync status update",
			zap.String("transaction_id", tx.ID),
			zap.String("status", "needs_sync"))
	}

	return nil
}
