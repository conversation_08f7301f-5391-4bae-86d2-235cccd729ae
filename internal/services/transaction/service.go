package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/services/nozzle"
	"fcc-service/pkg/models"
)

// TransactionService 交易服务接口
type TransactionService interface {
	// 核心持久化功能
	PersistDARTTransaction(ctx context.Context, dartTx *models.DARTTransaction) error

	// 查询功能
	GetTransaction(ctx context.Context, id string) (*models.Transaction, error)
	GetTransactionsByDevice(ctx context.Context, deviceID string, filter TransactionFilter) (*TransactionListResult, error)
	GetTransactionHistory(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error)

	// 统计功能
	GetTransactionStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error)
	GetDeviceTransactionSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error)

	// 🚀 新增：同步策略管理
	RegisterSyncStrategy(systemID string, strategy SyncStrategy)

	// 🚀 新增：获取未上传交易
	GetUnuploadedTransactions(ctx context.Context, systemID string, filter UnuploadedTransactionFilter) (*UnuploadedTransactionResult, error)
	GetUnuploadedTransactionsSummary(ctx context.Context) (*UnuploadedTransactionSummary, error)

	// 🚀 新增：获取内部依赖 - 用于事件桥接集成
	GetSyncPublisher() TransactionSyncPublisher
	GetDataCollector() TransactionDataCollector
	GetNozzleResolver() nozzle.NozzleResolver
}

// TransactionDataCollector 交易数据收集器接口
type TransactionDataCollector interface {
	// 收集设备相关信息
	CollectDeviceInfo(ctx context.Context, deviceID string) (*DeviceContext, error)

	// 收集员工信息
	CollectOperatorInfo(ctx context.Context, operatorID string) (*OperatorContext, error)

	// 收集站点信息
	CollectStationInfo(ctx context.Context, stationID string) (*StationContext, error)

	// 收集油品信息
	CollectFuelGradeInfo(ctx context.Context, nozzleID string) (*FuelGradeContext, error)

	// 收集支付信息（预留）
	CollectPaymentInfo(ctx context.Context, paymentRef string) (*PaymentContext, error)
}

// TransactionSyncPublisher 交易同步发布器接口
type TransactionSyncPublisher interface {
	// 发布交易事件到多个外部系统
	PublishTransactionEvent(ctx context.Context, event *TransactionEvent) error

	// 注册同步策略
	RegisterSyncStrategy(systemID string, strategy SyncStrategy)

	// 获取同步状态
	GetSyncStatus(ctx context.Context, transactionID string) ([]*SyncStatus, error)

	// 🎯 设置外部ID更新回调
	SetExternalIDUpdateCallback(callback func(ctx context.Context, transactionID, externalID string) error)
	SetSyncStatusUpdateCallback(callback func(ctx context.Context, transactionID, status string) error)

	// 🎯 调用外部ID更新回调
	UpdateExternalTransactionID(ctx context.Context, transactionID, externalID string) error
	UpdateSyncStatus(ctx context.Context, transactionID, status string) error
}

// 数据上下文结构体

// DeviceContext 设备上下文信息
type DeviceContext struct {
	DeviceID     string `json:"device_id"`
	DeviceName   string `json:"device_name"`
	DeviceType   string `json:"device_type"`
	ControllerID string `json:"controller_id"`
	StationID    string `json:"station_id"`
	IslandID     string `json:"island_id"`
	Position     string `json:"position"`
	SerialNumber string `json:"serial_number"`
	Model        string `json:"model"`
	Vendor       string `json:"vendor"`
}

// OperatorContext 操作员上下文信息
type OperatorContext struct {
	OperatorID    string `json:"operator_id"`
	OperatorName  string `json:"operator_name"`
	OperatorCode  string `json:"operator_code"`
	Department    string `json:"department"`
	Role          string `json:"role"`
	ShiftID       string `json:"shift_id"`
	WorkstationID string `json:"workstation_id"`
}

// StationContext 站点上下文信息
type StationContext struct {
	StationID   string `json:"station_id"`
	StationName string `json:"station_name"`
	StationCode string `json:"station_code"`
	Region      string `json:"region"`
	Address     string `json:"address"`
	CompanyID   string `json:"company_id"`
	CompanyName string `json:"company_name"`
}

// FuelGradeContext 油品上下文信息
type FuelGradeContext struct {
	FuelGradeID   string `json:"fuel_grade_id"`
	FuelGradeName string `json:"fuel_grade_name"`
	FuelType      string `json:"fuel_type"` // gasoline, diesel, etc.
	OctaneRating  int    `json:"octane_rating"`
}

// PaymentContext 支付上下文信息（预留）
type PaymentContext struct {
	PaymentMethod   string          `json:"payment_method"`
	PaymentProvider string          `json:"payment_provider"`
	CardType        string          `json:"card_type"`
	CardMask        string          `json:"card_mask"`
	AuthCode        string          `json:"auth_code"`
	ReferenceNumber string          `json:"reference_number"`
	Amount          decimal.Decimal `json:"amount"`
}

// 查询相关结构体

// TransactionFilter 交易查询过滤器
type TransactionFilter struct {
	// 基础过滤
	DeviceID   *string                   `json:"device_id"`
	NozzleID   *string                   `json:"nozzle_id"`
	Status     *models.TransactionStatus `json:"status"`
	Type       *models.TransactionType   `json:"type"`
	OperatorID *string                   `json:"operator_id"`
	StationID  *string                   `json:"station_id"`

	// 时间范围
	StartTime *time.Time `json:"start_time"`
	EndTime   *time.Time `json:"end_time"`

	// 金额范围
	MinAmount *decimal.Decimal `json:"min_amount"`
	MaxAmount *decimal.Decimal `json:"max_amount"`

	// 体积范围
	MinVolume *decimal.Decimal `json:"min_volume"`
	MaxVolume *decimal.Decimal `json:"max_volume"`

	// 任务7新增：泵码数据过滤器
	PumpReadingSource    *string          `json:"pump_reading_source"`    // 泵码数据来源过滤
	PumpReadingQuality   *string          `json:"pump_reading_quality"`   // 泵码数据质量过滤
	PumpReadingValidated *bool            `json:"pump_reading_validated"` // 泵码验证状态过滤
	MaxPumpDiscrepancy   *decimal.Decimal `json:"max_pump_discrepancy"`   // 最大泵码差异过滤
	HasPumpReadings      *bool            `json:"has_pump_readings"`      // 是否包含泵码数据过滤

	// 分页排序
	Page      int    `json:"page"`
	Limit     int    `json:"limit"`
	SortBy    string `json:"sort_by"`
	SortOrder string `json:"sort_order"`
}

// TransactionListResult 交易列表查询结果
type TransactionListResult struct {
	Transactions []*models.Transaction `json:"transactions"`
	Total        int64                 `json:"total"`
	Page         int                   `json:"page"`
	Limit        int                   `json:"limit"`
	HasMore      bool                  `json:"has_more"`
}

// TransactionStatsFilter 交易统计过滤器
type TransactionStatsFilter struct {
	DeviceID   *string    `json:"device_id"`
	StationID  *string    `json:"station_id"`
	OperatorID *string    `json:"operator_id"`
	StartTime  *time.Time `json:"start_time"`
	EndTime    *time.Time `json:"end_time"`
	GroupBy    string     `json:"group_by"` // day, hour, device, operator
}

// TransactionStats 交易统计结果
type TransactionStats struct {
	TotalCount       int64           `json:"total_count"`
	TotalVolume      decimal.Decimal `json:"total_volume"`
	TotalAmount      decimal.Decimal `json:"total_amount"`
	AverageVolume    decimal.Decimal `json:"average_volume"`
	AverageAmount    decimal.Decimal `json:"average_amount"`
	AverageUnitPrice decimal.Decimal `json:"average_unit_price"`
	CompletedCount   int64           `json:"completed_count"`
	CancelledCount   int64           `json:"cancelled_count"`
	FailedCount      int64           `json:"failed_count"`
	PeriodStats      []*PeriodStat   `json:"period_stats,omitempty"`
}

// PeriodStat 时期统计
type PeriodStat struct {
	Period       string          `json:"period"` // 2024-01-01, 2024-01-01 14:00
	Count        int64           `json:"count"`
	Volume       decimal.Decimal `json:"volume"`
	Amount       decimal.Decimal `json:"amount"`
	AvgUnitPrice decimal.Decimal `json:"avg_unit_price"`
}

// DeviceTransactionSummary 设备交易汇总
type DeviceTransactionSummary struct {
	DeviceID        string                      `json:"device_id"`
	DeviceName      string                      `json:"device_name"`
	TotalCount      int64                       `json:"total_count"`
	TotalVolume     decimal.Decimal             `json:"total_volume"`
	TotalAmount     decimal.Decimal             `json:"total_amount"`
	LastTransaction *models.Transaction         `json:"last_transaction"`
	NozzleSummaries []*NozzleTransactionSummary `json:"nozzle_summaries"`
}

// NozzleTransactionSummary 喷嘴交易汇总
type NozzleTransactionSummary struct {
	NozzleID string          `json:"nozzle_id"`
	Count    int64           `json:"count"`
	Volume   decimal.Decimal `json:"volume"`
	Amount   decimal.Decimal `json:"amount"`
}

// 同步相关结构体

// TransactionEvent 交易事件
type TransactionEvent struct {
	EventID       string               `json:"event_id"`
	EventType     TransactionEventType `json:"event_type"`
	TransactionID string               `json:"transaction_id"`
	Transaction   *models.Transaction  `json:"transaction"`
	Context       *TransactionContext  `json:"context"`
	Timestamp     time.Time            `json:"timestamp"`
	Source        string               `json:"source"`
	Version       string               `json:"version"`
}

// TransactionEventType 交易事件类型
type TransactionEventType string

const (
	TransactionEventCreated   TransactionEventType = "transaction.created"
	TransactionEventUpdated   TransactionEventType = "transaction.updated"
	TransactionEventCompleted TransactionEventType = "transaction.completed"
	TransactionEventCancelled TransactionEventType = "transaction.cancelled"
	TransactionEventFailed    TransactionEventType = "transaction.failed"
)

// TransactionContext 交易完整上下文
type TransactionContext struct {
	Device    *DeviceContext         `json:"device,omitempty"`
	Operator  *OperatorContext       `json:"operator,omitempty"`
	Station   *StationContext        `json:"station,omitempty"`
	FuelGrade *FuelGradeContext      `json:"fuel_grade,omitempty"`
	Payment   *PaymentContext        `json:"payment,omitempty"`
	ExtraData map[string]interface{} `json:"extra_data,omitempty"`
}

// HasCompleteContext 检查是否有完整的上下文信息
func (tc *TransactionContext) HasCompleteContext() bool {
	if tc.ExtraData == nil {
		return true // 如果没有记录默认值使用情况，认为是完整的
	}

	// 检查是否使用了默认值
	if defaultsUsed, exists := tc.ExtraData["context_defaults_used"]; exists {
		if defaults, ok := defaultsUsed.(map[string]string); ok {
			return len(defaults) == 0
		}
	}

	return true // 默认认为是完整的
}

// GetDefaultsUsedCount 获取使用默认值的上下文数量
func (tc *TransactionContext) GetDefaultsUsedCount() int {
	if tc.ExtraData == nil {
		return 0
	}

	if defaultsUsed, exists := tc.ExtraData["context_defaults_used"]; exists {
		if defaults, ok := defaultsUsed.(map[string]string); ok {
			return len(defaults)
		}
	}

	return 0
}

// SyncStrategy 同步策略接口
type SyncStrategy interface {
	// 系统标识
	SystemID() string

	// 支持的事件类型
	SupportedEvents() []TransactionEventType

	// 同步数据
	Sync(ctx context.Context, event *TransactionEvent) error

	// 数据转换（每个系统可能需要不同的数据格式）
	TransformData(event *TransactionEvent) (interface{}, error)

	// 重试策略
	ShouldRetry(err error) bool
	MaxRetries() int
	RetryDelay() time.Duration
}

// SyncStatus 同步状态
type SyncStatus struct {
	SystemID      string     `json:"system_id"`
	TransactionID string     `json:"transaction_id"`
	Status        string     `json:"status"` // pending, success, failed, retrying
	AttemptCount  int        `json:"attempt_count"`
	LastAttempt   time.Time  `json:"last_attempt"`
	LastError     string     `json:"last_error,omitempty"`
	NextRetry     *time.Time `json:"next_retry,omitempty"`
}

// UnuploadedTransactionFilter 未上传交易过滤器
type UnuploadedTransactionFilter struct {
	// 时间范围
	StartTime *time.Time `json:"start_time"`
	EndTime   *time.Time `json:"end_time"`

	// 设备过滤
	DeviceID *string `json:"device_id"`

	// 同步状态过滤
	SyncStatus     *string `json:"sync_status"`            // pending, failed, retrying
	MaxRetryCount  *int    `json:"max_retry_count"`        // 最大重试次数过滤
	LastAttemptAge *int    `json:"last_attempt_age_hours"` // 最后尝试时间距离现在的小时数

	// 分页
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

// UnuploadedTransactionResult 未上传交易结果
type UnuploadedTransactionResult struct {
	Transactions []*TransactionWithSyncStatus `json:"transactions"`
	Total        int64                        `json:"total"`
	Page         int                          `json:"page"`
	Limit        int                          `json:"limit"`
	HasMore      bool                         `json:"has_more"`
}

// TransactionWithSyncStatus 带同步状态的交易
type TransactionWithSyncStatus struct {
	Transaction *models.Transaction `json:"transaction"`
	SyncStatus  *SyncStatus         `json:"sync_status"`
}

// UnuploadedTransactionSummary 未上传交易汇总
type UnuploadedTransactionSummary struct {
	SystemSummaries []*SystemSyncSummary `json:"system_summaries"`
	TotalPending    int64                `json:"total_pending"`
	TotalFailed     int64                `json:"total_failed"`
	TotalRetrying   int64                `json:"total_retrying"`
	OldestPending   *time.Time           `json:"oldest_pending,omitempty"`
}

// SystemSyncSummary 系统同步汇总
type SystemSyncSummary struct {
	SystemID      string     `json:"system_id"`
	PendingCount  int64      `json:"pending_count"`
	FailedCount   int64      `json:"failed_count"`
	RetryingCount int64      `json:"retrying_count"`
	SuccessCount  int64      `json:"success_count"`
	LastSyncTime  *time.Time `json:"last_sync_time,omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// service 交易服务实现
type service struct {
	// 核心依赖
	repository     TransactionRepository
	dataCollector  TransactionDataCollector
	syncPublisher  TransactionSyncPublisher
	nozzleResolver nozzle.NozzleResolver // 🚀 新增：Nozzle 解析器
	logger         *zap.Logger
}

// NewTransactionService 创建交易服务实例
func NewTransactionService(
	repository TransactionRepository,
	dataCollector TransactionDataCollector,
	syncPublisher TransactionSyncPublisher,
	nozzleResolver nozzle.NozzleResolver, // 🚀 新增：Nozzle 解析器参数
	logger *zap.Logger,
) TransactionService {
	return &service{
		repository:     repository,
		dataCollector:  dataCollector,
		syncPublisher:  syncPublisher,
		nozzleResolver: nozzleResolver, // 🚀 注入 Nozzle 解析器
		logger:         logger,
	}
}

// PersistDARTTransaction 持久化DART交易
func (s *service) PersistDARTTransaction(ctx context.Context, dartTx *models.DARTTransaction) error {

	return nil
}

// GetTransaction 获取单个交易
func (s *service) GetTransaction(ctx context.Context, id string) (*models.Transaction, error) {
	return s.repository.GetByID(ctx, id)
}

// GetTransactionsByDevice 获取设备交易历史
func (s *service) GetTransactionsByDevice(ctx context.Context, deviceID string, filter TransactionFilter) (*TransactionListResult, error) {
	filter.DeviceID = &deviceID
	return s.repository.List(ctx, filter)
}

// GetTransactionHistory 获取交易历史
func (s *service) GetTransactionHistory(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error) {
	return s.repository.List(ctx, filter)
}

// GetTransactionStats 获取交易统计
func (s *service) GetTransactionStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error) {
	return s.repository.GetStats(ctx, filter)
}

// GetDeviceTransactionSummary 获取设备交易汇总
func (s *service) GetDeviceTransactionSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error) {
	return s.repository.GetDeviceSummary(ctx, deviceID, timeRange)
}

// RegisterSyncStrategy 注册同步策略
func (s *service) RegisterSyncStrategy(systemID string, strategy SyncStrategy) {
	s.syncPublisher.RegisterSyncStrategy(systemID, strategy)
}

// GetUnuploadedTransactions 获取未上传的交易
func (s *service) GetUnuploadedTransactions(ctx context.Context, systemID string, filter UnuploadedTransactionFilter) (*UnuploadedTransactionResult, error) {
	s.logger.Info("Getting unuploaded transactions",
		zap.String("system_id", systemID),
		zap.Any("filter", filter))

	// 设置默认分页参数
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.Limit <= 0 {
		filter.Limit = 50
	}

	// 构建交易查询过滤器
	txFilter := TransactionFilter{
		DeviceID:  filter.DeviceID,
		StartTime: filter.StartTime,
		EndTime:   filter.EndTime,
		Page:      filter.Page,
		Limit:     filter.Limit,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// 获取交易列表
	txResult, err := s.repository.List(ctx, txFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	// 检查每个交易的同步状态
	var unuploadedTxs []*TransactionWithSyncStatus
	for _, tx := range txResult.Transactions {
		syncStatuses, err := s.syncPublisher.GetSyncStatus(ctx, tx.ID)
		if err != nil {
			s.logger.Warn("Failed to get sync status for transaction",
				zap.String("transaction_id", tx.ID),
				zap.Error(err))
			continue
		}

		// 查找指定系统的同步状态
		var targetStatus *SyncStatus
		for _, status := range syncStatuses {
			if status.SystemID == systemID {
				targetStatus = status
				break
			}
		}

		// 如果没有同步状态或状态不是成功，则认为是未上传
		if targetStatus == nil {
			// 创建一个默认的pending状态
			targetStatus = &SyncStatus{
				SystemID:      systemID,
				TransactionID: tx.ID,
				Status:        "pending",
				AttemptCount:  0,
			}
		}

		// 应用状态过滤器
		if filter.SyncStatus != nil && *filter.SyncStatus != targetStatus.Status {
			continue
		}
		if filter.MaxRetryCount != nil && targetStatus.AttemptCount > *filter.MaxRetryCount {
			continue
		}
		if filter.LastAttemptAge != nil {
			ageHours := int(time.Since(targetStatus.LastAttempt).Hours())
			if ageHours > *filter.LastAttemptAge {
				continue
			}
		}

		// 只包含非成功状态的交易
		if targetStatus.Status != "success" {
			unuploadedTxs = append(unuploadedTxs, &TransactionWithSyncStatus{
				Transaction: tx,
				SyncStatus:  targetStatus,
			})
		}
	}

	result := &UnuploadedTransactionResult{
		Transactions: unuploadedTxs,
		Total:        int64(len(unuploadedTxs)),
		Page:         filter.Page,
		Limit:        filter.Limit,
		HasMore:      len(unuploadedTxs) >= filter.Limit,
	}

	s.logger.Info("Found unuploaded transactions",
		zap.String("system_id", systemID),
		zap.Int("count", len(unuploadedTxs)),
		zap.Int64("total", result.Total))

	return result, nil
}

// GetUnuploadedTransactionsSummary 获取未上传交易汇总
func (s *service) GetUnuploadedTransactionsSummary(ctx context.Context) (*UnuploadedTransactionSummary, error) {
	s.logger.Info("Getting unuploaded transactions summary")

	// 获取最近的交易（例如最近30天）
	endTime := time.Now()
	startTime := endTime.Add(-30 * 24 * time.Hour)

	txFilter := TransactionFilter{
		StartTime: &startTime,
		EndTime:   &endTime,
		Limit:     1000, // 获取较多数据以便统计
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	txResult, err := s.repository.List(ctx, txFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions for summary: %w", err)
	}

	// 统计每个系统的同步状态
	systemStats := make(map[string]*SystemSyncSummary)
	var totalPending, totalFailed, totalRetrying int64
	var oldestPending *time.Time

	for _, tx := range txResult.Transactions {
		syncStatuses, err := s.syncPublisher.GetSyncStatus(ctx, tx.ID)
		if err != nil {
			continue
		}

		// 如果没有任何同步状态，创建默认的pending状态
		if len(syncStatuses) == 0 {
			syncStatuses = []*SyncStatus{
				{
					SystemID:      "external_api_mvp",
					TransactionID: tx.ID,
					Status:        "pending",
					AttemptCount:  0,
					LastAttempt:   time.Time{},
				},
			}
		}

		for _, status := range syncStatuses {
			if systemStats[status.SystemID] == nil {
				systemStats[status.SystemID] = &SystemSyncSummary{
					SystemID: status.SystemID,
				}
			}

			stat := systemStats[status.SystemID]

			switch status.Status {
			case "pending":
				stat.PendingCount++
				totalPending++
				if oldestPending == nil || status.LastAttempt.Before(*oldestPending) {
					if !status.LastAttempt.IsZero() {
						oldestPending = &status.LastAttempt
					} else {
						t := tx.CreatedAt
						oldestPending = &t
					}
				}
			case "failed":
				stat.FailedCount++
				totalFailed++
			case "retrying":
				stat.RetryingCount++
				totalRetrying++
			case "success":
				stat.SuccessCount++
				if stat.LastSyncTime == nil || status.LastAttempt.After(*stat.LastSyncTime) {
					stat.LastSyncTime = &status.LastAttempt
				}
			}
		}
	}

	// 转换为数组
	var systemSummaries []*SystemSyncSummary
	for _, stat := range systemStats {
		systemSummaries = append(systemSummaries, stat)
	}

	summary := &UnuploadedTransactionSummary{
		SystemSummaries: systemSummaries,
		TotalPending:    totalPending,
		TotalFailed:     totalFailed,
		TotalRetrying:   totalRetrying,
		OldestPending:   oldestPending,
	}

	s.logger.Info("Generated unuploaded transactions summary",
		zap.Int("system_count", len(systemSummaries)),
		zap.Int64("total_pending", totalPending),
		zap.Int64("total_failed", totalFailed),
		zap.Int64("total_retrying", totalRetrying))

	return summary, nil
}

// 🚀 新增：获取内部依赖的实现方法

// GetSyncPublisher 获取同步发布器
func (s *service) GetSyncPublisher() TransactionSyncPublisher {
	return s.syncPublisher
}

// GetDataCollector 获取数据收集器
func (s *service) GetDataCollector() TransactionDataCollector {
	return s.dataCollector
}

// GetNozzleResolver 获取喷嘴解析器
func (s *service) GetNozzleResolver() nozzle.NozzleResolver {
	return s.nozzleResolver
}
