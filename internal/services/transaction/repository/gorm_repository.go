package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"fcc-service/pkg/models"
)

// gormRepository 统一的GORM数据库实现
//
// 此实现合并了原来 transaction 和 transaction_lifecycle 两个服务的所有数据访问逻辑，
// 消除了重复的CRUD操作，提供统一的数据访问点。
//
// 设计特点：
// 1. 统一错误处理：所有数据库操作都有一致的错误处理和日志记录
// 2. 性能优化：合理使用索引、分页、批量操作
// 3. 事务支持：重要操作支持事务处理
// 4. 查询优化：针对不同使用场景优化查询性能
type gormRepository struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewGormRepository 创建统一的GORM仓储实现
func NewGormRepository(db *gorm.DB, logger *zap.Logger) Repository {
	if logger == nil {
		logger = zap.NewNop()
	}

	return &gormRepository{
		db:     db,
		logger: logger,
	}
}

// ==================== 基础CRUD操作 ====================

// Create 创建新的交易记录
func (r *gormRepository) Create(ctx context.Context, tx *models.Transaction) error {
	r.logger.Debug("Creating transaction",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", tx.DeviceID),
		zap.String("nozzle_id", tx.NozzleID))

	if err := r.db.WithContext(ctx).Create(tx).Error; err != nil {
		r.logger.Error("Failed to create transaction",
			zap.String("transaction_id", tx.ID),
			zap.Error(err))
		return fmt.Errorf("failed to create transaction: %w", err)
	}

	r.logger.Debug("Transaction created successfully",
		zap.String("transaction_id", tx.ID))
	return nil
}

// GetByID 根据ID获取交易记录
func (r *gormRepository) GetByID(ctx context.Context, id string) (*models.Transaction, error) {
	r.logger.Debug("Getting transaction by ID", zap.String("transaction_id", id))

	var tx models.Transaction
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&tx).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			r.logger.Debug("Transaction not found", zap.String("transaction_id", id))
			return nil, fmt.Errorf("transaction not found: %s", id)
		}
		r.logger.Error("Failed to get transaction",
			zap.String("transaction_id", id),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	r.logger.Debug("Transaction retrieved successfully",
		zap.String("transaction_id", id),
		zap.String("status", string(tx.Status)))
	return &tx, nil
}

// Update 更新交易记录
func (r *gormRepository) Update(ctx context.Context, tx *models.Transaction) error {
	r.logger.Debug("Updating transaction",
		zap.String("transaction_id", tx.ID),
		zap.String("status", string(tx.Status)))

	// 使用 Updates 而不是 Save，避免零值字段被覆盖
	result := r.db.WithContext(ctx).
		Where("id = ?", tx.ID).
		Updates(tx)

	if result.Error != nil {
		r.logger.Error("Failed to update transaction",
			zap.String("transaction_id", tx.ID),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		r.logger.Warn("Transaction not found for update",
			zap.String("transaction_id", tx.ID))
		return fmt.Errorf("transaction not found for update: %s", tx.ID)
	}

	r.logger.Debug("Transaction updated successfully",
		zap.String("transaction_id", tx.ID),
		zap.Int64("rows_affected", result.RowsAffected))
	return nil
}

// Delete 删除交易记录
func (r *gormRepository) Delete(ctx context.Context, id string) error {
	r.logger.Debug("Deleting transaction", zap.String("transaction_id", id))

	result := r.db.WithContext(ctx).
		Where("id = ?", id).
		Delete(&models.Transaction{})

	if result.Error != nil {
		r.logger.Error("Failed to delete transaction",
			zap.String("transaction_id", id),
			zap.Error(result.Error))
		return fmt.Errorf("failed to delete transaction: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		r.logger.Warn("Transaction not found for deletion",
			zap.String("transaction_id", id))
		return fmt.Errorf("transaction not found for deletion: %s", id)
	}

	r.logger.Debug("Transaction deleted successfully",
		zap.String("transaction_id", id))
	return nil
}

// ==================== 实时查询操作 ====================

// GetActiveByDeviceAndNozzle 获取设备特定喷嘴的活跃交易
func (r *gormRepository) GetActiveByDeviceAndNozzle(ctx context.Context, deviceID string, nozzleID string) (*models.Transaction, error) {
	r.logger.Debug("Getting active transaction by device and nozzle",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	var tx models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND nozzle_id = ? AND status IN (?)",
			deviceID, nozzleID,
			[]string{
				string(models.TransactionStatusInitiated),
				string(models.TransactionStatusFilling),
				string(models.TransactionStatusAwaitingFinalDC2),
			}).
		Order("created_at DESC").
		First(&tx).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			r.logger.Debug("No active transaction found",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID))
			return nil, nil // 没有活跃交易是正常的
		}
		r.logger.Error("Failed to get active transaction",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get active transaction: %w", err)
	}

	r.logger.Debug("Active transaction found",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("transaction_id", tx.ID))
	return &tx, nil
}

// GetActiveByDevice 获取设备的所有活跃交易
func (r *gormRepository) GetActiveByDevice(ctx context.Context, deviceID string) ([]*models.Transaction, error) {
	r.logger.Debug("Getting active transactions by device",
		zap.String("device_id", deviceID))

	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND status IN (?)",
			deviceID,
			[]string{
				string(models.TransactionStatusInitiated),
				string(models.TransactionStatusFilling),
				string(models.TransactionStatusAwaitingFinalDC2),
			}).
		Order("created_at DESC").
		Find(&transactions).Error

	if err != nil {
		r.logger.Error("Failed to get active transactions",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get active transactions: %w", err)
	}

	r.logger.Debug("Active transactions retrieved",
		zap.String("device_id", deviceID),
		zap.Int("count", len(transactions)))
	return transactions, nil
}

// GetByDeviceAndTimeRange 获取设备指定时间范围内的交易
func (r *gormRepository) GetByDeviceAndTimeRange(ctx context.Context, deviceID string, start, end time.Time) ([]*models.Transaction, error) {
	r.logger.Debug("Getting transactions by device and time range",
		zap.String("device_id", deviceID),
		zap.Time("start", start),
		zap.Time("end", end))

	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND created_at BETWEEN ? AND ?", deviceID, start, end).
		Order("created_at DESC").
		Find(&transactions).Error

	if err != nil {
		r.logger.Error("Failed to get transactions by time range",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get transactions by time range: %w", err)
	}

	r.logger.Debug("Transactions retrieved by time range",
		zap.String("device_id", deviceID),
		zap.Int("count", len(transactions)))
	return transactions, nil
}

// GetStaleTransactions 获取过期的交易
func (r *gormRepository) GetStaleTransactions(ctx context.Context, maxAge time.Duration) ([]*models.Transaction, error) {
	cutoffTime := time.Now().Add(-maxAge)
	r.logger.Debug("Getting stale transactions",
		zap.Duration("max_age", maxAge),
		zap.Time("cutoff_time", cutoffTime))

	var staleTransactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("status IN (?) AND created_at < ?",
			[]string{string(models.TransactionStatusInitiated), string(models.TransactionStatusFilling)},
			cutoffTime).
		Find(&staleTransactions).Error

	if err != nil {
		r.logger.Error("Failed to get stale transactions", zap.Error(err))
		return nil, fmt.Errorf("failed to get stale transactions: %w", err)
	}

	r.logger.Debug("Stale transactions retrieved",
		zap.Int("count", len(staleTransactions)))
	return staleTransactions, nil
}

// ==================== 复杂查询操作 ====================

// List 根据过滤条件查询交易列表
func (r *gormRepository) List(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error) {
	r.logger.Debug("Listing transactions with filter",
		zap.Any("filter", filter))

	query := r.db.WithContext(ctx).Model(&models.Transaction{})

	// 应用过滤条件
	query = r.applyFilters(query, filter)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Failed to count transactions", zap.Error(err))
		return nil, fmt.Errorf("failed to count transactions: %w", err)
	}

	// 应用分页和排序
	query = r.applyPaginationAndSorting(query, filter)

	// 查询数据
	var transactions []*models.Transaction
	if err := query.Find(&transactions).Error; err != nil {
		r.logger.Error("Failed to list transactions", zap.Error(err))
		return nil, fmt.Errorf("failed to list transactions: %w", err)
	}

	// 计算分页信息
	page := filter.Page
	if page < 1 {
		page = 1
	}
	limit := filter.Limit
	if limit < 1 {
		limit = 20
	}

	hasMore := int64(page*limit) < total

	result := &TransactionListResult{
		Transactions: transactions,
		Total:        total,
		Page:         page,
		Limit:        limit,
		HasMore:      hasMore,
	}

	r.logger.Debug("Transactions listed successfully",
		zap.Int64("total", total),
		zap.Int("returned", len(transactions)))
	return result, nil
}

// Count 根据过滤条件统计交易数量
func (r *gormRepository) Count(ctx context.Context, filter TransactionFilter) (int64, error) {
	r.logger.Debug("Counting transactions with filter",
		zap.Any("filter", filter))

	query := r.db.WithContext(ctx).Model(&models.Transaction{})
	query = r.applyFilters(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		r.logger.Error("Failed to count transactions", zap.Error(err))
		return 0, fmt.Errorf("failed to count transactions: %w", err)
	}

	r.logger.Debug("Transactions counted successfully",
		zap.Int64("count", count))
	return count, nil
}

// GetStats 获取交易统计信息
func (r *gormRepository) GetStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error) {
	r.logger.Debug("Getting transaction stats",
		zap.Any("filter", filter))

	query := r.db.WithContext(ctx).Model(&models.Transaction{})
	query = r.applyStatsFilters(query, filter)

	// 基础统计查询
	var result struct {
		TotalCount     int64
		TotalVolume    decimal.Decimal
		TotalAmount    decimal.Decimal
		CompletedCount int64
		CancelledCount int64
		FailedCount    int64
	}

	if err := query.Select(`
		COUNT(*) as total_count,
		COALESCE(SUM(actual_volume), 0) as total_volume,
		COALESCE(SUM(actual_amount), 0) as total_amount,
		COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
		COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
		COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
	`).Scan(&result).Error; err != nil {
		r.logger.Error("Failed to get transaction stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get transaction stats: %w", err)
	}

	// 计算平均值
	var avgVolume, avgAmount, avgUnitPrice decimal.Decimal
	if result.TotalCount > 0 {
		avgVolume = result.TotalVolume.Div(decimal.NewFromInt(result.TotalCount))
		avgAmount = result.TotalAmount.Div(decimal.NewFromInt(result.TotalCount))

		// 计算平均单价
		if !result.TotalVolume.IsZero() {
			avgUnitPrice = result.TotalAmount.Div(result.TotalVolume)
		}
	}

	stats := &TransactionStats{
		TotalCount:       result.TotalCount,
		TotalVolume:      result.TotalVolume,
		TotalAmount:      result.TotalAmount,
		AverageVolume:    avgVolume,
		AverageAmount:    avgAmount,
		AverageUnitPrice: avgUnitPrice,
		CompletedCount:   result.CompletedCount,
		CancelledCount:   result.CancelledCount,
		FailedCount:      result.FailedCount,
	}

	// 如果需要分组统计
	if filter.GroupBy != "" {
		periodStats, err := r.getPeriodStats(ctx, query, filter)
		if err != nil {
			r.logger.Warn("Failed to get period stats", zap.Error(err))
		} else {
			stats.PeriodStats = periodStats
		}
	}

	r.logger.Debug("Transaction stats retrieved successfully",
		zap.Int64("total_count", stats.TotalCount))
	return stats, nil
}

// GetDeviceSummary 获取设备交易汇总信息
func (r *gormRepository) GetDeviceSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error) {
	r.logger.Debug("Getting device summary",
		zap.String("device_id", deviceID),
		zap.Time("start", timeRange.Start),
		zap.Time("end", timeRange.End))

	query := r.db.WithContext(ctx).Model(&models.Transaction{}).
		Where("device_id = ?", deviceID).
		Where("created_at BETWEEN ? AND ?", timeRange.Start, timeRange.End)

	// 基础统计
	var result struct {
		TotalCount  int64
		TotalVolume decimal.Decimal
		TotalAmount decimal.Decimal
	}

	if err := query.Select(`
		COUNT(*) as total_count,
		COALESCE(SUM(actual_volume), 0) as total_volume,
		COALESCE(SUM(actual_amount), 0) as total_amount
	`).Scan(&result).Error; err != nil {
		r.logger.Error("Failed to get device summary", zap.Error(err))
		return nil, fmt.Errorf("failed to get device summary: %w", err)
	}

	// 获取最后一笔交易
	var lastTransaction models.Transaction
	if err := query.Order("created_at DESC").First(&lastTransaction).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.logger.Error("Failed to get last transaction", zap.Error(err))
		return nil, fmt.Errorf("failed to get last transaction: %w", err)
	}

	// 获取喷嘴统计
	nozzleSummaries, err := r.getNozzleSummaries(ctx, deviceID, timeRange)
	if err != nil {
		r.logger.Warn("Failed to get nozzle summaries", zap.Error(err))
	}

	summary := &DeviceTransactionSummary{
		DeviceID:        deviceID,
		TotalCount:      result.TotalCount,
		TotalVolume:     result.TotalVolume,
		TotalAmount:     result.TotalAmount,
		NozzleSummaries: nozzleSummaries,
	}

	if lastTransaction.ID != "" {
		summary.LastTransaction = &lastTransaction
	}

	r.logger.Debug("Device summary retrieved successfully",
		zap.String("device_id", deviceID),
		zap.Int64("total_count", summary.TotalCount))
	return summary, nil
}

// GetLifecycleStatistics 获取生命周期统计信息
func (r *gormRepository) GetLifecycleStatistics(ctx context.Context, deviceID string) (*LifecycleStatistics, error) {
	r.logger.Debug("Getting lifecycle statistics",
		zap.String("device_id", deviceID))

	stats := &LifecycleStatistics{
		DeviceID: deviceID,
	}

	// 获取活跃交易数
	var activeCount int64
	err := r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Where("device_id = ? AND status IN (?)",
			deviceID,
			[]string{string(models.TransactionStatusInitiated), string(models.TransactionStatusFilling)}).
		Count(&activeCount).Error

	if err != nil {
		r.logger.Error("Failed to get active transaction count", zap.Error(err))
		return nil, fmt.Errorf("failed to get active transaction count: %w", err)
	}
	stats.ActiveTransactions = int(activeCount)

	// 获取今日统计
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)

	// 今日完成交易数
	var completedToday int64
	err = r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Where("device_id = ? AND status = ? AND completed_at BETWEEN ? AND ?",
			deviceID, string(models.TransactionStatusCompleted), today, tomorrow).
		Count(&completedToday).Error

	if err != nil {
		r.logger.Error("Failed to get completed transaction count", zap.Error(err))
		return nil, fmt.Errorf("failed to get completed transaction count: %w", err)
	}
	stats.CompletedToday = int(completedToday)

	// 今日体积和金额汇总
	var todayStats struct {
		TotalVolume decimal.Decimal `gorm:"column:total_volume"`
		TotalAmount decimal.Decimal `gorm:"column:total_amount"`
	}

	err = r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Select("COALESCE(SUM(actual_volume), 0) as total_volume, COALESCE(SUM(actual_amount), 0) as total_amount").
		Where("device_id = ? AND status = ? AND completed_at BETWEEN ? AND ?",
			deviceID, string(models.TransactionStatusCompleted), today, tomorrow).
		Scan(&todayStats).Error

	if err != nil {
		r.logger.Error("Failed to get today's volume/amount stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get today's volume/amount stats: %w", err)
	}

	stats.TotalVolumeToday = todayStats.TotalVolume
	stats.TotalAmountToday = todayStats.TotalAmount

	// 平均加油时间
	var avgDuration struct {
		AvgSeconds float64 `gorm:"column:avg_seconds"`
	}

	err = r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Select("AVG(TIMESTAMPDIFF(SECOND, filling_at, completed_at)) as avg_seconds").
		Where("device_id = ? AND status = ? AND filling_at IS NOT NULL AND completed_at IS NOT NULL AND completed_at BETWEEN ? AND ?",
			deviceID, string(models.TransactionStatusCompleted), today, tomorrow).
		Scan(&avgDuration).Error

	if err != nil {
		r.logger.Error("Failed to get average filling time", zap.Error(err))
		return nil, fmt.Errorf("failed to get average filling time: %w", err)
	}

	if avgDuration.AvgSeconds > 0 {
		stats.AverageFillingTime = time.Duration(avgDuration.AvgSeconds) * time.Second
	}

	// 最后交易时间
	var lastTransaction models.Transaction
	err = r.db.WithContext(ctx).
		Where("device_id = ?", deviceID).
		Order("created_at DESC").
		First(&lastTransaction).Error

	if err == nil {
		stats.LastTransactionTime = &lastTransaction.CreatedAt
	} else if err != gorm.ErrRecordNotFound {
		r.logger.Error("Failed to get last transaction time", zap.Error(err))
		return nil, fmt.Errorf("failed to get last transaction time: %w", err)
	}

	r.logger.Debug("Lifecycle statistics retrieved successfully",
		zap.String("device_id", deviceID),
		zap.Int("active_transactions", stats.ActiveTransactions))
	return stats, nil
}

// GetTransactionByPumpReading 根据泵码匹配查找交易
// 优化：1. 不判断status 2. 判断end pump reading为空或为0
func (r *gormRepository) GetTransactionByPumpReading(ctx context.Context, deviceID string, nozzleID string, counterType int16, previousValue decimal.Decimal) (*models.Transaction, error) {
	r.logger.Debug("Finding transaction by pump reading match",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Int16("counter_type", counterType),
		zap.String("previous_value", previousValue.String()))

	var tx models.Transaction
	var query *gorm.DB

	// 根据计数器类型选择匹配字段，并确保end pump reading为空或为0
	if counterType >= 1 && counterType <= 8 {
		// 体积计数器：匹配 start_pump_volume_reading，且 end_pump_volume_reading 为空或为0
		query = r.db.WithContext(ctx).
			Where("device_id = ? AND nozzle_id = ? AND start_pump_volume_reading = ? AND (end_pump_volume_reading IS NULL OR end_pump_volume_reading = 0)",
				deviceID, nozzleID, previousValue)
	} else if counterType >= 17 && counterType <= 24 {
		// 金额计数器：匹配 start_pump_amount_reading，且 end_pump_amount_reading 为空或为0
		query = r.db.WithContext(ctx).
			Where("device_id = ? AND nozzle_id = ? AND start_pump_amount_reading = ? AND (end_pump_amount_reading IS NULL OR end_pump_amount_reading = 0)",
				deviceID, nozzleID, previousValue)
	} else {
		// 未知计数器类型
		r.logger.Warn("Unknown counter type for pump reading match",
			zap.Int16("counter_type", counterType))
		return nil, nil
	}

	err := query.Order("created_at DESC").First(&tx).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			r.logger.Debug("No matching transaction found for pump reading",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Int16("counter_type", counterType),
				zap.String("previous_value", previousValue.String()))
			return nil, nil // 找不到匹配交易是正常的
		}
		r.logger.Error("Failed to find transaction by pump reading",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to find transaction by pump reading: %w", err)
	}

	r.logger.Debug("Found matching transaction for pump reading",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	return &tx, nil
}

// ==================== 批量操作 ====================

// BatchCreate 批量创建交易记录
func (r *gormRepository) BatchCreate(ctx context.Context, transactions []*models.Transaction) error {
	if len(transactions) == 0 {
		return nil
	}

	r.logger.Debug("Batch creating transactions",
		zap.Int("count", len(transactions)))

	if err := r.db.WithContext(ctx).CreateInBatches(transactions, 100).Error; err != nil {
		r.logger.Error("Failed to batch create transactions", zap.Error(err))
		return fmt.Errorf("failed to batch create transactions: %w", err)
	}

	r.logger.Debug("Transactions batch created successfully",
		zap.Int("count", len(transactions)))
	return nil
}

// BatchUpdate 批量更新交易记录
func (r *gormRepository) BatchUpdate(ctx context.Context, transactions []*models.Transaction) error {
	if len(transactions) == 0 {
		return nil
	}

	r.logger.Debug("Batch updating transactions",
		zap.Int("count", len(transactions)))

	// GORM不直接支持批量更新，这里使用事务逐个更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, transaction := range transactions {
			if err := tx.Save(transaction).Error; err != nil {
				r.logger.Error("Failed to update transaction in batch",
					zap.String("transaction_id", transaction.ID),
					zap.Error(err))
				return err
			}
		}
		return nil
	})
}

// ==================== 辅助方法 ====================

// applyFilters 应用查询过滤条件
func (r *gormRepository) applyFilters(query *gorm.DB, filter TransactionFilter) *gorm.DB {
	if filter.DeviceID != nil {
		query = query.Where("device_id = ?", *filter.DeviceID)
	}

	if filter.NozzleID != nil {
		query = query.Where("nozzle_id = ?", *filter.NozzleID)
	}

	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}

	if filter.Type != nil {
		query = query.Where("type = ?", *filter.Type)
	}

	if filter.OperatorID != nil {
		query = query.Where("operator_id = ?", *filter.OperatorID)
	}

	if filter.StationID != nil {
		query = query.Where("station_id = ?", *filter.StationID)
	}

	if filter.StartTime != nil {
		query = query.Where("created_at >= ?", *filter.StartTime)
	}

	if filter.EndTime != nil {
		query = query.Where("created_at <= ?", *filter.EndTime)
	}

	if filter.MinAmount != nil {
		query = query.Where("actual_amount >= ?", *filter.MinAmount)
	}

	if filter.MaxAmount != nil {
		query = query.Where("actual_amount <= ?", *filter.MaxAmount)
	}

	if filter.MinVolume != nil {
		query = query.Where("actual_volume >= ?", *filter.MinVolume)
	}

	if filter.MaxVolume != nil {
		query = query.Where("actual_volume <= ?", *filter.MaxVolume)
	}

	return query
}

// applyStatsFilters 应用统计过滤条件
func (r *gormRepository) applyStatsFilters(query *gorm.DB, filter TransactionStatsFilter) *gorm.DB {
	if filter.DeviceID != nil {
		query = query.Where("device_id = ?", *filter.DeviceID)
	}

	if filter.StationID != nil {
		query = query.Where("station_id = ?", *filter.StationID)
	}

	if filter.OperatorID != nil {
		query = query.Where("operator_id = ?", *filter.OperatorID)
	}

	if filter.StartTime != nil {
		query = query.Where("created_at >= ?", *filter.StartTime)
	}

	if filter.EndTime != nil {
		query = query.Where("created_at <= ?", *filter.EndTime)
	}

	return query
}

// applyPaginationAndSorting 应用分页和排序
func (r *gormRepository) applyPaginationAndSorting(query *gorm.DB, filter TransactionFilter) *gorm.DB {
	// 排序
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := strings.ToUpper(filter.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	page := filter.Page
	if page < 1 {
		page = 1
	}

	limit := filter.Limit
	if limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100 // 限制最大查询数量
	}

	offset := (page - 1) * limit
	query = query.Offset(offset).Limit(limit)

	return query
}

// getPeriodStats 获取时期统计
func (r *gormRepository) getPeriodStats(ctx context.Context, baseQuery *gorm.DB, filter TransactionStatsFilter) ([]*PeriodStat, error) {
	var dateFormat string
	var groupBy string

	switch filter.GroupBy {
	case "hour":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d %H:00')"
		groupBy = "hour_period"
	case "day":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d')"
		groupBy = "day_period"
	default:
		return nil, fmt.Errorf("unsupported group by: %s", filter.GroupBy)
	}

	var results []struct {
		Period       string          `json:"period"`
		Count        int64           `json:"count"`
		Volume       decimal.Decimal `json:"volume"`
		Amount       decimal.Decimal `json:"amount"`
		AvgUnitPrice decimal.Decimal `json:"avg_unit_price"`
	}

	query := baseQuery.Select(fmt.Sprintf(`
		%s as period,
		COUNT(*) as count,
		COALESCE(SUM(actual_volume), 0) as volume,
		COALESCE(SUM(actual_amount), 0) as amount,
		CASE 
			WHEN SUM(actual_volume) > 0 THEN SUM(actual_amount) / SUM(actual_volume)
			ELSE 0 
		END as avg_unit_price
	`, dateFormat)).Group(groupBy).Order("period ASC")

	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get period stats: %w", err)
	}

	// 转换结果
	periodStats := make([]*PeriodStat, len(results))
	for i, result := range results {
		periodStats[i] = &PeriodStat{
			Period:       result.Period,
			Count:        result.Count,
			Volume:       result.Volume,
			Amount:       result.Amount,
			AvgUnitPrice: result.AvgUnitPrice,
		}
	}

	return periodStats, nil
}

// getNozzleSummaries 获取喷嘴统计
func (r *gormRepository) getNozzleSummaries(ctx context.Context, deviceID string, timeRange TimeRange) ([]*NozzleTransactionSummary, error) {
	var results []struct {
		NozzleID string          `json:"nozzle_id"`
		Count    int64           `json:"count"`
		Volume   decimal.Decimal `json:"volume"`
		Amount   decimal.Decimal `json:"amount"`
	}

	query := r.db.WithContext(ctx).Model(&models.Transaction{}).
		Select(`
			nozzle_id,
			COUNT(*) as count,
			COALESCE(SUM(actual_volume), 0) as volume,
			COALESCE(SUM(actual_amount), 0) as amount
		`).
		Where("device_id = ?", deviceID).
		Where("created_at BETWEEN ? AND ?", timeRange.Start, timeRange.End).
		Group("nozzle_id").
		Order("nozzle_id ASC")

	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get nozzle summaries: %w", err)
	}

	// 转换结果
	summaries := make([]*NozzleTransactionSummary, len(results))
	for i, result := range results {
		summaries[i] = &NozzleTransactionSummary{
			NozzleID: result.NozzleID,
			Count:    result.Count,
			Volume:   result.Volume,
			Amount:   result.Amount,
		}
	}

	return summaries, nil
}
