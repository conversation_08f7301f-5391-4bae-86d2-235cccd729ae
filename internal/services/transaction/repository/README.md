# Transaction Repository 统一数据访问层

## 概述

此包提供统一的交易数据访问层，合并了原来 `transaction` 和 `transaction_lifecycle` 两个服务的重复实现，遵循 CQRS 模式，支持命令和查询两种操作模式。

## 设计原则

### 1. 单一数据访问点
- 消除重复的CRUD操作实现
- 提供统一的数据访问接口
- 减少维护成本和代码重复

### 2. CQRS模式支持
- **命令操作（Command）**: 用于生命周期管理，专注写操作和实时查询
- **查询操作（Query）**: 用于数据分析和报表，专注复杂查询和统计

### 3. 接口隔离
- 不同服务只使用需要的方法
- 清晰的方法分组和文档说明
- 支持未来的扩展需求

## 接口结构

### Repository 接口
```go
type Repository interface {
    // 基础CRUD操作 - 共同使用
    Create(ctx context.Context, tx *models.Transaction) error
    GetByID(ctx context.Context, id string) (*models.Transaction, error)
    Update(ctx context.Context, tx *models.Transaction) error
    Delete(ctx context.Context, id string) error

    // 实时查询操作 - 生命周期服务专用
    GetActiveByDeviceAndNozzle(ctx context.Context, deviceID string, nozzleID byte) (*models.Transaction, error)
    GetActiveByDevice(ctx context.Context, deviceID string) ([]*models.Transaction, error)
    GetStaleTransactions(ctx context.Context, maxAge time.Duration) ([]*models.Transaction, error)

    // 复杂查询操作 - 查询服务专用
    List(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error)
    GetStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error)
    GetDeviceSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error)

    // 批量操作 - 查询服务专用
    BatchCreate(ctx context.Context, transactions []*models.Transaction) error
    BatchUpdate(ctx context.Context, transactions []*models.Transaction) error
}
```

## 文件说明

### interface.go
- 定义统一的Repository接口
- 包含所有查询类型和结果结构体
- 详细的接口文档和使用说明

### gorm_repository.go
- GORM数据库实现
- 合并了两个服务的所有数据访问逻辑
- 优化的查询性能和错误处理
- 完整的日志记录

## 使用示例

### 创建Repository实例
```go
// 创建统一的Repository
repo := repository.NewGormRepository(db, logger)
```

### 生命周期服务使用
```go
// 查询活跃交易
activeTx, err := repo.GetActiveByDeviceAndNozzle(ctx, "device-001", 1)

// 创建新交易
tx := &models.Transaction{...}
err := repo.Create(ctx, tx)

// 获取生命周期统计
stats, err := repo.GetLifecycleStatistics(ctx, "device-001")
```

### 查询服务使用
```go
// 复杂查询
filter := repository.TransactionFilter{
    DeviceID: &deviceID,
    StartTime: &startTime,
    EndTime: &endTime,
    Page: 1,
    Limit: 20,
}
result, err := repo.List(ctx, filter)

// 统计分析
statsFilter := repository.TransactionStatsFilter{
    DeviceID: &deviceID,
    GroupBy: "day",
}
stats, err := repo.GetStats(ctx, statsFilter)
```

## 性能优化

### 1. 查询优化
- 合理使用数据库索引
- 分页查询避免大数据量加载
- 批量操作提高处理效率

### 2. 缓存策略
- 活跃交易查询适合缓存
- 统计数据可以定期刷新缓存
- 设备摘要信息可以缓存

### 3. 数据库优化
- 建议在以下字段创建索引：
  - `device_id`
  - `nozzle_id` 
  - `status`
  - `created_at`
  - `completed_at`

## 错误处理

### 1. 统一错误格式
- 所有方法返回包装的错误信息
- 包含详细的上下文信息
- 区分业务错误和系统错误

### 2. 日志记录
- Debug级别：查询参数和结果统计
- Info级别：重要操作和性能指标
- Error级别：操作失败和异常情况

### 3. 事务支持
- 批量操作使用数据库事务
- 关键操作支持回滚
- 并发安全的操作

## 扩展指南

### 1. 添加新查询方法
1. 在 `interface.go` 中添加方法定义
2. 在 `gorm_repository.go` 中实现方法
3. 添加相应的测试用例
4. 更新文档说明

### 2. 添加新的过滤条件
1. 扩展 `TransactionFilter` 结构体
2. 在 `applyFilters` 方法中添加处理逻辑
3. 更新相关的查询方法

### 3. 性能优化
1. 分析慢查询日志
2. 添加合适的数据库索引
3. 优化复杂查询的SQL语句
4. 考虑读写分离和分库分表

## 测试策略

### 1. 单元测试
- 每个Repository方法都有对应测试
- 使用Mock数据库进行测试
- 覆盖正常和异常场景

### 2. 集成测试
- 使用真实数据库测试
- 验证复杂查询的正确性
- 性能基准测试

### 3. 压力测试
- 高并发场景测试
- 大数据量查询测试
- 内存和CPU使用监控

## 迁移指南

### 从旧版本迁移
1. 更新import路径：
   ```go
   // 旧版本
   import "fcc-service/internal/services/transaction"
   import "fcc-service/internal/services/transaction_lifecycle"
   
   // 新版本
   import "fcc-service/internal/services/transaction/repository"
   ```

2. 更新Repository创建：
   ```go
   // 旧版本
   txRepo := transaction.NewGormRepository(db, logger)
   lifecycleRepo := transaction_lifecycle.NewGormRepository(db, logger)
   
   // 新版本
   repo := repository.NewGormRepository(db, logger)
   ```

3. 更新方法调用：
   ```go
   // 方法名称保持不变，只是Repository实例统一
   tx, err := repo.GetByID(ctx, id)
   activeTx, err := repo.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
   ```

## 维护建议

### 1. 定期维护
- 监控查询性能
- 清理过期数据
- 优化数据库索引

### 2. 版本升级
- 保持向后兼容
- 渐进式迁移
- 充分测试验证

### 3. 监控告警
- 数据库连接池监控
- 慢查询告警
- 错误率监控 