package repository

import (
	"context"
	"time"

	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
)

// Repository 统一的交易数据访问接口
//
// 此接口合并了原来 transaction 和 transaction_lifecycle 两个服务的数据访问需求，
// 遵循 CQRS 模式，支持命令和查询两种操作模式：
//
// 1. 命令操作（Command）：用于生命周期管理
//   - 基础CRUD操作
//   - 实时查询（活跃交易、过期交易等）
//   - 设备相关查询
//
// 2. 查询操作（Query）：用于数据分析和报表
//   - 复杂过滤查询
//   - 统计和分析
//   - 批量操作
//
// 设计原则：
// - 单一数据访问点：避免重复实现
// - 接口隔离：不同服务只使用需要的方法
// - 依赖倒置：服务层依赖抽象而非具体实现
type Repository interface {
	// ==================== 基础CRUD操作 ====================
	// 这些方法被生命周期服务和查询服务共同使用

	// Create 创建新的交易记录
	Create(ctx context.Context, tx *models.Transaction) error

	// GetByID 根据ID获取交易记录
	GetByID(ctx context.Context, id string) (*models.Transaction, error)

	// Update 更新交易记录
	Update(ctx context.Context, tx *models.Transaction) error

	// Delete 删除交易记录
	Delete(ctx context.Context, id string) error

	// ==================== 实时查询操作 ====================
	// 这些方法主要被生命周期服务使用，用于实时交易管理

	// GetActiveByDeviceAndNozzle 获取设备特定喷嘴的活跃交易
	// 返回状态为 initiated 或 filling 的交易
	GetActiveByDeviceAndNozzle(ctx context.Context, deviceID string, nozzleID string) (*models.Transaction, error)

	// GetActiveByDevice 获取设备的所有活跃交易
	// 返回状态为 initiated 或 filling 的所有交易
	GetActiveByDevice(ctx context.Context, deviceID string) ([]*models.Transaction, error)

	// GetByDeviceAndTimeRange 获取设备指定时间范围内的交易
	// 用于生命周期管理中的时间窗口查询
	GetByDeviceAndTimeRange(ctx context.Context, deviceID string, start, end time.Time) ([]*models.Transaction, error)

	// GetStaleTransactions 获取过期的交易
	// 用于清理长时间未完成的交易
	GetStaleTransactions(ctx context.Context, maxAge time.Duration) ([]*models.Transaction, error)

	// ==================== 复杂查询操作 ====================
	// 这些方法主要被查询服务使用，用于数据分析和报表

	// List 根据过滤条件查询交易列表
	// 支持分页、排序、复杂过滤条件
	List(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error)

	// Count 根据过滤条件统计交易数量
	Count(ctx context.Context, filter TransactionFilter) (int64, error)

	// GetStats 获取交易统计信息
	// 支持按时间段、设备、操作员等维度统计
	GetStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error)

	// GetDeviceSummary 获取设备交易汇总信息
	// 包括总数、体积、金额、最后交易时间等
	GetDeviceSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error)

	// GetLifecycleStatistics 获取生命周期统计信息
	// 专门为生命周期服务提供的统计接口
	GetLifecycleStatistics(ctx context.Context, deviceID string) (*LifecycleStatistics, error)

	// GetTransactionByPumpReading 根据泵码匹配查找交易
	// 查找条件：deviceID + nozzleID + start_pump_reading == previousValue
	GetTransactionByPumpReading(ctx context.Context, deviceID string, nozzleID string, counterType int16, previousValue decimal.Decimal) (*models.Transaction, error)

	// ==================== 批量操作 ====================
	// 这些方法主要被查询服务使用，用于批量数据处理

	// BatchCreate 批量创建交易记录
	BatchCreate(ctx context.Context, transactions []*models.Transaction) error

	// BatchUpdate 批量更新交易记录
	BatchUpdate(ctx context.Context, transactions []*models.Transaction) error
}

// ==================== 查询类型定义 ====================

// TransactionFilter 交易查询过滤器
type TransactionFilter struct {
	// 基础过滤条件
	DeviceID   *string `json:"device_id,omitempty"`
	NozzleID   *string `json:"nozzle_id,omitempty"`
	Status     *string `json:"status,omitempty"`
	Type       *string `json:"type,omitempty"`
	OperatorID *string `json:"operator_id,omitempty"`
	StationID  *string `json:"station_id,omitempty"`

	// 时间范围过滤
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`

	// 数值范围过滤
	MinAmount *decimal.Decimal `json:"min_amount,omitempty"`
	MaxAmount *decimal.Decimal `json:"max_amount,omitempty"`
	MinVolume *decimal.Decimal `json:"min_volume,omitempty"`
	MaxVolume *decimal.Decimal `json:"max_volume,omitempty"`

	// 分页和排序
	Page      int    `json:"page"`
	Limit     int    `json:"limit"`
	SortBy    string `json:"sort_by"`
	SortOrder string `json:"sort_order"`
}

// TransactionListResult 交易列表查询结果
type TransactionListResult struct {
	Transactions []*models.Transaction `json:"transactions"`
	Total        int64                 `json:"total"`
	Page         int                   `json:"page"`
	Limit        int                   `json:"limit"`
	HasMore      bool                  `json:"has_more"`
}

// TransactionStatsFilter 交易统计过滤器
type TransactionStatsFilter struct {
	DeviceID   *string    `json:"device_id,omitempty"`
	StationID  *string    `json:"station_id,omitempty"`
	OperatorID *string    `json:"operator_id,omitempty"`
	StartTime  *time.Time `json:"start_time,omitempty"`
	EndTime    *time.Time `json:"end_time,omitempty"`
	GroupBy    string     `json:"group_by,omitempty"` // hour, day, month
}

// TransactionStats 交易统计结果
type TransactionStats struct {
	TotalCount       int64           `json:"total_count"`
	TotalVolume      decimal.Decimal `json:"total_volume"`
	TotalAmount      decimal.Decimal `json:"total_amount"`
	AverageVolume    decimal.Decimal `json:"average_volume"`
	AverageAmount    decimal.Decimal `json:"average_amount"`
	AverageUnitPrice decimal.Decimal `json:"average_unit_price"`
	CompletedCount   int64           `json:"completed_count"`
	CancelledCount   int64           `json:"cancelled_count"`
	FailedCount      int64           `json:"failed_count"`
	PeriodStats      []*PeriodStat   `json:"period_stats,omitempty"`
}

// PeriodStat 时期统计
type PeriodStat struct {
	Period       string          `json:"period"`
	Count        int64           `json:"count"`
	Volume       decimal.Decimal `json:"volume"`
	Amount       decimal.Decimal `json:"amount"`
	AvgUnitPrice decimal.Decimal `json:"avg_unit_price"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// DeviceTransactionSummary 设备交易汇总
type DeviceTransactionSummary struct {
	DeviceID        string                      `json:"device_id"`
	TotalCount      int64                       `json:"total_count"`
	TotalVolume     decimal.Decimal             `json:"total_volume"`
	TotalAmount     decimal.Decimal             `json:"total_amount"`
	LastTransaction *models.Transaction         `json:"last_transaction,omitempty"`
	NozzleSummaries []*NozzleTransactionSummary `json:"nozzle_summaries,omitempty"`
}

// NozzleTransactionSummary 喷嘴交易汇总
type NozzleTransactionSummary struct {
	NozzleID string          `json:"nozzle_id"`
	Count    int64           `json:"count"`
	Volume   decimal.Decimal `json:"volume"`
	Amount   decimal.Decimal `json:"amount"`
}

// LifecycleStatistics 生命周期统计信息
// 专门为生命周期服务设计的统计结构
type LifecycleStatistics struct {
	DeviceID            string          `json:"device_id"`
	ActiveTransactions  int             `json:"active_transactions"`
	CompletedToday      int             `json:"completed_today"`
	AverageFillingTime  time.Duration   `json:"average_filling_time"`
	TotalVolumeToday    decimal.Decimal `json:"total_volume_today"`
	TotalAmountToday    decimal.Decimal `json:"total_amount_today"`
	LastTransactionTime *time.Time      `json:"last_transaction_time"`
}
