package transaction

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/pkg/models"
)

// DeviceInfoProvider 设备信息提供者接口 - 接口隔离原则
type DeviceInfoProvider interface {
	GetDevice(ctx context.Context, deviceID string) (*models.Device, error)
	GetController(ctx context.Context, controllerID string) (*models.Controller, error)
}

// NozzleInfoProvider 喷嘴信息提供者接口
type NozzleInfoProvider interface {
	GetNozzle(ctx context.Context, deviceID string, number byte) (*models.Nozzle, error)
}

// OperatorInfoProvider 操作员信息提供者接口（预留）
type OperatorInfoProvider interface {
	GetOperator(ctx context.Context, operatorID string) (*OperatorInfo, error)
}

// StationInfoProvider 站点信息提供者接口（预留）
type StationInfoProvider interface {
	GetStation(ctx context.Context, stationID string) (*StationInfo, error)
}

// dataCollector 数据收集器实现
type dataCollector struct {
	// 使用接口依赖，而非具体实现
	deviceProvider   DeviceInfoProvider
	nozzleProvider   NozzleInfoProvider
	operatorProvider OperatorInfoProvider // 可选
	stationProvider  StationInfoProvider  // 可选
	logger           *zap.Logger
}

// NewDataCollector 创建数据收集器 - 使用接口依赖注入
func NewDataCollector(
	deviceProvider DeviceInfoProvider,
	nozzleProvider NozzleInfoProvider,
	logger *zap.Logger,
) TransactionDataCollector {
	return &dataCollector{
		deviceProvider: deviceProvider,
		nozzleProvider: nozzleProvider,
		logger:         logger,
	}
}

// SetOperatorProvider 设置操作员信息提供者（可选）
func (dc *dataCollector) SetOperatorProvider(provider OperatorInfoProvider) {
	dc.operatorProvider = provider
}

// SetStationProvider 设置站点信息提供者（可选）
func (dc *dataCollector) SetStationProvider(provider StationInfoProvider) {
	dc.stationProvider = provider
}

// CollectDeviceInfo 收集设备信息
func (dc *dataCollector) CollectDeviceInfo(ctx context.Context, deviceID string) (*DeviceContext, error) {
	device, err := dc.deviceProvider.GetDevice(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device info: %w", err)
	}

	// 获取控制器信息
	var controllerID string
	if device.ControllerID != "" {
		controllerID = device.ControllerID
	} else {
		controllerID = "unknown-controller"
		dc.logger.Warn("Device has no controller ID", zap.String("device_id", deviceID))
	}

	// 构建设备上下文
	deviceContext := &DeviceContext{
		DeviceID:     device.ID,
		DeviceName:   device.Name,
		DeviceType:   string(device.Type),
		ControllerID: controllerID,
		StationID:    device.StationID,
		Position:     extractPosition(device),
		SerialNumber: extractSerialNumber(device),
		Model:        extractModel(device),
		Vendor:       extractVendor(device),
	}

	// 尝试获取Island信息
	if device.Metadata != nil {
		if islandID, ok := device.Metadata["island_id"].(string); ok {
			deviceContext.IslandID = islandID
		}
	}

	return deviceContext, nil
}

// CollectOperatorInfo 收集操作员信息
func (dc *dataCollector) CollectOperatorInfo(ctx context.Context, operatorID string) (*OperatorContext, error) {
	if dc.operatorProvider == nil {
		// 返回默认操作员信息
		return &OperatorContext{
			OperatorID:   operatorID,
			OperatorName: "Unknown Operator",
			Department:   "Unknown",
			Role:         "operator",
		}, nil
	}

	operatorInfo, err := dc.operatorProvider.GetOperator(ctx, operatorID)
	if err != nil {
		dc.logger.Warn("Failed to get operator info, using default",
			zap.String("operator_id", operatorID),
			zap.Error(err))
		return &OperatorContext{
			OperatorID:   operatorID,
			OperatorName: "Unknown Operator",
		}, nil
	}

	return &OperatorContext{
		OperatorID:    operatorInfo.ID,
		OperatorName:  operatorInfo.Name,
		OperatorCode:  operatorInfo.Code,
		Department:    operatorInfo.Department,
		Role:          operatorInfo.Role,
		ShiftID:       operatorInfo.ShiftID,
		WorkstationID: operatorInfo.WorkstationID,
	}, nil
}

// CollectStationInfo 收集站点信息
func (dc *dataCollector) CollectStationInfo(ctx context.Context, stationID string) (*StationContext, error) {
	if dc.stationProvider == nil {
		// 返回基于设备信息的默认站点信息
		return &StationContext{
			StationID:   stationID,
			StationName: fmt.Sprintf("Station-%s", stationID),
			StationCode: stationID,
			Region:      "Unknown",
			CompanyID:   "default",
			CompanyName: "Unknown Company",
		}, nil
	}

	stationInfo, err := dc.stationProvider.GetStation(ctx, stationID)
	if err != nil {
		dc.logger.Warn("Failed to get station info, using default",
			zap.String("station_id", stationID),
			zap.Error(err))
		return &StationContext{
			StationID:   stationID,
			StationName: fmt.Sprintf("Station-%s", stationID),
		}, nil
	}

	return &StationContext{
		StationID:   stationInfo.ID,
		StationName: stationInfo.Name,
		StationCode: stationInfo.Code,
		Region:      stationInfo.Region,
		Address:     stationInfo.Address,
		CompanyID:   stationInfo.CompanyID,
		CompanyName: stationInfo.CompanyName,
	}, nil
}

// CollectFuelGradeInfo 收集油品信息
func (dc *dataCollector) CollectFuelGradeInfo(ctx context.Context, nozzleID string) (*FuelGradeContext, error) {
	// 解析设备ID和喷嘴编号
	deviceID, nozzleNumber, err := parseNozzleID(nozzleID)
	if err != nil {
		return nil, fmt.Errorf("invalid nozzle ID format: %w", err)
	}

	nozzle, err := dc.nozzleProvider.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to get nozzle info: %w", err)
	}

	// 构建油品上下文
	fuelContext := &FuelGradeContext{
		FuelType: "unknown", // 临时默认值，将被实际数据或智能推断替换
	}

	// 如果有油品等级信息
	if nozzle.FuelGrade != nil {
		fuelContext.FuelGradeID = nozzle.FuelGrade.ID
		fuelContext.FuelGradeName = nozzle.FuelGrade.Name
		fuelContext.FuelType = extractFuelType(nozzle.FuelGrade)
		fuelContext.OctaneRating = extractOctaneRating(nozzle.FuelGrade)
		// fuelContext.Density = extractDensity(nozzle.FuelGrade)
		// fuelContext.NetCalorificValue = extractCalorificValue(nozzle.FuelGrade)
	}

	return fuelContext, nil
}

// CollectPaymentInfo 收集支付信息（预留）
func (dc *dataCollector) CollectPaymentInfo(ctx context.Context, paymentRef string) (*PaymentContext, error) {
	// 目前返回空的支付上下文
	return &PaymentContext{
		PaymentMethod:   "unknown",
		PaymentProvider: "unknown",
	}, nil
}

// 辅助方法和类型定义

// OperatorInfo 操作员信息（预留）
type OperatorInfo struct {
	ID            string `json:"id"`
	Name          string `json:"name"`
	Code          string `json:"code"`
	Department    string `json:"department"`
	Role          string `json:"role"`
	ShiftID       string `json:"shift_id"`
	WorkstationID string `json:"workstation_id"`
}

// StationInfo 站点信息（预留）
type StationInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Region      string `json:"region"`
	Address     string `json:"address"`
	CompanyID   string `json:"company_id"`
	CompanyName string `json:"company_name"`
}

// 辅助提取方法
func extractPosition(device *models.Device) string {
	if device.Metadata != nil {
		if pos, ok := device.Metadata["position"].(string); ok {
			return pos
		}
	}
	return "unknown"
}

func extractSerialNumber(device *models.Device) string {
	if device.Metadata != nil {
		if sn, ok := device.Metadata["serial_number"].(string); ok {
			return sn
		}
	}
	return "unknown"
}

func extractModel(device *models.Device) string {
	if device.Metadata != nil {
		if model, ok := device.Metadata["model"].(string); ok {
			return model
		}
	}
	return "unknown"
}

func extractVendor(device *models.Device) string {
	if device.Metadata != nil {
		if vendor, ok := device.Metadata["vendor"].(string); ok {
			return vendor
		}
	}
	return "Wayne"
}

func extractFuelType(fuelGrade *models.FuelGrade) string {
	// 🚀 优先使用FuelGrade结构体的Type字段（真实的油品类型）
	if fuelGrade.Type != "" {
		return fuelGrade.Type
	}

	// 备选：从Metadata中提取（兼容性考虑）
	if fuelGrade.Metadata != nil {
		var metadata map[string]interface{}
		if err := json.Unmarshal(fuelGrade.Metadata, &metadata); err == nil {
			if fuelType, ok := metadata["fuel_type"].(string); ok {
				return fuelType
			}
		}
	}

	// 最后的默认值
	return "gasoline"
}

func extractOctaneRating(fuelGrade *models.FuelGrade) int {
	if fuelGrade.Metadata != nil {
		var metadata map[string]interface{}
		if err := json.Unmarshal(fuelGrade.Metadata, &metadata); err == nil {
			if octane, ok := metadata["octane_rating"].(float64); ok {
				return int(octane)
			}
		}
	}
	return 92
}

func extractDensity(fuelGrade *models.FuelGrade) decimal.Decimal {
	if fuelGrade.Metadata != nil {
		var metadata map[string]interface{}
		if err := json.Unmarshal(fuelGrade.Metadata, &metadata); err == nil {
			if density, ok := metadata["density"].(string); ok {
				if d, err := decimal.NewFromString(density); err == nil {
					return d
				}
			}
		}
	}
	return decimal.NewFromFloat(0.75) // 默认汽油密度
}

func extractCalorificValue(fuelGrade *models.FuelGrade) decimal.Decimal {
	if fuelGrade.Metadata != nil {
		var metadata map[string]interface{}
		if err := json.Unmarshal(fuelGrade.Metadata, &metadata); err == nil {
			if calorific, ok := metadata["calorific_value"].(string); ok {
				if c, err := decimal.NewFromString(calorific); err == nil {
					return c
				}
			}
		}
	}
	return decimal.NewFromFloat(44.0) // 默认热值
}

func parseNozzleID(nozzleID string) (deviceID string, nozzleNumber byte, err error) {
	// 假设nozzleID格式为 "deviceID-nozzle-N" 或直接为数字
	if len(nozzleID) == 1 && nozzleID[0] >= '1' && nozzleID[0] <= '8' {
		return "", byte(nozzleID[0] - '0'), nil
	}

	// 其他格式解析逻辑...
	return "", 1, fmt.Errorf("unsupported nozzle ID format: %s", nozzleID)
}
