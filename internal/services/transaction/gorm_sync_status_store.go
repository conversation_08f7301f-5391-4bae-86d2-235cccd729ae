package transaction

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"go.uber.org/zap"
)

type gormSyncStatusStore struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewGormSyncStatusStore 创建一个新的持久化状态存储实例
func NewGormSyncStatusStore(db *gorm.DB, logger *zap.Logger) SyncStatusStore {
	store := &gormSyncStatusStore{
		db:     db,
		logger: logger,
	}

	// 自动迁移数据库结构
	if err := db.AutoMigrate(&SyncStatusModel{}); err != nil {
		logger.Error("Failed to migrate sync status table", zap.Error(err))
		// 在此处处理错误，如果关键则记录日志并退出
	} else {
		logger.Info("Sync status table migration completed successfully")
	}

	return store
}

// Save 保存同步状态
func (s *gormSyncStatusStore) Save(ctx context.Context, status *SyncStatus) error {
	model := s.toModel(status)
	
	result := s.db.WithContext(ctx).Create(model)
	if result.Error != nil {
		s.logger.Error("Failed to save sync status",
			zap.String("transaction_id", status.TransactionID),
			zap.String("system_id", status.SystemID),
			zap.Error(result.Error))
		return fmt.Errorf("failed to save sync status: %w", result.Error)
	}

	s.logger.Debug("Sync status saved successfully",
		zap.String("transaction_id", status.TransactionID),
		zap.String("system_id", status.SystemID),
		zap.String("status", status.Status))

	return nil
}

// Get 获取指定交易和系统的同步状态
func (s *gormSyncStatusStore) Get(ctx context.Context, transactionID, systemID string) (*SyncStatus, error) {
	var model SyncStatusModel
	
	result := s.db.WithContext(ctx).Where(
		"transaction_id = ? AND system_id = ?", 
		transactionID, systemID,
	).First(&model)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("sync status not found for transaction %s and system %s", transactionID, systemID)
		}
		return nil, fmt.Errorf("failed to get sync status: %w", result.Error)
	}

	return s.fromModel(&model), nil
}

// List 获取指定交易的所有同步状态
func (s *gormSyncStatusStore) List(ctx context.Context, transactionID string) ([]*SyncStatus, error) {
	var models []SyncStatusModel
	
	result := s.db.WithContext(ctx).Where("transaction_id = ?", transactionID).Find(&models)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to list sync statuses: %w", result.Error)
	}

	statuses := make([]*SyncStatus, len(models))
	for i, model := range models {
		statuses[i] = s.fromModel(&model)
	}

	return statuses, nil
}

// Update 更新同步状态
func (s *gormSyncStatusStore) Update(ctx context.Context, status *SyncStatus) error {
	updates := map[string]interface{}{
		"status":        status.Status,
		"attempt_count": status.AttemptCount,
		"last_attempt":  status.LastAttempt,
		"last_error":    status.LastError,
		"next_retry":    status.NextRetry,
		"updated_at":    time.Now(),
	}

	result := s.db.WithContext(ctx).Model(&SyncStatusModel{}).Where(
		"transaction_id = ? AND system_id = ?",
		status.TransactionID, status.SystemID,
	).Updates(updates)

	if result.Error != nil {
		s.logger.Error("Failed to update sync status",
			zap.String("transaction_id", status.TransactionID),
			zap.String("system_id", status.SystemID),
			zap.Error(result.Error))
		return fmt.Errorf("failed to update sync status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("sync status not found for update: transaction_id=%s, system_id=%s", 
			status.TransactionID, status.SystemID)
	}

	s.logger.Debug("Sync status updated successfully",
		zap.String("transaction_id", status.TransactionID),
		zap.String("system_id", status.SystemID),
		zap.String("status", status.Status))

	return nil
}

// Delete 删除同步状态
func (s *gormSyncStatusStore) Delete(ctx context.Context, transactionID, systemID string) error {
	result := s.db.WithContext(ctx).Where(
		"transaction_id = ? AND system_id = ?",
		transactionID, systemID,
	).Delete(&SyncStatusModel{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete sync status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("sync status not found for deletion: transaction_id=%s, system_id=%s", 
			transactionID, systemID)
	}

	s.logger.Debug("Sync status deleted successfully",
		zap.String("transaction_id", transactionID),
		zap.String("system_id", systemID))

	return nil
}

// toModel 将SyncStatus转换为SyncStatusModel
func (s *gormSyncStatusStore) toModel(status *SyncStatus) *SyncStatusModel {
	return &SyncStatusModel{
		TransactionID: status.TransactionID,
		SystemID:      status.SystemID,
		Status:        status.Status,
		AttemptCount:  status.AttemptCount,
		LastAttempt:   status.LastAttempt,
		LastError:     status.LastError,
		NextRetry:     status.NextRetry,
	}
}

// fromModel 将SyncStatusModel转换为SyncStatus
func (s *gormSyncStatusStore) fromModel(model *SyncStatusModel) *SyncStatus {
	return &SyncStatus{
		SystemID:      model.SystemID,
		TransactionID: model.TransactionID,
		Status:        model.Status,
		AttemptCount:  model.AttemptCount,
		LastAttempt:   model.LastAttempt,
		LastError:     model.LastError,
		NextRetry:     model.NextRetry,
	}
} 