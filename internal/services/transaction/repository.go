package transaction

import (
	"context"
	"fmt"
	"strings"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"fcc-service/pkg/models"
)

// TransactionRepository 交易数据访问接口
type TransactionRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, tx *models.Transaction) error
	GetByID(ctx context.Context, id string) (*models.Transaction, error)
	Update(ctx context.Context, tx *models.Transaction) error
	Delete(ctx context.Context, id string) error

	// 查询操作
	List(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error)
	Count(ctx context.Context, filter TransactionFilter) (int64, error)

	// 统计操作
	GetStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error)
	GetDeviceSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error)

	// 批量操作
	BatchCreate(ctx context.Context, transactions []*models.Transaction) error
	BatchUpdate(ctx context.Context, transactions []*models.Transaction) error
}

// gormRepository GORM数据库实现
type gormRepository struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewGormRepository 创建GORM仓储实现
func NewGormRepository(db *gorm.DB, logger *zap.Logger) TransactionRepository {
	return &gormRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建交易
func (r *gormRepository) Create(ctx context.Context, tx *models.Transaction) error {
	if err := r.db.WithContext(ctx).Create(tx).Error; err != nil {
		return fmt.Errorf("failed to create transaction: %w", err)
	}
	return nil
}

// GetByID 根据ID获取交易
func (r *gormRepository) GetByID(ctx context.Context, id string) (*models.Transaction, error) {
	var tx models.Transaction
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&tx).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("transaction not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}
	return &tx, nil
}

// Update 更新交易
func (r *gormRepository) Update(ctx context.Context, tx *models.Transaction) error {
	if err := r.db.WithContext(ctx).Save(tx).Error; err != nil {
		return fmt.Errorf("failed to update transaction: %w", err)
	}
	return nil
}

// Delete 删除交易
func (r *gormRepository) Delete(ctx context.Context, id string) error {
	if err := r.db.WithContext(ctx).Delete(&models.Transaction{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete transaction: %w", err)
	}
	return nil
}

// List 查询交易列表
func (r *gormRepository) List(ctx context.Context, filter TransactionFilter) (*TransactionListResult, error) {
	query := r.db.WithContext(ctx).Model(&models.Transaction{})

	// 应用过滤条件
	query = r.applyFilters(query, filter)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count transactions: %w", err)
	}

	// 应用分页和排序
	query = r.applyPaginationAndSorting(query, filter)

	// 查询数据
	var transactions []*models.Transaction
	if err := query.Find(&transactions).Error; err != nil {
		return nil, fmt.Errorf("failed to list transactions: %w", err)
	}

	// 计算分页信息
	page := filter.Page
	if page < 1 {
		page = 1
	}
	limit := filter.Limit
	if limit < 1 {
		limit = 20
	}

	hasMore := int64(page*limit) < total

	return &TransactionListResult{
		Transactions: transactions,
		Total:        total,
		Page:         page,
		Limit:        limit,
		HasMore:      hasMore,
	}, nil
}

// Count 统计交易数量
func (r *gormRepository) Count(ctx context.Context, filter TransactionFilter) (int64, error) {
	query := r.db.WithContext(ctx).Model(&models.Transaction{})
	query = r.applyFilters(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count transactions: %w", err)
	}

	return count, nil
}

// GetStats 获取交易统计
func (r *gormRepository) GetStats(ctx context.Context, filter TransactionStatsFilter) (*TransactionStats, error) {
	query := r.db.WithContext(ctx).Model(&models.Transaction{})

	// 应用过滤条件
	query = r.applyStatsFilters(query, filter)

	// 基础统计查询
	var result struct {
		TotalCount     int64
		TotalVolume    decimal.Decimal
		TotalAmount    decimal.Decimal
		CompletedCount int64
		CancelledCount int64
		FailedCount    int64
	}

	if err := query.Select(`
		COUNT(*) as total_count,
		COALESCE(SUM(actual_volume), 0) as total_volume,
		COALESCE(SUM(actual_amount), 0) as total_amount,
		COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
		COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
		COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
	`).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("failed to get transaction stats: %w", err)
	}

	// 计算平均值
	var avgVolume, avgAmount, avgUnitPrice decimal.Decimal
	if result.TotalCount > 0 {
		avgVolume = result.TotalVolume.Div(decimal.NewFromInt(result.TotalCount))
		avgAmount = result.TotalAmount.Div(decimal.NewFromInt(result.TotalCount))

		// 计算平均单价
		if !result.TotalVolume.IsZero() {
			avgUnitPrice = result.TotalAmount.Div(result.TotalVolume)
		}
	}

	stats := &TransactionStats{
		TotalCount:       result.TotalCount,
		TotalVolume:      result.TotalVolume,
		TotalAmount:      result.TotalAmount,
		AverageVolume:    avgVolume,
		AverageAmount:    avgAmount,
		AverageUnitPrice: avgUnitPrice,
		CompletedCount:   result.CompletedCount,
		CancelledCount:   result.CancelledCount,
		FailedCount:      result.FailedCount,
	}

	// 如果需要分组统计
	if filter.GroupBy != "" {
		periodStats, err := r.getPeriodStats(ctx, query, filter)
		if err != nil {
			r.logger.Warn("Failed to get period stats", zap.Error(err))
		} else {
			stats.PeriodStats = periodStats
		}
	}

	return stats, nil
}

// GetDeviceSummary 获取设备交易汇总
func (r *gormRepository) GetDeviceSummary(ctx context.Context, deviceID string, timeRange TimeRange) (*DeviceTransactionSummary, error) {
	query := r.db.WithContext(ctx).Model(&models.Transaction{}).
		Where("device_id = ?", deviceID).
		Where("created_at BETWEEN ? AND ?", timeRange.Start, timeRange.End)

	// 基础统计
	var result struct {
		TotalCount  int64
		TotalVolume decimal.Decimal
		TotalAmount decimal.Decimal
	}

	if err := query.Select(`
		COUNT(*) as total_count,
		COALESCE(SUM(actual_volume), 0) as total_volume,
		COALESCE(SUM(actual_amount), 0) as total_amount
	`).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("failed to get device summary: %w", err)
	}

	// 获取最后一笔交易
	var lastTransaction models.Transaction
	if err := query.Order("created_at DESC").First(&lastTransaction).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get last transaction: %w", err)
	}

	// 获取喷嘴统计
	nozzleSummaries, err := r.getNozzleSummaries(ctx, deviceID, timeRange)
	if err != nil {
		r.logger.Warn("Failed to get nozzle summaries", zap.Error(err))
	}

	summary := &DeviceTransactionSummary{
		DeviceID:        deviceID,
		TotalCount:      result.TotalCount,
		TotalVolume:     result.TotalVolume,
		TotalAmount:     result.TotalAmount,
		NozzleSummaries: nozzleSummaries,
	}

	if lastTransaction.ID != "" {
		summary.LastTransaction = &lastTransaction
	}

	return summary, nil
}

// BatchCreate 批量创建交易
func (r *gormRepository) BatchCreate(ctx context.Context, transactions []*models.Transaction) error {
	if len(transactions) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).CreateInBatches(transactions, 100).Error; err != nil {
		return fmt.Errorf("failed to batch create transactions: %w", err)
	}

	return nil
}

// BatchUpdate 批量更新交易
func (r *gormRepository) BatchUpdate(ctx context.Context, transactions []*models.Transaction) error {
	if len(transactions) == 0 {
		return nil
	}

	// GORM不直接支持批量更新，这里使用事务逐个更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, transaction := range transactions {
			if err := tx.Save(transaction).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// 辅助方法

// applyFilters 应用查询过滤条件
func (r *gormRepository) applyFilters(query *gorm.DB, filter TransactionFilter) *gorm.DB {
	if filter.DeviceID != nil {
		query = query.Where("device_id = ?", *filter.DeviceID)
	}

	if filter.NozzleID != nil {
		query = query.Where("nozzle_id = ?", *filter.NozzleID)
	}

	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}

	if filter.Type != nil {
		query = query.Where("type = ?", *filter.Type)
	}

	if filter.OperatorID != nil {
		query = query.Where("operator_id = ?", *filter.OperatorID)
	}

	if filter.StationID != nil {
		query = query.Where("station_id = ?", *filter.StationID)
	}

	if filter.StartTime != nil {
		query = query.Where("created_at >= ?", *filter.StartTime)
	}

	if filter.EndTime != nil {
		query = query.Where("created_at <= ?", *filter.EndTime)
	}

	if filter.MinAmount != nil {
		query = query.Where("actual_amount >= ?", *filter.MinAmount)
	}

	if filter.MaxAmount != nil {
		query = query.Where("actual_amount <= ?", *filter.MaxAmount)
	}

	if filter.MinVolume != nil {
		query = query.Where("actual_volume >= ?", *filter.MinVolume)
	}

	if filter.MaxVolume != nil {
		query = query.Where("actual_volume <= ?", *filter.MaxVolume)
	}

	return query
}

// applyStatsFilters 应用统计过滤条件
func (r *gormRepository) applyStatsFilters(query *gorm.DB, filter TransactionStatsFilter) *gorm.DB {
	if filter.DeviceID != nil {
		query = query.Where("device_id = ?", *filter.DeviceID)
	}

	if filter.StationID != nil {
		query = query.Where("station_id = ?", *filter.StationID)
	}

	if filter.OperatorID != nil {
		query = query.Where("operator_id = ?", *filter.OperatorID)
	}

	if filter.StartTime != nil {
		query = query.Where("created_at >= ?", *filter.StartTime)
	}

	if filter.EndTime != nil {
		query = query.Where("created_at <= ?", *filter.EndTime)
	}

	return query
}

// applyPaginationAndSorting 应用分页和排序
func (r *gormRepository) applyPaginationAndSorting(query *gorm.DB, filter TransactionFilter) *gorm.DB {
	// 排序
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := strings.ToUpper(filter.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	page := filter.Page
	if page < 1 {
		page = 1
	}

	limit := filter.Limit
	if limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100 // 限制最大查询数量
	}

	offset := (page - 1) * limit
	query = query.Offset(offset).Limit(limit)

	return query
}

// getPeriodStats 获取时期统计
func (r *gormRepository) getPeriodStats(ctx context.Context, baseQuery *gorm.DB, filter TransactionStatsFilter) ([]*PeriodStat, error) {
	var dateFormat string
	var groupBy string

	switch filter.GroupBy {
	case "hour":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d %H:00')"
		groupBy = "hour_period"
	case "day":
		dateFormat = "DATE_FORMAT(created_at, '%Y-%m-%d')"
		groupBy = "day_period"
	default:
		return nil, fmt.Errorf("unsupported group by: %s", filter.GroupBy)
	}

	var results []struct {
		Period       string          `json:"period"`
		Count        int64           `json:"count"`
		Volume       decimal.Decimal `json:"volume"`
		Amount       decimal.Decimal `json:"amount"`
		AvgUnitPrice decimal.Decimal `json:"avg_unit_price"`
	}

	query := baseQuery.Select(fmt.Sprintf(`
		%s as period,
		COUNT(*) as count,
		COALESCE(SUM(actual_volume), 0) as volume,
		COALESCE(SUM(actual_amount), 0) as amount,
		CASE 
			WHEN SUM(actual_volume) > 0 THEN SUM(actual_amount) / SUM(actual_volume)
			ELSE 0 
		END as avg_unit_price
	`, dateFormat)).Group(groupBy).Order("period ASC")

	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get period stats: %w", err)
	}

	// 转换结果
	periodStats := make([]*PeriodStat, len(results))
	for i, result := range results {
		periodStats[i] = &PeriodStat{
			Period:       result.Period,
			Count:        result.Count,
			Volume:       result.Volume,
			Amount:       result.Amount,
			AvgUnitPrice: result.AvgUnitPrice,
		}
	}

	return periodStats, nil
}

// getNozzleSummaries 获取喷嘴统计
func (r *gormRepository) getNozzleSummaries(ctx context.Context, deviceID string, timeRange TimeRange) ([]*NozzleTransactionSummary, error) {
	var results []struct {
		NozzleID string          `json:"nozzle_id"`
		Count    int64           `json:"count"`
		Volume   decimal.Decimal `json:"volume"`
		Amount   decimal.Decimal `json:"amount"`
	}

	query := r.db.WithContext(ctx).Model(&models.Transaction{}).
		Select(`
			nozzle_id,
			COUNT(*) as count,
			COALESCE(SUM(actual_volume), 0) as volume,
			COALESCE(SUM(actual_amount), 0) as amount
		`).
		Where("device_id = ?", deviceID).
		Where("created_at BETWEEN ? AND ?", timeRange.Start, timeRange.End).
		Group("nozzle_id").
		Order("nozzle_id ASC")

	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get nozzle summaries: %w", err)
	}

	// 转换结果
	summaries := make([]*NozzleTransactionSummary, len(results))
	for i, result := range results {
		summaries[i] = &NozzleTransactionSummary{
			NozzleID: result.NozzleID,
			Count:    result.Count,
			Volume:   result.Volume,
			Amount:   result.Amount,
		}
	}

	return summaries, nil
}
