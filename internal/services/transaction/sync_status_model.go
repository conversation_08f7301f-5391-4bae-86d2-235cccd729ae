package transaction

import (
	"time"

	"gorm.io/gorm"
)

// SyncStatusModel 定义了同步状态的数据库结构
type SyncStatusModel struct {
	gorm.Model
	TransactionID string    `gorm:"size:255;not null;index:idx_tx_system,unique"`
	SystemID      string    `gorm:"size:100;not null;index:idx_tx_system,unique"`
	Status        string    `gorm:"size:50;not null;index"` // pending, success, failed, retrying
	AttemptCount  int       `gorm:"not null;default:0"`
	LastAttempt   time.Time `gorm:"not null"`
	LastError     string    `gorm:"type:text"`
	NextRetry     *time.Time
}

func (s SyncStatusModel) TableName() string {
	return "transaction_sync_statuses"
}
