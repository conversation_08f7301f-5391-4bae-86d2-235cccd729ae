package fuel_sync

import (
	"context"
	"testing"
	"time"

	"go.uber.org/zap/zaptest"
)

func TestNewSyncScheduler(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟的同步服务
	syncService := &FuelGradeSyncService{
		logger: logger,
	}

	// 测试默认配置
	scheduler := NewSyncScheduler(nil, syncService, logger)

	if scheduler == nil {
		t.Fatal("NewSyncScheduler returned nil")
	}

	if scheduler.config == nil {
		t.Fatal("config should not be nil")
	}

	// 验证默认配置
	if scheduler.config.SyncInterval != 1*time.Hour {
		t.Errorf("expected default sync interval to be 1 hour, got %v", scheduler.config.SyncInterval)
	}

	if !scheduler.config.EnableScheduled {
		t.Error("expected EnableScheduled to be true by default")
	}

	if !scheduler.config.EnableManual {
		t.Error("expected EnableManual to be true by default")
	}
}

func TestSyncScheduler_StartStop(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟的同步服务
	syncService := &FuelGradeSyncService{
		logger: logger,
	}

	config := &SyncSchedulerConfig{
		SyncInterval:    10 * time.Minute,
		StartDelay:      0,
		EnableScheduled: true,
		EnableManual:    true,
	}

	scheduler := NewSyncScheduler(config, syncService, logger)

	// 测试启动
	ctx := context.Background()
	err := scheduler.Start(ctx)
	if err != nil {
		t.Fatalf("failed to start scheduler: %v", err)
	}

	// 检查运行状态
	if !scheduler.IsRunning() {
		t.Error("scheduler should be running")
	}

	// 检查状态
	status := scheduler.GetStatus()
	if !status.IsRunning {
		t.Error("status should indicate running")
	}

	// 测试停止
	err = scheduler.Stop()
	if err != nil {
		t.Fatalf("failed to stop scheduler: %v", err)
	}

	// 检查停止状态
	if scheduler.IsRunning() {
		t.Error("scheduler should not be running after stop")
	}
}

func TestSyncScheduler_TriggerManualSync(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟的同步服务
	syncService := &FuelGradeSyncService{
		logger: logger,
	}

	config := &SyncSchedulerConfig{
		SyncInterval:    10 * time.Minute,
		StartDelay:      0,
		EnableScheduled: false, // 禁用定时同步
		EnableManual:    true,  // 启用手动同步
	}

	scheduler := NewSyncScheduler(config, syncService, logger)

	// 启动调度器
	ctx := context.Background()
	err := scheduler.Start(ctx)
	if err != nil {
		t.Fatalf("failed to start scheduler: %v", err)
	}
	defer scheduler.Stop()

	// 测试手动触发
	err = scheduler.TriggerManualSync("test_user", "test_request_123")
	if err != nil {
		t.Errorf("failed to trigger manual sync: %v", err)
	}

	// 等待一小段时间让触发器处理
	time.Sleep(100 * time.Millisecond)

	// 验证状态更新
	status := scheduler.GetStatus()
	if status.TotalSyncs == 0 {
		t.Log("Note: TotalSyncs is 0, which is expected since we're using a mock service")
	}
}

func TestSyncScheduler_UpdateConfig(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟的同步服务
	syncService := &FuelGradeSyncService{
		logger: logger,
	}

	scheduler := NewSyncScheduler(nil, syncService, logger)

	// 测试配置更新
	newConfig := &SyncSchedulerConfig{
		SyncInterval:    30 * time.Minute,
		StartDelay:      1 * time.Minute,
		EnableScheduled: false,
		EnableManual:    true,
	}

	err := scheduler.UpdateConfig(newConfig)
	if err != nil {
		t.Errorf("failed to update config: %v", err)
	}

	// 验证配置更新
	if scheduler.config.SyncInterval != 30*time.Minute {
		t.Errorf("expected sync interval to be 30 minutes, got %v", scheduler.config.SyncInterval)
	}

	if scheduler.config.EnableScheduled {
		t.Error("expected EnableScheduled to be false")
	}

	// 测试无效配置
	invalidConfig := &SyncSchedulerConfig{
		SyncInterval: 0, // 无效的间隔
	}

	err = scheduler.UpdateConfig(invalidConfig)
	if err == nil {
		t.Error("expected error for invalid config")
	}
}

func TestSyncScheduler_GetSyncHistory(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟的同步服务
	syncService := &FuelGradeSyncService{
		logger: logger,
	}

	scheduler := NewSyncScheduler(nil, syncService, logger)

	// 测试空历史
	history := scheduler.GetSyncHistory(10)
	if len(history) != 0 {
		t.Errorf("expected empty history, got %d items", len(history))
	}

	// 手动添加一些历史记录用于测试
	scheduler.syncHistoryMu.Lock()
	scheduler.syncHistory = append(scheduler.syncHistory,
		SyncResult{Total: 10, Added: 5, Updated: 3, Deleted: 2},
		SyncResult{Total: 15, Added: 8, Updated: 4, Deleted: 3},
	)
	scheduler.syncHistoryMu.Unlock()

	// 测试获取历史
	history = scheduler.GetSyncHistory(5)
	if len(history) != 2 {
		t.Errorf("expected 2 history items, got %d", len(history))
	}

	// 验证历史内容
	if history[0].Total != 10 {
		t.Errorf("expected first item total to be 10, got %d", history[0].Total)
	}
}

func TestSyncSchedulerConfig_Validation(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟的同步服务
	syncService := &FuelGradeSyncService{
		logger: logger,
	}

	// 测试nil配置
	scheduler := NewSyncScheduler(nil, syncService, logger)
	if scheduler.config == nil {
		t.Error("config should not be nil when passed nil")
	}

	// 测试自定义配置
	customConfig := &SyncSchedulerConfig{
		SyncInterval:       2 * time.Hour,
		StartDelay:         5 * time.Minute,
		EnableScheduled:    false,
		EnableManual:       true,
		ManualSyncTimeout:  10 * time.Minute,
		MaxRetries:         5,
		RetryDelay:         1 * time.Minute,
		RetryBackoff:       false,
		MaxRetryDelay:      10 * time.Minute,
		StopOnError:        true,
		MaxConsecutiveErrs: 5,
	}

	scheduler2 := NewSyncScheduler(customConfig, syncService, logger)

	if scheduler2.config.SyncInterval != 2*time.Hour {
		t.Errorf("expected sync interval to be 2 hours, got %v", scheduler2.config.SyncInterval)
	}

	if scheduler2.config.EnableScheduled {
		t.Error("expected EnableScheduled to be false")
	}

	if scheduler2.config.MaxRetries != 5 {
		t.Errorf("expected MaxRetries to be 5, got %d", scheduler2.config.MaxRetries)
	}
}
