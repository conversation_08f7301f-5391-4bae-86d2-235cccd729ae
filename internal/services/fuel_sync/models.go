package fuel_sync

import (
	"fmt"
	"time"
)

// OilProduct 外部API返回的油品数据结构
// 对应 http://*************:8080/api/v1/oil/products 接口返回格式
type OilProduct struct {
	ID             int64     `json:"id"`             // 外部系统的油品ID
	Code           string    `json:"code"`           // 油品代码，如 "BP-92"
	Name           string    `json:"name"`           // 油品名称，如 "BP 92"
	Category       string    `json:"category"`       // 油品类别，如 "Gasoline", "Diesel"
	Grade          string    `json:"grade"`          // 油品等级，如 "BP 92", "BP Ult"
	Description    string    `json:"description"`    // 描述信息
	Unit           string    `json:"unit"`           // 计量单位，通常为 "Litre"
	DefaultPrice   int64     `json:"default_price"`  // 默认价格（印尼盾，无小数点）
	CurrentPrice   int64     `json:"current_price"`  // 当前价格（印尼盾，无小数点）
	IsActive       bool      `json:"is_active"`      // 是否活跃状态
	Specifications string    `json:"specifications"` // 规格说明
	ImageURL       string    `json:"image_url"`      // 图片URL
	CreatedBy      string    `json:"created_by"`     // 创建者
	CreatedAt      time.Time `json:"created_at"`     // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`     // 更新时间
}

// OilProductsResponse 外部API响应结构
type OilProductsResponse struct {
	Products []OilProduct `json:"products,omitempty"` // 当API返回包装格式时使用
}

// SyncResult 同步结果统计
type SyncResult struct {
	Total   int `json:"total"`   // 外部API返回的总记录数
	Added   int `json:"added"`   // 新增记录数
	Updated int `json:"updated"` // 更新记录数
	Deleted int `json:"deleted"` // 软删除记录数
	Skipped int `json:"skipped"` // 跳过记录数（数据异常等）
	Errors  int `json:"errors"`  // 错误记录数
}

// SyncStatus 同步状态
type SyncStatus struct {
	IsRunning    bool        `json:"is_running"`     // 是否正在同步
	LastSyncTime *time.Time  `json:"last_sync_time"` // 上次同步时间
	LastResult   *SyncResult `json:"last_result"`    // 上次同步结果
	LastError    string      `json:"last_error"`     // 上次错误信息
	NextSyncTime *time.Time  `json:"next_sync_time"` // 下次同步时间
}

// ConversionError 数据转换错误
type ConversionError struct {
	Field        string      `json:"field"`          // 出错字段
	Value        interface{} `json:"value"`          // 原始值
	ExpectedType string      `json:"expected_type"`  // 期望类型
	Reason       string      `json:"reason"`         // 错误原因
	OilProductID int64       `json:"oil_product_id"` // 关联的油品ID
}

func (e *ConversionError) Error() string {
	return fmt.Sprintf("conversion error for oil product %d, field '%s': %s (value: %v, expected: %s)",
		e.OilProductID, e.Field, e.Reason, e.Value, e.ExpectedType)
}

// SyncConfig 同步配置
type SyncConfig struct {
	Enabled            bool          `yaml:"enabled" json:"enabled"`                         // 是否启用同步
	Endpoint           string        `yaml:"endpoint" json:"endpoint"`                       // 外部API端点
	SyncInterval       time.Duration `yaml:"sync_interval" json:"sync_interval"`             // 同步间隔
	Timeout            time.Duration `yaml:"timeout" json:"timeout"`                         // HTTP请求超时
	RetryCount         int           `yaml:"retry_count" json:"retry_count"`                 // 重试次数
	FullSync           bool          `yaml:"full_sync" json:"full_sync"`                     // 是否启用全量同步
	IncrementalSync    bool          `yaml:"incremental_sync" json:"incremental_sync"`       // 是否启用增量同步
	SoftDelete         bool          `yaml:"soft_delete" json:"soft_delete"`                 // 是否启用软删除
	ConflictResolution string        `yaml:"conflict_resolution" json:"conflict_resolution"` // 冲突解决策略: "external_wins", "local_wins", "manual"
}

// DefaultSyncConfig 返回默认同步配置
func DefaultSyncConfig() *SyncConfig {
	return &SyncConfig{
		Enabled:            true,
		Endpoint:           "http://*************:8080/api/v1/oil/products",
		SyncInterval:       time.Hour,        // 默认每小时同步一次
		Timeout:            30 * time.Second, // 30秒超时
		RetryCount:         3,                // 重试3次
		FullSync:           true,             // 启用全量同步
		IncrementalSync:    true,             // 启用增量同步
		SoftDelete:         true,             // 启用软删除
		ConflictResolution: "external_wins",  // 外部数据优先
	}
}
