package fuel_sync

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// SyncSchedulerConfig 同步调度器配置
type SyncSchedulerConfig struct {
	// 定时同步配置
	SyncInterval    time.Duration `json:"sync_interval"`    // 同步间隔
	StartDelay      time.Duration `json:"start_delay"`      // 启动延迟
	EnableScheduled bool          `json:"enable_scheduled"` // 是否启用定时同步

	// 手动触发配置
	EnableManual      bool          `json:"enable_manual"`       // 是否启用手动触发
	ManualSyncTimeout time.Duration `json:"manual_sync_timeout"` // 手动同步超时

	// 重试配置
	MaxRetries    int           `json:"max_retries"`     // 最大重试次数
	RetryDelay    time.Duration `json:"retry_delay"`     // 重试延迟
	RetryBackoff  bool          `json:"retry_backoff"`   // 是否使用指数退避
	MaxRetryDelay time.Duration `json:"max_retry_delay"` // 最大重试延迟

	// 错误处理
	StopOnError        bool `json:"stop_on_error"`          // 遇到错误是否停止
	MaxConsecutiveErrs int  `json:"max_consecutive_errors"` // 最大连续错误次数
}

// SyncSchedulerStatus 调度器状态
type SyncSchedulerStatus struct {
	IsRunning           bool      `json:"is_running"`            // 是否运行中
	LastSyncTime        time.Time `json:"last_sync_time"`        // 上次同步时间
	NextSyncTime        time.Time `json:"next_sync_time"`        // 下次同步时间
	TotalSyncs          int64     `json:"total_syncs"`           // 总同步次数
	SuccessfulSyncs     int64     `json:"successful_syncs"`      // 成功同步次数
	FailedSyncs         int64     `json:"failed_syncs"`          // 失败同步次数
	ConsecutiveErrors   int       `json:"consecutive_errors"`    // 连续错误次数
	LastError           string    `json:"last_error"`            // 最后错误信息
	StartTime           time.Time `json:"start_time"`            // 启动时间
	Uptime              string    `json:"uptime"`                // 运行时间
	CurrentSyncStatus   string    `json:"current_sync_status"`   // 当前同步状态
	LastSyncDuration    string    `json:"last_sync_duration"`    // 上次同步耗时
	AverageSyncDuration string    `json:"average_sync_duration"` // 平均同步耗时
}

// SyncTrigger 同步触发器
type SyncTrigger struct {
	Type      string    `json:"type"`       // 触发类型：scheduled, manual
	Timestamp time.Time `json:"timestamp"`  // 触发时间
	UserID    string    `json:"user_id"`    // 触发用户（手动触发时）
	RequestID string    `json:"request_id"` // 请求ID（手动触发时）
}

// SyncScheduler 油品同步调度器
type SyncScheduler struct {
	// 配置
	config *SyncSchedulerConfig
	logger *zap.Logger

	// 同步服务
	syncService *FuelGradeSyncService

	// 运行状态
	running   bool
	runningMu sync.RWMutex
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup

	// 定时器
	ticker *time.Ticker

	// 状态统计
	status   *SyncSchedulerStatus
	statusMu sync.RWMutex

	// 手动触发通道
	manualTriggerChan chan *SyncTrigger

	// 同步历史记录
	syncHistory    []SyncResult
	syncHistoryMu  sync.RWMutex
	maxHistorySize int

	// 同步耗时统计
	syncDurations    []time.Duration
	syncDurationsMu  sync.RWMutex
	maxDurationCount int
}

// NewSyncScheduler 创建同步调度器
func NewSyncScheduler(config *SyncSchedulerConfig, syncService *FuelGradeSyncService, logger *zap.Logger) *SyncScheduler {
	if config == nil {
		config = &SyncSchedulerConfig{
			SyncInterval:       1 * time.Hour,
			StartDelay:         30 * time.Second,
			EnableScheduled:    true,
			EnableManual:       true,
			ManualSyncTimeout:  5 * time.Minute,
			MaxRetries:         3,
			RetryDelay:         30 * time.Second,
			RetryBackoff:       true,
			MaxRetryDelay:      5 * time.Minute,
			StopOnError:        false,
			MaxConsecutiveErrs: 10,
		}
	}

	return &SyncScheduler{
		config:            config,
		logger:            logger,
		syncService:       syncService,
		manualTriggerChan: make(chan *SyncTrigger, 10),
		status: &SyncSchedulerStatus{
			IsRunning:         false,
			CurrentSyncStatus: "idle",
		},
		syncHistory:      make([]SyncResult, 0),
		maxHistorySize:   100,
		syncDurations:    make([]time.Duration, 0),
		maxDurationCount: 50,
	}
}

// Start 启动调度器
func (s *SyncScheduler) Start(ctx context.Context) error {
	s.runningMu.Lock()
	defer s.runningMu.Unlock()

	if s.running {
		return fmt.Errorf("sync scheduler already running")
	}

	s.ctx, s.cancel = context.WithCancel(ctx)
	s.running = true

	// 更新状态
	s.statusMu.Lock()
	s.status.IsRunning = true
	s.status.StartTime = time.Now()
	s.status.CurrentSyncStatus = "starting"
	s.statusMu.Unlock()

	// 启动定时同步循环
	if s.config.EnableScheduled {
		s.wg.Add(1)
		go s.scheduledSyncLoop()
	}

	// 启动手动触发处理循环
	if s.config.EnableManual {
		s.wg.Add(1)
		go s.manualTriggerLoop()
	}

	s.logger.Info("油品同步调度器已启动",
		zap.Duration("sync_interval", s.config.SyncInterval),
		zap.Duration("start_delay", s.config.StartDelay),
		zap.Bool("scheduled_enabled", s.config.EnableScheduled),
		zap.Bool("manual_enabled", s.config.EnableManual))

	return nil
}

// Stop 停止调度器
func (s *SyncScheduler) Stop() error {
	s.runningMu.Lock()
	defer s.runningMu.Unlock()

	if !s.running {
		return nil
	}

	s.logger.Info("正在停止油品同步调度器...")

	// 取消上下文
	if s.cancel != nil {
		s.cancel()
	}

	// 停止定时器
	if s.ticker != nil {
		s.ticker.Stop()
	}

	// 等待所有goroutine结束
	s.wg.Wait()

	s.running = false

	// 更新状态
	s.statusMu.Lock()
	s.status.IsRunning = false
	s.status.CurrentSyncStatus = "stopped"
	if !s.status.StartTime.IsZero() {
		s.status.Uptime = time.Since(s.status.StartTime).String()
	}
	s.statusMu.Unlock()

	s.logger.Info("油品同步调度器已停止")
	return nil
}

// IsRunning 检查是否运行中
func (s *SyncScheduler) IsRunning() bool {
	s.runningMu.RLock()
	defer s.runningMu.RUnlock()
	return s.running
}

// TriggerManualSync 触发手动同步
func (s *SyncScheduler) TriggerManualSync(userID, requestID string) error {
	if !s.config.EnableManual {
		return fmt.Errorf("manual sync is disabled")
	}

	if !s.IsRunning() {
		return fmt.Errorf("sync scheduler is not running")
	}

	trigger := &SyncTrigger{
		Type:      "manual",
		Timestamp: time.Now(),
		UserID:    userID,
		RequestID: requestID,
	}

	select {
	case s.manualTriggerChan <- trigger:
		s.logger.Info("手动同步已触发",
			zap.String("user_id", userID),
			zap.String("request_id", requestID))
		return nil
	default:
		return fmt.Errorf("manual sync trigger queue is full")
	}
}

// GetStatus 获取调度器状态
func (s *SyncScheduler) GetStatus() *SyncSchedulerStatus {
	s.statusMu.RLock()
	defer s.statusMu.RUnlock()

	// 创建状态副本
	status := *s.status

	// 计算运行时间
	if status.IsRunning && !status.StartTime.IsZero() {
		status.Uptime = time.Since(status.StartTime).String()
	}

	// 计算平均同步耗时
	s.syncDurationsMu.RLock()
	if len(s.syncDurations) > 0 {
		var total time.Duration
		for _, d := range s.syncDurations {
			total += d
		}
		avgDuration := total / time.Duration(len(s.syncDurations))
		status.AverageSyncDuration = avgDuration.String()
	}
	s.syncDurationsMu.RUnlock()

	return &status
}

// GetSyncHistory 获取同步历史
func (s *SyncScheduler) GetSyncHistory(limit int) []SyncResult {
	s.syncHistoryMu.RLock()
	defer s.syncHistoryMu.RUnlock()

	if limit <= 0 || limit > len(s.syncHistory) {
		limit = len(s.syncHistory)
	}

	// 返回最近的记录
	start := len(s.syncHistory) - limit
	if start < 0 {
		start = 0
	}

	history := make([]SyncResult, limit)
	copy(history, s.syncHistory[start:])
	return history
}

// UpdateConfig 更新配置
func (s *SyncScheduler) UpdateConfig(config *SyncSchedulerConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 验证配置
	if config.SyncInterval <= 0 {
		return fmt.Errorf("sync interval must be positive")
	}

	s.config = config
	s.logger.Info("同步调度器配置已更新",
		zap.Duration("sync_interval", config.SyncInterval),
		zap.Bool("scheduled_enabled", config.EnableScheduled),
		zap.Bool("manual_enabled", config.EnableManual))

	return nil
}

// scheduledSyncLoop 定时同步循环
func (s *SyncScheduler) scheduledSyncLoop() {
	defer s.wg.Done()

	// 启动延迟
	if s.config.StartDelay > 0 {
		s.logger.Info("等待启动延迟",
			zap.Duration("delay", s.config.StartDelay))

		select {
		case <-s.ctx.Done():
			return
		case <-time.After(s.config.StartDelay):
		}
	}

	// 创建定时器
	s.ticker = time.NewTicker(s.config.SyncInterval)
	defer s.ticker.Stop()

	// 更新下次同步时间
	s.statusMu.Lock()
	s.status.NextSyncTime = time.Now().Add(s.config.SyncInterval)
	s.status.CurrentSyncStatus = "waiting"
	s.statusMu.Unlock()

	s.logger.Info("定时同步循环已启动",
		zap.Duration("interval", s.config.SyncInterval))

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("定时同步循环已停止")
			return
		case <-s.ticker.C:
			trigger := &SyncTrigger{
				Type:      "scheduled",
				Timestamp: time.Now(),
			}
			s.performSync(trigger)
		}
	}
}

// manualTriggerLoop 手动触发处理循环
func (s *SyncScheduler) manualTriggerLoop() {
	defer s.wg.Done()

	s.logger.Info("手动触发处理循环已启动")

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("手动触发处理循环已停止")
			return
		case trigger := <-s.manualTriggerChan:
			s.performSync(trigger)
		}
	}
}

// performSync 执行同步
func (s *SyncScheduler) performSync(trigger *SyncTrigger) {
	startTime := time.Now()

	// 更新状态
	s.statusMu.Lock()
	s.status.CurrentSyncStatus = "syncing"
	s.statusMu.Unlock()

	s.logger.Info("开始执行同步",
		zap.String("trigger_type", trigger.Type),
		zap.String("user_id", trigger.UserID),
		zap.String("request_id", trigger.RequestID))

	// 执行同步（带重试）
	var result SyncResult
	var err error

	for attempt := 0; attempt <= s.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 计算重试延迟
			retryDelay := s.config.RetryDelay
			if s.config.RetryBackoff {
				// 指数退避
				retryDelay = time.Duration(attempt) * s.config.RetryDelay
				if retryDelay > s.config.MaxRetryDelay {
					retryDelay = s.config.MaxRetryDelay
				}
			}

			s.logger.Warn("同步失败，准备重试",
				zap.Int("attempt", attempt),
				zap.Duration("retry_delay", retryDelay),
				zap.Error(err))

			select {
			case <-s.ctx.Done():
				return
			case <-time.After(retryDelay):
			}
		}

		// 创建超时上下文
		syncCtx, cancel := context.WithTimeout(s.ctx, s.config.ManualSyncTimeout)

		// 执行同步
		stats, err := s.syncService.SyncAll(syncCtx)
		if err == nil && stats != nil {
			// 将SyncStatistics转换为SyncResult
			result = SyncResult{
				Total:   stats.TotalFetched,
				Added:   stats.NewCreated,
				Updated: stats.Updated,
				Deleted: stats.SoftDeleted,
				Skipped: stats.Skipped,
				Errors:  stats.Errors,
			}
		}

		cancel()

		if err == nil {
			// 同步成功
			break
		}

		// 检查是否应该停止重试
		if s.config.StopOnError && attempt == 0 {
			break
		}
	}

	duration := time.Since(startTime)

	// 更新统计信息
	s.statusMu.Lock()
	s.status.TotalSyncs++
	s.status.LastSyncTime = startTime
	s.status.LastSyncDuration = duration.String()

	if err == nil {
		s.status.SuccessfulSyncs++
		s.status.ConsecutiveErrors = 0
		s.status.CurrentSyncStatus = "success"
		s.status.LastError = ""
	} else {
		s.status.FailedSyncs++
		s.status.ConsecutiveErrors++
		s.status.CurrentSyncStatus = "failed"
		s.status.LastError = err.Error()
	}

	// 更新下次同步时间（仅定时同步）
	if trigger.Type == "scheduled" {
		s.status.NextSyncTime = time.Now().Add(s.config.SyncInterval)
	}
	s.statusMu.Unlock()

	// 记录同步耗时
	s.syncDurationsMu.Lock()
	s.syncDurations = append(s.syncDurations, duration)
	if len(s.syncDurations) > s.maxDurationCount {
		s.syncDurations = s.syncDurations[1:]
	}
	s.syncDurationsMu.Unlock()

	// 记录同步历史
	s.syncHistoryMu.Lock()
	s.syncHistory = append(s.syncHistory, result)
	if len(s.syncHistory) > s.maxHistorySize {
		s.syncHistory = s.syncHistory[1:]
	}
	s.syncHistoryMu.Unlock()

	// 记录日志
	if err == nil {
		s.logger.Info("同步执行成功",
			zap.String("trigger_type", trigger.Type),
			zap.Duration("duration", duration),
			zap.Int("added", result.Added),
			zap.Int("updated", result.Updated),
			zap.Int("deleted", result.Deleted),
			zap.Int("errors", result.Errors))
	} else {
		s.logger.Error("同步执行失败",
			zap.String("trigger_type", trigger.Type),
			zap.Duration("duration", duration),
			zap.Error(err))
	}

	// 检查是否达到最大连续错误次数
	if s.config.MaxConsecutiveErrs > 0 && s.status.ConsecutiveErrors >= s.config.MaxConsecutiveErrs {
		s.logger.Error("达到最大连续错误次数，停止调度器",
			zap.Int("consecutive_errors", s.status.ConsecutiveErrors),
			zap.Int("max_consecutive_errors", s.config.MaxConsecutiveErrs))

		// 停止调度器
		go func() {
			s.Stop()
		}()
	}
}
