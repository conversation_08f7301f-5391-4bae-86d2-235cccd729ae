package fuel_sync

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestHTTPFuelSyncStrategy_FetchProducts_Success(t *testing.T) {
	// 创建测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "GET", r.Method)
		assert.Equal(t, "application/json", r.Header.Get("Accept"))
		assert.Equal(t, "FCC-Service-Fuel-Sync/1.0", r.Header.Get("User-Agent"))

		// 返回测试数据
		response := `[
			{
				"id": 101,
				"code": "BP-92",
				"name": "BP 92",
				"category": "Gasoline",
				"grade": "BP 92",
				"description": "Standard 92 Octane Gasoline",
				"unit": "Litre",
				"default_price": 12360,
				"current_price": 12360,
				"is_active": true,
				"specifications": "",
				"image_url": "",
				"created_by": "system",
				"created_at": "2025-06-23T23:15:45.72494-04:00",
				"updated_at": "2025-06-23T23:15:45.72494-04:00"
			}
		]`
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(response))
	}))
	defer server.Close()

	// 创建策略
	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 30*time.Second, "", logger)

	// 测试获取数据
	ctx := context.Background()
	products, err := strategy.FetchProducts(ctx)

	require.NoError(t, err)
	assert.Len(t, products, 1)
	assert.Equal(t, int64(101), products[0].ID)
	assert.Equal(t, "BP-92", products[0].Code)
	assert.Equal(t, int64(12360), products[0].CurrentPrice)
}

func TestHTTPFuelSyncStrategy_FetchProducts_WithAPIKey(t *testing.T) {
	// 创建测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证API密钥
		assert.Equal(t, "test-api-key", r.Header.Get("X-API-Key"))

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`[]`)) // 返回空数组
	}))
	defer server.Close()

	// 创建带API密钥的策略
	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 30*time.Second, "test-api-key", logger)

	// 测试获取数据
	ctx := context.Background()
	products, err := strategy.FetchProducts(ctx)

	require.NoError(t, err)
	assert.Len(t, products, 0)
}

func TestHTTPFuelSyncStrategy_FetchProducts_WrappedResponse(t *testing.T) {
	// 创建测试HTTP服务器返回包装格式
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := `{
			"products": [
				{
					"id": 102,
					"code": "BP-95",
					"name": "BP Ultimate",
					"category": "Gasoline",
					"grade": "BP Ult",
					"description": "Premium 95 Octane Gasoline",
					"unit": "Litre",
					"default_price": 12780,
					"current_price": 12780,
					"is_active": true,
					"specifications": "",
					"image_url": "",
					"created_by": "system",
					"created_at": "2025-06-23T23:15:45.72494-04:00",
					"updated_at": "2025-06-23T23:15:45.72494-04:00"
				}
			]
		}`
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(response))
	}))
	defer server.Close()

	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 30*time.Second, "", logger)

	ctx := context.Background()
	products, err := strategy.FetchProducts(ctx)

	require.NoError(t, err)
	assert.Len(t, products, 1)
	assert.Equal(t, int64(102), products[0].ID)
	assert.Equal(t, "BP-95", products[0].Code)
}

func TestHTTPFuelSyncStrategy_FetchProducts_HTTPError(t *testing.T) {
	// 创建返回错误的测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal Server Error"))
	}))
	defer server.Close()

	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 30*time.Second, "", logger)

	ctx := context.Background()
	products, err := strategy.FetchProducts(ctx)

	assert.Error(t, err)
	assert.Nil(t, products)
	assert.Contains(t, err.Error(), "HTTP request failed with status 500")
}

func TestHTTPFuelSyncStrategy_FetchProducts_InvalidJSON(t *testing.T) {
	// 创建返回无效JSON的测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`invalid json`))
	}))
	defer server.Close()

	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 30*time.Second, "", logger)

	ctx := context.Background()
	products, err := strategy.FetchProducts(ctx)

	assert.Error(t, err)
	assert.Nil(t, products)
	assert.Contains(t, err.Error(), "failed to parse response")
}

func TestHTTPFuelSyncStrategy_Timeout(t *testing.T) {
	// 创建延迟响应的测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`[]`))
	}))
	defer server.Close()

	// 创建短超时的策略
	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 50*time.Millisecond, "", logger)

	ctx := context.Background()
	products, err := strategy.FetchProducts(ctx)

	assert.Error(t, err)
	assert.Nil(t, products)
	assert.Contains(t, err.Error(), "HTTP request failed")
}

func TestHTTPFuelSyncStrategy_ShouldRetry(t *testing.T) {
	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", "http://example.com", 30*time.Second, "", logger).(*HTTPFuelSyncStrategy)

	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "HTTP 4xx error - should not retry",
			err:      &HTTPError{StatusCode: 404, Message: "Not Found"},
			expected: false,
		},
		{
			name:     "HTTP 5xx error - should retry",
			err:      &HTTPError{StatusCode: 500, Message: "Internal Server Error"},
			expected: true,
		},
		{
			name:     "network error - should retry",
			err:      fmt.Errorf("network unreachable"),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := strategy.ShouldRetry(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHTTPFuelSyncStrategy_Properties(t *testing.T) {
	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", "http://example.com", 30*time.Second, "", logger)

	assert.Equal(t, "test_system", strategy.SystemID())
	assert.Equal(t, 3, strategy.MaxRetries())
	assert.Equal(t, 5*time.Second, strategy.RetryDelay())
}

func TestFuelSyncClient_FetchProductsWithRetry_Success(t *testing.T) {
	callCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		callCount++
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`[{"id": 101, "code": "BP-92", "name": "BP 92", "category": "Gasoline", "grade": "BP 92", "description": "Standard 92 Octane Gasoline", "unit": "Litre", "default_price": 12360, "current_price": 12360, "is_active": true, "specifications": "", "image_url": "", "created_by": "system", "created_at": "2025-06-23T23:15:45.72494-04:00", "updated_at": "2025-06-23T23:15:45.72494-04:00"}]`))
	}))
	defer server.Close()

	logger := zap.NewNop()
	strategy := NewHTTPFuelSyncStrategy("test_system", server.URL, 30*time.Second, "", logger)
	client := NewFuelSyncClient(strategy, logger)

	ctx := context.Background()
	products, err := client.FetchProductsWithRetry(ctx)

	require.NoError(t, err)
	assert.Len(t, products, 1)
	assert.Equal(t, 1, callCount, "应该只调用一次API")
}

func TestFuelSyncClient_FetchProductsWithRetry_WithRetries(t *testing.T) {
	callCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		callCount++
		if callCount < 3 {
			// 前两次失败
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Server Error"))
			return
		}
		// 第三次成功
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`[{"id": 101, "code": "BP-92", "name": "BP 92", "category": "Gasoline", "grade": "BP 92", "description": "Standard 92 Octane Gasoline", "unit": "Litre", "default_price": 12360, "current_price": 12360, "is_active": true, "specifications": "", "image_url": "", "created_by": "system", "created_at": "2025-06-23T23:15:45.72494-04:00", "updated_at": "2025-06-23T23:15:45.72494-04:00"}]`))
	}))
	defer server.Close()

	logger := zap.NewNop()
	// 创建快速重试的策略以加速测试
	strategy := &HTTPFuelSyncStrategy{
		systemID:   "test_system",
		endpoint:   server.URL,
		timeout:    30 * time.Second,
		maxRetries: 3,
		retryDelay: 10 * time.Millisecond, // 快速重试
		headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
			"User-Agent":   "FCC-Service-Fuel-Sync/1.0",
		},
		logger: logger,
	}
	client := NewFuelSyncClient(strategy, logger)

	ctx := context.Background()
	products, err := client.FetchProductsWithRetry(ctx)

	require.NoError(t, err)
	assert.Len(t, products, 1)
	assert.Equal(t, 3, callCount, "应该重试3次")
}

func TestFuelSyncClient_FetchProductsWithRetry_MaxRetriesExceeded(t *testing.T) {
	callCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		callCount++
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Server Error"))
	}))
	defer server.Close()

	logger := zap.NewNop()
	// 创建快速重试的策略
	strategy := &HTTPFuelSyncStrategy{
		systemID:   "test_system",
		endpoint:   server.URL,
		timeout:    30 * time.Second,
		maxRetries: 2, // 只重试2次
		retryDelay: 10 * time.Millisecond,
		headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
			"User-Agent":   "FCC-Service-Fuel-Sync/1.0",
		},
		logger: logger,
	}
	client := NewFuelSyncClient(strategy, logger)

	ctx := context.Background()
	products, err := client.FetchProductsWithRetry(ctx)

	assert.Error(t, err)
	assert.Nil(t, products)
	assert.Contains(t, err.Error(), "failed to fetch products after 3 attempts")
	assert.Equal(t, 3, callCount, "应该总共调用3次（1次初始 + 2次重试）")
}

func TestFuelSyncClient_FetchProductsWithRetry_ContextCancellation(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Server Error"))
	}))
	defer server.Close()

	logger := zap.NewNop()
	strategy := &HTTPFuelSyncStrategy{
		systemID:   "test_system",
		endpoint:   server.URL,
		timeout:    30 * time.Second,
		maxRetries: 5,
		retryDelay: 100 * time.Millisecond,
		headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
			"User-Agent":   "FCC-Service-Fuel-Sync/1.0",
		},
		logger: logger,
	}
	client := NewFuelSyncClient(strategy, logger)

	// 创建会被取消的上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 在短时间后取消上下文
	go func() {
		time.Sleep(50 * time.Millisecond)
		cancel()
	}()

	products, err := client.FetchProductsWithRetry(ctx)

	assert.Error(t, err)
	assert.Nil(t, products)
	assert.Equal(t, context.Canceled, err)
}

func TestHTTPError_Error(t *testing.T) {
	err := &HTTPError{
		StatusCode: 404,
		Message:    "Not Found",
	}

	expected := "HTTP 404: Not Found"
	assert.Equal(t, expected, err.Error())
}
