package fuel_sync

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)

// FuelGradeConverter 油品数据转换器
type FuelGradeConverter struct {
	logger *zap.Logger
}

// NewFuelGradeConverter 创建油品数据转换器
func NewFuelGradeConverter(logger *zap.Logger) *FuelGradeConverter {
	return &FuelGradeConverter{
		logger: logger,
	}
}

// ConvertOilProductToFuelGrade 将外部OilProduct转换为FuelGrade
func (c *FuelGradeConverter) ConvertOilProductToFuelGrade(oilProduct *OilProduct) (*models.FuelGrade, error) {
	if oilProduct == nil {
		return nil, fmt.Errorf("oil product cannot be nil")
	}

	// 生成FuelGrade ID：外部ID
	fuelGradeID := fmt.Sprintf("%d", oilProduct.ID)

	// 转换价格：印尼盾直接转换为decimal（无小数点）
	price := decimal.NewFromInt(oilProduct.CurrentPrice)

	// 转换油品类型
	fuelType := oilProduct.Category

	// 提取辛烷值（如果是汽油）
	octane := c.extractOctane(oilProduct.Grade, oilProduct.Description)

	// 创建元数据
	metadata, err := c.createMetadata(oilProduct)
	if err != nil {
		return nil, &ConversionError{
			Field:        "metadata",
			Value:        oilProduct,
			ExpectedType: "JSON",
			Reason:       err.Error(),
			OilProductID: oilProduct.ID,
		}
	}

	// 获取显示颜色（基于油品类型）
	color := c.getDisplayColor(fuelType)

	// 创建FuelGrade实例
	fuelGrade := &models.FuelGrade{
		ID:          fuelGradeID,
		Name:        oilProduct.Name,
		Type:        fuelType,
		Octane:      octane,
		Description: oilProduct.Description,
		Color:       color,
		Price:       price,
		Currency:    "IDR", // 印尼盾
		Metadata:    metadata,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     1,
	}

	c.logger.Debug("[FuelSync] Converted oil product to fuel grade",
		zap.Int64("oil_product_id", oilProduct.ID),
		zap.String("fuel_grade_id", fuelGradeID),
		zap.String("name", fuelGrade.Name),
		zap.String("type", fuelGrade.Type),
		zap.String("price", price.String()),
		zap.Int("octane", octane))

	return fuelGrade, nil
}

// ConvertBatchOilProductsToFuelGrades 批量转换油品数据
func (c *FuelGradeConverter) ConvertBatchOilProductsToFuelGrades(oilProducts []OilProduct) ([]*models.FuelGrade, []error) {
	var fuelGrades []*models.FuelGrade
	var errors []error

	for i, oilProduct := range oilProducts {
		fuelGrade, err := c.ConvertOilProductToFuelGrade(&oilProduct)
		if err != nil {
			c.logger.Warn("[FuelSync] Failed to convert oil product",
				zap.Int("index", i),
				zap.Int64("oil_product_id", oilProduct.ID),
				zap.Error(err))
			errors = append(errors, err)
			continue
		}

		fuelGrades = append(fuelGrades, fuelGrade)
	}

	c.logger.Info("[FuelSync] Batch conversion completed",
		zap.Int("total_input", len(oilProducts)),
		zap.Int("successful_conversions", len(fuelGrades)),
		zap.Int("conversion_errors", len(errors)))

	return fuelGrades, errors
}

// UpdateExistingFuelGrade 更新现有FuelGrade（用于增量更新）
func (c *FuelGradeConverter) UpdateExistingFuelGrade(existing *models.FuelGrade, oilProduct *OilProduct) (*models.FuelGrade, error) {
	if existing == nil {
		return c.ConvertOilProductToFuelGrade(oilProduct)
	}

	if oilProduct == nil {
		return nil, fmt.Errorf("oil product cannot be nil")
	}

	// 更新基本字段
	existing.Name = oilProduct.Name
	existing.Description = oilProduct.Description
	existing.UpdatedAt = time.Now()
	existing.Version += 1

	// 更新价格（重要：处理印尼盾格式）
	newPrice := decimal.NewFromInt(oilProduct.CurrentPrice)
	if !existing.Price.Equal(newPrice) {
		c.logger.Info("[FuelSync] Price updated",
			zap.String("fuel_grade_id", existing.ID),
			zap.String("old_price", existing.Price.String()),
			zap.String("new_price", newPrice.String()))
		existing.Price = newPrice
	}

	// 更新油品类型（如果改变）
	newType := oilProduct.Category
	if existing.Type != newType {
		c.logger.Info("[FuelSync] Fuel type updated",
			zap.String("fuel_grade_id", existing.ID),
			zap.String("old_type", existing.Type),
			zap.String("new_type", newType))
		existing.Type = newType
		existing.Color = c.getDisplayColor(newType) // 更新颜色
	}

	// 更新辛烷值
	newOctane := c.extractOctane(oilProduct.Grade, oilProduct.Description)
	if existing.Octane != newOctane {
		existing.Octane = newOctane
	}

	// 更新元数据
	metadata, err := c.createMetadata(oilProduct)
	if err == nil {
		existing.Metadata = metadata
	}

	// 处理软删除/恢复
	c.handleSoftDelete(oilProduct, existing)

	c.logger.Debug("[FuelSync] Updated existing fuel grade",
		zap.String("fuel_grade_id", existing.ID),
		zap.Int("version", existing.Version))

	return existing, nil
}

// handleSoftDelete 处理软删除逻辑
func (c *FuelGradeConverter) handleSoftDelete(oilProduct *OilProduct, fuelGrade *models.FuelGrade) {
	// 注意：这里需要检查FuelGrade模型是否有DeletedAt字段
	// 根据当前模型定义，FuelGrade还没有DeletedAt字段
	// 这将在任务4中添加，现在只记录日志

	if !oilProduct.IsActive {
		c.logger.Info("[FuelSync] Oil product is inactive, should be soft deleted",
			zap.String("fuel_grade_id", fuelGrade.ID),
			zap.Int64("oil_product_id", oilProduct.ID))
		// TODO: 在添加DeletedAt字段后实现软删除
		// now := time.Now()
		// fuelGrade.DeletedAt = &now
	} else {
		c.logger.Debug("[FuelSync] Oil product is active",
			zap.String("fuel_grade_id", fuelGrade.ID),
			zap.Int64("oil_product_id", oilProduct.ID))
		// TODO: 在添加DeletedAt字段后实现恢复
		// fuelGrade.DeletedAt = nil
	}
}

// extractOctane 提取辛烷值
func (c *FuelGradeConverter) extractOctane(grade, description string) int {
	// 从grade或description中提取数字（辛烷值）
	text := grade + " " + description

	// 常见辛烷值模式
	octanePatterns := map[string]int{
		"88":  88,
		"90":  90,
		"92":  92,
		"95":  95,
		"98":  98,
		"100": 100,
	}

	for pattern, octane := range octanePatterns {
		if strings.Contains(text, pattern) {
			return octane
		}
	}

	// 默认返回0（表示未知或不适用）
	return 0
}

// getDisplayColor 获取显示颜色
func (c *FuelGradeConverter) getDisplayColor(fuelType string) string {
	switch fuelType {
	case "gasoline":
		return "#FF6B6B" // 红色系（汽油）
	case "diesel":
		return "#4ECDC4" // 蓝绿色（柴油）
	case "gas":
		return "#45B7D1" // 蓝色（气体燃料）
	default:
		return "#95A5A6" // 灰色（默认）
	}
}

// createMetadata 创建元数据
func (c *FuelGradeConverter) createMetadata(oilProduct *OilProduct) (datatypes.JSON, error) {
	metadata := map[string]interface{}{
		"external_id":    oilProduct.ID,
		"external_code":  oilProduct.Code,
		"grade":          oilProduct.Grade,
		"unit":           oilProduct.Unit,
		"default_price":  oilProduct.DefaultPrice,
		"specifications": oilProduct.Specifications,
		"image_url":      oilProduct.ImageURL,
		"created_by":     oilProduct.CreatedBy,
		"sync_info": map[string]interface{}{
			"last_sync_at": time.Now(),
			"source_api":   "oil_products_api",
			"version":      1,
		},
	}

	// 转换为JSON
	jsonData, err := json.Marshal(metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	return datatypes.JSON(jsonData), nil
}

// ValidateConvertedFuelGrade 验证转换后的FuelGrade
func (c *FuelGradeConverter) ValidateConvertedFuelGrade(fuelGrade *models.FuelGrade) error {
	if fuelGrade == nil {
		return fmt.Errorf("fuel grade cannot be nil")
	}

	// 验证必要字段
	if fuelGrade.ID == "" {
		return fmt.Errorf("fuel grade ID cannot be empty")
	}

	if fuelGrade.Name == "" {
		return fmt.Errorf("fuel grade name cannot be empty")
	}

	if fuelGrade.Type == "" {
		return fmt.Errorf("fuel grade type cannot be empty")
	}

	// 验证价格
	if fuelGrade.Price.IsNegative() {
		return fmt.Errorf("fuel grade price cannot be negative")
	}

	if fuelGrade.Price.IsZero() {
		return fmt.Errorf("fuel grade price cannot be zero")
	}

	// 验证货币
	if fuelGrade.Currency != "IDR" {
		return fmt.Errorf("expected currency to be IDR, got %s", fuelGrade.Currency)
	}

	c.logger.Debug("[FuelSync] Fuel grade validation passed",
		zap.String("fuel_grade_id", fuelGrade.ID),
		zap.String("name", fuelGrade.Name),
		zap.String("price", fuelGrade.Price.String()))

	return nil
}

// GetConversionSummary 获取转换摘要
func (c *FuelGradeConverter) GetConversionSummary(original []OilProduct, converted []*models.FuelGrade, errors []error) map[string]interface{} {
	summary := map[string]interface{}{
		"total_input":            len(original),
		"successful_conversions": len(converted),
		"conversion_errors":      len(errors),
		"success_rate":           float64(len(converted)) / float64(len(original)) * 100,
		"timestamp":              time.Now(),
	}

	if len(converted) > 0 {
		// 统计类型分布
		typeCount := make(map[string]int)
		totalPrice := decimal.Zero

		for _, fg := range converted {
			typeCount[fg.Type]++
			totalPrice = totalPrice.Add(fg.Price)
		}

		summary["type_distribution"] = typeCount
		summary["average_price"] = totalPrice.Div(decimal.NewFromInt(int64(len(converted)))).String()
	}

	if len(errors) > 0 {
		errorMessages := make([]string, len(errors))
		for i, err := range errors {
			errorMessages[i] = err.Error()
		}
		summary["error_details"] = errorMessages
	}

	return summary
}
