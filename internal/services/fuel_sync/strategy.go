package fuel_sync

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// FuelSyncStrategy 油品同步策略接口
type FuelSyncStrategy interface {
	// FetchProducts 从外部API获取油品数据
	FetchProducts(ctx context.Context) ([]OilProduct, error)

	// SystemID 返回系统标识
	SystemID() string

	// ShouldRetry 检查错误是否应该重试
	ShouldRetry(err error) bool

	// MaxRetries 返回最大重试次数
	MaxRetries() int

	// RetryDelay 返回重试延迟
	RetryDelay() time.Duration
}

// HTTPFuelSyncStrategy HTTP油品同步策略
// 基于现有HTTPSyncStrategy模式设计
type HTTPFuelSyncStrategy struct {
	systemID   string
	endpoint   string
	timeout    time.Duration
	maxRetries int
	retryDelay time.Duration
	headers    map[string]string
	apiKey     string
	logger     *zap.Logger
}

// NewHTTPFuelSyncStrategy 创建HTTP油品同步策略
func NewHTTPFuelSyncStrategy(systemID, endpoint string, timeout time.Duration, apiKey string, logger *zap.Logger) FuelSyncStrategy {
	headers := map[string]string{
		"Content-Type": "application/json",
		"Accept":       "application/json",
		"User-Agent":   "FCC-Service-Fuel-Sync/1.0",
	}

	// 添加API密钥认证头（如果提供）
	if apiKey != "" {
		headers["X-API-Key"] = apiKey
	}

	return &HTTPFuelSyncStrategy{
		systemID:   systemID,
		endpoint:   endpoint,
		timeout:    timeout,
		maxRetries: 3,
		retryDelay: 5 * time.Second,
		headers:    headers,
		apiKey:     apiKey,
		logger:     logger,
	}
}

// SystemID 返回系统标识
func (h *HTTPFuelSyncStrategy) SystemID() string {
	return h.systemID
}

// FetchProducts 从外部API获取油品数据
func (h *HTTPFuelSyncStrategy) FetchProducts(ctx context.Context) ([]OilProduct, error) {
	h.logger.Info("[FuelSync] Fetching products from external API",
		zap.String("system_id", h.systemID),
		zap.String("endpoint", h.endpoint))

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: h.timeout,
	}

	// 创建GET请求
	req, err := http.NewRequestWithContext(ctx, "GET", h.endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	for key, value := range h.headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		h.logger.Error("HTTP request failed",
			zap.String("system_id", h.systemID),
			zap.String("endpoint", h.endpoint),
			zap.Error(err))
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		h.logger.Error("HTTP request returned error status",
			zap.String("system_id", h.systemID),
			zap.String("endpoint", h.endpoint),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)))
		return nil, fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	h.logger.Debug("[FuelSync] Raw API response",
		zap.String("system_id", h.systemID),
		zap.String("response_body", string(body)))

	// 解析JSON响应
	products, err := h.parseResponse(body)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	h.logger.Info("[FuelSync] Successfully fetched products",
		zap.String("system_id", h.systemID),
		zap.String("endpoint", h.endpoint),
		zap.Int("product_count", len(products)),
		zap.Int("status_code", resp.StatusCode))

	return products, nil
}

// parseResponse 解析API响应
// 支持两种格式：直接数组 []OilProduct 或包装格式 {products: []OilProduct}
func (h *HTTPFuelSyncStrategy) parseResponse(body []byte) ([]OilProduct, error) {
	// 首先尝试解析为直接数组格式（当前API使用的格式）
	var productsArray []OilProduct
	if err := json.Unmarshal(body, &productsArray); err == nil {
		return productsArray, nil
	}

	// 如果失败，尝试解析为包装格式
	var response OilProductsResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response as array or wrapped format: %w", err)
	}

	// 如果使用包装格式但products字段为空，返回空数组
	if response.Products == nil {
		return []OilProduct{}, nil
	}

	return response.Products, nil
}

// ShouldRetry 检查错误是否应该重试
func (h *HTTPFuelSyncStrategy) ShouldRetry(err error) bool {
	// 网络错误、超时等应该重试
	// 4xx客户端错误通常不应该重试，5xx服务器错误可以重试
	if err == nil {
		return false
	}

	// 检查是否是HTTP状态码错误
	if httpErr, ok := err.(*HTTPError); ok {
		// 5xx服务器错误可以重试
		return httpErr.StatusCode >= 500
	}

	// 其他错误（网络错误、超时等）都重试
	return true
}

// MaxRetries 返回最大重试次数
func (h *HTTPFuelSyncStrategy) MaxRetries() int {
	return h.maxRetries
}

// RetryDelay 返回重试延迟
func (h *HTTPFuelSyncStrategy) RetryDelay() time.Duration {
	return h.retryDelay
}

// HTTPError HTTP错误类型
type HTTPError struct {
	StatusCode int
	Message    string
}

func (e *HTTPError) Error() string {
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
}

// FuelSyncClient 油品同步客户端
// 封装策略并提供重试机制
type FuelSyncClient struct {
	strategy FuelSyncStrategy
	logger   *zap.Logger
}

// NewFuelSyncClient 创建油品同步客户端
func NewFuelSyncClient(strategy FuelSyncStrategy, logger *zap.Logger) *FuelSyncClient {
	return &FuelSyncClient{
		strategy: strategy,
		logger:   logger,
	}
}

// FetchProductsWithRetry 带重试机制的获取油品数据
func (c *FuelSyncClient) FetchProductsWithRetry(ctx context.Context) ([]OilProduct, error) {
	var lastErr error
	maxRetries := c.strategy.MaxRetries()
	retryDelay := c.strategy.RetryDelay()

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			c.logger.Info("[FuelSync] Retrying fetch products",
				zap.String("system_id", c.strategy.SystemID()),
				zap.Int("attempt", attempt),
				zap.Int("max_retries", maxRetries))

			// 等待重试延迟
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(retryDelay):
				// 继续重试
			}
		}

		products, err := c.strategy.FetchProducts(ctx)
		if err == nil {
			if attempt > 0 {
				c.logger.Info("[FuelSync] Retry successful",
					zap.String("system_id", c.strategy.SystemID()),
					zap.Int("attempt", attempt))
			}
			return products, nil
		}

		lastErr = err

		// 检查是否应该重试
		if !c.strategy.ShouldRetry(err) {
			c.logger.Error("[FuelSync] Error not retryable, giving up",
				zap.String("system_id", c.strategy.SystemID()),
				zap.Error(err))
			break
		}

		c.logger.Warn("[FuelSync] Fetch failed, will retry",
			zap.String("system_id", c.strategy.SystemID()),
			zap.Int("attempt", attempt),
			zap.Error(err))
	}

	return nil, fmt.Errorf("failed to fetch products after %d attempts: %w", maxRetries+1, lastErr)
}
