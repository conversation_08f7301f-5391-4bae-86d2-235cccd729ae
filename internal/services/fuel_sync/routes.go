package fuel_sync

import (
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// SetupFuelSyncRoutes 设置油品同步相关的路由
// 在现有的v2 API路由组中添加fuel-sync子路由
func SetupFuelSyncRoutes(v2API *echo.Group, handler *FuelSyncHandler, logger *zap.Logger) {
	// 创建fuel-sync路由组
	fuelSync := v2API.Group("/fuel-sync")

	// 同步控制接口
	fuelSync.POST("/trigger", handler.TriggerSync)  // 手动触发同步
	fuelSync.POST("/start", handler.StartScheduler) // 启动调度器
	fuelSync.POST("/stop", handler.StopScheduler)   // 停止调度器

	// 状态查询接口
	fuelSync.GET("/status", handler.GetSyncStatus)         // 获取同步状态
	fuelSync.GET("/history", handler.GetSyncHistory)       // 获取同步历史
	fuelSync.GET("/statistics", handler.GetSyncStatistics) // 获取统计信息
	fuelSync.GET("/health", handler.HealthCheck)           // 健康检查

	// 配置管理接口
	fuelSync.GET("/config", handler.GetConfig)    // 获取配置
	fuelSync.PUT("/config", handler.UpdateConfig) // 更新配置

	// 油品数据查询接口
	fuelSync.GET("/fuel-grades", handler.GetActiveFuelGrades) // 获取活跃油品列表
	fuelSync.GET("/fuel-grades/:id", handler.GetFuelGrade)    // 获取单个油品

	logger.Info("Fuel sync routes configured successfully",
		zap.String("base_path", "/api/v2/fuel-sync"),
		zap.Int("route_count", 10))
}

// SetupFuelSyncRoutesStandalone 设置独立的油品同步路由
// 用于在独立的Echo实例中设置路由（可选）
func SetupFuelSyncRoutesStandalone(e *echo.Echo, handler *FuelSyncHandler, logger *zap.Logger) {
	// API v2 路由组
	v2API := e.Group("/api/v2")

	// 设置fuel-sync路由
	SetupFuelSyncRoutes(v2API, handler, logger)

	logger.Info("Standalone fuel sync routes configured successfully")
}

// FuelSyncRouteInfo 路由信息结构
type FuelSyncRouteInfo struct {
	Method      string `json:"method"`
	Path        string `json:"path"`
	Handler     string `json:"handler"`
	Description string `json:"description"`
}

// GetFuelSyncRouteInfo 获取所有fuel-sync路由信息
// 用于API文档生成或调试
func GetFuelSyncRouteInfo() []FuelSyncRouteInfo {
	return []FuelSyncRouteInfo{
		{
			Method:      "POST",
			Path:        "/api/v2/fuel-sync/trigger",
			Handler:     "TriggerSync",
			Description: "手动触发油品同步",
		},
		{
			Method:      "POST",
			Path:        "/api/v2/fuel-sync/start",
			Handler:     "StartScheduler",
			Description: "启动同步调度器",
		},
		{
			Method:      "POST",
			Path:        "/api/v2/fuel-sync/stop",
			Handler:     "StopScheduler",
			Description: "停止同步调度器",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/status",
			Handler:     "GetSyncStatus",
			Description: "获取同步状态和配置信息",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/history",
			Handler:     "GetSyncHistory",
			Description: "获取同步历史记录（支持分页）",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/statistics",
			Handler:     "GetSyncStatistics",
			Description: "获取同步统计信息",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/health",
			Handler:     "HealthCheck",
			Description: "健康检查接口",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/config",
			Handler:     "GetConfig",
			Description: "获取当前同步配置",
		},
		{
			Method:      "PUT",
			Path:        "/api/v2/fuel-sync/config",
			Handler:     "UpdateConfig",
			Description: "更新同步配置",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/fuel-grades",
			Handler:     "GetActiveFuelGrades",
			Description: "获取活跃的油品列表",
		},
		{
			Method:      "GET",
			Path:        "/api/v2/fuel-sync/fuel-grades/:id",
			Handler:     "GetFuelGrade",
			Description: "获取单个油品详细信息",
		},
	}
}
