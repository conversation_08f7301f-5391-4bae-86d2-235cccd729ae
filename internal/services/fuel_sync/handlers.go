package fuel_sync

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// FuelSyncHandler 油品同步API处理器
type FuelSyncHandler struct {
	scheduler   *SyncScheduler
	syncService *FuelGradeSyncService
	logger      *zap.Logger
}

// NewFuelSyncHandler 创建油品同步处理器
func NewFuelSyncHandler(scheduler *SyncScheduler, syncService *FuelGradeSyncService, logger *zap.Logger) *FuelSyncHandler {
	return &FuelSyncHandler{
		scheduler:   scheduler,
		syncService: syncService,
		logger:      logger,
	}
}

// TriggerSyncRequest 手动触发同步请求
type TriggerSyncRequest struct {
	UserID    string `json:"user_id"`    // 触发用户ID
	RequestID string `json:"request_id"` // 请求ID（可选）
	Force     bool   `json:"force"`      // 是否强制同步（忽略正在运行的同步）
}

// TriggerSyncResponse 触发同步响应
type TriggerSyncResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Timestamp string `json:"timestamp"`
}

// SyncStatusResponse 同步状态响应
type SyncStatusResponse struct {
	*SyncSchedulerStatus
	Config *SyncSchedulerConfig `json:"config"`
}

// SyncHistoryResponse 同步历史响应
type SyncHistoryResponse struct {
	History []SyncResult `json:"history"`
	Total   int          `json:"total"`
	Page    int          `json:"page"`
	Limit   int          `json:"limit"`
}

// SyncStatisticsResponse 同步统计响应
type SyncStatisticsResponse struct {
	TotalSyncs      int64   `json:"total_syncs"`
	SuccessfulSyncs int64   `json:"successful_syncs"`
	FailedSyncs     int64   `json:"failed_syncs"`
	SuccessRate     float64 `json:"success_rate"`
	LastSyncTime    string  `json:"last_sync_time"`
	NextSyncTime    string  `json:"next_sync_time"`
	Uptime          string  `json:"uptime"`
}

// ConfigUpdateRequest 配置更新请求
type ConfigUpdateRequest struct {
	SyncInterval       string `json:"sync_interval"`        // 同步间隔（如"1h", "30m"）
	EnableScheduled    *bool  `json:"enable_scheduled"`     // 是否启用定时同步
	EnableManual       *bool  `json:"enable_manual"`        // 是否启用手动同步
	MaxRetries         *int   `json:"max_retries"`          // 最大重试次数
	RetryDelay         string `json:"retry_delay"`          // 重试延迟（如"30s", "1m"）
	MaxConsecutiveErrs *int   `json:"max_consecutive_errs"` // 最大连续错误次数
}

// TriggerSync 手动触发同步
// POST /api/v2/fuel-sync/trigger
func (h *FuelSyncHandler) TriggerSync(c echo.Context) error {
	var req TriggerSyncRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid trigger sync request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 验证请求
	if req.UserID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "user_id is required")
	}

	// 生成请求ID（如果未提供）
	if req.RequestID == "" {
		req.RequestID = strconv.FormatInt(time.Now().UnixNano(), 10)
	}

	h.logger.Info("收到手动同步触发请求",
		zap.String("user_id", req.UserID),
		zap.String("request_id", req.RequestID),
		zap.Bool("force", req.Force))

	// 检查调度器状态
	if !h.scheduler.IsRunning() {
		return echo.NewHTTPError(http.StatusServiceUnavailable, "Sync scheduler is not running")
	}

	// 如果不是强制同步，检查是否已有同步在运行
	if !req.Force {
		status := h.scheduler.GetStatus()
		if status.CurrentSyncStatus == "syncing" {
			return echo.NewHTTPError(http.StatusConflict, "Sync is already in progress")
		}
	}

	// 触发同步
	err := h.scheduler.TriggerManualSync(req.UserID, req.RequestID)
	if err != nil {
		h.logger.Error("Failed to trigger manual sync",
			zap.String("user_id", req.UserID),
			zap.String("request_id", req.RequestID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to trigger sync: "+err.Error())
	}

	response := TriggerSyncResponse{
		Success:   true,
		Message:   "Sync triggered successfully",
		RequestID: req.RequestID,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	h.logger.Info("手动同步触发成功",
		zap.String("user_id", req.UserID),
		zap.String("request_id", req.RequestID))

	return c.JSON(http.StatusOK, response)
}

// GetSyncStatus 获取同步状态
// GET /api/v2/fuel-sync/status
func (h *FuelSyncHandler) GetSyncStatus(c echo.Context) error {
	status := h.scheduler.GetStatus()

	// 获取配置信息
	config := h.scheduler.config

	response := SyncStatusResponse{
		SyncSchedulerStatus: status,
		Config:              config,
	}

	return c.JSON(http.StatusOK, response)
}

// GetSyncHistory 获取同步历史
// GET /api/v2/fuel-sync/history
func (h *FuelSyncHandler) GetSyncHistory(c echo.Context) error {
	// 解析分页参数
	page := 1
	limit := 20

	if p := c.QueryParam("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	if l := c.QueryParam("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// 获取同步历史
	history := h.scheduler.GetSyncHistory(limit)

	response := SyncHistoryResponse{
		History: history,
		Total:   len(history),
		Page:    page,
		Limit:   limit,
	}

	return c.JSON(http.StatusOK, response)
}

// GetSyncStatistics 获取同步统计信息
// GET /api/v2/fuel-sync/statistics
func (h *FuelSyncHandler) GetSyncStatistics(c echo.Context) error {
	status := h.scheduler.GetStatus()

	// 计算成功率
	var successRate float64
	if status.TotalSyncs > 0 {
		successRate = float64(status.SuccessfulSyncs) / float64(status.TotalSyncs) * 100
	}

	response := SyncStatisticsResponse{
		TotalSyncs:      status.TotalSyncs,
		SuccessfulSyncs: status.SuccessfulSyncs,
		FailedSyncs:     status.FailedSyncs,
		SuccessRate:     successRate,
		LastSyncTime:    status.LastSyncTime.Format(time.RFC3339),
		NextSyncTime:    status.NextSyncTime.Format(time.RFC3339),
		Uptime:          status.Uptime,
	}

	return c.JSON(http.StatusOK, response)
}

// UpdateConfig 更新同步配置
// PUT /api/v2/fuel-sync/config
func (h *FuelSyncHandler) UpdateConfig(c echo.Context) error {
	var req ConfigUpdateRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid config update request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 获取当前配置
	currentConfig := h.scheduler.config

	// 创建新配置副本
	newConfig := *currentConfig

	// 更新配置字段
	if req.SyncInterval != "" {
		duration, err := time.ParseDuration(req.SyncInterval)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid sync_interval format")
		}
		newConfig.SyncInterval = duration
	}

	if req.EnableScheduled != nil {
		newConfig.EnableScheduled = *req.EnableScheduled
	}

	if req.EnableManual != nil {
		newConfig.EnableManual = *req.EnableManual
	}

	if req.MaxRetries != nil {
		if *req.MaxRetries < 0 {
			return echo.NewHTTPError(http.StatusBadRequest, "max_retries cannot be negative")
		}
		newConfig.MaxRetries = *req.MaxRetries
	}

	if req.RetryDelay != "" {
		duration, err := time.ParseDuration(req.RetryDelay)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid retry_delay format")
		}
		newConfig.RetryDelay = duration
	}

	if req.MaxConsecutiveErrs != nil {
		if *req.MaxConsecutiveErrs < 0 {
			return echo.NewHTTPError(http.StatusBadRequest, "max_consecutive_errs cannot be negative")
		}
		newConfig.MaxConsecutiveErrs = *req.MaxConsecutiveErrs
	}

	// 更新配置
	err := h.scheduler.UpdateConfig(&newConfig)
	if err != nil {
		h.logger.Error("Failed to update sync config", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update config: "+err.Error())
	}

	h.logger.Info("同步配置已更新",
		zap.Duration("sync_interval", newConfig.SyncInterval),
		zap.Bool("enable_scheduled", newConfig.EnableScheduled),
		zap.Bool("enable_manual", newConfig.EnableManual),
		zap.Int("max_retries", newConfig.MaxRetries))

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Configuration updated successfully",
		"config":  &newConfig,
	})
}

// GetConfig 获取当前配置
// GET /api/v2/fuel-sync/config
func (h *FuelSyncHandler) GetConfig(c echo.Context) error {
	config := h.scheduler.config
	return c.JSON(http.StatusOK, config)
}

// StartScheduler 启动调度器
// POST /api/v2/fuel-sync/start
func (h *FuelSyncHandler) StartScheduler(c echo.Context) error {
	if h.scheduler.IsRunning() {
		return echo.NewHTTPError(http.StatusConflict, "Scheduler is already running")
	}

	ctx := c.Request().Context()
	err := h.scheduler.Start(ctx)
	if err != nil {
		h.logger.Error("Failed to start scheduler", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to start scheduler: "+err.Error())
	}

	h.logger.Info("同步调度器已启动")

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Scheduler started successfully",
	})
}

// StopScheduler 停止调度器
// POST /api/v2/fuel-sync/stop
func (h *FuelSyncHandler) StopScheduler(c echo.Context) error {
	if !h.scheduler.IsRunning() {
		return echo.NewHTTPError(http.StatusConflict, "Scheduler is not running")
	}

	err := h.scheduler.Stop()
	if err != nil {
		h.logger.Error("Failed to stop scheduler", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to stop scheduler: "+err.Error())
	}

	h.logger.Info("同步调度器已停止")

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Scheduler stopped successfully",
	})
}

// GetActiveFuelGrades 获取活跃的油品列表
// GET /api/v2/fuel-sync/fuel-grades
func (h *FuelSyncHandler) GetActiveFuelGrades(c echo.Context) error {
	ctx := c.Request().Context()

	fuelGrades, err := h.syncService.GetActiveFuelGrades(ctx)
	if err != nil {
		h.logger.Error("Failed to get active fuel grades", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get fuel grades")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"fuel_grades": fuelGrades,
		"total":       len(fuelGrades),
		"timestamp":   time.Now().Format(time.RFC3339),
	})
}

// GetFuelGrade 获取单个油品信息
// GET /api/v2/fuel-sync/fuel-grades/:id
func (h *FuelSyncHandler) GetFuelGrade(c echo.Context) error {
	fuelGradeID := c.Param("id")
	if fuelGradeID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Fuel grade ID is required")
	}

	ctx := c.Request().Context()

	fuelGrade, err := h.syncService.GetFuelGradeByID(ctx, fuelGradeID)
	if err != nil {
		h.logger.Error("Failed to get fuel grade",
			zap.String("fuel_grade_id", fuelGradeID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Fuel grade not found")
	}

	return c.JSON(http.StatusOK, fuelGrade)
}

// HealthCheck 健康检查
// GET /api/v2/fuel-sync/health
func (h *FuelSyncHandler) HealthCheck(c echo.Context) error {
	isRunning := h.scheduler.IsRunning()
	status := h.scheduler.GetStatus()

	health := map[string]interface{}{
		"service":   "fuel-sync",
		"status":    "healthy",
		"running":   isRunning,
		"timestamp": time.Now().Format(time.RFC3339),
		"scheduler": status.CurrentSyncStatus,
		"last_sync": status.LastSyncTime.Format(time.RFC3339),
		"uptime":    status.Uptime,
	}

	// 如果有连续错误，标记为警告状态
	if status.ConsecutiveErrors > 0 {
		health["status"] = "warning"
		health["warning"] = "有连续同步错误"
		health["consecutive_errors"] = status.ConsecutiveErrors
	}

	// 如果调度器未运行，标记为不健康
	if !isRunning {
		health["status"] = "unhealthy"
		health["error"] = "调度器未运行"
		return c.JSON(http.StatusServiceUnavailable, health)
	}

	return c.JSON(http.StatusOK, health)
}
