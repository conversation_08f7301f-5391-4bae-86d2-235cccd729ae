package fuel_sync

import (
	"context"
	"fmt"
	"time"

	"fcc-service/pkg/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// FuelGradeSyncService 油品同步服务
type FuelGradeSyncService struct {
	db        *gorm.DB
	client    *FuelSyncClient
	converter *FuelGradeConverter
	logger    *zap.Logger
	config    *SyncConfig
}

// SyncStatistics 同步统计信息
type SyncStatistics struct {
	StartTime time.Time     `json:"start_time"`
	EndTime   time.Time     `json:"end_time"`
	Duration  time.Duration `json:"duration"`

	// 数据统计
	TotalFetched   int `json:"total_fetched"`   // 从外部API获取的总数
	TotalProcessed int `json:"total_processed"` // 处理的总数
	NewCreated     int `json:"new_created"`     // 新创建的数量
	Updated        int `json:"updated"`         // 更新的数量
	SoftDeleted    int `json:"soft_deleted"`    // 软删除的数量
	Restored       int `json:"restored"`        // 恢复的数量
	Errors         int `json:"errors"`          // 错误数量
	Skipped        int `json:"skipped"`         // 跳过的数量

	// 错误详情
	ErrorDetails []string `json:"error_details,omitempty"`

	// 性能指标
	AvgProcessingTime time.Duration `json:"avg_processing_time"` // 平均处理时间
	ThroughputPerSec  float64       `json:"throughput_per_sec"`  // 每秒处理量
}

// NewFuelGradeSyncService 创建新的油品同步服务
func NewFuelGradeSyncService(db *gorm.DB, client *FuelSyncClient, converter *FuelGradeConverter, logger *zap.Logger, config *SyncConfig) *FuelGradeSyncService {
	if config == nil {
		config = DefaultSyncConfig()
	}

	return &FuelGradeSyncService{
		db:        db,
		client:    client,
		converter: converter,
		logger:    logger,
		config:    config,
	}
}

// SyncAll 执行完整同步流程
func (s *FuelGradeSyncService) SyncAll(ctx context.Context) (*SyncStatistics, error) {
	s.logger.Info("开始执行油品同步",
		zap.Bool("full_sync", s.config.FullSync),
		zap.Bool("incremental_sync", s.config.IncrementalSync),
		zap.Bool("soft_delete", s.config.SoftDelete))

	stats := &SyncStatistics{
		StartTime:    time.Now(),
		ErrorDetails: make([]string, 0),
	}

	// 1. 从外部API获取数据
	oilProducts, err := s.fetchExternalData(ctx)
	if err != nil {
		return stats, fmt.Errorf("获取外部数据失败: %w", err)
	}

	stats.TotalFetched = len(oilProducts)
	s.logger.Info("成功获取外部数据", zap.Int("count", stats.TotalFetched))

	// 2. 数据转换
	fuelGrades, conversionErrors := s.converter.ConvertBatchOilProductsToFuelGrades(oilProducts)
	stats.TotalProcessed = len(fuelGrades)
	stats.Errors = len(conversionErrors)

	// 记录转换错误
	for _, convErr := range conversionErrors {
		stats.ErrorDetails = append(stats.ErrorDetails, convErr.Error())
	}

	s.logger.Info("数据转换完成",
		zap.Int("converted", stats.TotalProcessed),
		zap.Int("conversion_errors", stats.Errors))

	// 3. 获取本地现有数据
	localFuelGrades, err := s.getLocalFuelGrades(ctx)
	if err != nil {
		return stats, fmt.Errorf("获取本地数据失败: %w", err)
	}

	s.logger.Info("获取本地数据", zap.Int("local_count", len(localFuelGrades)))

	// 4. 执行同步逻辑
	if s.config.FullSync {
		syncStats, err := s.performFullSync(ctx, fuelGrades, localFuelGrades)
		if err != nil {
			return stats, fmt.Errorf("全量同步失败: %w", err)
		}
		s.mergeSyncStats(stats, syncStats)
	} else if s.config.IncrementalSync {
		syncStats, err := s.performIncrementalSync(ctx, fuelGrades, localFuelGrades)
		if err != nil {
			return stats, fmt.Errorf("增量同步失败: %w", err)
		}
		s.mergeSyncStats(stats, syncStats)
	}

	// 5. 完成统计
	stats.EndTime = time.Now()
	stats.Duration = stats.EndTime.Sub(stats.StartTime)

	if stats.Duration > 0 {
		stats.ThroughputPerSec = float64(stats.TotalProcessed) / stats.Duration.Seconds()
		if stats.TotalProcessed > 0 {
			stats.AvgProcessingTime = stats.Duration / time.Duration(stats.TotalProcessed)
		}
	}

	s.logger.Info("油品同步完成",
		zap.Duration("duration", stats.Duration),
		zap.Int("total_fetched", stats.TotalFetched),
		zap.Int("total_processed", stats.TotalProcessed),
		zap.Int("new_created", stats.NewCreated),
		zap.Int("updated", stats.Updated),
		zap.Int("soft_deleted", stats.SoftDeleted),
		zap.Int("restored", stats.Restored),
		zap.Int("errors", stats.Errors),
		zap.Float64("throughput_per_sec", stats.ThroughputPerSec))

	return stats, nil
}

// fetchExternalData 从外部API获取数据
func (s *FuelGradeSyncService) fetchExternalData(ctx context.Context) ([]OilProduct, error) {
	s.logger.Debug("开始从外部API获取数据")

	oilProducts, err := s.client.FetchProductsWithRetry(ctx)
	if err != nil {
		s.logger.Error("获取外部数据失败", zap.Error(err))
		return nil, err
	}

	s.logger.Debug("成功获取外部数据", zap.Int("count", len(oilProducts)))
	return oilProducts, nil
}

// getLocalFuelGrades 获取本地油品数据
func (s *FuelGradeSyncService) getLocalFuelGrades(ctx context.Context) (map[string]*models.FuelGrade, error) {
	s.logger.Debug("开始获取本地油品数据")

	var fuelGrades []*models.FuelGrade

	// 获取所有油品（包括软删除的）
	query := s.db.WithContext(ctx).Unscoped()
	if err := query.Find(&fuelGrades).Error; err != nil {
		s.logger.Error("获取本地油品数据失败", zap.Error(err))
		return nil, err
	}

	// 转换为map以便快速查找
	localMap := make(map[string]*models.FuelGrade)
	for _, fg := range fuelGrades {
		localMap[fg.ID] = fg
	}

	s.logger.Debug("成功获取本地油品数据", zap.Int("count", len(localMap)))
	return localMap, nil
}

// performFullSync 执行全量同步
func (s *FuelGradeSyncService) performFullSync(ctx context.Context, externalFuelGrades []*models.FuelGrade, localFuelGrades map[string]*models.FuelGrade) (*SyncStatistics, error) {
	s.logger.Info("开始执行全量同步")

	stats := &SyncStatistics{
		StartTime:    time.Now(),
		ErrorDetails: make([]string, 0),
	}

	// 创建外部数据的ID集合
	externalIDs := make(map[string]bool)
	for _, fg := range externalFuelGrades {
		externalIDs[fg.ID] = true
	}

	// 1. 处理外部数据：新增和更新
	for _, externalFG := range externalFuelGrades {
		if localFG, exists := localFuelGrades[externalFG.ID]; exists {
			// 更新现有记录
			if s.shouldUpdate(localFG, externalFG) {
				if err := s.updateFuelGrade(ctx, localFG, externalFG); err != nil {
					stats.Errors++
					stats.ErrorDetails = append(stats.ErrorDetails, fmt.Sprintf("更新油品%s失败: %v", externalFG.ID, err))
					s.logger.Error("更新油品失败", zap.String("id", externalFG.ID), zap.Error(err))
				} else {
					stats.Updated++
					s.logger.Debug("更新油品成功", zap.String("id", externalFG.ID))
				}
			} else {
				stats.Skipped++
				s.logger.Debug("跳过更新，数据无变化", zap.String("id", externalFG.ID))
			}
		} else {
			// 创建新记录
			if err := s.createFuelGrade(ctx, externalFG); err != nil {
				stats.Errors++
				stats.ErrorDetails = append(stats.ErrorDetails, fmt.Sprintf("创建油品%s失败: %v", externalFG.ID, err))
				s.logger.Error("创建油品失败", zap.String("id", externalFG.ID), zap.Error(err))
			} else {
				stats.NewCreated++
				s.logger.Debug("创建油品成功", zap.String("id", externalFG.ID))
			}
		}
	}

	// 2. 处理软删除：本地存在但外部不存在的记录
	if s.config.SoftDelete {
		for localID, localFG := range localFuelGrades {
			if !externalIDs[localID] {
				if localFG.IsActive() {
					// 软删除活跃的本地记录
					if err := s.softDeleteFuelGrade(ctx, localFG); err != nil {
						stats.Errors++
						stats.ErrorDetails = append(stats.ErrorDetails, fmt.Sprintf("软删除油品%s失败: %v", localID, err))
						s.logger.Error("软删除油品失败", zap.String("id", localID), zap.Error(err))
					} else {
						stats.SoftDeleted++
						s.logger.Debug("软删除油品成功", zap.String("id", localID))
					}
				}
			} else if !localFG.IsActive() {
				// 恢复已软删除但外部重新出现的记录
				if err := s.restoreFuelGrade(ctx, localFG); err != nil {
					stats.Errors++
					stats.ErrorDetails = append(stats.ErrorDetails, fmt.Sprintf("恢复油品%s失败: %v", localID, err))
					s.logger.Error("恢复油品失败", zap.String("id", localID), zap.Error(err))
				} else {
					stats.Restored++
					s.logger.Debug("恢复油品成功", zap.String("id", localID))
				}
			}
		}
	}

	stats.EndTime = time.Now()
	stats.Duration = stats.EndTime.Sub(stats.StartTime)

	s.logger.Info("全量同步完成",
		zap.Duration("duration", stats.Duration),
		zap.Int("new_created", stats.NewCreated),
		zap.Int("updated", stats.Updated),
		zap.Int("soft_deleted", stats.SoftDeleted),
		zap.Int("restored", stats.Restored),
		zap.Int("errors", stats.Errors),
		zap.Int("skipped", stats.Skipped))

	return stats, nil
}

// performIncrementalSync 执行增量同步
func (s *FuelGradeSyncService) performIncrementalSync(ctx context.Context, externalFuelGrades []*models.FuelGrade, localFuelGrades map[string]*models.FuelGrade) (*SyncStatistics, error) {
	s.logger.Info("开始执行增量同步")

	stats := &SyncStatistics{
		StartTime:    time.Now(),
		ErrorDetails: make([]string, 0),
	}

	// 增量同步只处理变化的数据
	for _, externalFG := range externalFuelGrades {
		if localFG, exists := localFuelGrades[externalFG.ID]; exists {
			// 检查是否需要更新
			if s.shouldUpdate(localFG, externalFG) {
				if err := s.updateFuelGrade(ctx, localFG, externalFG); err != nil {
					stats.Errors++
					stats.ErrorDetails = append(stats.ErrorDetails, fmt.Sprintf("增量更新油品%s失败: %v", externalFG.ID, err))
					s.logger.Error("增量更新油品失败", zap.String("id", externalFG.ID), zap.Error(err))
				} else {
					stats.Updated++
					s.logger.Debug("增量更新油品成功", zap.String("id", externalFG.ID))
				}
			} else {
				stats.Skipped++
			}
		} else {
			// 创建新记录
			if err := s.createFuelGrade(ctx, externalFG); err != nil {
				stats.Errors++
				stats.ErrorDetails = append(stats.ErrorDetails, fmt.Sprintf("增量创建油品%s失败: %v", externalFG.ID, err))
				s.logger.Error("增量创建油品失败", zap.String("id", externalFG.ID), zap.Error(err))
			} else {
				stats.NewCreated++
				s.logger.Debug("增量创建油品成功", zap.String("id", externalFG.ID))
			}
		}
	}

	stats.EndTime = time.Now()
	stats.Duration = stats.EndTime.Sub(stats.StartTime)

	s.logger.Info("增量同步完成",
		zap.Duration("duration", stats.Duration),
		zap.Int("new_created", stats.NewCreated),
		zap.Int("updated", stats.Updated),
		zap.Int("errors", stats.Errors),
		zap.Int("skipped", stats.Skipped))

	return stats, nil
}

// shouldUpdate 判断是否需要更新
func (s *FuelGradeSyncService) shouldUpdate(local, external *models.FuelGrade) bool {
	// 检查关键字段是否有变化
	if local.Name != external.Name {
		return true
	}
	if local.Type != external.Type {
		return true
	}
	if !local.Price.Equal(external.Price) {
		return true
	}
	if local.Description != external.Description {
		return true
	}
	if local.Octane != external.Octane {
		return true
	}
	if local.Color != external.Color {
		return true
	}

	// 检查软删除状态
	if local.IsDeleted() != external.IsDeleted() {
		return true
	}

	return false
}

// createFuelGrade 创建新的油品记录
func (s *FuelGradeSyncService) createFuelGrade(ctx context.Context, fuelGrade *models.FuelGrade) error {
	return s.db.WithContext(ctx).Create(fuelGrade).Error
}

// updateFuelGrade 更新现有的油品记录
func (s *FuelGradeSyncService) updateFuelGrade(ctx context.Context, local, external *models.FuelGrade) error {
	// 根据冲突解决策略处理
	switch s.config.ConflictResolution {
	case "external_wins":
		// 外部数据优先：完全使用外部数据
		local.UpdateFromExternal(external.Name, external.Type, external.Description, external.Price, nil)
		local.Octane = external.Octane
		local.Color = external.Color
		local.Currency = external.Currency

		// 处理软删除状态
		if external.IsDeleted() && !local.IsDeleted() {
			local.SoftDelete()
		} else if !external.IsDeleted() && local.IsDeleted() {
			local.Restore()
		}

	case "local_wins":
		// 本地数据优先：只更新非冲突字段
		s.logger.Info("本地数据优先策略：跳过更新", zap.String("id", local.ID))
		return nil

	case "manual":
		// 手动处理：记录冲突但不自动解决
		s.logger.Warn("检测到数据冲突，需要手动处理",
			zap.String("id", local.ID),
			zap.String("local_name", local.Name),
			zap.String("external_name", external.Name))
		return fmt.Errorf("数据冲突需要手动处理: %s", local.ID)

	default:
		return fmt.Errorf("未知的冲突解决策略: %s", s.config.ConflictResolution)
	}

	return s.db.WithContext(ctx).Save(local).Error
}

// softDeleteFuelGrade 软删除油品记录
func (s *FuelGradeSyncService) softDeleteFuelGrade(ctx context.Context, fuelGrade *models.FuelGrade) error {
	fuelGrade.SoftDelete()
	return s.db.WithContext(ctx).Save(fuelGrade).Error
}

// restoreFuelGrade 恢复软删除的油品记录
func (s *FuelGradeSyncService) restoreFuelGrade(ctx context.Context, fuelGrade *models.FuelGrade) error {
	fuelGrade.Restore()
	return s.db.WithContext(ctx).Save(fuelGrade).Error
}

// mergeSyncStats 合并同步统计信息
func (s *FuelGradeSyncService) mergeSyncStats(target, source *SyncStatistics) {
	target.NewCreated += source.NewCreated
	target.Updated += source.Updated
	target.SoftDeleted += source.SoftDeleted
	target.Restored += source.Restored
	target.Errors += source.Errors
	target.Skipped += source.Skipped
	target.ErrorDetails = append(target.ErrorDetails, source.ErrorDetails...)
}

// GetSyncConfig 获取同步配置
func (s *FuelGradeSyncService) GetSyncConfig() *SyncConfig {
	return s.config
}

// UpdateSyncConfig 更新同步配置
func (s *FuelGradeSyncService) UpdateSyncConfig(config *SyncConfig) {
	s.config = config
	s.logger.Info("同步配置已更新",
		zap.Bool("full_sync", config.FullSync),
		zap.Bool("incremental_sync", config.IncrementalSync),
		zap.Bool("soft_delete", config.SoftDelete),
		zap.String("conflict_resolution", config.ConflictResolution))
}

// GetActiveFuelGrades 获取活跃的油品列表
func (s *FuelGradeSyncService) GetActiveFuelGrades(ctx context.Context) ([]*models.FuelGrade, error) {
	var fuelGrades []*models.FuelGrade

	err := s.db.WithContext(ctx).
		Where("deleted_at IS NULL").
		Order("created_at DESC").
		Find(&fuelGrades).Error

	if err != nil {
		s.logger.Error("获取活跃油品列表失败", zap.Error(err))
		return nil, err
	}

	s.logger.Debug("获取活跃油品列表成功", zap.Int("count", len(fuelGrades)))
	return fuelGrades, nil
}

// GetFuelGradeByID 根据ID获取油品
func (s *FuelGradeSyncService) GetFuelGradeByID(ctx context.Context, id string) (*models.FuelGrade, error) {
	var fuelGrade models.FuelGrade

	err := s.db.WithContext(ctx).
		Where("id = ?", id).
		First(&fuelGrade).Error

	if err != nil {
		s.logger.Error("获取油品失败", zap.String("id", id), zap.Error(err))
		return nil, err
	}

	return &fuelGrade, nil
}

// GetSyncStatistics 获取同步统计信息
func (s *FuelGradeSyncService) GetSyncStatistics(ctx context.Context) (*SyncStatistics, error) {
	// 这里可以从数据库或缓存中获取历史统计信息
	// 目前返回当前状态的统计

	var totalCount, activeCount, deletedCount int64

	// 获取总数
	if err := s.db.WithContext(ctx).Model(&models.FuelGrade{}).Unscoped().Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// 获取活跃数量
	if err := s.db.WithContext(ctx).Model(&models.FuelGrade{}).Where("deleted_at IS NULL").Count(&activeCount).Error; err != nil {
		return nil, err
	}

	// 获取软删除数量
	if err := s.db.WithContext(ctx).Model(&models.FuelGrade{}).Where("deleted_at IS NOT NULL").Count(&deletedCount).Error; err != nil {
		return nil, err
	}

	return &SyncStatistics{
		TotalFetched:   int(totalCount),
		TotalProcessed: int(activeCount),
		SoftDeleted:    int(deletedCount),
		StartTime:      time.Now(),
		EndTime:        time.Now(),
	}, nil
}
