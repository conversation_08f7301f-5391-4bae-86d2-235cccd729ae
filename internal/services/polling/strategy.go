package polling

import (
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PollResult 轮询结果
type PollResult struct {
	Success      bool          `json:"success"`       // 是否成功
	ResponseTime time.Duration `json:"response_time"` // 响应时间
	Error        string        `json:"error"`         // 错误信息
	Data         interface{}   `json:"data"`          // 响应数据
}

// ReconnectInfo 重连信息
type ReconnectInfo struct {
	LastAttempt  time.Time     `json:"last_attempt"`  // 上次重连尝试时间
	AttemptCount int           `json:"attempt_count"` // 重连尝试次数
	NextAttempt  time.Time     `json:"next_attempt"`  // 下次重连时间
	BackoffDelay time.Duration `json:"backoff_delay"` // 退避延迟
	MaxBackoff   time.Duration `json:"max_backoff"`   // 最大退避时间
	Enabled      bool          `json:"enabled"`       // 是否启用重连
}

// ErrorInfo 错误信息
type ErrorInfo struct {
	ErrorType     string    `json:"error_type"`     // 错误类型
	ErrorCode     string    `json:"error_code"`     // 错误代码
	ErrorMessage  string    `json:"error_message"`  // 错误消息
	FirstOccurred time.Time `json:"first_occurred"` // 首次出现时间
	LastOccurred  time.Time `json:"last_occurred"`  // 最后出现时间
	Count         int       `json:"count"`          // 出现次数
	Severity      string    `json:"severity"`       // 严重级别
}

// PollStrategy 轮询策略
type PollStrategy struct {
	config *PollConfig
	logger *zap.Logger
	mu     sync.RWMutex
}

// NewPollStrategy 创建轮询策略
func NewPollStrategy(config *PollConfig, logger *zap.Logger) *PollStrategy {
	return &PollStrategy{
		config: config,
		logger: logger,
	}
}

// GetPollInterval 获取轮询间隔
func (ps *PollStrategy) GetPollInterval(deviceInfo *DevicePollInfo) time.Duration {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	// 根据优先级确定基础间隔
	var baseInterval time.Duration
	switch deviceInfo.Priority {
	case PriorityHigh:
		baseInterval = ps.config.FastInterval
	case PriorityNormal:
		baseInterval = ps.config.DefaultInterval
	case PriorityLow:
		baseInterval = ps.config.SlowInterval
	default:
		baseInterval = ps.config.DefaultInterval
	}

	// 根据失败次数调整间隔
	if deviceInfo.FailureCount > 0 {
		// 故障设备使用慢速轮询
		baseInterval = ps.config.SlowInterval
	}

	// 新设备特殊处理
	if deviceInfo.LastPoll.IsZero() && deviceInfo.Priority == PriorityHigh {
		baseInterval = ps.config.FastInterval
	}

	return baseInterval
}

// ShouldPoll 判断是否应该轮询
func (ps *PollStrategy) ShouldPoll(deviceInfo *DevicePollInfo) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	// 检查是否超过最大失败次数
	if deviceInfo.FailureCount > ps.config.MaxFailures {
		ps.logger.Debug("Device exceeded max failures",
			zap.String("device_id", deviceInfo.DeviceID),
			zap.Int("failure_count", deviceInfo.FailureCount),
			zap.Int("max_failures", ps.config.MaxFailures))
		return false
	}

	// 检查是否到了轮询时间
	now := time.Now()
	return now.After(deviceInfo.NextPoll) || now.Equal(deviceInfo.NextPoll)
}

// UpdatePriority 更新设备优先级
func (ps *PollStrategy) UpdatePriority(deviceInfo *DevicePollInfo, result *PollResult) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if result.Success {
		// 成功轮询的处理
		deviceInfo.Status = "success"

		// 如果之前是故障设备，现在恢复了，提升优先级
		if deviceInfo.Priority == PriorityLow && deviceInfo.FailureCount == 0 {
			deviceInfo.Priority = PriorityNormal
			ps.logger.Info("Device recovered, upgrading priority",
				zap.String("device_id", deviceInfo.DeviceID),
				zap.String("priority", "normal"))
		}

		// 新设备首次成功后降为正常优先级
		if deviceInfo.Priority == PriorityHigh {
			deviceInfo.Priority = PriorityNormal
			ps.logger.Info("New device stabilized, normalizing priority",
				zap.String("device_id", deviceInfo.DeviceID),
				zap.String("priority", "normal"))
		}
	} else {
		// 失败轮询的处理
		deviceInfo.Status = "failed"

		// 根据失败次数调整优先级
		if deviceInfo.FailureCount >= 2 && deviceInfo.Priority != PriorityLow {
			deviceInfo.Priority = PriorityLow
			ps.logger.Warn("Device failing repeatedly, lowering priority",
				zap.String("device_id", deviceInfo.DeviceID),
				zap.Int("failure_count", deviceInfo.FailureCount),
				zap.String("priority", "low"))
		}
	}

	// 计算轮询间隔（内联逻辑避免死锁）
	var interval time.Duration
	switch deviceInfo.Priority {
	case PriorityHigh:
		interval = ps.config.FastInterval
	case PriorityNormal:
		interval = ps.config.DefaultInterval
	case PriorityLow:
		interval = ps.config.SlowInterval
	default:
		interval = ps.config.DefaultInterval
	}

	// 根据失败次数调整间隔
	if deviceInfo.FailureCount > 0 {
		interval = ps.config.SlowInterval
	}

	// 新设备特殊处理
	if deviceInfo.LastPoll.IsZero() && deviceInfo.Priority == PriorityHigh {
		interval = ps.config.FastInterval
	}

	// 更新下次轮询时间
	deviceInfo.NextPoll = time.Now().Add(interval)
	deviceInfo.PollInterval = interval

	ps.logger.Debug("Updated device poll info",
		zap.String("device_id", deviceInfo.DeviceID),
		zap.String("priority", ps.priorityToString(deviceInfo.Priority)),
		zap.Duration("next_interval", interval),
		zap.Time("next_poll", deviceInfo.NextPoll))
}

// UpdateConfig 更新轮询配置
func (ps *PollStrategy) UpdateConfig(config *PollConfig) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	ps.config = config
	ps.logger.Info("Poll strategy configuration updated",
		zap.Duration("default_interval", config.DefaultInterval),
		zap.Duration("fast_interval", config.FastInterval),
		zap.Duration("slow_interval", config.SlowInterval),
		zap.Int("max_failures", config.MaxFailures))
}

// GetConfig 获取当前配置
func (ps *PollStrategy) GetConfig() *PollConfig {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	// 返回配置副本
	configCopy := *ps.config
	return &configCopy
}

// CalculateNextPollTime 计算下次轮询时间（支持负载均衡）
func (ps *PollStrategy) CalculateNextPollTime(deviceInfo *DevicePollInfo) time.Time {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	interval := ps.GetPollInterval(deviceInfo)
	baseTime := time.Now().Add(interval)

	// 简单的负载均衡：添加随机抖动避免同时轮询
	jitterRange := 5 * time.Second
	jitter := time.Duration(time.Now().UnixNano() % int64(jitterRange))
	if time.Now().UnixNano()%2 == 0 {
		baseTime = baseTime.Add(jitter)
	} else {
		baseTime = baseTime.Add(-jitter)
	}

	return baseTime
}

// GetPriorityDevices 获取指定优先级的设备数量（用于统计）
func (ps *PollStrategy) GetPriorityDevices(devices []*DevicePollInfo, priority PollPriority) []*DevicePollInfo {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	var result []*DevicePollInfo
	for _, device := range devices {
		if device.Priority == priority {
			result = append(result, device)
		}
	}
	return result
}

// priorityToString 优先级转字符串（用于日志）
func (ps *PollStrategy) priorityToString(priority PollPriority) string {
	switch priority {
	case PriorityHigh:
		return "high"
	case PriorityNormal:
		return "normal"
	case PriorityLow:
		return "low"
	default:
		return "unknown"
	}
}

// ValidateConfig 验证轮询配置
func ValidateConfig(config *PollConfig) error {
	if config.DefaultInterval <= 0 {
		return ErrInvalidPollInterval
	}
	if config.FastInterval <= 0 {
		return ErrInvalidPollInterval
	}
	if config.SlowInterval <= 0 {
		return ErrInvalidPollInterval
	}
	if config.MaxFailures < 0 {
		return ErrInvalidMaxFailures
	}
	if config.FastInterval > config.DefaultInterval {
		return ErrInvalidIntervalOrder
	}
	if config.DefaultInterval > config.SlowInterval {
		return ErrInvalidIntervalOrder
	}
	return nil
}

// DefaultPollStrategyConfig 默认轮询策略配置
func DefaultPollStrategyConfig() *PollConfig {
	return &PollConfig{
		DefaultInterval: 30 * time.Second,
		FastInterval:    5 * time.Second,
		SlowInterval:    60 * time.Second,
		MaxFailures:     5,
	}
}

// GetReconnectInterval 获取重连间隔（指数退避）
func (ps *PollStrategy) GetReconnectInterval(reconnectInfo *ReconnectInfo) time.Duration {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if !reconnectInfo.Enabled {
		return 0 // 禁用重连
	}

	// 计算指数退避间隔
	baseDelay := 1 * time.Second
	maxDelay := 5 * time.Minute

	if reconnectInfo.MaxBackoff > 0 {
		maxDelay = reconnectInfo.MaxBackoff
	}

	// 指数退避: baseDelay * 2^attemptCount，但不超过maxDelay
	backoffDelay := baseDelay
	for i := 0; i < reconnectInfo.AttemptCount && backoffDelay < maxDelay; i++ {
		backoffDelay *= 2
	}

	if backoffDelay > maxDelay {
		backoffDelay = maxDelay
	}

	return backoffDelay
}

// ShouldAttemptReconnect 判断是否应该尝试重连
func (ps *PollStrategy) ShouldAttemptReconnect(deviceInfo *DevicePollInfo, reconnectInfo *ReconnectInfo) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if !reconnectInfo.Enabled {
		return false
	}

	// 检查是否超过最大失败次数
	if deviceInfo.FailureCount < 3 {
		return false // 失败次数不够，不需要重连
	}

	// 检查是否到了重连时间
	now := time.Now()
	return now.After(reconnectInfo.NextAttempt) || now.Equal(reconnectInfo.NextAttempt)
}

// UpdateReconnectInfo 更新重连信息
func (ps *PollStrategy) UpdateReconnectInfo(reconnectInfo *ReconnectInfo, success bool) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	now := time.Now()
	reconnectInfo.LastAttempt = now

	if success {
		// 重连成功，重置信息
		reconnectInfo.AttemptCount = 0
		reconnectInfo.BackoffDelay = 0
		reconnectInfo.NextAttempt = time.Time{} // 清除下次重连时间

		ps.logger.Info("Device reconnected successfully",
			zap.Time("last_attempt", reconnectInfo.LastAttempt))
	} else {
		// 重连失败，增加计数并计算下次尝试时间
		reconnectInfo.AttemptCount++
		reconnectInfo.BackoffDelay = ps.GetReconnectInterval(reconnectInfo)
		reconnectInfo.NextAttempt = now.Add(reconnectInfo.BackoffDelay)

		ps.logger.Warn("Device reconnection failed",
			zap.Int("attempt_count", reconnectInfo.AttemptCount),
			zap.Duration("backoff_delay", reconnectInfo.BackoffDelay),
			zap.Time("next_attempt", reconnectInfo.NextAttempt))
	}
}

// ClassifyError 分类错误类型
func (ps *PollStrategy) ClassifyError(errorMsg string) *ErrorInfo {
	now := time.Now()

	// 简单的错误分类逻辑
	switch {
	case contains(errorMsg, "timeout", "time out"):
		return &ErrorInfo{
			ErrorType:     "communication",
			ErrorCode:     "TIMEOUT",
			ErrorMessage:  errorMsg,
			FirstOccurred: now,
			LastOccurred:  now,
			Count:         1,
			Severity:      "medium",
		}
	case contains(errorMsg, "connection", "connect"):
		return &ErrorInfo{
			ErrorType:     "connection",
			ErrorCode:     "CONNECTION_FAILED",
			ErrorMessage:  errorMsg,
			FirstOccurred: now,
			LastOccurred:  now,
			Count:         1,
			Severity:      "high",
		}
	case contains(errorMsg, "protocol", "frame", "invalid"):
		return &ErrorInfo{
			ErrorType:     "protocol",
			ErrorCode:     "PROTOCOL_ERROR",
			ErrorMessage:  errorMsg,
			FirstOccurred: now,
			LastOccurred:  now,
			Count:         1,
			Severity:      "medium",
		}
	case contains(errorMsg, "device not found", "not found"):
		return &ErrorInfo{
			ErrorType:     "device",
			ErrorCode:     "DEVICE_NOT_FOUND",
			ErrorMessage:  errorMsg,
			FirstOccurred: now,
			LastOccurred:  now,
			Count:         1,
			Severity:      "high",
		}
	default:
		return &ErrorInfo{
			ErrorType:     "unknown",
			ErrorCode:     "UNKNOWN_ERROR",
			ErrorMessage:  errorMsg,
			FirstOccurred: now,
			LastOccurred:  now,
			Count:         1,
			Severity:      "low",
		}
	}
}

// ShouldRetryImmediately 判断是否应该立即重试
func (ps *PollStrategy) ShouldRetryImmediately(errorInfo *ErrorInfo) bool {
	// 根据错误类型决定是否立即重试
	switch errorInfo.ErrorType {
	case "communication":
		return errorInfo.Count <= 2 // 通信错误最多重试2次
	case "protocol":
		return errorInfo.Count <= 1 // 协议错误最多重试1次
	case "connection":
		return false // 连接错误不立即重试
	case "device":
		return false // 设备错误不立即重试
	default:
		return errorInfo.Count <= 1 // 未知错误最多重试1次
	}
}

// GetRetryDelay 获取重试延迟
func (ps *PollStrategy) GetRetryDelay(errorInfo *ErrorInfo) time.Duration {
	switch errorInfo.ErrorType {
	case "communication":
		return time.Duration(errorInfo.Count) * 2 * time.Second // 2s, 4s, 6s...
	case "protocol":
		return 5 * time.Second // 固定5秒
	case "connection":
		return 30 * time.Second // 连接错误等30秒
	case "device":
		return 60 * time.Second // 设备错误等60秒
	default:
		return 10 * time.Second // 未知错误等10秒
	}
}

// contains 检查字符串是否包含任一关键词（不区分大小写）
func contains(text string, keywords ...string) bool {
	lowerText := strings.ToLower(text)
	for _, keyword := range keywords {
		if strings.Contains(lowerText, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}
