package polling

import "errors"

var (
	ErrSchedulerNotRunning  = errors.New("scheduler is not running")
	ErrDeviceNotFound       = errors.New("device not found in scheduler")
	ErrInvalidDeviceID      = errors.New("invalid device ID")
	ErrInvalidPollInterval  = errors.New("invalid poll interval")
	ErrInvalidMaxFailures   = errors.New("invalid max failures")
	ErrInvalidIntervalOrder = errors.New("invalid interval order: fast > default > slow")
)
