/*
Package v2 提供接口定义以避免轮询服务中的循环依赖。

# 架构概述

本包包含解决以下循环依赖问题的接口定义：
- transaction_lifecycle → transaction → polling/v2 → transaction_lifecycle

# 设计原则

1. **避免循环依赖**：接口定义与具体实现分离
2. **接口隔离**：每个接口专注于特定职责
3. **依赖倒置**：高层模块依赖于抽象，而不是具体实现

# 使用模式

在 dispatch_task.go 中：
```go
// 使用接口类型作为依赖
lifecycleService      TransactionLifecycleServiceInterface
nozzleCountersService NozzleCountersServiceInterface
```

在 main_v2.go 中使用适配器模式解决类型不匹配：
```go
// 创建适配器
lifecycleAdapter := &TransactionLifecycleServiceAdapter{
    service: lifecycleService,
}
nozzleCountersAdapter := &NozzleCountersServiceAdapter{
    service: nozzleCountersService,
}
```

# 维护指南

1. **接口变更**：修改接口时需要更新所有实现类
2. **版本兼容**：添加新接口方法时考虑向后兼容性
3. **文档更新**：接口变更时及时更新此文档
4. **测试覆盖**：确保所有接口方法都有对应的测试用例
*/

package v2

import (
	"context"
	"time"

	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
)

// NozzleCountersServiceInterface 提供DC101泵码表管理功能。
//
// 此接口处理：
// - DC101交易数据处理
// - 计数器值更新和检索
// - 更新事件监听
type NozzleCountersServiceInterface interface {
	// RecordDC101Event 记录DC101事件
	RecordDC101Event(ctx context.Context, deviceID string, nozzleID string, counterType int16, value decimal.Decimal, timestamp time.Time, dc101Data []byte) (*models.NozzleCounters, error)

	// GetLatestCounterValue 获取指定类型的最新计数器值
	GetLatestCounterValue(ctx context.Context, deviceID string, nozzleID string, counterType int16) (decimal.Decimal, error)

	// GetCounterHistory 获取计数器历史记录
	GetCounterHistory(ctx context.Context, deviceID string, nozzleID string, counterType int16, limit int) ([]*models.NozzleCounters, error)

	// RegisterUpdateListener 注册计数器更新监听器
	RegisterUpdateListener(listener CounterUpdateListener)

	// UnregisterUpdateListener 移除计数器更新监听器
	UnregisterUpdateListener(listener CounterUpdateListener)
}

// TransactionLifecycleServiceInterface 提供实时交易状态管理。
//
// 此接口处理：
// - 交易生命周期事件（DC1、DC2、DC3、DC101）
// - 交易状态管理
// - 交易清理和取消
// - 事件监听器管理
// - 交易统计和监控
type TransactionLifecycleServiceInterface interface {
	// HandleNozzleOut 处理喷嘴出枪事件（DC3）
	HandleNozzleOut(ctx context.Context, deviceID string, nozzleID string, price decimal.Decimal, timestamp time.Time) (*models.Transaction, error)

	// HandleVolumeAmountUpdate 处理体积/金额更新（DC2）
	HandleVolumeAmountUpdate(ctx context.Context, deviceID string, nozzleID string, volume, amount decimal.Decimal, timestamp time.Time) error

	// HandleFillingCompleted 处理加油完成事件（DC1）
	HandleFillingCompleted(ctx context.Context, deviceID string, timestamp time.Time) error

	// HandleDC101CounterUpdate 处理DC101泵码更新事件
	HandleDC101CounterUpdate(ctx context.Context, deviceID string, nozzleID string, counterRecord *models.NozzleCounters) error

	// 🆕 新的事件驱动方法

	// HandleNozzleIn 处理喷嘴插回事件
	HandleNozzleIn(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error

	// HandleAuth 处理授权事件
	HandleAuth(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error

	// HandleFillingStart 处理开始加油事件
	HandleFillingStart(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error

	// HandleStop 处理停止事件
	HandleStop(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error

	// EnsureActiveTransaction 确保设备+喷嘴始终有一个活跃交易
	EnsureActiveTransaction(ctx context.Context, deviceID string, nozzleID string, triggerReason string) (*models.Transaction, error)

	// RegisterTransactionListener 注册交易事件监听器
	RegisterTransactionListener(listener TransactionLifecycleListenerInterface) error

	// GetActiveTransaction 获取特定喷嘴的活跃交易
	GetActiveTransaction(ctx context.Context, deviceID string, nozzleID string) (*models.Transaction, error)

	// GetActiveTransactions 获取设备的所有活跃交易
	GetActiveTransactions(ctx context.Context, deviceID string) ([]*models.Transaction, error)
}

// TransactionLifecycleListenerInterface 定义交易生命周期事件处理器。
//
// 实现应该处理交易状态变化并提供唯一的监听器ID用于管理。
type TransactionLifecycleListenerInterface interface {
	// OnTransactionCreated 处理交易创建事件
	OnTransactionCreated(tx *models.Transaction) error

	// OnTransactionUpdated 处理交易更新事件
	OnTransactionUpdated(tx *models.Transaction) error

	// OnTransactionCompleted 处理交易完成事件
	OnTransactionCompleted(tx *models.Transaction) error

	// OnTransactionCancelled 处理交易取消事件
	OnTransactionCancelled(tx *models.Transaction) error

	OnTransactionPending(tx *models.Transaction) error

	// GetListenerID 返回此监听器的唯一标识符
	GetListenerID() string
}

// TransactionStatistics 包含设备的交易统计信息。
//
// 此结构体提供全面的交易指标，包括活跃交易数量、完成统计和体积/金额总计。
type TransactionStatistics struct {
	DeviceID            string          `json:"device_id"`             // 设备ID
	ActiveTransactions  int             `json:"active_transactions"`   // 活跃交易数
	CompletedToday      int             `json:"completed_today"`       // 今日完成交易数
	AverageFillingTime  time.Duration   `json:"average_filling_time"`  // 平均加油时间
	TotalVolumeToday    decimal.Decimal `json:"total_volume_today"`    // 今日总体积
	TotalAmountToday    decimal.Decimal `json:"total_amount_today"`    // 今日总金额
	LastTransactionTime *time.Time      `json:"last_transaction_time"` // 最后交易时间
}

// CounterUpdateListener 定义计数器更新事件处理器。
//
// 实现应该处理计数器更新事件并提供唯一的监听器ID用于管理。
type CounterUpdateListener interface {
	// OnCounterUpdated 处理计数器更新事件
	OnCounterUpdated(event *models.CounterUpdateEvent) error

	// GetListenerID 返回此监听器的唯一标识符
	GetListenerID() string
}
