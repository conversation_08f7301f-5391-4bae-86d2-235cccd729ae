package v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/adapters/wayne/pump"
	"fcc-service/internal/server/dto"
	"fcc-service/internal/services/nozzle_counters"
	"fcc-service/pkg/models"
	v2models "fcc-service/pkg/models/v2"
	"strings"
)

// CD2ConfigurationProvider 接口：提供CD2配置信息的接口
type CD2ConfigurationProvider interface {
	GetAuthorizedNozzles() ([]byte, error)
}

// TransactionAssembler 交易组装器 - 🔄 重构为纯协议处理器
type TransactionAssembler struct {
	deviceID     string
	logger       *zap.Logger
	bcdConverter pump.BCDConverter

	// 🔄 重构：移除交易管理逻辑，只保留协议状态跟踪
	// 删除：activeTransactions, transactionHistory, pendingTransactionData

	// 设备协议状态跟踪（纯协议层面）
	deviceStatus          string
	deviceStatusTime      time.Time
	currentActiveNozzle   *byte
	currentActiveNozzleID string

	// 员工ID缓存（协议层共享数据）
	// 🆕 扩展：现在也包含预授权和自动授权功能
	operatorIDCache OperatorIDCacheInterface

	// 协议处理核心组件
	// pumpReadingCache *PumpReadingCache

	// 🚀 完全托付：交易业务逻辑处理器
	lifecycleService TransactionLifecycleServiceInterface

	// 🎯 新增：DC101流水记录服务
	nozzleCountersService nozzle_counters.Service

	// 🎯 解决方案：CD2配置提供者接口
	cd2ConfigProvider CD2ConfigurationProvider

	// 并发保护
	mu sync.RWMutex

	nozzleIDResolver NozzleIDResolver

	// 🔄 重构：简化回调，只用于适配器模式
	// onTransactionStart    func(*models.Transaction)
	// onTransactionUpdate   func(*models.Transaction)
	// onTransactionComplete func(*models.Transaction)

	// 🆕 设备轮询器引用，用于执行预授权和自动授权命令
	devicePoller *DevicePoller
}

// NewTransactionAssembler 创建交易组装器 - 🔄 重构为纯协议处理器
func NewTransactionAssembler(
	deviceID string,
	logger *zap.Logger,
	operatorIDCache OperatorIDCacheInterface,
	lifecycleService TransactionLifecycleServiceInterface,
	nozzleCountersService nozzle_counters.Service,
	nozzleIDResolver NozzleIDResolver,
	devicePoller *DevicePoller,
) *TransactionAssembler {
	return &TransactionAssembler{
		deviceID:              deviceID,
		logger:                logger,
		bcdConverter:          pump.NewBCDConverter(),
		operatorIDCache:       operatorIDCache,
		lifecycleService:      lifecycleService,
		nozzleCountersService: nozzleCountersService,
		nozzleIDResolver:      nozzleIDResolver,
		devicePoller:          devicePoller, // 🆕 注入设备轮询器引用
	}
}

// ProcessDC101Transaction 处理DC101计数器事务 - 🔄 重构版本
func (ta *TransactionAssembler) ProcessDC101Transaction(data []byte, timestamp time.Time) error {
	if len(data) < 6 {
		return fmt.Errorf("DC101事务数据过短，需要至少6字节")
	}

	counterType := data[0]
	counterBCD := data[1:6] // 5字节BCD计数器值

	// 解码计数器值
	bcdConverter := pump.NewBCDConverter()
	var counterValue decimal.Decimal

	// 🎯 完整的Wayne DART协议DC101计数器类型支持
	// 根据Wayne Pump Interface v2.1规范
	switch {
	case counterType >= 0x01 && counterType <= 0x08:
		// 体积计数器：逻辑喷嘴1-8的体积总计
		counterValue = decimal.NewFromFloat(bcdConverter.DecodeVolume(counterBCD, 3))
	case counterType == 0x09:
		// 🆕 总体积计数器：所有逻辑喷嘴体积总和
		counterValue = decimal.NewFromFloat(bcdConverter.DecodeVolume(counterBCD, 3))
	case counterType >= 0x11 && counterType <= 0x18:
		// 金额计数器：逻辑喷嘴1-8的金额总计
		rawValue := decimal.NewFromFloat(bcdConverter.DecodeAmount(counterBCD, 3))
		counterValue = rawValue.Div(decimal.NewFromInt(1000)) // DC101金额特殊处理：除以1000
		// counterValue = rawValue.Div(decimal.NewFromInt(1)) // DC101金额特殊处理TODOTODO

	case counterType == 0x19:
		// 🆕 总金额计数器：所有逻辑喷嘴金额总和
		rawValue := decimal.NewFromFloat(bcdConverter.DecodeAmount(counterBCD, 3))
		counterValue = rawValue.Div(decimal.NewFromInt(1000)) // DC101金额特殊处理：除以1000
		// counterValue = rawValue.Div(decimal.NewFromInt(1)) // DC101金额特殊处理TODOTODO

	default:
		// 不支持的计数器类型 - 记录但不报错，保持系统稳定性
		ta.logger.Warn("收到不支持的DC101计数器类型",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("counter_type", counterType),
			zap.String("counter_type_hex", fmt.Sprintf("0x%02X", counterType)),
			zap.String("note", "Wayne DART协议未定义此计数器类型"))
		return nil // 不报错，避免中断系统运行
	}

	ta.logger.Debug("Processing DC101 counter transaction",
		zap.String("device_id", ta.deviceID),
		zap.Uint8("counter_type", counterType),
		zap.String("counter_type_hex", fmt.Sprintf("0x%02X", counterType)),
		zap.String("counter_value", counterValue.String()),
		zap.String("counter_category", ta.getCounterTypeCategory(counterType)),
		zap.Time("timestamp", timestamp))

	// 🎯 核心改进：区分单喷嘴计数器vs总计数器的处理逻辑
	ctx := context.Background()

	// 处理单喷嘴计数器（0x01-0x08, 0x11-0x18）
	if (counterType >= 0x01 && counterType <= 0x08) || (counterType >= 0x11 && counterType <= 0x18) {
		// 🎯 关键修复：从计数器类型直接推导喷嘴编号
		// 根据Wayne DART协议：
		// - 0x01-0x08: 喷嘴1-8的体积计数器
		// - 0x11-0x18: 喷嘴1-8的金额计数器
		var targetNozzleNumber byte
		if counterType >= 0x01 && counterType <= 0x08 {
			targetNozzleNumber = counterType // 0x01->1, 0x02->2, ..., 0x08->8
		} else if counterType >= 0x11 && counterType <= 0x18 {
			targetNozzleNumber = counterType - 0x10 // 0x11->1, 0x12->2, ..., 0x18->8
		}

		ta.logger.Debug("DC101 counter type mapped to nozzle",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("counter_type", counterType),
			zap.String("counter_type_hex", fmt.Sprintf("0x%02X", counterType)),
			zap.Uint8("target_nozzle_number", targetNozzleNumber))

		// 解析目标喷嘴的ID
		nozzleID, err := ta.nozzleIDResolver.ResolveNozzleID(ctx, ta.deviceID, targetNozzleNumber)
		if err != nil {
			// 🔧 修复：添加容错机制，当喷嘴ID解析失败时记录警告但不中断处理
			ta.logger.Warn("Failed to resolve nozzle ID for DC101 counter, skipping this counter update",
				zap.String("device_id", ta.deviceID),
				zap.Uint8("target_nozzle_number", targetNozzleNumber),
				zap.Uint8("counter_type", counterType),
				zap.String("counter_type_hex", fmt.Sprintf("0x%02X", counterType)),
				zap.String("counter_value", counterValue.String()),
				zap.Error(err),
				zap.String("suggestion", "Please check if nozzle record exists in database"))

			// 记录到错误统计中，但不返回错误，避免中断整个处理流程
			ta.logger.Info("DC101 counter update skipped due to missing nozzle configuration",
				zap.String("device_id", ta.deviceID),
				zap.Uint8("missing_nozzle_number", targetNozzleNumber),
				zap.String("counter_type_hex", fmt.Sprintf("0x%02X", counterType)))

			// 继续处理，不返回错误
			return nil
		}

		// 🎯 【业务逻辑1】流水记录：将DC101事件作为客观事实独立记录到 nozzle_counters 表
		// ⚠️ 重要：DC101事件不应主动关联交易 - 这违背了数据驱动原则
		// 正确做法：DC101作为客观泵码数据独立存在，由交易状态机主动查找匹配的泵码
		if counterRecord, err := ta.nozzleCountersService.RecordDC101Event(ctx, ta.deviceID, nozzleID, int16(counterType), counterValue, timestamp, data); err != nil {
			ta.logger.Warn("Failed to record DC101 event - continuing with transaction processing",
				zap.String("device_id", ta.deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Uint8("counter_type", counterType),
				zap.String("counter_value", counterValue.String()),
				zap.Error(err),
				zap.String("impact", "Counter recording failed but transaction processing continues"))
			// 继续执行，不返回错误
		} else {
			ta.logger.Debug("DC101 event recorded successfully as independent counter data",
				zap.String("device_id", ta.deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Uint8("counter_type", counterType),
				zap.String("counter_value", counterValue.String()),
				zap.String("principle", "DC101 recorded as objective pump data - transactions will query for matching increments"))

			// 🎯 【业务逻辑2】泵码更新成功后，尝试更新交易的 end pump reading
			// 直接使用返回的计数器记录，调用 lifecycle 服务进行匹配更新
			if counterRecord != nil {
				if err := ta.lifecycleService.HandleDC101CounterUpdate(ctx, ta.deviceID, nozzleID, counterRecord); err != nil {
					ta.logger.Warn("Failed to update transaction end pump reading from DC101",
						zap.String("device_id", ta.deviceID),
						zap.String("nozzle_id", nozzleID),
						zap.Uint8("counter_type", counterType),
						zap.Error(err),
						zap.String("impact", "End pump reading update failed but DC101 recording succeeded"))
				} else {
					ta.logger.Debug("Successfully attempted to update transaction end pump reading from DC101",
						zap.String("device_id", ta.deviceID),
						zap.String("nozzle_id", nozzleID),
						zap.Uint8("counter_type", counterType))
				}
			}
		}

		ta.logger.Debug("DC101 single nozzle counter update processed successfully",
			zap.String("device_id", ta.deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Uint8("counter_type", counterType),
			zap.Uint8("target_nozzle_number", targetNozzleNumber),
			zap.String("counter_value", counterValue.String()))
	} else if counterType == 0x09 || counterType == 0x19 {
		// 🆕 处理总计数器（0x09总体积, 0x19总金额）
		// 总计数器是设备级别的，使用nozzle 0记录
		ta.logger.Info("收到设备总计数器更新",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("counter_type", counterType),
			zap.String("counter_value", counterValue.String()),
			zap.String("counter_name", ta.getCounterTypeName(counterType)))

		// 处理总计数器时，直接返回 nil，不进行后续交易管理
		return nil
	}

	return nil
}

// getCounterTypeCategory 获取计数器类型分类
func (ta *TransactionAssembler) getCounterTypeCategory(counterType byte) string {
	switch {
	case counterType >= 0x01 && counterType <= 0x08:
		return "single_nozzle_volume"
	case counterType == 0x09:
		return "total_device_volume"
	case counterType >= 0x11 && counterType <= 0x18:
		return "single_nozzle_amount"
	case counterType == 0x19:
		return "total_device_amount"
	default:
		return "unknown"
	}
}

// getCounterTypeName 获取计数器类型名称
func (ta *TransactionAssembler) getCounterTypeName(counterType byte) string {
	switch counterType {
	case 0x09:
		return "Total Volume (All Nozzles)"
	case 0x19:
		return "Total Amount (All Nozzles)"
	default:
		if counterType >= 0x01 && counterType <= 0x08 {
			return fmt.Sprintf("Nozzle %d Volume", counterType)
		} else if counterType >= 0x11 && counterType <= 0x18 {
			return fmt.Sprintf("Nozzle %d Amount", counterType-0x10)
		}
		return fmt.Sprintf("Unknown (0x%02X)", counterType)
	}
}

// ProcessDC1Transaction 处理DC1状态事务 - 🔄 重构为完全托付模式
func (ta *TransactionAssembler) ProcessDC1Transaction(data []byte, timestamp time.Time) error {
	if len(data) < 1 {
		return fmt.Errorf("DC1 transaction data too short")
	}

	status := data[0]

	// 🎯 协议处理：更新设备协议状态
	ta.mu.Lock()
	ta.deviceStatus = ta.getPumpStatusName(status)
	ta.deviceStatusTime = timestamp
	ta.mu.Unlock()

	// 🚀 完全托付：将业务逻辑处理委托给TransactionLifecycleService
	ctx := context.Background()

	// 获取当前活跃喷嘴ID的辅助函数
	getCurrentActiveNozzleID := func() string {
		ta.mu.RLock()
		defer ta.mu.RUnlock()
		return ta.currentActiveNozzleID
	}

	ta.logger.Info("[TransactionAssembler]DC1设备状态更新",
		zap.String("device_id", ta.deviceID),
		zap.Uint8("status_code", status),
		zap.String("status_name", ta.deviceStatus),
		zap.String("current_active_nozzle_id", getCurrentActiveNozzleID()))

	switch status {
	case 0x01: // RESET - 设备重置
		// 设备重置的状态管理由 DevicePoller.processDC1Status 处理
		// 这里不需要额外的交易生命周期处理
		ta.logger.Debug("Device reset - state management handled by DevicePoller",
			zap.String("device_id", ta.deviceID))
		return nil

	case 0x02: // AUTHORIZED - 授权

		if authorizedNozzles, err := ta.cd2ConfigProvider.GetAuthorizedNozzles(); err == nil && len(authorizedNozzles) > 0 {
			// 解析第一个授权喷嘴为nozzleID
			// TODO: 需要优化，如果授权喷嘴有多个怎么办
			nozzleNumber := authorizedNozzles[0]
			if nozzleID, err := ta.nozzleIDResolver.ResolveNozzleID(ctx, ta.deviceID, nozzleNumber); err == nil {
				ta.currentActiveNozzleID = nozzleID
				ta.logger.Info("🎯 通过CD2缓存获取授权喷嘴信息",
					zap.String("device_id", ta.deviceID),
					zap.Uint8("nozzle_number", nozzleNumber),
					zap.String("nozzle_id", nozzleID),
					zap.String("solution", "CD2配置缓存解决DC1 AUTHORIZED状态无法获取喷嘴问题"))

				// TODO 这个状态不再被使用
				return nil
			} else {
				ta.logger.Error("解析CD2缓存喷嘴ID失败",
					zap.String("device_id", ta.deviceID),
					zap.Uint8("nozzle_number", nozzleNumber),
					zap.Error(err))
			}
		} else {
			ta.logger.Warn("无法从CD2缓存获取授权喷嘴信息",
				zap.String("device_id", ta.deviceID),
				zap.Error(err))
		}
		return nil

	case 0x03: // FILLING_START - 开始加油
		// 开始加油需要当前活跃的喷嘴
		if currentActiveNozzleID := getCurrentActiveNozzleID(); currentActiveNozzleID != "" {
			ta.logger.Debug("Handling FILLING_START event",
				zap.String("device_id", ta.deviceID),
				zap.String("active_nozzle_id", currentActiveNozzleID))
			return ta.lifecycleService.HandleFillingStart(ctx, ta.deviceID, currentActiveNozzleID, timestamp)
		} else {
			ta.logger.Debug("Filling start received but no active nozzle selected",
				zap.String("device_id", ta.deviceID))
			return nil
		}

	case 0x04: // FILLING - 正在加油 🔧 修正：之前错误地映射到HandleStop
		// 正在加油状态 - 这是一个中间状态，不需要特殊的生命周期处理
		// 实际的加油数据会通过DC2事务报告

		ta.logger.Debug("Device in FILLING state - no special lifecycle handling needed",
			zap.String("device_id", ta.deviceID),
			zap.String("note", "Volume/Amount updates will come via DC2 transactions"))
		return nil

	case 0x05: // FILLING_COMPLETED - 加油完成
		ta.logger.Debug("Handling FILLING_COMPLETED event",
			zap.String("device_id", ta.deviceID))
		return ta.lifecycleService.HandleFillingCompleted(ctx, ta.deviceID, timestamp)

	case 0x06: // MAX_AMOUNT_REACHED - 达到最大预设金额/体积，停止
		// 🎯 修正：达到最大预设也应该走 FILLING_COMPLETED 流程
		ta.logger.Debug("Handling MAX_AMOUNT_REACHED event - treating as FILLING_COMPLETED",
			zap.String("device_id", ta.deviceID))
		return ta.lifecycleService.HandleFillingCompleted(ctx, ta.deviceID, timestamp)

	case 0x07: // SWITCHED_OFF - 泵已关闭（可人工或软件控制）
		// 🎯 修正：泵关闭也应该走 FILLING_COMPLETED 流程
		ta.logger.Debug("Handling SWITCHED_OFF event - treating as FILLING_COMPLETED",
			zap.String("device_id", ta.deviceID))
		return ta.lifecycleService.HandleFillingCompleted(ctx, ta.deviceID, timestamp)

	case 0x08: // SUSPENDED - 泵暂停状态
		// 暂停状态不需要完成交易，只是状态记录
		ta.logger.Debug("Device suspended - no transaction completion needed",
			zap.String("device_id", ta.deviceID))
		return nil

	case 0x09: // EMERGENCY_STOP - 被急停装置关闭
		// 🎯 修正：急停也应该走 FILLING_COMPLETED 流程
		ta.logger.Debug("Handling EMERGENCY_STOP event - treating as FILLING_COMPLETED",
			zap.String("device_id", ta.deviceID))
		return ta.lifecycleService.HandleFillingCompleted(ctx, ta.deviceID, timestamp)

	case 0x0A: // TRANSACTION_ABORTED - 交易中止（可能 nozzle 拔出）
		// 🎯 修正：交易中止也应该走 FILLING_COMPLETED 流程
		ta.logger.Debug("Handling TRANSACTION_ABORTED event - treating as FILLING_COMPLETED",
			zap.String("device_id", ta.deviceID))
		return ta.lifecycleService.HandleFillingCompleted(ctx, ta.deviceID, timestamp)

	case 0x0F: // NOZZLE_RETURNED - 喷嘴挂回后进入 reset 等待下一次启动
		// 喷嘴归位 - 这通常在交易完成后发生，不需要额外处理
		ta.logger.Debug("Nozzle returned - ready for next transaction",
			zap.String("device_id", ta.deviceID))
		return nil

	default:
		// 其他状态更新 - 暂不需要特殊处理
		ta.logger.Debug("DC1状态更新无需特殊业务处理",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("status_code", status),
			zap.String("status_name", ta.deviceStatus))
		return nil
	}
}

// ProcessDC2Transaction 处理DC2体积/金额事务 - 🔄 重构为完全托付模式
func (ta *TransactionAssembler) ProcessDC2Transaction(data []byte, nozzleID string, timestamp time.Time) error {
	if len(data) < 8 {
		return fmt.Errorf("DC2 transaction data too short: expected 8 bytes, got %d", len(data))
	}

	// 🎯 协议处理：解码BCD数据
	volumeBCD := data[0:4]
	amountBCD := data[4:8]

	volume := decimal.NewFromFloat(ta.bcdConverter.DecodeVolume(volumeBCD, 3))
	amount := decimal.NewFromFloat(ta.bcdConverter.DecodeAmount(amountBCD, 0))

	ta.logger.Info("[ProcessDC2Transaction]DC2体积金额数据",
		zap.String("device_id", ta.deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("volume", volume.String()),
		zap.String("amount", amount.String()))

	// 🚀 完全托付：将业务逻辑处理委托给TransactionLifecycleService
	ctx := context.Background()

	ta.logger.Info("[ProcessDC2Transaction]HandleVolumeAmountUpdate")
	return ta.lifecycleService.HandleVolumeAmountUpdate(ctx, ta.deviceID, nozzleID, volume, amount, timestamp)
}

// ProcessDC3Transaction 处理DC3价格喷嘴事务 - 🔄 重构版本
func (ta *TransactionAssembler) ProcessDC3Transaction(data []byte, timestamp time.Time) error {
	if len(data) < 4 {
		return fmt.Errorf("DC3事务数据过短，需要至少4字节")
	}

	priceBCD := data[0:3]
	nozzleIO := data[3]

	nozzleNumber := nozzleIO & 0x0F
	isOut := (nozzleIO & 0x10) != 0

	// 解码价格
	bcdConverter := pump.NewBCDConverter()
	price := decimal.NewFromFloat(bcdConverter.DecodePrice(priceBCD, 3))

	ta.logger.Debug("Processing DC3 transaction",
		zap.String("device_id", ta.deviceID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("price", price.String()),
		zap.Bool("is_out", isOut),
		zap.Time("timestamp", timestamp))

	// 🎯 核心状态更新：更新当前活跃喷嘴
	ta.mu.Lock()
	if nozzleNumber == 0 {
		// nozzle 0 表示清除选择
		ta.currentActiveNozzle = nil
		ta.currentActiveNozzleID = ""
		ta.mu.Unlock()

		ta.logger.Debug("DC3 with nozzle 0 (no nozzle selected) - clearing active nozzle",
			zap.String("device_id", ta.deviceID),
			zap.String("price", price.String()),
			zap.Bool("is_out", isOut))

		// ✅ DART协议合规：nozzle 0表示"无喷嘴选中"状态，不需要业务逻辑处理
		return nil
	}

	// 只有非0喷嘴才需要解析nozzle ID
	ctx := context.Background()
	nozzleID, err := ta.nozzleIDResolver.ResolveNozzleID(ctx, ta.deviceID, nozzleNumber)
	if err != nil {
		ta.mu.Unlock()
		return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", ta.deviceID, nozzleNumber, err)
	}

	ta.currentActiveNozzle = &nozzleNumber
	ta.currentActiveNozzleID = nozzleID
	ta.mu.Unlock()

	// 处理真实喷嘴的业务逻辑

	if isOut {
		// 🆕 模式2：预授权模式检查（最高优先级） TODO
		// if ta.operatorIDCache.IsPreAuthEnabled() {

		// }

		if preauth := ta.operatorIDCache.GetPreAuth(ctx, nozzleID); preauth != nil {

			ta.lifecycleService.HandleAuth(ctx, ta.deviceID, nozzleID, timestamp)

			ta.logger.Info("命中预授权，执行预授权流程",
				zap.String("device_id", ta.deviceID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.String("nozzle_id", nozzleID),
				zap.String("price", price.String()),
				zap.String("preset_volume", preauth.Volume.String()))
			return ta.devicePoller.executePreAuthFlow(ctx, nozzleID, price, preauth, timestamp)
		}

		ta.logger.Info("未命中预授权，执行喷嘴拔出流程",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("nozzle_id", nozzleID),
			zap.String("price", price.String()))
		// 如果 Nozzle 是 Auth 状态转为 idle

		// // 🆕 模式1：自动授权模式检查（中等优先级）
		// if ta.operatorIDCache.IsAutoAuthEnabled() {
		// 	if err := ta.devicePoller.executeAutoAuth(ctx, nozzleNumber, nozzleID); err != nil {
		// 		ta.logger.Error("自动授权失败",
		// 			zap.String("device_id", ta.deviceID),
		// 			zap.Uint8("nozzle_number", nozzleNumber),
		// 			zap.Error(err))
		// 		// 继续执行现有逻辑，不中断流程
		// 	} else {
		// 		ta.logger.Info("自动授权执行成功",
		// 			zap.String("device_id", ta.deviceID),
		// 			zap.Uint8("nozzle_number", nozzleNumber))
		// 	}
		// }

		// 现有逻辑：喷嘴拔出事件（最低优先级，保持不变）
		_, err := ta.lifecycleService.HandleNozzleOut(ctx, ta.deviceID, nozzleID, price, timestamp)
		if err != nil {
			ta.logger.Error("Failed to handle nozzle out event",
				zap.String("device_id", ta.deviceID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.String("price", price.String()),
				zap.Error(err))
			return fmt.Errorf("failed to handle nozzle out: %w", err)
		}

		ta.logger.Info("Nozzle out event processed successfully",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("price", price.String()))

		// 抬枪查状态
		ta.devicePoller.ReturnStatus()
	} else {
		// 喷嘴插回事件：完成当前交易

		// ta.logger.Info("喷嘴插回事件，消费授权",
		// 	zap.String("device_id", ta.deviceID),
		// 	zap.Uint8("nozzle_number", nozzleNumber),
		// 	zap.String("nozzle_id", nozzleID),
		// 	zap.String("price", price.String()))
		// if preauth := ta.operatorIDCache.ConsumePreAuth(ctx, nozzleID); preauth != nil {
		// 	ta.logger.Info("喷嘴插回事件，消费授权",
		// 		zap.String("device_id", ta.deviceID),
		// 		zap.Uint8("nozzle_number", nozzleNumber),
		// 		zap.String("nozzle_id", nozzleID),
		// 		zap.String("price", price.String()),
		// 		zap.String("preset_volume", preauth.Volume.String()))
		// }

		err := ta.lifecycleService.HandleNozzleIn(ctx, ta.deviceID, nozzleID, timestamp)
		if err != nil {
			ta.logger.Error("Failed to handle nozzle in event",
				zap.String("device_id", ta.deviceID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.Error(err))
			return fmt.Errorf("failed to handle nozzle in: %w", err)
		}

		ta.logger.Info("Nozzle in event processed successfully",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("nozzle_number", nozzleNumber))

		// 挂枪查泵码
		ta.devicePoller.sendRequestCountersCommandWithNozzle(nozzleID)
	}

	return nil
}

// processDCTransaction 处理单个DC事务
func (p *DevicePoller) processDCTransaction(transaction pump.DCTransaction) error {
	switch transaction.GetType() {
	case pump.TransactionTypeDC1:
		return p.processDC1Status(transaction)
	case pump.TransactionTypeDC2:
		return p.processDC2VolumeAmount(transaction)
	case pump.TransactionTypeDC3:
		return p.processDC3PriceNozzle(transaction)
	case pump.TransactionTypeDC5:
		return p.processDC5Alarm(transaction)
	case pump.TransactionTypeDC7:
		return p.processDC7PumpParameters(transaction)
	case pump.TransactionTypeDC14:
		return p.processDC14SuspendReply(transaction)
	case pump.TransactionTypeDC15:
		return p.processDC15ResumeReply(transaction)
	case pump.TransactionTypeDC101:
		return p.processDC101Counters(transaction)
	default:
		p.logger.Debug("未处理的DC事务类型",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("transaction_type", fmt.Sprintf("0x%02X", transaction.GetType())))
		return nil
	}
}

// processDC1Status 处理DC1状态事务
func (p *DevicePoller) processDC1Status(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 1 {
		return fmt.Errorf("DC1事务数据过短")
	}

	status := data[0]
	// 🔧 修复：使用事务的实际时间戳，而不是当前时间
	timestamp := transaction.GetTimestamp()

	p.logger.Info("设备状态更新",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("status_code", status),
		zap.String("status_name", p.getPumpStatusName(status)))

	// 🔧 DART协议合规：设备状态机状态只能通过DC1事务更新
	// 这是唯一正确的UpdatePumpStatus调用位置 - 响应设备报告的状态
	p.deviceSM.UpdatePumpStatus(v2models.PumpStatus(status))

	// 使用交易组装器处理
	if err := p.transactionAssembler.ProcessDC1Transaction(data, timestamp); err != nil {
		p.logger.Error("交易组装器处理DC1失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Error(err))
	}

	// 处理特殊状态
	ctx := context.Background()
	switch status {
	case 0x02: // AUTHORIZED (修正：Wayne DART协议授权状态码是0x02)
		if authorizedNozzles, err := p.transactionAssembler.cd2ConfigProvider.GetAuthorizedNozzles(); err == nil && len(authorizedNozzles) > 0 {
			// 🎯 批量更新CD2缓存的授权喷嘴状态（避免循环获取锁）
			if err := p.nozzleService.UpdateMultipleNozzlesStatusByNumberDirect(ctx, p.config.DeviceInfo.ID, authorizedNozzles, models.NozzleStatusAuthorized); err != nil {
				p.logger.Error("批量更新指定喷嘴授权状态失败",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Any("nozzle_numbers", authorizedNozzles),
					zap.Error(err))
			} else {
				p.logger.Info("✅ 批量更新指定喷嘴状态为授权（基于CD2缓存）",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Any("nozzle_numbers", authorizedNozzles),
					zap.Int("updated_count", len(authorizedNozzles)),
					zap.String("solution", "CD2配置缓存提供精确喷嘴状态批量更新"))
			}
		} else {
			p.logger.Warn("无法从CD2缓存获取授权喷嘴信息，回退到更新所有有效喷嘴",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Error(err))
			if err := p.nozzleService.UpdateActiveNozzlesStatusDirect(ctx, p.config.DeviceInfo.ID, models.NozzleStatusAuthorized); err != nil {
				p.logger.Error("更新有效喷嘴授权状态失败", zap.Error(err))
			} else {
				p.logger.Info("✅ 有效喷嘴状态更新为授权（回退方案）",
					zap.String("device_id", p.config.DeviceInfo.ID))
			}
		}

	case 0x04: // FILLING - 加油中状态
		// 当进入加油状态时，将其他非工作喷嘴置为idle
		if err := p.handleFillingState(ctx); err != nil {
			p.logger.Error("处理加油状态失败", zap.Error(err))
		}
	case 0x05: // FILLING_COMPLETED - 加油完成状态
		// 当加油完成时，将活跃喷嘴设置为completed状态
		if err := p.handleFillingCompletedState(ctx); err != nil {
			p.logger.Error("处理加油完成状态失败", zap.Error(err))
		}
	case 0x01: // RESET
		if err := p.nozzleService.ResetDeviceNozzlesDirect(ctx, p.config.DeviceInfo.ID); err != nil {
			p.logger.Error("重置设备喷嘴状态失败", zap.Error(err))
		} else {
			p.logger.Info("✅ 设备重置，所有喷嘴状态重置为idle（后续等待DC3事务确认）",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("note", "系统级重置操作，后续DC3事务将报告实际状态"))
		}
	case 0x00: // PUMP_NOT_PROGRAMMED
		// 触发自动价格配置
		go p.triggerAutoPriceConfiguration()
	}

	return nil
}

// processDC2VolumeAmount 处理DC2体积金额事务
func (p *DevicePoller) processDC2VolumeAmount(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 8 {
		return fmt.Errorf("DC2事务数据过短")
	}

	volumeBCD := data[0:4]
	amountBCD := data[4:8]
	// 🔧 修复：使用事务的实际时间戳，而不是当前时间
	timestamp := transaction.GetTimestamp()

	// 获取活跃喷嘴
	var selectedNozzle byte = 0 // 🎯 修复：默认为0表示无活跃喷嘴
	var selectedNozzleID string
	var err error
	ctx := context.Background()

	if activeNozzle := p.transactionAssembler.getCurrentActiveNozzle(); activeNozzle != nil {
		selectedNozzle = *activeNozzle
		// 只有非0喷嘴才解析nozzle ID
		if selectedNozzle != 0 {
			selectedNozzleID, err = p.nozzleIDResolver.ResolveNozzleID(ctx, p.config.DeviceInfo.ID, selectedNozzle)
			if err != nil {
				return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", p.config.DeviceInfo.ID, selectedNozzle, err)
			}
		}
	}

	// 🔧 修复：根据Wayne DART协议，喷嘴编号0表示"无喷嘴选中"状态，不应进行数据库操作
	if selectedNozzle == 0 {
		p.logger.Debug("DC2事务获取到喷嘴编号0（无喷嘴选中状态），跳过数据库更新",
			zap.String("device_id", p.config.DeviceInfo.ID))

		// 🎯 关键修复：当没有活跃喷嘴时，不调用ProcessDC2Transaction避免外键约束错误
		p.logger.Debug("跳过DC2交易处理，因为没有有效的活跃喷嘴",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("reason", "No active nozzle selected"))
		return nil
	}

	// 解码数据
	bcdConverter := pump.NewBCDConverter()
	volume := decimal.NewFromFloat(bcdConverter.DecodeVolume(volumeBCD, 3))
	amount := decimal.NewFromFloat(bcdConverter.DecodeAmount(amountBCD, 3))

	p.logger.Info("收到体积金额数据",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("nozzle_number", selectedNozzle),
		zap.String("nozzle_id", selectedNozzleID),
		zap.String("volume", volume.String()),
		zap.String("amount", amount.String()))

	if amount.GreaterThan(decimal.Zero) {
		if preauth := p.operatorIDCache.ConsumePreAuth(ctx, selectedNozzleID); preauth != nil {
			p.logger.Info("喷嘴FILLING，消费授权+数据库状态",
				zap.String("nozzle_id", selectedNozzleID),
				zap.String("preset_volume", preauth.Volume.String()))
		}

		// 🆕 清理数据库中的预授权信息
		if err := p.nozzleService.ClearNozzlePreAuth(ctx, selectedNozzleID); err != nil {
			p.logger.Error("清理数据库预授权信息失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("nozzle_id", selectedNozzleID),
				zap.Error(err))
			// 注意：这里不返回错误，因为缓存中的预授权已经删除，数据库清理失败不应该影响删除操作
			// 但会记录错误日志用于监控和调试
		} else {
			p.logger.Info("数据库预授权信息已清理",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("nozzle_id", selectedNozzleID))
		}
	}

	if err := p.nozzleService.UpdateNozzleDataDirect(ctx, p.config.DeviceInfo.ID, selectedNozzleID, volume, amount); err != nil {
		p.logger.Error("更新喷嘴数据失败", zap.Error(err))
	}

	// 使用交易组装器处理
	if err := p.transactionAssembler.ProcessDC2Transaction(data, selectedNozzleID, timestamp); err != nil {
		p.logger.Error("交易组装器处理DC2失败", zap.Error(err))
	}

	return nil
}

// processDC3PriceNozzle 处理DC3价格喷嘴事务
func (p *DevicePoller) processDC3PriceNozzle(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 4 {
		return fmt.Errorf("DC3事务数据过短")
	}

	priceBCD := data[0:3]
	nozzleIO := data[3]
	// 🔧 修复：使用事务的实际时间戳，而不是当前时间
	timestamp := transaction.GetTimestamp()

	nozzleNumber := nozzleIO & 0x0F
	isOut := (nozzleIO & 0x10) != 0

	// 解码价格
	bcdConverter := pump.NewBCDConverter()
	price := decimal.NewFromFloat(bcdConverter.DecodePrice(priceBCD, 3))

	p.logger.Info("收到价格喷嘴数据",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("price", price.String()),
		zap.Bool("is_out", isOut))

	// ✅ DART协议合规：DC3事务是喷嘴状态更新的唯一合法来源
	ctx := context.Background()
	if nozzleNumber == 0 {
		// nozzle number 0表示"无喷嘴选中"状态，按Wayne DART协议不需要更新数据库
		p.logger.Debug("收到nozzle编号0的DC3事务，表示清除喷嘴选择",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("price", price.String()),
			zap.Bool("is_out", isOut))
		// 只处理交易组装器的状态逻辑，不进行数据库操作
	} else {
		// ✅ 协议合规：基于设备DC3事务更新喷嘴状态（喷嘴号、价格、isOut状态）
		// 这是喷嘴状态更新的唯一正确方式

		nozzleID, err := p.nozzleIDResolver.ResolveNozzleID(ctx, p.config.DeviceInfo.ID, nozzleNumber)
		if err != nil {
			return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", p.config.DeviceInfo.ID, nozzleNumber, err)
		}

		// 🆕 MVP实现：检测状态变化，如果从 out=true 变为 out=false，消费预授权
		if previousIsOut, err := p.nozzleService.QueryCurrentNozzleIsOut(ctx, nozzleID); err == nil {
			if previousIsOut && !isOut {
				// 状态从 out=true 变为 out=false，消费预授权
				if preauth := p.operatorIDCache.ConsumePreAuth(ctx, nozzleID); preauth != nil {
					// 根据授权类型记录不同的日志
					logFields := []zap.Field{
						zap.String("device_id", p.config.DeviceInfo.ID),
						zap.Uint8("nozzle_number", nozzleNumber),
						zap.String("nozzle_id", nozzleID),
						zap.String("auth_type", preauth.AuthType),
					}

					if preauth.AuthType == "preset_volume" {
						logFields = append(logFields, zap.String("preset_volume", preauth.Volume.String()))
					} else if preauth.AuthType == "preset_amount" && preauth.Amount != nil {
						logFields = append(logFields, zap.String("preset_amount", preauth.Amount.String()))
					}

					p.logger.Info("🎯 检测到喷嘴插回事件，消费预授权", logFields...)
					// 🆕 清理数据库中的预授权信息
					if err := p.nozzleService.ClearNozzlePreAuth(ctx, nozzleID); err != nil {
						p.logger.Error("清理数据库预授权信息失败",
							zap.String("device_id", p.config.DeviceInfo.ID),
							zap.String("nozzle_id", nozzleID),
							zap.Error(err))
						// 注意：这里不返回错误，因为缓存中的预授权已经删除，数据库清理失败不应该影响删除操作
						// 但会记录错误日志用于监控和调试
					} else {
						p.logger.Info("数据库预授权信息已清理",
							zap.String("device_id", p.config.DeviceInfo.ID),
							zap.String("nozzle_id", nozzleID))
					}

				} else {
					p.logger.Debug("喷嘴插回事件，但未找到预授权",
						zap.String("device_id", p.config.DeviceInfo.ID),
						zap.Uint8("nozzle_number", nozzleNumber),
						zap.String("nozzle_id", nozzleID))
				}
			}
		} else {
			p.logger.Warn("查询喷嘴上一次状态失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("nozzle_id", nozzleID),
				zap.Error(err))
		}

		if err := p.nozzleService.UpdateNozzleDC3Direct(ctx, p.config.DeviceInfo.ID, nozzleID, price, isOut, true); err != nil {
			p.logger.Error("更新喷嘴DC3数据失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.Error(err))
		} else {
			p.logger.Debug("✅ 喷嘴状态已根据DC3事务更新",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("nozzle_number", nozzleNumber),
				zap.String("price", price.String()),
				zap.Bool("is_out", isOut),
				zap.String("protocol_source", "DC3_transaction"))
		}
	}

	// 使用交易组装器处理
	if err := p.transactionAssembler.ProcessDC3Transaction(data, timestamp); err != nil {
		p.logger.Error("交易组装器处理DC3失败", zap.Error(err))
	}

	// ✅ DART协议核心原则：DC3事务是喷嘴状态管理的唯一正确来源
	//
	// DC3事务包含：
	// - nozzleNumber: 喷嘴编号 (0=无选中, 1-15=具体喷嘴)
	// - price: 当前价格设置
	// - isOut: 喷嘴状态标志 (true=拔出, false=放回)
	//
	// 协议要求：
	// 1. 主机只能通过DC3事务获取和更新喷嘴状态
	// 2. 主机不得基于业务逻辑主动推测或设置喷嘴状态
	// 3. 所有喷嘴状态变化必须等待设备通过DC3事务报告

	return nil
}

// processDC5Alarm 处理DC5报警事务
func (p *DevicePoller) processDC5Alarm(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 1 {
		return fmt.Errorf("DC5事务数据过短")
	}

	alarmCode := data[0]
	p.logger.Warn("设备报警",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("alarm_code", alarmCode),
		zap.String("alarm_name", p.getAlarmCodeName(alarmCode)))

	return nil
}

// processDC101Counters 处理DC101计数器事务
func (p *DevicePoller) processDC101Counters(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 6 {
		return fmt.Errorf("DC101事务数据过短")
	}

	counterType := data[0]
	// 🔧 修复：使用事务的实际时间戳，而不是当前时间
	timestamp := transaction.GetTimestamp()

	p.logger.Info("[transaction_dc_assembler][DC101]收到计数器数据",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("counter_type", counterType))

	// 使用交易组装器处理
	if err := p.transactionAssembler.ProcessDC101Transaction(data, timestamp); err != nil {
		p.logger.Error("交易组装器处理DC101失败", zap.Error(err))
	}

	return nil
}

// 🆕 优化：handleOperatorIDTimeout 统一的超时处理逻辑
// 无论是定时器触发还是主动检查触发，都使用相同的处理逻辑
func (p *DevicePoller) handleOperatorIDTimeout(operatorID string) {
	p.logger.Warn("处理员工ID超时",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("operator_id", operatorID),
		zap.Duration("timeout_duration", authorizationTimeout))

	// 获取当前泵状态
	deviceState := p.deviceSM.GetStateData()
	currentPumpStatus := deviceState.PumpStatus

	p.logger.Info("员工ID超时时的设备状态",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("operator_id", operatorID),
		zap.Uint8("pump_status", byte(currentPumpStatus)),
		zap.String("status_name", p.getPumpStatusName(byte(currentPumpStatus))))

	// 只有在授权状态下才发送stop命令
	// 如果已经在加油或完成状态，不干预
	if currentPumpStatus == 0x02 { // AUTHORIZED
		p.logger.Info("设备处于授权状态，发送stop命令终止授权",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("operator_id", operatorID))

		if err := p.StopDevice(); err != nil {
			p.logger.Error("员工ID超时发送stop命令失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("operator_id", operatorID),
				zap.Error(err))
		} else {
			p.logger.Info("✅ 员工ID超时stop命令已发送",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("operator_id", operatorID))
		}
	} else if currentPumpStatus == 0x04 { // FILLING
		p.logger.Info("设备正在加油，不发送stop命令",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("operator_id", operatorID),
			zap.String("reason", "避免中断正在进行的加油"))
	} else if currentPumpStatus == 0x05 { // FILLING_COMPLETED
		p.logger.Info("设备加油已完成，不需要发送stop命令",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("operator_id", operatorID))
	} else {
		p.logger.Debug("设备非授权状态，无需发送stop命令",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("operator_id", operatorID),
			zap.String("current_status", p.getPumpStatusName(byte(currentPumpStatus))))
	}
}

// triggerAutoPriceConfiguration 触发自动价格配置
func (p *DevicePoller) triggerAutoPriceConfiguration() {
	p.logger.Info("触发自动价格配置",
		zap.String("device_id", p.config.DeviceInfo.ID))

	// 获取设备价格
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	prices, err := p.getDevicePricesFromNozzleService(ctx)
	if err != nil {
		p.logger.Warn("获取设备价格失败，使用默认价格", zap.Error(err))
		return
	}

	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildUpdatePricesCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		prices,
	)
	if err != nil {
		p.logger.Error("构建价格配置命令失败", zap.Error(err))
		return
	}

	// 发送命令
	if err := p.SendCommand(*cmd); err != nil {
		p.logger.Error("发送价格配置命令失败", zap.Error(err))
	}
}

func (p *DevicePoller) OnTransactionCreated(tx *models.Transaction) error {
	p.logger.Info("交易创建事件",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("transaction_id", tx.ID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("listener", "DevicePoller"))

	return nil
}

func (p *DevicePoller) OnTransactionUpdated(tx *models.Transaction) error {
	p.logger.Debug("交易更新事件",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("transaction_id", tx.ID),
		zap.String("status", string(tx.Status)),
		zap.String("listener", "DevicePoller"))

	// 🆕 智能完成机制：检查是否需要发送CD1 04H命令
	if tx.Status == models.TransactionStatusAwaitingFinalDC2 && tx.AwaitingFinalDC2At != nil {
		p.logger.Info("[DevicePoller] 🆕 检测到交易进入awaiting_final_dc2状态，发送CD1 04H命令",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("transaction_id", tx.ID),
			zap.String("nozzle_id", tx.NozzleID))

		// 立即发送CD1 04H命令获取最终数据
		if err := p.sendCD1_04H_Command(); err != nil {
			p.logger.Error("[DevicePoller] 发送CD1 04H命令失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("transaction_id", tx.ID),
				zap.Error(err))
		} else {
			p.logger.Info("[DevicePoller] ✅ CD1 04H命令已发送，等待最终DC2响应",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("transaction_id", tx.ID))
		}
	}

	return nil
}

func (p *DevicePoller) OnTransactionCompleted(tx *models.Transaction) error {
	p.logger.Info("交易完成事件",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("transaction_id", tx.ID),
		zap.String("volume", tx.ActualVolume.String()),
		zap.String("amount", tx.ActualAmount.String()),
		zap.String("listener", "DevicePoller"))

	p.sendRequestCountersCommand(tx)

	return nil
}

func (p *DevicePoller) OnTransactionCancelled(tx *models.Transaction) error {
	p.logger.Info("交易取消事件",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("transaction_id", tx.ID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("listener", "DevicePoller"))

	// 交易取消时的处理逻辑
	// 🔄 未来可以在这里添加设备级别的清理逻辑
	return nil
}

func (p *DevicePoller) OnTransactionPending(tx *models.Transaction) error {
	// 🆕 智能完成机制：检查是否需要发送CD1 04H命令
	if tx.Status == models.TransactionStatusAwaitingFinalDC2 && tx.AwaitingFinalDC2At != nil {
		p.logger.Info("[DevicePoller] 🆕 检测到交易进入awaiting_final_dc2状态，发送CD1 04H命令",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("transaction_id", tx.ID),
			zap.String("nozzle_id", tx.NozzleID))

		// 立即发送CD1 04H命令获取最终数据
		if err := p.sendCD1_04H_Command(); err != nil {
			p.logger.Error("[DevicePoller] 发送CD1 04H命令失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("transaction_id", tx.ID),
				zap.Error(err))
		} else {
			p.logger.Info("[DevicePoller] ✅ CD1 04H命令已发送，等待最终DC2响应",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("transaction_id", tx.ID))
		}
	}

	return nil
}

// sendRequestCountersCommand 为特定交易发送计数器请求命令
func (p *DevicePoller) sendRequestCountersCommand(tx *models.Transaction) error {
	return p.sendRequestCountersCommandWithPriority(tx, PriorityLow) // 默认低优先级
}

// sendRequestCountersCommandWithPriority 为特定交易发送指定优先级的计数器请求命令
func (p *DevicePoller) sendRequestCountersCommandWithPriority(tx *models.Transaction, priority CommandPriority) error {
	// 根据交易的喷嘴信息确定需要请求的计数器类型
	nozzle, err := p.nozzleService.GetNozzleByID(context.Background(), tx.NozzleID)
	if err != nil {
		p.logger.Error("无法获取喷嘴信息，跳过计数器请求",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("transaction_id", tx.ID),
			zap.String("nozzle_id", tx.NozzleID),
			zap.Error(err))
		return fmt.Errorf("failed to get nozzle info: %w", err)
	}

	nozzleNumber := nozzle.Number
	volumeCounterType := CounterTypeEnum(nozzleNumber)      // 0x01-0x08
	amountCounterType := CounterTypeEnum(nozzleNumber + 16) // 0x11-0x18

	p.logger.Info("🎯 为交易发送计数器请求",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("transaction_id", tx.ID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.Uint8("volume_counter", uint8(volumeCounterType)),
		zap.Uint8("amount_counter", uint8(amountCounterType)))

	// 🔧 修复：使用指定优先级发送计数器请求
	// 发送体积计数器请求
	if err := p.RequestCountersWithPriority(volumeCounterType, priority); err != nil {
		p.logger.Error("发送体积计数器请求失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("transaction_id", tx.ID),
			zap.Uint8("counter_type", uint8(volumeCounterType)),
			zap.String("priority", string(priority)),
			zap.Error(err))
		// 不返回错误，继续发送金额计数器请求
	}

	// 发送金额计数器请求
	// if err := p.RequestCountersWithPriority(amountCounterType, priority); err != nil {
	// 	p.logger.Error("发送金额计数器请求失败",
	// 		zap.String("device_id", p.config.DeviceInfo.ID),
	// 		zap.String("transaction_id", tx.ID),
	// 		zap.Uint8("counter_type", uint8(amountCounterType)),
	// 		zap.String("priority", string(priority)),
	// 		zap.Error(err))
	// 	return fmt.Errorf("failed to request amount counter: %w", err)
	// }

	// p.logger.Info("✅ 计数器请求命令已发送",
	// 	zap.String("device_id", p.config.DeviceInfo.ID),
	// 	zap.String("transaction_id", tx.ID),
	// 	zap.String("nozzle_id", tx.NozzleID),
	// 	zap.Uint8("volume_counter", uint8(volumeCounterType)),
	// 	zap.Uint8("amount_counter", uint8(amountCounterType)))

	return nil
}

func (p *DevicePoller) sendRequestCountersCommandWithNozzle(nozzleID string) error {
	// 根据交易的喷嘴信息确定需要请求的计数器类型
	nozzle, err := p.nozzleService.GetNozzleByID(context.Background(), nozzleID)
	if err != nil {
		p.logger.Error("无法获取喷嘴信息，跳过计数器请求",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return fmt.Errorf("failed to get nozzle info: %w", err)
	}

	nozzleNumber := nozzle.Number
	volumeCounterType := CounterTypeEnum(nozzleNumber)      // 0x01-0x08
	amountCounterType := CounterTypeEnum(nozzleNumber + 16) // 0x11-0x18

	p.logger.Info("🎯 为交易发送计数器请求",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("nozzle_id", nozzleID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.Uint8("volume_counter", uint8(volumeCounterType)),
		zap.Uint8("amount_counter", uint8(amountCounterType)))

	// 🔧 修复：使用指定优先级发送计数器请求
	// 发送体积计数器请求
	if err := p.RequestCountersWithPriority(volumeCounterType, PriorityLow); err != nil {
		p.logger.Error("发送体积计数器请求失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("counter_type", uint8(volumeCounterType)),
			zap.Error(err))
		// 不返回错误，继续发送金额计数器请求
	}

	return nil
}

// handleFillingState 处理加油状态
// 🔧 协议合规性注意：当前实现基于主机端推测来设置喷嘴状态
// 理想情况：应该基于设备DC事务报告的精确状态，而非主机端业务逻辑推测
//
// 当设备进入加油状态时的处理：
// 1. 确定当前工作的喷嘴（通过交易组装器获取）
// 2. 将工作喷嘴设置为filling状态（⚠️ 主机端推测）
// 3. 将其他喷嘴设置为idle状态（⚠️ 主机端推测）
func (p *DevicePoller) handleFillingState(ctx context.Context) error {
	p.logger.Info("处理加油状态开始",
		zap.String("device_id", p.config.DeviceInfo.ID))

	// 获取当前活跃的喷嘴
	activeNozzle := p.transactionAssembler.getCurrentActiveNozzle()
	if activeNozzle == nil {
		// 如果没有活跃喷嘴，尝试从最近的DC3事务获取
		if lastNozzle := p.transactionAssembler.getLastActiveNozzle(); lastNozzle != nil {
			activeNozzle = lastNozzle
			p.logger.Info("从最近DC3事务获取活跃喷嘴",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("nozzle_id", *activeNozzle))
		} else {
			// 如果仍然没有，默认使用喷嘴1
			defaultNozzle := byte(1)
			activeNozzle = &defaultNozzle
			p.logger.Warn("无法确定活跃喷嘴，使用默认喷嘴1",
				zap.String("device_id", p.config.DeviceInfo.ID))
		}
	}

	// 🔧 修复：根据Wayne DART协议，喷嘴编号0表示"无喷嘴选中"状态，不应进行数据库操作
	if *activeNozzle == 0 {
		p.logger.Debug("加油状态获取到喷嘴编号0（无喷嘴选中状态），跳过状态更新",
			zap.String("device_id", p.config.DeviceInfo.ID))
		return nil
	}

	nozzleID, err := p.nozzleIDResolver.ResolveNozzleID(ctx, p.config.DeviceInfo.ID, *activeNozzle)
	if err != nil {
		return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", p.config.DeviceInfo.ID, *activeNozzle, err)
	}

	// 设置活跃喷嘴为filling状态
	p.logger.Info("设置活跃喷嘴为filling状态",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("nozzle_id", nozzleID))
	if err := p.nozzleService.UpdateNozzleStatusDirect(ctx, p.config.DeviceInfo.ID, nozzleID, models.NozzleStatusFilling); err != nil {
		p.logger.Error("设置活跃喷嘴为filling状态失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return fmt.Errorf("设置活跃喷嘴为filling状态失败: %w", err)
	}

	// 获取设备的所有喷嘴
	nozzles, err := p.nozzleService.GetDeviceNozzles(ctx, p.config.DeviceInfo.ID)
	if err != nil {
		p.logger.Error("获取设备喷嘴失败", zap.Error(err))
		return fmt.Errorf("获取设备喷嘴失败: %w", err)
	}

	// 将其他喷嘴设置为idle状态
	idleCount := 0
	for _, nozzle := range nozzles {
		if nozzle.Number != *activeNozzle && nozzle.IsEnabled {

			otherNozzleID, err := p.nozzleIDResolver.ResolveNozzleID(ctx, p.config.DeviceInfo.ID, nozzle.Number)
			if err != nil {
				return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", p.config.DeviceInfo.ID, nozzle.Number, err)
			}

			if err := p.nozzleService.UpdateNozzleStatusDirect(ctx, p.config.DeviceInfo.ID, otherNozzleID, models.NozzleStatusIdle); err != nil {
				p.logger.Error("设置其他喷嘴为idle状态失败",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.String("nozzle_id", otherNozzleID),
					zap.Error(err))
			} else {
				idleCount++
			}
		}
	}

	p.logger.Info("✅ 加油状态处理完成",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("active_nozzle", *activeNozzle),
		zap.String("active_status", "filling"),
		zap.Int("idle_nozzles_count", idleCount))

	return nil
}

// handleFillingCompletedState 处理加油完成状态
// 🔧 协议合规性注意：当前实现基于主机端推测来设置喷嘴状态
// 理想情况：应该基于设备DC事务报告的精确状态，而非主机端业务逻辑推测
//
// 当设备加油完成时的处理：
// 1. 确定完成加油的喷嘴（通过交易组装器获取）
// 2. 将完成加油的喷嘴设置为completed状态（⚠️ 主机端推测）
// 3. 将其他喷嘴保持原状态或设置为idle状态（⚠️ 主机端推测）
func (p *DevicePoller) handleFillingCompletedState(ctx context.Context) error {
	p.logger.Info("处理加油完成状态开始",
		zap.String("device_id", p.config.DeviceInfo.ID))

	// 获取当前活跃的喷嘴（正在加油的喷嘴）
	activeNozzle := p.transactionAssembler.getCurrentActiveNozzle()
	if activeNozzle == nil {
		// 如果没有活跃喷嘴，尝试从最近的加油活动获取
		if lastNozzle := p.transactionAssembler.getLastActiveNozzle(); lastNozzle != nil {
			activeNozzle = lastNozzle
			p.logger.Info("从最近加油活动获取完成喷嘴",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("nozzle_id", *activeNozzle))
		} else {
			// 如果仍然没有找到，检查是否有filling状态的喷嘴
			if completedNozzle := p.findFillingNozzle(ctx); completedNozzle != nil {
				activeNozzle = completedNozzle
				p.logger.Info("从数据库中找到filling状态的喷嘴",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Uint8("nozzle_id", *activeNozzle))
			} else {
				p.logger.Warn("无法确定完成加油的喷嘴，跳过状态更新",
					zap.String("device_id", p.config.DeviceInfo.ID))
				return nil
			}
		}
	}

	// 🔧 修复：根据Wayne DART协议，喷嘴编号0表示"无喷嘴选中"状态，不应进行数据库操作
	if *activeNozzle == 0 {
		p.logger.Debug("加油完成状态获取到喷嘴编号0（无喷嘴选中状态），跳过状态更新",
			zap.String("device_id", p.config.DeviceInfo.ID))
		return nil
	}

	nozzleID, err := p.nozzleIDResolver.ResolveNozzleID(ctx, p.config.DeviceInfo.ID, *activeNozzle)
	if err != nil {
		return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", p.config.DeviceInfo.ID, *activeNozzle, err)
	}

	// 设置完成加油的喷嘴为completed状态
	if err := p.nozzleService.UpdateNozzleStatusDirect(ctx, p.config.DeviceInfo.ID, nozzleID, models.NozzleStatusCompleted); err != nil {
		p.logger.Error("设置喷嘴为completed状态失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return fmt.Errorf("设置喷嘴为completed状态失败: %w", err)
	}

	// 获取设备的所有喷嘴
	nozzles, err := p.nozzleService.GetDeviceNozzles(ctx, p.config.DeviceInfo.ID)
	if err != nil {
		p.logger.Error("获取设备喷嘴失败", zap.Error(err))
		return fmt.Errorf("获取设备喷嘴失败: %w", err)
	}

	// 将其他喷嘴设置为idle状态（如果它们不是completed状态）
	idleCount := 0
	for _, nozzle := range nozzles {
		if nozzle.Number != *activeNozzle && nozzle.IsEnabled {
			// 只有当喷嘴不是completed状态时才设置为idle
			if nozzle.Status != models.NozzleStatusCompleted {
				otherNozzleID, err := p.nozzleIDResolver.ResolveNozzleID(ctx, p.config.DeviceInfo.ID, nozzle.Number)
				if err != nil {
					return fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", p.config.DeviceInfo.ID, nozzle.Number, err)
				}
				if err := p.nozzleService.UpdateNozzleStatusDirect(ctx, p.config.DeviceInfo.ID, otherNozzleID, models.NozzleStatusIdle); err != nil {
					p.logger.Error("设置其他喷嘴为idle状态失败",
						zap.String("device_id", p.config.DeviceInfo.ID),
						zap.Uint8("nozzle_id", nozzle.Number),
						zap.Error(err))
				} else {
					idleCount++
				}
			}
		}
	}

	p.logger.Info("✅ 加油完成状态处理完成",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("completed_nozzle", *activeNozzle),
		zap.String("completed_status", "completed"),
		zap.Int("idle_nozzles_count", idleCount))

	return nil
}

// findFillingNozzle 从数据库中查找当前处于filling状态的喷嘴
func (p *DevicePoller) findFillingNozzle(ctx context.Context) *byte {
	nozzles, err := p.nozzleService.GetDeviceNozzles(ctx, p.config.DeviceInfo.ID)
	if err != nil {
		p.logger.Error("查找filling喷嘴时获取设备喷嘴失败", zap.Error(err))
		return nil
	}

	for _, nozzle := range nozzles {
		if nozzle.Status == models.NozzleStatusFilling {
			p.logger.Debug("找到filling状态的喷嘴",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("nozzle_id", nozzle.Number))
			return &nozzle.Number
		}
	}

	return nil
}

// shouldUseTxZero 判断是否应该使用TX#=0
// 根据Wayne DART协议标准，TX#=0用于特定的系统级命令
func (p *DevicePoller) shouldUseTxZero(cmd PollCommand) bool {
	// TX#=0的使用场景：
	// 1. 设备重启和初始化命令
	if cmd.BusinessType == "initialization" ||
		cmd.BusinessType == "device_restart" ||
		strings.Contains(cmd.BusinessType, "initialization") {
		p.logger.Info("[TransactionDCAssembler] 11使用TX#=0",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType))
		return true
	}

	// 2. 系统重置和恢复命令
	if strings.Contains(cmd.BusinessType, "_tx_reset") ||
		cmd.BusinessType == "recovery" ||
		strings.Contains(cmd.BusinessType, "recovery") {
		p.logger.Info("[TransactionDCAssembler] 22使用TX#=0",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType))
		return true
	}

	// 3. 看门狗超时恢复命令
	if strings.Contains(cmd.BusinessType, "watchdog") {
		p.logger.Info("[TransactionDCAssembler] 33使用TX#=0",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType))
		return true
	}

	// // 4. 检查命令数据是否包含复位指令
	// if data, ok := cmd.Data.([]byte); ok && len(data) >= 3 {
	// 	// 检查是否为CD1复位命令：[0x01, 0x01, 0x05]
	// 	if data[0] == 0x01 && data[1] == 0x01 && data[2] == 0x05 {
	// 		p.logger.Info("[TransactionDCAssembler] 44使用TX#=0",
	// 			zap.String("device_id", p.config.DeviceInfo.ID),
	// 			zap.String("business_type", cmd.BusinessType))
	// 		return true
	// 	}
	// }

	return false
}

// processDC7PumpParameters 处理DC7泵参数事务
// 根据Wayne DART协议规范，DC7包含泵的配置参数信息
func (p *DevicePoller) processDC7PumpParameters(transaction pump.DCTransaction) error {
	data := transaction.GetData()

	// DC7 数据格式：
	// LNG (1) + RES (22) + DPVOL (1) + DPAMO (1) + DPUNP (1) + RES (5) + MAMO (4) + RES (2) + GRADE (15)
	// 总共 50 字节 (不包括 TRANS 和 LNG)
	expectedLength := 50
	if len(data) < expectedLength {
		return fmt.Errorf("DC7事务数据长度不足: 期望 %d 字节, 实际 %d 字节", expectedLength, len(data))
	}

	p.logger.Info("收到DC7泵参数数据",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Int("data_length", len(data)),
		zap.String("data_hex", fmt.Sprintf("%x", data)))

	// 解析DC7数据结构
	offset := 0

	// 跳过前22个保留字节 (RES)
	offset += 22

	// DPVOL - 体积小数位数 (0-8)
	if offset >= len(data) {
		return fmt.Errorf("DC7数据解析失败: DPVOL字段越界")
	}
	volumeDecimals := data[offset]
	offset++

	// DPAMO - 金额小数位数 (0-8)
	if offset >= len(data) {
		return fmt.Errorf("DC7数据解析失败: DPAMO字段越界")
	}
	amountDecimals := data[offset]
	offset++

	// DPUNP - 单价小数位数 (0-4)
	if offset >= len(data) {
		return fmt.Errorf("DC7数据解析失败: DPUNP字段越界")
	}
	priceDecimals := data[offset]
	offset++

	// 跳过5个保留字节 (RES)
	offset += 5

	// MAMO - 最大金额 (4字节压缩BCD, MSB在第一个字节)
	if offset+4 > len(data) {
		return fmt.Errorf("DC7数据解析失败: MAMO字段越界")
	}
	maxAmountBCD := data[offset : offset+4]
	bcdConverter := pump.NewBCDConverter()
	maxAmount := bcdConverter.DecodeAmount(maxAmountBCD, 2) // 使用2位小数解码金额
	offset += 4

	// 跳过2个保留字节 (RES)
	offset += 2

	// GRADE - 每个喷嘴编号的现有等级 (15字节)
	if offset+15 > len(data) {
		return fmt.Errorf("DC7数据解析失败: GRADE字段越界")
	}
	gradeData := data[offset : offset+15]

	// 验证参数范围
	if volumeDecimals > 8 {
		p.logger.Warn("体积小数位数超出范围", zap.Uint8("volume_decimals", volumeDecimals))
	}
	if amountDecimals > 8 {
		p.logger.Warn("金额小数位数超出范围", zap.Uint8("amount_decimals", amountDecimals))
	}
	if priceDecimals > 4 {
		p.logger.Warn("单价小数位数超出范围", zap.Uint8("price_decimals", priceDecimals))
	}

	p.logger.Info("解析DC7泵参数成功",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("volume_decimals", volumeDecimals),
		zap.Uint8("amount_decimals", amountDecimals),
		zap.Uint8("price_decimals", priceDecimals),
		zap.Float64("max_amount", maxAmount),
		zap.String("grade_data_hex", fmt.Sprintf("%x", gradeData)))

	// 存储泵参数到设备配置中
	p.updatePumpParameters(volumeDecimals, amountDecimals, priceDecimals, maxAmount, gradeData)

	return nil
}

// updatePumpParameters 更新泵参数到设备配置
func (p *DevicePoller) updatePumpParameters(volumeDecimals, amountDecimals, priceDecimals uint8, maxAmount float64, gradeData []byte) {
	// 这里可以根据需要将参数存储到设备状态或配置中
	// 例如更新设备的精度配置，最大金额限制等

	p.logger.Info("更新泵参数配置",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("volume_decimals", volumeDecimals),
		zap.Uint8("amount_decimals", amountDecimals),
		zap.Uint8("price_decimals", priceDecimals),
		zap.Float64("max_amount", maxAmount))

	// TODO: 将参数保存到设备状态或数据库中
	// 可以考虑：
	// 1. 更新设备的小数位数配置
	// 2. 设置最大金额限制
	// 3. 解析和存储每个喷嘴的等级信息
}

// processDC14SuspendReply 处理DC14暂停回复事务
func (p *DevicePoller) processDC14SuspendReply(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 1 {
		return fmt.Errorf("DC14事务数据过短")
	}

	// DC14通常包含暂停操作的确认信息
	result := data[0]
	success := result == 0x00 // 0x00表示成功

	p.logger.Info("收到暂停操作回复",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("result_code", result),
		zap.Bool("success", success))

	if !success {
		p.logger.Warn("暂停操作失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("error_code", result))
	}

	return nil
}

// processDC15ResumeReply 处理DC15恢复回复事务
func (p *DevicePoller) processDC15ResumeReply(transaction pump.DCTransaction) error {
	data := transaction.GetData()
	if len(data) < 1 {
		return fmt.Errorf("DC15事务数据过短")
	}

	// DC15通常包含恢复操作的确认信息
	result := data[0]
	success := result == 0x00 // 0x00表示成功

	p.logger.Info("收到恢复操作回复",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("result_code", result),
		zap.Bool("success", success))

	if !success {
		p.logger.Warn("恢复操作失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("error_code", result))
	}

	return nil
}

// 🆕 预授权执行流程
func (p *DevicePoller) executePreAuthFlow(ctx context.Context, nozzleID string, price decimal.Decimal, preauth *dto.PreAuthRequest, timestamp time.Time) error {
	// 预授权已经在ConsumePreAuth中被消费了，无需再次删除

	// 获取TransactionAssembler实例
	ta := p.GetTransactionAssembler()

	// 🎯 根据 AuthType 执行不同的预授权命令序列
	if err := p.executePreAuthCommands(ctx, *ta.currentActiveNozzle, preauth); err != nil {
		p.logger.Error("[executePreAuthFlow] 预授权命令执行失败",
			zap.String("device_id", ta.deviceID),
			zap.Uint8("nozzle_number", *ta.currentActiveNozzle),
			zap.String("auth_type", preauth.AuthType),
			zap.Error(err))
		// 继续执行，不中断流程
	}

	// 调用现有生命周期服务
	_, err := ta.lifecycleService.HandleNozzleOut(ctx, ta.deviceID, nozzleID, price, timestamp)
	if err != nil {
		p.logger.Error("预授权模式下HandleNozzleOut失败",
			zap.String("device_id", ta.deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("auth_type", preauth.AuthType),
			zap.Error(err))
		return fmt.Errorf("预授权模式下处理喷嘴拔出失败: %w", err)
	}

	p.logger.Info("预授权流程执行完成",
		zap.String("device_id", ta.deviceID),
		zap.Uint8("nozzle_number", *ta.currentActiveNozzle),
		zap.String("auth_type", preauth.AuthType),
		zap.String("preset_volume", preauth.Volume.String()))

	return nil
}

// 🆕 自动授权执行方法
func (p *DevicePoller) executeAutoAuth(ctx context.Context, nozzleNumber byte, nozzleID string) error {
	// 获取TransactionAssembler实例
	ta := p.GetTransactionAssembler()

	p.logger.Info("开始执行自动授权",
		zap.String("device_id", ta.deviceID),
		zap.Uint8("nozzle_number", nozzleNumber))

	// 1. 发送CD2配置喷嘴
	if err := p.ConfigureNozzles([]byte{nozzleNumber}); err != nil {
		return fmt.Errorf("CD2配置失败: %w", err)
	}

	// 2. 短暂延时确保CD2生效
	time.Sleep(50 * time.Millisecond)

	// 3. 发送CD1授权命令
	if err := p.AuthorizeDevice("", nozzleNumber); err != nil {
		return fmt.Errorf("CD1授权失败: %w", err)
	}

	p.logger.Info("自动授权执行完成",
		zap.String("device_id", ta.deviceID),
		zap.Uint8("nozzle_number", nozzleNumber))

	return nil
}

// 🆕 预授权命令执行 - 支持 preset_volume 和 preset_amount 两种类型
func (p *DevicePoller) executePreAuthCommands(ctx context.Context, nozzleNumber byte, preauth *dto.PreAuthRequest) error {

	// 先重置
	p.ResetDevice()

	// 1. 发送CD2配置喷嘴
	if err := p.ConfigureNozzles([]byte{nozzleNumber}); err != nil {
		return fmt.Errorf("预授权CD2配置失败: %w", err)
	}

	// 2. 根据 AuthType 发送不同的预设命令
	switch preauth.AuthType {
	case "preset_volume":
		// 发送CD3预设体积
		if err := p.PresetVolume(preauth.Volume, preauth.EmployeeID); err != nil {
			return fmt.Errorf("预授权CD3预设体积失败: %w", err)
		}
		p.logger.Info("[executePreAuthFlow] 预授权CD3预设体积命令已发送",
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("volume", preauth.Volume.String()),
			zap.String("employee_id", preauth.EmployeeID))

	case "preset_amount":
		// 发送CD4预设金额
		if preauth.Amount == nil {
			return fmt.Errorf("预授权类型为 preset_amount 但 Amount 字段为空")
		}
		if err := p.PresetAmount(*preauth.Amount, preauth.EmployeeID); err != nil {
			return fmt.Errorf("预授权CD4预设金额失败: %w", err)
		}
		p.logger.Info("[executePreAuthFlow] 预授权CD4预设金额命令已发送",
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("amount", preauth.Amount.String()),
			zap.String("employee_id", preauth.EmployeeID))
	case "preset_full":
		// 发送CD3预设体积
		if err := p.PresetVolume(decimal.NewFromFloat(999.99), preauth.EmployeeID); err != nil {
			return fmt.Errorf("预授权CD3-Full-预设体积失败: %w", err)
		}
		p.logger.Info("[executePreAuthFlow] 预授权CD3-Full-预设体积命令已发送",
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.String("volume", "999.99"),
			zap.String("employee_id", preauth.EmployeeID))

	default:
		return fmt.Errorf("不支持的预授权类型: %s", preauth.AuthType)
	}

	// 3. 发送CD1授权
	if err := p.AuthorizeDevice(preauth.EmployeeID, nozzleNumber); err != nil {
		return fmt.Errorf("预授权CD1授权失败: %w", err)
	}

	p.logger.Info("[executePreAuthFlow] 预授权命令序列执行完成",
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("auth_type", preauth.AuthType),
		zap.String("employee_id", preauth.EmployeeID))

	return nil
}
