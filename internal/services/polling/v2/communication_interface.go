package v2

import (
	"context"
	"time"

	"fcc-service/internal/adapters/wayne/dartline"
)

// CommunicationInterface 通信接口抽象
// DevicePoller 通过此接口与设备通信，不需要关心底层连接管理
type CommunicationInterface interface {
	// SendFrameAndWait 发送帧并等待响应
	SendFrameAndWait(ctx context.Context, data []byte, timeout time.Duration) (*dartline.Frame, error)

	// GetDeviceAddress 获取设备地址
	GetDeviceAddress() byte

	// IsConnected 检查连接状态
	IsConnected() bool

	// GetConnectionInfo 获取连接信息
	GetConnectionInfo() ConnectionInfo

	Connect(ctx context.Context) error
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	DeviceID    string     `json:"device_id"`
	Address     byte       `json:"address"`
	Port        string     `json:"port"`
	IsConnected bool       `json:"is_connected"`
	ConnectedAt *time.Time `json:"connected_at,omitempty"`
}

// CommunicationManager 通信管理器接口
// 负责管理多个设备的通信接口
type CommunicationManager interface {
	// CreateCommunication 为指定设备创建通信接口
	CreateCommunication(deviceID string, address byte) (CommunicationInterface, error)

	// ReleaseCommunication 释放设备通信接口
	ReleaseCommunication(deviceID string) error

	// GetActiveCommunications 获取所有活跃的通信接口
	GetActiveCommunications() map[string]CommunicationInterface

	// Shutdown 关闭所有通信
	Shutdown() error
}
