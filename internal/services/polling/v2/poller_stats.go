package v2

import (
	"sync"
	"time"
)

// PollerStats 轮询器统计信息
type PollerStats struct {
	StartTime       time.Time     `json:"start_time"`       // 启动时间
	TotalPolls      int64         `json:"total_polls"`      // 总轮询次数
	SuccessfulPolls int64         `json:"successful_polls"` // 成功轮询次数
	FailedPolls     int64         `json:"failed_polls"`     // 失败轮询次数
	TimeoutPolls    int64         `json:"timeout_polls"`    // 超时轮询次数
	AverageLatency  time.Duration `json:"average_latency"`  // 平均延迟
	LastPollTime    time.Time     `json:"last_poll_time"`   // 最后轮询时间
	mu              sync.RWMutex  `json:"-"`                // 并发保护
}

// StatsCollector 统计信息收集器
type StatsCollector struct {
	stats PollerStats
}

// NewStatsCollector 创建统计信息收集器
func NewStatsCollector() *StatsCollector {
	return &StatsCollector{
		stats: PollerStats{
			StartTime: time.Now(),
		},
	}
}

// UpdateStats 更新统计信息
func (sc *StatsCollector) UpdateStats(result PollResult) {
	sc.stats.mu.Lock()
	defer sc.stats.mu.Unlock()

	sc.stats.TotalPolls++
	sc.stats.LastPollTime = result.Timestamp

	if result.Success {
		sc.stats.SuccessfulPolls++
	} else {
		sc.stats.FailedPolls++

		// 检查是否是超时错误
		if result.Error != nil && result.ResponseTime >= 25*time.Millisecond {
			sc.stats.TimeoutPolls++
		}
	}

	// 更新平均延迟
	if sc.stats.TotalPolls > 0 {
		oldAvg := sc.stats.AverageLatency
		sc.stats.AverageLatency = time.Duration(
			(int64(oldAvg)*(sc.stats.TotalPolls-1) + int64(result.ResponseTime)) / sc.stats.TotalPolls)
	}
}

// GetStats 获取统计信息副本
func (sc *StatsCollector) GetStats() PollerStats {
	sc.stats.mu.RLock()
	defer sc.stats.mu.RUnlock()

	// 创建不包含锁的副本
	return PollerStats{
		StartTime:       sc.stats.StartTime,
		TotalPolls:      sc.stats.TotalPolls,
		SuccessfulPolls: sc.stats.SuccessfulPolls,
		FailedPolls:     sc.stats.FailedPolls,
		TimeoutPolls:    sc.stats.TimeoutPolls,
		AverageLatency:  sc.stats.AverageLatency,
		LastPollTime:    sc.stats.LastPollTime,
		// 注意：不包含 mu 字段
	}
}

// Reset 重置统计信息
func (sc *StatsCollector) Reset() {
	sc.stats.mu.Lock()
	defer sc.stats.mu.Unlock()

	// 只重置数据字段，保留mutex
	sc.stats.StartTime = time.Now()
	sc.stats.TotalPolls = 0
	sc.stats.SuccessfulPolls = 0
	sc.stats.FailedPolls = 0
	sc.stats.TimeoutPolls = 0
	sc.stats.AverageLatency = 0
	sc.stats.LastPollTime = time.Time{}
}

// GetSuccessRate 获取成功率
func (sc *StatsCollector) GetSuccessRate() float64 {
	sc.stats.mu.RLock()
	defer sc.stats.mu.RUnlock()

	if sc.stats.TotalPolls == 0 {
		return 0.0
	}
	return float64(sc.stats.SuccessfulPolls) / float64(sc.stats.TotalPolls) * 100.0
}

// GetTimeoutRate 获取超时率
func (sc *StatsCollector) GetTimeoutRate() float64 {
	sc.stats.mu.RLock()
	defer sc.stats.mu.RUnlock()

	if sc.stats.TotalPolls == 0 {
		return 0.0
	}
	return float64(sc.stats.TimeoutPolls) / float64(sc.stats.TotalPolls) * 100.0
}

// GetUptime 获取运行时间
func (sc *StatsCollector) GetUptime() time.Duration {
	sc.stats.mu.RLock()
	defer sc.stats.mu.RUnlock()
	return time.Since(sc.stats.StartTime)
}

// StatsSnapshot 统计信息快照
type StatsSnapshot struct {
	DeviceID        string        `json:"device_id"`
	StartTime       time.Time     `json:"start_time"`
	Uptime          time.Duration `json:"uptime"`
	TotalPolls      int64         `json:"total_polls"`
	SuccessfulPolls int64         `json:"successful_polls"`
	FailedPolls     int64         `json:"failed_polls"`
	TimeoutPolls    int64         `json:"timeout_polls"`
	SuccessRate     float64       `json:"success_rate"`
	TimeoutRate     float64       `json:"timeout_rate"`
	AverageLatency  time.Duration `json:"average_latency"`
	LastPollTime    time.Time     `json:"last_poll_time"`
}

// CreateSnapshot 创建统计快照
func (sc *StatsCollector) CreateSnapshot(deviceID string) StatsSnapshot {
	stats := sc.GetStats()
	return StatsSnapshot{
		DeviceID:        deviceID,
		StartTime:       stats.StartTime,
		Uptime:          sc.GetUptime(),
		TotalPolls:      stats.TotalPolls,
		SuccessfulPolls: stats.SuccessfulPolls,
		FailedPolls:     stats.FailedPolls,
		TimeoutPolls:    stats.TimeoutPolls,
		SuccessRate:     sc.GetSuccessRate(),
		TimeoutRate:     sc.GetTimeoutRate(),
		AverageLatency:  stats.AverageLatency,
		LastPollTime:    stats.LastPollTime,
	}
}
