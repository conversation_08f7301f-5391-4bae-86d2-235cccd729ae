package v2

import (
	"context"
	"fmt"
	"io"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"fcc-service/internal/adapters/wayne/connection"
	"fcc-service/internal/adapters/wayne/dartline"

	"go.uber.org/zap"
)

// 性能优化相关常量
const (
	DefaultReadBufferSize   = 4096 // 增大读取缓冲区
	DefaultFrameChannelSize = 32   // 增大帧通道容量
	MaxBufferSize           = 8192 // 最大缓冲区大小
	BufferShrinkThreshold   = 2048 // 缓冲区收缩阈值
)

// 内存池优化
var (
	// 缓冲区对象池
	bufferPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, DefaultReadBufferSize)
		},
	}

	// 帧对象池
	framePool = sync.Pool{
		New: func() interface{} {
			return make([]*dartline.Frame, 0, 8)
		},
	}
)

// TransportLogFormatter 传输层日志格式化器（优化版）
type TransportLogFormatter struct {
	deviceID  string
	layer     string
	txCounter int64 // 使用 atomic 替代 mutex
	rxCounter int64
}

func NewTransportLogFormatter(deviceID string) *TransportLogFormatter {
	return &TransportLogFormatter{
		deviceID: deviceID,
		layer:    "transport",
	}
}

func (tlf *TransportLogFormatter) FormatTransportTx(data []byte, port string, address byte, timeout time.Duration) []zap.Field {
	seq := atomic.AddInt64(&tlf.txCounter, 1)

	return []zap.Field{
		zap.String("layer", "传输层"),
		zap.String("direction", "TX↑"),
		zap.String("device_id", tlf.deviceID),
		zap.String("port", port),
		zap.Uint8("address", address),
		zap.Int64("seq", seq),
		zap.Int("bytes", len(data)),
		zap.String("hex", formatHexWithSpacesOptimized(data, 16)),
		zap.Duration("timeout", timeout),
		zap.String("timestamp", time.Now().Format("15:04:05.000")),
	}
}

func (tlf *TransportLogFormatter) FormatTransportRx(data []byte, port string, address byte, responseTime time.Duration) []zap.Field {
	seq := atomic.AddInt64(&tlf.rxCounter, 1)

	return []zap.Field{
		zap.String("layer", "传输层"),
		zap.String("direction", "↓RX"),
		zap.String("device_id", tlf.deviceID),
		zap.String("port", port),
		zap.Uint8("address", address),
		zap.Int64("seq", seq),
		zap.Int("bytes", len(data)),
		zap.String("hex", formatHexWithSpacesOptimized(data, 16)),
		zap.Duration("response_time", responseTime),
		zap.String("timestamp", time.Now().Format("15:04:05.000")),
	}
}

// 优化的十六进制格式化 - 使用 strings.Builder 预分配容量
func formatHexWithSpacesOptimized(data []byte, maxBytes int) string {
	if len(data) == 0 {
		return ""
	}

	displayData := data
	truncated := false
	if len(data) > maxBytes {
		displayData = data[:maxBytes]
		truncated = true
	}

	// 预分配容量，减少内存分配
	capacity := len(displayData)*2 + len(displayData)/4 + 10
	var result strings.Builder
	result.Grow(capacity)

	for i, b := range displayData {
		if i > 0 && i%4 == 0 {
			result.WriteByte(' ')
		}
		// 使用更高效的十六进制转换
		result.WriteByte(hexChars[b>>4])
		result.WriteByte(hexChars[b&0x0F])
	}

	if truncated {
		result.WriteString("...")
		result.WriteString(fmt.Sprintf("[+%d]", len(data)-maxBytes))
	}

	return result.String()
}

// 预定义十六进制字符表，避免 fmt.Sprintf
var hexChars = "0123456789ABCDEF"

// SerialTransport 串口传输实现 - 高性能优化版
type SerialTransport struct {
	deviceID string
	address  byte
	port     string
	logger   *zap.Logger

	serialManager connection.SerialManagerInterface

	// 优化的无锁数据传输
	pipeReader *io.PipeReader
	pipeWriter *io.PipeWriter

	// Frame 组装 goroutine 管理
	assemblerCtx    context.Context
	assemblerCancel context.CancelFunc
	assemblerWg     sync.WaitGroup

	// 增大帧通道容量，减少阻塞
	frameReady chan *dartline.Frame

	// 原子状态管理
	connected int32 // 0: disconnected, 1: connected

	// 传输层日志格式化器
	transportLogFormatter *TransportLogFormatter

	// 性能统计（可选）
	stats struct {
		bytesReceived   int64
		framesProcessed int64
		frameDropped    int64
		bufferOverflows int64
	}
}

func NewSerialTransport(deviceID string, address byte, port string, logger *zap.Logger) (*SerialTransport, error) {
	pipeReader, pipeWriter := io.Pipe()
	assemblerCtx, assemblerCancel := context.WithCancel(context.Background())

	// 优化的串口配置
	config := connection.SerialConfig{
		Port:                 port,
		BaudRate:             9600,
		DataBits:             8,
		StopBits:             1,
		Parity:               connection.ParityOdd,
		Timeout:              25 * time.Millisecond,
		ReadTimeout:          50 * time.Millisecond,
		WriteTimeout:         50 * time.Millisecond,
		MaxRetries:           3,
		RetryInterval:        10 * time.Millisecond,
		ReadBufferSize:       DefaultReadBufferSize, // 增大缓冲区
		WriteBufferSize:      DefaultReadBufferSize,
		EnableRawDataLogging: true,
	}

	serialManager, err := connection.NewSerialManager(config)
	if err != nil {
		pipeWriter.Close()
		pipeReader.Close()
		assemblerCancel()
		return nil, fmt.Errorf("failed to create serial manager: %w", err)
	}

	transport := &SerialTransport{
		deviceID:              deviceID,
		address:               address,
		port:                  port,
		logger:                logger,
		serialManager:         serialManager,
		pipeReader:            pipeReader,
		pipeWriter:            pipeWriter,
		assemblerCtx:          assemblerCtx,
		assemblerCancel:       assemblerCancel,
		frameReady:            make(chan *dartline.Frame, DefaultFrameChannelSize),
		transportLogFormatter: NewTransportLogFormatter(deviceID),
	}

	serialManager.SetLogger(logger)

	// 启动帧组装器
	transport.assemblerWg.Add(1)
	go transport.frameAssemblerRoutineOptimized()

	serialManager.SetDataReceivedCallback(transport.onDataReceivedOptimized)

	return transport, nil
}

func (st *SerialTransport) Connect(ctx context.Context) error {
	if atomic.LoadInt32(&st.connected) == 1 {
		return nil
	}

	err := st.serialManager.Connect(ctx)
	if err != nil {
		return fmt.Errorf("failed to connect serial: %w", err)
	}

	atomic.StoreInt32(&st.connected, 1)
	st.logger.Info("Serial transport connected",
		zap.String("device_id", st.deviceID),
		zap.String("port", st.port),
		zap.Uint8("address", st.address))

	return nil
}

func (st *SerialTransport) IsConnected() bool {
	return atomic.LoadInt32(&st.connected) == 1 && st.serialManager.IsConnected()
}

func (st *SerialTransport) Disconnect() error {
	if atomic.LoadInt32(&st.connected) == 0 {
		return nil
	}

	st.logger.Info("Disconnecting SerialTransport",
		zap.String("device_id", st.deviceID),
		zap.String("port", st.port))

	err := st.serialManager.Disconnect(context.Background())
	if err != nil {
		st.logger.Warn("Failed to disconnect serial", zap.Error(err))
	}

	st.shutdownFrameAssembler()
	atomic.StoreInt32(&st.connected, 0)

	st.logger.Info("SerialTransport disconnected",
		zap.String("device_id", st.deviceID),
		zap.String("port", st.port))

	return err
}

// 优化的数据接收回调 - 减少内存分配
func (st *SerialTransport) onDataReceivedOptimized(data []byte) {
	defer connection.PutPooledBytes(data)

	if len(data) == 0 {
		return
	}

	// 更新统计
	atomic.AddInt64(&st.stats.bytesReceived, int64(len(data)))

	// 快速写入管道
	if _, err := st.pipeWriter.Write(data); err != nil {
		st.logger.Error("Failed to write to internal pipe",
			zap.String("device_id", st.deviceID),
			zap.String("port", st.port),
			zap.Int("data_bytes", len(data)),
			zap.Error(err))
		return
	}
}

// 优化的帧组装器 - 使用对象池和批量处理
func (st *SerialTransport) frameAssemblerRoutineOptimized() {
	defer st.assemblerWg.Done()
	defer st.pipeReader.Close()

	// 从对象池获取缓冲区
	buffer := bufferPool.Get().([]byte)
	buffer = buffer[:0] // 重置长度但保持容量
	defer bufferPool.Put(buffer)

	readBuf := make([]byte, DefaultReadBufferSize)
	lastShrink := time.Now()

	for {
		select {
		case <-st.assemblerCtx.Done():
			return
		default:
		}

		n, err := st.pipeReader.Read(readBuf)
		if err != nil {
			if err == io.EOF {
				return
			}
			st.logger.Error("Error reading from pipe",
				zap.String("device_id", st.deviceID),
				zap.Error(err))
			return
		}

		if n > 0 {
			buffer = append(buffer, readBuf[:n]...)

			// 缓冲区大小管理
			if len(buffer) > MaxBufferSize {
				atomic.AddInt64(&st.stats.bufferOverflows, 1)
				// 保留最后一部分数据，防止丢失有效帧
				keepSize := MaxBufferSize / 2
				copy(buffer[:keepSize], buffer[len(buffer)-keepSize:])
				buffer = buffer[:keepSize]
			}

			// 批量提取帧
			frames := st.extractFramesOptimized(&buffer)

			// 批量发送帧
			for _, frame := range frames {
				select {
				case st.frameReady <- frame:
					atomic.AddInt64(&st.stats.framesProcessed, 1)
				case <-st.assemblerCtx.Done():
					return
				default:
					atomic.AddInt64(&st.stats.frameDropped, 1)
					st.logger.Warn("Frame ready channel full, dropping frame",
						zap.String("device_id", st.deviceID),
						zap.String("frame_type", st.getFrameTypeName(frame)))
				}
			}

			// 定期收缩缓冲区，释放内存
			if time.Since(lastShrink) > 30*time.Second &&
				len(buffer) < BufferShrinkThreshold &&
				cap(buffer) > DefaultReadBufferSize*2 {
				newBuffer := make([]byte, len(buffer), DefaultReadBufferSize)
				copy(newBuffer, buffer)
				buffer = newBuffer
				lastShrink = time.Now()
			}
		}
	}
}

// 优化的帧提取 - 使用对象池和改进的查找算法
func (st *SerialTransport) extractFramesOptimized(buffer *[]byte) []*dartline.Frame {
	frames := framePool.Get().([]*dartline.Frame)
	frames = frames[:0] // 重置长度
	defer framePool.Put(frames)

	for len(*buffer) >= 3 {
		candidateAddr := (*buffer)[0]
		if candidateAddr < 0x50 || candidateAddr > 0x6F {
			// 使用更高效的搜索
			nextValid := st.findNextValidAddress(*buffer, 1)
			if nextValid == -1 {
				*buffer = (*buffer)[:0]
				break
			}
			*buffer = (*buffer)[nextValid:]
			continue
		}

		sfIndex := st.findStopFlagOptimized(*buffer, 2)
		if sfIndex == -1 {
			if len(*buffer) > MaxBufferSize {
				keepSize := 256
				if len(*buffer) > keepSize {
					*buffer = (*buffer)[len(*buffer)-keepSize:]
				}
			}
			break
		}

		frameLength := sfIndex + 1
		frameData := make([]byte, frameLength)
		copy(frameData, (*buffer)[0:frameLength])
		*buffer = (*buffer)[frameLength:]

		if st.isValidFrameOptimized(frameData) {
			frame, err := dartline.DecodeFrame(frameData)
			if err != nil {
				continue
			}
			frames = append(frames, frame)
		}
	}

	// 返回拷贝，避免切片引用问题
	result := make([]*dartline.Frame, len(frames))
	copy(result, frames)
	return result
}

// 优化的地址查找
func (st *SerialTransport) findNextValidAddress(buffer []byte, start int) int {
	for i := start; i < len(buffer)-2; i++ {
		if buffer[i] >= 0x50 && buffer[i] <= 0x6F {
			return i
		}
	}
	return -1
}

// 优化的停止标志查找 - 减少比较次数
func (st *SerialTransport) findStopFlagOptimized(buffer []byte, startPos int) int {
	for i := startPos; i < len(buffer); i++ {
		if buffer[i] == 0xFA {
			// 检查前一个字节是否为DLE
			if i > 0 && buffer[i-1] == 0x10 {
				continue
			}
			return i
		}
	}
	return -1
}

// 优化的帧有效性检查 - 使用位运算优化
func (st *SerialTransport) isValidFrameOptimized(frameData []byte) bool {
	if len(frameData) < 3 {
		return false
	}

	address := frameData[0]
	if address < 0x50 || address > 0x6F {
		return false
	}

	command := frameData[1]
	commandType := command & 0xF0

	// 使用位运算快速检查
	switch commandType {
	case 0x20, 0x30, 0x50, 0x70, 0xC0, 0xE0:
		return true
	default:
		return false
	}
}

// SendFrameAndWait 保持原有接口，内部优化
func (st *SerialTransport) SendFrameAndWait(ctx context.Context, data []byte, timeout time.Duration) (*dartline.Frame, error) {
	if !st.IsConnected() {
		return nil, fmt.Errorf("not connected")
	}

	logFields := st.transportLogFormatter.FormatTransportTx(data, st.port, st.address, timeout)
	logFields = append(logFields, zap.String("method", "SendFrameAndWait"))
	st.logger.Debug("[SerialTransport]传输层数据发送", logFields...)

	startTime := time.Now()

	// 快速清空通道
	st.drainFrameChannel()

	err := st.serialManager.SendFrame(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("failed to send frame: %w", err)
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 3*timeout)
	defer cancel()

	select {
	case frame := <-st.frameReady:
		elapsed := time.Since(startTime)
		st.logger.Debug("[SerialTransport]传输层Frame接收",
			zap.String("layer", "传输层"),
			zap.String("direction", "↓RX✓"),
			zap.String("device_id", st.deviceID),
			zap.String("port", st.port),
			zap.Uint8("address", frame.Address),
			zap.String("frame_type", st.getFrameTypeName(frame)),
			zap.Uint8("tx_num", frame.GetTxNumber()),
			zap.Duration("response_time", elapsed),
			zap.String("result", "success"),
			zap.String("timestamp", time.Now().Format("15:04:05.000")))
		return frame, nil
	case <-timeoutCtx.Done():
		elapsed := time.Since(startTime)
		st.logger.Warn("[SerialTransport]传输层Frame超时",
			zap.String("layer", "传输层"),
			zap.String("direction", "RX✗"),
			zap.String("device_id", st.deviceID),
			zap.String("port", st.port),
			zap.Uint8("address", st.address),
			zap.Duration("timeout_configured", timeout),
			zap.Duration("elapsed", elapsed),
			zap.String("result", "timeout"),
			zap.String("timestamp", time.Now().Format("15:04:05.000")))
		return nil, fmt.Errorf("timeout waiting for response after %v (configured: %v)", elapsed, timeout)
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// 快速清空帧通道
func (st *SerialTransport) drainFrameChannel() {
	for {
		select {
		case <-st.frameReady:
		default:
			return
		}
	}
}

// 获取性能统计
func (st *SerialTransport) GetStats() map[string]int64 {
	return map[string]int64{
		"bytes_received":   atomic.LoadInt64(&st.stats.bytesReceived),
		"frames_processed": atomic.LoadInt64(&st.stats.framesProcessed),
		"frames_dropped":   atomic.LoadInt64(&st.stats.frameDropped),
		"buffer_overflows": atomic.LoadInt64(&st.stats.bufferOverflows),
	}
}

// 其余方法保持不变...
func (st *SerialTransport) shutdownFrameAssembler() {
	if st.assemblerCancel != nil {
		st.assemblerCancel()
	}

	if st.pipeWriter != nil {
		if err := st.pipeWriter.Close(); err != nil {
			st.logger.Warn("Failed to close pipe writer", zap.Error(err))
		}
	}

	st.assemblerWg.Wait()
}

func (st *SerialTransport) Close() error {
	st.logger.Info("Closing SerialTransport",
		zap.String("device_id", st.deviceID),
		zap.String("port", st.port))

	disconnectErr := st.Disconnect()
	closeErr := st.serialManager.Close()

	st.logger.Info("SerialTransport closed",
		zap.String("device_id", st.deviceID),
		zap.String("port", st.port))

	if disconnectErr != nil {
		return disconnectErr
	}
	return closeErr
}

func (st *SerialTransport) getFrameTypeName(frame *dartline.Frame) string {
	switch {
	case frame.IsPollFrame():
		return "POLL"
	case frame.IsDataFrame():
		return "DATA"
	case frame.IsAckFrame():
		return "ACK"
	case frame.IsNakFrame():
		return "NAK"
	case frame.IsEotFrame():
		return "EOT"
	default:
		return "UNKNOWN"
	}
}

func (st *SerialTransport) GetConnectionInfo() ConnectionInfo {
	return ConnectionInfo{
		DeviceID:    st.deviceID,
		Address:     st.address,
		Port:        st.port,
		IsConnected: st.IsConnected(),
	}
}

func (st *SerialTransport) GetDeviceAddress() byte {
	return st.address
}
