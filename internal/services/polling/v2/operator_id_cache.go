package v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/config"
	"fcc-service/internal/server/dto"
)

const authorizationTimeout = 60 * time.Second

// TimeoutCallback 超时回调函数类型
type TimeoutCallback func(operatorID string)

// OperatorIDCache 用于在 Authorize/Preset 和交易创建之间安全地传递员工ID
// 采用类似 PumpReadingCache 的设计模式，提供线程安全的缓存机制

// OperatorIDCacheInterface 操作员ID缓存接口
type OperatorIDCacheInterface interface {
	// 基础操作员ID缓存功能
	Consume() string
	Set(operatorID string)
	HasPendingOperator() bool
	GetStatus() map[string]interface{}
	CheckTimeout() (bool, string)
	IsTimedOut() bool

	// 预授权功能
	IsPreAuthEnabled() bool
	SetPreAuth(ctx context.Context, nozzleID string, preauth *dto.PreAuthRequest) error
	GetPreAuth(ctx context.Context, nozzleID string) *dto.PreAuthRequest
	ConsumePreAuth(ctx context.Context, nozzleID string) *dto.PreAuthRequest
	GetPreAuthTTL() time.Duration

	// 自动授权功能
	IsAutoAuthEnabled() bool
}

type OperatorIDCache struct {
	// 原有员工ID缓存字段
	pendingOperatorID  string          // 待处理的员工ID
	authorizationTimer *time.Timer     // 授权超时计时器
	setTime            time.Time       // 🔧 优化：授权时间记录
	authorizationTime  time.Time       // 🆕 新增：明确的授权时间记录
	logger             *zap.Logger     // 日志记录器
	mu                 sync.Mutex      // 互斥锁保护并发访问
	timeoutCallback    TimeoutCallback // 超时回调函数
	isTimedOut         bool            // 🆕 新增：超时状态标记

	// 🆕 预授权相关字段（简化设计）
	lastPreAuth         *dto.PreAuthRequest // 最近一次预授权数据
	lastPreAuthTime     time.Time           // 最近一次预授权时间
	lastPreAuthNozzleID string              // 最近一次预授权喷嘴ID

	// 🆕 配置字段
	preAuthConf  *config.PreAuthConfig  // 预授权配置
	autoAuthConf *config.AutoAuthConfig // 自动授权配置
}

// NewOperatorIDCache 创建一个新的操作员ID缓存实例
func NewOperatorIDCache(logger *zap.Logger, preAuthConf *config.PreAuthConfig, autoAuthConf *config.AutoAuthConfig) *OperatorIDCache {
	return &OperatorIDCache{
		logger:       logger,
		preAuthConf:  preAuthConf,
		autoAuthConf: autoAuthConf,
	}
}

// SetTimeoutCallback 设置超时回调函数
// 当缓存的员工ID超时时，会调用此回调函数通知上层应用
func (c *OperatorIDCache) SetTimeoutCallback(callback TimeoutCallback) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.timeoutCallback = callback
}

// Set 设置待处理的员工ID，并启动超时计时器
// 如果之前有未消费的ID，会被新的ID覆盖
func (c *OperatorIDCache) Set(operatorID string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果存在旧的计时器，先停止
	if c.authorizationTimer != nil {
		c.authorizationTimer.Stop()
	}

	// 🔧 优化：记录授权时间
	now := time.Now()
	c.pendingOperatorID = operatorID
	c.setTime = now
	c.authorizationTime = now // 🆕 明确记录授权时间
	c.isTimedOut = false      // 🆕 重置超时状态

	c.logger.Info("Employee ID cached with authorization time",
		zap.String("operator_id", operatorID),
		zap.Time("authorization_time", c.authorizationTime),
		zap.Duration("timeout_duration", authorizationTimeout))

	// 启动新的超时计时器
	c.authorizationTimer = time.AfterFunc(authorizationTimeout, func() {
		c.handleTimeout()
	})
}

// 🆕 新增：CheckTimeout 主动检查超时状态
// 返回 (是否超时, 超时的员工ID)
// 这个方法可以被 DevicePoller 定期调用来检查超时状态
func (c *OperatorIDCache) CheckTimeout() (bool, string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果没有待处理的员工ID，返回false
	if c.pendingOperatorID == "" {
		return false, ""
	}

	// 检查是否已经超时
	if c.isTimedOut {
		// 已经超时，返回超时的员工ID并清除缓存
		operatorID := c.pendingOperatorID
		c.logger.Info("Timeout detected by active check, consuming expired operator ID",
			zap.String("operator_id", operatorID),
			zap.Time("authorization_time", c.authorizationTime),
			zap.Duration("elapsed_time", time.Since(c.authorizationTime)))

		// 清除缓存
		c.clearInternal("active_check")
		return true, operatorID
	}

	// 检查是否即将超时（还未到时间但可以主动检查）
	elapsed := time.Since(c.authorizationTime)
	if elapsed >= authorizationTimeout {
		// 手动触发超时
		operatorID := c.pendingOperatorID
		c.logger.Info("Manual timeout triggered by active check",
			zap.String("operator_id", operatorID),
			zap.Time("authorization_time", c.authorizationTime),
			zap.Duration("elapsed_time", elapsed))

		c.isTimedOut = true
		c.clearInternal("manual_timeout")
		return true, operatorID
	}

	return false, ""
}

// 🆕 新增：IsTimedOut 检查是否已超时
func (c *OperatorIDCache) IsTimedOut() bool {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.isTimedOut
}

// Consume 获取并清除待处理的员工ID
// 这是"一次性读取"操作，读取后立即清空缓存和停止计时器
// 返回空字符串表示没有待处理的员工ID
func (c *OperatorIDCache) Consume() string {
	c.mu.Lock()
	defer c.mu.Unlock()

	operatorID := c.pendingOperatorID
	if operatorID != "" {
		c.logger.Info("Employee ID consumed from cache",
			zap.String("operator_id", operatorID),
			zap.Time("authorization_time", c.authorizationTime),
			zap.Duration("cache_duration", time.Since(c.authorizationTime)))

		// 消费后立即清空
		c.clearInternal("consumed")
	}
	return operatorID
}

// 🔧 优化：handleTimeout 处理超时事件
func (c *OperatorIDCache) handleTimeout() {
	c.mu.Lock()
	operatorID := c.pendingOperatorID
	callback := c.timeoutCallback

	// 检查是否已经被主动检查处理过
	if operatorID != "" && !c.isTimedOut {
		c.isTimedOut = true // 标记为超时状态
		c.logger.Warn("Employee ID authorization timeout (timer)",
			zap.String("operator_id", operatorID),
			zap.Time("authorization_time", c.authorizationTime),
			zap.Duration("timeout_duration", authorizationTimeout))

		// 如果有回调函数，异步执行
		if callback != nil {
			c.logger.Info("Triggering timeout callback for expired authorization",
				zap.String("operator_id", operatorID))
			c.mu.Unlock()
			go callback(operatorID)
			return
		}
	} else if operatorID != "" && c.isTimedOut {
		// 已经被主动检查处理过了
		c.logger.Debug("Employee ID timeout already handled by active check",
			zap.String("operator_id", operatorID))
	}

	c.mu.Unlock()
}

// clear 是内部方法，用于因超时等原因清除待处理的员工ID
func (c *OperatorIDCache) clear(reason string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.clearInternal(reason)
}

// 🆕 新增：clearInternal 内部清理方法（已加锁）
func (c *OperatorIDCache) clearInternal(reason string) {
	if c.pendingOperatorID != "" {
		c.logger.Debug("Employee ID cache cleared",
			zap.String("reason", reason),
			zap.String("pending_operator_id", c.pendingOperatorID),
			zap.Time("authorization_time", c.authorizationTime),
			zap.Duration("cache_duration", time.Since(c.authorizationTime)))
	}

	// 清除缓存状态
	c.pendingOperatorID = ""
	c.setTime = time.Time{}
	c.authorizationTime = time.Time{}
	c.isTimedOut = false

	if c.authorizationTimer != nil {
		c.authorizationTimer.Stop()
		c.authorizationTimer = nil
	}
}

// HasPendingOperator 检查是否有待处理的员工ID（用于测试和调试）
func (c *OperatorIDCache) HasPendingOperator() bool {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.pendingOperatorID != ""
}

// GetStatus 获取缓存状态信息（用于调试）
func (c *OperatorIDCache) GetStatus() map[string]interface{} {
	c.mu.Lock()
	defer c.mu.Unlock()

	status := map[string]interface{}{
		"has_pending_operator": c.pendingOperatorID != "",
		"pending_operator_id":  c.pendingOperatorID,
		"has_active_timer":     c.authorizationTimer != nil,
		"timeout_duration":     authorizationTimeout.String(),
		"is_timed_out":         c.isTimedOut, // 🆕 新增超时状态
	}

	if !c.authorizationTime.IsZero() {
		status["authorization_time"] = c.authorizationTime
		status["elapsed_time"] = time.Since(c.authorizationTime).String()
		remaining := authorizationTimeout - time.Since(c.authorizationTime)
		if remaining > 0 {
			status["remaining_time"] = remaining.String()
		} else {
			status["remaining_time"] = "expired"
		}
	}

	// 保持向后兼容
	if !c.setTime.IsZero() {
		status["set_time"] = c.setTime
	}

	return status
}

// 🆕 预授权相关方法实现

// SetPreAuth 设置预授权数据
func (c *OperatorIDCache) SetPreAuth(ctx context.Context, nozzleID string, preauth *dto.PreAuthRequest) error {
	if !c.IsPreAuthEnabled() {
		return fmt.Errorf("预授权功能未启用")
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 更新最近一次预授权信息
	c.lastPreAuth = preauth
	c.lastPreAuthTime = time.Now()
	c.lastPreAuthNozzleID = nozzleID

	c.logger.Debug("预授权已设置",
		zap.String("nozzle_id", nozzleID),
		zap.String("auth_type", preauth.AuthType),
		zap.String("preset_volume", preauth.Volume.String()))

	return nil
}

// GetPreAuth 获取预授权数据（不消费）
func (c *OperatorIDCache) GetPreAuth(ctx context.Context, nozzleID string) *dto.PreAuthRequest {
	if !c.IsPreAuthEnabled() {
		return nil
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 检查最近一次预授权是否有效
	if c.lastPreAuth != nil &&
		c.lastPreAuthTime.Add(c.GetPreAuthTTL()).After(time.Now()) &&
		c.lastPreAuthNozzleID == nozzleID {
		c.logger.Debug("预授权获取成功 (from recent)",
			zap.String("nozzle_id", nozzleID),
			zap.String("auth_type", c.lastPreAuth.AuthType),
			zap.String("preset_volume", c.lastPreAuth.Volume.String()))
		return c.lastPreAuth
	}

	return nil
}

// ConsumePreAuth 消费预授权数据（获取并删除）
func (c *OperatorIDCache) ConsumePreAuth(ctx context.Context, nozzleID string) *dto.PreAuthRequest {
	if !c.IsPreAuthEnabled() {
		return nil
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 打印当前缓存状态
	c.logger.Debug("当前缓存状态",
		zap.String("nozzle_id", nozzleID),
		zap.Any("last_preauth", c.lastPreAuth),
		zap.String("last_preauth_nozzle_id", c.lastPreAuthNozzleID),
		zap.Time("last_preauth_time", c.lastPreAuthTime),
		zap.Duration("preauth_ttl", c.GetPreAuthTTL()),
		zap.Duration("now - last_preauth_time", time.Now().Sub(c.lastPreAuthTime)),
	)

	// 检查最近一次预授权是否有效
	if c.lastPreAuth != nil &&
		c.lastPreAuthTime.Add(c.GetPreAuthTTL()).After(time.Now()) &&
		c.lastPreAuthNozzleID == nozzleID {
		// 获取并清空最近一次预授权
		preauth := c.lastPreAuth
		c.lastPreAuth = nil
		c.lastPreAuthTime = time.Time{}
		c.lastPreAuthNozzleID = ""

		c.logger.Info("预授权已消费 (from recent)",
			zap.String("nozzle_id", nozzleID),
			zap.String("auth_type", preauth.AuthType),
			zap.String("preset_volume", preauth.Volume.String()))

		return preauth
	}

	c.logger.Debug("预授权不存在或已过期",
		zap.String("nozzle_id", nozzleID))
	return nil
}

// IsPreAuthEnabled 检查预授权是否启用
func (c *OperatorIDCache) IsPreAuthEnabled() bool {
	return true
	// return c.preAuthConf != nil && c.preAuthConf.Enabled
}

// IsAutoAuthEnabled 检查自动授权是否启用
func (c *OperatorIDCache) IsAutoAuthEnabled() bool {
	return c.autoAuthConf != nil && c.autoAuthConf.Enabled
}

// GetPreAuthTTL 获取预授权TTL
func (c *OperatorIDCache) GetPreAuthTTL() time.Duration {
	if c.preAuthConf == nil {
		return 35 * time.Second // 默认值
	}
	return c.preAuthConf.DefaultTTL
}
