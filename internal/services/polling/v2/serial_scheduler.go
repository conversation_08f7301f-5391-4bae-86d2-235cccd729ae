package v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// SerialPortScheduler 串口调度器接口
type SerialPortScheduler interface {
	// RequestAccess 请求串口访问权限
	RequestAccess(ctx context.Context, deviceID string) bool

	// ReleaseAccess 释放串口访问权限
	ReleaseAccess(deviceID string)

	// RegisterDevice 注册设备到调度器
	RegisterDevice(deviceID, serialPort string) error

	// UnregisterDevice 注销设备
	UnregisterDevice(deviceID string) error

	// GetCurrentActiveDevice 获取当前活跃设备
	GetCurrentActiveDevice(serialPort string) string

	// IsSchedulingEnabled 检查调度是否启用
	IsSchedulingEnabled() bool

	// Start 启动调度器
	Start(ctx context.Context) error

	// Stop 停止调度器
	Stop() error
}

// TimeSliceSchedulerConfig 时间片调度器配置
type TimeSliceSchedulerConfig struct {
	// TimeSliceMs 每个设备的时间片长度（毫秒）
	TimeSliceMs int `yaml:"time_slice_ms" json:"time_slice_ms"`

	// SwitchDelayMs 设备切换间隔（毫秒）
	SwitchDelayMs int `yaml:"switch_delay_ms" json:"switch_delay_ms"`

	// MaxWaitTimeoutMs 最大等待调度超时（毫秒）
	MaxWaitTimeoutMs int `yaml:"max_wait_timeout_ms" json:"max_wait_timeout_ms"`

	// Enabled 是否启用调度
	Enabled bool `yaml:"enabled" json:"enabled"`
}

// DefaultTimeSliceSchedulerConfig 默认配置
var DefaultTimeSliceSchedulerConfig = TimeSliceSchedulerConfig{
	TimeSliceMs:      100,  // 优化：从250ms减少到100ms，提高响应速度
	SwitchDelayMs:    2,    // 优化：从10ms减少到2ms，减少切换开销  
	MaxWaitTimeoutMs: 3000, // 优化：从1000ms增加到3000ms，支持更多设备避免饥饿
	Enabled:          true,
}

// SerialPortGroup 串口设备组
type SerialPortGroup struct {
	SerialPort       string    // 串口路径
	Devices          []string  // 设备ID列表
	CurrentIndex     int       // 当前活跃设备索引
	LastSwitchTime   time.Time // 上次切换时间
	CurrentDeviceID  string    // 当前活跃设备ID
	InSilencePeriod  bool      // 是否处于静默期
	SilenceStartTime time.Time // 静默期开始时间
}

// TimeSliceScheduler 时间片调度器实现
type TimeSliceScheduler struct {
	config TimeSliceSchedulerConfig
	logger *zap.Logger

	// 串口分组管理
	portGroups map[string]*SerialPortGroup // serialPort -> group
	devicePort map[string]string           // deviceID -> serialPort

	// 访问权限控制
	activeDevices map[string]bool // deviceID -> isActive

	// 🚀 新增：主动释放通知机制
	releaseChan chan struct{} // 用于主动释放的通知通道

	// 同步控制
	mu      sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	wg      sync.WaitGroup
}

// NewTimeSliceScheduler 创建时间片调度器
func NewTimeSliceScheduler(config TimeSliceSchedulerConfig, logger *zap.Logger) *TimeSliceScheduler {
	return &TimeSliceScheduler{
		config:        config,
		logger:        logger,
		portGroups:    make(map[string]*SerialPortGroup),
		devicePort:    make(map[string]string),
		activeDevices: make(map[string]bool),
		releaseChan:   make(chan struct{}, 10), // 🚀 新增：带缓冲的通道，避免阻塞
	}
}

// RegisterDevice 注册设备到调度器
func (ts *TimeSliceScheduler) RegisterDevice(deviceID, serialPort string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	// 检查设备是否已注册
	if existingPort, exists := ts.devicePort[deviceID]; exists {
		if existingPort == serialPort {
			return nil // 已经注册到相同串口
		}
		return fmt.Errorf("device %s already registered to different port %s", deviceID, existingPort)
	}

	// 添加到设备-串口映射
	ts.devicePort[deviceID] = serialPort

	// 创建或更新串口组
	group, exists := ts.portGroups[serialPort]
	if !exists {
		group = &SerialPortGroup{
			SerialPort:       serialPort,
			Devices:          make([]string, 0),
			CurrentIndex:     0,
			LastSwitchTime:   time.Now(),
			CurrentDeviceID:  "",
			InSilencePeriod:  false,
			SilenceStartTime: time.Time{},
		}
		ts.portGroups[serialPort] = group
	}

	// 添加设备到组
	group.Devices = append(group.Devices, deviceID)

	// 如果是第一个设备，设置为当前活跃设备
	if len(group.Devices) == 1 {
		group.CurrentDeviceID = deviceID
		ts.activeDevices[deviceID] = true
	}

	ts.logger.Info("Device registered to serial scheduler",
		zap.String("device_id", deviceID),
		zap.String("serial_port", serialPort),
		zap.Int("total_devices_on_port", len(group.Devices)))

	return nil
}

// UnregisterDevice 注销设备
func (ts *TimeSliceScheduler) UnregisterDevice(deviceID string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	serialPort, exists := ts.devicePort[deviceID]
	if !exists {
		return fmt.Errorf("device %s not found", deviceID)
	}

	// 从设备-串口映射中删除
	delete(ts.devicePort, deviceID)
	delete(ts.activeDevices, deviceID)

	// 从串口组中删除设备
	group := ts.portGroups[serialPort]
	if group != nil {
		newDevices := make([]string, 0)
		for _, dev := range group.Devices {
			if dev != deviceID {
				newDevices = append(newDevices, dev)
			}
		}
		group.Devices = newDevices

		// 重置当前设备索引
		if group.CurrentDeviceID == deviceID {
			if len(newDevices) > 0 {
				group.CurrentIndex = 0
				group.CurrentDeviceID = newDevices[0]
				ts.activeDevices[newDevices[0]] = true
			} else {
				group.CurrentDeviceID = ""
			}
		}

		// 如果没有设备了，删除组
		if len(newDevices) == 0 {
			delete(ts.portGroups, serialPort)
		}
	}

	ts.logger.Info("Device unregistered from serial scheduler",
		zap.String("device_id", deviceID),
		zap.String("serial_port", serialPort))

	return nil
}

// RequestAccess 请求串口访问权限
func (ts *TimeSliceScheduler) RequestAccess(ctx context.Context, deviceID string) bool {
	// 如果调度未启用，直接允许访问
	if !ts.config.Enabled {
		return true
	}

	ts.mu.RLock()
	serialPort, exists := ts.devicePort[deviceID]
	if !exists {
		ts.mu.RUnlock()
		ts.logger.Warn("Device not registered for scheduling",
			zap.String("device_id", deviceID))
		return true // 未注册的设备直接允许访问
	}

	group := ts.portGroups[serialPort]
	if group == nil || len(group.Devices) <= 1 {
		ts.mu.RUnlock()
		return true // 单设备直接允许访问
	}

	// 检查是否处于静默期
	if group.InSilencePeriod {
		ts.mu.RUnlock()
		// 静默期内所有设备都不能访问
		ts.logger.Debug("Serial port in silence period, access denied",
			zap.String("device_id", deviceID),
			zap.String("serial_port", serialPort))
	} else {
		// 检查是否是当前活跃设备
		isActive := ts.activeDevices[deviceID]
		ts.mu.RUnlock()

		if isActive {
			ts.logger.Debug("Device marked as in-use",
				zap.String("device_id", deviceID),
				zap.String("serial_port", serialPort))
			return true // 当前活跃设备直接返回
		}
	}

	// 等待轮到该设备
	startTime := time.Now()
	timeout := time.Duration(ts.config.MaxWaitTimeoutMs) * time.Millisecond

	for {
		select {
		case <-ctx.Done():
			return false
		case <-time.After(50 * time.Millisecond): // 每50ms检查一次
			ts.mu.RLock()
			isActive := ts.activeDevices[deviceID]
			ts.mu.RUnlock()

			if isActive {
				return true
			}

			// 检查超时
			if time.Since(startTime) > timeout {
				ts.logger.Warn("Wait for serial access timeout",
					zap.String("device_id", deviceID),
					zap.Duration("wait_time", time.Since(startTime)))
				return false
			}
		}
	}
}

// ReleaseAccess 释放串口访问权限
func (ts *TimeSliceScheduler) ReleaseAccess(deviceID string) {
	// 如果调度未启用，无需操作
	if !ts.config.Enabled {
		return
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	serialPort, exists := ts.devicePort[deviceID]
	if exists {
		group := ts.portGroups[serialPort]
		if group != nil && group.CurrentDeviceID == deviceID {
			// 显式释放当前设备的访问权限
			delete(ts.activeDevices, deviceID)
			ts.logger.Debug("Device released access explicitly",
				zap.String("device_id", deviceID),
				zap.String("serial_port", serialPort),
				zap.Duration("use_duration", time.Since(group.LastSwitchTime)))

			// 🚀 新增：发送主动释放通知，触发立即切换
			select {
			case ts.releaseChan <- struct{}{}:
				ts.logger.Debug("Active release notification sent",
					zap.String("device_id", deviceID),
					zap.String("serial_port", serialPort))
			default:
				// 通道已满，无所谓，定时器会处理
				ts.logger.Debug("Release notification channel full, using timer fallback",
					zap.String("device_id", deviceID))
			}
		}
	}
}

// GetCurrentActiveDevice 获取当前活跃设备
func (ts *TimeSliceScheduler) GetCurrentActiveDevice(serialPort string) string {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	group := ts.portGroups[serialPort]
	if group == nil {
		return ""
	}

	return group.CurrentDeviceID
}

// IsSchedulingEnabled 检查调度是否启用
func (ts *TimeSliceScheduler) IsSchedulingEnabled() bool {
	return ts.config.Enabled
}

// Start 启动调度器
func (ts *TimeSliceScheduler) Start(ctx context.Context) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.running {
		return nil // 已经运行
	}

	ts.ctx, ts.cancel = context.WithCancel(ctx)
	ts.running = true

	// 启动调度循环
	ts.wg.Add(1)
	go ts.scheduleLoop()

	ts.logger.Info("Serial port scheduler started",
		zap.Bool("enabled", ts.config.Enabled),
		zap.Int("time_slice_ms", ts.config.TimeSliceMs))

	return nil
}

// Stop 停止调度器
func (ts *TimeSliceScheduler) Stop() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if !ts.running {
		return nil // 已经停止
	}

	ts.cancel()
	ts.running = false

	// 等待调度循环结束
	ts.wg.Wait()

	// 🚀 新增：清理释放通知通道
	close(ts.releaseChan)

	ts.logger.Info("Serial port scheduler stopped")
	return nil
}

// scheduleLoop 调度循环
func (ts *TimeSliceScheduler) scheduleLoop() {
	defer ts.wg.Done()
	
	// 如果调度器被禁用，直接退出
	if !ts.config.Enabled {
		ts.logger.Info("串口调度器已禁用，调度循环不启动")
		return
	}
	
	// 确保时间片配置合理，避免0值导致panic
	timeSlice := time.Duration(ts.config.TimeSliceMs) * time.Millisecond
	if timeSlice <= 0 {
		timeSlice = 250 * time.Millisecond // 使用默认值
		ts.logger.Warn("Invalid time slice configuration, using default",
			zap.Int("configured_ms", ts.config.TimeSliceMs),
			zap.Duration("default_used", timeSlice))
	}
	
	ticker := time.NewTicker(timeSlice)
	defer ticker.Stop()
	
	ts.logger.Info("Enhanced schedule loop started with active release support",
		zap.Duration("time_slice", timeSlice),
		zap.String("mode", "time_and_event_driven"),
		zap.Bool("enabled", ts.config.Enabled))
	
	for {
		select {
		case <-ts.ctx.Done():
			ts.logger.Debug("Schedule loop stopped by context")
			return
		case <-ticker.C:
			// 定时器触发的切换
			if ts.config.Enabled {
				ts.logger.Debug("Timer-triggered device switch")
				ts.switchDevices()
			}
		case <-ts.releaseChan:
			// 🚀 新增：主动释放触发的立即切换
			if ts.config.Enabled {
				ts.logger.Debug("Active-release-triggered device switch")
				ts.switchDevices()
			}
		}
	}
}

// switchDevices 切换设备 - 时分复用实现
func (ts *TimeSliceScheduler) switchDevices() {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	now := time.Now()
	silencePeriod := time.Duration(ts.config.SwitchDelayMs) * time.Millisecond

	// 🚀 新增：诊断日志，帮助理解切换触发原因
	ts.logger.Debug("Device switch evaluation started",
		zap.Time("current_time", now),
		zap.Duration("silence_period", silencePeriod),
		zap.Int("total_port_groups", len(ts.portGroups)))

	for serialPort, group := range ts.portGroups {
		if len(group.Devices) <= 1 {
			continue // 单设备无需切换
		}

		// 处理静默期
		if group.InSilencePeriod {
			if now.Sub(group.SilenceStartTime) >= silencePeriod {
				// 静默期结束，激活下一个设备
				group.InSilencePeriod = false
				ts.activeDevices[group.CurrentDeviceID] = true
				
				ts.logger.Debug("Silence period ended, device activated",
					zap.String("serial_port", serialPort),
					zap.String("active_device", group.CurrentDeviceID),
					zap.Duration("silence_duration", silencePeriod))
			} else {
				// 仍在静默期，跳过此串口组
				ts.logger.Debug("Skipping device switch - in silence period",
					zap.String("serial_port", serialPort),
					zap.Duration("remaining_silence", silencePeriod-now.Sub(group.SilenceStartTime)))
				continue
			}
		}

		// 检查是否需要切换设备
		timeSlice := time.Duration(ts.config.TimeSliceMs) * time.Millisecond
		if !group.InSilencePeriod && now.Sub(group.LastSwitchTime) >= timeSlice {
			// 当前设备时间片结束，开始切换
			oldDeviceID := group.CurrentDeviceID
			
			// 清除当前活跃设备
			if group.CurrentDeviceID != "" {
				delete(ts.activeDevices, group.CurrentDeviceID)
			}

			// 进入静默期
			group.InSilencePeriod = true
			group.SilenceStartTime = now

			// 切换到下一个设备（但暂不激活）
			group.CurrentIndex = (group.CurrentIndex + 1) % len(group.Devices)
			group.CurrentDeviceID = group.Devices[group.CurrentIndex]
			group.LastSwitchTime = now

			ts.logger.Debug("Switched active device",
				zap.String("serial_port", serialPort),
				zap.String("old_device", oldDeviceID),
				zap.String("new_device", group.CurrentDeviceID),
				zap.Int("device_index", group.CurrentIndex),
				zap.Int("total_devices", len(group.Devices)),
				zap.Duration("silence_period", silencePeriod),
				zap.String("status", "entering_silence_period"))
		} else if !group.InSilencePeriod {
			// 检查当前设备是否使用时间过长
			maxUseTime := timeSlice + 500*time.Millisecond // 允许超出500ms
			if now.Sub(group.LastSwitchTime) > maxUseTime {
				oldDeviceID := group.CurrentDeviceID
				
				ts.logger.Warn("Device using serial port too long, forcing release",
					zap.String("device_id", group.CurrentDeviceID),
					zap.String("serial_port", serialPort),
					zap.Duration("use_duration", now.Sub(group.LastSwitchTime)),
					zap.Duration("max_allowed", maxUseTime))
				
				// 强制释放并进入静默期
				delete(ts.activeDevices, group.CurrentDeviceID)
				group.InSilencePeriod = true
				group.SilenceStartTime = now
				
				// 切换到下一个设备
				group.CurrentIndex = (group.CurrentIndex + 1) % len(group.Devices)
				group.CurrentDeviceID = group.Devices[group.CurrentIndex]
				group.LastSwitchTime = now
				
				ts.logger.Debug("Forced device switch due to timeout",
					zap.String("serial_port", serialPort),
					zap.String("old_device", oldDeviceID),
					zap.String("new_device", group.CurrentDeviceID),
					zap.String("status", "forced_switch_entering_silence"))
			}
		}
	}
}
