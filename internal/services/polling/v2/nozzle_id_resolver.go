package v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/services/nozzle"
	"fcc-service/pkg/models"
)

// NozzleIDResolver 喷嘴ID解析器
// 职责：在DevicePoller层级统一管理 nozzle number (byte) 到 nozzle ID (string) 的映射
// 解决根本问题：Nozzle Number 不是唯一ID，不能直接用于数据库操作
type NozzleIDResolver interface {
	// ResolveNozzleID 将设备内的喷嘴编号转换为系统全局唯一的nozzle ID
	ResolveNozzleID(ctx context.Context, deviceID string, nozzleNumber byte) (string, error)

	// ResolveNozzle 获取完整的nozzle对象
	ResolveNozzle(ctx context.Context, deviceID string, nozzleNumber byte) (*models.Nozzle, error)

	// BatchResolveNozzleIDs 批量解析多个喷嘴ID
	BatchResolveNozzleIDs(ctx context.Context, deviceID string, nozzleNumbers []byte) (map[byte]string, error)

	// RefreshCache 刷新缓存（当nozzle配置变更时调用）
	RefreshCache(ctx context.Context, deviceID string) error

	// GetCachedNozzleID 获取缓存的nozzle ID（快速路径，不查询数据库）
	GetCachedNozzleID(deviceID string, nozzleNumber byte) (string, bool)
}

// nozzleIDResolver 实现
type nozzleIDResolver struct {
	nozzleService nozzle.ServiceV2
	logger        *zap.Logger

	// 缓存：deviceID -> nozzleNumber -> nozzleID
	cache     map[string]map[byte]string
	cacheMu   sync.RWMutex
	cacheTime map[string]time.Time // 缓存时间
	cacheTTL  time.Duration        // 缓存生存时间
}

// NewNozzleIDResolver 创建喷嘴ID解析器
func NewNozzleIDResolver(nozzleService nozzle.ServiceV2, logger *zap.Logger) NozzleIDResolver {
	return &nozzleIDResolver{
		nozzleService: nozzleService,
		logger:        logger,
		cache:         make(map[string]map[byte]string),
		cacheTime:     make(map[string]time.Time),
		cacheTTL:      5 * time.Minute, // 5分钟缓存
	}
}

// ResolveNozzleID 将设备内的喷嘴编号转换为系统全局唯一的nozzle ID
func (r *nozzleIDResolver) ResolveNozzleID(ctx context.Context, deviceID string, nozzleNumber byte) (string, error) {
	// 1. 尝试从缓存获取
	if nozzleID, found := r.GetCachedNozzleID(deviceID, nozzleNumber); found {
		return nozzleID, nil
	}

	// 2. 从数据库查询
	nozzle, err := r.nozzleService.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		r.logger.Error("Failed to resolve nozzle ID",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.Error(err))
		return "", fmt.Errorf("failed to resolve nozzle ID for device %s nozzle %d: %w", deviceID, nozzleNumber, err)
	}

	if nozzle == nil {
		return "", fmt.Errorf("nozzle %d not found for device %s", nozzleNumber, deviceID)
	}

	// 3. 更新缓存
	r.updateCache(deviceID, nozzleNumber, nozzle.ID)

	r.logger.Debug("Nozzle ID resolved",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", nozzleNumber),
		zap.String("nozzle_id", nozzle.ID))

	return nozzle.ID, nil
}

// ResolveNozzle 获取完整的nozzle对象
func (r *nozzleIDResolver) ResolveNozzle(ctx context.Context, deviceID string, nozzleNumber byte) (*models.Nozzle, error) {
	nozzle, err := r.nozzleService.GetNozzle(ctx, deviceID, nozzleNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve nozzle for device %s nozzle %d: %w", deviceID, nozzleNumber, err)
	}

	if nozzle == nil {
		return nil, fmt.Errorf("nozzle %d not found for device %s", nozzleNumber, deviceID)
	}

	// 更新缓存
	r.updateCache(deviceID, nozzleNumber, nozzle.ID)

	return nozzle, nil
}

// BatchResolveNozzleIDs 批量解析多个喷嘴ID
func (r *nozzleIDResolver) BatchResolveNozzleIDs(ctx context.Context, deviceID string, nozzleNumbers []byte) (map[byte]string, error) {
	result := make(map[byte]string)
	var missingNumbers []byte

	// 1. 先从缓存获取
	for _, nozzleNumber := range nozzleNumbers {
		if nozzleID, found := r.GetCachedNozzleID(deviceID, nozzleNumber); found {
			result[nozzleNumber] = nozzleID
		} else {
			missingNumbers = append(missingNumbers, nozzleNumber)
		}
	}

	// 2. 查询缺失的喷嘴
	if len(missingNumbers) > 0 {
		nozzles, err := r.nozzleService.GetDeviceNozzles(ctx, deviceID)
		if err != nil {
			return nil, fmt.Errorf("failed to get device nozzles: %w", err)
		}

		// 建立number到ID的映射
		numberToID := make(map[byte]string)
		for _, nozzle := range nozzles {
			numberToID[nozzle.Number] = nozzle.ID
		}

		// 填充结果并更新缓存
		for _, nozzleNumber := range missingNumbers {
			if nozzleID, found := numberToID[nozzleNumber]; found {
				result[nozzleNumber] = nozzleID
				r.updateCache(deviceID, nozzleNumber, nozzleID)
			} else {
				r.logger.Warn("Nozzle not found in batch resolve",
					zap.String("device_id", deviceID),
					zap.Uint8("nozzle_number", nozzleNumber))
			}
		}
	}

	return result, nil
}

// RefreshCache 刷新缓存
func (r *nozzleIDResolver) RefreshCache(ctx context.Context, deviceID string) error {
	r.cacheMu.Lock()
	defer r.cacheMu.Unlock()

	// 清除旧缓存
	delete(r.cache, deviceID)
	delete(r.cacheTime, deviceID)

	// 重新加载
	nozzles, err := r.nozzleService.GetDeviceNozzles(ctx, deviceID)
	if err != nil {
		return fmt.Errorf("failed to refresh nozzle cache: %w", err)
	}

	// 重建缓存
	deviceCache := make(map[byte]string)
	for _, nozzle := range nozzles {
		deviceCache[nozzle.Number] = nozzle.ID
	}

	r.cache[deviceID] = deviceCache
	r.cacheTime[deviceID] = time.Now()

	r.logger.Info("Nozzle ID cache refreshed",
		zap.String("device_id", deviceID),
		zap.Int("nozzle_count", len(deviceCache)))

	return nil
}

// GetCachedNozzleID 获取缓存的nozzle ID
func (r *nozzleIDResolver) GetCachedNozzleID(deviceID string, nozzleNumber byte) (string, bool) {
	r.cacheMu.RLock()
	defer r.cacheMu.RUnlock()

	// 检查缓存是否过期
	if cacheTime, exists := r.cacheTime[deviceID]; exists {
		if time.Since(cacheTime) > r.cacheTTL {
			// 缓存过期，异步刷新
			go func() {
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()
				r.RefreshCache(ctx, deviceID)
			}()
			return "", false
		}
	}

	deviceCache, deviceExists := r.cache[deviceID]
	if !deviceExists {
		return "", false
	}

	nozzleID, nozzleExists := deviceCache[nozzleNumber]
	return nozzleID, nozzleExists
}

// updateCache 更新缓存
func (r *nozzleIDResolver) updateCache(deviceID string, nozzleNumber byte, nozzleID string) {
	r.cacheMu.Lock()
	defer r.cacheMu.Unlock()

	if r.cache[deviceID] == nil {
		r.cache[deviceID] = make(map[byte]string)
	}

	r.cache[deviceID][nozzleNumber] = nozzleID
	r.cacheTime[deviceID] = time.Now()
}
