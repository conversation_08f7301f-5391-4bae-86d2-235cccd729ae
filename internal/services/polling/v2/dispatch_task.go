package v2

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/config"
	"fcc-service/internal/services/device_runtime_state"
	"fcc-service/internal/services/nozzle"
	"fcc-service/internal/services/nozzle_counters"
	"fcc-service/internal/services/watchdog"
	"fcc-service/pkg/api"
	"fcc-service/pkg/models"
	v2models "fcc-service/pkg/models/v2"
)

// 注意：接口定义已移动到 interfaces.go 文件中

const (
	DefaultPollInterval = 1 * time.Second // 🔧 改回：恢复1秒轮询间隔
	DefaultTimeout      = 25 * time.Millisecond
	DefaultRetries      = 3
)

// DispatchTaskConfig 调度任务配置
type DispatchTaskConfig struct {
	Name                string        `json:"name"`                  // 任务名称
	MaxDevices          int           `json:"max_devices"`           // 最大设备数
	DefaultPollInterval time.Duration `json:"default_poll_interval"` // 默认轮询间隔
	DefaultPollTimeout  time.Duration `json:"default_poll_timeout"`  // 默认轮询超时
	WatchdogTimeout     time.Duration `json:"watchdog_timeout"`      // 看门狗超时
	MaxRetries          int           `json:"max_retries"`           // 最大重试次数
	ResultBufferSize    int           `json:"result_buffer_size"`    // 结果缓冲区大小
	EnableMetrics       bool          `json:"enable_metrics"`        // 启用指标收集
}

// DispatchMetrics 调度指标数据模型（纯数据，无并发控制）
type DispatchMetrics struct {
	TotalDevices    int64         `json:"total_devices"`
	ActiveDevices   int64         `json:"active_devices"`
	TotalPolls      int64         `json:"total_polls"`
	SuccessfulPolls int64         `json:"successful_polls"`
	FailedPolls     int64         `json:"failed_polls"`
	AverageLatency  time.Duration `json:"average_latency"`
	StartTime       time.Time     `json:"start_time"`
	LastUpdateTime  time.Time     `json:"last_update_time"`
}

// metricsCollector 指标收集器（包含并发控制）
type metricsCollector struct {
	data DispatchMetrics
	mu   sync.RWMutex
}

// newMetricsCollector 创建指标收集器
func newMetricsCollector() *metricsCollector {
	return &metricsCollector{
		data: DispatchMetrics{
			StartTime: time.Now(),
		},
	}
}

// GetSnapshot 获取指标快照（无锁版本）
func (mc *metricsCollector) GetSnapshot() DispatchMetrics {
	mc.mu.RLock()
	defer mc.mu.RUnlock()

	// 返回数据副本，不包含mutex
	return DispatchMetrics{
		TotalDevices:    mc.data.TotalDevices,
		ActiveDevices:   mc.data.ActiveDevices,
		TotalPolls:      mc.data.TotalPolls,
		SuccessfulPolls: mc.data.SuccessfulPolls,
		FailedPolls:     mc.data.FailedPolls,
		AverageLatency:  mc.data.AverageLatency,
		StartTime:       mc.data.StartTime,
		LastUpdateTime:  mc.data.LastUpdateTime,
	}
}

// Update 更新指标
func (mc *metricsCollector) Update(updateFunc func(*DispatchMetrics)) {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	updateFunc(&mc.data)
}

// DevicePollerManager 设备轮询器管理接口
type DevicePollerManager interface {
	// 设备管理
	RegisterDevice(deviceInfo v2models.DeviceInfo, config DevicePollerConfig) error
	UnregisterDevice(deviceID string) error
	GetDevice(deviceID string) (*DevicePoller, error)
	GetAllDevices() map[string]*DevicePoller

	// 轮询控制
	StartDevice(deviceID string) error
	StopDevice(deviceID string) error
	StartAll() error
	StopAll() error

	// 命令发送
	SendCommand(deviceID string, cmd PollCommand) error
	BroadcastCommand(cmd PollCommand) error

	// 状态查询
	GetDeviceStats(deviceID string) (*PollerStats, error)
	GetSystemMetrics() DispatchMetrics
	GetSystemStatus() SystemStatus
}

// SystemStatus 系统状态
type SystemStatus struct {
	IsRunning      bool                               `json:"is_running"`
	TotalDevices   int                                `json:"total_devices"`
	ActiveDevices  int                                `json:"active_devices"`
	StartTime      time.Time                          `json:"start_time"`
	Uptime         time.Duration                      `json:"uptime"`
	DeviceStatuses map[string]DeviceStatus            `json:"device_statuses"`
	WatchdogStatus map[string]watchdog.WatchdogStatus `json:"watchdog_status"`
}

// DeviceStatus 设备运行状态
type DeviceStatus struct {
	DeviceID     string               `json:"device_id"`
	IsRunning    bool                 `json:"is_running"`
	LastActivity time.Time            `json:"last_activity"`
	Stats        PollerStats          `json:"stats"`
	State        v2models.DeviceState `json:"state"`
}

// pollerMap 是一个围绕 sync.Map 的类型安全的并发封装器
type pollerMap struct {
	m sync.Map
}

// Store 是类型安全的写入方法
func (pm *pollerMap) Store(key string, value *DevicePoller) {
	pm.m.Store(key, value)
}

// Load 是类型安全的读取方法
func (pm *pollerMap) Load(key string) (*DevicePoller, bool) {
	value, ok := pm.m.Load(key)
	if !ok {
		return nil, false
	}
	// 内部处理类型断言
	return value.(*DevicePoller), true
}

// Delete 是类型安全的删除方法
func (pm *pollerMap) Delete(key string) {
	pm.m.Delete(key)
}

// Range 是类型安全的遍历方法
func (pm *pollerMap) Range(f func(key string, value *DevicePoller) bool) {
	pm.m.Range(func(key, value interface{}) bool {
		// 内部处理类型断言
		return f(key.(string), value.(*DevicePoller))
	})
}

func (pm *pollerMap) Len() int {
	var length int
	pm.m.Range(func(_, _ interface{}) bool {
		length++
		return true // 继续遍历
	})
	return length
}

// DispatchTask 设备轮询任务调度器
type DispatchTask struct {
	config DispatchTaskConfig
	logger *zap.Logger

	// 设备管理
	pollers       pollerMap                   // 设备轮询器映射
	stateManager  v2models.DeviceStateManager // 状态管理器
	deviceManager api.DeviceManager           // 设备管理器（用于获取设备配置）

	// 🚀 新增：通信锁管理 - 每个串口一个锁，避免RS485冲突
	commLocks map[string]*sync.Mutex // port -> lock
	locksMu   sync.RWMutex           // 锁管理的锁

	// 🚀 新增：通信实例管理 - 每个串口一个通信实例，真正共享
	commInstances map[string]CommunicationInterface // port -> communication
	commMu        sync.RWMutex                      // 通信实例锁

	// 🚀 新增：设备调度管理 - 10ms时间片轮询
	deviceOrder  []string     // 设备ID列表，保持调度顺序
	currentIndex int          // 当前调度索引
	orderMu      sync.RWMutex // 设备顺序锁

	// 服务依赖
	nozzleService           nozzle.ServiceV2                                                                    // Nozzle服务
	transactionService      TransactionServiceInterface                                                         // 交易服务
	lifecycleServiceFactory func(operatorIDCache OperatorIDCacheInterface) TransactionLifecycleServiceInterface // 🚀 修改：工厂函数
	nozzleCountersService   nozzle_counters.Service                                                             // 🚀 新增：喷嘴计数器服务
	runtimeStateService     device_runtime_state.DeviceRuntimeStateService                                      // 🆕 新增：设备运行时状态服务

	// 通信管理
	communicationManager CommunicationManager // 通信管理器

	// 🆕 配置参数
	preAuthConf  *config.PreAuthConfig  // 预授权配置
	autoAuthConf *config.AutoAuthConfig // 自动授权配置

	// 注意：串口调度器已被CommunicationInterface替代，直接使用传输层通信
	serialScheduler SerialPortScheduler // 串口调度器（已弃用，保留兼容性）

	// 看门狗管理
	watchdogMgr *watchdog.WatchdogManager // 看门狗管理器

	// 结果处理
	resultChan     chan PollResult // 统一结果通道
	resultHandlers []ResultHandler // 结果处理器列表
	handlersMu     sync.RWMutex    // 处理器锁

	// 生命周期管理
	ctx       context.Context
	cancel    context.CancelFunc
	isRunning bool
	runningMu sync.RWMutex
	wg        sync.WaitGroup

	// 指标统计 - 使用新的架构
	metricsCollector *metricsCollector
	startTime        time.Time
}

// NewDispatchTask 创建调度任务
//
// 使用示例:
//
//	// 1. 创建必要的依赖
//	nozzleService := nozzle.NewServiceV2(gormDB, logger)
//	transactionService := transaction.NewTransactionService(repo, collector, publisher, logger)
//	stateManager := v2models.NewStateManager()
//	deviceManager := device.NewManager(db, cache, adapterMgr, logger)
//
//	// 2. 创建调度任务（自动注册设备）
//	dispatcher := NewDispatchTask(config, stateManager, nozzleService, transactionService, deviceManager, logger)
//
//	// 3. 启动和使用
//	dispatcher.Start()
//	defer dispatcher.Stop()
//
// 参数说明:
//   - config: 调度任务配置
//   - stateManager: 设备状态管理器
//   - nozzleService: Nozzle服务实例，可以为nil（将禁用Nozzle功能）
//   - transactionService: 交易服务实例，可以为nil（将禁用交易持久化功能）
//   - deviceManager: 设备管理器，可以为nil（需要手动注册设备）
//   - logger: 日志器
func NewDispatchTask(config DispatchTaskConfig, stateManager v2models.DeviceStateManager, nozzleService nozzle.ServiceV2, transactionService TransactionServiceInterface, lifecycleServiceFactory func(operatorIDCache OperatorIDCacheInterface) TransactionLifecycleServiceInterface, nozzleCountersService nozzle_counters.Service, deviceManager api.DeviceManager, preAuthConf *config.PreAuthConfig, autoAuthConf *config.AutoAuthConfig, runtimeStateService device_runtime_state.DeviceRuntimeStateService, logger *zap.Logger) *DispatchTask {
	if logger == nil {
		logger = zap.NewNop()
	}

	// 设置默认值
	if config.MaxDevices <= 0 {
		config.MaxDevices = 100
	}
	if config.DefaultPollInterval <= 0 {
		config.DefaultPollInterval = 2 * time.Second // 🔧 阶段1优化：轮询间隔改为2秒
	}
	if config.DefaultPollTimeout <= 0 {
		config.DefaultPollTimeout = 25 * time.Millisecond // 🔧 修复：严格遵循DART协议25ms响应超时要求
	}
	if config.WatchdogTimeout <= 0 {
		config.WatchdogTimeout = 30 * time.Second // 🚀 优化：从10秒增加到30秒，给授权流程更多时间
	}
	if config.ResultBufferSize <= 0 {
		config.ResultBufferSize = 1000
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 延迟创建通信管理器 - 每个设备可能有不同的通信配置
	// 将在 RegisterDevice 时根据设备配置创建具体的通信接口
	var commManager CommunicationManager

	// 注意：完全禁用串口调度器，使用SimpleSerialManager替代
	disabledConfig := DefaultTimeSliceSchedulerConfig
	disabledConfig.Enabled = false                                   // 禁用复杂调度器，使用简化架构
	serialScheduler := NewTimeSliceScheduler(disabledConfig, logger) // 保持兼容性，但配置为禁用

	dt := &DispatchTask{
		config: config,
		logger: logger,
		// pollers:                 pollerMap{m: sync.Map{}},
		stateManager:            stateManager,
		deviceManager:           deviceManager,                           // 保存设备管理器引用
		commLocks:               make(map[string]*sync.Mutex),            // 🚀 新增：初始化通信锁管理
		commInstances:           make(map[string]CommunicationInterface), // 🚀 新增：初始化通信实例管理
		deviceOrder:             make([]string, 0),                       // 🚀 新增：初始化设备调度顺序
		currentIndex:            0,                                       // 🚀 新增：初始化调度索引
		nozzleService:           nozzleService,
		transactionService:      transactionService,      // 🚀 新增：交易服务
		lifecycleServiceFactory: lifecycleServiceFactory, // 🚀 新增：交易生命周期服务工厂
		nozzleCountersService:   nozzleCountersService,   // 🚀 新增：喷嘴计数器服务
		runtimeStateService:     runtimeStateService,     // 🆕 新增：设备运行时状态服务
		communicationManager:    commManager,             // 设置通信管理器
		serialScheduler:         serialScheduler,         // 🚀 新增：串口调度器
		watchdogMgr:             watchdog.NewWatchdogManager(logger),
		resultChan:              make(chan PollResult, config.ResultBufferSize),
		resultHandlers:          make([]ResultHandler, 0),
		ctx:                     ctx,
		cancel:                  cancel,
		preAuthConf:             preAuthConf,  // 🆕 保存预授权配置
		autoAuthConf:            autoAuthConf, // 🆕 保存自动授权配置
		metricsCollector:        newMetricsCollector(),
		startTime:               time.Now(),
	}

	// 如果提供了设备管理器，自动注册设备
	if deviceManager != nil {
		if err := dt.autoRegisterDevicesFromManager(deviceManager); err != nil {
			logger.Error("Failed to auto-register devices from device manager", zap.Error(err))
		}
	}

	return dt
}

// 🚀 新增：获取通信锁 - 每个串口一个锁
func (dt *DispatchTask) getCommunicationLock(deviceInfo v2models.DeviceInfo) *sync.Mutex {
	port := dt.getDeviceSerialPort(deviceInfo)

	dt.locksMu.RLock()
	if lock, exists := dt.commLocks[port]; exists {
		dt.locksMu.RUnlock()
		return lock
	}
	dt.locksMu.RUnlock()

	// 创建新锁（双重检查）
	dt.locksMu.Lock()
	defer dt.locksMu.Unlock()

	if lock, exists := dt.commLocks[port]; exists {
		return lock
	}

	dt.commLocks[port] = &sync.Mutex{}
	dt.logger.Debug("创建新的通信锁",
		zap.String("port", port),
		zap.String("device_id", deviceInfo.ID))

	return dt.commLocks[port]
}

// 🚀 新增：通信调度器循环 - 10ms时间片轮询设备队列
func (dt *DispatchTask) communicationSchedulerLoop() {
	defer dt.wg.Done()

	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	dt.logger.Info("通信调度器已启动",
		zap.Duration("time_slice", 10*time.Millisecond))

	for {
		select {
		case <-dt.ctx.Done():
			dt.logger.Info("通信调度器已停止")
			return
		case <-ticker.C:
			dt.processNextDeviceQueue()
		}
	}
}

// 🚀 新增：处理下一个设备的发送队列
func (dt *DispatchTask) processNextDeviceQueue() {
	dt.orderMu.RLock()
	if len(dt.deviceOrder) == 0 {
		dt.orderMu.RUnlock()
		return
	}

	deviceID := dt.deviceOrder[dt.currentIndex]
	dt.currentIndex = (dt.currentIndex + 1) % len(dt.deviceOrder)
	dt.orderMu.RUnlock()

	// 获取设备轮询器
	poller, exists := dt.pollers.Load(deviceID)

	if !exists {
		dt.logger.Debug("调度器找不到设备", zap.String("device_id", deviceID))
		return
	}

	// 检查队列是否有数据
	poller.ProcessPendingCommand()

}

// SetSerialSchedulerConfig 设置串口调度器配置（已弃用）
// 注意：在新的简化架构中，串口调度由SimpleSerialManager自动处理
func (dt *DispatchTask) SetSerialSchedulerConfig(config TimeSliceSchedulerConfig) {
	dt.logger.Info("串口调度器配置已被CommunicationInterface替代",
		zap.Bool("old_config_enabled", config.Enabled),
		zap.String("new_architecture", "CommunicationInterface"),
		zap.String("note", "串口通信现在直接通过传输层接口处理"))

	// 为了向后兼容，仍然保存配置但强制禁用
	if !dt.isRunning {
		config.Enabled = false // 强制禁用
		dt.serialScheduler = NewTimeSliceScheduler(config, dt.logger)
		dt.logger.Debug("Serial scheduler config updated (但已禁用)",
			zap.Bool("enabled", config.Enabled))
	} else {
		dt.logger.Debug("Cannot update serial scheduler config while running (且已禁用)")
	}
}

// Start 启动调度任务
func (dt *DispatchTask) Start() error {
	dt.runningMu.Lock()
	defer dt.runningMu.Unlock()

	if dt.isRunning {
		return fmt.Errorf("dispatch task already running")
	}

	dt.isRunning = true
	dt.startTime = time.Now()

	// 注意：串口调度器已禁用，使用CommunicationInterface代替
	if err := dt.serialScheduler.Start(dt.ctx); err != nil {
		dt.logger.Debug("Serial scheduler start (disabled)", zap.Error(err))
	}

	// 启动看门狗管理器
	if err := dt.watchdogMgr.StartAll(dt.ctx); err != nil {
		dt.logger.Warn("Failed to start watchdog manager", zap.Error(err))
	}

	// 启动结果处理循环
	dt.wg.Add(1)
	go dt.resultProcessingLoop()

	// 启动指标收集循环
	if dt.config.EnableMetrics {
		dt.wg.Add(1)
		go dt.metricsCollectionLoop()
	}

	// 🚀 新增：启动通信调度器
	dt.wg.Add(1)
	go dt.communicationSchedulerLoop()

	dt.logger.Info("DispatchTask started",
		zap.String("name", dt.config.Name),
		zap.Int("max_devices", dt.config.MaxDevices),
		zap.Duration("default_poll_interval", dt.config.DefaultPollInterval),
		zap.String("communication_scheduler", "enabled"),
		zap.Duration("scheduler_time_slice", 10*time.Millisecond))

	return nil
}

// Stop 停止调度任务
func (dt *DispatchTask) Stop() error {
	dt.runningMu.Lock()
	defer dt.runningMu.Unlock()

	if !dt.isRunning {
		return nil
	}

	dt.isRunning = false

	// 停止所有设备轮询器
	if err := dt.StopAll(); err != nil {
		dt.logger.Warn("Failed to stop all device pollers", zap.Error(err))
	}

	// 注意：串口调度器已禁用
	if err := dt.serialScheduler.Stop(); err != nil {
		dt.logger.Debug("Serial scheduler stop (disabled)", zap.Error(err))
	}

	// 停止看门狗管理器
	if err := dt.watchdogMgr.StopAll(); err != nil {
		dt.logger.Warn("Failed to stop watchdog manager", zap.Error(err))
	}

	// 取消上下文，停止所有goroutine
	dt.cancel()

	// 等待所有goroutine结束
	dt.wg.Wait()

	dt.logger.Info("DispatchTask stopped",
		zap.String("name", dt.config.Name),
		zap.Duration("uptime", time.Since(dt.startTime)))

	return nil
}

// RegisterDevice 注册设备
func (dt *DispatchTask) RegisterDevice(deviceInfo v2models.DeviceInfo, config DevicePollerConfig) error {
	// 检查设备是否已注册
	if _, exists := dt.pollers.Load(deviceInfo.ID); exists {
		return fmt.Errorf("device %s already registered", deviceInfo.ID)
	}

	// 检查设备数量限制
	if dt.pollers.Len() >= dt.config.MaxDevices {
		return fmt.Errorf("maximum device limit (%d) reached", dt.config.MaxDevices)
	}

	// 设置默认配置
	if config.PollInterval <= 0 {
		config.PollInterval = dt.config.DefaultPollInterval
	}
	if config.PollTimeout <= 0 {
		config.PollTimeout = dt.config.DefaultPollTimeout
	}
	if config.WatchdogTimeout <= 0 {
		config.WatchdogTimeout = dt.config.WatchdogTimeout
	}

	// 🔧 分配设备轮询序列索引，实现设备间轮询错开
	config.PollingSequenceIndex = dt.pollers.Len()

	config.DeviceInfo = deviceInfo

	// 注册到状态管理器
	deviceSM, err := dt.stateManager.RegisterDevice(deviceInfo)
	if err != nil {
		return fmt.Errorf("failed to register device state: %w", err)
	}

	// 创建或获取通信接口 - 根据设备配置创建
	var communication CommunicationInterface
	communication, err = dt.createCommunicationForDevice(deviceInfo)
	if err != nil {
		dt.logger.Warn("Failed to create communication for device, will use simulation mode",
			zap.String("device_id", deviceInfo.ID),
			zap.Error(err))
		communication = nil
	}

	// 注意：串口调度器已禁用，保留代码以维持兼容性
	serialPort := dt.getDeviceSerialPort(deviceInfo)
	if err := dt.serialScheduler.RegisterDevice(deviceInfo.ID, serialPort); err != nil {
		dt.logger.Debug("Serial scheduler registration (disabled)",
			zap.String("device_id", deviceInfo.ID),
			zap.String("serial_port", serialPort),
			zap.Error(err))
	}

	// 🚀 创建设备轮询器（集成新服务）
	frameBuilder := NewDefaultFrameBuilder()
	commLock := dt.getCommunicationLock(deviceInfo) // 🚀 获取通信锁
	poller := NewDevicePoller(config, deviceSM, frameBuilder, dt.nozzleService, dt.transactionService, dt.lifecycleServiceFactory, dt.nozzleCountersService, communication, dt.serialScheduler, commLock, dt.preAuthConf, dt.autoAuthConf, dt.runtimeStateService, dt.logger)
	dt.pollers.Store(deviceInfo.ID, poller)

	// 🚀 新增：将设备添加到调度顺序
	dt.orderMu.Lock()
	dt.deviceOrder = append(dt.deviceOrder, deviceInfo.ID)
	dt.orderMu.Unlock()

	// 注册看门狗
	dt.watchdogMgr.Register(fmt.Sprintf("device_%s", deviceInfo.ID), watchdog.WatchdogConfig{
		Timeout:   config.WatchdogTimeout,
		OnTimeout: func() { dt.handleDeviceTimeout(deviceInfo.ID) },
	})

	// 启动结果监听
	dt.wg.Add(1)
	go dt.deviceResultListener(deviceInfo.ID, poller.GetResultChannel())

	dt.logger.Info("Device registered with polling sequence",
		zap.String("device_id", deviceInfo.ID),
		zap.Uint8("address", deviceInfo.Address),
		zap.Duration("poll_interval", config.PollInterval),
		zap.Int("sequence_index", config.PollingSequenceIndex),
		zap.Duration("polling_offset", time.Duration(config.PollingSequenceIndex)*1000*time.Millisecond),
		zap.Bool("communication_available", communication != nil))

	return nil
}

// UnregisterDevice 注销设备
func (dt *DispatchTask) UnregisterDevice(deviceID string) error {

	poller, exists := dt.pollers.Load(deviceID)
	if !exists {
		return fmt.Errorf("device %s not found", deviceID)
	}

	// 停止轮询器
	if err := poller.Stop(); err != nil {
		dt.logger.Warn("Failed to stop device poller",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 注销看门狗
	dt.watchdogMgr.Unregister(fmt.Sprintf("device_%s", deviceID))

	// 从状态管理器注销
	if err := dt.stateManager.UnregisterDevice(deviceID); err != nil {
		dt.logger.Warn("Failed to unregister device state",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 注意：串口调度器已禁用
	if err := dt.serialScheduler.UnregisterDevice(deviceID); err != nil {
		dt.logger.Debug("Serial scheduler unregistration (disabled)",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 🚀 新增：检查是否需要清理通信实例
	// TODO: 需要实现通信实例清理逻辑

	// 从映射中删除
	dt.pollers.Delete(deviceID)

	// 🚀 新增：从调度顺序中移除设备
	dt.orderMu.Lock()
	for i, id := range dt.deviceOrder {
		if id == deviceID {
			dt.deviceOrder = append(dt.deviceOrder[:i], dt.deviceOrder[i+1:]...)
			// 调整当前索引
			if dt.currentIndex >= len(dt.deviceOrder) && len(dt.deviceOrder) > 0 {
				dt.currentIndex = 0
			}
			break
		}
	}
	dt.orderMu.Unlock()

	dt.logger.Info("Device unregistered",
		zap.String("device_id", deviceID))

	return nil
}

// StartDevice 启动设备轮询
func (dt *DispatchTask) StartDevice(deviceID string) error {
	poller, exists := dt.pollers.Load(deviceID)

	if !exists {
		return fmt.Errorf("device %s not found", deviceID)
	}

	if err := poller.Start(dt.ctx); err != nil {
		return fmt.Errorf("failed to start device poller: %w", err)
	}

	// 启动对应的看门狗
	if err := dt.watchdogMgr.Start(dt.ctx, fmt.Sprintf("device_%s", deviceID)); err != nil {
		dt.logger.Warn("Failed to start device watchdog",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	return nil
}

// StopDevice 停止设备轮询
func (dt *DispatchTask) StopDevice(deviceID string) error {
	poller, exists := dt.pollers.Load(deviceID)

	if !exists {
		return fmt.Errorf("device %s not found", deviceID)
	}

	if err := poller.Stop(); err != nil {
		return fmt.Errorf("failed to stop device poller: %w", err)
	}

	// 停止对应的看门狗
	if err := dt.watchdogMgr.Stop(fmt.Sprintf("device_%s", deviceID)); err != nil {
		dt.logger.Warn("Failed to stop device watchdog",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	return nil
}

// StartAll 启动所有设备轮询
func (dt *DispatchTask) StartAll() error {
	deviceIDs := make([]string, 0, dt.pollers.Len())
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		deviceIDs = append(deviceIDs, key)
		return true
	})

	dt.logger.Info("Starting all device pollers",
		zap.Int("total_devices", len(deviceIDs)),
		zap.Strings("device_ids", deviceIDs))

	successCount := 0
	failureCount := 0

	for _, deviceID := range deviceIDs {
		dt.logger.Debug("Starting device poller",
			zap.String("device_id", deviceID))

		if err := dt.StartDevice(deviceID); err != nil {
			failureCount++
			dt.logger.Error("Failed to start device poller",
				zap.String("device_id", deviceID),
				zap.Error(err))
		} else {
			successCount++
			dt.logger.Debug("Device poller started successfully",
				zap.String("device_id", deviceID))
		}
	}

	dt.logger.Info("Device pollers startup completed",
		zap.Int("total_devices", len(deviceIDs)),
		zap.Int("successful", successCount),
		zap.Int("failed", failureCount))

	// 只要有设备成功启动，就认为启动成功
	if successCount > 0 || len(deviceIDs) == 0 {
		return nil
	}

	return fmt.Errorf("all device pollers failed to start (%d failures)", failureCount)
}

// StopAll 停止所有设备轮询
func (dt *DispatchTask) StopAll() error {
	deviceIDs := make([]string, 0, dt.pollers.Len())
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		deviceIDs = append(deviceIDs, key)
		return true
	})

	for _, deviceID := range deviceIDs {
		if err := dt.StopDevice(deviceID); err != nil {
			dt.logger.Error("Failed to stop device",
				zap.String("device_id", deviceID),
				zap.Error(err))
		}
	}

	return nil
}

// SendCommand 向指定设备发送命令
func (dt *DispatchTask) SendCommand(deviceID string, cmd PollCommand) error {
	poller, exists := dt.pollers.Load(deviceID)

	if !exists {
		return fmt.Errorf("device %s not found", deviceID)
	}

	return poller.SendCommand(cmd)
}

// BroadcastCommand 向所有设备广播命令
func (dt *DispatchTask) BroadcastCommand(cmd PollCommand) error {
	pollers := make(map[string]*DevicePoller)
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		pollers[key] = value
		return true
	})

	for deviceID, poller := range pollers {
		if err := poller.SendCommand(cmd); err != nil {
			dt.logger.Warn("Failed to send command to device",
				zap.String("device_id", deviceID),
				zap.Error(err))
		}
	}

	return nil
}

// AddResultHandler 添加结果处理器
func (dt *DispatchTask) AddResultHandler(handler ResultHandler) {
	dt.handlersMu.Lock()
	defer dt.handlersMu.Unlock()

	dt.resultHandlers = append(dt.resultHandlers, handler)
	dt.logger.Info("Result handler added",
		zap.String("handler_name", handler.GetName()))
}

// RemoveResultHandler 移除结果处理器
func (dt *DispatchTask) RemoveResultHandler(handlerName string) {
	dt.handlersMu.Lock()
	defer dt.handlersMu.Unlock()

	for i, handler := range dt.resultHandlers {
		if handler.GetName() == handlerName {
			dt.resultHandlers = append(dt.resultHandlers[:i], dt.resultHandlers[i+1:]...)
			dt.logger.Info("Result handler removed",
				zap.String("handler_name", handlerName))
			return
		}
	}
}

// GetSystemMetrics 获取系统指标
func (dt *DispatchTask) GetSystemMetrics() DispatchMetrics {
	return dt.metricsCollector.GetSnapshot()
}

// GetSystemStatus 获取系统状态
func (dt *DispatchTask) GetSystemStatus() SystemStatus {
	dt.runningMu.RLock()
	isRunning := dt.isRunning
	dt.runningMu.RUnlock()

	deviceStatuses := make(map[string]DeviceStatus)
	totalDevices := dt.pollers.Len()
	activeDevices := 0

	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		if value.IsRunning() {
			activeDevices++
		}

		deviceState, _ := dt.stateManager.GetDeviceState(key)
		var state v2models.DeviceState
		if deviceState != nil {
			state = deviceState.State
		}

		deviceStatuses[key] = DeviceStatus{
			DeviceID:     key,
			IsRunning:    value.IsRunning(),
			LastActivity: value.GetLastActivity(),
			Stats:        value.GetStats(),
			State:        state,
		}
		return true
	})

	return SystemStatus{
		IsRunning:      isRunning,
		TotalDevices:   totalDevices,
		ActiveDevices:  activeDevices,
		StartTime:      dt.startTime,
		Uptime:         time.Since(dt.startTime),
		DeviceStatuses: deviceStatuses,
		WatchdogStatus: dt.watchdogMgr.GetStatus(),
	}
}

// GetDevice 获取设备轮询器
func (dt *DispatchTask) GetDevice(deviceID string) (*DevicePoller, error) {

	poller, exists := dt.pollers.Load(deviceID)
	if !exists {
		return nil, fmt.Errorf("device %s not found", deviceID)
	}

	return poller, nil
}

// GetAllDevices 获取所有设备轮询器
func (dt *DispatchTask) GetAllDevices() map[string]*DevicePoller {

	devices := make(map[string]*DevicePoller)
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		devices[key] = value
		return true
	})

	return devices
}

// GetDeviceStats 获取设备统计信息
func (dt *DispatchTask) GetDeviceStats(deviceID string) (*PollerStats, error) {
	poller, exists := dt.pollers.Load(deviceID)

	if !exists {
		return nil, fmt.Errorf("device %s not found", deviceID)
	}

	stats := poller.GetStats()
	return &stats, nil
}

// resultProcessingLoop 结果处理循环
func (dt *DispatchTask) resultProcessingLoop() {
	defer dt.wg.Done()

	for {
		select {
		case <-dt.ctx.Done():
			return
		case result := <-dt.resultChan:
			dt.processResult(result)
		}
	}
}

// processResult 处理轮询结果
func (dt *DispatchTask) processResult(result PollResult) {
	// 更新指标
	dt.metricsCollector.Update(func(metrics *DispatchMetrics) {
		metrics.TotalPolls++
		if result.Error == nil {
			metrics.SuccessfulPolls++
		} else {
			metrics.FailedPolls++
		}

		metrics.LastUpdateTime = time.Now()

		// 更新平均延迟
		if result.Error == nil {
			latency := result.ResponseTime
			if metrics.TotalPolls == 1 {
				metrics.AverageLatency = latency
			} else {
				metrics.AverageLatency = (metrics.AverageLatency + latency) / 2
			}
		}
	})

	// 调用结果处理器
	dt.handlersMu.RLock()
	handlers := make([]ResultHandler, len(dt.resultHandlers))
	copy(handlers, dt.resultHandlers)
	dt.handlersMu.RUnlock()

	for _, handler := range handlers {
		if err := handler.HandleResult(result); err != nil {
			dt.logger.Error("Result handler failed",
				zap.String("handler_name", handler.GetName()),
				zap.Error(err))
		}
	}
}

// deviceResultListener 设备结果监听器
func (dt *DispatchTask) deviceResultListener(deviceID string, resultChan <-chan PollResult) {
	defer dt.wg.Done()

	for {
		select {
		case <-dt.ctx.Done():
			return
		case result, ok := <-resultChan:
			if !ok {
				return
			}

			// 更新看门狗
			dt.watchdogMgr.Ping(fmt.Sprintf("device_%s", deviceID))

			// 转发到统一结果通道
			select {
			case dt.resultChan <- result:
			case <-dt.ctx.Done():
				return
			}
		}
	}
}

// metricsCollectionLoop 指标收集循环
func (dt *DispatchTask) metricsCollectionLoop() {
	defer dt.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-dt.ctx.Done():
			return
		case <-ticker.C:
			dt.collectMetrics()
		}
	}
}

// collectMetrics 收集指标
func (dt *DispatchTask) collectMetrics() {
	totalDevices := 0
	activeDevices := 0
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		if value.IsRunning() {
			activeDevices++
		}
		return true
	})

	dt.metricsCollector.Update(func(metrics *DispatchMetrics) {
		metrics.TotalDevices = int64(totalDevices)
		metrics.ActiveDevices = int64(activeDevices)
	})
}

// handleDeviceTimeout 处理设备超时
func (dt *DispatchTask) handleDeviceTimeout(deviceID string) {
	dt.logger.Warn("Device timeout detected, initiating restart sequence.",
		zap.String("device_id", deviceID))

	// 在独立的goroutine中执行重启，避免阻塞watchdog管理器
	go func() {
		if err := dt.RestartDevice(deviceID); err != nil {
			dt.logger.Error("Device restart sequence failed",
				zap.String("device_id", deviceID),
				zap.Error(err))
		}
	}()
}

// RestartDevice 尝试重启一个已注册的设备轮询器（强化版）
func (dt *DispatchTask) RestartDevice(deviceID string) error {
	dt.logger.Info("🔄 Attempting to restart device poller (enhanced)", zap.String("device_id", deviceID))

	// 1. 强制停止设备，使用更严格的超时控制
	dt.logger.Info("🛑 Force stopping device", zap.String("device_id", deviceID))

	// 首先尝试优雅停止
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer stopCancel()

	stopDone := make(chan error, 1)
	go func() {
		stopDone <- dt.StopDevice(deviceID)
	}()

	select {
	case err := <-stopDone:
		if err != nil {
			dt.logger.Warn("Graceful stop failed, will proceed with force restart",
				zap.String("device_id", deviceID),
				zap.Error(err))
		} else {
			dt.logger.Info("✅ Graceful stop successful", zap.String("device_id", deviceID))
		}
	case <-stopCtx.Done():
		dt.logger.Warn("⏰ Stop operation timed out, proceeding with force restart",
			zap.String("device_id", deviceID))
	}

	// 2. 强制清理资源 - 先从map移除，再在锁外停止
	var pollerToStop *DevicePoller

	// 快速操作：先从map中移除（避免长时间持锁）
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		if key == deviceID {
			dt.logger.Info("🧹 Force removing poller from registry", zap.String("device_id", deviceID))
			dt.pollers.Delete(deviceID) // 立即从map移除
			pollerToStop = value        // 保存引用，稍后停止
			dt.logger.Info("🗑️ Poller removed from registry", zap.String("device_id", deviceID))
			return false
		}
		return true
	})

	// 在锁外停止poller（可能阻塞的操作）
	if pollerToStop != nil {
		dt.logger.Info("🛑 Force stopping poller outside lock", zap.String("device_id", deviceID))

		// 直接停止轮询器（新架构中CommunicationInterface会自动处理连接管理）
		go func() {
			dt.logger.Info("Stopping poller with simplified architecture",
				zap.String("device_id", deviceID))

			// 停止轮询器（内部会自动管理通信接口）
			if err := pollerToStop.Stop(); err != nil {
				dt.logger.Warn("Failed to stop poller during restart",
					zap.String("device_id", deviceID),
					zap.Error(err))
			} else {
				dt.logger.Info("✅ Poller stopped successfully",
					zap.String("device_id", deviceID))
			}

			// 等待资源释放（简化架构中无需长时间等待）
			time.Sleep(100 * time.Millisecond)
		}()
	}

	// 3. 等待资源清理完成，增加等待时间
	dt.logger.Info("⏳ Waiting for resource cleanup", zap.String("device_id", deviceID))
	time.Sleep(1 * time.Second) // 增加等待时间确保goroutine完全退出

	// 4. 清理看门狗状态（安全方式）
	if dt.watchdogMgr != nil {
		watchdogKey := fmt.Sprintf("device_%s", deviceID)
		dt.logger.Info("🐕 Safely cleaning up watchdog",
			zap.String("device_id", deviceID),
			zap.String("watchdog_key", watchdogKey))

		// 安全停止看门狗：使用超时和重试机制
		for attempt := 1; attempt <= 3; attempt++ {
			if err := dt.watchdogMgr.Stop(watchdogKey); err != nil {
				dt.logger.Warn("Failed to stop watchdog",
					zap.String("device_id", deviceID),
					zap.Int("attempt", attempt),
					zap.Error(err))
				if attempt < 3 {
					time.Sleep(200 * time.Millisecond)
					continue
				}
			} else {
				dt.logger.Info("✅ Watchdog stopped successfully",
					zap.String("device_id", deviceID))
				break
			}
		}

		// 注销看门狗，防止重复注册
		if err := dt.watchdogMgr.Unregister(watchdogKey); err != nil {
			dt.logger.Debug("Watchdog unregister completed",
				zap.String("device_id", deviceID),
				zap.Error(err))
		}

		// 额外等待，确保看门狗资源完全释放
		time.Sleep(100 * time.Millisecond)
	}

	// 5. 重新注册和启动设备，使用重试机制
	dt.logger.Info("🚀 Re-registering and starting device with retry mechanism", zap.String("device_id", deviceID))

	var lastErr error
	for attempt := 1; attempt <= 3; attempt++ {
		dt.logger.Info("🔄 Re-registration attempt",
			zap.String("device_id", deviceID),
			zap.Int("attempt", attempt))

		// 首先尝试从设备管理器重新获取设备配置
		if dt.deviceManager != nil {
			ctx := context.Background()
			if device, err := dt.deviceManager.GetDevice(ctx, deviceID); err == nil {
				dt.logger.Info("📋 Found device configuration, re-registering",
					zap.String("device_id", deviceID))

				// 转换设备信息
				deviceInfo, err := dt.convertDeviceToDeviceInfo(device)
				if err == nil {
					// 生成轮询配置
					pollerConfig := dt.generatePollerConfig(device, deviceInfo)

					// 重新注册设备
					if err := dt.RegisterDevice(deviceInfo, pollerConfig); err != nil {
						dt.logger.Warn("Failed to re-register device",
							zap.String("device_id", deviceID),
							zap.Int("attempt", attempt),
							zap.Error(err))
						lastErr = err
					} else {
						dt.logger.Info("✅ Device re-registered successfully",
							zap.String("device_id", deviceID))

						// 启动设备，增加串口重试机制
						startErr := dt.startDeviceWithRetry(deviceID, 3)
						if startErr != nil {
							dt.logger.Warn("Failed to start re-registered device after retries",
								zap.String("device_id", deviceID),
								zap.Error(startErr))
							lastErr = startErr
						} else {
							dt.logger.Info("✅ Device restarted successfully",
								zap.String("device_id", deviceID),
								zap.Int("attempts", attempt))

							// 6. 验证重启成功
							time.Sleep(100 * time.Millisecond) // 短暂等待确保启动
							if dt.isDeviceHealthy(deviceID) {
								dt.logger.Info("💚 Device restart verified healthy", zap.String("device_id", deviceID))
								return nil
							} else {
								dt.logger.Warn("⚠️ Device restarted but not healthy, will continue anyway",
									zap.String("device_id", deviceID))
								return nil // 还是返回成功，让它自己恢复
							}
						}
					}
				} else {
					lastErr = err
					dt.logger.Warn("Failed to convert device info",
						zap.String("device_id", deviceID),
						zap.Error(err))
				}
			} else {
				lastErr = err
				dt.logger.Warn("Failed to get device from device manager",
					zap.String("device_id", deviceID),
					zap.Error(err))
			}
		} else {
			lastErr = fmt.Errorf("device manager not available for re-registration")
			dt.logger.Warn("Device manager not available, cannot re-register device",
				zap.String("device_id", deviceID))
		}

		if attempt < 3 {
			waitTime := time.Duration(attempt) * 500 * time.Millisecond
			dt.logger.Info("⏳ Waiting before retry",
				zap.String("device_id", deviceID),
				zap.Duration("wait_time", waitTime))
			time.Sleep(waitTime)
		}
	}

	dt.logger.Error("❌ Device restart failed after all attempts",
		zap.String("device_id", deviceID),
		zap.Error(lastErr))
	return fmt.Errorf("failed to restart device '%s' after 3 attempts: %w", deviceID, lastErr)
}

// isDeviceHealthy 检查设备是否健康（新增辅助方法）
func (dt *DispatchTask) isDeviceHealthy(deviceID string) bool {
	poller, exists := dt.pollers.Load(deviceID)

	if !exists {
		return false
	}

	// 检查轮询器是否正在运行
	return poller.IsRunning()
}

// IsRunning 检查调度任务是否运行中
func (dt *DispatchTask) IsRunning() bool {
	dt.runningMu.RLock()
	defer dt.runningMu.RUnlock()
	return dt.isRunning
}

// autoRegisterDevicesFromManager 从设备管理器自动注册设备
func (dt *DispatchTask) autoRegisterDevicesFromManager(deviceManager api.DeviceManager) error {
	ctx := context.Background()

	// 获取所有设备
	devices, err := deviceManager.ListDevices(ctx, api.DeviceFilter{})
	if err != nil {
		return fmt.Errorf("failed to list devices from device manager: %w", err)
	}

	dt.logger.Info("Auto-registering devices from device manager",
		zap.Int("device_count", len(devices)))

	var registered int
	var failed int

	for _, device := range devices {
		// 转换设备信息
		deviceInfo, err := dt.convertDeviceToDeviceInfo(device)
		if err != nil {
			dt.logger.Warn("Failed to convert device info",
				zap.String("device_id", device.ID),
				zap.Error(err))
			failed++
			continue
		}

		// 生成轮询配置
		pollerConfig := dt.generatePollerConfig(device, deviceInfo)

		// 注册设备 - 注册所有设备，让轮询系统来确定真实状态
		if err := dt.RegisterDevice(deviceInfo, pollerConfig); err != nil {
			dt.logger.Warn("Failed to register device",
				zap.String("device_id", device.ID),
				zap.Error(err))
			failed++
			continue
		}

		registered++
		// 设备自动注册完成
	}

	dt.logger.Info("Device auto-registration completed",
		zap.Int("total_devices", len(devices)),
		zap.Int("registered", registered),
		zap.Int("failed", failed))

	return nil
}

// convertDeviceToDeviceInfo 转换 models.Device 到 v2models.DeviceInfo
func (dt *DispatchTask) convertDeviceToDeviceInfo(device *models.Device) (v2models.DeviceInfo, error) {
	if device == nil {
		return v2models.DeviceInfo{}, fmt.Errorf("device cannot be nil")
	}

	// 转换设备类型
	var deviceType string
	switch device.Type {
	// case models.DeviceTypeWayneOverheadController:
	// 	deviceType = "wayne_overhead"
	// case models.DeviceTypeWayneSubmersibleController:
	// 	deviceType = "wayne_submersible"
	// case models.DeviceTypeWaynePump:
	// 	deviceType = "wayne_pump"
	default:
		deviceType = string(device.Type)
	}

	return v2models.DeviceInfo{
		ID:           device.ID,
		Name:         device.Name,
		Type:         deviceType,
		Address:      byte(device.DeviceAddress),
		ControllerID: device.ControllerID,
		CreatedAt:    device.CreatedAt,
		UpdatedAt:    device.UpdatedAt,
	}, nil
}

// generatePollerConfig 为设备生成轮询配置
func (dt *DispatchTask) generatePollerConfig(device *models.Device, deviceInfo v2models.DeviceInfo) DevicePollerConfig {
	config := DevicePollerConfig{
		DeviceInfo:      deviceInfo,
		PollInterval:    dt.config.DefaultPollInterval,
		PollTimeout:     dt.config.DefaultPollTimeout,
		WatchdogTimeout: dt.config.WatchdogTimeout,
		MaxRetries:      dt.config.MaxRetries,
		RetryDelay:      500 * time.Millisecond, // 默认重试延迟
		BufferSize:      100,                    // 默认缓冲区大小
	}

	// 根据设备类型调整配置
	switch device.Type {
	// case models.DeviceTypeWayneOverheadController, models.DeviceTypeWayneSubmersibleController:
	// 	// Wayne控制器配置
	// 	config.PollInterval = 2 * time.Second      // 控制器轮询间隔稍长
	// 	config.PollTimeout = 50 * time.Millisecond // 控制器响应时间可能更长
	// 	config.MaxRetries = 3

	// case models.DeviceTypeWaynePump:
	// 	// Wayne泵配置
	// 	config.PollInterval = 1 * time.Second      // 泵需要更频繁的轮询
	// 	config.PollTimeout = 25 * time.Millisecond // DART协议标准超时
	// 	config.MaxRetries = 2

	default:
		// 使用默认配置
	}

	// 根据设备健康状态调整配置
	switch device.Health {
	case models.DeviceHealthHealthy:
		// 健康设备使用标准配置
	case models.DeviceHealthWarning:
		// 警告状态的设备降低轮询频率
		config.PollInterval = config.PollInterval * 2
		config.MaxRetries = config.MaxRetries + 1

	case models.DeviceHealthCritical:
		// 严重状态的设备使用保守配置
		config.PollInterval = config.PollInterval * 3
		config.PollTimeout = config.PollTimeout * 2
		config.MaxRetries = 1
		config.RetryDelay = config.RetryDelay * 2 // 增加重试延迟
	}

	// 从设备元数据中读取自定义配置
	if device.Metadata != nil {
		if pollInterval, ok := device.Metadata["poll_interval"]; ok {
			if intervalStr, ok := pollInterval.(string); ok {
				if interval, err := time.ParseDuration(intervalStr); err == nil {
					config.PollInterval = interval
				}
			}
		}

		if pollTimeout, ok := device.Metadata["poll_timeout"]; ok {
			if timeoutStr, ok := pollTimeout.(string); ok {
				if timeout, err := time.ParseDuration(timeoutStr); err == nil {
					config.PollTimeout = timeout
				}
			}
		}

		if bufferSize, ok := device.Metadata["buffer_size"]; ok {
			if bufSize, ok := bufferSize.(int); ok {
				config.BufferSize = bufSize
			}
		}
	}

	return config
}

// RefreshDevicesFromManager 从设备管理器刷新设备
func (dt *DispatchTask) RefreshDevicesFromManager(deviceManager api.DeviceManager) error {
	if deviceManager == nil {
		return fmt.Errorf("device manager cannot be nil")
	}

	dt.logger.Info("Refreshing devices from device manager")

	ctx := context.Background()

	// 获取当前系统中的所有设备
	devices, err := deviceManager.ListDevices(ctx, api.DeviceFilter{})
	if err != nil {
		return fmt.Errorf("failed to list devices from device manager: %w", err)
	}

	// 创建设备ID映射
	currentDevices := make(map[string]*models.Device)
	for _, device := range devices {
		currentDevices[device.ID] = device
	}

	// 获取调度任务中已注册的设备
	registeredDevices := make(map[string]bool)
	dt.pollers.Range(func(key string, value *DevicePoller) bool {
		registeredDevices[key] = true
		return true
	})

	var added, removed, updated int

	// 添加新设备
	for deviceID, device := range currentDevices {
		if !registeredDevices[deviceID] {
			// 新设备，需要注册
			deviceInfo, err := dt.convertDeviceToDeviceInfo(device)
			if err != nil {
				dt.logger.Warn("Failed to convert new device info",
					zap.String("device_id", deviceID),
					zap.Error(err))
				continue
			}

			pollerConfig := dt.generatePollerConfig(device, deviceInfo)
			if err := dt.RegisterDevice(deviceInfo, pollerConfig); err != nil {
				dt.logger.Warn("Failed to register new device",
					zap.String("device_id", deviceID),
					zap.Error(err))
				continue
			}

			added++
			dt.logger.Info("New device added during refresh",
				zap.String("device_id", deviceID))
		} else {
			// 现有设备，检查是否需要更新配置
			updated++
		}
	}

	// 移除不存在的设备
	for deviceID := range registeredDevices {
		if _, exists := currentDevices[deviceID]; !exists {
			if err := dt.UnregisterDevice(deviceID); err != nil {
				dt.logger.Warn("Failed to unregister removed device",
					zap.String("device_id", deviceID),
					zap.Error(err))
				continue
			}

			removed++
			dt.logger.Info("Device removed during refresh",
				zap.String("device_id", deviceID))
		}
	}

	dt.logger.Info("Device refresh completed",
		zap.Int("total_current", len(currentDevices)),
		zap.Int("added", added),
		zap.Int("removed", removed),
		zap.Int("updated", updated))

	return nil
}

// createCommunicationForDevice 为设备创建或获取共享通信接口 - 修复版本
func (dt *DispatchTask) createCommunicationForDevice(deviceInfo v2models.DeviceInfo) (CommunicationInterface, error) {
	// 获取设备串口信息
	serialPort := dt.getDeviceSerialPort(deviceInfo)

	// 检查是否已有该串口的通信实例
	dt.commMu.RLock()
	if comm, exists := dt.commInstances[serialPort]; exists {
		dt.commMu.RUnlock()
		dt.logger.Debug("复用已存在的通信实例",
			zap.String("device_id", deviceInfo.ID),
			zap.Uint8("device_address", deviceInfo.Address),
			zap.String("serial_port", serialPort))
		return comm, nil
	}
	dt.commMu.RUnlock()

	// 创建新的通信实例（双重检查）
	dt.commMu.Lock()
	defer dt.commMu.Unlock()

	// 再次检查是否已经有其他goroutine创建了
	if comm, exists := dt.commInstances[serialPort]; exists {
		dt.logger.Debug("复用刚创建的通信实例",
			zap.String("device_id", deviceInfo.ID),
			zap.Uint8("device_address", deviceInfo.Address),
			zap.String("serial_port", serialPort))
		return comm, nil
	}

	// 创建新的串口传输 - 注意：这里不传递deviceInfo.Address，因为会被多个设备共享
	dt.logger.Info("为串口创建新的通信实例",
		zap.String("device_id", deviceInfo.ID),
		zap.Uint8("device_address", deviceInfo.Address),
		zap.String("serial_port", serialPort))
	transport, err := NewSerialTransport(
		fmt.Sprintf("shared_%s", serialPort), // 使用串口名作为ID
		0,                                    // 地址设为0，因为会被多个设备共享
		serialPort,
		dt.logger,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create serial transport: %w", err)
	}

	// 缓存通信实例
	dt.commInstances[serialPort] = transport

	dt.logger.Info("✅ 新通信实例已创建并缓存",
		zap.String("serial_port", serialPort),
		zap.String("shared_id", fmt.Sprintf("shared_%s", serialPort)))

	return transport, nil
}

// getDeviceSerialPort 获取设备串口 - MVP 实现
func (dt *DispatchTask) getDeviceSerialPort(deviceInfo v2models.DeviceInfo) string {
	// 如果有设备管理器，尝试从设备管理器获取串口配置
	if dt.deviceManager != nil {
		ctx := context.Background()
		if device, err := dt.deviceManager.GetDevice(ctx, deviceInfo.ID); err == nil {
			if device.Metadata != nil {
				// 尝试从 metadata 获取串口配置
				if serialPort, ok := device.Metadata["serial_port"].(string); ok && serialPort != "" {
					dt.logger.Debug("从设备metadata获取串口配置",
						zap.String("device_id", deviceInfo.ID),
						zap.String("serial_port", serialPort))
					return serialPort
				}

				// 尝试从 ConnectionString 字段获取
				if connStr, ok := device.Metadata["connection_string"].(string); ok && connStr != "" {
					dt.logger.Debug("从设备ConnectionString获取串口配置",
						zap.String("device_id", deviceInfo.ID),
						zap.String("connection_string", connStr))
					return connStr
				}
			}

			// 尝试从Controller配置获取串口配置
			if device.Controller.Config.SerialPort != "" {
				dt.logger.Debug("从控制器配置获取串口配置",
					zap.String("device_id", deviceInfo.ID),
					zap.String("controller_id", device.ControllerID),
					zap.String("serial_port", device.Controller.Config.SerialPort))
				return device.Controller.Config.SerialPort
			}
		} else {
			dt.logger.Warn("无法从设备管理器获取设备信息",
				zap.String("device_id", deviceInfo.ID),
				zap.Error(err))
		}
	}

	// 使用默认串口映射
	dt.logger.Debug("使用默认串口映射",
		zap.String("device_id", deviceInfo.ID),
		zap.Uint8("device_address", deviceInfo.Address))
	return dt.getDefaultSerialPort(deviceInfo.Address)
}

// getDefaultSerialPort 根据设备地址获取默认串口
func (dt *DispatchTask) getDefaultSerialPort(address byte) string {
	// Linux 风格的默认串口映射（适应实际环境）
	portMap := map[byte]string{
		0x50: "/dev/ttyUSB0",
		0x51: "/dev/ttyUSB1",
		0x52: "/dev/ttyUSB2",
		0x53: "/dev/ttyUSB3",
		0x54: "/dev/ttyUSB4",
		0x55: "/dev/ttyUSB5",
		0x56: "/dev/ttyUSB6",
		0x57: "/dev/ttyUSB7",
	}

	if port, exists := portMap[address]; exists {
		dt.logger.Debug("使用预定义串口映射",
			zap.Uint8("device_address", address),
			zap.String("serial_port", port))
		return port
	}

	// 如果没有预定义映射，使用计算得出的串口
	portNum := int(address) - 0x50
	serialPort := fmt.Sprintf("/dev/ttyUSB%d", portNum)

	dt.logger.Debug("使用计算的串口映射",
		zap.Uint8("device_address", address),
		zap.String("serial_port", serialPort))

	return serialPort
}

// startDeviceWithRetry 启动设备，增加串口重试机制处理"Serial port busy"
func (dt *DispatchTask) startDeviceWithRetry(deviceID string, maxRetries int) error {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		dt.logger.Info("🔄 Starting device",
			zap.String("device_id", deviceID),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", maxRetries))

		err := dt.StartDevice(deviceID)
		if err == nil {
			dt.logger.Info("✅ Device started successfully",
				zap.String("device_id", deviceID),
				zap.Int("attempts", attempt))
			return nil
		}

		lastErr = err

		// 检查是否是串口忙的错误
		if attempt < maxRetries {
			isSerialBusy := false
			errStr := err.Error()

			// 检查常见的串口忙错误消息
			if strings.Contains(errStr, "Serial port busy") ||
				strings.Contains(errStr, "port busy") ||
				strings.Contains(errStr, "device busy") ||
				strings.Contains(errStr, "access denied") {
				isSerialBusy = true
			}

			if isSerialBusy {
				waitTime := time.Duration(attempt) * 500 * time.Millisecond
				dt.logger.Warn("🔌 Serial port busy, waiting before retry",
					zap.String("device_id", deviceID),
					zap.Int("attempt", attempt),
					zap.Duration("wait_time", waitTime),
					zap.Error(err))

				time.Sleep(waitTime)
			} else {
				// 非串口忙错误，等待更短时间
				waitTime := 100 * time.Millisecond
				dt.logger.Warn("Device start failed, retrying",
					zap.String("device_id", deviceID),
					zap.Int("attempt", attempt),
					zap.Duration("wait_time", waitTime),
					zap.Error(err))

				time.Sleep(waitTime)
			}
		}
	}

	dt.logger.Error("❌ Device start failed after all retries",
		zap.String("device_id", deviceID),
		zap.Int("max_retries", maxRetries),
		zap.Error(lastErr))

	return fmt.Errorf("failed to start device after %d retries: %w", maxRetries, lastErr)
}

// 🚀 新增：清理未使用的通信实例
// TODO: 需要实现通信实例清理逻辑，当前暂时保留所有实例

// NewDispatchTaskWithoutAutoRegister 创建调度任务但不自动注册设备（用于测试）
func NewDispatchTaskWithoutAutoRegister(config DispatchTaskConfig, stateManager v2models.DeviceStateManager, nozzleService nozzle.ServiceV2, logger *zap.Logger) *DispatchTask {
	return NewDispatchTask(config, stateManager, nozzleService, nil, nil, nil, nil, nil, nil, nil, logger)
}
