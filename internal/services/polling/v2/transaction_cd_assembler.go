package v2

import (
	"fmt"
	"time"

	"context"
	"fcc-service/internal/services/nozzle"

	"sync/atomic"
	"unsafe"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// AuthorizeDevice 授权设备
func (p *DevicePoller) AuthorizeDevice(employeeID string, nozzleNumber ...byte) error {
	// 在授权开始时ping看门狗
	p.watchdog.Ping()

	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	// TX序列号将在实际执行时动态分配，确保严格顺序
	cmd, err := p.cdBuilder.BuildAuthorizeCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建授权命令失败: %w", err)
	}

	// 在发送命令前再次ping看门狗
	p.watchdog.Ping()

	if err := p.SendCommand(*cmd); err != nil {
		return err
	}

	// 缓存员工ID
	if employeeID != "" {
		p.operatorIDCache.Set(employeeID)
	}

	// 授权完成后再次ping看门狗，维持活动状态
	p.watchdog.Ping()

	return nil
}

func (p *DevicePoller) ResetDevice() error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildResetCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建重置命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

func (p *DevicePoller) PresetVolume(volume decimal.Decimal, employeeID string) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildPresetVolumeCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		volume,
	)
	if err != nil {
		return fmt.Errorf("构建预设体积命令失败: %w", err)
	}

	if err := p.SendCommand(*cmd); err != nil {
		return err
	}

	// 缓存员工ID
	if employeeID != "" {
		p.operatorIDCache.Set(employeeID)
	}

	return nil
}

func (p *DevicePoller) PresetAmount(amount decimal.Decimal, employeeID string) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildPresetAmountCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		amount,
	)
	if err != nil {
		return fmt.Errorf("构建预设金额命令失败: %w", err)
	}

	if err := p.SendCommand(*cmd); err != nil {
		return err
	}

	// 缓存员工ID
	if employeeID != "" {
		p.operatorIDCache.Set(employeeID)
	}

	return nil
}

func (p *DevicePoller) RequestCounters(counterType CounterTypeEnum) error {
	return p.RequestCountersWithPriority(counterType, PriorityLow) // 默认低优先级
}

// RequestCountersWithPriority 发送指定优先级的计数器请求
func (p *DevicePoller) RequestCountersWithPriority(counterType CounterTypeEnum, priority CommandPriority) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildRequestCountersCommandWithPriority(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		byte(counterType),
		priority, // 🔧 支持指定优先级
	)

	if err != nil {
		return fmt.Errorf("构建请求计数器命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

// ReturnStatus 请求设备状态（CD1命令）
func (p *DevicePoller) ReturnStatus() error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildReturnStatusCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建状态查询命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

// ReturnFillingInformation 请求加油信息（CD1命令变体）
func (p *DevicePoller) ReturnFillingInformation() error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildReturnFillingInfoCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建加油信息查询命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

// ConfigureNozzles 配置喷嘴（CD2命令）
func (p *DevicePoller) ConfigureNozzles(nozzles []byte) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildConfigureNozzlesCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		nozzles,
	)
	if err != nil {
		return fmt.Errorf("构建喷嘴配置命令失败: %w", err)
	}

	// 🎯 解决方案：缓存CD2配置的喷嘴信息，用于解决DC1 AUTHORIZED状态无法获取喷嘴的问题
	p.cacheConfiguredNozzles(nozzles)

	// 发送CD2命令
	if err := p.SendCommand(*cmd); err != nil {
		return fmt.Errorf("发送喷嘴配置命令失败: %w", err)
	}

	// 🚀 新增：更新数据库中的喷嘴启用状态
	/*
		ctx := context.Background()

		// 先将所有喷嘴设为禁用状态
		if err := p.updateAllNozzlesEnabled(ctx, false); err != nil {
			p.logger.Error("更新所有喷嘴为禁用状态失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Error(err))
			// 不返回错误，因为命令已经发送成功，数据库更新失败不应该影响命令执行
		}

		// 根据CD2命令参数启用相应的喷嘴
		// nozzles 参数是喷嘴编号列表，例如：[1, 3, 4] 表示启用喷嘴1、3、4
		for _, nozzleNumber := range nozzles {
			// 验证喷嘴编号范围
			if nozzleNumber < 1 || nozzleNumber > 15 {
				p.logger.Warn("无效的喷嘴编号，跳过",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Uint8("nozzle_number", nozzleNumber))
				continue
			}

			if err := p.updateNozzleEnabled(ctx, nozzleNumber, true); err != nil {
				p.logger.Error("更新喷嘴启用状态失败",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Uint8("nozzle_number", nozzleNumber),
					zap.Error(err))
				// 继续处理其他喷嘴，不中断流程
			} else {
				p.logger.Info("喷嘴启用状态已更新",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Uint8("nozzle_number", nozzleNumber))
			}
		}
	*/

	return nil
}

// EnableOneNozzle 启用单个喷嘴（基于CD2命令）
func (p *DevicePoller) EnableOneNozzle(nozzleNumber byte) error {
	// 验证喷嘴编号
	if nozzleNumber < 1 || nozzleNumber > 15 {
		return fmt.Errorf("无效的喷嘴编号: %d，必须在1-15之间", nozzleNumber)
	}

	// 构建CD2命令数据：启用单个喷嘴
	// 格式：[TRANS][LNG][NOZ1]
	// TRANS: 0x02 (CD2命令)
	// LNG: 0x01 (1个喷嘴)
	// NOZ1: nozzleNumber (喷嘴编号)
	nozzleConfig := []byte{nozzleNumber}

	p.logger.Info("启用单个喷嘴",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("nozzle_number", nozzleNumber))

	// 🎯 缓存单个喷嘴配置
	p.cacheConfiguredNozzles(nozzleConfig)

	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildConfigureNozzlesCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		nozzleConfig,
	)
	if err != nil {
		return fmt.Errorf("构建启用喷嘴命令失败: %w", err)
	}

	// 发送CD2命令
	if err := p.SendCommand(*cmd); err != nil {
		return fmt.Errorf("发送启用喷嘴命令失败: %w", err)
	}

	// 🚀 更新数据库中的喷嘴启用状态
	ctx := context.Background()

	// 先将所有喷嘴设为禁用状态
	if err := p.updateAllNozzlesEnabled(ctx, false); err != nil {
		p.logger.Error("更新所有喷嘴为禁用状态失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Error(err))
		// 不返回错误，因为命令已经发送成功
	}

	// 只启用指定喷嘴
	if err := p.updateNozzleEnabled(ctx, nozzleNumber, true); err != nil {
		p.logger.Error("更新喷嘴启用状态失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("nozzle_number", nozzleNumber),
			zap.Error(err))
		// 不返回错误，因为命令已经发送成功
	}

	p.logger.Info("单个喷嘴启用成功",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("nozzle_number", nozzleNumber))

	return nil
}

// UpdatePrices 更新价格（CD5命令）
func (p *DevicePoller) UpdatePrices(prices []nozzle.NozzlePriceInfo) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildUpdatePricesCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		prices,
	)
	if err != nil {
		return fmt.Errorf("构建价格更新命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

// SuspendNozzle 暂停喷嘴（CD14命令）
func (p *DevicePoller) SuspendNozzle(nozzleNumber byte) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildSuspendNozzleCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		nozzleNumber,
	)
	if err != nil {
		return fmt.Errorf("构建暂停喷嘴命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

// ResumeNozzle 恢复喷嘴（CD15命令）
func (p *DevicePoller) ResumeNozzle(nozzleNumber byte) error {
	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildResumeNozzleCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
		nozzleNumber,
	)
	if err != nil {
		return fmt.Errorf("构建恢复喷嘴命令失败: %w", err)
	}

	return p.SendCommand(*cmd)
}

// StopDevice 停止设备（发送停止命令）
func (p *DevicePoller) StopDevice() error {
	// 获取当前设备状态
	deviceState := p.deviceSM.GetStateData()
	currentPumpStatus := deviceState.PumpStatus

	p.logger.Info("执行stop命令",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("current_pump_status", byte(currentPumpStatus)),
		zap.String("current_status_name", p.getPumpStatusName(byte(currentPumpStatus))))

	// 🔧 修复TX序列号管理：移除构建时的TX序列号依赖
	cmd, err := p.cdBuilder.BuildStopCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建停止命令失败: %w", err)
	}

	// 发送stop命令
	if err := p.SendCommand(*cmd); err != nil {
		return fmt.Errorf("发送停止命令失败: %w", err)
	}

	// 🔧 DART协议遵循：发送stop命令后，数据库状态同步更新，但设备状态机状态等待DC1事务确认
	// 协议要求：主机发送stop命令，设备通过DC1事务报告状态变化，主机不应主动设置设备状态
	if currentPumpStatus == 0x02 { // AUTHORIZED状态
		p.logger.Info("授权状态下执行stop命令，更新数据库状态为completed（设备状态等待DC1确认）",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("protocol_basis", "Wayne DART: 主机发送STOP，设备通过DC1报告状态变化"))

		// ❌ DART协议违规：喷嘴状态只能通过DC3事务更新
		// 移除主动的喷嘴状态更新，等待设备通过DC3事务报告
		/*
			// 异步更新数据库状态，避免阻塞命令响应
			go func() {
				ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()

				// 短暂延迟，让设备有时间处理stop命令
				time.Sleep(50 * time.Millisecond)

				// 🔧 修复：授权状态下可能没有活跃喷嘴数据，直接更新authorized状态的喷嘴
				if err := p.handleAuthorizedStopToCompleted(ctx); err != nil {
					p.logger.Error("授权状态stop命令后更新数据库状态失败",
						zap.String("device_id", p.config.DeviceInfo.ID),
						zap.Error(err))
				} else {
					p.logger.Info("✅ 授权状态stop命令后数据库状态已同步更新为completed（等待设备DC1确认状态机状态）",
						zap.String("device_id", p.config.DeviceInfo.ID))
				}
			}()
		*/
		p.logger.Info("🔧 STOP命令已发送，等待设备通过DC1/DC3事务报告状态变化",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("protocol_compliance", "喷嘴状态由设备DC3事务驱动"))
	} else if currentPumpStatus == 0x04 { // FILLING状态
		p.logger.Info("加油状态下执行stop命令，更新数据库状态为completed（设备状态等待DC1确认）",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("protocol_basis", "Wayne DART: 主机发送STOP，设备通过DC1报告状态变化"))

		// ❌ DART协议违规：喷嘴状态只能通过DC3事务更新
		// 移除主动的喷嘴状态更新，等待设备通过DC3事务报告
		/*
			// 异步更新数据库状态
			go func() {
				ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()

				// 短暂延迟，让设备有时间处理stop命令
				time.Sleep(50 * time.Millisecond)

				// 🔧 DART协议修复：移除主动状态设置，等待设备DC1事务确认状态变化
				// p.deviceSM.UpdatePumpStatus(0x05) // 违反协议 - 状态应由设备DC1事务报告

				// 更新数据库状态
				if err := p.handleFillingCompletedState(ctx); err != nil {
					p.logger.Error("stop命令后更新数据库状态失败",
						zap.String("device_id", p.config.DeviceInfo.ID),
						zap.Error(err))
				} else {
					p.logger.Info("✅ stop命令后数据库状态已同步更新为completed（等待设备DC1确认状态机状态）",
						zap.String("device_id", p.config.DeviceInfo.ID))
				}
			}()
		*/
		p.logger.Info("🔧 STOP命令已发送，等待设备通过DC1/DC3事务报告状态变化",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("protocol_compliance", "喷嘴状态由设备DC3事务驱动"))
	}

	return nil
}

// ResumeFuellingPoint 恢复加油点（发送CD1恢复加油点命令）
func (p *DevicePoller) ResumeFuellingPoint() error {
	// 获取当前设备状态
	deviceState := p.deviceSM.GetStateData()
	currentPumpStatus := deviceState.PumpStatus

	p.logger.Info("执行resume fuelling point命令",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("current_pump_status", byte(currentPumpStatus)),
		zap.String("current_status_name", p.getPumpStatusName(byte(currentPumpStatus))))

	// 构建CD1恢复加油点命令
	cmd, err := p.cdBuilder.BuildResumeFuellingPointCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // 占位符，实际TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建恢复加油点命令失败: %w", err)
	}

	// 发送resume fuelling point命令
	if err := p.SendCommand(*cmd); err != nil {
		return fmt.Errorf("发送恢复加油点命令失败: %w", err)
	}

	p.logger.Info("恢复加油点命令已发送，等待设备通过DC1事务确认状态变化",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("protocol_compliance", "CD1(0x0E) Resume Fuelling Point命令"))

	return nil
}

// handleAuthorizedStopToCompleted 处理授权状态下stop命令的数据库更新
// 专门用于授权状态下没有活跃喷嘴数据的情况
// func (p *DevicePoller) handleAuthorizedStopToCompleted(ctx context.Context) error {
// 	p.logger.Info("处理授权状态下stop命令的数据库更新",
// 		zap.String("device_id", p.config.DeviceInfo.ID))

// 	// 获取设备的所有喷嘴
// 	nozzles, err := p.nozzleService.GetDeviceNozzles(ctx, p.config.DeviceInfo.ID)
// 	if err != nil {
// 		p.logger.Error("获取设备喷嘴失败", zap.Error(err))
// 		return fmt.Errorf("获取设备喷嘴失败: %w", err)
// 	}

// 	// 查找处于authorized状态的喷嘴并更新为completed
// 	var authorizedNozzles []byte
// 	var completedCount int

// 	for _, nozzle := range nozzles {
// 		if nozzle.IsEnabled && nozzle.Status == models.NozzleStatusAuthorized {
// 			authorizedNozzles = append(authorizedNozzles, nozzle.Number)

// 			// 更新为completed状态
// 			if err := p.nozzleService.UpdateNozzleStatusDirect(ctx, p.config.DeviceInfo.ID, nozzle.Number, models.NozzleStatusCompleted); err != nil {
// 				p.logger.Error("设置授权喷嘴为completed状态失败",
// 					zap.String("device_id", p.config.DeviceInfo.ID),
// 					zap.Uint8("nozzle_id", nozzle.Number),
// 					zap.Error(err))
// 			} else {
// 				completedCount++
// 			}
// 		} else if nozzle.IsEnabled && nozzle.Status != models.NozzleStatusCompleted {
// 			// 将其他非completed状态的喷嘴设为idle
// 			if err := p.nozzleService.UpdateNozzleStatusDirect(ctx, p.config.DeviceInfo.ID, nozzle.Number, models.NozzleStatusIdle); err != nil {
// 				p.logger.Error("设置其他喷嘴为idle状态失败",
// 					zap.String("device_id", p.config.DeviceInfo.ID),
// 					zap.Uint8("nozzle_id", nozzle.Number),
// 					zap.Error(err))
// 			}
// 		}
// 	}

// 	if len(authorizedNozzles) == 0 {
// 		p.logger.Warn("未找到授权状态的喷嘴，可能设备状态不一致",
// 			zap.String("device_id", p.config.DeviceInfo.ID))
// 		return nil
// 	}

// 	p.logger.Info("✅ 授权状态下stop命令数据库更新完成",
// 		zap.String("device_id", p.config.DeviceInfo.ID),
// 		zap.Any("authorized_nozzles", authorizedNozzles),
// 		zap.Int("completed_count", completedCount))

// 	return nil
// }

// 🎯 解决方案：CD2喷嘴配置缓存，解决DC1 AUTHORIZED状态无法获取喷嘴信息的问题

// cacheConfiguredNozzles 缓存CD2命令配置的喷嘴信息
// 这是解决"DC1 auth状态无法获取到是哪个nozzle"问题的关键方法
func (p *DevicePoller) cacheConfiguredNozzles(nozzles []byte) {
	if len(nozzles) == 0 {
		p.logger.Warn("CD2配置空喷嘴列表",
			zap.String("device_id", p.config.DeviceInfo.ID))
		return
	}

	// 验证和过滤有效的喷嘴编号
	validNozzles := make([]byte, 0, len(nozzles))
	for _, nozzle := range nozzles {
		if nozzle >= 1 && nozzle <= 15 { // Wayne DART协议：喷嘴编号1-15
			validNozzles = append(validNozzles, nozzle)
		} else {
			p.logger.Warn("CD2配置无效喷嘴编号",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("invalid_nozzle", nozzle))
		}
	}

	if len(validNozzles) == 0 {
		p.logger.Error("CD2配置无有效喷嘴",
			zap.String("device_id", p.config.DeviceInfo.ID))
		return
	}

	p.logger.Info("缓存CD2配置的喷嘴信息",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Any("configured_nozzles", validNozzles),
		zap.Int("nozzle_count", len(validNozzles)))

	// 🎯 使用原子操作缓存CD2配置的喷嘴信息
	nozzlesCopy := make([]byte, len(validNozzles))
	copy(nozzlesCopy, validNozzles)
	atomic.StorePointer(&p.lastConfiguredNozzles, unsafe.Pointer(&nozzlesCopy))
	atomic.StoreInt64(&p.configuredAt, time.Now().UnixNano())

	p.logger.Debug("CD2喷嘴配置已缓存(原子操作)",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Any("cached_nozzles", nozzlesCopy),
		zap.Int64("configured_at_nano", atomic.LoadInt64(&p.configuredAt)))
}

// GetAuthorizedNozzles 获取当前授权状态下的喷嘴信息
// 🎯 解决方案核心：基于最后发送的CD2命令来推断当前授权的喷嘴
func (p *DevicePoller) GetAuthorizedNozzles() ([]byte, error) {
	// 检查设备是否处于授权状态
	deviceState := p.deviceSM.GetStateData()
	if deviceState.PumpStatus != 0x02 { // AUTHORIZED状态
		return nil, fmt.Errorf("设备不在授权状态，当前状态: 0x%02X", deviceState.PumpStatus)
	}

	// 🎯 使用原子操作从缓存中获取最后配置的喷嘴
	nozzlesPtr := atomic.LoadPointer(&p.lastConfiguredNozzles)
	if nozzlesPtr == nil {
		return nil, fmt.Errorf("未找到CD2配置的喷嘴信息")
	}

	// 获取喷嘴slice的指针并创建副本
	nozzlesSlice := (*[]byte)(nozzlesPtr)
	if len(*nozzlesSlice) == 0 {
		return nil, fmt.Errorf("CD2配置的喷嘴列表为空")
	}

	// 检查配置是否过期（可选的安全检查）
	configuredAtNano := atomic.LoadInt64(&p.configuredAt)
	if configuredAtNano > 0 {
		configuredTime := time.Unix(0, configuredAtNano)
		if time.Since(configuredTime) > 10*time.Minute {
			p.logger.Warn("CD2喷嘴配置可能已过期",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Duration("age", time.Since(configuredTime)))
		}
	}

	// 返回配置喷嘴的副本
	result := make([]byte, len(*nozzlesSlice))
	copy(result, *nozzlesSlice)

	p.logger.Debug("获取授权喷嘴信息(原子操作)",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Any("authorized_nozzles", result),
		zap.Int64("configured_at_nano", configuredAtNano))

	return result, nil
}

// sendCD1_04H_Command 发送CD1 04H命令获取最终加油信息
// 这是智能完成机制的核心部分，用于主动获取最终DC2数据
func (p *DevicePoller) sendCD1_04H_Command() error {
	p.logger.Info("[DevicePoller] 🚀 发送CD1 04H命令获取最终加油信息",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("command", "ReturnFillingInformation"),
		zap.String("purpose", "智能完成机制获取最终DC2数据"))

	// 构建CD1 04H (ReturnFillingInformation) 命令
	cmd, err := p.cdBuilder.BuildReturnFillingInfoCommand(
		p.config.DeviceInfo.ID,
		p.config.DeviceInfo.Address,
		0, // TX序列号在executeCommand中分配
	)
	if err != nil {
		return fmt.Errorf("构建CD1 04H命令失败: %w", err)
	}

	// 发送命令
	if err := p.SendCommand(*cmd); err != nil {
		return fmt.Errorf("发送CD1 04H命令失败: %w", err)
	}

	p.logger.Info("[DevicePoller] ✅ CD1 04H命令已发送，等待设备响应最终DC2数据",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("next_step", "等待DC2响应触发"))

	return nil
}
