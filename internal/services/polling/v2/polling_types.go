package v2

import (
	"context"
	"sync"
	"time"

	"fcc-service/pkg/models"
	v2models "fcc-service/pkg/models/v2"
)

// PollResult 轮询结果
type PollResult struct {
	DeviceID     string        `json:"device_id"`     // 设备ID
	Success      bool          `json:"success"`       // 是否成功
	ResponseTime time.Duration `json:"response_time"` // 响应时间
	Data         interface{}   `json:"data"`          // 响应数据
	Error        error         `json:"error"`         // 错误信息
	Timestamp    time.Time     `json:"timestamp"`     // 时间戳

	// 新增：业务相关信息
	BusinessType string `json:"business_type"` // 业务类型
	CommandType  string `json:"command_type"`  // 命令类型
	StateChanged bool   `json:"state_changed"` // 是否引起状态变更
}

// PollCommand 轮询命令
type PollCommand struct {
	Type      string        `json:"type"`      // 命令类型: poll, data, configure
	Data      interface{}   `json:"data"`      // 命令数据
	Timeout   time.Duration `json:"timeout"`   // 超时时间
	Timestamp time.Time     `json:"timestamp"` // 命令时间

	// 新增：业务相关信息
	BusinessType string `json:"business_type"` // 业务类型：authorize, preset, price_update等
	Priority     int    `json:"priority"`      // 命令优先级：1=高，2=中，3=低
	Retryable    bool   `json:"retryable"`     // 是否可重试
	MaxRetries   int    `json:"max_retries"`   // 最大重试次数
}

// DevicePollerConfig 设备轮询器配置
type DevicePollerConfig struct {
	DeviceInfo      v2models.DeviceInfo `json:"device_info"`      // 设备信息
	PollInterval    time.Duration       `json:"poll_interval"`    // 轮询间隔
	PollTimeout     time.Duration       `json:"poll_timeout"`     // 轮询超时
	MaxRetries      int                 `json:"max_retries"`      // 最大重试次数
	RetryDelay      time.Duration       `json:"retry_delay"`      // 重试延迟
	WatchdogTimeout time.Duration       `json:"watchdog_timeout"` // 看门狗超时
	BufferSize      int                 `json:"buffer_size"`      // channel缓冲区大小
	// 🚀 新增：增强轮询功能配置 (MVP)
	EnhancedPollingEnabled bool `json:"enhanced_polling_enabled"` // 是否启用增强轮询
	// 🔧 新增：设备轮询调度配置
	PollingSequenceIndex int `json:"polling_sequence_index"` // 设备在轮询序列中的索引 (0, 1, 2...)

	// 🆕 MVP增强配置
	SkipTotalCounters     bool          `json:"skip_total_counters"`     // 跳过总计数器(0x09, 0x19)
	NozzlePollingInterval time.Duration `json:"nozzle_polling_interval"` // 喷嘴轮询间隔
}

// CommandType 命令类型常量
type CommandType string

const (
	CommandTypePoll      CommandType = "poll"      // 轮询命令
	CommandTypeData      CommandType = "data"      // 数据命令
	CommandTypeConfigure CommandType = "configure" // 配置命令
	CommandTypeAck       CommandType = "ack"       // 应答命令
)

// BusinessType 业务类型常量
type BusinessType string

const (
	BusinessTypeAuthorize   BusinessType = "authorize"    // 授权业务
	BusinessTypePreset      BusinessType = "preset"       // 预设业务
	BusinessTypePriceUpdate BusinessType = "price_update" // 价格更新
	BusinessTypeStatus      BusinessType = "status"       // 状态查询
	BusinessTypeStop        BusinessType = "stop"         // 停止业务
	BusinessTypeReset       BusinessType = "reset"        // 重置业务
	BusinessTypeFilling     BusinessType = "filling"      // 加油业务
	BusinessTypeMonitoring  BusinessType = "monitoring"   // 监控轮询
	BusinessTypeAck         BusinessType = "ack"          // 应答业务
)

// CommandPriority 命令优先级
type CommandPriority int

const (
	PriorityHigh   CommandPriority = 1 // 高优先级：紧急停止、安全相关
	PriorityMedium CommandPriority = 2 // 中优先级：授权、预设等业务命令
	PriorityLow    CommandPriority = 3 // 低优先级：状态查询、监控等
)

// PollStatus 轮询状态
type PollStatus string

const (
	PollStatusIdle       PollStatus = "idle"       // 空闲
	PollStatusPolling    PollStatus = "polling"    // 轮询中
	PollStatusWaiting    PollStatus = "waiting"    // 等待响应
	PollStatusProcessing PollStatus = "processing" // 处理中
	PollStatusRetrying   PollStatus = "retrying"   // 重试中
	PollStatusError      PollStatus = "error"      // 错误状态
)

// BusinessState 业务状态跟踪
type BusinessState struct {
	Type              BusinessType `json:"type"`               // 业务类型
	Status            string       `json:"status"`             // 业务状态
	StartTime         time.Time    `json:"start_time"`         // 开始时间
	LastCommandTime   time.Time    `json:"last_command_time"`  // 最后命令时间
	RetryCount        int          `json:"retry_count"`        // 重试次数
	Data              interface{}  `json:"data"`               // 业务数据
	NeedsContinuation bool         `json:"needs_continuation"` // 是否需要继续处理

	// Wayne DART协议相关
	LastTxSequence   byte   `json:"last_tx_sequence"`  // 最后发送的TX#
	ExpectedResponse string `json:"expected_response"` // 期望的响应类型

	mu sync.RWMutex `json:"-"`
}

// GetStatus 获取业务状态（线程安全）
func (bs *BusinessState) GetStatus() string {
	bs.mu.RLock()
	defer bs.mu.RUnlock()
	return bs.Status
}

// SetStatus 设置业务状态（线程安全）
func (bs *BusinessState) SetStatus(status string) {
	bs.mu.Lock()
	defer bs.mu.Unlock()
	bs.Status = status
}

// IsActive 检查业务是否处于活跃状态
func (bs *BusinessState) IsActive() bool {
	bs.mu.RLock()
	defer bs.mu.RUnlock()
	return bs.Status != "completed" && bs.Status != "failed" && bs.Status != "cancelled"
}

// ShouldContinue 检查是否需要继续处理
func (bs *BusinessState) ShouldContinue() bool {
	bs.mu.RLock()
	defer bs.mu.RUnlock()
	return bs.NeedsContinuation && bs.IsActive()
}

// PollerContext 轮询器上下文 - 跟踪当前轮询状态
type PollerContext struct {
	CurrentBusiness   *BusinessState `json:"current_business"`   // 当前业务状态
	PendingCommands   []PollCommand  `json:"pending_commands"`   // 待处理命令队列
	LastPollTime      time.Time      `json:"last_poll_time"`     // 最后轮询时间
	LastCommandTime   time.Time      `json:"last_command_time"`  // 最后命令时间
	ConsecutiveErrors int            `json:"consecutive_errors"` // 连续错误次数

	mu sync.RWMutex `json:"-"`
}

// HasPendingCommands 检查是否有待处理命令
func (pc *PollerContext) HasPendingCommands() bool {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	return len(pc.PendingCommands) > 0
}

// GetNextCommand 获取下一个最高优先级命令
func (pc *PollerContext) GetNextCommand() *PollCommand {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	if len(pc.PendingCommands) == 0 {
		return nil
	}

	// 按优先级排序，返回最高优先级命令
	highestPriority := PriorityLow + 1
	highestIndex := -1

	for i, cmd := range pc.PendingCommands {
		if CommandPriority(cmd.Priority) < highestPriority {
			highestPriority = CommandPriority(cmd.Priority)
			highestIndex = i
		}
	}

	if highestIndex == -1 {
		return nil
	}

	// 移除并返回命令
	cmd := pc.PendingCommands[highestIndex]
	pc.PendingCommands = append(pc.PendingCommands[:highestIndex], pc.PendingCommands[highestIndex+1:]...)

	return &cmd
}

// GetQueueSize 获取队列大小（线程安全）
func (pc *PollerContext) GetQueueSize() int {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	return len(pc.PendingCommands)
}

// AddCommand 添加命令到队列
func (pc *PollerContext) AddCommand(cmd PollCommand) {
	pc.mu.Lock()
	defer pc.mu.Unlock()
	pc.PendingCommands = append(pc.PendingCommands, cmd)
}

// HasActiveBusiness 检查是否有活跃业务
func (pc *PollerContext) HasActiveBusiness() bool {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	return pc.CurrentBusiness != nil && pc.CurrentBusiness.IsActive()
}

// ShouldContinueBusiness 检查是否应该继续处理当前业务
func (pc *PollerContext) ShouldContinueBusiness() bool {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	return pc.CurrentBusiness != nil && pc.CurrentBusiness.ShouldContinue()
}

// ResultHandler 结果处理器接口
type ResultHandler interface {
	HandleResult(result PollResult) error
	GetName() string // 新增：获取处理器名称，用于管理和日志
}

// DevicePollerInterface 设备轮询器接口
type DevicePollerInterface interface {
	Start(ctx context.Context) error
	Stop() error
	SendCommand(cmd PollCommand) error
	GetResultChannel() <-chan PollResult
	GetStats() PollerStats
	IsRunning() bool
	GetDeviceID() string
	GetLastActivity() time.Time

	// 新增：业务状态相关接口
	GetCurrentBusiness() *BusinessState
	GetPollerContext() *PollerContext

	// 🗑️ 移除：GetLastTxResetTime() 方法已移除，现在使用 DeviceRuntimeStateService
}

// ResponseProcessor 响应处理器接口
type ResponseProcessor interface {
	ProcessACKResponse(deviceID string, business *BusinessState) error
	ProcessNAKResponse(deviceID string, business *BusinessState) error
	ProcessDATAResponse(deviceID string, data []byte, business *BusinessState) error
	ProcessEOTResponse(deviceID string, business *BusinessState) error
}

// TransactionServiceInterface 交易服务接口 - 解耦循环依赖
type TransactionServiceInterface interface {
	// 核心持久化功能
	PersistDARTTransaction(ctx context.Context, transaction *models.DARTTransaction) error
}
