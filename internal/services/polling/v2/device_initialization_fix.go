package v2

import (
	"context"
	"fmt"
	"time"

	v2models "fcc-service/pkg/models/v2"

	"go.uber.org/zap"
)

// DeviceInitializationFix 设备初始化修复器
type DeviceInitializationFix struct {
	logger   *zap.Logger
	deviceSM v2models.DeviceStateMachine
	poller   *DevicePoller
}

// NewDeviceInitializationFix 创建设备初始化修复器
func NewDeviceInitializationFix(logger *zap.Logger, deviceSM v2models.DeviceStateMachine, poller *DevicePoller) *DeviceInitializationFix {
	return &DeviceInitializationFix{
		logger:   logger,
		deviceSM: deviceSM,
		poller:   poller,
	}
}

// FixInitializationState 修复初始化状态问题
// 这是针对 err.log 中 "cannot transition from initializing to polling" 问题的解决方案
func (fix *DeviceInitializationFix) FixInitializationState(ctx context.Context) error {
	currentState := fix.deviceSM.GetState()

	fix.logger.Info("开始修复设备初始化状态",
		zap.String("current_state", string(currentState)),
		zap.String("device_id", fix.poller.GetDeviceID()))

	// 根据当前状态决定修复策略
	switch currentState {
	case v2models.DeviceStateInitializing:
		return fix.fixFromInitializingState(ctx)
	case v2models.DeviceStateError:
		return fix.fixFromErrorState(ctx)
	case v2models.DeviceStateOffline:
		return fix.fixFromOfflineState(ctx)
	default:
		fix.logger.Info("✅ 设备状态正常，无需修复",
			zap.String("current_state", string(currentState)))
		return nil
	}
}

// fixFromInitializingState 修复从初始化状态开始的问题
func (fix *DeviceInitializationFix) fixFromInitializingState(ctx context.Context) error {
	fix.logger.Info("🔄 正在修复初始化状态...")

	// 步骤1：强制重置设备状态
	if err := fix.forceResetDeviceState(); err != nil {
		fix.logger.Warn("⚠️ 强制重置失败，尝试渐进式修复", zap.Error(err))
	}

	// 步骤2：发送设备重启信号 (TX#=0)
	if err := fix.sendDeviceRestartSignal(ctx); err != nil {
		fix.logger.Warn("⚠️ 设备重启信号发送失败", zap.Error(err))
	}

	// 步骤3：等待设备响应
	time.Sleep(500 * time.Millisecond)

	// 步骤4：尝试建立连接
	if err := fix.establishConnection(); err != nil {
		return fmt.Errorf("建立连接失败: %w", err)
	}

	// 步骤5：验证状态转换
	return fix.verifyStateTransition()
}

// fixFromErrorState 修复错误状态
func (fix *DeviceInitializationFix) fixFromErrorState(ctx context.Context) error {
	fix.logger.Info("🔄 正在修复错误状态...")

	// 首先重置状态机
	if err := fix.deviceSM.OnReset(); err != nil {
		fix.logger.Warn("状态机重置失败", zap.Error(err))
	}

	// 然后按照初始化流程处理
	return fix.fixFromInitializingState(ctx)
}

// fixFromOfflineState 修复离线状态
func (fix *DeviceInitializationFix) fixFromOfflineState(ctx context.Context) error {
	fix.logger.Info("🔄 正在修复离线状态...")

	// 尝试连接
	if err := fix.deviceSM.OnConnect(); err != nil {
		fix.logger.Warn("连接失败", zap.Error(err))
		return fix.fixFromInitializingState(ctx)
	}

	return fix.verifyStateTransition()
}

// forceResetDeviceState 强制重置设备状态
func (fix *DeviceInitializationFix) forceResetDeviceState() error {
	// 通过多种方式尝试重置
	resetMethods := []func() error{
		fix.deviceSM.OnReset,
		fix.deviceSM.OnDisconnect,
		func() error { return fix.deviceSM.OnConnect() },
	}

	for i, method := range resetMethods {
		if err := method(); err != nil {
			fix.logger.Debug("重置方法失败",
				zap.Int("method_index", i),
				zap.Error(err))
		} else {
			fix.logger.Debug("重置方法成功",
				zap.Int("method_index", i))
			return nil
		}
	}

	return fmt.Errorf("所有重置方法都失败")
}

// sendDeviceRestartSignal 发送设备重启信号
func (fix *DeviceInitializationFix) sendDeviceRestartSignal(ctx context.Context) error {
	fix.logger.Info("📡 发送设备重启信号 (TX#=0)")

	// 构建TX#=0重启信号
	restartCommand := PollCommand{
		Type:         string(CommandTypeConfigure),
		BusinessType: "initialization_tx_reset",
		Priority:     0,
		Timeout:      25 * time.Millisecond,
		Retryable:    false,
		Data:         []byte{0x00}, // TX#=0 indicates device restart
	}

	// 发送命令
	if err := fix.poller.SendCommand(restartCommand); err != nil {
		return fmt.Errorf("发送重启信号失败: %w", err)
	}

	fix.logger.Info("✅ 设备重启信号发送成功")
	return nil
}

// establishConnection 建立连接
func (fix *DeviceInitializationFix) establishConnection() error {
	fix.logger.Info("🔗 建立设备连接...")

	maxAttempts := 3
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		if err := fix.deviceSM.OnConnect(); err != nil {
			fix.logger.Warn("连接尝试失败",
				zap.Int("attempt", attempt),
				zap.Int("max_attempts", maxAttempts),
				zap.Error(err))

			if attempt < maxAttempts {
				time.Sleep(time.Duration(attempt) * 100 * time.Millisecond)
				continue
			}
			return fmt.Errorf("连接失败，已尝试 %d 次", maxAttempts)
		}

		fix.logger.Info("✅ 设备连接成功",
			zap.Int("attempt", attempt))
		return nil
	}

	return fmt.Errorf("连接建立失败")
}

// verifyStateTransition 验证状态转换
func (fix *DeviceInitializationFix) verifyStateTransition() error {
	// 等待状态稳定
	time.Sleep(200 * time.Millisecond)

	currentState := fix.deviceSM.GetState()

	// 检查状态是否正常
	validStates := []v2models.DeviceState{
		v2models.DeviceStateOnline,
		v2models.DeviceStateReady,
		v2models.DeviceStatePolling,
	}

	for _, validState := range validStates {
		if currentState == validState {
			fix.logger.Info("✅ 设备状态修复成功",
				zap.String("final_state", string(currentState)))
			return nil
		}
	}

	// 如果仍然不正常，尝试强制转换到在线状态
	if currentState == v2models.DeviceStateInitializing {
		fix.logger.Info("🔄 强制转换到在线状态...")

		if err := fix.deviceSM.OnConnect(); err != nil {
			fix.logger.Warn("强制转换失败", zap.Error(err))
		}

		// 再次检查
		time.Sleep(100 * time.Millisecond)
		newState := fix.deviceSM.GetState()

		if newState != currentState {
			fix.logger.Info("✅ 强制转换成功",
				zap.String("old_state", string(currentState)),
				zap.String("new_state", string(newState)))
			return nil
		}
	}

	return fmt.Errorf("状态验证失败，当前状态: %s", string(currentState))
}

// IsAuthorizationBlocked 检查授权是否被阻塞
// 基于 err.log 中 "command rejected by device" 错误的分析
func (fix *DeviceInitializationFix) IsAuthorizationBlocked() bool {
	currentState := fix.deviceSM.GetState()

	// 这些状态下授权会被设备拒绝
	blockedStates := []v2models.DeviceState{
		v2models.DeviceStateInitializing,
		v2models.DeviceStateError,
		v2models.DeviceStateOffline,
	}

	for _, blockedState := range blockedStates {
		if currentState == blockedState {
			fix.logger.Warn("⚠️ 授权被阻塞",
				zap.String("current_state", string(currentState)),
				zap.String("reason", "设备未完成初始化"))
			return true
		}
	}

	return false
}

// PreventAuthorizationRejection 预防授权被拒绝
func (fix *DeviceInitializationFix) PreventAuthorizationRejection(ctx context.Context) error {
	if !fix.IsAuthorizationBlocked() {
		return nil // 状态正常，无需处理
	}

	fix.logger.Info("🛡️ 检测到授权可能被拒绝，预防性修复...")

	// 先修复初始化状态
	if err := fix.FixInitializationState(ctx); err != nil {
		return fmt.Errorf("预防性修复失败: %w", err)
	}

	// 验证授权现在是否可用
	if fix.IsAuthorizationBlocked() {
		return fmt.Errorf("授权仍然被阻塞")
	}

	fix.logger.Info("✅ 授权预防性修复完成")
	return nil
}

// GetInitializationStatus 获取初始化状态报告
func (fix *DeviceInitializationFix) GetInitializationStatus() map[string]interface{} {
	currentState := fix.deviceSM.GetState()

	return map[string]interface{}{
		"current_state":       string(currentState),
		"is_initialized":      fix.isInitialized(),
		"can_accept_commands": fix.canAcceptCommands(),
		"authorization_ready": !fix.IsAuthorizationBlocked(),
		"last_check_time":     time.Now(),
		"recommendations":     fix.getRecommendations(),
	}
}

// isInitialized 检查是否已初始化
func (fix *DeviceInitializationFix) isInitialized() bool {
	currentState := fix.deviceSM.GetState()
	return currentState != v2models.DeviceStateInitializing &&
		currentState != v2models.DeviceStateOffline
}

// canAcceptCommands 检查是否可以接受命令
func (fix *DeviceInitializationFix) canAcceptCommands() bool {
	currentState := fix.deviceSM.GetState()
	return currentState == v2models.DeviceStateOnline ||
		currentState == v2models.DeviceStateReady ||
		currentState == v2models.DeviceStatePolling
}

// getRecommendations 获取建议
func (fix *DeviceInitializationFix) getRecommendations() []string {
	recommendations := []string{}

	if !fix.isInitialized() {
		recommendations = append(recommendations, "运行初始化修复")
	}

	if fix.IsAuthorizationBlocked() {
		recommendations = append(recommendations, "运行授权预防性修复")
	}

	if !fix.canAcceptCommands() {
		recommendations = append(recommendations, "检查设备连接状态")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "设备状态正常")
	}

	return recommendations
}
