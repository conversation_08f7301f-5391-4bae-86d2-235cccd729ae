package v2

import (
	"fmt"
	"time"

	"fcc-service/internal/adapters/wayne/dartline"
	"fcc-service/internal/adapters/wayne/pump"
	"fcc-service/internal/services/nozzle"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// 🎯 CDTransactionBuilder - 纯协议构建器
// 职责：
// 1. 构建类型安全的CD事务参数
// 2. 转换业务参数为协议数据
// 3. 构建DART帧
// 4. 输出PollCommand供DevicePoller处理
//
// 不负责：
// - 通信发送
// - 响应等待
// - 状态管理

// 解决问题1：统一使用协议层的 pump.TransactionType，避免重复定义
// 解决问题2：从请求参数中获取具体值，避免硬编码
// 解决问题3：定义明确的参数结构体，避免 map[string]interface{}

// CD1TransactionRequest CD1 事务请求参数
type CD1TransactionRequest struct {
	Command pump.PumpCommand `json:"command" validate:"required"` // 明确的命令类型
}

// CD2TransactionRequest CD2 事务请求参数
type CD2TransactionRequest struct {
	AllowedNozzles []byte `json:"allowed_nozzles" validate:"required,min=1,max=8"` // 允许的喷嘴列表
}

// CD3TransactionRequest CD3 事务请求参数
type CD3TransactionRequest struct {
	Volume   decimal.Decimal `json:"volume" validate:"required,gt=0"` // 预设体积
	Decimals int             `json:"decimals" validate:"min=0,max=3"` // 小数位数，默认3
}

// CD4TransactionRequest CD4 事务请求参数
type CD4TransactionRequest struct {
	Amount   decimal.Decimal `json:"amount" validate:"required,gt=0"` // 预设金额
	Decimals int             `json:"decimals" validate:"min=0,max=3"` // 小数位数，默认2
}

// CD5TransactionRequest CD5 事务请求参数
type CD5TransactionRequest struct {
	Prices []nozzle.NozzlePriceInfo `json:"prices" validate:"required,dive"` // 价格列表
}

// CD14TransactionRequest CD14 暂停事务请求参数
type CD14TransactionRequest struct {
	NozzleNumber byte `json:"nozzle_number" validate:"required,min=1,max=8"` // 解决问题2：从参数获取喷嘴编号
}

// CD15TransactionRequest CD15 恢复事务请求参数
type CD15TransactionRequest struct {
	NozzleNumber byte `json:"nozzle_number" validate:"required,min=1,max=8"` // 解决问题2：从参数获取喷嘴编号
}

// CD101TransactionRequest CD101 事务请求参数
type CD101TransactionRequest struct {
	CounterType byte `json:"counter_type" validate:"required"` // 计数器类型：0x09=总体积, 0x19=总金额
}

type CounterTypeEnum byte

const (
	// 单喷嘴体积计数器
	CounterTypeNozzle1Volume CounterTypeEnum = 0x01
	CounterTypeNozzle2Volume CounterTypeEnum = 0x02
	CounterTypeNozzle3Volume CounterTypeEnum = 0x03
	CounterTypeNozzle4Volume CounterTypeEnum = 0x04
	CounterTypeNozzle5Volume CounterTypeEnum = 0x05
	CounterTypeNozzle6Volume CounterTypeEnum = 0x06
	CounterTypeNozzle7Volume CounterTypeEnum = 0x07
	CounterTypeNozzle8Volume CounterTypeEnum = 0x08

	// 总体积
	CounterTypeTotalVolume CounterTypeEnum = 0x09

	// 单喷嘴金额计数器
	CounterTypeNozzle1Amount CounterTypeEnum = 0x11
	CounterTypeNozzle2Amount CounterTypeEnum = 0x12
	CounterTypeNozzle3Amount CounterTypeEnum = 0x13
	CounterTypeNozzle4Amount CounterTypeEnum = 0x14
	CounterTypeNozzle5Amount CounterTypeEnum = 0x15
	CounterTypeNozzle6Amount CounterTypeEnum = 0x16
	CounterTypeNozzle7Amount CounterTypeEnum = 0x17
	CounterTypeNozzle8Amount CounterTypeEnum = 0x18

	// 总金额
	CounterTypeTotalAmount CounterTypeEnum = 0x19
)

// CDTransactionRequest CD事务构建请求
type CDTransactionRequest struct {
	Type       pump.TransactionType `json:"type" validate:"required"`        // 解决问题1：直接使用协议层类型
	DeviceID   string               `json:"device_id" validate:"required"`   // 设备ID
	Address    byte                 `json:"address" validate:"required"`     // 设备地址
	TxSequence byte                 `json:"tx_sequence" validate:"required"` // TX序列号
	Timeout    time.Duration        `json:"timeout" validate:"required"`     // 超时时间
	Priority   CommandPriority      `json:"priority"`                        // 命令优先级

	// 解决问题3：使用类型安全的参数结构，文档清晰
	CD1Params   *CD1TransactionRequest   `json:"cd1_params,omitempty"`   // CD1参数：泵命令
	CD2Params   *CD2TransactionRequest   `json:"cd2_params,omitempty"`   // CD2参数：允许喷嘴
	CD3Params   *CD3TransactionRequest   `json:"cd3_params,omitempty"`   // CD3参数：预设体积
	CD4Params   *CD4TransactionRequest   `json:"cd4_params,omitempty"`   // CD4参数：预设金额
	CD5Params   *CD5TransactionRequest   `json:"cd5_params,omitempty"`   // CD5参数：价格更新
	CD14Params  *CD14TransactionRequest  `json:"cd14_params,omitempty"`  // CD14参数：暂停
	CD15Params  *CD15TransactionRequest  `json:"cd15_params,omitempty"`  // CD15参数：恢复
	CD101Params *CD101TransactionRequest `json:"cd101_params,omitempty"` // CD101参数：计数器查询
}

// CDTransactionBuilder CD事务构建器
type CDTransactionBuilder struct {
	transactionBuilder pump.TransactionBuilder
	frameBuilder       FrameBuilder
	logger             *zap.Logger
}

// NewCDTransactionBuilder 创建CD事务构建器
func NewCDTransactionBuilder(logger *zap.Logger) *CDTransactionBuilder {
	return &CDTransactionBuilder{
		transactionBuilder: pump.NewTransactionBuilder(),
		frameBuilder:       NewDefaultFrameBuilder(),
		logger:             logger,
	}
}

// BuildCDCommand 构建CD事务命令
// 输入：业务参数
// 输出：PollCommand（供DevicePoller处理）
func (b *CDTransactionBuilder) BuildCDCommand(request CDTransactionRequest) (*PollCommand, error) {
	// 验证请求参数
	if err := b.validateRequest(request); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 构建CD命令

	// 1. 构建 CD 事务
	cdTransaction, err := b.buildCDTransaction(request)
	if err != nil {
		return nil, fmt.Errorf("failed to build CD transaction: %w", err)
	}

	// 2. 修复：不需要预先构建DART帧，在DevicePoller中会处理frame构建

	// 3. 构建 PollCommand
	var businessType string
	if request.Type == pump.TransactionTypeCD1 && request.CD1Params != nil {
		businessType, err = b.getBusinessType(request.Type, request.CD1Params.Command)
		if err != nil {
			return nil, fmt.Errorf("failed to get business type: %w", err)
		}
	} else {
		businessType, err = b.getBusinessType(request.Type)
		if err != nil {
			return nil, fmt.Errorf("failed to get business type: %w", err)
		}
	}

	pollCommand := &PollCommand{
		Type:         string(CommandTypeData), // Wayne DART 数据命令
		Data:         cdTransaction.Encode(),  // 修复：使用事务数据而不是frame数据
		Timeout:      request.Timeout,
		Timestamp:    time.Now(),
		BusinessType: businessType, // 映射业务类型
		Priority:     int(request.Priority),
		Retryable:    b.isRetryable(request.Type),
		MaxRetries:   b.getMaxRetries(request.Type),
	}

	// CD命令构建成功

	return pollCommand, nil
}

// validateRequest 验证请求参数
func (b *CDTransactionBuilder) validateRequest(request CDTransactionRequest) error {
	// 根据事务类型验证对应的参数是否存在
	switch request.Type {
	case pump.TransactionTypeCD1:
		if request.CD1Params == nil {
			return fmt.Errorf("CD1 parameters required for pump commands")
		}
	case pump.TransactionTypeCD2:
		if request.CD2Params == nil {
			return fmt.Errorf("CD2 parameters required for nozzle selection")
		}
		if len(request.CD2Params.AllowedNozzles) == 0 {
			return fmt.Errorf("at least one nozzle must be allowed")
		}
	case pump.TransactionTypeCD3:
		if request.CD3Params == nil {
			return fmt.Errorf("CD3 parameters required for volume preset")
		}
		if request.CD3Params.Volume.LessThanOrEqual(decimal.Zero) {
			return fmt.Errorf("volume must be greater than zero")
		}
	case pump.TransactionTypeCD4:
		if request.CD4Params == nil {
			return fmt.Errorf("CD4 parameters required for amount preset")
		}
		if request.CD4Params.Amount.LessThanOrEqual(decimal.Zero) {
			return fmt.Errorf("amount must be greater than zero")
		}
	case pump.TransactionTypeCD5:
		if request.CD5Params == nil {
			return fmt.Errorf("CD5 parameters required for price update")
		}
		if len(request.CD5Params.Prices) == 0 {
			return fmt.Errorf("at least one price must be provided")
		}
	case pump.TransactionTypeCD14:
		if request.CD14Params == nil {
			return fmt.Errorf("CD14 parameters required for suspend operation")
		}
		if request.CD14Params.NozzleNumber < 1 || request.CD14Params.NozzleNumber > 8 {
			return fmt.Errorf("nozzle number must be between 1 and 8")
		}
	case pump.TransactionTypeCD15:
		if request.CD15Params == nil {
			return fmt.Errorf("CD15 parameters required for resume operation")
		}
		if request.CD15Params.NozzleNumber < 1 || request.CD15Params.NozzleNumber > 8 {
			return fmt.Errorf("nozzle number must be between 1 and 8")
		}
	case pump.TransactionTypeCD101:
		if request.CD101Params == nil {
			return fmt.Errorf("CD101 parameters required for counter request")
		}
	default:
		return fmt.Errorf("unsupported transaction type: %02X", request.Type)
	}

	return nil
}

// buildCDTransaction 构建 CD 事务
func (b *CDTransactionBuilder) buildCDTransaction(request CDTransactionRequest) (pump.CDTransaction, error) {
	switch request.Type {
	case pump.TransactionTypeCD1:
		return b.transactionBuilder.BuildCD1(request.CD1Params.Command)

	case pump.TransactionTypeCD2:
		return b.transactionBuilder.BuildCD2(request.CD2Params.AllowedNozzles)

	case pump.TransactionTypeCD3:
		return b.buildCD3(request.CD3Params)

	case pump.TransactionTypeCD4:
		return b.buildCD4(request.CD4Params)

	case pump.TransactionTypeCD5:
		return b.buildCD5(request.CD5Params)

	case pump.TransactionTypeCD14:
		// 解决问题2：使用参数中的喷嘴编号，而不是硬编码 0x01
		return b.transactionBuilder.BuildCD14(request.CD14Params.NozzleNumber)

	case pump.TransactionTypeCD15:
		// 解决问题2：使用参数中的喷嘴编号，而不是硬编码 0x01
		return b.transactionBuilder.BuildCD15(request.CD15Params.NozzleNumber)

	case pump.TransactionTypeCD101:
		return b.transactionBuilder.BuildCD101(request.CD101Params.CounterType)

	default:
		return nil, fmt.Errorf("unsupported CD transaction type: %02X", request.Type)
	}
}

// buildCD3 构建 CD3 预设体积事务
func (b *CDTransactionBuilder) buildCD3(params *CD3TransactionRequest) (pump.CDTransaction, error) {
	bcdConverter := pump.NewBCDConverter()
	volumeFloat, _ := params.Volume.Float64()

	// 使用参数指定的小数位数，如果未指定则默认为3
	decimals := params.Decimals
	if decimals == 0 {
		decimals = 3
	}

	volumeBCD := bcdConverter.EncodeVolume(volumeFloat, decimals, 4)
	return b.transactionBuilder.BuildCD3(volumeBCD)
}

// buildCD4 构建 CD4 预设金额事务
func (b *CDTransactionBuilder) buildCD4(params *CD4TransactionRequest) (pump.CDTransaction, error) {
	bcdConverter := pump.NewBCDConverter()

	// 修复：直接使用前端传输的金额，不做任何倍数处理
	// 前端传输 10000 表示 10000 元，直接下发给油机和存入数据库
	amountFloat, _ := params.Amount.Float64()

	// decimals参数现在被BCD函数忽略，但保留以维持接口兼容性
	decimals := params.Decimals
	if decimals == 0 {
		decimals = 2
	}

	amountBCD := bcdConverter.EncodeAmount(amountFloat, decimals, 4)
	return b.transactionBuilder.BuildCD4(amountBCD)
}

// buildCD5 构建 CD5 价格更新事务
// 支持两种场景：
// 1. 自动价格配置：通过GetNozzlePricesForCD5从数据库获取所有喷嘴价格
// 2. 接口调价：直接传入特定喷嘴的价格信息（推荐用于单喷嘴或指定喷嘴调价）
func (b *CDTransactionBuilder) buildCD5(params *CD5TransactionRequest) (pump.CDTransaction, error) {
	var pricesData [][]byte
	bcdConverter := pump.NewBCDConverter()

	b.logger.Info("Building CD5 price transaction",
		zap.Int("price_count", len(params.Prices)),
		zap.String("scenario", "interface_price_update"))

	for i, priceInfo := range params.Prices {
		// 修复：直接使用前端传输的价格，不做任何倍数处理
		// 前端传输的价格是多少，就下发多少给油机
		priceFloat, _ := priceInfo.Price.Float64()
		priceInt := int64(priceFloat)

		// 修正：支持高面值货币的价格验证（基于整数表示）
		// BCD 3字节支持范围: 1 - 999,999,999（对应0.001 - 999,999.999元）
		// 适应各国货币：印尼盾12,840/升 -> 12840000、委内瑞拉玻利瓦尔等
		if priceInt < 1 || priceInt > 999999999 {
			originalPrice, _ := priceInfo.Price.Float64()
			b.logger.Error("🚨 Price out of BCD 3-byte range",
				zap.Int("price_index", i),
				zap.Uint8("nozzle_number", priceInfo.NozzleNumber),
				zap.Float64("original_price", originalPrice),
				zap.Int64("price_int", priceInt),
				zap.String("valid_range", "0.001_to_999999.999"),
				zap.String("bcd_limit", "3_bytes_max_999999"))
			return nil, fmt.Errorf("price %v for nozzle %d exceeds BCD 3-byte range (0.001-999999.999)",
				priceInfo.Price, priceInfo.NozzleNumber)
		}

		// 检查明显的数据错误
		if priceInt <= 0 {
			originalPrice, _ := priceInfo.Price.Float64()
			b.logger.Error("🚨 Invalid zero or negative price",
				zap.Int("price_index", i),
				zap.Uint8("nozzle_number", priceInfo.NozzleNumber),
				zap.Float64("original_price", originalPrice),
				zap.Int64("price_int", priceInt))
			return nil, fmt.Errorf("invalid price %v for nozzle %d: must be positive",
				priceInfo.Price, priceInfo.NozzleNumber)
		}

		// decimals参数现在被BCD函数忽略，但保留以维持接口兼容性
		decimals := priceInfo.Decimals
		if decimals == 0 {
			decimals = 3
		}

		priceBCD := bcdConverter.EncodePrice(priceFloat, decimals, 3)

		// 验证BCD编码结果
		if len(priceBCD) != 3 {
			originalPrice, _ := priceInfo.Price.Float64()
			b.logger.Error("🚨 Invalid BCD encoding length",
				zap.Int("price_index", i),
				zap.Float64("original_price", originalPrice),
				zap.Int64("price_int", priceInt),
				zap.Int("expected_length", 3),
				zap.Int("actual_length", len(priceBCD)))
			return nil, fmt.Errorf("BCD encoding failed for price %v", priceInfo.Price)
		}

		// 验证BCD数据不是异常值
		if priceBCD[0] > 0x99 || priceBCD[1] > 0x99 || priceBCD[2] > 0x99 {
			originalPrice, _ := priceInfo.Price.Float64()
			b.logger.Error("🚨 Invalid BCD encoding result",
				zap.Int("price_index", i),
				zap.Float64("original_price", originalPrice),
				zap.Int64("price_int", priceInt),
				zap.String("bcd_hex", fmt.Sprintf("%02X%02X%02X", priceBCD[0], priceBCD[1], priceBCD[2])))
			return nil, fmt.Errorf("BCD encoding produced invalid result for price %v", priceInfo.Price)
		}

		pricesData = append(pricesData, priceBCD)

		// CD5价格编码成功
	}

	// 构建CD5事务前的最终验证
	totalDataLength := len(pricesData) * 3
	b.logger.Info("Building CD5 transaction with validated prices",
		zap.Int("price_count", len(pricesData)),
		zap.Int("total_data_bytes", totalDataLength),
		zap.String("protocol_note", "LNG_field_will_be_set_to_total_data_bytes"),
		zap.String("use_case", "interface_driven_price_update"))

	return b.transactionBuilder.BuildCD5(pricesData)
}

// buildDARTFrame 构建 DART 帧
func (b *CDTransactionBuilder) buildDARTFrame(address, txSequence byte, cdTransaction pump.CDTransaction) (*dartline.Frame, error) {
	transactionData := cdTransaction.Encode()
	return b.frameBuilder.BuildDataFrame(address, txSequence, transactionData)
}

// getBusinessType 将协议类型映射为业务类型 - 严格遵守Wayne协议
func (b *CDTransactionBuilder) getBusinessType(transactionType pump.TransactionType, cd1Command ...pump.PumpCommand) (string, error) {
	switch transactionType {
	case pump.TransactionTypeCD1:
		// 严格按照Wayne DART协议：根据具体的CD1命令类型细分业务类型
		if len(cd1Command) > 0 {
			switch cd1Command[0] {
			case pump.CmdReset:
				return string(BusinessTypeReset), nil
			case pump.CmdAuthorize:
				return string(BusinessTypeAuthorize), nil
			case pump.CmdStop:
				return string(BusinessTypeStop), nil
			case pump.CmdReturnStatus:
				return string(BusinessTypeStatus), nil
			case pump.CmdReturnFillingInformation:
				return string(BusinessTypeStatus), nil
			default:
				return "", fmt.Errorf("unsupported CD1 command: 0x%02X", cd1Command[0])
			}
		}
		return "", fmt.Errorf("CD1 command not specified")
	case pump.TransactionTypeCD2:
		return string(BusinessTypePreset), nil
	case pump.TransactionTypeCD3:
		return string(BusinessTypePreset), nil
	case pump.TransactionTypeCD4:
		return string(BusinessTypePreset), nil
	case pump.TransactionTypeCD5:
		return string(BusinessTypePriceUpdate), nil
	case pump.TransactionTypeCD7:
		return string(BusinessTypeStatus), nil
	case pump.TransactionTypeCD14:
		return string(BusinessTypeStop), nil
	case pump.TransactionTypeCD15:
		return string(BusinessTypeAuthorize), nil
	case pump.TransactionTypeCD101:
		return string(BusinessTypeStatus), nil
	default:
		return "", fmt.Errorf("unsupported Wayne DART transaction type: 0x%02X", transactionType)
	}
}

// isRetryable 判断事务是否可重试
func (b *CDTransactionBuilder) isRetryable(transactionType pump.TransactionType) bool {
	switch transactionType {
	case pump.TransactionTypeCD1:
		return true // 泵命令可重试
	case pump.TransactionTypeCD2:
		return true // 喷嘴选择可重试
	case pump.TransactionTypeCD3:
		return false // 预设体积不应重试（避免重复预设）
	case pump.TransactionTypeCD4:
		return false // 预设金额不应重试
	case pump.TransactionTypeCD5:
		return true // 价格更新可重试
	case pump.TransactionTypeCD14:
		return true // 暂停可重试
	case pump.TransactionTypeCD15:
		return true // 恢复可重试
	case pump.TransactionTypeCD101:
		return true // 查询可重试
	default:
		return false
	}
}

// getMaxRetries 获取最大重试次数
func (b *CDTransactionBuilder) getMaxRetries(transactionType pump.TransactionType) int {
	switch transactionType {
	case pump.TransactionTypeCD1:
		return 3 // 泵命令重试3次
	case pump.TransactionTypeCD2:
		return 2 // 喷嘴选择重试2次
	case pump.TransactionTypeCD5:
		return 2 // 价格更新重试2次
	case pump.TransactionTypeCD14, pump.TransactionTypeCD15:
		return 3 // 暂停/恢复重试3次
	case pump.TransactionTypeCD101:
		return 2 // 查询重试2次
	default:
		return 0 // 其他不重试
	}
}

// 🎯 便捷方法：提供业务友好的接口

// BuildAuthorizeCommand 构建授权命令
func (b *CDTransactionBuilder) BuildAuthorizeCommand(deviceID string, address byte, txSeq byte) (*PollCommand, error) {
	request := CDTransactionRequest{
		Type:       pump.TransactionTypeCD1,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD1Params: &CD1TransactionRequest{
			Command: pump.CmdAuthorize,
		},
	}

	// 1. 构建 CD 事务
	cdTransaction, err := b.buildCDTransaction(request)
	if err != nil {
		return nil, fmt.Errorf("failed to build CD transaction: %w", err)
	}

	// 3. 直接构建 PollCommand，明确指定业务类型
	// 修复双重编码问题：PollCommand.Data应该包含事务数据，而不是已编码的frame数据
	pollCommand := &PollCommand{
		Type:         string(CommandTypeData),
		Data:         cdTransaction.Encode(), // ← 修复：使用事务数据而不是frame数据
		Timeout:      request.Timeout,
		Timestamp:    time.Now(),
		BusinessType: string(BusinessTypeAuthorize), // ← 直接指定，不依赖映射
		Priority:     int(request.Priority),
		Retryable:    b.isRetryable(request.Type),
		MaxRetries:   b.getMaxRetries(request.Type),
	}

	return pollCommand, nil
}

// BuildResetCommand 构建重置命令
func (b *CDTransactionBuilder) BuildResetCommand(deviceID string, address byte, txSeq byte) (*PollCommand, error) {
	request := CDTransactionRequest{
		Type:       pump.TransactionTypeCD1,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityHigh,
		CD1Params: &CD1TransactionRequest{
			Command: pump.CmdReset,
		},
	}

	// 1. 构建 CD 事务
	cdTransaction, err := b.buildCDTransaction(request)
	if err != nil {
		return nil, fmt.Errorf("failed to build CD transaction: %w", err)
	}

	// 2. 构建 DART 帧
	// frame, err := b.buildDARTFrame(request.Address, request.TxSequence, cdTransaction)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to build DART frame: %w", err)
	// }

	// 3. 直接构建 PollCommand，明确指定业务类型
	// 修复双重编码问题：PollCommand.Data应该包含事务数据，而不是已编码的frame数据
	pollCommand := &PollCommand{
		Type:         string(CommandTypeData),
		Data:         cdTransaction.Encode(), // ← 修复：使用事务数据而不是frame数据
		Timeout:      request.Timeout,
		Timestamp:    time.Now(),
		BusinessType: string(BusinessTypeReset), // ← 直接指定，不依赖映射
		Priority:     int(request.Priority),
		Retryable:    b.isRetryable(request.Type),
		MaxRetries:   b.getMaxRetries(request.Type),
	}

	return pollCommand, nil
}

// BuildPresetVolumeCommand 构建预设体积命令
func (b *CDTransactionBuilder) BuildPresetVolumeCommand(deviceID string, address byte, txSeq byte, volume decimal.Decimal) (*PollCommand, error) {
	request := CDTransactionRequest{
		Type:       pump.TransactionTypeCD3,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD3Params: &CD3TransactionRequest{
			Volume:   volume,
			Decimals: 3, // 默认3位小数
		},
	}

	// 1. 构建 CD 事务
	cdTransaction, err := b.buildCDTransaction(request)
	if err != nil {
		return nil, fmt.Errorf("failed to build CD transaction: %w", err)
	}

	// 2. 修复：不需要预先构建DART帧，在DevicePoller中会处理frame构建

	// 3. 直接构建 PollCommand，明确指定业务类型
	pollCommand := &PollCommand{
		Type:         string(CommandTypeData),
		Data:         cdTransaction.Encode(), // 修复：使用事务数据而不是frame数据
		Timeout:      request.Timeout,
		Timestamp:    time.Now(),
		BusinessType: string(BusinessTypePreset), // ← 直接指定，不依赖映射
		Priority:     int(request.Priority),
		Retryable:    b.isRetryable(request.Type),
		MaxRetries:   b.getMaxRetries(request.Type),
	}

	return pollCommand, nil
}

// BuildPresetAmountCommand 构建预设金额命令
func (b *CDTransactionBuilder) BuildPresetAmountCommand(deviceID string, address byte, txSeq byte, amount decimal.Decimal) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD4,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD4Params: &CD4TransactionRequest{
			Amount:   amount,
			Decimals: 2, // 默认2位小数
		},
	})
}

// BuildSuspendNozzleCommand 构建暂停喷嘴命令
func (b *CDTransactionBuilder) BuildSuspendNozzleCommand(deviceID string, address byte, txSeq byte, nozzleNumber byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD14,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityHigh,
		CD14Params: &CD14TransactionRequest{
			NozzleNumber: nozzleNumber, // 明确指定喷嘴编号
		},
	})
}

// BuildResumeNozzleCommand 构建恢复喷嘴命令
func (b *CDTransactionBuilder) BuildResumeNozzleCommand(deviceID string, address byte, txSeq byte, nozzleNumber byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD15,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD15Params: &CD15TransactionRequest{
			NozzleNumber: nozzleNumber, // 明确指定喷嘴编号
		},
	})
}

// BuildUpdatePricesCommand 构建价格更新命令
func (b *CDTransactionBuilder) BuildUpdatePricesCommand(deviceID string, address byte, txSeq byte, prices []nozzle.NozzlePriceInfo) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD5,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD5Params: &CD5TransactionRequest{
			Prices: prices,
		},
	})
}

// BuildRequestCountersCommand 构建计数器查询命令（默认低优先级）
func (b *CDTransactionBuilder) BuildRequestCountersCommand(deviceID string, address byte, txSeq byte, counterType byte) (*PollCommand, error) {
	return b.BuildRequestCountersCommandWithPriority(deviceID, address, txSeq, counterType, PriorityLow)
}

// BuildRequestCountersCommandWithPriority 构建指定优先级的计数器查询命令
func (b *CDTransactionBuilder) BuildRequestCountersCommandWithPriority(deviceID string, address byte, txSeq byte, counterType byte, priority CommandPriority) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD101,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   priority,               // 🔧 支持指定优先级
		CD101Params: &CD101TransactionRequest{
			CounterType: counterType, // 0x09=总体积, 0x19=总金额
		},
	})
}

// BuildConfigureNozzlesCommand 构建喷嘴配置命令
func (b *CDTransactionBuilder) BuildConfigureNozzlesCommand(deviceID string, address byte, txSeq byte, nozzles []byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD2,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD2Params: &CD2TransactionRequest{
			AllowedNozzles: nozzles,
		},
	})
}

// BuildStopCommand 构建停止命令
func (b *CDTransactionBuilder) BuildStopCommand(deviceID string, address byte, txSeq byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD1,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityHigh,
		CD1Params: &CD1TransactionRequest{
			Command: pump.CmdStop,
		},
	})
}

// BuildReturnStatusCommand 构建返回状态命令
func (b *CDTransactionBuilder) BuildReturnStatusCommand(deviceID string, address byte, txSeq byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD1,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD1Params: &CD1TransactionRequest{
			Command: pump.CmdReturnStatus,
		},
	})
}

// BuildReturnFillingInfoCommand 构建返回填充信息命令
func (b *CDTransactionBuilder) BuildReturnFillingInfoCommand(deviceID string, address byte, txSeq byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD1,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityMedium,
		CD1Params: &CD1TransactionRequest{
			Command: pump.CmdReturnFillingInformation,
		},
	})
}

// BuildResumeFuellingPointCommand 构建恢复加油点命令
func (b *CDTransactionBuilder) BuildResumeFuellingPointCommand(deviceID string, address byte, txSeq byte) (*PollCommand, error) {
	return b.BuildCDCommand(CDTransactionRequest{
		Type:       pump.TransactionTypeCD1,
		DeviceID:   deviceID,
		Address:    address,
		TxSequence: txSeq,
		Timeout:    200 * time.Millisecond, // 业务操作命令使用较长超时确保可靠性
		Priority:   PriorityHigh,
		CD1Params: &CD1TransactionRequest{
			Command: pump.CmdResumeFuellingPoint,
		},
	})
}
