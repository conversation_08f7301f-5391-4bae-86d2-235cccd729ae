package v2

import (
	"context"
	"fmt"
	"os"
	"runtime/debug"
	"sync"
	"time"
	"unsafe"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/adapters/wayne/dartline"
	"fcc-service/internal/adapters/wayne/pump"
	"fcc-service/internal/config"
	"fcc-service/internal/services/device_runtime_state"
	"fcc-service/internal/services/nozzle"
	"fcc-service/internal/services/nozzle_counters"
	"fcc-service/internal/services/watchdog"
	"fcc-service/pkg/models"
	v2models "fcc-service/pkg/models/v2"
)

// DevicePoller 设备轮询器 - 简化重构版本
// 核心职责：
// 1. 管理单个设备的独立轮询
// 2. 处理命令队列和定时轮询
// 3. 与设备状态机交互
// 4. 集成简化串口管理器
type DevicePoller struct {
	// 基础配置
	config   DevicePollerConfig
	deviceSM v2models.DeviceStateMachine
	logger   *zap.Logger

	// 环境配置缓存
	isProduction bool // 缓存生产环境标志，避免重复检查

	// 通信channels
	commandChan chan PollCommand
	resultChan  chan PollResult
	stopChan    chan struct{}

	// 通信接口 - 直接使用通信接口
	communication CommunicationInterface

	// 🚀 新增：通信锁 - 每个通信接口一个锁，避免RS485冲突
	commLock *sync.Mutex

	// 协议组件
	frameBuilder       FrameBuilder
	transactionBuilder pump.TransactionBuilder
	cdBuilder          *CDTransactionBuilder

	// 业务服务
	nozzleService      nozzle.ServiceV2
	transactionService TransactionServiceInterface

	// 🚀 新增：TransactionLifecycleService依赖 - 用于完全托付交易业务逻辑
	lifecycleService TransactionLifecycleServiceInterface

	// 🎯 新增：DC101流水记录服务
	nozzleCountersService nozzle_counters.Service

	// 🔧 新增：NozzleID解析器 - 统一管理nozzle number到nozzle ID的映射
	nozzleIDResolver NozzleIDResolver

	// 状态管理
	isRunning    bool
	mu           sync.RWMutex
	wg           sync.WaitGroup
	lastActivity time.Time

	// 看门狗
	watchdog *watchdog.Watchdog

	// 统计收集
	statsCollector *StatsCollector

	// 业务状态
	pollerContext *PollerContext

	// 交易组装器
	transactionAssembler *TransactionAssembler
	operatorIDCache      *OperatorIDCache

	// 🆕 智能计数器轮询状态 - MVP版本
	nextCounterType     byte          // 下一个要请求的计数器类型
	lastCounterRequest  time.Time     // 上次请求计数器的时间
	counterRequestDelay time.Duration // 计数器请求间隔

	// 🆕 喷嘴配置缓存 - 避免频繁查询数据库
	cachedNozzles     []byte        // 缓存的喷嘴配置
	nozzleCacheTime   time.Time     // 缓存更新时间
	nozzleCacheExpiry time.Duration // 缓存过期时间

	// 🎯 CD2喷嘴配置缓存 - 解决DC1 AUTHORIZED状态无法获取喷嘴信息的问题
	lastConfiguredNozzles unsafe.Pointer // 原子操作：*[]byte 最后配置的喷嘴列表（CD2命令）
	configuredAt          int64          // 原子操作：Unix纳秒时间戳

	// 🗑️ 移除：lastTxResetTimeUnix 字段已移除，现在使用 DeviceRuntimeStateService

	runtimeStateService device_runtime_state.DeviceRuntimeStateService
}

// NewDevicePoller 创建设备轮询器
func NewDevicePoller(
	config DevicePollerConfig,
	deviceSM v2models.DeviceStateMachine,
	frameBuilder FrameBuilder,
	nozzleService nozzle.ServiceV2,
	transactionService TransactionServiceInterface,
	lifecycleServiceFactory func(operatorIDCache OperatorIDCacheInterface) TransactionLifecycleServiceInterface, // 🚀 修改：接收工厂函数
	nozzleCountersService nozzle_counters.Service, // 🎯 新增：DC101流水记录服务参数
	communication CommunicationInterface,
	serialScheduler SerialPortScheduler, // 保持接口兼容，内部不使用
	commLock *sync.Mutex, // 🚀 新增：通信锁参数
	preAuthConf *config.PreAuthConfig, // 🆕 新增：预授权配置
	autoAuthConf *config.AutoAuthConfig, // 🆕 新增：自动授权配置
	runtimeStateService device_runtime_state.DeviceRuntimeStateService, // 🆕 新增：设备运行时状态服务
	logger *zap.Logger,
) *DevicePoller {
	// 创建基础结构
	poller := &DevicePoller{
		config:                config,
		deviceSM:              deviceSM,
		logger:                logger,
		isProduction:          logger.Core().Enabled(zap.InfoLevel) && !logger.Core().Enabled(zap.DebugLevel), // 判断是否为生产环境
		commandChan:           make(chan PollCommand, config.BufferSize),
		resultChan:            make(chan PollResult, config.BufferSize),
		stopChan:              make(chan struct{}),
		communication:         communication, // 直接使用传入的通信接口
		commLock:              commLock,      // 🚀 新增：注入通信锁
		frameBuilder:          frameBuilder,
		transactionBuilder:    pump.NewTransactionBuilder(),
		cdBuilder:             NewCDTransactionBuilder(logger),
		nozzleService:         nozzleService,
		transactionService:    transactionService,
		nozzleCountersService: nozzleCountersService, // 🎯 新增：注入DC101流水记录服务
		runtimeStateService:   runtimeStateService,   // 🆕 新增：注入设备运行时状态服务
		// lifecycleService 将在创建 operatorIDCache 后初始化
		lastActivity:   time.Now(),
		statsCollector: NewStatsCollector(),
		pollerContext:  &PollerContext{},
	}

	// 🆕 修复：创建员工ID缓存时传递配置参数
	poller.operatorIDCache = NewOperatorIDCache(logger, preAuthConf, autoAuthConf)

	// 🔧 创建NozzleID解析器
	poller.nozzleIDResolver = NewNozzleIDResolver(nozzleService, logger)

	// 🚀 使用工厂函数创建设备专用的 TransactionLifecycleService
	poller.lifecycleService = lifecycleServiceFactory(poller.operatorIDCache)

	// 🗑️ 移除：不再需要设置DevicePoller引用，现在使用 DeviceRuntimeStateService 解耦

	// 创建交易组装器
	poller.transactionAssembler = NewTransactionAssembler(
		config.DeviceInfo.ID,
		logger,
		poller.operatorIDCache,
		poller.lifecycleService,
		poller.nozzleCountersService,
		poller.nozzleIDResolver,
		poller, // 🎯 传递DevicePoller自身作为CD2ConfigurationProvider
	)

	// 🚀 Phase 3: 注册DevicePoller为TransactionLifecycleService的监听器
	if err := poller.lifecycleService.RegisterTransactionListener(poller); err != nil {
		logger.Error("注册交易生命周期监听器失败",
			zap.String("device_id", config.DeviceInfo.ID),
			zap.String("listener_id", poller.GetListenerID()),
			zap.Error(err))
		// 注意：这里不返回错误，因为注册失败不应该阻止DevicePoller创建
		// 但会影响交易事件的接收
	} else {
		logger.Info("✅ DevicePoller已注册为交易生命周期监听器",
			zap.String("device_id", config.DeviceInfo.ID),
			zap.String("listener_id", poller.GetListenerID()))
	}

	// 🆕 初始化智能计数器轮询状态 - MVP版本
	poller.nextCounterType = 0x01                // 从喷嘴1的体积计数器开始
	poller.counterRequestDelay = 5 * time.Second // 5秒间隔避免频繁请求

	// 🆕 初始化喷嘴配置缓存
	poller.nozzleCacheExpiry = 5 * time.Minute // 缓存5分钟过期

	// 创建看门狗
	poller.watchdog = watchdog.NewWatchdog(watchdog.WatchdogConfig{
		Name:      fmt.Sprintf("device_%s_watchdog", config.DeviceInfo.ID),
		Timeout:   config.WatchdogTimeout,
		OnTimeout: poller.onWatchdogTimeout,
		Logger:    logger,
	})

	// 🆕 注册设备到运行时状态服务
	if err := poller.runtimeStateService.RegisterDevice(config.DeviceInfo.ID); err != nil {
		logger.Error("注册设备到运行时状态服务失败",
			zap.String("device_id", config.DeviceInfo.ID),
			zap.Error(err))
		// 注意：这里不返回错误，因为注册失败不应该阻止DevicePoller创建
	} else {
		logger.Info("✅ 设备已注册到运行时状态服务",
			zap.String("device_id", config.DeviceInfo.ID))
	}

	return poller
}

// Start 启动轮询器
func (p *DevicePoller) Start(ctx context.Context) error {
	p.mu.Lock()
	if p.isRunning {
		p.mu.Unlock()
		return fmt.Errorf("device poller already running")
	}
	p.isRunning = true
	p.mu.Unlock()

	// 确保通信接口已连接
	if p.communication != nil && !p.communication.IsConnected() {
		if err := p.communication.Connect(ctx); err != nil {
			p.mu.Lock()
			p.isRunning = false
			p.mu.Unlock()
			return fmt.Errorf("failed to connect communication: %w", err)
		}
	}

	// 启动看门狗
	if err := p.watchdog.Start(ctx); err != nil {
		p.mu.Lock()
		p.isRunning = false
		p.mu.Unlock()
		return fmt.Errorf("failed to start watchdog: %w", err)
	}

	// 📝 设备轮询错开优化
	// 为避免多设备同时轮询造成RS485总线冲突，根据设备的序列索引
	// 计算启动延迟，确保设备间轮询时间错开
	startupDelay := time.Duration(p.config.PollingSequenceIndex) * 500 * time.Millisecond // 🔧 改回：错开时间改为500ms
	if startupDelay > 0 {
		p.logger.Info("设备轮询启动延迟 - 避免总线冲突",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Int("sequence_index", p.config.PollingSequenceIndex),
			zap.Duration("startup_delay", startupDelay),
			zap.String("目的", "RS485总线防冲突优化"))

		select {
		case <-time.After(startupDelay):
			p.logger.Debug("设备轮询启动延迟完成",
				zap.String("device_id", p.config.DeviceInfo.ID))
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	// 启动主轮询循环
	p.wg.Add(1)
	go p.mainPollLoop(ctx)

	p.logger.Info("设备轮询器已启动",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Int("sequence_index", p.config.PollingSequenceIndex),
		zap.Duration("startup_delay", startupDelay),
		zap.Duration("poll_interval", p.config.PollInterval),
		zap.Bool("communication_available", p.communication != nil))

	// 发送初始化命令
	go p.sendInitializationCommand()

	return nil
}

// Stop 停止轮询器
func (p *DevicePoller) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.isRunning {
		return nil
	}

	p.isRunning = false

	// 发送停止信号
	close(p.stopChan)

	// 等待所有goroutine结束
	p.wg.Wait()

	// 停止看门狗
	if err := p.watchdog.Stop(); err != nil {
		p.logger.Warn("停止看门狗失败", zap.Error(err))
	}

	p.logger.Info("设备轮询器已停止", zap.String("device_id", p.config.DeviceInfo.ID))

	return nil
}

// SendCommand 发送命令到队列
func (p *DevicePoller) SendCommand(cmd PollCommand) error {
	p.mu.RLock()
	if !p.isRunning {
		p.mu.RUnlock()
		return fmt.Errorf("device poller not running")
	}
	p.mu.RUnlock()

	// 添加到命令队列（不在这里缓存，在实际发送时缓存）
	p.pollerContext.AddCommand(cmd)

	// 移除日常命令日志: p.logger.Info("[DevicePoller] 命令已添加到队列")

	return nil
}

func (p *DevicePoller) ProcessPendingCommand() {

	if cmd := p.pollerContext.GetNextCommand(); cmd != nil {
		// 移除详细调度日志: p.logger.Debug("[DevicePoller]调度器处理设备命令")

		// 直接调用poller的ProcessPendingCommand方法
		// 这个方法会上锁、执行命令、释放锁
		p.processPendingCommand(*cmd)
	}
}

// 🚀 新增：处理待发送命令（由DispatchTask调度器调用）
// 这个方法会上锁，复用现有的executeCommand逻辑，然后释放锁
func (p *DevicePoller) processPendingCommand(cmd PollCommand) {
	// 上锁 - 确保同一通信接口在同一时间只有一个设备在发送
	p.commLock.Lock()
	defer p.commLock.Unlock()

	// 复用现有的executeCommand逻辑
	startTime := time.Now()

	result := p.executeCommand(context.Background(), cmd, startTime)

	// 复用现有的结果处理
	p.handleResult(result)

	// 只在失败时记录详细信息
	if !result.Success {
		p.logger.Warn("[DevicePoller] 命令执行失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("command_type", cmd.Type),
			zap.String("business_type", cmd.BusinessType),
			zap.Duration("execution_time", time.Since(startTime)),
			zap.Error(result.Error))
	}
	// 移除成功的详细日志: p.logger.Debug("[DevicePoller] 调度器处理命令完成")
}

// mainPollLoop 主轮询循环 - 设备启动时已错开，无需额外偏移
func (p *DevicePoller) mainPollLoop(ctx context.Context) {
	defer p.wg.Done()

	// 🛡️ Level 1 - 主轮询循环总体防护
	defer func() {
		if r := recover(); r != nil {
			p.logger.Error("🚨 CRITICAL: DevicePoller主轮询循环发生panic",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Any("panic_value", r),
				zap.String("panic_type", fmt.Sprintf("%T", r)),
				zap.String("stack_trace", string(debug.Stack())))

			// 记录panic到专用日志文件
			p.writePanicLog("mainPollLoop", r)

			// 继续运行但标记为错误状态
			p.logger.Error("⚠️ 主轮询循环因panic终止，轮询器将停止运行",
				zap.String("device_id", p.config.DeviceInfo.ID))
		}
	}()

	ticker := time.NewTicker(p.config.PollInterval)
	defer ticker.Stop()

	p.logger.Info("[DevicePoller]设备轮询循环已启动",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Int("sequence_index", p.config.PollingSequenceIndex),
		zap.Duration("poll_interval", p.config.PollInterval))

	for {
		select {
		case <-ctx.Done():
			p.logger.Info("[DevicePoller]设备轮询循环已停止",
				zap.String("device_id", p.config.DeviceInfo.ID))
			return
		case <-p.stopChan:
			p.logger.Info("[DevicePoller]收到停止信号",
				zap.String("device_id", p.config.DeviceInfo.ID))
			return
		case <-ticker.C:
			p.executePollCycle(ctx)
		}
	}

}

// executePollCycle 执行轮询周期
func (p *DevicePoller) executePollCycle(ctx context.Context) {
	// 移除详细轮询开始日志 - 只在状态变化时记录
	// p.logger.Debug("[DevicePoller]开始轮询周期") - 移除高频日志

	// 🆕 优化：主动检查员工ID超时状态
	p.checkOperatorIDTimeout()

	// 1. 检查是否有命令需要处理
	if cmd := p.pollerContext.GetNextCommand(); cmd == nil {
		// 2. 没有命令则执行例行轮询
		// 移除例行轮询日志: p.logger.Debug("执行例行轮询")

		p.pullPollFrame()

	}

	// 3. 更新活动时间
	p.updateActivity()

}

// executeCommand 执行命令
func (p *DevicePoller) executeCommand(ctx context.Context, cmd PollCommand, startTime time.Time) PollResult {
	result := PollResult{
		DeviceID:     p.config.DeviceInfo.ID,
		Timestamp:    startTime,
		BusinessType: cmd.BusinessType,
		CommandType:  cmd.Type,
	}

	// 🛡️ Level 3 - 命令执行防护
	defer func() {
		if r := recover(); r != nil {
			p.logger.Error("🚨 命令执行时发生panic",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("command_type", cmd.Type),
				zap.String("business_type", cmd.BusinessType),
				zap.Time("command_start_time", startTime),
				zap.Duration("command_duration", time.Since(startTime)),
				zap.Any("panic_value", r),
				zap.String("panic_type", fmt.Sprintf("%T", r)),
				zap.String("stack_trace", string(debug.Stack())))

			// 记录panic到专用日志文件
			p.writePanicLog("executeCommand", r)

			// 设置错误结果
			result.Success = false
			result.Error = fmt.Errorf("命令执行发生panic: %v", r)
			result.ResponseTime = time.Since(startTime)

			p.logger.Warn("⚠️ 命令执行因panic终止，返回错误结果",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("command_type", cmd.Type),
				zap.String("recovery_action", "return_error_result"))
		}
	}()

	// 提取数据参数
	data, ok := cmd.Data.([]byte)
	if !ok {
		result.Success = false
		result.Error = fmt.Errorf("无效的命令数据格式")
		return result
	}

	// 🔧 修复：在实际执行时才获取和分配TX序列号
	// 这确保了TX序列号的严格顺序性，避免竞态条件
	deviceState := p.deviceSM.GetStateData()
	txNum := deviceState.TxSequence

	// TX#=0的使用场景 - 严格按照DART协议标准
	if p.shouldUseTxZero(cmd) {
		txNum = 0
		p.logger.Info("[DevicePoller]使用TX#=0",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType),
			zap.String("reason", "dart_protocol_requirement"))
	}

	var frame *dartline.Frame
	var err error
	if cmd.BusinessType == string(BusinessTypeAck) {
		// ACK 的 TX序列号不可以被重置
		frame, err = dartline.CreateAckFrame(p.config.DeviceInfo.Address, data[0])
		if err != nil {
			result.Success = false
			result.Error = fmt.Errorf("[DevicePoller]构建ACK帧失败: %w", err)
			return result
		}
	} else {
		// 🔧 关键修复：由于TX序列号影响CRC计算，必须使用当前TX序列号重新构建帧
		// 而不是修改已有帧，这样可以确保CRC正确计算

		// 构建POLL帧

		if cmd.BusinessType == string(BusinessTypeMonitoring) {
			frame, err = p.frameBuilder.BuildPollFrame(p.config.DeviceInfo.Address)
		} else {
			frame, err = p.frameBuilder.BuildDataFrame(p.config.DeviceInfo.Address, txNum, data)
		}

		if err != nil {
			result.Success = false
			result.Error = fmt.Errorf("[DevicePoller]构建帧失败: %w", err)
			return result
		}

		p.logger.Info("[DevicePoller]发送命令帧",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType),
			zap.Uint8("allocated_tx_number", txNum),
			zap.String("crc_status", "自动重新计算")) // TX序列号变化会自动重新计算CRC

	}

	// 发送帧并等待响应
	responseFrame, err := p.sendFrameAndWait(frame, cmd.Timeout, cmd.BusinessType)
	if err != nil {
		result.Success = false
		result.Error = err
		result.ResponseTime = time.Since(startTime)

		// 生产环境只记录关键错误，开发环境记录详细信息
		if p.isProduction {
			p.logger.Error("命令执行失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("business_type", cmd.BusinessType),
				zap.Duration("response_time", result.ResponseTime),
				zap.Error(err))
		} else {
			p.logger.Error("命令执行失败(详细)",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("business_type", cmd.BusinessType),
				zap.String("command_type", cmd.Type),
				zap.Uint8("tx_number", txNum),
				zap.Duration("response_time", result.ResponseTime),
				zap.String("frame_data", fmt.Sprintf("%X", frame.Encode())),
				zap.Error(err))
		}

		return result
	}

	if cmd.BusinessType == string(BusinessTypeMonitoring) {
		result = p.processPollResponse(responseFrame, startTime)
	} else {
		result = p.processCommandResponse(responseFrame, cmd, startTime)
	}

	// !frame.IsAckFrame() && cmd.BusinessType != string(BusinessTypeMonitoring)
	if result.Success && frame.IsDataFrame() {
		// 只有数据帧需要递增TX序列号
		nextTxNum := txNum + 1
		if nextTxNum > 15 { // DART协议标准：TX序列号范围1-15
			nextTxNum = 1
		}
		p.deviceSM.UpdateTxSequence(nextTxNum)
		// 移除TX序列号递增的详细日志 - 只在异常时记录
		// p.logger.Debug("[DevicePoller]TX序列号递增") - 移除高频日志
	}

	return result
}

// 构建 PollFrame 到队列
func (p *DevicePoller) pullPollFrame() {

	// 创建高优先级协议命令
	cmd := PollCommand{
		Type:         "poll", // 协议层命令
		BusinessType: string(BusinessTypeMonitoring),
		Priority:     1,
		Data:         []byte{p.config.DeviceInfo.Address},
		Timeout:      100 * time.Millisecond,
	}

	if err := p.SendCommand(cmd); err != nil {
		p.logger.Error("发送POLL命令失败", zap.Error(err))
		return
	}
}

// executeRoutinePoll 执行例行轮询
// func (p *DevicePoller) executeRoutinePoll(ctx context.Context, startTime time.Time) PollResult {
// 	result := PollResult{
// 		DeviceID:     p.config.DeviceInfo.ID,
// 		Timestamp:    startTime,
// 		BusinessType: "monitoring",
// 		CommandType:  "poll",
// 	}

// 	// 构建POLL帧
// 	frame, err := p.frameBuilder.BuildPollFrame(p.config.DeviceInfo.Address)
// 	if err != nil {
// 		result.Success = false
// 		result.Error = fmt.Errorf("构建轮询帧失败: %w", err)
// 		return result
// 	}

// 	// 发送并等待响应
// 	responseFrame, err := p.sendFrameAndWait(frame, p.config.PollTimeout, "monitoring")
// 	if err != nil {
// 		result.Success = false
// 		result.Error = err
// 		result.ResponseTime = time.Since(startTime)

// 		// 添加详细的错误诊断日志
// 		p.logger.Debug("轮询失败详细信息",
// 			zap.String("device_id", p.config.DeviceInfo.ID),
// 			zap.Uint8("device_address", p.config.DeviceInfo.Address),
// 			zap.String("frame_data", fmt.Sprintf("%X", frame.Encode())),
// 			zap.Duration("timeout", p.config.PollTimeout),
// 			zap.Duration("response_time", result.ResponseTime),
// 			zap.Error(err),
// 			zap.Bool("communication_connected", p.communication != nil && p.communication.IsConnected()))

// 		return result
// 	}

// 	// 处理响应
// 	result = p.processPollResponse(responseFrame, startTime)

// 	return result
// }

// sendFrameAndWait 发送帧并等待响应
func (p *DevicePoller) sendFrameAndWait(frame *dartline.Frame, timeout time.Duration, businessType string) (*dartline.Frame, error) {
	frameData := frame.Encode()

	// 生产环境：只记录非监控业务和错误情况
	// 开发环境：记录所有帧发送的详细信息
	if !p.isProduction && businessType != string(BusinessTypeMonitoring) {
		p.logger.Debug("[DevicePoller]发送DART帧",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("frame_data", fmt.Sprintf("%X", frameData)),
			zap.String("business_type", businessType))
	}

	if p.communication == nil || !p.communication.IsConnected() {
		return nil, fmt.Errorf("通信接口不可用")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*timeout)
	defer cancel()

	// 🔧 修复：ACK帧不应该等待响应
	if businessType == string(BusinessTypeAck) {
		// ACK是确认帧，只发送不等待响应
		// 使用一个非常短的超时来"模拟"只发送
		quickCtx, quickCancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer quickCancel()

		// 尝试发送，忽略超时错误
		_, err := p.communication.SendFrameAndWait(quickCtx, frameData, 1*time.Millisecond)
		if err != nil && (err == context.DeadlineExceeded || err.Error() == "context deadline exceeded") {
			// 对于ACK，超时是预期的，表示发送成功但没有响应
			// 移除ACK发送完成的日志 - 这是正常操作
			// p.logger.Debug("[DevicePoller]ACK帧发送完成（预期无响应）")

			// 创建一个虚拟的ACK响应表示发送成
			// ✅ 修复：使用正确的ACK命令格式 (0xC0 | txNum)
			dummyAck := &dartline.Frame{
				Command: 0xC0, // ACK command (高4位=1100，低4位=0000)
			}
			return dummyAck, nil
		}

		// 如果是其他错误（不是超时），返回错误
		if err != nil {
			return nil, fmt.Errorf("发送ACK帧失败: %w", err)
		}

		// 如果意外收到了响应，也返回成功
		return &dartline.Frame{Command: 0x06}, nil
	}

	// 其他帧正常等待响应
	// return p.communication.SendFrameAndWait(ctx, frameData, 3*timeout)

	// 其他帧正常等待响应
	responseFrame, err := p.communication.SendFrameAndWait(ctx, frameData, 3*timeout)
	if err != nil {
		return nil, err
	}

	// 🚀 MVP: 地址验证 - 防止数据串扰
	if responseFrame != nil {
		expectedAddr := p.config.DeviceInfo.Address
		receivedAddr := responseFrame.Address

		if receivedAddr != expectedAddr {
			p.logger.Error("[重要]设备地址不匹配，检测到数据串扰",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("expected_address", expectedAddr),
				zap.Uint8("received_address", receivedAddr),
				zap.String("business_type", businessType),
				zap.String("sent_frame", fmt.Sprintf("%X", frameData)),
				zap.String("received_frame", fmt.Sprintf("%X", responseFrame.Encode())),
				zap.String("error_type", "ADDRESS_MISMATCH"))

			return nil, fmt.Errorf("address mismatch: expected %d, got %d (data crosstalk detected)",
				expectedAddr, receivedAddr)
		}

		// 记录成功的地址验证（仅在调试模式）
		if !p.isProduction {
			p.logger.Debug("地址验证成功",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("address", receivedAddr),
				zap.String("business_type", businessType))
		}
	}

	return responseFrame, nil
}

// processCommandResponse 处理命令响应
func (p *DevicePoller) processCommandResponse(responseFrame *dartline.Frame, cmd PollCommand, startTime time.Time) PollResult {
	result := PollResult{
		DeviceID:     p.config.DeviceInfo.ID,
		Timestamp:    startTime,
		BusinessType: cmd.BusinessType,
		CommandType:  cmd.Type,
		ResponseTime: time.Since(startTime),
	}

	if responseFrame.IsAckFrame() {
		result.Success = true
		p.logger.Info("[DevicePoller]命令被设备接受",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType))
	} else if responseFrame.IsNakFrame() {
		result.Success = false
		result.Error = fmt.Errorf("[DevicePoller]命令被设备拒绝")
		p.logger.Warn("命令被设备拒绝",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType))
	} else if responseFrame.IsDataFrame() {
		result.Success = true
		result.StateChanged = true
		p.logger.Info("[DevicePoller]收到命令数据响应",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("business_type", cmd.BusinessType))

		// 🔧 修复ACK时序：立即发送ACK，符合DART协议25ms响应要求
		p.sendAckFrame(responseFrame)

		if err := p.processDeviceData(responseFrame.Data); err != nil {
			p.logger.Error("处理业务数据失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Error(err))
		}
	}

	return result
}

func (p *DevicePoller) processPollResponse(responseFrame *dartline.Frame, startTime time.Time) PollResult {
	result := PollResult{
		DeviceID:     p.config.DeviceInfo.ID,
		Timestamp:    startTime,
		BusinessType: "monitoring",
		CommandType:  "poll",
		ResponseTime: time.Since(startTime),
	}

	if responseFrame.IsAckFrame() {
		result.Success = true
		// 移除正常ACK响应日志: p.logger.Debug("收到ACK轮询响应")
	} else if responseFrame.IsDataFrame() {
		result.Success = true
		result.StateChanged = true

		p.logger.Info("收到轮询数据响应",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Int("data_length", len(responseFrame.Data)))

		// 🔧 修复ACK时序：立即发送ACK，符合DART协议25ms响应要求
		p.sendAckFrame(responseFrame)

		// 异步处理设备数据，避免阻塞ACK发送
		if err := p.processDeviceData(responseFrame.Data); err != nil {
			p.logger.Error("处理设备数据失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Error(err))
		}
	} else if responseFrame.IsEotFrame() {
		// ✅ 新增：处理EOT帧 - 设备收到轮询但没有数据要发送（正常情况）
		result.Success = true
		// 移除正常EOT响应日志: p.logger.Debug("收到EOT轮询响应（设备无数据发送）")

		// 🆕 智能计数器请求 - 当设备无数据时主动获取计数器
		p.handleEOTResponse()
	} else {
		result.Success = false
		result.Error = fmt.Errorf("未知响应类型: 0x%02X", responseFrame.Command)
		p.logger.Warn("收到未知类型的轮询响应",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("response_frame", fmt.Sprintf("%X", responseFrame.Encode())),
			zap.Uint8("command", responseFrame.Command))
	}

	return result
}

// processDeviceData 处理设备数据
func (p *DevicePoller) processDeviceData(data []byte) error {
	// 生成数据处理跟踪ID
	traceID := fmt.Sprintf("data_%s_%d", p.config.DeviceInfo.ID, time.Now().UnixNano())

	// 解析DC事务
	transactions, err := p.parseDCTransactions(data)
	if err != nil {
		p.logger.Error("解析DC事务失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("trace_id", traceID),
			zap.Int("data_length", len(data)),
			zap.String("data_hex", fmt.Sprintf("%X", data)),
			zap.Error(err))
		return fmt.Errorf("解析DC事务失败: %w", err)
	}

	if len(transactions) > 0 {
		p.logger.Info("[DevicePoller]解析到DC事务",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("trace_id", traceID),
			zap.Int("transaction_count", len(transactions)))
	}

	// 处理每个DC事务
	for i, transaction := range transactions {
		txTraceID := fmt.Sprintf("%s_tx%d", traceID, i)
		if err := p.processDCTransactionWithTrace(transaction, txTraceID); err != nil {
			p.logger.Error("处理DC事务失败",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("trace_id", txTraceID),
				zap.String("transaction_type", fmt.Sprintf("DC%d", int(transaction.GetType()))),
				zap.Int("transaction_index", i),
				zap.Error(err))
		}
	}

	return nil
}

// processDCTransactionWithTrace 处理DC事务（带跟踪ID）
func (p *DevicePoller) processDCTransactionWithTrace(transaction pump.DCTransaction, traceID string) error {
	// 委托给现有的processDCTransaction处理，增加跟踪信息
	defer func() {
		if r := recover(); r != nil {
			p.logger.Error("处理DC事务时发生panic",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("trace_id", traceID),
				zap.String("transaction_type", fmt.Sprintf("DC%d", int(transaction.GetType()))),
				zap.Any("panic_value", r))
		}
	}()

	return p.processDCTransaction(transaction)
}

// parseDCTransactions 解析DC事务列表
func (p *DevicePoller) parseDCTransactions(data []byte) ([]pump.DCTransaction, error) {
	var transactions []pump.DCTransaction
	dataOffset := 0

	for dataOffset < len(data) {
		if dataOffset+2 > len(data) {
			break
		}

		transType := pump.TransactionType(data[dataOffset])
		transLength := data[dataOffset+1]
		totalTransSize := int(transLength) + 2

		if dataOffset+totalTransSize > len(data) {
			return nil, fmt.Errorf("数据不足，无法读取事务")
		}

		transactionData := data[dataOffset : dataOffset+totalTransSize]

		var transaction pump.DCTransaction
		var err error

		switch transType {
		case pump.TransactionTypeDC1:
			transaction, err = p.transactionBuilder.ParseDC1(transactionData)
		case pump.TransactionTypeDC2:
			transaction, err = p.transactionBuilder.ParseDC2(transactionData)
		case pump.TransactionTypeDC3:
			transaction, err = p.transactionBuilder.ParseDC3(transactionData)
		case pump.TransactionTypeDC5:
			transaction, err = p.transactionBuilder.ParseDC5(transactionData)
		case pump.TransactionTypeDC7:
			transaction, err = p.transactionBuilder.ParseDC7(transactionData)
		case pump.TransactionTypeDC14:
			transaction, err = p.transactionBuilder.ParseDC14(transactionData)
		case pump.TransactionTypeDC15:
			transaction, err = p.transactionBuilder.ParseDC15(transactionData)
		case pump.TransactionTypeDC101:
			transaction, err = p.transactionBuilder.ParseDC101(transactionData)
		default:
			// 记录未知的DC事务类型以便调试
			p.logger.Debug("[DevicePoller]收到未支持的DC事务类型",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.Uint8("transaction_type", uint8(transType)),
				zap.String("hex_type", fmt.Sprintf("0x%02X", transType)))
			dataOffset += totalTransSize
			continue
		}

		if err != nil {
			return nil, fmt.Errorf("[DevicePoller]解析DC%d事务失败: %w", int(transType), err)
		}

		transactions = append(transactions, transaction)
		dataOffset += totalTransSize
	}

	return transactions, nil
}

// // sendAckFrame 发送ACK确认帧
// func (p *DevicePoller) sendAckFrame(receivedFrame *dartline.Frame) {

// 	receivedTxNum := receivedFrame.GetTxNumber()
// 	// 直接构建DART ACK帧
// 	// ackFrame, err := dartline.CreateAckFrame(
// 	// 	p.config.DeviceInfo.Address,
// 	// 	receivedTxNum,
// 	// )
// 	// if err != nil {
// 	// 	p.logger.Error("创建ACK帧失败", zap.Error(err))
// 	// 	return
// 	// }

// 	// Data 格式要求： cmd.Data.([]byte)

// 	// 创建高优先级协议命令
// 	cmd := PollCommand{
// 		Type:         "protocol_ack", // 协议层命令
// 		BusinessType: string(BusinessTypeAck),
// 		Priority:     1,
// 		Data:         []byte{receivedTxNum},
// 		Timeout:      100 * time.Millisecond,
// 	}

// 	if err := p.SendCommand(cmd); err != nil {
// 		p.logger.Error("发送ACK命令失败", zap.Error(err))
// 		return
// 	}

// }

// sendAckFrame 发送ACK确认帧
func (p *DevicePoller) sendAckFrame(receivedFrame *dartline.Frame) {
	receivedTxNum := receivedFrame.GetTxNumber()

	// 🔧 关键修复：直接发送ACK，避免死锁
	// ACK必须在当前通信会话中立即发送，不能通过命令队列
	ackFrame, err := dartline.CreateAckFrame(
		p.config.DeviceInfo.Address,
		receivedTxNum,
	)
	if err != nil {
		p.logger.Error("创建ACK帧失败", zap.Error(err))
		return
	}

	p.logger.Debug("ACK帧发送成功",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("tx_num", receivedTxNum))
	// 直接调用sendFrameAndWait发送ACK（不会再次获取锁，因为已经在锁内）
	_, err = p.sendFrameAndWait(ackFrame, 100*time.Millisecond, string(BusinessTypeAck))
	if err != nil {
		// p.logger.Error("发送ACK帧失败", zap.Error(err))
		return
	}
}

// handleResult 处理结果
func (p *DevicePoller) handleResult(result PollResult) {
	// 更新统计
	p.statsCollector.UpdateStats(result)

	// 通知状态机
	if result.Success {
		if err := p.deviceSM.OnPollResponse(result.ResponseTime); err != nil {
			p.logger.Debug("通知状态机轮询响应失败", zap.Error(err))
		} else {
			// 只在状态实际发生变化时记录
			if result.StateChanged {
				currentState := p.deviceSM.GetState()
				p.logger.Info("设备状态变化",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Duration("response_time", result.ResponseTime),
					zap.String("new_state", string(currentState)),
					zap.String("business_type", result.BusinessType),
					zap.String("command_type", result.CommandType))
			}
		}
	} else {
		pollError := result.Error
		if pollError == nil {
			pollError = fmt.Errorf("轮询失败")
		}

		// 获取当前状态用于更详细的日志
		currentState := p.deviceSM.GetState()

		if err := p.deviceSM.OnPollError(pollError); err != nil {
			// 降级为DEBUG级别，因为从offline到error的转换被状态机拒绝是正常的
			p.logger.Debug("状态机拒绝轮询错误转换（正常保护机制）",
				zap.Error(err),
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("current_state", string(currentState)),
				zap.String("attempted_transition", "poll_error"),
				zap.String("original_error", pollError.Error()),
				zap.String("correlation_id", fmt.Sprintf("poll_%d", time.Now().Unix())))
		} else {
			p.logger.Warn("设备轮询错误",
				zap.String("device_id", p.config.DeviceInfo.ID),
				zap.String("error", pollError.Error()),
				zap.String("previous_state", string(currentState)),
				zap.String("business_type", result.BusinessType),
				zap.String("command_type", result.CommandType),
				zap.Duration("response_time", result.ResponseTime),
				zap.String("correlation_id", fmt.Sprintf("poll_error_%d", time.Now().Unix())))
		}
	}

	// 发送结果
	select {
	case p.resultChan <- result:
		// 结果发送成功
	default:
		p.logger.Warn("结果通道满，丢弃结果",
			zap.String("device_id", p.config.DeviceInfo.ID))
	}
}

// updateActivity 更新活动时间
func (p *DevicePoller) updateActivity() {
	p.lastActivity = time.Now()

	// 获取当前状态进行状态感知的看门狗管理
	deviceState := p.deviceSM.GetStateData()
	currentPumpStatus := deviceState.PumpStatus

	// 📖 业务关键状态下增强看门狗保护
	isBusinessCriticalState := currentPumpStatus == 0x02 || // AUTHORIZED
		currentPumpStatus == 0x04 || // FILLING
		currentPumpStatus == 0x05 // FILLING_COMPLETED

	if isBusinessCriticalState {
		// 在关键业务状态下，主动ping看门狗
		p.watchdog.Ping()

		// p.logger.Debug("业务关键状态下主动ping看门狗",
		// 	zap.String("device_id", p.config.DeviceInfo.ID),
		// 	zap.Uint8("pump_status", byte(currentPumpStatus)),
		// 	zap.String("status_name", p.getPumpStatusName(byte(currentPumpStatus))))
	} else {
		// 常规状态下的正常ping
		p.watchdog.Ping()
	}
}

// sendTXStatusQuery 发送状态查询命令（温和恢复）
func (p *DevicePoller) sendTXStatusQuery(purpose string) {
	cd1Data := []byte{0x01, 0x01, 0x00} // CD1, LNG=1, CMD=RETURN STATUS

	cmd := PollCommand{
		Type:         "configure",
		BusinessType: purpose + "_status_query",
		Priority:     0,
		Data:         cd1Data,
		Timeout:      100 * time.Millisecond,
	}

	p.logger.Info("发送状态查询命令（温和恢复）",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("purpose", purpose))

	p.SendCommand(cmd)
}

// sendInitializationCommand 发送初始化命令
func (p *DevicePoller) sendInitializationCommand() {
	time.Sleep(100 * time.Millisecond) // 等待启动完成

	// 通知 Pump 重置 TX
	p.sendTXResetCommand("initialization")

	// 配置喷嘴 TODO
	// 发一遍数据库价格
	completePrices, err := p.nozzleService.BuildCompleteNozzlePrices(context.Background(), p.config.DeviceInfo.ID, 0, decimal.NewFromFloat(0))
	if err == nil {
		p.logger.Info("发送数据库价格",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Any("completePrices", completePrices))
		p.UpdatePrices(completePrices)
	}

	// 获取累计泵码 - 使用CD101查询总金额累计
	// p.RequestCounters(CounterTypeAmount)
}

// sendTXResetCommand 发送TX重置命令
func (p *DevicePoller) sendTXResetCommand(purpose string) {
	cd1Data := []byte{0x01, 0x01, 0x02} // CD1, LNG=1, CMD=RETURN STATUS

	cmd := PollCommand{
		Type:         "configure",
		BusinessType: purpose + "_tx_reset",
		Priority:     0,
		Data:         cd1Data,
		Timeout:      100 * time.Millisecond,
	}

	p.logger.Info("发送TX重置命令",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("purpose", purpose))

	p.SendCommand(cmd)

	// 🆕 DC101优先验证机制：记录TX重置时间用于丢包检测
	p.recordTxReset(time.Now())
}

// 🆕 DC101优先验证机制：记录TX重置时间用于丢包检测
func (p *DevicePoller) recordTxReset(timestamp time.Time) {
	// 🆕 使用运行时状态服务存储TX重置时间
	if err := p.runtimeStateService.SetTxResetTime(p.config.DeviceInfo.ID, timestamp); err != nil {
		p.logger.Error("[DevicePoller] TxReset记录TX重置时间失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("timestamp", timestamp.Format("2006-01-02 15:04:05.000")),
			zap.Error(err))
	} else {
		p.logger.Debug("[DevicePoller] TxReset记录TX重置时间",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("timestamp", timestamp.Format("2006-01-02 15:04:05.000")))
	}
}

// 🗑️ 移除：GetLastTxResetTime 方法已移除，现在使用 DeviceRuntimeStateService

func (p *DevicePoller) GetListenerID() string {
	return fmt.Sprintf("device_poller_%s", p.config.DeviceInfo.ID)
}

// 🆕 DC101优先验证机制：处理泵码质量改进事件
func (p *DevicePoller) OnPumpReadingQualityImproved(tx *models.Transaction, oldQuality, newQuality string) error {
	p.logger.Info("[DevicePoller] 收到泵码质量改进事件",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.String("transaction_id", tx.ID),
		zap.String("nozzle_id", tx.NozzleID),
		zap.String("old_quality", oldQuality),
		zap.String("new_quality", newQuality),
		zap.String("status", string(tx.Status)))

	// 如果质量从pending改进为verified，可以停止后台DC101请求
	if oldQuality == "pending" && newQuality == "verified" {
		p.logger.Info("[DevicePoller] 泵码验证完成，质量已改进为verified",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("transaction_id", tx.ID),
			zap.String("improvement", "pending → verified"))
	}

	return nil
}

// 公共接口方法
func (p *DevicePoller) GetResultChannel() <-chan PollResult {
	return p.resultChan
}

func (p *DevicePoller) GetStats() PollerStats {
	return p.statsCollector.GetStats()
}

func (p *DevicePoller) GetCurrentBusiness() *BusinessState {
	return p.pollerContext.CurrentBusiness
}

func (p *DevicePoller) GetPollerContext() *PollerContext {
	return p.pollerContext
}

func (p *DevicePoller) IsRunning() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.isRunning
}

func (p *DevicePoller) GetDeviceID() string {
	return p.config.DeviceInfo.ID
}

func (p *DevicePoller) GetLastActivity() time.Time {
	return p.lastActivity
}

// GetOperatorIDCache 获取设备的OperatorIDCache
func (p *DevicePoller) GetOperatorIDCache() OperatorIDCacheInterface {
	return p.operatorIDCache
}

func (p *DevicePoller) GetTransactionAssembler() *TransactionAssembler {
	return p.transactionAssembler
}

func (p *DevicePoller) GetTransactionLifecycleService() TransactionLifecycleServiceInterface {
	return p.lifecycleService
}

// writePanicLog 写入panic日志
func (p *DevicePoller) writePanicLog(location string, panicValue interface{}) {
	// 创建panic日志文件
	filename := fmt.Sprintf("panic_%s_%s.log", p.config.DeviceInfo.ID, time.Now().Format("20060102_150405"))
	file, err := os.Create(filename)
	if err != nil {
		p.logger.Error("创建panic日志文件失败", zap.Error(err))
		return
	}
	defer file.Close()

	// 写入panic信息
	fmt.Fprintf(file, "Panic in DevicePoller at %s\n", location)
	fmt.Fprintf(file, "Device ID: %s\n", p.config.DeviceInfo.ID)
	fmt.Fprintf(file, "Time: %s\n", time.Now().Format(time.RFC3339))
	fmt.Fprintf(file, "Panic Value: %v\n", panicValue)
	fmt.Fprintf(file, "Stack Trace:\n%s\n", debug.Stack())

	p.logger.Error("Panic日志已写入",
		zap.String("filename", filename),
		zap.String("location", location))
}

// 🆕 handleEOTResponse 处理EOT响应 - 智能计数器请求
func (p *DevicePoller) handleEOTResponse() {
	// 检查是否需要发送计数器请求（避免频繁请求）
	if time.Since(p.lastCounterRequest) < p.counterRequestDelay {
		return
	}

	// 发送下一个计数器请求
	if err := p.requestNextCounter(); err != nil {
		p.logger.Debug("发送智能计数器请求失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("counter_type", p.nextCounterType),
			zap.Error(err))
	} else {
		p.logger.Debug("发送智能计数器请求",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("counter_type", p.nextCounterType),
			zap.String("counter_name", p.getCounterTypeName(p.nextCounterType)))

		p.lastCounterRequest = time.Now()
	}
}

// 🆕 requestNextCounter 请求下一个计数器
func (p *DevicePoller) requestNextCounter() error {

	// 	什么时候要泵码? 待完成交易

	// 状态
	p.ReturnStatus()
	return nil
}

// 🆕 getNextCounterType 获取下一个计数器类型 - 轮询策略
func (p *DevicePoller) getNextCounterType(current byte) byte {
	switch current {
	// 体积计数器序列：0x01 -> 0x02 -> ... -> 0x08 -> 0x09
	case 0x01:
		return 0x02
	case 0x02:
		return 0x03
	case 0x03:
		return 0x04
	case 0x04:
		return 0x05
	case 0x05:
		return 0x06
	case 0x06:
		return 0x07
	case 0x07:
		return 0x08
	case 0x08:
		return 0x09 // 总体积

	// 从体积转到金额：0x09 -> 0x11
	case 0x09:
		return 0x11

	// 金额计数器序列：0x11 -> 0x12 -> ... -> 0x18 -> 0x19
	case 0x11:
		return 0x12
	case 0x12:
		return 0x13
	case 0x13:
		return 0x14
	case 0x14:
		return 0x15
	case 0x15:
		return 0x16
	case 0x16:
		return 0x17
	case 0x17:
		return 0x18
	case 0x18:
		return 0x19 // 总金额

	// 循环回到开始：0x19 -> 0x01
	case 0x19:
		return 0x01

	// 默认从喷嘴1体积开始
	default:
		return 0x01
	}
}

// 🆕 getCounterTypeName 获取计数器类型名称 - 用于日志
func (p *DevicePoller) getCounterTypeName(counterType byte) string {
	switch counterType {
	case 0x09:
		return "总体积"
	case 0x19:
		return "总金额"
	default:
		if counterType >= 0x01 && counterType <= 0x08 {
			return fmt.Sprintf("喷嘴%d体积", counterType)
		} else if counterType >= 0x11 && counterType <= 0x18 {
			return fmt.Sprintf("喷嘴%d金额", counterType-0x10)
		}
		return fmt.Sprintf("未知(0x%02X)", counterType)
	}
}

// 🆕 优化：checkOperatorIDTimeout 主动检查员工ID超时状态
// 在轮询循环中主动检查超时，而不是仅依赖定时器回调
func (p *DevicePoller) checkOperatorIDTimeout() {
	// 主动检查超时状态
	isTimedOut, timedOutOperatorID := p.operatorIDCache.CheckTimeout()

	if isTimedOut && timedOutOperatorID != "" {
		p.logger.Warn("主动检测到员工ID超时，处理超时逻辑",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("timed_out_operator_id", timedOutOperatorID),
			zap.Duration("timeout_duration", authorizationTimeout))

		// 调用统一的超时处理逻辑
		p.handleOperatorIDTimeout(timedOutOperatorID)
	}
}
