package v2

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/datatypes"

	"fcc-service/internal/services/device_runtime_state"
	"fcc-service/internal/services/nozzle"
	"fcc-service/internal/services/nozzle_counters"
	"fcc-service/pkg/models"
)

// 重构V2.1 - 数据驱动模型常量定义
// 🎯 交易完成优化：基于DC1和DC2的简化完成机制
// - 交易完成只依赖DC1和DC2数据
// - 如果没有丢包，直接完成交易
// - 如果有丢包，主动获取并等待新的DC2数据

// TransactionLifecycleServiceInterface 的实现，移除了MaxTransactionAge常量，改用SQL优化的方式处理多个活跃交易

// TransactionLifecycleListener 交易生命周期监听器 - 保持向后兼容
type TransactionLifecycleListener interface {
	OnTransactionCreated(tx *models.Transaction) error
	OnTransactionUpdated(tx *models.Transaction) error
	OnTransactionCompleted(tx *models.Transaction) error
	OnTransactionCancelled(tx *models.Transaction) error
	OnTransactionPending(tx *models.Transaction) error

	GetListenerID() string
}

// Repository 数据访问接口
type Repository interface {
	// 基础CRUD
	Create(ctx context.Context, tx *models.Transaction) error
	GetByID(ctx context.Context, id string) (*models.Transaction, error)
	Update(ctx context.Context, tx *models.Transaction) error
	Delete(ctx context.Context, id string) error

	// 查询操作
	GetActiveByDeviceAndNozzle(ctx context.Context, deviceID string, nozzleID string) (*models.Transaction, error)
	GetActiveByDevice(ctx context.Context, deviceID string) ([]*models.Transaction, error)
	GetByDeviceAndTimeRange(ctx context.Context, deviceID string, start, end time.Time) ([]*models.Transaction, error)
	GetStaleTransactions(ctx context.Context, maxAge time.Duration) ([]*models.Transaction, error)
	// 🆕 重构V2.1新增：根据设备、喷嘴和状态查询交易
	GetByDeviceNozzleAndStatus(ctx context.Context, deviceID string, nozzleID string, status models.TransactionStatus) ([]*models.Transaction, error)
	// 查询最近完成的交易
	GetRecentCompletedTransactions(ctx context.Context, deviceID string, nozzleID string, duration time.Duration) ([]*models.Transaction, error)
	// 🆕 根据泵码匹配查找交易
	GetTransactionByPumpReading(ctx context.Context, deviceID string, nozzleID string, counterType int16, previousValue decimal.Decimal) (*models.Transaction, error)

	// 统计操作
	GetStatistics(ctx context.Context, deviceID string) (*TransactionStatistics, error)

	// 🚀 SQL优化：确保同一喷嘴只有一个活跃交易
	EnsureSingleActiveTransaction(ctx context.Context, deviceID string, nozzleID string, reason string) (keptTransactionID *string, cancelledCount int, err error)
}

// service 服务实现 - 直接实现 TransactionLifecycleServiceInterface
type service struct {
	repository            Repository
	nozzleService         nozzle.ServiceV2        // 用于更新Nozzle current_volume/current_amount和获取nozzle信息
	nozzleCountersService nozzle_counters.Service // 用于获取start pump reading
	logger                *zap.Logger

	// 配置
	config ServiceConfig

	// 事件监听器
	listeners           []TransactionLifecycleListenerInterface
	operatorIDCache     OperatorIDCacheInterface
	runtimeStateService device_runtime_state.DeviceRuntimeStateService
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	// 时间窗口配置
	CounterUpdateWindow time.Duration `json:"counter_update_window"` // 计数器更新窗口，默认30秒
	TransactionTimeout  time.Duration `json:"transaction_timeout"`   // 交易超时时间，默认10分钟

	// 数据质量配置
	RequireStartCounters   bool `json:"require_start_counters"`   // 是否要求起始泵码
	AllowEmptyTransactions bool `json:"allow_empty_transactions"` // 是否允许空交易

	// 并发控制
	MaxConcurrentTransactions int `json:"max_concurrent_transactions"` // 每个设备最大并发交易数
}

// DefaultServiceConfig 默认配置
var DefaultServiceConfig = ServiceConfig{
	CounterUpdateWindow:       30 * time.Second,
	TransactionTimeout:        10 * time.Minute,
	RequireStartCounters:      false,
	AllowEmptyTransactions:    true,
	MaxConcurrentTransactions: 8, // 每个设备最多8个喷嘴
}

// NewService 创建交易生命周期服务，直接返回 TransactionLifecycleServiceInterface
func NewService(
	repository Repository,
	nozzleCountersService nozzle_counters.Service,
	nozzleService nozzle.ServiceV2,
	logger *zap.Logger,
	config ServiceConfig,
	operatorIDCache OperatorIDCacheInterface,
	runtimeStateService device_runtime_state.DeviceRuntimeStateService, // 🆕 新增：设备运行时状态服务
) TransactionLifecycleServiceInterface {
	if config.CounterUpdateWindow == 0 {
		config = DefaultServiceConfig
	}

	return &service{
		repository:            repository,
		nozzleService:         nozzleService,
		nozzleCountersService: nozzleCountersService,
		logger:                logger,
		config:                config,
		listeners:             make([]TransactionLifecycleListenerInterface, 0),
		operatorIDCache:       operatorIDCache,
		runtimeStateService:   runtimeStateService, // 🆕 新增：注入设备运行时状态服务
	}
}

// HandleNozzleOut 处理DC3 Nozzle Out事务，更新现有交易或创建新交易
func (s *service) HandleNozzleOut(ctx context.Context, deviceID string, nozzleID string, price decimal.Decimal, timestamp time.Time) (*models.Transaction, error) {
	s.logger.Info("[TxLifecycle] 处理DC3喷嘴拔出 - 确保活跃交易并更新价格",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("price", price.String()))

	// 1. 查找现有活跃交易，不创建新的
	tx, err := s.repository.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active transaction: %w", err)
	}

	if tx == nil {
		s.logger.Debug("No active transaction for filling start, this may be normal",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil, fmt.Errorf("failed to get active transaction: %w", err)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to ensure active transaction: %w", err)
	}

	// 2. 🆕 确保 StartedAt 字段有值（nozzle out 表示交易开始）
	needsUpdate := false
	if tx.StartedAt == nil {
		tx.StartedAt = &timestamp
		tx.UpdatedAt = time.Now()
		needsUpdate = true

		s.logger.Info("[TxLifecycle] 设置交易开始时间",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Time("started_at", timestamp))

		// if tx.OperatorID == "" {
		// 	// 从缓存中获取操作员ID
		// 	operatorID := s.operatorIDCache.Consume()
		// 	if operatorID != "" {
		// 		tx.OperatorID = operatorID
		// 		tx.UpdatedAt = time.Now()
		// 		needsUpdate = true

		// 		s.logger.Info("[TxLifecycle] 从缓存中获取并设置操作员ID",
		// 			zap.String("transaction_id", tx.ID),
		// 			zap.String("device_id", deviceID),
		// 			zap.String("nozzle_id", nozzleID),
		// 			zap.String("operator_id", operatorID))
		// 	}
		// }

	}

	// 3. 更新价格（如果价格有效且不同）
	if !price.IsZero() && !price.Equal(tx.UnitPrice) {
		oldPrice := tx.UnitPrice
		tx.UnitPrice = price
		tx.UpdatedAt = time.Now()
		needsUpdate = true

		// 更新状态历史记录价格变更
		var existingHistory []models.TransactionStateChange
		if len(tx.StateHistory) > 0 {
			json.Unmarshal(tx.StateHistory, &existingHistory)
		}
		existingHistory = append(existingHistory, models.TransactionStateChange{
			FromStatus: tx.Status,
			ToStatus:   tx.Status, // 状态不变，只是价格更新
			Timestamp:  timestamp,
			Reason:     fmt.Sprintf("Price updated from %s to %s via DC3 Nozzle Out", oldPrice.String(), price.String()),
			Source:     "DC3",
		})
		stateHistoryJSON, _ := json.Marshal(existingHistory)
		tx.StateHistory = datatypes.JSON(stateHistoryJSON)

		s.logger.Info("[TxLifecycle] 通过喷嘴拔出更新交易价格",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("old_price", oldPrice.String()),
			zap.String("new_price", price.String()))
	} else {
		s.logger.Debug("[TxLifecycle] 交易价格未变或为零，无需更新",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("current_price", tx.UnitPrice.String()),
			zap.String("incoming_price", price.String()))
	}

	// 4. 🆕 如果有任何更新（StartedAt 或价格），保存交易
	if needsUpdate {
		if err := s.repository.Update(ctx, tx); err != nil {
			return nil, fmt.Errorf("failed to update transaction: %w", err)
		}

		// 通知监听器
		s.notifyTransactionUpdated(tx)
	}

	return tx, nil
}

// HandleAuth 处理授权事件
// 🎯 业务逻辑：设备授权时，确保有交易可用并更新为授权状态
func (s *service) HandleAuth(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error {
	s.logger.Info("Handling Auth event",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	// 1. 确保有活跃交易
	tx, err := s.EnsureActiveTransaction(ctx, deviceID, nozzleID, "auth")
	if err != nil {
		return fmt.Errorf("failed to ensure active transaction: %w", err)
	}

	// 3. 更新交易状态为授权 - 暂时使用已有状态
	oldStatus := tx.Status
	tx.Status = models.TransactionStatusActive // 🔧 临时使用已有状态，后续添加Authorized状态
	tx.StartedAt = &timestamp                  // 🔧 使用已有字段StartedAt代替AuthorizedAt
	tx.UpdatedAt = time.Now()

	// 4. 更新状态历史
	var existingHistory []models.TransactionStateChange
	if len(tx.StateHistory) > 0 {
		json.Unmarshal(tx.StateHistory, &existingHistory)
	}
	existingHistory = append(existingHistory, models.TransactionStateChange{
		FromStatus: oldStatus,
		ToStatus:   models.TransactionStatusActive,
		Timestamp:  timestamp,
		Reason:     fmt.Sprintf("Operator %s authorized", tx.OperatorID),
		Source:     "DC1_auth",
	})
	stateHistoryJSON, _ := json.Marshal(existingHistory)
	tx.StateHistory = datatypes.JSON(stateHistoryJSON)

	// 5. 保存更新
	if err := s.repository.Update(ctx, tx); err != nil {
		return fmt.Errorf("failed to update transaction with auth: %w", err)
	}

	// 6. 通知监听器
	s.notifyTransactionUpdated(tx)

	s.logger.Info("Transaction authorized successfully",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	return nil
}

// HandleFillingStart 处理开始加油事件
// 🎯 业务逻辑：设备开始加油时，更新交易状态并记录开始时间
// 🔧 修复：避免与DC2的filling状态转换重复
func (s *service) HandleFillingStart(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error {
	s.logger.Info("Handling Filling Start event",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	// 1. 查找现有活跃交易，不创建新的
	tx, err := s.repository.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
	if err != nil {
		return fmt.Errorf("failed to get active transaction: %w", err)
	}

	if tx == nil {
		s.logger.Debug("No active transaction for filling start, this may be normal",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil
	}

	// 2. 检查是否已经是filling状态，避免重复转换
	if tx.Status == models.TransactionStatusFilling {
		s.logger.Debug("Transaction already in filling status, skipping duplicate transition",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil
	}

	// 3. 只有在非filling状态时才转换
	if tx.Status == models.TransactionStatusInitiated || tx.Status == models.TransactionStatusActive {
		oldStatus := tx.Status
		tx.Status = models.TransactionStatusFilling
		tx.FillingAt = &timestamp
		tx.UpdatedAt = time.Now()

		// 4. 更新状态历史
		s.addStateHistory(tx, oldStatus, models.TransactionStatusFilling, timestamp, "DC1 Filling started", "DC1_filling")

		// 5. 保存更新
		if err := s.repository.Update(ctx, tx); err != nil {
			return fmt.Errorf("failed to update transaction with filling start: %w", err)
		}

		// 6. 通知监听器
		s.notifyTransactionUpdated(tx)

		s.logger.Info("Transaction filling started via DC1",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("from_status", string(oldStatus)))
	}

	return nil
}

// HandleVolumeAmountUpdate 处理DC2体积金额更新 - 记录当前已加注的总量, 或者完成交易
func (s *service) HandleVolumeAmountUpdate(ctx context.Context, deviceID string, nozzleID string, volume, amount decimal.Decimal, timestamp time.Time) error {
	s.logger.Debug("[TxLifecycle] 处理DC2体积金额更新 - 当前加油总量",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("current_volume", volume.String()),
		zap.String("current_amount", amount.String()))

	// 🔧 修复：只查找现有活跃交易，不创建新的
	tx, err := s.repository.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
	if err != nil {
		return fmt.Errorf("failed to get active transaction: %w", err)
	}

	if tx == nil {
		s.logger.Debug("No active transaction for DC2 volume/amount update, this is normal - nozzle ready for next session",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil
	}

	// 2. 🎯 核心修正：DC2是当前已加注的总量，不是累加增量
	tx.ActualVolume = volume // 当前已加注总体积
	tx.ActualAmount = amount // 当前已加注总金额
	tx.TotalAmount = amount  // 同步更新总金额
	tx.UpdatedAt = time.Now()

	// 3. 🎯 关键修正：根据当前状态进行状态转换
	// 因为已经确保了单一活跃交易，所以任何initiated交易都可以安全地转换为filling
	if (tx.Status == models.TransactionStatusInitiated || tx.Status == models.TransactionStatusActive) && (!volume.IsZero() || !amount.IsZero()) {
		// 转换到filling状态
		oldStatus := tx.Status
		tx.Status = models.TransactionStatusFilling
		tx.FillingAt = &timestamp

		// 记录状态转换
		var existingHistory []models.TransactionStateChange
		if len(tx.StateHistory) > 0 {
			json.Unmarshal(tx.StateHistory, &existingHistory)
		}
		existingHistory = append(existingHistory, models.TransactionStateChange{
			FromStatus: oldStatus,
			ToStatus:   models.TransactionStatusFilling,
			Timestamp:  timestamp,
			Reason:     fmt.Sprintf("DC2 fueling started: %s L, %s Yuan", volume.String(), amount.String()),
			Source:     "DC2",
		})
		stateHistoryJSON, _ := json.Marshal(existingHistory)
		tx.StateHistory = datatypes.JSON(stateHistoryJSON)

		s.logger.Info("[TxLifecycle] 交易转换为加油状态",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("volume", volume.String()),
			zap.String("amount", amount.String()))
	} else if tx.Status == models.TransactionStatusAwaitingFinalDC2 && (!volume.IsZero() || !amount.IsZero()) {
		// 🆕 简化完成机制：awaiting_final_dc2 → completed
		// 这是CD1 04H命令的响应DC2数据，表示收到了最终的加油数据，直接完成交易
		oldStatus := tx.Status
		tx.Status = models.TransactionStatusCompleted
		completedTime := timestamp
		tx.CompletedAt = &completedTime

		// 记录状态转换
		var existingHistory []models.TransactionStateChange
		if len(tx.StateHistory) > 0 {
			json.Unmarshal(tx.StateHistory, &existingHistory)
		}
		existingHistory = append(existingHistory, models.TransactionStateChange{
			FromStatus: oldStatus,
			ToStatus:   models.TransactionStatusCompleted,
			Timestamp:  timestamp,
			Reason:     fmt.Sprintf("CD1 04H response received - final DC2 data: %s L, %s Yuan, transaction completed", volume.String(), amount.String()),
			Source:     "DC2_final",
		})
		stateHistoryJSON, _ := json.Marshal(existingHistory)
		tx.StateHistory = datatypes.JSON(stateHistoryJSON)

		s.logger.Info("[TxLifecycle] 🆕 交易通过最终DC2数据完成",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("final_volume", volume.String()),
			zap.String("final_amount", amount.String()),
			zap.String("note", "已收到CD1 04H响应的最终DC2数据，交易完成"))

		// 通知监听器交易完成
		defer s.notifyTransactionCompleted(tx)
	} else {
		// 记录数据更新
		var existingHistory []models.TransactionStateChange
		if len(tx.StateHistory) > 0 {
			json.Unmarshal(tx.StateHistory, &existingHistory)
		}
		existingHistory = append(existingHistory, models.TransactionStateChange{
			FromStatus: tx.Status,
			ToStatus:   tx.Status, // 状态不变，只是数据更新
			Timestamp:  timestamp,
			Reason:     fmt.Sprintf("DC2 current totals: %s L, %s Yuan", volume.String(), amount.String()),
			Source:     "DC2",
		})
		stateHistoryJSON, _ := json.Marshal(existingHistory)
		tx.StateHistory = datatypes.JSON(stateHistoryJSON)
	}

	// 4. 保存交易更新
	if err := s.repository.Update(ctx, tx); err != nil {
		return fmt.Errorf("failed to update transaction with DC2 data: %w", err)
	}

	// 5. 🎯 重要：更新Nozzle的current_amount和current_volume
	// 确保DC2数据同步到Nozzle表
	if s.nozzleService != nil {
		if err := s.updateNozzleCurrentData(ctx, deviceID, nozzleID, volume, amount); err != nil {
			s.logger.Error("[TxLifecycle] 更新喷嘴当前数据失败",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Error(err))
			// 不返回错误，因为交易数据已更新成功
		}
	}

	// 6. 通知监听器
	s.notifyTransactionUpdated(tx)

	s.logger.Debug("[TxLifecycle] DC2当前总量更新成功")

	return nil
}

// updateNozzleCurrentData 更新喷嘴的当前体积和金额数据
func (s *service) updateNozzleCurrentData(ctx context.Context, deviceID string, nozzleID string, volume, amount decimal.Decimal) error {
	s.logger.Debug("[TxLifecycle] 更新喷嘴当前数据",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("volume", volume.String()),
		zap.String("amount", amount.String()))

	// 调用nozzle service更新current_volume和current_amount
	if err := s.nozzleService.UpdateNozzleDataDirect(ctx, deviceID, nozzleID, volume, amount); err != nil {
		return fmt.Errorf("failed to update nozzle current data: %w", err)
	}
	return nil
}

// HandleNozzleIn 处理喷嘴插回事件
// 🎯 业务逻辑：喷嘴插回只做状态更新，不创建新交易
func (s *service) HandleNozzleIn(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error {
	s.logger.Info("Handling Nozzle In - updating existing transaction state only",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	// 🔧 修复：只查找现有活跃交易，不创建新的
	tx, err := s.repository.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
	if err != nil {
		return fmt.Errorf("failed to get active transaction: %w", err)
	}

	if tx == nil {
		// 没有活跃交易是正常的，喷嘴插回不需要创建交易
		s.logger.Debug("No active transaction for nozzle_in, this is normal - nozzle ready for next session",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil
	}

	// 2. 🎯 关键修改：只更新状态，不完成交易
	// 如果交易正在加油中，标记为准备完成状态
	if tx.Status == models.TransactionStatusFilling {
		// 可以添加一个"准备完成"的状态更新，但不完成交易
		// 交易的实际完成由 DC1 FILLING_COMPLETED 事件处理

		// 更新状态历史记录喷嘴插回事件
		var existingHistory []models.TransactionStateChange
		if len(tx.StateHistory) > 0 {
			json.Unmarshal(tx.StateHistory, &existingHistory)
		}
		existingHistory = append(existingHistory, models.TransactionStateChange{
			FromStatus: tx.Status,
			ToStatus:   tx.Status, // 状态不变，只记录事件
			Timestamp:  timestamp,
			Reason:     "Nozzle inserted back - ready for completion",
			Source:     "DC3_nozzle_in",
		})
		stateHistoryJSON, _ := json.Marshal(existingHistory)
		tx.StateHistory = datatypes.JSON(stateHistoryJSON)
		tx.UpdatedAt = time.Now()

		// 保存状态更新
		if err := s.repository.Update(ctx, tx); err != nil {
			return fmt.Errorf("failed to update transaction state: %w", err)
		}

		// 通知监听器
		s.notifyTransactionUpdated(tx)

		s.logger.Info("Transaction state updated for nozzle in (not completed)",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("status", string(tx.Status)))
	} else {
		s.logger.Debug("Transaction not in filling state, no state update needed",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("status", string(tx.Status)))
	}

	return nil
}

// HandleStop 处理停止事件, Nozzle 会变成 FILLING COMPLETE, 走上面的流程
func (s *service) HandleStop(ctx context.Context, deviceID string, nozzleID string, timestamp time.Time) error {
	s.logger.Info("Handling Stop event",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	// 1. 获取当前活跃交易
	tx, err := s.repository.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
	if err != nil {
		return fmt.Errorf("failed to get active transaction: %w", err)
	}

	if tx == nil {
		s.logger.Debug("No active transaction for filling start, this may be normal",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil
	}

	// 2. 更新状态历史记录价格变更
	var existingHistory []models.TransactionStateChange
	if len(tx.StateHistory) > 0 {
		json.Unmarshal(tx.StateHistory, &existingHistory)
	}
	existingHistory = append(existingHistory, models.TransactionStateChange{
		FromStatus: tx.Status,
		ToStatus:   tx.Status, // 状态不变，只是记录
		Timestamp:  timestamp,
		Reason:     fmt.Sprintf("Handling Stop event for device %s nozzle %s", deviceID, nozzleID),
		Source:     "DC1",
	})
	stateHistoryJSON, _ := json.Marshal(existingHistory)
	tx.StateHistory = datatypes.JSON(stateHistoryJSON)

	return nil
}

// HandleFillingCompleted 处理DC1 FILLING_COMPLETED事务 - 简化完成机制
// 🆕 简化完成：基于丢包检测的双路径处理，只依赖DC1和DC2
func (s *service) HandleFillingCompleted(ctx context.Context, deviceID string, timestamp time.Time) error {
	s.logger.Info("[TxLifecycle] 处理DC1加油完成事件 - 简化完成机制",
		zap.String("device_id", deviceID),
		zap.String("flow", "丢包检测 → 双路径处理 → 基于DC2完成"))

	// 查找设备所有活跃交易
	activeTransactions, err := s.repository.GetActiveByDevice(ctx, deviceID)
	if err != nil {
		return fmt.Errorf("failed to get active transactions: %w", err)
	}

	if len(activeTransactions) == 0 {
		s.logger.Debug("[TxLifecycle] 加油完成时未找到活跃交易",
			zap.String("device_id", deviceID))
		return nil
	}

	// 处理每个filling状态的交易
	for _, tx := range activeTransactions {
		if tx.Status != models.TransactionStatusFilling {
			continue
		}

		// 🔍 简化丢包检测：基于DC2数据完整性
		hasPacketLoss := s.detectPacketLoss(tx, timestamp)

		if hasPacketLoss {
			// 有丢包：发送CD1 04H确认，等待最终DC2
			s.logger.Info("[TxLifecycle] PacketLoss检测到丢包，发送CD1 04H确认等待最终DC2",
				zap.String("device_id", deviceID),
				zap.String("transaction_id", tx.ID),
				zap.String("path", "packet_loss_await_final_dc2"))
			return s.handleCompletedWithPacketLoss(ctx, tx, timestamp)
		} else {
			// 无丢包：直接完成交易
			s.logger.Info("[TxLifecycle] NoPacketLoss无丢包检测，直接完成交易",
				zap.String("device_id", deviceID),
				zap.String("transaction_id", tx.ID),
				zap.String("path", "no_packet_loss_direct"))
			return s.handleCompletedWithoutPacketLoss(ctx, tx, timestamp)
		}
	}

	return nil
}

// 🆕 简化丢包检测逻辑：基于DC2数据完整性
func (s *service) detectPacketLoss(tx *models.Transaction, fillingCompletedTime time.Time) bool {
	// 1. 检查基础数据完整性
	if tx.ActualVolume.IsZero() || tx.ActualAmount.IsZero() {
		s.logger.Debug("[TxLifecycle] PacketLoss基础数据不完整",
			zap.String("transaction_id", tx.ID),
			zap.Bool("has_volume", !tx.ActualVolume.IsZero()),
			zap.Bool("has_amount", !tx.ActualAmount.IsZero()))
		return true
	}

	// 3. 🆕 从运行时状态服务获取TX重置时间
	lastTxResetTime, err := s.runtimeStateService.GetTxResetTime(tx.DeviceID)
	if err != nil {
		s.logger.Warn("[TxLifecycle] PacketLoss获取TX重置时间失败",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", tx.DeviceID),
			zap.Error(err))
		return true
	} else if !lastTxResetTime.IsZero() && tx.StartedAt != nil {
		// 🔧 修复：检查 StartedAt 是否为 nil，避免 panic
		// 重置时间晚于开始时间是丢包

		timeSinceReset := tx.StartedAt.Sub(lastTxResetTime)
		if timeSinceReset <= 0 { // 配置参数：TX重置后5秒内认为可能有丢包
			s.logger.Debug("[TxLifecycle] PacketLoss检测到近期TX重置",
				zap.String("transaction_id", tx.ID),
				zap.String("device_id", tx.DeviceID),
				zap.Duration("time_since_reset", timeSinceReset),
				zap.Time("last_reset_time", lastTxResetTime),
				zap.Time("tx_started_at", *tx.StartedAt))
			return true
		}
	}

	s.logger.Debug("[TxLifecycle] PacketLoss数据完整性检查通过",
		zap.String("transaction_id", tx.ID),
		zap.String("volume", tx.ActualVolume.String()),
		zap.String("amount", tx.ActualAmount.String()))

	return false
}

// 🆕 简化无丢包处理 - 直接完成交易
func (s *service) handleCompletedWithoutPacketLoss(ctx context.Context, tx *models.Transaction, timestamp time.Time) error {
	oldStatus := tx.Status
	tx.Status = models.TransactionStatusCompleted
	tx.CompletedAt = &timestamp
	tx.UpdatedAt = time.Now()

	// 记录状态转换
	s.addStateHistory(tx, oldStatus, models.TransactionStatusCompleted, timestamp,
		"No packet loss detected - direct completion", "DC1_no_packet_loss")

	// 保存更新
	if err := s.repository.Update(ctx, tx); err != nil {
		return fmt.Errorf("failed to complete transaction without packet loss: %w", err)
	}

	s.logger.Info("[TxLifecycle] NoPacketLoss无丢包直接完成交易",
		zap.String("transaction_id", tx.ID),
		zap.String("completion_type", "direct"))

	// 通知监听器
	s.notifyTransactionCompleted(tx)

	return nil
}

// 🆕 简化有丢包处理 - CD1确认等待最终DC2
func (s *service) handleCompletedWithPacketLoss(ctx context.Context, tx *models.Transaction, timestamp time.Time) error {
	oldStatus := tx.Status
	tx.Status = models.TransactionStatusAwaitingFinalDC2
	tx.AwaitingFinalDC2At = &timestamp
	tx.UpdatedAt = time.Now()

	// 记录状态转换
	s.addStateHistory(tx, oldStatus, models.TransactionStatusAwaitingFinalDC2, timestamp,
		"Packet loss detected - sending CD1 04H for final DC2 confirmation", "DC1_packet_loss")

	// 保存更新
	if err := s.repository.Update(ctx, tx); err != nil {
		return fmt.Errorf("failed to handle transaction with packet loss: %w", err)
	}

	s.logger.Info("[TxLifecycle] PacketLoss检测到丢包，发送CD1 04H确认等待最终DC2",
		zap.String("transaction_id", tx.ID),
		zap.String("status", "awaiting_final_dc2"),
		zap.String("next_step", "wait_for_final_dc2"))

	// 通知监听器触发CD1 04H命令
	s.notifyTransactionPending(tx)

	return nil
}

// HandleDC101CounterUpdate 处理DC101泵码更新
func (s *service) HandleDC101CounterUpdate(ctx context.Context,
	deviceID, nozzleID string,
	counterRecord *models.NozzleCounters) error {

	// 边界检查
	if counterRecord.PreviousValue == nil {
		s.logger.Debug("DC101 counter has no previous value, skipping end pump reading update",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return nil
	}

	// 🎯 直接用 SQL 查询匹配的交易
	tx, err := s.repository.GetTransactionByPumpReading(ctx,
		deviceID, nozzleID,
		counterRecord.CounterType,
		*counterRecord.PreviousValue)

	if err != nil {
		s.logger.Error("Failed to find matching transaction for DC101 update",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return err
	}

	if tx == nil {
		s.logger.Debug("No matching transaction found for DC101 counter update",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Int16("counter_type", counterRecord.CounterType),
			zap.String("previous_value", counterRecord.PreviousValue.String()))
		return nil // 正常情况，不报错
	}

	// 🎯 更新对应的 end pump reading
	updated := false
	if counterRecord.IsVolumeCounter() {
		tx.EndPumpVolumeReading = &counterRecord.CounterValue
		updated = true
		s.logger.Debug("Updated end pump volume reading",
			zap.String("transaction_id", tx.ID),
			zap.String("end_volume", counterRecord.CounterValue.String()))
	} else if counterRecord.IsAmountCounter() {
		tx.EndPumpAmountReading = &counterRecord.CounterValue
		updated = true
		s.logger.Debug("Updated end pump amount reading",
			zap.String("transaction_id", tx.ID),
			zap.String("end_amount", counterRecord.CounterValue.String()))
	}

	if updated {
		tx.UpdatedAt = time.Now()

		// 更新泵码质量
		if tx.PumpReadingQuality == "" {
			tx.PumpReadingQuality = "good"
		}

		// 保存更新
		if err := s.repository.Update(ctx, tx); err != nil {
			s.logger.Error("Failed to update transaction with end pump reading",
				zap.String("transaction_id", tx.ID),
				zap.Error(err))
			return fmt.Errorf("failed to update transaction: %w", err)
		}

		s.logger.Info("Successfully updated transaction end pump reading from DC101",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("counter_type", counterRecord.GetCounterTypeName()),
			zap.String("end_reading", counterRecord.CounterValue.String()))

		// 🎯 触发交易更新事件
		s.notifyTransactionCounterUpdated(tx)
	}

	return nil
}

// GetActiveTransaction 获取活跃交易
func (s *service) GetActiveTransaction(ctx context.Context, deviceID string, nozzleID string) (*models.Transaction, error) {
	return s.repository.GetActiveByDeviceAndNozzle(ctx, deviceID, nozzleID)
}

// RegisterTransactionListener 注册交易监听器
func (s *service) RegisterTransactionListener(listener TransactionLifecycleListenerInterface) error {
	// 直接添加监听器，因为现在listeners类型已经是TransactionLifecycleListenerInterface
	s.listeners = append(s.listeners, listener)
	s.logger.Info("Registered transaction lifecycle listener",
		zap.String("listener_id", listener.GetListenerID()))
	return nil
}

// addStateHistory 添加状态历史记录 - MVP辅助方法
func (s *service) addStateHistory(tx *models.Transaction, fromStatus, toStatus models.TransactionStatus, timestamp time.Time, reason, source string) {
	var existingHistory []models.TransactionStateChange
	if len(tx.StateHistory) > 0 {
		json.Unmarshal(tx.StateHistory, &existingHistory)
	}
	existingHistory = append(existingHistory, models.TransactionStateChange{
		FromStatus: fromStatus,
		ToStatus:   toStatus,
		Timestamp:  timestamp,
		Reason:     reason,
		Source:     source,
	})
	stateHistoryJSON, _ := json.Marshal(existingHistory)
	tx.StateHistory = datatypes.JSON(stateHistoryJSON)
}

// generateTransactionID 生成交易ID
func generateTransactionID() string {
	return uuid.New().String()
}

// 事件通知方法
func (s *service) notifyTransactionCreated(tx *models.Transaction) {
	for _, listener := range s.listeners {
		if err := listener.OnTransactionCreated(tx); err != nil {
			s.logger.Error("Transaction created listener error",
				zap.String("listener_id", listener.GetListenerID()),
				zap.Error(err))
		}
	}
}

func (s *service) notifyTransactionUpdated(tx *models.Transaction) {

}

func (s *service) notifyTransactionCounterUpdated(tx *models.Transaction) {
	for _, listener := range s.listeners {
		if err := listener.OnTransactionUpdated(tx); err != nil {
			s.logger.Error("Transaction updated listener error",
				zap.String("listener_id", listener.GetListenerID()),
				zap.Error(err))
		}
	}
}

func (s *service) notifyTransactionCompleted(tx *models.Transaction) {
	for _, listener := range s.listeners {
		if err := listener.OnTransactionCompleted(tx); err != nil {
			s.logger.Error("Transaction completed listener error",
				zap.String("listener_id", listener.GetListenerID()),
				zap.Error(err))
		}
	}
}

func (s *service) notifyTransactionPending(tx *models.Transaction) {
	for _, listener := range s.listeners {
		if err := listener.OnTransactionPending(tx); err != nil {
			s.logger.Error("Transaction pending listener error",
				zap.String("listener_id", listener.GetListenerID()),
				zap.Error(err))
		}
	}
}

// EnsureActiveTransaction 确保设备和喷嘴始终有一个活跃交易
func (s *service) EnsureActiveTransaction(ctx context.Context, deviceID string, nozzleID string, triggerReason string) (*models.Transaction, error) {
	// 🚀 Step 1: 直接使用SQL优化方法确保单一活跃交易
	keptTxID, cancelledCount, err := s.repository.EnsureSingleActiveTransaction(ctx, deviceID, nozzleID, triggerReason)
	if err != nil {
		s.logger.Error("Failed to ensure single active transaction",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("trigger", triggerReason),
			zap.Error(err))
		return nil, fmt.Errorf("failed to ensure single active transaction: %w", err)
	}

	if cancelledCount > 0 {
		s.logger.Info("Cancelled duplicate transactions during EnsureActiveTransaction",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Int("cancelled_count", cancelledCount),
			zap.String("trigger", triggerReason))
	}

	// 🚀 Step 2: 根据返回结果决定操作
	if keptTxID != nil {
		// 已有活跃交易，直接获取并返回
		existingTx, err := s.repository.GetByID(ctx, *keptTxID)
		if err != nil {
			return nil, fmt.Errorf("failed to get kept transaction: %w", err)
		}

		s.logger.Debug("[TxLifecycle] 清理后发现现有活跃交易",
			zap.String("transaction_id", existingTx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("status", string(existingTx.Status)),
			zap.String("trigger", triggerReason))

		return existingTx, nil
	}

	// 🚀 Step 3: 没有活跃交易，创建新的
	// Convert operatorID cache status to JSON string
	operatorStatus := s.operatorIDCache.GetStatus()
	operatorStatusJSON, _ := json.Marshal(operatorStatus)

	s.logger.Info("[TxLifecycle] 没有活跃交易，创建新的待机交易",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("trigger", triggerReason),
		zap.String("operatorID", string(operatorStatusJSON)))

	// operatorID

	// 获取nozzle信息用于燃料等级设置
	nozzle, nozzleErr := s.nozzleService.GetNozzleByID(ctx, nozzleID)
	if nozzleErr != nil {
		s.logger.Warn("[TxLifecycle] 无法获取喷嘴信息，不允许创建交易",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(nozzleErr))
		return nil, fmt.Errorf("failed to get nozzle information: %w", nozzleErr)
	}

	// 🎯 使用 nozzleCountersService 获取起始泵码
	s.logger.Debug("[TxLifecycle] 创建交易时获取起始泵码",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Uint8("nozzle_number", nozzle.Number))

	// 获取起始体积和金额泵码
	var startVolumeReading, startAmountReading *decimal.Decimal
	var missingCounters []string

	volumeCounterType := int16(nozzle.Number)      // 0x01-0x08
	amountCounterType := int16(nozzle.Number + 16) // 0x11-0x18

	// 获取起始体积泵码
	if volumeValue, err := s.nozzleCountersService.GetLatestCounterValue(ctx, deviceID, nozzleID, volumeCounterType); err == nil && !volumeValue.IsZero() {
		startVolumeReading = &volumeValue
		s.logger.Debug("[TxLifecycle] 获取到起始体积泵码",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("start_volume", volumeValue.String()))
	} else {
		s.logger.Debug("[TxLifecycle] 未获取到起始体积泵码",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		missingCounters = append(missingCounters, fmt.Sprintf("volume(0x%02x)", volumeCounterType))
	}

	// 获取起始金额泵码
	if amountValue, err := s.nozzleCountersService.GetLatestCounterValue(ctx, deviceID, nozzleID, amountCounterType); err == nil && !amountValue.IsZero() {
		startAmountReading = &amountValue
		s.logger.Debug("[TxLifecycle] 获取到起始金额泵码",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("start_amount", amountValue.String()))
	} else {
		s.logger.Debug("[TxLifecycle] 未获取到起始金额泵码",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		missingCounters = append(missingCounters, fmt.Sprintf("amount(0x%02x)", amountCounterType))
	}

	// 获取操作员ID
	operatorID := s.operatorIDCache.Consume() //

	// 🎯 新增：获取喷嘴的燃料等级信息
	var fuelGradeID *string
	var fuelGradeName string
	if nozzle.FuelGrade != nil {
		fuelGradeID = &nozzle.FuelGrade.ID
		fuelGradeName = nozzle.FuelGrade.Name
		s.logger.Debug("[TxLifecycle] 成功获取燃料等级信息",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("fuel_grade_id", nozzle.FuelGrade.ID),
			zap.String("fuel_grade_name", nozzle.FuelGrade.Name))
	} else {
		s.logger.Debug("Nozzle has no fuel grade configured",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
	}

	// 创建新的待机交易
	timestamp := time.Now()
	tx := &models.Transaction{
		ID:                  generateTransactionID(),
		Type:                models.TransactionTypeFuel,
		Status:              models.TransactionStatusInitiated,
		DeviceID:            deviceID,
		ControllerID:        deviceID,
		NozzleID:            nozzleID,
		UnitPrice:           decimal.Zero, // 初始价格为0，等待后续更新
		CounterUpdateWindow: s.config.CounterUpdateWindow,
		CreatedBy:           fmt.Sprintf("ensure_active_%s", triggerReason),
		OperatorID:          operatorID,
		// 🎯 新增：设置燃料等级信息
		FuelType:      nozzle.FuelGrade.Type,
		FuelGradeID:   fuelGradeID,
		FuelGradeName: fuelGradeName,
		// 🎯 设置起始泵码（从 nozzleCountersService 获取）
		StartPumpVolumeReading: startVolumeReading,
		StartPumpAmountReading: startAmountReading,
	}

	// 设置时间戳
	tx.InitiatedAt = &timestamp
	tx.CreatedAt = timestamp
	tx.UpdatedAt = timestamp

	// 🎯 设置泵码来源和质量，并记录到状态历史
	var pumpReadingInfo string
	var stateHistory []models.TransactionStateChange

	if startVolumeReading != nil || startAmountReading != nil {
		tx.PumpReadingSource = "nozzle_counters"
		tx.PumpReadingQuality = "good"

		// 记录成功获取的泵码信息
		var readingDetails []string
		if startVolumeReading != nil {
			readingDetails = append(readingDetails, fmt.Sprintf("start_volume:%s", startVolumeReading.String()))
		}
		if startAmountReading != nil {
			readingDetails = append(readingDetails, fmt.Sprintf("start_amount:%s", startAmountReading.String()))
		}
		pumpReadingInfo = fmt.Sprintf("Initial pump readings obtained: %s", strings.Join(readingDetails, ", "))

		s.logger.Debug("Transaction created with initial pump readings from nozzle_counters",
			zap.String("transaction_id", tx.ID),
			zap.String("start_volume", func() string {
				if startVolumeReading != nil {
					return startVolumeReading.String()
				}
				return "nil"
			}()),
			zap.String("start_amount", func() string {
				if startAmountReading != nil {
					return startAmountReading.String()
				}
				return "nil"
			}()))
	} else {
		tx.PumpReadingSource = "pending"
		tx.PumpReadingQuality = "pending"
		pumpReadingInfo = fmt.Sprintf("No initial pump readings available, will request via CD101: %s", strings.Join(missingCounters, ", "))

		s.logger.Debug("Transaction created without initial pump readings",
			zap.String("transaction_id", tx.ID),
			zap.String("reason", "No counter values available from nozzle_counters service"))
	}

	// 初始化状态历史，包含泵码信息
	stateHistory = []models.TransactionStateChange{
		{
			FromStatus: "",
			ToStatus:   models.TransactionStatusInitiated,
			Timestamp:  timestamp,
			Reason:     fmt.Sprintf("Auto-created standby transaction (%s) - %s - operatorID(%s)", triggerReason, pumpReadingInfo, operatorID),
			Source:     "system",
		},
	}
	stateHistoryJSON, _ := json.Marshal(stateHistory)
	tx.StateHistory = datatypes.JSON(stateHistoryJSON)

	// 保存到数据库
	if err := s.repository.Create(ctx, tx); err != nil {
		return nil, fmt.Errorf("failed to create standby transaction: %w", err)
	}

	// 通知监听器
	s.notifyTransactionCreated(tx)

	s.logger.Info("[TxLifecycle] 成功创建待机交易",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("status", string(tx.Status)),
		zap.String("trigger", triggerReason))

	// 🆕 改进1：如果没有起始泵码，立即发送CD101请求
	if len(missingCounters) > 0 {
		s.logger.Info("[TxLifecycle] 检测到缺失的起始泵码，触发CD101请求",
			zap.String("transaction_id", tx.ID),
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Strings("missing_counters", missingCounters))

	}

	return tx, nil
}

// GetActiveTransactions 获取设备所有活跃交易
func (s *service) GetActiveTransactions(ctx context.Context, deviceID string) ([]*models.Transaction, error) {
	return s.repository.GetActiveByDevice(ctx, deviceID)
}
