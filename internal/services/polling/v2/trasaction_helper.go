package v2

import (
	"fmt"
	// "time"

	"context"
	nozzleModel "fcc-service/internal/services/nozzle"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)



// updateAllNozzlesEnabled 更新设备所有喷嘴的启用状态
func (p *DevicePoller) updateAllNozzlesEnabled(ctx context.Context, isEnabled bool) error {
	// 获取设备所有喷嘴
	nozzles, err := p.nozzleService.GetDeviceNozzles(ctx, p.config.DeviceInfo.ID)
	if err != nil {
		return fmt.Errorf("获取设备喷嘴失败: %w", err)
	}

	// 更新每个喷嘴的启用状态
	for _, nozzle := range nozzles {
		if nozzle.IsEnabled != isEnabled {
			nozzle.IsEnabled = isEnabled
			if err := p.nozzleService.UpdateNozzle(ctx, nozzle); err != nil {
				p.logger.Error("更新喷嘴启用状态失败",
					zap.String("device_id", p.config.DeviceInfo.ID),
					zap.Uint8("nozzle_number", nozzle.Number),
					zap.Bool("is_enabled", isEnabled),
					zap.Error(err))
				continue // 继续处理其他喷嘴
			}
		}
	}

	return nil
}

// updateNozzleEnabled 更新单个喷嘴的启用状态
func (p *DevicePoller) updateNozzleEnabled(ctx context.Context, nozzleNumber byte, isEnabled bool) error {
	// 获取指定喷嘴
	nozzle, err := p.nozzleService.GetNozzle(ctx, p.config.DeviceInfo.ID, nozzleNumber)
	if err != nil {
		return fmt.Errorf("获取喷嘴失败: %w", err)
	}

	// 如果启用状态已经是目标状态，则无需更新
	if nozzle.IsEnabled == isEnabled {
		return nil
	}

	// 更新启用状态
	nozzle.IsEnabled = isEnabled
	if err := p.nozzleService.UpdateNozzle(ctx, nozzle); err != nil {
		return fmt.Errorf("更新喷嘴启用状态失败: %w", err)
	}

	return nil
}

// onWatchdogTimeout 看门狗超时处理 - 优化授权状态保护
func (p *DevicePoller) onWatchdogTimeout() {
	p.logger.Warn("设备看门狗超时",
		zap.String("device_id", p.config.DeviceInfo.ID))

	// 获取当前状态机状态和泵状态
	deviceState := p.deviceSM.GetStateData()
	currentPumpStatus := deviceState.PumpStatus

	// 📖 Wayne DART协议保护：授权和加油状态下避免强制reset
	if currentPumpStatus == 0x02 || currentPumpStatus == 0x04 || currentPumpStatus == 0x05 {
		statusName := p.getPumpStatusName(byte(currentPumpStatus))
		p.logger.Warn("⚠️ 授权/加油状态下的看门狗超时 - 使用温和恢复",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.Uint8("pump_status", byte(currentPumpStatus)),
			zap.String("status_name", statusName),
			zap.String("action", "发送状态查询而非reset"))

		// 温和恢复：仅发送状态查询，保持业务状态
		p.sendTXStatusQuery("watchdog_gentle_recovery")

		// 通知状态机超时，但不触发严厉的状态转换
		if err := p.deviceSM.OnPollTimeout(); err != nil {
			p.logger.Debug("状态机拒绝超时转换（授权状态保护）",
				zap.Error(err),
				zap.String("current_status", statusName))
		}
		return
	}

	// 对于其他状态，执行正常的超时处理
	p.logger.Info("非关键状态的看门狗超时 - 执行标准恢复",
		zap.String("device_id", p.config.DeviceInfo.ID),
		zap.Uint8("pump_status", byte(currentPumpStatus)),
		zap.String("status_name", p.getPumpStatusName(byte(currentPumpStatus))))

	// 通知状态机超时
	if err := p.deviceSM.OnPollTimeout(); err != nil {
		p.logger.Error("通知状态机超时失败", zap.Error(err))
	}

	// 发送TX重置命令恢复
	p.sendTXResetCommand("recovery")
}

// updateCounterCache 更新计数器缓存
// func (ta *TransactionAssembler) updateCounterCache(counterType byte, counterValue decimal.Decimal, timestamp time.Time) {
// 	nozzleID := ta.extractNozzleFromCounterType(counterType)
// 	if nozzleID > 0 {
// 		if counterType >= 0x01 && counterType <= 0x08 {
// 			ta.pumpReadingCache.UpdateVolumeCounter(nozzleID, counterValue, timestamp)
// 		} else if counterType >= 0x11 && counterType <= 0x18 {
// 			ta.pumpReadingCache.UpdateAmountCounter(nozzleID, counterValue, timestamp)
// 		}
// 	}
// }

// extractNozzleFromCounterType 从计数器类型提取喷嘴ID
func (ta *TransactionAssembler) extractNozzleFromCounterType(counterType byte) byte {
	if counterType >= 0x01 && counterType <= 0x08 {
		return counterType
	} else if counterType >= 0x11 && counterType <= 0x18 {
		return counterType - 0x10
	}
	return 0
}

// GetDeviceStatus 获取设备状态
func (ta *TransactionAssembler) GetDeviceStatus() string {
	ta.mu.RLock()
	defer ta.mu.RUnlock()
	return ta.deviceStatus
}

// getCurrentActiveNozzle 获取当前活跃喷嘴
func (ta *TransactionAssembler) getCurrentActiveNozzle() *byte {
	ta.mu.RLock()
	defer ta.mu.RUnlock()
	return ta.currentActiveNozzle
}

// getCurrentActiveNozzleID 获取当前活跃喷嘴ID
func (ta *TransactionAssembler) getCurrentActiveNozzleID() string {
	ta.mu.RLock()
	defer ta.mu.RUnlock()
	return ta.currentActiveNozzleID
}

// getLastActiveNozzle 获取最后活跃喷嘴（简化版本）
func (ta *TransactionAssembler) getLastActiveNozzle() *byte {
	ta.mu.RLock()
	defer ta.mu.RUnlock()
	return ta.currentActiveNozzle
}

// getPumpStatusName 获取泵状态名称
func (ta *TransactionAssembler) getPumpStatusName(status byte) string {
	switch status {
	case 0x01:
		return "RESET"
	case 0x02:
		return "AUTHORIZED"
	case 0x03:
		return "CALLING"
	case 0x04:
		return "FILLING"
	case 0x05:
		return "FILLING_COMPLETED"
	case 0x06:
		return "STOPPED"
	case 0x07:
		return "SUSPENDED"
	case 0x08:
		return "NOZZLE_OUT"
	default:
		return fmt.Sprintf("UNKNOWN_%d", status)
	}
}

// getDevicePricesFromNozzleService 从喷嘴服务获取设备价格
func (p *DevicePoller) getDevicePricesFromNozzleService(ctx context.Context) ([]nozzleModel.NozzlePriceInfo, error) {
	if p.nozzleService == nil {
		return nil, fmt.Errorf("喷嘴服务不可用")
	}

	nozzles, err := p.nozzleService.GetDeviceNozzles(ctx, p.config.DeviceInfo.ID)
	if err != nil {
		return nil, fmt.Errorf("获取设备喷嘴失败: %w", err)
	}

	var prices []nozzleModel.NozzlePriceInfo
	for _, nozzle := range nozzles {
		if nozzle.IsEnabled {
			priceInt := nozzle.CurrentPrice.IntPart()
			if priceInt < 1 || priceInt > 999999 {
				priceInt = 6333 // 默认价格
			}

			prices = append(prices, nozzleModel.NozzlePriceInfo{
				NozzleNumber: nozzle.Number,
				Price:        decimal.NewFromInt(priceInt),
				Decimals:     3,
			})
		}
	}

	if len(prices) == 0 {
		return nil, fmt.Errorf("没有找到启用的喷嘴")
	}

	return prices, nil
}

// 辅助方法
func (p *DevicePoller) getPumpStatusName(status byte) string {
	switch status {
	case 0x00:
		return "PUMP_NOT_PROGRAMMED"
	case 0x01:
		return "RESET" // RESET状态对应数据库中的idle状态
	case 0x02:
		return "AUTHORIZED" // 修正：Wayne DART协议授权状态码是0x02
	case 0x04:
		return "FILLING"
	case 0x05:
		return "FILLING_COMPLETED"
	case 0x06:
		return "SUSPENDED"
	case 0x07:
		return "ERROR"
	default:
		return fmt.Sprintf("UNKNOWN_STATUS_0x%02X", status)
	}
}

func (p *DevicePoller) getAlarmCodeName(alarmCode byte) string {
	switch alarmCode {
	case 0x01:
		return "GENERAL_ALARM"
	case 0x02:
		return "COMMUNICATION_ERROR"
	case 0x03:
		return "HARDWARE_FAILURE"
	default:
		return fmt.Sprintf("UNKNOWN_ALARM_0x%02X", alarmCode)
	}
}