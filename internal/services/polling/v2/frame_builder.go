package v2

import (
	"fcc-service/internal/adapters/wayne/dartline"
)

// FrameBuilder DART协议帧构建器接口
type FrameBuilder interface {
	BuildPollFrame(address byte) (*dartline.Frame, error)
	BuildDataFrame(address, txNum byte, data []byte) (*dartline.Frame, error)
	ParseResponse(data []byte) (*dartline.Frame, error)
}

// DefaultFrameBuilder 默认帧构建器实现
// 复用现有的dartline包进行帧构建和解析
type DefaultFrameBuilder struct {
	// 可以添加配置参数，如协议版本等
}

// NewDefaultFrameBuilder 创建默认帧构建器
func NewDefaultFrameBuilder() *DefaultFrameBuilder {
	return &DefaultFrameBuilder{}
}

// BuildPollFrame 构建轮询帧
// Wayne DART协议：POLL使用纯20H格式，不包含TX#
func (dfb *DefaultFrameBuilder) BuildPollFrame(address byte) (*dartline.Frame, error) {
	return dartline.CreatePollFrame(address, 0)
}

// BuildDataFrame 构建数据帧
// Wayne DART协议：DATA使用30H+TX#格式
func (dfb *DefaultFrameBuilder) BuildDataFrame(address, txNum byte, data []byte) (*dartline.Frame, error) {
	return dartline.CreateDataFrame(address, txNum, data)
}

// ParseResponse 解析响应帧
// 支持所有DART协议帧类型：ACK/NAK/DATA/EOT
func (dfb *DefaultFrameBuilder) ParseResponse(data []byte) (*dartline.Frame, error) {
	return dartline.DecodeFrame(data)
}

// WayneFrameBuilder Wayne专用帧构建器
// 可以扩展支持Wayne特定的协议特性
type WayneFrameBuilder struct {
	DefaultFrameBuilder
	// Wayne特定配置
	protocolVersion string
	deviceType      string
}

// NewWayneFrameBuilder 创建Wayne专用帧构建器
func NewWayneFrameBuilder(protocolVersion, deviceType string) *WayneFrameBuilder {
	return &WayneFrameBuilder{
		DefaultFrameBuilder: DefaultFrameBuilder{},
		protocolVersion:     protocolVersion,
		deviceType:          deviceType,
	}
}
