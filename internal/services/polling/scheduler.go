package polling

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/pkg/api"
	"fcc-service/pkg/models"
)

// 默认轮询配置常量
const (
	DefaultPollInterval = 10 * time.Second // 正常设备轮询间隔
	FastPollInterval    = 10 * time.Second  // 新发现设备快速轮询间隔
	SlowPollInterval    = 10 * time.Second // 空闲设备慢速轮询间隔
	MaxFailures         = 5                // 最大失败次数
)

// PollConfig 轮询配置
type PollConfig struct {
	DefaultInterval time.Duration `json:"default_interval"` // 默认轮询间隔
	FastInterval    time.Duration `json:"fast_interval"`    // 快速轮询间隔
	SlowInterval    time.Duration `json:"slow_interval"`    // 慢速轮询间隔
	MaxFailures     int           `json:"max_failures"`     // 最大失败次数
}

// DevicePollInfo 设备轮询信息
type DevicePollInfo struct {
	DeviceID     string        `json:"device_id"`     // 设备ID
	LastPoll     time.Time     `json:"last_poll"`     // 上次轮询时间
	NextPoll     time.Time     `json:"next_poll"`     // 下次轮询时间
	PollInterval time.Duration `json:"poll_interval"` // 轮询间隔
	FailureCount int           `json:"failure_count"` // 失败次数
	Status       string        `json:"status"`        // 轮询状态
	Priority     PollPriority  `json:"priority"`      // 轮询优先级

	// 重连和错误处理
	ReconnectInfo *ReconnectInfo `json:"reconnect_info,omitempty"` // 重连信息
	LastError     *ErrorInfo     `json:"last_error,omitempty"`     // 最后一次错误
	ErrorHistory  []*ErrorInfo   `json:"error_history,omitempty"`  // 错误历史
}

// PollPriority 轮询优先级
type PollPriority int

const (
	PriorityNormal PollPriority = iota // 正常优先级
	PriorityHigh                       // 高优先级
	PriorityLow                        // 低优先级
)

// PollEvent 轮询事件
type PollEvent struct {
	DeviceID  string                 `json:"device_id"`  // 设备ID
	EventType string                 `json:"event_type"` // 事件类型
	Data      map[string]interface{} `json:"data"`       // 事件数据
	Timestamp time.Time              `json:"timestamp"`  // 时间戳
}

// PollStatistics 轮询统计信息
type PollStatistics struct {
	TotalDevices    int           `json:"total_devices"`    // 总设备数
	TotalPolls      int64         `json:"total_polls"`      // 总轮询次数
	SuccessfulPolls int64         `json:"successful_polls"` // 成功轮询次数
	FailedPolls     int64         `json:"failed_polls"`     // 失败轮询次数
	IsRunning       bool          `json:"is_running"`       // 是否运行中
	Uptime          time.Duration `json:"uptime"`           // 运行时间
	StartTime       time.Time     `json:"start_time"`       // 启动时间
}

// AdapterManagerInterface 适配器管理器接口
type AdapterManagerInterface interface {
	ExecuteCommand(ctx context.Context, controllerID, deviceID string, command api.Command) (*api.CommandResult, error)
}

// DeviceManagerInterface 设备管理器接口
type DeviceManagerInterface interface {
	GetDevice(deviceID string) (*models.Device, error)
}

// PollScheduler 轮询调度器
type PollScheduler struct {
	// 配置
	config *PollConfig
	logger *zap.Logger

	// 轮询策略
	strategy *PollStrategy

	// 设备管理
	devices    map[string]*DevicePollInfo
	devicesMux sync.RWMutex

	// 运行状态
	running   bool
	runningMu sync.RWMutex
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup

	// 定时器
	timer *time.Ticker

	// 统计信息
	stats      *PollStatistics
	statsMutex sync.RWMutex

	// 事件通道
	eventBus chan *PollEvent

	// 依赖注入
	adapterManager AdapterManagerInterface
	deviceManager  DeviceManagerInterface
}

// NewPollScheduler 创建轮询调度器
func NewPollScheduler(logger *zap.Logger) *PollScheduler {
	config := &PollConfig{
		DefaultInterval: DefaultPollInterval,
		FastInterval:    FastPollInterval,
		SlowInterval:    SlowPollInterval,
		MaxFailures:     MaxFailures,
	}

	return &PollScheduler{
		config:   config,
		logger:   logger,
		strategy: NewPollStrategy(config, logger),
		devices:  make(map[string]*DevicePollInfo),
		eventBus: make(chan *PollEvent, 100),
		stats: &PollStatistics{
			TotalDevices:    0,
			TotalPolls:      0,
			SuccessfulPolls: 0,
			FailedPolls:     0,
			IsRunning:       false,
		},
	}
}

// Start 启动轮询调度器
func (ps *PollScheduler) Start(ctx context.Context) error {
	ps.runningMu.Lock()
	defer ps.runningMu.Unlock()

	if ps.running {
		return nil // 已经运行中，幂等操作
	}

	ps.ctx, ps.cancel = context.WithCancel(ctx)
	ps.running = true

	// 更新统计信息
	ps.statsMutex.Lock()
	ps.stats.IsRunning = true
	ps.stats.StartTime = time.Now()
	ps.statsMutex.Unlock()

	// 启动轮询协程
	ps.wg.Add(1)
	go ps.pollLoop()

	ps.logger.Info("Poll scheduler started")
	return nil
}

// Stop 停止轮询调度器
func (ps *PollScheduler) Stop() error {
	ps.runningMu.Lock()
	defer ps.runningMu.Unlock()

	if !ps.running {
		return nil // 已经停止，幂等操作
	}

	// 定时器由pollLoop内部管理，这里不需要停止

	// 取消上下文
	if ps.cancel != nil {
		ps.cancel()
	}

	// 等待协程结束
	ps.wg.Wait()

	ps.running = false

	// 更新统计信息
	ps.statsMutex.Lock()
	ps.stats.IsRunning = false
	if !ps.stats.StartTime.IsZero() {
		ps.stats.Uptime = time.Since(ps.stats.StartTime)
	}
	ps.statsMutex.Unlock()

	ps.logger.Info("Poll scheduler stopped")
	return nil
}

// IsRunning 检查是否运行中
func (ps *PollScheduler) IsRunning() bool {
	ps.runningMu.RLock()
	defer ps.runningMu.RUnlock()
	return ps.running
}

// AddDevice 添加设备到轮询队列
func (ps *PollScheduler) AddDevice(device *models.Device) error {
	ps.devicesMux.Lock()
	defer ps.devicesMux.Unlock()

	// 创建设备轮询信息
	pollInfo := &DevicePollInfo{
		DeviceID:     device.ID,
		LastPoll:     time.Time{},            // 从未轮询
		NextPoll:     time.Now(),             // 立即轮询
		PollInterval: ps.config.FastInterval, // 新设备使用快速轮询
		FailureCount: 0,
		Status:       "pending",
		Priority:     PriorityHigh, // 新设备高优先级

		// 初始化重连信息
		ReconnectInfo: &ReconnectInfo{
			Enabled:    true,
			MaxBackoff: 5 * time.Minute,
		},
		ErrorHistory: make([]*ErrorInfo, 0),
	}

	// 使用策略计算下次轮询时间
	pollInfo.NextPoll = ps.strategy.CalculateNextPollTime(pollInfo)

	ps.devices[device.ID] = pollInfo

	// 更新统计信息
	ps.statsMutex.Lock()
	ps.stats.TotalDevices = len(ps.devices)
	ps.statsMutex.Unlock()

	ps.logger.Debug("Device added to poll scheduler",
		zap.String("device_id", device.ID),
		zap.String("device_name", device.Name),
		zap.String("priority", "high"),
		zap.Time("next_poll", pollInfo.NextPoll))

	return nil
}

// RemoveDevice 从轮询队列移除设备
func (ps *PollScheduler) RemoveDevice(deviceID string) error {
	ps.devicesMux.Lock()
	defer ps.devicesMux.Unlock()

	delete(ps.devices, deviceID)

	// 更新统计信息
	ps.statsMutex.Lock()
	ps.stats.TotalDevices = len(ps.devices)
	ps.statsMutex.Unlock()

	ps.logger.Debug("Device removed from poll scheduler",
		zap.String("device_id", deviceID))

	return nil
}

// GetDeviceCount 获取设备数量
func (ps *PollScheduler) GetDeviceCount() int {
	ps.devicesMux.RLock()
	defer ps.devicesMux.RUnlock()
	return len(ps.devices)
}

// GetDeviceInfo 获取设备轮询信息
func (ps *PollScheduler) GetDeviceInfo(deviceID string) *DevicePollInfo {
	ps.devicesMux.RLock()
	defer ps.devicesMux.RUnlock()

	if info, exists := ps.devices[deviceID]; exists {
		// 返回副本，避免外部修改
		infoCopy := *info
		return &infoCopy
	}
	return nil
}

// GetConfig 获取轮询配置
func (ps *PollScheduler) GetConfig() *PollConfig {
	// 返回配置副本
	configCopy := *ps.config
	return &configCopy
}

// UpdateConfig 更新轮询配置
func (ps *PollScheduler) UpdateConfig(config *PollConfig) error {
	if config == nil {
		return nil
	}

	// 验证配置
	if config.DefaultInterval <= 0 || config.FastInterval <= 0 || config.SlowInterval <= 0 {
		return fmt.Errorf("轮询间隔必须大于0")
	}

	// 更新配置
	ps.config = &PollConfig{
		DefaultInterval: config.DefaultInterval,
		FastInterval:    config.FastInterval,
		SlowInterval:    config.SlowInterval,
		MaxFailures:     config.MaxFailures,
	}

	// 同时更新策略配置
	ps.strategy.UpdateConfig(ps.config)

	ps.logger.Info("Poll scheduler config updated",
		zap.Duration("default_interval", config.DefaultInterval),
		zap.Duration("fast_interval", config.FastInterval))

	return nil
}

// GetStatistics 获取统计信息
func (ps *PollScheduler) GetStatistics() *PollStatistics {
	ps.statsMutex.RLock()
	defer ps.statsMutex.RUnlock()

	// 返回统计信息副本
	statsCopy := *ps.stats
	if ps.stats.IsRunning && !ps.stats.StartTime.IsZero() {
		statsCopy.Uptime = time.Since(ps.stats.StartTime)
	}
	return &statsCopy
}

// SetAdapterManager 设置适配器管理器
func (ps *PollScheduler) SetAdapterManager(manager AdapterManagerInterface) {
	ps.adapterManager = manager
}

// SetDeviceManager 设置设备管理器
func (ps *PollScheduler) SetDeviceManager(manager DeviceManagerInterface) {
	ps.deviceManager = manager
}

// pollLoop 轮询循环
func (ps *PollScheduler) pollLoop() {
	defer ps.wg.Done()

	ps.logger.Info("Starting poll loop")

	// 创建定时器
	timer := time.NewTicker(1 * time.Second)
	defer timer.Stop()

	for {
		select {
		case <-ps.ctx.Done():
			ps.logger.Info("Poll loop stopped by context")
			return
		case <-timer.C:
			ps.processPollTick()
		}
	}
}

// processPollTick 处理轮询时钟
func (ps *PollScheduler) processPollTick() {
	ps.devicesMux.RLock()
	var devicesToPoll []*DevicePollInfo
	for _, info := range ps.devices {
		// 使用策略判断是否应该轮询
		if ps.strategy.ShouldPoll(info) {
			// 创建副本用于轮询
			infoCopy := *info
			devicesToPoll = append(devicesToPoll, &infoCopy)
		}
	}
	ps.devicesMux.RUnlock()

	// 轮询需要轮询的设备
	for _, info := range devicesToPoll {
		ps.pollDevice(info)
	}
}

// pollDevice 轮询单个设备（集成真实协议和错误处理）
func (ps *PollScheduler) pollDevice(info *DevicePollInfo) {
	startTime := time.Now()
	ps.logger.Debug("开始轮询设备",
		zap.String("设备ID", info.DeviceID),
		zap.String("轮询状态", info.Status),
		zap.Int("失败次数", info.FailureCount))

	// 更新统计信息
	ps.statsMutex.Lock()
	ps.stats.TotalPolls++
	ps.statsMutex.Unlock()

	// 尝试真实协议轮询
	result := ps.performDevicePoll(info.DeviceID)

	// 处理轮询结果和错误
	ps.handlePollResult(info.DeviceID, result)

	ps.logger.Info("设备轮询完成",
		zap.String("设备ID", info.DeviceID),
		zap.Bool("成功", result.Success),
		zap.Duration("响应时间", result.ResponseTime),
		zap.String("错误", result.Error),
		zap.Duration("总耗时", time.Since(startTime)),
		zap.Int("失败次数", info.FailureCount))
}

// handlePollResult 处理轮询结果
func (ps *PollScheduler) handlePollResult(deviceID string, result *PollResult) {
	ps.devicesMux.Lock()
	defer ps.devicesMux.Unlock()

	deviceInfo, exists := ps.devices[deviceID]
	if !exists {
		ps.logger.Warn("Device not found when handling poll result",
			zap.String("device_id", deviceID))
		return
	}

	deviceInfo.LastPoll = time.Now()

	if result.Success {
		ps.handleSuccessfulPoll(deviceInfo, result)
	} else {
		ps.handleFailedPoll(deviceInfo, result)
	}

	// 使用策略更新优先级和下次轮询时间
	ps.strategy.UpdatePriority(deviceInfo, result)
}

// handleSuccessfulPoll 处理成功的轮询
func (ps *PollScheduler) handleSuccessfulPoll(deviceInfo *DevicePollInfo, result *PollResult) {
	// 重置失败计数
	deviceInfo.FailureCount = 0
	deviceInfo.Status = "success"

	// 如果之前有重连信息，标记重连成功
	if deviceInfo.ReconnectInfo != nil && deviceInfo.ReconnectInfo.AttemptCount > 0 {
		ps.strategy.UpdateReconnectInfo(deviceInfo.ReconnectInfo, true)
	}

	// 清除错误信息
	deviceInfo.LastError = nil

	// 更新成功统计
	ps.statsMutex.Lock()
	ps.stats.SuccessfulPolls++
	ps.statsMutex.Unlock()

	// 发送轮询成功事件到事件总线
	ps.publishPollEvent(&PollEvent{
		DeviceID:  deviceInfo.DeviceID,
		EventType: "poll_success",
		Data: map[string]interface{}{
			"response_time":         result.ResponseTime,
			"poll_data":             result.Data,
			"failure_count":         deviceInfo.FailureCount,
			"consecutive_successes": ps.getConsecutiveSuccesses(deviceInfo),
		},
		Timestamp: time.Now(),
	})

	ps.logger.Debug("Device poll succeeded",
		zap.String("device_id", deviceInfo.DeviceID),
		zap.Duration("response_time", result.ResponseTime))
}

// handleFailedPoll 处理失败的轮询
func (ps *PollScheduler) handleFailedPoll(deviceInfo *DevicePollInfo, result *PollResult) {
	deviceInfo.FailureCount++
	deviceInfo.Status = "failed"

	// 分类错误
	errorInfo := ps.strategy.ClassifyError(result.Error)
	deviceInfo.LastError = errorInfo

	// 添加到错误历史（最多保留10个）
	deviceInfo.ErrorHistory = append(deviceInfo.ErrorHistory, errorInfo)
	if len(deviceInfo.ErrorHistory) > 10 {
		deviceInfo.ErrorHistory = deviceInfo.ErrorHistory[1:]
	}

	// 检查是否需要重连
	if ps.shouldTriggerReconnect(deviceInfo) {
		ps.scheduleReconnect(deviceInfo)
	}

	// 更新失败统计
	ps.statsMutex.Lock()
	ps.stats.FailedPolls++
	ps.statsMutex.Unlock()

	// 发送轮询失败事件到事件总线
	ps.publishPollEvent(&PollEvent{
		DeviceID:  deviceInfo.DeviceID,
		EventType: "poll_failure",
		Data: map[string]interface{}{
			"error":            result.Error,
			"error_type":       errorInfo.ErrorType,
			"error_code":       errorInfo.ErrorCode,
			"failure_count":    deviceInfo.FailureCount,
			"response_time":    result.ResponseTime,
			"should_reconnect": ps.shouldTriggerReconnect(deviceInfo),
		},
		Timestamp: time.Now(),
	})

	ps.logger.Warn("Device poll failed",
		zap.String("device_id", deviceInfo.DeviceID),
		zap.String("error", result.Error),
		zap.String("error_type", errorInfo.ErrorType),
		zap.String("error_code", errorInfo.ErrorCode),
		zap.Int("failure_count", deviceInfo.FailureCount))
}

// shouldTriggerReconnect 判断是否应该触发重连
func (ps *PollScheduler) shouldTriggerReconnect(deviceInfo *DevicePollInfo) bool {
	if deviceInfo.ReconnectInfo == nil {
		return false
	}

	// 检查错误类型是否需要重连
	if deviceInfo.LastError != nil {
		switch deviceInfo.LastError.ErrorType {
		case "connection", "device":
			return ps.strategy.ShouldAttemptReconnect(deviceInfo, deviceInfo.ReconnectInfo)
		default:
			return false
		}
	}

	return false
}

// scheduleReconnect 安排重连
func (ps *PollScheduler) scheduleReconnect(deviceInfo *DevicePollInfo) {
	if deviceInfo.ReconnectInfo == nil {
		deviceInfo.ReconnectInfo = &ReconnectInfo{
			Enabled:    true,
			MaxBackoff: 5 * time.Minute,
		}
	}

	// 计算重连间隔
	backoffDelay := ps.strategy.GetReconnectInterval(deviceInfo.ReconnectInfo)
	if backoffDelay == 0 {
		return // 重连被禁用
	}

	deviceInfo.ReconnectInfo.NextAttempt = time.Now().Add(backoffDelay)
	deviceInfo.ReconnectInfo.BackoffDelay = backoffDelay

	ps.logger.Info("Scheduled device reconnect",
		zap.String("device_id", deviceInfo.DeviceID),
		zap.Duration("backoff_delay", backoffDelay),
		zap.Time("next_attempt", deviceInfo.ReconnectInfo.NextAttempt))
}

// performReconnect 执行重连
func (ps *PollScheduler) performReconnect(deviceID string) bool {
	ps.logger.Info("Attempting device reconnect",
		zap.String("device_id", deviceID))

	// 尝试重新轮询设备
	result := ps.performDevicePoll(deviceID)

	ps.devicesMux.Lock()
	defer ps.devicesMux.Unlock()

	deviceInfo, exists := ps.devices[deviceID]
	if !exists {
		return false
	}

	// 更新重连信息
	if deviceInfo.ReconnectInfo != nil {
		ps.strategy.UpdateReconnectInfo(deviceInfo.ReconnectInfo, result.Success)
	}

	return result.Success
}

// performDevicePoll 执行真实设备轮询
func (ps *PollScheduler) performDevicePoll(deviceID string) *PollResult {
	startTime := time.Now()

	// 1. 检查是否有适配器管理器
	if ps.adapterManager == nil {
		ps.logger.Warn("适配器管理器不可用，使用降级轮询",
			zap.String("设备ID", deviceID))
		return ps.fallbackPoll(deviceID)
	}

	ps.logger.Debug("开始执行设备轮询",
		zap.String("设备ID", deviceID),
		zap.String("适配器管理器", "可用"))

	// 2. 获取设备信息
	device, err := ps.getDeviceInfo(deviceID)
	if err != nil {
		ps.logger.Error("获取设备信息失败",
			zap.String("设备ID", deviceID),
			zap.Error(err))
		// 设备信息获取失败也要继续轮询，只是标记为失败
		return &PollResult{
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Sprintf("设备信息错误: %v", err),
			Data:         nil,
		}
	}

	ps.logger.Debug("设备信息获取成功",
		zap.String("设备ID", deviceID),
		zap.String("设备名称", device.Name),
		zap.String("设备类型", string(device.Type)),
		zap.String("控制器ID", device.ControllerID),
		zap.Int("设备地址", device.DeviceAddress))

	// 3. 创建设备状态查询命令
	statusCommand := ps.createStatusCommand(device)

	ps.logger.Debug("创建轮询命令",
		zap.String("设备ID", deviceID),
		zap.String("命令类型", statusCommand.Type),
		zap.Any("命令参数", statusCommand.Parameters))

	// 4. 执行命令 - 添加重试逻辑
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second) // 增加超时时间
	defer cancel()

	ps.logger.Debug("执行设备轮询命令",
		zap.String("设备ID", deviceID),
		zap.String("控制器ID", device.ControllerID),
		zap.Duration("超时时间", 10*time.Second))

	// 尝试执行命令，失败时继续轮询而不是停止
	commandResult, err := ps.adapterManager.ExecuteCommand(ctx, device.ControllerID, deviceID, statusCommand)
	responseTime := time.Since(startTime)

	if err != nil {
		ps.logger.Warn("设备轮询命令执行失败，将在下次轮询重试",
			zap.String("设备ID", deviceID),
			zap.String("控制器ID", device.ControllerID),
			zap.Duration("响应时间", responseTime),
			zap.Error(err))

		// 返回失败结果，但继续轮询调度
		return &PollResult{
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("命令执行错误: %v", err),
			Data: map[string]interface{}{
				"retry_scheduled": true,
				"next_retry":      time.Now().Add(ps.config.DefaultInterval),
			},
		}
	}

	ps.logger.Debug("设备轮询命令执行成功",
		zap.String("设备ID", deviceID),
		zap.String("控制器ID", device.ControllerID),
		zap.Duration("响应时间", responseTime),
		zap.Bool("命令成功", commandResult.Success))

	// 5. 处理命令结果
	return ps.processCommandResult(commandResult, responseTime, deviceID)
}

// getDeviceInfo 获取设备详细信息
func (ps *PollScheduler) getDeviceInfo(deviceID string) (*models.Device, error) {
	if ps.deviceManager == nil {
		return nil, fmt.Errorf("device manager not available")
	}

	device, err := ps.deviceManager.GetDevice(deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device %s: %w", deviceID, err)
	}

	if device == nil {
		return nil, fmt.Errorf("device %s not found", deviceID)
	}

	return device, nil
}

// createStatusCommand 创建设备状态查询命令
func (ps *PollScheduler) createStatusCommand(device *models.Device) api.Command {
	// 根据设备类型创建相应的状态查询命令
	switch device.Type {
	case models.DeviceTypeFuelPump, models.DeviceTypeDARTPump, models.DeviceTypePump:
		return ps.createPumpPollCommand(device)
	case models.DeviceTypeController, models.DeviceTypeFuelController:
		return ps.createControllerPollCommand(device)
	default:
		// 对于未知类型，也使用泵轮询命令（因为大部分设备是泵）
		ps.logger.Debug("设备类型未识别，使用泵轮询命令",
			zap.String("设备ID", device.ID),
			zap.String("设备类型", string(device.Type)))
		return ps.createPumpPollCommand(device)
	}
}

// createPumpPollCommand 创建泵设备轮询命令（DART协议）
func (ps *PollScheduler) createPumpPollCommand(device *models.Device) api.Command {
	return api.Command{
		Type: "pump_status_poll",
		Parameters: map[string]interface{}{
			"device_id":      device.ID,
			"device_address": device.DeviceAddress,
			"controller_id":  device.ControllerID,
			"poll_type":      "enhanced_status",
			// DART协议特定参数
			"dart_commands": []string{
				"get_basic_status",    // 获取基础状态
				"get_current_totals",  // 获取当前累计值
				"get_system_status",   // 获取系统状态
				"detect_state_change", // 新增：检测状态变化
			},
			"timeout_ms":            3000, // 降低超时时间
			"max_retries":           2,    // 减少重试次数
			"response_format":       "dart_frame",
			"enable_state_analysis": true, // 启用状态分析
		},
		Timeout: 3 * time.Second, // 缩短超时时间提高响应
	}
}

// createControllerPollCommand 创建控制器轮询命令
func (ps *PollScheduler) createControllerPollCommand(device *models.Device) api.Command {
	return api.Command{
		Type: "controller_status_poll",
		Parameters: map[string]interface{}{
			"controller_id": device.ID,
			"poll_type":     "system_status",
			"query_items": []string{
				"system_health",
				"connected_devices",
				"communication_status",
				"error_status",
			},
			"timeout_ms":  3000,
			"max_retries": 2,
		},
		Timeout: 3 * time.Second,
	}
}

// createGenericPollCommand 创建通用设备轮询命令
func (ps *PollScheduler) createGenericPollCommand(device *models.Device) api.Command {
	return api.Command{
		Type: "generic_status_poll",
		Parameters: map[string]interface{}{
			"device_id":   device.ID,
			"device_type": device.Type,
			"poll_type":   "basic_status",
			"timeout_ms":  4000,
			"max_retries": 2,
		},
		Timeout: 4 * time.Second,
	}
}

// processCommandResult 处理命令执行结果
func (ps *PollScheduler) processCommandResult(commandResult *api.CommandResult, responseTime time.Duration, deviceID string) *PollResult {
	if commandResult == nil {
		return &PollResult{
			Success:      false,
			ResponseTime: responseTime,
			Error:        "nil command result",
			Data:         nil,
		}
	}

	// 检查命令是否成功
	if !commandResult.Success {
		ps.logger.Warn("Device poll command returned failure",
			zap.String("device_id", deviceID),
			zap.String("error", commandResult.Error),
			zap.Duration("response_time", responseTime))

		return &PollResult{
			Success:      false,
			ResponseTime: responseTime,
			Error:        commandResult.Error,
			Data:         commandResult.Data,
		}
	}

	// 验证响应数据
	if commandResult.Data == nil {
		ps.logger.Warn("Device poll returned success but no data",
			zap.String("device_id", deviceID),
			zap.Duration("response_time", responseTime))

		return &PollResult{
			Success:      true,
			ResponseTime: responseTime,
			Error:        "",
			Data:         map[string]interface{}{"status": "no_data"},
		}
	}

	// 处理DART协议特定响应
	processedData := ps.processDartResponse(commandResult.Data, deviceID)

	ps.logger.Debug("Device poll successful",
		zap.String("device_id", deviceID),
		zap.Duration("response_time", responseTime),
		zap.Any("processed_data", processedData))

	return &PollResult{
		Success:      true,
		ResponseTime: responseTime,
		Error:        "",
		Data:         processedData,
	}
}

// processDartResponse 处理DART协议响应数据
func (ps *PollScheduler) processDartResponse(data map[string]interface{}, deviceID string) map[string]interface{} {
	processedData := make(map[string]interface{})

	// 复制原始数据
	for k, v := range data {
		processedData[k] = v
	}

	// 添加处理时间戳
	processedData["processed_at"] = time.Now()
	processedData["device_id"] = deviceID

	// 检查是否有DART帧数据
	if dartFrames, exists := data["dart_frames"]; exists {
		processedFrames := ps.processDartFrames(dartFrames, deviceID)
		processedData["dart_status"] = processedFrames
	}

	// 检查是否有设备状态数据
	if status, exists := data["device_status"]; exists {
		processedData["status"] = ps.processDeviceStatus(status, deviceID)
	}

	// 检查是否有累计值数据
	if totals, exists := data["totals"]; exists {
		processedData["totals"] = ps.processTotalsData(totals, deviceID)
	}

	// 检查是否有错误信息
	if errors, exists := data["errors"]; exists {
		processedData["device_errors"] = ps.processErrorData(errors, deviceID)
	}

	return processedData
}

// processDartFrames 处理DART帧数据
func (ps *PollScheduler) processDartFrames(frames interface{}, deviceID string) map[string]interface{} {
	frameData := map[string]interface{}{
		"frame_count":  0,
		"valid_frames": 0,
		"errors":       []string{},
	}

	frameList, ok := frames.([]interface{})
	if !ok {
		frameData["errors"] = append(frameData["errors"].([]string), "invalid frame format")
		return frameData
	}

	frameData["frame_count"] = len(frameList)
	validCount := 0

	for i, frame := range frameList {
		if frameMap, ok := frame.(map[string]interface{}); ok {
			if valid, exists := frameMap["valid"]; exists && valid == true {
				validCount++
			}
		} else {
			frameData["errors"] = append(frameData["errors"].([]string),
				fmt.Sprintf("frame %d: invalid format", i))
		}
	}

	frameData["valid_frames"] = validCount
	frameData["success_rate"] = float64(validCount) / float64(len(frameList))

	return frameData
}

// processDeviceStatus 处理设备状态数据
func (ps *PollScheduler) processDeviceStatus(status interface{}, deviceID string) map[string]interface{} {
	statusData := map[string]interface{}{
		"online":      false,
		"health":      "unknown",
		"last_update": time.Now(),
	}

	statusMap, ok := status.(map[string]interface{})
	if !ok {
		statusData["error"] = "invalid status format"
		return statusData
	}

	// 提取在线状态
	if online, exists := statusMap["online"]; exists {
		statusData["online"] = online
	}

	// 提取健康状态
	if health, exists := statusMap["health"]; exists {
		statusData["health"] = health
	}

	// 提取其他状态字段
	for key, value := range statusMap {
		if key != "online" && key != "health" {
			statusData[key] = value
		}
	}

	return statusData
}

// processTotalsData 处理累计值数据
func (ps *PollScheduler) processTotalsData(totals interface{}, deviceID string) map[string]interface{} {
	totalsData := map[string]interface{}{
		"valid":     false,
		"timestamp": time.Now(),
	}

	totalsMap, ok := totals.(map[string]interface{})
	if !ok {
		totalsData["error"] = "invalid totals format"
		return totalsData
	}

	totalsData["valid"] = true

	// 复制累计值数据
	for key, value := range totalsMap {
		totalsData[key] = value
	}

	return totalsData
}

// processErrorData 处理错误数据
func (ps *PollScheduler) processErrorData(errors interface{}, deviceID string) []map[string]interface{} {
	var processedErrors []map[string]interface{}

	errorList, ok := errors.([]interface{})
	if !ok {
		return []map[string]interface{}{
			{
				"error":     "invalid error format",
				"timestamp": time.Now(),
			},
		}
	}

	for _, err := range errorList {
		if errMap, ok := err.(map[string]interface{}); ok {
			processedError := make(map[string]interface{})
			for k, v := range errMap {
				processedError[k] = v
			}
			processedError["processed_at"] = time.Now()
			processedErrors = append(processedErrors, processedError)
		}
	}

	return processedErrors
}

// fallbackPoll 降级轮询（当没有适配器时使用）
func (ps *PollScheduler) fallbackPoll(deviceID string) *PollResult {
	// 模拟轮询延迟
	time.Sleep(10 * time.Millisecond)

	// 90%的成功率模拟
	success := time.Now().UnixNano()%10 < 9

	if success {
		return &PollResult{
			Success:      true,
			ResponseTime: 15 * time.Millisecond,
			Error:        "",
			Data: map[string]interface{}{
				"status":   "online",
				"fallback": true,
			},
		}
	} else {
		return &PollResult{
			Success:      false,
			ResponseTime: 25 * time.Millisecond,
			Error:        "simulated connection timeout",
			Data:         nil,
		}
	}
}

// GetStrategy 获取轮询策略
func (ps *PollScheduler) GetStrategy() *PollStrategy {
	return ps.strategy
}

// GetEventBus 获取事件总线通道
func (ps *PollScheduler) GetEventBus() <-chan *PollEvent {
	return ps.eventBus
}

// GetDevicesByPriority 获取指定优先级的设备列表
func (ps *PollScheduler) GetDevicesByPriority(priority PollPriority) []*DevicePollInfo {
	ps.devicesMux.RLock()
	defer ps.devicesMux.RUnlock()

	var devices []*DevicePollInfo
	for _, device := range ps.devices {
		if device.Priority == priority {
			// 返回副本，避免外部修改
			deviceCopy := *device
			devices = append(devices, &deviceCopy)
		}
	}
	return devices
}

// UpdateDevicePriority 手动更新设备优先级
func (ps *PollScheduler) UpdateDevicePriority(deviceID string, priority PollPriority) error {
	ps.devicesMux.Lock()
	defer ps.devicesMux.Unlock()

	device, exists := ps.devices[deviceID]
	if !exists {
		return ErrDeviceNotFound
	}

	oldPriority := device.Priority
	device.Priority = priority

	// 重新计算轮询间隔
	interval := ps.strategy.GetPollInterval(device)
	device.PollInterval = interval
	device.NextPoll = time.Now().Add(interval)

	ps.logger.Info("Device priority updated",
		zap.String("device_id", deviceID),
		zap.String("old_priority", ps.priorityToString(oldPriority)),
		zap.String("new_priority", ps.priorityToString(priority)),
		zap.Duration("new_interval", interval))

	return nil
}

// GetPriorityStatistics 获取优先级统计信息
func (ps *PollScheduler) GetPriorityStatistics() map[string]int {
	ps.devicesMux.RLock()
	defer ps.devicesMux.RUnlock()

	stats := map[string]int{
		"high":   0,
		"normal": 0,
		"low":    0,
	}

	for _, device := range ps.devices {
		switch device.Priority {
		case PriorityHigh:
			stats["high"]++
		case PriorityNormal:
			stats["normal"]++
		case PriorityLow:
			stats["low"]++
		}
	}

	return stats
}

// priorityToString 优先级转字符串（内部辅助方法）
func (ps *PollScheduler) priorityToString(priority PollPriority) string {
	switch priority {
	case PriorityHigh:
		return "high"
	case PriorityNormal:
		return "normal"
	case PriorityLow:
		return "low"
	default:
		return "unknown"
	}
}

// publishPollEvent 发布轮询事件到事件总线
func (ps *PollScheduler) publishPollEvent(event *PollEvent) {
	select {
	case ps.eventBus <- event:
	default:
		// 如果事件通道已满，丢弃事件
	}
}

// getConsecutiveSuccesses 获取连续成功次数
func (ps *PollScheduler) getConsecutiveSuccesses(deviceInfo *DevicePollInfo) int {
	successCount := 0
	for _, errorInfo := range deviceInfo.ErrorHistory {
		if errorInfo.ErrorType == "success" {
			successCount++
		} else {
			break
		}
	}
	return successCount
}
