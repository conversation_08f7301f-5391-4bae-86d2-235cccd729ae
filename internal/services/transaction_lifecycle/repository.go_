package transaction_lifecycle

import (
	"context"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"fcc-service/pkg/models"
)

// gormRepository GORM数据库实现
type gormRepository struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewGormRepository 创建GORM仓储实现
func NewGormRepository(db *gorm.DB, logger *zap.Logger) Repository {
	return &gormRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建交易记录
func (r *gormRepository) Create(ctx context.Context, tx *models.Transaction) error {
	if err := r.db.WithContext(ctx).Create(tx).Error; err != nil {
		return fmt.Errorf("failed to create transaction: %w", err)
	}
	return nil
}

// GetByID 根据ID获取交易
func (r *gormRepository) GetByID(ctx context.Context, id string) (*models.Transaction, error) {
	var tx models.Transaction
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&tx).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("transaction not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}
	
	return &tx, nil
}

// Update 更新交易记录
func (r *gormRepository) Update(ctx context.Context, tx *models.Transaction) error {
	result := r.db.WithContext(ctx).
		Where("id = ?", tx.ID).
		Updates(tx)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update transaction: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("transaction not found for update: %s", tx.ID)
	}
	
	return nil
}

// Delete 删除交易记录
func (r *gormRepository) Delete(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).
		Where("id = ?", id).
		Delete(&models.Transaction{})
	
	if result.Error != nil {
		return fmt.Errorf("failed to delete transaction: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("transaction not found for deletion: %s", id)
	}
	
	return nil
}

// GetActiveByDeviceAndNozzle 获取设备喷嘴的活跃交易
func (r *gormRepository) GetActiveByDeviceAndNozzle(ctx context.Context, deviceID string, nozzleID byte) (*models.Transaction, error) {
	var tx models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND nozzle_id = ? AND status IN (?)", 
			deviceID, fmt.Sprintf("%d", nozzleID), 
			[]string{string(models.TransactionStatusInitiated), string(models.TransactionStatusFilling)}).
		Order("created_at DESC").
		First(&tx).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有活跃交易是正常的
		}
		return nil, fmt.Errorf("failed to get active transaction: %w", err)
	}
	
	return &tx, nil
}

// GetActiveByDevice 获取设备所有活跃交易
func (r *gormRepository) GetActiveByDevice(ctx context.Context, deviceID string) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND status IN (?)", 
			deviceID, 
			[]string{string(models.TransactionStatusInitiated), string(models.TransactionStatusFilling)}).
		Order("created_at DESC").
		Find(&transactions).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get active transactions: %w", err)
	}
	
	return transactions, nil
}

// GetByDeviceAndTimeRange 获取设备指定时间范围内的交易
func (r *gormRepository) GetByDeviceAndTimeRange(ctx context.Context, deviceID string, start, end time.Time) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND created_at BETWEEN ? AND ?", deviceID, start, end).
		Order("created_at DESC").
		Find(&transactions).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions by time range: %w", err)
	}
	
	return transactions, nil
}

// GetStaleTransactions 获取过期交易
func (r *gormRepository) GetStaleTransactions(ctx context.Context, maxAge time.Duration) ([]*models.Transaction, error) {
	cutoffTime := time.Now().Add(-maxAge)
	
	var staleTransactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("status IN (?) AND created_at < ?", 
			[]string{string(models.TransactionStatusInitiated), string(models.TransactionStatusFilling)},
			cutoffTime).
		Find(&staleTransactions).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get stale transactions: %w", err)
	}
	
	return staleTransactions, nil
}

// GetStatistics 获取设备交易统计
func (r *gormRepository) GetStatistics(ctx context.Context, deviceID string) (*TransactionStatistics, error) {
	stats := &TransactionStatistics{
		DeviceID: deviceID,
	}
	
	// 获取活跃交易数
	var activeCount int64
	err := r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Where("device_id = ? AND status IN (?)", 
			deviceID, 
			[]string{string(models.TransactionStatusInitiated), string(models.TransactionStatusFilling)}).
		Count(&activeCount).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get active transaction count: %w", err)
	}
	stats.ActiveTransactions = int(activeCount)
	
	// 获取今日统计
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)
	
	// 今日完成交易数
	var completedToday int64
	err = r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Where("device_id = ? AND status = ? AND completed_at BETWEEN ? AND ?", 
			deviceID, string(models.TransactionStatusCompleted), today, tomorrow).
		Count(&completedToday).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get completed transaction count: %w", err)
	}
	stats.CompletedToday = int(completedToday)
	
	// 今日体积和金额汇总
	var todayStats struct {
		TotalVolume decimal.Decimal `gorm:"column:total_volume"`
		TotalAmount decimal.Decimal `gorm:"column:total_amount"`
	}
	
	err = r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Select("COALESCE(SUM(actual_volume), 0) as total_volume, COALESCE(SUM(actual_amount), 0) as total_amount").
		Where("device_id = ? AND status = ? AND completed_at BETWEEN ? AND ?", 
			deviceID, string(models.TransactionStatusCompleted), today, tomorrow).
		Scan(&todayStats).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get today's volume/amount stats: %w", err)
	}
	
	stats.TotalVolumeToday = todayStats.TotalVolume
	stats.TotalAmountToday = todayStats.TotalAmount
	
	// 平均加油时间
	var avgDuration struct {
		AvgSeconds float64 `gorm:"column:avg_seconds"`
	}
	
	err = r.db.WithContext(ctx).
		Model(&models.Transaction{}).
		Select("AVG(TIMESTAMPDIFF(SECOND, filling_at, completed_at)) as avg_seconds").
		Where("device_id = ? AND status = ? AND filling_at IS NOT NULL AND completed_at IS NOT NULL AND completed_at BETWEEN ? AND ?", 
			deviceID, string(models.TransactionStatusCompleted), today, tomorrow).
		Scan(&avgDuration).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get average filling time: %w", err)
	}
	
	if avgDuration.AvgSeconds > 0 {
		stats.AverageFillingTime = time.Duration(avgDuration.AvgSeconds) * time.Second
	}
	
	// 最后交易时间
	var lastTransaction models.Transaction
	err = r.db.WithContext(ctx).
		Where("device_id = ?", deviceID).
		Order("created_at DESC").
		First(&lastTransaction).Error
	
	if err == nil {
		stats.LastTransactionTime = &lastTransaction.CreatedAt
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get last transaction time: %w", err)
	}
	
	return stats, nil
} 