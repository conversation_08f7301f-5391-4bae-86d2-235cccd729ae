package command

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"go.uber.org/zap"

	"fcc-service/internal/services/adapter"
	"fcc-service/internal/storage"
	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// Executor 命令执行器实现
type Executor struct {
	// 依赖注入
	database   storage.Database
	cache      storage.Cache
	deviceMgr  api.DeviceManager
	adapterMgr *adapter.Manager
	logger     *zap.Logger

	// 内存状态管理
	mu         sync.RWMutex
	commands   map[string]*CommandExecution // 活跃命令映射
	queues     map[string]*DeviceQueue      // 设备队列映射
	resultChan chan *CommandResult          // 结果通道

	// 配置参数
	maxQueueSize    int
	defaultTimeout  time.Duration
	cleanupInterval time.Duration
	maxRetries      int

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
	done   chan struct{}
}

// CommandExecution 命令执行状态
type CommandExecution struct {
	// 基本信息
	CommandID   string                 `json:"command_id"`
	DeviceID    string                 `json:"device_id"`
	CommandType string                 `json:"command_type"`
	Parameters  map[string]interface{} `json:"parameters"`

	// 执行控制
	Priority   api.CommandPriority `json:"priority"`
	Timeout    time.Duration       `json:"timeout"`
	MaxRetries int                 `json:"max_retries"`
	RetryCount int                 `json:"retry_count"`

	// 状态信息
	Status   models.CommandStatus `json:"status"`
	Progress int                  `json:"progress"` // 0-100
	ErrorMsg string               `json:"error_msg,omitempty"`

	// 时间戳
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at,omitempty"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`

	// 执行控制
	ProgressChan chan int           `json:"-"`
	CancelFunc   context.CancelFunc `json:"-"`

	// 元数据
	Metadata map[string]string `json:"metadata,omitempty"`
}

// DeviceQueue 设备命令队列
type DeviceQueue struct {
	DeviceID        string              `json:"device_id"`
	PendingCommands []*CommandExecution `json:"pending_commands"`
	MaxSize         int                 `json:"max_size"`
	TotalProcessed  int64               `json:"total_processed"`
	LastProcessed   time.Time           `json:"last_processed"`
}

// CommandResult 命令执行结果
type CommandResult struct {
	CommandID     string                 `json:"command_id"`
	DeviceID      string                 `json:"device_id"`
	Success       bool                   `json:"success"`
	Data          map[string]interface{} `json:"data,omitempty"`
	ErrorMsg      string                 `json:"error_msg,omitempty"`
	ExecutionTime time.Duration          `json:"execution_time"`
	CompletedAt   time.Time              `json:"completed_at"`
}

// NewExecutor 创建命令执行器实例
func NewExecutor(
	database storage.Database,
	cache storage.Cache,
	deviceMgr api.DeviceManager,
	adapterMgr *adapter.Manager,
	logger *zap.Logger,
) *Executor {
	ctx, cancel := context.WithCancel(context.Background())

	executor := &Executor{
		database:   database,
		cache:      cache,
		deviceMgr:  deviceMgr,
		adapterMgr: adapterMgr,
		logger:     logger,

		commands:   make(map[string]*CommandExecution),
		queues:     make(map[string]*DeviceQueue),
		resultChan: make(chan *CommandResult, 100),

		maxQueueSize:    100,
		defaultTimeout:  30 * time.Second,
		cleanupInterval: 5 * time.Minute,
		maxRetries:      3,

		ctx:    ctx,
		cancel: cancel,
		done:   make(chan struct{}),
	}

	logger.Info("Command executor initialized")
	return executor
}

// ExecuteCommand 同步执行命令
func (e *Executor) ExecuteCommand(ctx context.Context, req *api.CommandRequest) (*api.CommandResponse, error) {
	e.logger.Info("Executing command synchronously",
		zap.String("command_id", req.CommandID),
		zap.String("device_id", req.DeviceID),
		zap.String("command_type", req.CommandType))

	// 验证请求
	if err := e.validateCommandRequest(req); err != nil {
		return nil, err
	}

	// 验证设备存在（如果设备管理器可用）
	if e.deviceMgr != nil {
		device, err := e.deviceMgr.GetDevice(ctx, req.DeviceID)
		if err != nil {
			return nil, errors.NewValidationError("device not found", req.DeviceID)
		}

		// 检查设备状态
		if device.Status != models.DeviceStatusOnline {
			return nil, errors.NewValidationError("device not online", string(device.Status))
		}
	}

	// 创建命令执行
	execution := e.createCommandExecution(req)

	// 直接执行命令（同步）
	startTime := time.Now()
	result, err := e.executeCommandInternal(ctx, execution)
	if err != nil {
		e.logger.Error("Command execution failed",
			zap.String("command_id", req.CommandID),
			zap.Error(err))

		return &api.CommandResponse{
			CommandID:  req.CommandID,
			DeviceID:   req.DeviceID,
			Success:    false,
			Error:      err.Error(),
			ExecutedAt: time.Now(),
			Duration:   time.Since(startTime),
		}, nil
	}

	return &api.CommandResponse{
		CommandID:  req.CommandID,
		DeviceID:   req.DeviceID,
		Success:    result.Success,
		Result:     result.Data,
		Error:      result.ErrorMsg,
		ExecutedAt: time.Now(),
		Duration:   time.Since(startTime),
	}, nil
}

// ExecuteCommandAsync 异步执行命令
func (e *Executor) ExecuteCommandAsync(ctx context.Context, req *api.CommandRequest) (*api.AsyncCommandResponse, error) {
	e.logger.Info("Executing command asynchronously",
		zap.String("command_id", req.CommandID),
		zap.String("device_id", req.DeviceID))

	// 验证请求
	if err := e.validateCommandRequest(req); err != nil {
		return nil, err
	}

	// 验证设备存在（如果设备管理器可用）
	if e.deviceMgr != nil {
		_, err := e.deviceMgr.GetDevice(ctx, req.DeviceID)
		if err != nil {
			return nil, errors.NewValidationError("device not found", req.DeviceID)
		}
	}

	// 创建命令执行
	execution := e.createCommandExecution(req)

	// 添加到队列
	if err := e.enqueueCommand(execution); err != nil {
		return nil, err
	}

	return &api.AsyncCommandResponse{
		CommandID: req.CommandID,
		Status: api.CommandStatus{
			CommandID: req.CommandID,
			DeviceID:  req.DeviceID,
			State:     api.CommandStateQueued,
			Progress:  0,
			CreatedAt: execution.CreatedAt,
			UpdatedAt: execution.UpdatedAt,
		},
		QueuedAt: time.Now(),
	}, nil
}

// GetCommandStatus 获取命令状态
func (e *Executor) GetCommandStatus(ctx context.Context, commandID string) (*api.CommandStatus, error) {
	e.mu.RLock()
	execution, exists := e.commands[commandID]
	e.mu.RUnlock()

	if !exists {
		return nil, errors.NewNotFoundError("command not found", commandID)
	}

	return &api.CommandStatus{
		CommandID:   execution.CommandID,
		DeviceID:    execution.DeviceID,
		State:       e.convertCommandState(execution.Status),
		Progress:    execution.Progress,
		Error:       execution.ErrorMsg,
		CreatedAt:   execution.CreatedAt,
		UpdatedAt:   execution.UpdatedAt,
		CompletedAt: execution.CompletedAt,
	}, nil
}

// CancelCommand 取消命令
func (e *Executor) CancelCommand(ctx context.Context, commandID string) error {
	e.mu.Lock()
	execution, exists := e.commands[commandID]
	e.mu.Unlock()

	if !exists {
		return errors.NewNotFoundError("command not found", commandID)
	}

	// 检查命令是否可以取消
	if !e.canCancelCommand(execution) {
		return errors.NewValidationError("command cannot be cancelled", string(execution.Status))
	}

	// 取消命令
	if execution.CancelFunc != nil {
		execution.CancelFunc()
	}

	// 更新状态
	e.mu.Lock()
	execution.Status = models.CommandStatusCancelled
	execution.UpdatedAt = time.Now()
	if execution.CompletedAt == nil {
		now := time.Now()
		execution.CompletedAt = &now
	}
	e.mu.Unlock()

	e.logger.Info("Command cancelled", zap.String("command_id", commandID))
	return nil
}

// ExecuteBatchCommands 批量执行命令
func (e *Executor) ExecuteBatchCommands(ctx context.Context, requests []*api.CommandRequest) ([]*api.CommandResponse, error) {
	e.logger.Info("Executing batch commands", zap.Int("count", len(requests)))

	responses := make([]*api.CommandResponse, len(requests))
	resultChan := make(chan struct {
		index int
		resp  *api.CommandResponse
	}, len(requests))

	// 并发执行所有命令
	for i, req := range requests {
		go func(index int, request *api.CommandRequest) {
			resp, err := e.ExecuteCommand(ctx, request)
			if err != nil {
				resp = &api.CommandResponse{
					CommandID:  request.CommandID,
					DeviceID:   request.DeviceID,
					Success:    false,
					Error:      err.Error(),
					ExecutedAt: time.Now(),
				}
			}
			resultChan <- struct {
				index int
				resp  *api.CommandResponse
			}{index, resp}
		}(i, req)
	}

	// 收集结果
	for i := 0; i < len(requests); i++ {
		result := <-resultChan
		responses[result.index] = result.resp
	}

	return responses, nil
}

// validateCommandRequest 验证命令请求
func (e *Executor) validateCommandRequest(req *api.CommandRequest) error {
	if req.CommandID == "" {
		return errors.NewValidationError("command_id is required")
	}
	if req.DeviceID == "" {
		return errors.NewValidationError("device_id is required")
	}
	if req.CommandType == "" {
		return errors.NewValidationError("command_type is required")
	}
	return nil
}

// createCommandExecution 创建命令执行对象
func (e *Executor) createCommandExecution(req *api.CommandRequest) *CommandExecution {
	timeout := req.Timeout
	if timeout == 0 {
		timeout = e.defaultTimeout
	}

	priority := req.Priority
	if priority == "" {
		priority = api.CommandPriorityNormal
	}

	_, cancel := context.WithTimeout(e.ctx, timeout)

	return &CommandExecution{
		CommandID:    req.CommandID,
		DeviceID:     req.DeviceID,
		CommandType:  req.CommandType,
		Parameters:   req.Parameters,
		Priority:     priority,
		Timeout:      timeout,
		MaxRetries:   e.maxRetries,
		Status:       models.CommandStatusPending,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		ProgressChan: make(chan int, 10),
		CancelFunc:   cancel,
		Metadata:     req.Metadata,
	}
}

// enqueueCommand 添加命令到队列
func (e *Executor) enqueueCommand(execution *CommandExecution) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	// 获取或创建设备队列
	queue, exists := e.queues[execution.DeviceID]
	if !exists {
		queue = &DeviceQueue{
			DeviceID:        execution.DeviceID,
			PendingCommands: make([]*CommandExecution, 0),
			MaxSize:         e.maxQueueSize,
		}
		e.queues[execution.DeviceID] = queue
	}

	// 检查队列容量
	if !queue.HasCapacity() {
		return errors.NewInternalError("device queue is full", execution.DeviceID)
	}

	// 添加到队列并按优先级排序
	queue.PendingCommands = append(queue.PendingCommands, execution)
	queue.PendingCommands = e.sortCommandsByPriority(queue.PendingCommands)

	// 添加到活跃命令映射
	e.commands[execution.CommandID] = execution

	e.logger.Debug("Command enqueued",
		zap.String("command_id", execution.CommandID),
		zap.String("device_id", execution.DeviceID),
		zap.Int("queue_size", len(queue.PendingCommands)))

	return nil
}

// HasCapacity 检查队列是否有容量
func (q *DeviceQueue) HasCapacity() bool {
	return len(q.PendingCommands) < q.MaxSize
}

// sortCommandsByPriority 按优先级排序命令
func (e *Executor) sortCommandsByPriority(commands []*CommandExecution) []*CommandExecution {
	sorted := make([]*CommandExecution, len(commands))
	copy(sorted, commands)

	sort.Slice(sorted, func(i, j int) bool {
		return e.hasHigherPriority(sorted[i], sorted[j])
	})

	return sorted
}

// hasHigherPriority 比较命令优先级
func (e *Executor) hasHigherPriority(cmd1, cmd2 *CommandExecution) bool {
	priority1 := e.getPriorityValue(cmd1.Priority)
	priority2 := e.getPriorityValue(cmd2.Priority)

	if priority1 != priority2 {
		return priority1 > priority2
	}

	// 优先级相同时，按创建时间排序（先创建的优先）
	return cmd1.CreatedAt.Before(cmd2.CreatedAt)
}

// getPriorityValue 获取优先级数值
func (e *Executor) getPriorityValue(priority api.CommandPriority) int {
	switch priority {
	case api.CommandPriorityUrgent:
		return 10
	case api.CommandPriorityHigh:
		return 8
	case api.CommandPriorityNormal:
		return 5
	case api.CommandPriorityLow:
		return 1
	default:
		return 5
	}
}

// isCommandTimeout 检查命令是否超时
func (e *Executor) isCommandTimeout(execution *CommandExecution) bool {
	if execution.StartedAt == nil {
		return false
	}
	return time.Since(*execution.StartedAt) > execution.Timeout
}

// canCancelCommand 检查命令是否可以取消
func (e *Executor) canCancelCommand(execution *CommandExecution) bool {
	return execution.Status == models.CommandStatusPending ||
		execution.Status == models.CommandStatusSending ||
		execution.Status == models.CommandStatusExecuting
}

// convertCommandState 转换命令状态
func (e *Executor) convertCommandState(status models.CommandStatus) api.CommandState {
	switch status {
	case models.CommandStatusPending:
		return api.CommandStatePending
	case models.CommandStatusSending:
		return api.CommandStateQueued
	case models.CommandStatusExecuting:
		return api.CommandStateExecuting
	case models.CommandStatusCompleted:
		return api.CommandStateCompleted
	case models.CommandStatusFailed:
		return api.CommandStateFailed
	case models.CommandStatusCancelled:
		return api.CommandStateCancelled
	case models.CommandStatusTimeout:
		return api.CommandStateTimeout
	default:
		return api.CommandStatePending
	}
}

// executeCommandInternal 执行命令的内部逻辑
func (e *Executor) executeCommandInternal(ctx context.Context, execution *CommandExecution) (*CommandResult, error) {
	e.logger.Debug("Executing command internally",
		zap.String("command_id", execution.CommandID),
		zap.String("device_id", execution.DeviceID),
		zap.String("command_type", execution.CommandType))

	startTime := time.Now()

	// 如果设备管理器或适配器管理器为nil，模拟成功执行
	if e.deviceMgr == nil || e.adapterMgr == nil {
		e.logger.Debug("Simulating command execution (no device manager or adapter manager)",
			zap.String("command_id", execution.CommandID))

		return &CommandResult{
			CommandID:     execution.CommandID,
			DeviceID:      execution.DeviceID,
			Success:       true,
			Data:          map[string]interface{}{"simulated": true, "command_type": execution.CommandType},
			ExecutionTime: time.Since(startTime),
			CompletedAt:   time.Now(),
		}, nil
	}

	// 获取设备信息
	device, err := e.deviceMgr.GetDevice(ctx, execution.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %w", err)
	}

	// 获取控制器信息
	controller, err := e.deviceMgr.GetController(ctx, device.ControllerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get controller: %w", err)
	}

	// 构造命令对象
	command := api.Command{
		Type:       execution.CommandType,
		Parameters: execution.Parameters,
		Timeout:    execution.Timeout,
	}

	// 通过适配器管理器执行命令
	result, err := e.adapterMgr.ExecuteCommand(ctx, controller, execution.DeviceID, command)
	if err != nil {
		e.logger.Error("Command execution failed",
			zap.String("command_id", execution.CommandID),
			zap.String("device_id", execution.DeviceID),
			zap.Error(err))

		return &CommandResult{
			CommandID:     execution.CommandID,
			DeviceID:      execution.DeviceID,
			Success:       false,
			ErrorMsg:      err.Error(),
			ExecutionTime: time.Since(startTime),
			CompletedAt:   time.Now(),
		}, nil
	}

	// 构造成功结果
	commandResult := &CommandResult{
		CommandID:     execution.CommandID,
		DeviceID:      execution.DeviceID,
		Success:       result.Success,
		Data:          result.Data,
		ErrorMsg:      result.Error,
		ExecutionTime: time.Since(startTime),
		CompletedAt:   time.Now(),
	}

	e.logger.Debug("Command executed successfully",
		zap.String("command_id", execution.CommandID),
		zap.String("device_id", execution.DeviceID),
		zap.Duration("execution_time", commandResult.ExecutionTime))

	return commandResult, nil
}

// Close 关闭命令执行器
func (e *Executor) Close() error {
	e.logger.Info("Closing command executor")

	// 取消所有活跃命令
	e.mu.Lock()
	for _, execution := range e.commands {
		if execution.CancelFunc != nil {
			execution.CancelFunc()
		}
	}
	e.mu.Unlock()

	// 关闭生命周期
	e.cancel()
	close(e.resultChan)

	// 等待后台goroutine退出
	select {
	case <-e.done:
		e.logger.Info("Command executor closed")
	case <-time.After(5 * time.Second):
		e.logger.Warn("Command executor close timeout")
	}

	return nil
}
