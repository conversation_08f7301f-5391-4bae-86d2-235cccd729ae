package command

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	v2models "fcc-service/pkg/models/v2"
)

// DispatchTaskInterface 调度任务接口
type DispatchTaskInterface interface {
	GetDevice(deviceID string) (DevicePollerInterface, error)
}

// DevicePollerInterface 设备轮询器接口
type DevicePollerInterface interface {
	GetStateMachine() v2models.DeviceStateMachine
}

// StateValidator 状态验证器接口
type StateValidator interface {
	// ValidateCommand 验证命令是否可以在当前状态下执行
	ValidateCommand(ctx context.Context, deviceID string, command dto.WayneCommandType) (*CommandValidationResult, error)
	
	// GetProtocolStatus 基于当前状态获取协议状态
	GetProtocolStatus(ctx context.Context, deviceID string, command dto.WayneCommandType) (string, error)
}

// CommandValidationResult 命令验证结果
type CommandValidationResult struct {
	IsValid        bool                    `json:"is_valid"`
	ProtocolStatus string                  `json:"protocol_status"` // ACK, NACK
	Reason         string                  `json:"reason"`
	DeviceState    v2models.DeviceState    `json:"device_state"`
	PumpStatus     v2models.PumpStatus     `json:"pump_status"`
	NozzleStates   map[byte]*NozzleStatus  `json:"nozzle_states,omitempty"`
	Suggestions    []string                `json:"suggestions,omitempty"`
}

// NozzleStatus 喷嘴状态信息
type NozzleStatus struct {
	Number   byte                    `json:"number"`
	State    v2models.NozzleState    `json:"state"`
	IsOut    bool                    `json:"is_out"`
	IsActive bool                    `json:"is_active"`
}

// stateValidator 状态验证器实现
type stateValidator struct {
	dispatchTask DispatchTaskInterface
	logger       *zap.Logger
}

// NewStateValidator 创建状态验证器
func NewStateValidator(dispatchTask DispatchTaskInterface, logger *zap.Logger) StateValidator {
	return &stateValidator{
		dispatchTask: dispatchTask,
		logger:       logger,
	}
}

// ValidateCommand 验证命令是否可以在当前状态下执行
func (sv *stateValidator) ValidateCommand(ctx context.Context, deviceID string, command dto.WayneCommandType) (*CommandValidationResult, error) {
	// 获取设备轮询器
	devicePoller, err := sv.dispatchTask.GetDevice(deviceID)
	if err != nil {
		return &CommandValidationResult{
			IsValid:        false,
			ProtocolStatus: "NACK",
			Reason:         fmt.Sprintf("Device poller not available: %v", err),
		}, nil
	}

	// 获取设备状态机
	stateMachine := devicePoller.GetStateMachine()
	if stateMachine == nil {
		return &CommandValidationResult{
			IsValid:        false,
			ProtocolStatus: "NACK",
			Reason:         "Device state machine not available",
		}, nil
	}

	// 获取当前状态
	deviceStateData := stateMachine.GetStateData()
	if deviceStateData == nil {
		return &CommandValidationResult{
			IsValid:        false,
			ProtocolStatus: "NACK",
			Reason:         "Device state data not available",
		}, nil
	}

	// 根据命令类型和当前状态进行验证
	return sv.validateCommandByState(deviceStateData, command, devicePoller)
}

// validateCommandByState 根据设备状态验证命令
func (sv *stateValidator) validateCommandByState(
	deviceState *v2models.DeviceStateData,
	command dto.WayneCommandType,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	result := &CommandValidationResult{
		DeviceState: deviceState.State,
		PumpStatus:  deviceState.PumpStatus,
		Suggestions: make([]string, 0),
	}

	// 检查设备是否在线
	if !deviceState.IsOnline {
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Device is offline"
		result.Suggestions = append(result.Suggestions, "Wait for device to come online")
		return result, nil
	}

	// 根据命令类型进行具体验证
	switch command {
	case dto.WayneCommandAuthorize:
		return sv.validateAuthorizeCommand(result, deviceState, devicePoller)
	case dto.WayneCommandReset:
		return sv.validateResetCommand(result, deviceState, devicePoller)
	case dto.WayneCommandStop:
		return sv.validateStopCommand(result, deviceState, devicePoller)
	case dto.WayneCommandPresetVolume, dto.WayneCommandPresetAmount:
		return sv.validatePresetCommand(result, deviceState, devicePoller)
	case dto.WayneCommandUpdatePrices:
		return sv.validatePriceUpdateCommand(result, deviceState, devicePoller)
	case dto.WayneCommandSuspendNozzle:
		return sv.validateSuspendCommand(result, deviceState, devicePoller)
	case dto.WayneCommandResumeNozzle:
		return sv.validateResumeCommand(result, deviceState, devicePoller)
	default:
		// 查询类命令通常总是允许的
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Query command is always allowed"
		return result, nil
	}
}

// validateAuthorizeCommand 验证授权命令
func (sv *stateValidator) validateAuthorizeCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// 修改：取消状态检查，始终允许授权命令执行
	// 这解决了 reset 后需要等待轮询器更新状态才能 authorize 的问题
	result.IsValid = true
	result.ProtocolStatus = "ACK"
	result.Reason = fmt.Sprintf("Authorize command accepted (state validation bypassed, current pump status: %d)", deviceState.PumpStatus)
	
	// 💡 保留状态信息用于日志记录和调试
	sv.logger.Info("Authorize command validation bypassed",
		zap.String("device_state", string(deviceState.State)),
		zap.Int("pump_status", int(deviceState.PumpStatus)),
		zap.Bool("is_online", deviceState.IsOnline),
		zap.String("reason", "State validation disabled to avoid polling synchronization delay"))

	return result, nil
}

// validateResetCommand 验证重置命令
func (sv *stateValidator) validateResetCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// 根据Wayne DART协议，Reset命令可以在Completed或MaxAmount状态执行
	switch deviceState.PumpStatus {
	case v2models.PumpStatusCompleted:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Device completed transaction, ready for reset"
		
	case v2models.PumpStatusReset:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Device is already in reset state"
		
	case v2models.PumpStatusFilling:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Cannot reset while filling is in progress"
		result.Suggestions = append(result.Suggestions, "Use stop command first, then reset")
		
	case v2models.PumpStatusError:
		// 错误状态下允许重置以恢复设备
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Reset allowed to recover from error state"
		
	default:
		// 其他状态下也允许重置（强制恢复）
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Reset command accepted for state recovery"
	}

	return result, nil
}

// validateStopCommand 验证停止命令
func (sv *stateValidator) validateStopCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// Stop命令可以在多种状态下执行
	switch deviceState.PumpStatus {
	case v2models.PumpStatusFilling:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Stop command accepted - filling in progress"
		
	case v2models.PumpStatusAuthorized:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Stop command accepted - device authorized"
		
	case v2models.PumpStatusSuspended:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Stop command accepted - device suspended"
		
	case v2models.PumpStatusCompleted:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Transaction already completed"
		result.Suggestions = append(result.Suggestions, "Use reset command instead")
		
	case v2models.PumpStatusReset:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "No transaction to stop - device in reset state"
		
	default:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = fmt.Sprintf("Stop not applicable in current state: %d", deviceState.PumpStatus)
	}

	return result, nil
}

// validatePresetCommand 验证预设命令
func (sv *stateValidator) validatePresetCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// 修改：取消状态检查，始终允许预设命令执行
	// 这解决了需要等待轮询器更新状态才能执行预设的问题
	result.IsValid = true
	result.ProtocolStatus = "ACK"
	result.Reason = fmt.Sprintf("Preset command accepted (state validation bypassed, current pump status: %d)", deviceState.PumpStatus)
	
	// 💡 保留状态信息用于日志记录和调试
	sv.logger.Info("Preset command validation bypassed",
		zap.String("device_state", string(deviceState.State)),
		zap.Int("pump_status", int(deviceState.PumpStatus)),
		zap.Bool("is_online", deviceState.IsOnline),
		zap.String("reason", "State validation disabled to avoid polling synchronization delay"))

	return result, nil
}

// validatePriceUpdateCommand 验证价格更新命令
func (sv *stateValidator) validatePriceUpdateCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// 价格更新在大多数状态下都允许，除了正在加油时
	switch deviceState.PumpStatus {
	case v2models.PumpStatusFilling:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Cannot update prices while filling"
		result.Suggestions = append(result.Suggestions, "Wait for filling to complete")
		
	default:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Price update command accepted"
	}

	return result, nil
}

// validateSuspendCommand 验证暂停命令
func (sv *stateValidator) validateSuspendCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// 暂停命令只在授权或加油状态下有效
	switch deviceState.PumpStatus {
	case v2models.PumpStatusAuthorized, v2models.PumpStatusFilling:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Suspend command accepted"
		
	case v2models.PumpStatusSuspended:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Device is already suspended"
		
	default:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Suspend only allowed during authorized or filling state"
	}

	return result, nil
}

// validateResumeCommand 验证恢复命令
func (sv *stateValidator) validateResumeCommand(
	result *CommandValidationResult,
	deviceState *v2models.DeviceStateData,
	devicePoller DevicePollerInterface,
) (*CommandValidationResult, error) {
	
	// 恢复命令只在暂停状态下有效
	switch deviceState.PumpStatus {
	case v2models.PumpStatusSuspended:
		result.IsValid = true
		result.ProtocolStatus = "ACK"
		result.Reason = "Resume command accepted"
		
	default:
		result.IsValid = false
		result.ProtocolStatus = "NACK"
		result.Reason = "Resume only allowed when device is suspended"
	}

	return result, nil
}

// GetProtocolStatus 基于当前状态获取协议状态
func (sv *stateValidator) GetProtocolStatus(ctx context.Context, deviceID string, command dto.WayneCommandType) (string, error) {
	result, err := sv.ValidateCommand(ctx, deviceID, command)
	if err != nil {
		return "NACK", err
	}
	return result.ProtocolStatus, nil
} 