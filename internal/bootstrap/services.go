package bootstrap

import (
	"context"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"fcc-service/internal/services/device_runtime_state"
	"fcc-service/internal/services/nozzle"
	"fcc-service/internal/services/nozzle_counters"
	v2 "fcc-service/internal/services/polling/v2"
	"fcc-service/internal/services/transaction"
	"fcc-service/internal/storage"
	"fcc-service/pkg/models"
)

// TransactionLifecycleServiceFactory 交易生命周期服务工厂函数类型
type TransactionLifecycleServiceFactory func(operatorIDCache v2.OperatorIDCacheInterface) v2.TransactionLifecycleServiceInterface

// CreateTransactionLifecycleServiceFactory 创建交易生命周期服务工厂函数
// 🚀 新增：集成TransactionEventBridge以支持上游事件发布
func CreateTransactionLifecycleServiceFactory(
	db *gorm.DB,
	logger *zap.Logger,
	cache storage.Cache, // 🚀 新增：传入缓存实例
	nozzleService nozzle.ServiceV2,
	// 🚀 新增：TransactionService相关依赖用于事件桥接
	transactionSyncPublisher transaction.TransactionSyncPublisher,
	transactionDataCollector transaction.TransactionDataCollector,
	nozzleResolver nozzle.NozzleResolver,
	nozzleCountersService nozzle_counters.Service,
	runtimeStateService device_runtime_state.DeviceRuntimeStateService, // 🆕 新增：设备运行时状态服务
) TransactionLifecycleServiceFactory {
	logger.Info("Creating transaction lifecycle service factory with event bridge integration")

	// 创建共享的 Repository (这个可以共享)
	repository := NewGormTransactionLifecycleRepository(db, logger)

	// 🎯 创建 TransactionRepository 用于事件桥接器的数据库更新
	transactionRepository := transaction.NewGormRepository(db, logger)

	// 🚀 创建事件桥接器（用于将TransactionLifecycleService事件转发到上游）
	var eventBridge v2.TransactionLifecycleListenerInterface
	if transactionSyncPublisher != nil && transactionDataCollector != nil && nozzleResolver != nil {
		eventBridge = transaction.NewTransactionEventBridge(
			transactionSyncPublisher,
			transactionDataCollector,
			nozzleResolver,
			transactionRepository, // 🎯 传递正确类型的Repository用于数据库更新
			logger,
		)

		// 🎯 设置SyncPublisher的回调函数，连接到TransactionEventBridge
		if bridge, ok := eventBridge.(*transaction.TransactionEventBridge); ok {
			transactionSyncPublisher.SetExternalIDUpdateCallback(bridge.UpdateExternalTransactionID)
			transactionSyncPublisher.SetSyncStatusUpdateCallback(func(ctx context.Context, transactionID, status string) error {
				return bridge.MarkTransactionAsNeedsSync(ctx, transactionID)
			})
			logger.Info("SyncPublisher callbacks configured successfully")
		}

		logger.Info("Transaction event bridge created successfully")
	} else {
		logger.Warn("Transaction event bridge not created - missing dependencies",
			zap.Bool("has_sync_publisher", transactionSyncPublisher != nil),
			zap.Bool("has_data_collector", transactionDataCollector != nil),
			zap.Bool("has_nozzle_resolver", nozzleResolver != nil))
	}

	// 返回工厂函数
	return func(operatorIDCache v2.OperatorIDCacheInterface) v2.TransactionLifecycleServiceInterface {
		// 为每个 DevicePoller 创建独立的服务实例
		lifecycleService := v2.NewService(
			repository,
			nozzleCountersService,
			nozzleService,
			logger,
			v2.DefaultServiceConfig,
			operatorIDCache,     // 使用对应设备的 OperatorIDCache
			runtimeStateService, // 🆕 新增：传递设备运行时状态服务
		)

		// 🚀 注册事件桥接器监听器
		if eventBridge != nil {
			if err := lifecycleService.RegisterTransactionListener(eventBridge); err != nil {
				logger.Error("Failed to register transaction event bridge",
					zap.String("bridge_id", eventBridge.GetListenerID()),
					zap.Error(err))
			} else {
				logger.Info("Transaction event bridge registered successfully",
					zap.String("bridge_id", eventBridge.GetListenerID()))
			}
		}

		return lifecycleService
	}
}

// GormTransactionLifecycleRepository GORM 仓储实现
type GormTransactionLifecycleRepository struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewGormTransactionLifecycleRepository 创建 GORM 仓储
func NewGormTransactionLifecycleRepository(db *gorm.DB, logger *zap.Logger) v2.Repository {
	return &GormTransactionLifecycleRepository{
		db:     db,
		logger: logger,
	}
}

// 实现 Repository 接口
func (r *GormTransactionLifecycleRepository) Create(ctx context.Context, tx *models.Transaction) error {
	return r.db.WithContext(ctx).Create(tx).Error
}

func (r *GormTransactionLifecycleRepository) GetByID(ctx context.Context, id string) (*models.Transaction, error) {
	var tx models.Transaction
	err := r.db.WithContext(ctx).First(&tx, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &tx, nil
}

func (r *GormTransactionLifecycleRepository) Update(ctx context.Context, tx *models.Transaction) error {
	return r.db.WithContext(ctx).Save(tx).Error
}

func (r *GormTransactionLifecycleRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&models.Transaction{}, "id = ?", id).Error
}

func (r *GormTransactionLifecycleRepository) GetActiveByDeviceAndNozzle(ctx context.Context, deviceID string, nozzleID string) (*models.Transaction, error) {
	var tx models.Transaction
	err := r.db.WithContext(ctx).First(&tx, "device_id = ? AND nozzle_id = ? AND status IN ?",
		deviceID, nozzleID, []string{"pending", "pending_counters", "started", "active", "initiated", "filling", "awaiting_final_dc2", "smart_waiting"}).Error
	if err != nil {
		// 🔧 修复：区分"record not found"和其他错误
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有活跃交易是正常情况，返回nil而不是错误
		}
		return nil, err // 其他错误才是真正的错误
	}
	return &tx, nil
}

func (r *GormTransactionLifecycleRepository) GetActiveByDevice(ctx context.Context, deviceID string) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).Find(&transactions, "device_id = ? AND status IN ?",
		deviceID, []string{"pending", "pending_counters", "started", "active", "initiated", "filling", "awaiting_final_dc2", "smart_waiting"}).Error
	return transactions, err
}

func (r *GormTransactionLifecycleRepository) GetByDeviceAndTimeRange(ctx context.Context, deviceID string, start, end time.Time) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).Find(&transactions, "device_id = ? AND created_at BETWEEN ? AND ?",
		deviceID, start, end).Error
	return transactions, err
}

func (r *GormTransactionLifecycleRepository) GetStaleTransactions(ctx context.Context, maxAge time.Duration) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	cutoff := time.Now().Add(-maxAge)
	err := r.db.WithContext(ctx).Find(&transactions, "status IN ? AND updated_at < ?",
		[]string{"pending", "pending_counters", "started", "active", "initiated", "filling"}, cutoff).Error
	return transactions, err
}

func (r *GormTransactionLifecycleRepository) GetByDeviceNozzleAndStatus(ctx context.Context, deviceID string, nozzleID string, status models.TransactionStatus) ([]*models.Transaction, error) {
	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).Find(&transactions, "device_id = ? AND nozzle_id = ? AND status = ?",
		deviceID, nozzleID, status).Error
	return transactions, err
}

// GetRecentCompletedTransactions 获取最近完成的交易（用于处理DC101延迟响应）
func (r *GormTransactionLifecycleRepository) GetRecentCompletedTransactions(ctx context.Context, deviceID string, nozzleID string, duration time.Duration) ([]*models.Transaction, error) {
	cutoffTime := time.Now().Add(-duration)

	var transactions []*models.Transaction
	err := r.db.WithContext(ctx).
		Where("device_id = ? AND nozzle_id = ? AND status = ? AND completed_at > ?",
			deviceID, nozzleID, string(models.TransactionStatusCompleted), cutoffTime).
		Order("completed_at DESC").
		Find(&transactions).Error

	if err != nil {
		r.logger.Error("Failed to get recent completed transactions",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Duration("duration", duration),
			zap.Error(err))
		return nil, err
	}

	r.logger.Debug("Retrieved recent completed transactions",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Duration("duration", duration),
		zap.Int("count", len(transactions)))

	return transactions, nil
}

func (r *GormTransactionLifecycleRepository) GetStatistics(ctx context.Context, deviceID string) (*v2.TransactionStatistics, error) {
	// 简化的统计实现
	stats := &v2.TransactionStatistics{
		DeviceID: deviceID,
	}

	// 获取今日活跃交易数
	var count int64
	err := r.db.WithContext(ctx).Model(&models.Transaction{}).
		Where("device_id = ? AND status IN ?", deviceID, []string{"pending", "pending_counters", "started", "active", "initiated", "filling"}).
		Count(&count).Error
	if err != nil {
		return nil, err
	}

	stats.ActiveTransactions = int(count)

	return stats, nil
}

// EnsureSingleActiveTransaction 确保同一喷嘴只有一个活跃交易
// 🚀 SQL优化实现，使用GORM自动事务管理防止连接泄漏
func (r *GormTransactionLifecycleRepository) EnsureSingleActiveTransaction(ctx context.Context, deviceID string, nozzleID string, reason string) (keptTransactionID *string, cancelledCount int, err error) {
	r.logger.Debug("Ensuring single active transaction using SQL optimization",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("reason", reason))

	// 🔧 修复：使用GORM自动事务管理，避免连接泄漏
	var localKeptTransactionID *string
	var localCancelledCount int

	err = r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 🚀 Step 1: 使用CTE和窗口函数找到需要处理的交易
		var results []struct {
			ID        string    `json:"id"`
			CreatedAt time.Time `json:"created_at"`
			RowNum    int       `json:"row_num"`
		}

		// 使用原生SQL查询，利用索引(device_id, nozzle_id, status)
		query := `
			WITH ranked_transactions AS (
				SELECT
					id,
					created_at,
					ROW_NUMBER() OVER (ORDER BY created_at DESC) as row_num
				FROM transactions
				WHERE device_id = ? AND nozzle_id = ?
				AND status IN ('pending', 'pending_counters', 'started', 'active', 'initiated', 'filling', 'awaiting_final_dc2', 'smart_waiting')
			)
			SELECT id, created_at, row_num
			FROM ranked_transactions
			ORDER BY row_num
		`

		if err := tx.Raw(query, deviceID, nozzleID).Scan(&results).Error; err != nil {
			return err
		}

		// 如果没有或只有一个活跃交易，无需处理
		if len(results) <= 1 {
			if len(results) == 1 {
				localKeptTransactionID = &results[0].ID
			}
			localCancelledCount = 0
			return nil
		}

		// 🚀 Step 2: 批量取消旧的交易（除了最新的）
		var transactionIDsToCancel []string
		keptID := results[0].ID // 最新的交易（row_num = 1）
		localKeptTransactionID = &keptID

		for i := 1; i < len(results); i++ {
			transactionIDsToCancel = append(transactionIDsToCancel, results[i].ID)
		}

		r.logger.Info("Found multiple active transactions, will cancel older ones",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("kept_transaction_id", keptID),
			zap.Int("total_transactions", len(results)),
			zap.Int("to_cancel", len(transactionIDsToCancel)))

		// 🚀 Step 3: 批量更新状态为cancelled
		now := time.Now()
		updateResult := tx.Model(&models.Transaction{}).
			Where("id IN ?", transactionIDsToCancel).
			Updates(map[string]interface{}{
				"status":       "cancelled",
				"cancelled_at": now,
				"updated_at":   now,
			})

		if updateResult.Error != nil {
			return updateResult.Error
		}

		actualCancelledCount := int(updateResult.RowsAffected)
		localCancelledCount = actualCancelledCount

		// 🚀 Step 4: 批量更新状态历史（使用JSON函数）
		stateChangeJSON := `{
			"from_status": "active",
			"to_status": "cancelled", 
			"timestamp": "` + now.Format(time.RFC3339) + `",
			"reason": "` + reason + `",
			"source": "sql_cleanup"
		}`

		// 使用PostgreSQL的JSON函数更新状态历史
		updateHistoryQuery := `
			UPDATE transactions 
			SET state_history = CASE 
				WHEN state_history IS NULL OR state_history = '[]' THEN 
					?::jsonb
				ELSE 
					state_history || ?::jsonb
				END
			WHERE id IN ?
		`

		if err := tx.Exec(updateHistoryQuery,
			"["+stateChangeJSON+"]",
			stateChangeJSON,
			transactionIDsToCancel).Error; err != nil {
			// 状态历史更新失败不影响主要逻辑
			r.logger.Warn("Failed to update state history for cancelled transactions",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Error(err))
		}

		r.logger.Info("Successfully ensured single active transaction",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.String("kept_transaction_id", keptID),
			zap.Int("cancelled_count", actualCancelledCount),
			zap.String("reason", reason))

		return nil
	})

	return localKeptTransactionID, localCancelledCount, err
}

// GetTransactionByPumpReading 根据泵码匹配查找交易
// 优化：1. 不判断status 2. 判断end pump reading为空或为0
func (r *GormTransactionLifecycleRepository) GetTransactionByPumpReading(ctx context.Context, deviceID string, nozzleID string, counterType int16, previousValue decimal.Decimal) (*models.Transaction, error) {
	r.logger.Debug("Finding transaction by pump reading match",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.Int16("counter_type", counterType),
		zap.String("previous_value", previousValue.String()))

	var tx models.Transaction
	var query *gorm.DB

	// 根据计数器类型选择匹配字段，并确保end pump reading为空或为0
	if counterType >= 1 && counterType <= 8 {
		// 体积计数器：匹配 start_pump_volume_reading，且 end_pump_volume_reading 为空或为0
		query = r.db.WithContext(ctx).
			Where("device_id = ? AND nozzle_id = ? AND start_pump_volume_reading = ? AND (end_pump_volume_reading IS NULL OR end_pump_volume_reading = 0)",
				deviceID, nozzleID, previousValue)
	} else if counterType >= 17 && counterType <= 24 {
		// 金额计数器：匹配 start_pump_amount_reading，且 end_pump_amount_reading 为空或为0
		query = r.db.WithContext(ctx).
			Where("device_id = ? AND nozzle_id = ? AND start_pump_amount_reading = ? AND (end_pump_amount_reading IS NULL OR end_pump_amount_reading = 0)",
				deviceID, nozzleID, previousValue)
	} else {
		// 未知计数器类型
		r.logger.Warn("Unknown counter type for pump reading match",
			zap.Int16("counter_type", counterType))
		return nil, nil
	}

	err := query.Order("created_at DESC").First(&tx).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			r.logger.Debug("No matching transaction found for pump reading",
				zap.String("device_id", deviceID),
				zap.String("nozzle_id", nozzleID),
				zap.Int16("counter_type", counterType),
				zap.String("previous_value", previousValue.String()))
			return nil, nil // 找不到匹配交易是正常的
		}
		r.logger.Error("Failed to find transaction by pump reading",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to find transaction by pump reading: %w", err)
	}

	r.logger.Debug("Found matching transaction for pump reading",
		zap.String("transaction_id", tx.ID),
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID))

	return &tx, nil
}
