package adapters

import (
	"fmt"

	"fcc-service/internal/adapters/wayne"
	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"
)

// Factory 适配器工厂实现
type Factory struct {
	// 注册的适配器创建函数
	creators map[models.ProtocolType]api.AdapterCreator
}

// NewFactory 创建适配器工厂
func NewFactory() api.AdapterFactory {
	f := &Factory{
		creators: make(map[models.ProtocolType]api.AdapterCreator),
	}

	// 注册Wayne DART适配器
	f.RegisterCreator(models.ProtocolTypeWayneDart, wayne.NewWayneAdapter)

	return f
}

// CreateAdapter 创建设备适配器
func (f *Factory) CreateAdapter(protocolType models.ProtocolType, config api.AdapterConfig) (api.DeviceAdapter, error) {
	creator, exists := f.creators[protocolType]
	if !exists {
		return nil, errors.NewValidationError(
			fmt.Sprintf("Unsupported protocol type: %s", protocolType),
		)
	}

	adapter, err := creator(config)
	if err != nil {
		return nil, errors.NewInternalError(
			fmt.Sprintf("Failed to create adapter for protocol %s: %v", protocolType, err),
		)
	}

	return adapter, nil
}

// RegisterCreator 注册适配器创建函数
func (f *Factory) RegisterCreator(protocolType models.ProtocolType, creator api.AdapterCreator) {
	f.creators[protocolType] = creator
}

// GetSupportedProtocols 获取支持的协议类型
func (f *Factory) GetSupportedProtocols() []models.ProtocolType {
	protocols := make([]models.ProtocolType, 0, len(f.creators))
	for protocol := range f.creators {
		protocols = append(protocols, protocol)
	}
	return protocols
}

// RegisterAdapter 注册适配器（实现AdapterFactory接口）
func (f *Factory) RegisterAdapter(protocolType models.ProtocolType, creator api.AdapterCreator) error {
	f.creators[protocolType] = creator
	return nil
}
