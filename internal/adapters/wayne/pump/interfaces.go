// Package pump implements Wayne Pump Interface Protocol v2.1
//
// This package provides CD/DC transaction format processing and BCD format conversion
// according to Wayne DART Pump Interface Protocol specification.
package pump

import (
	"time"
)

// TransactionType represents the type of transaction (CD or DC)
type TransactionType byte

const (
	// CD transactions (Controller to Device)
	TransactionTypeCD1   TransactionType = 0x01 // Command to pump
	TransactionTypeCD2   TransactionType = 0x02 // Allowed nozzle numbers
	TransactionTypeCD3   TransactionType = 0x03 // Preset volume
	TransactionTypeCD4   TransactionType = 0x04 // Preset amount
	TransactionTypeCD5   TransactionType = 0x05 // Price update
	TransactionTypeCD7   TransactionType = 0x07 // Command to output
	TransactionTypeCD8   TransactionType = 0x08 // Request total counters
	TransactionTypeCD9   TransactionType = 0x09 // Set pump parameters
	TransactionTypeCD14  TransactionType = 0x0E // Suspend request
	TransactionTypeCD15  TransactionType = 0x0F // Resume request
	TransactionTypeCD101 TransactionType = 0x65 // Request total counters

	// DC transactions (Device to Controller)
	TransactionTypeDC1   TransactionType = 0x01 // Pump status
	TransactionTypeDC2   TransactionType = 0x02 // Filled volume and amount
	TransactionTypeDC3   TransactionType = 0x03 // Nozzle status and filling price
	TransactionTypeDC5   TransactionType = 0x05 // Alarm code
	TransactionTypeDC7   TransactionType = 0x07 // Pump parameters
	TransactionTypeDC14  TransactionType = 0x0E // Suspend reply
	TransactionTypeDC15  TransactionType = 0x0F // Resume reply
	TransactionTypeDC101 TransactionType = 0x65 // Total counters

	// 新增：特殊事务类型
	TransactionTypeNoData  TransactionType = 0xFE // No data available (ACK response)
	TransactionTypeSuccess TransactionType = 0xFF // Success operation (ACK response)
)

// PumpCommand represents the pump control command codes for CD1 transaction
type PumpCommand byte

const (
	CmdReturnStatus             PumpCommand = 0x00 // Return status
	CmdReturnPumpParameters     PumpCommand = 0x02 // Return pump parameters
	CmdReturnPumpIdentity       PumpCommand = 0x03 // Return pump identity
	CmdReturnFillingInformation PumpCommand = 0x04 // Return filling information
	CmdReset                    PumpCommand = 0x05 // Reset
	CmdAuthorize                PumpCommand = 0x06 // Authorize
	CmdStop                     PumpCommand = 0x08 // Stop
	CmdSwitchOff                PumpCommand = 0x0A // Switch off
	CmdSuspendFuellingPoint     PumpCommand = 0x0D // Suspend fuelling point
	CmdResumeFuellingPoint      PumpCommand = 0x0E // Resume fuelling point
	CmdReturnPricesOfAllGrades  PumpCommand = 0x0F // Return prices of all current grades
)

// PumpStatus represents the pump status codes for DC1 transaction
type PumpStatus byte

const (
	StatusPumpNotProgrammed PumpStatus = 0x00 // Pump not programmed
	StatusReset             PumpStatus = 0x01 // Reset (对应数据库idle状态)
	StatusAuthorized        PumpStatus = 0x02 // Authorized
	StatusFilling           PumpStatus = 0x04 // Filling
	StatusFillingCompleted  PumpStatus = 0x05 // Filling completed
	StatusMaxAmountReached  PumpStatus = 0x06 // Max amount/volume reached
	StatusSwitchedOff       PumpStatus = 0x07 // Switched off
	StatusSuspended         PumpStatus = 0x08 // Suspended (fuelling point suspended)
)

// Transaction represents a Wayne pump interface transaction
type Transaction interface {
	// GetType returns the transaction type (CD or DC)
	GetType() TransactionType

	// GetLength returns the length of transaction data
	GetLength() byte

	// GetData returns the transaction data bytes
	GetData() []byte

	// Encode encodes the transaction to bytes for transmission
	Encode() []byte

	// Decode decodes bytes into transaction data
	Decode(data []byte) error

	// Validate validates the transaction data
	Validate() error
}

// CDTransaction represents a Controller to Device transaction
type CDTransaction interface {
	Transaction

	// GetCommandCode returns the command code for CD transactions
	GetCommandCode() byte
}

// DCTransaction represents a Device to Controller transaction
type DCTransaction interface {
	Transaction

	// GetStatusCode returns the status code for DC transactions (if applicable)
	GetStatusCode() byte

	// GetTimestamp returns when this transaction was created/received
	GetTimestamp() time.Time
}

// BCDConverter handles conversion between numeric values and BCD format
type BCDConverter interface {
	// EncodeBCD converts an integer value to packed BCD format
	// Returns bytes with MSB first (big-endian)
	EncodeBCD(value int64, bytes int) []byte

	// DecodeBCD converts packed BCD bytes to integer value
	// Expects bytes with MSB first (big-endian)
	DecodeBCD(data []byte) int64

	// EncodePrice converts a price (float64) to BCD format for price fields
	// Uses the specified number of decimal places and byte length
	EncodePrice(price float64, decimals int, bytes int) []byte

	// DecodePrice converts BCD bytes to price (float64)
	// Uses the specified number of decimal places
	DecodePrice(data []byte, decimals int) float64

	// EncodeVolume converts a volume (float64) to BCD format for volume fields
	// Uses the specified number of decimal places and byte length
	EncodeVolume(volume float64, decimals int, bytes int) []byte

	// DecodeVolume converts BCD bytes to volume (float64)
	// Uses the specified number of decimal places
	DecodeVolume(data []byte, decimals int) float64

	// EncodeAmount converts an amount (float64) to BCD format for amount fields
	// Uses the specified number of decimal places and byte length
	EncodeAmount(amount float64, decimals int, bytes int) []byte

	// DecodeAmount converts BCD bytes to amount (float64)
	// Uses the specified number of decimal places
	DecodeAmount(data []byte, decimals int) float64

	// DC101 累计计数器专用方法 - Wayne DART协议扩展

	// EncodeTotalCounter converts a total counter value to 5-byte BCD format
	// Used for DC101 total counter transactions (5 bytes MSB first)
	EncodeTotalCounter(value int64) []byte

	// DecodeTotalCounter converts 5-byte BCD to total counter value
	// Used for DC101 total counter transactions (5 bytes MSB first)
	DecodeTotalCounter(data []byte) int64

	// DecodeTotalCounterVolume converts 5-byte BCD to volume with specified decimals
	// Used for DC101 volume counter (01H-08H, 09H types)
	DecodeTotalCounterVolume(data []byte, decimals int) float64

	// DecodeTotalCounterAmount converts 5-byte BCD to amount with specified decimals
	// Used for DC101 amount counter (11H-18H, 19H types)
	DecodeTotalCounterAmount(data []byte, decimals int) float64

	// ValidateTotalCounterData validates 5-byte BCD data for DC101
	ValidateTotalCounterData(data []byte) error
}

// TransactionBuilder creates specific transaction types
type TransactionBuilder interface {
	// CD Transaction builders
	BuildCD1(command PumpCommand) (CDTransaction, error)
	BuildCD2(nozzleNumbers []byte) (CDTransaction, error)
	BuildCD3(volume []byte) (CDTransaction, error)
	BuildCD4(amount []byte) (CDTransaction, error)
	BuildCD5(prices [][]byte) (CDTransaction, error)
	BuildCD14(nozzleNumber byte) (CDTransaction, error)
	BuildCD15(nozzleNumber byte) (CDTransaction, error)
	BuildCD101(counter byte) (CDTransaction, error)

	// DC Transaction parsers
	ParseDC1(data []byte) (DCTransaction, error)
	ParseDC2(data []byte) (DCTransaction, error)
	ParseDC3(data []byte) (DCTransaction, error)
	ParseDC5(data []byte) (DCTransaction, error)
	ParseDC7(data []byte) (DCTransaction, error)
	ParseDC14(data []byte) (DCTransaction, error)
	ParseDC15(data []byte) (DCTransaction, error)
	ParseDC101(data []byte) (DCTransaction, error)
}

// TransactionProcessor processes transactions and manages pump state
type TransactionProcessor interface {
	// ProcessCDTransaction processes a CD (Controller to Device) transaction
	ProcessCDTransaction(transaction CDTransaction) (DCTransaction, error)

	// ProcessDCTransaction processes a DC (Device to Controller) transaction
	ProcessDCTransaction(transaction DCTransaction) error

	// GetCurrentPumpStatus returns the current pump status
	GetCurrentPumpStatus() PumpStatus

	// GetTransactionHistory returns recent transaction history
	GetTransactionHistory(limit int) []Transaction
}

// PumpStateMachineInterface defines the interface for pump state management
type PumpStateMachineInterface interface {
	// State management
	GetCurrentStatus() PumpStatus
	SetState(status PumpStatus) error
	IsSuspended() bool
	GetStateHistory() []StateTransition

	// Command processing
	ProcessCommand(command PumpCommand) error
	ProcessCDTransaction(transaction CDTransaction) (DCTransaction, error)
	ProcessDCTransaction(transaction DCTransaction) error

	// Event processing
	ProcessNozzleEvent(nozzleOut bool, nozzleNumber byte) error
	ProcessVolumeUpdate(volume int64) error
	ProcessAmountUpdate(amount int64) error
	ProcessAlarm(alarmCode byte) error

	// Preset management
	SetPresetVolume(volume int64) error
	SetPresetAmount(amount int64) error

	// Price management
	SetPricesReceived(received bool)
}

// StateTransition represents a state change event
type StateTransition struct {
	FromStatus PumpStatus
	ToStatus   PumpStatus
	Timestamp  time.Time
	Trigger    string // What caused the transition
}

// ValidationError represents a transaction validation error
type ValidationError struct {
	Field   string
	Message string
	Value   interface{}
}

func (e *ValidationError) Error() string {
	return "validation error: " + e.Field + " - " + e.Message
}

// TransactionError represents a transaction processing error
type TransactionError struct {
	Type    TransactionType
	Message string
	Cause   error
}

func (e *TransactionError) Error() string {
	return "transaction error: " + e.Message
}

func (e *TransactionError) Unwrap() error {
	return e.Cause
}
