package pump

import (
	"fmt"
	"math"
)

// bcdConverter implements the BCDConverter interface
type bcdConverter struct{}

// NewBCDConverter creates a new BCD converter instance
func NewBCDConverter() BCDConverter {
	return &bcdConverter{}
}

// EncodeBCD converts an integer value to packed BCD format
// Returns bytes with MSB first (big-endian)
func (c *bcdConverter) EncodeBCD(value int64, bytes int) []byte {
	if bytes <= 0 {
		return []byte{}
	}

	if value < 0 {
		value = 0 // BCD doesn't support negative values
	}

	result := make([]byte, bytes)

	// Convert from least significant to most significant byte
	for i := bytes - 1; i >= 0; i-- {
		// Extract two decimal digits for this byte
		lowDigit := value % 10
		value /= 10
		highDigit := value % 10
		value /= 10

		// Pack into BCD byte: high nibble + low nibble
		result[i] = byte((highDigit << 4) | lowDigit)
	}

	return result
}

// DecodeBCD converts packed BCD bytes to integer value
// Expects bytes with MSB first (big-endian)
func (c *bcdConverter) DecodeBCD(data []byte) int64 {
	if len(data) == 0 {
		return 0
	}

	var result int64 = 0

	for _, b := range data {
		highDigit := (b >> 4) & 0x0F
		lowDigit := b & 0x0F

		// Check for invalid BCD digits
		if highDigit > 9 || lowDigit > 9 {
			// Invalid BCD - could return error or handle gracefully
			// For now, treat invalid digits as 0
			if highDigit > 9 {
				highDigit = 0
			}
			if lowDigit > 9 {
				lowDigit = 0
			}
		}

		result = result*100 + int64(highDigit)*10 + int64(lowDigit)
	}

	return result
}

// EncodePrice converts a price (float64) to BCD format for price fields
// 修复：直接使用传入的价格值，不做任何倍数或小数处理
// 前端传输的价格是多少，就下发多少给油机，保证数据一致性
func (c *bcdConverter) EncodePrice(price float64, decimals int, bytes int) []byte {
	// 直接将传入的float64转换为int64，不做任何倍数或小数处理
	intValue := int64(math.Round(price))

	return c.EncodeBCD(intValue, bytes)
}

// DecodePrice converts BCD bytes to price (float64)
// 修复：直接返回解码后的价格值，不做任何倍数或小数处理
// 保证解码后的价格与原始传入的价格一致
func (c *bcdConverter) DecodePrice(data []byte, decimals int) float64 {
	intValue := c.DecodeBCD(data)
	// 直接返回整数值作为float64，不做任何倍数或小数处理
	return float64(intValue)
}

// EncodeVolume converts a volume (float64) to BCD format for volume fields
// Uses the specified number of decimal places and byte length
func (c *bcdConverter) EncodeVolume(volume float64, decimals int, bytes int) []byte {
	// Convert float to integer by multiplying by 10^decimals
	multiplier := int64(math.Pow(10, float64(decimals)))
	intValue := int64(math.Round(volume * float64(multiplier)))

	return c.EncodeBCD(intValue, bytes)
}

// DecodeVolume converts BCD bytes to volume (float64)
// Uses the specified number of decimal places
func (c *bcdConverter) DecodeVolume(data []byte, decimals int) float64 {
	intValue := c.DecodeBCD(data)

	// Convert integer back to float by dividing by 10^decimals
	divisor := math.Pow(10, float64(decimals))
	return float64(intValue) / divisor
}

// EncodeAmount converts an amount (float64) to BCD format for amount fields
// 修复：直接使用传入的金额值，不做任何倍数或小数处理
// 前端传输的金额是多少，就下发多少给油机，保证数据一致性
func (c *bcdConverter) EncodeAmount(amount float64, decimals int, bytes int) []byte {
	// 直接将传入的float64转换为int64，不做任何倍数或小数处理
	intValue := int64(math.Round(amount))

	return c.EncodeBCD(intValue, bytes)
}

// DecodeAmount converts BCD bytes to amount (float64)
// 修复：直接返回解码后的金额值，不做任何倍数或小数处理
// 保证解码后的金额与原始传入的金额一致
func (c *bcdConverter) DecodeAmount(data []byte, decimals int) float64 {
	intValue := c.DecodeBCD(data)

	// 直接返回整数值作为float64，不做任何倍数或小数处理
	return float64(intValue)
}

// Additional utility functions for common Wayne protocol BCD operations

// EncodeBCDPrice3 encodes a price to 3-byte BCD format (common in Wayne protocol)
// 修复：直接使用传入的价格值，不做任何倍数或小数处理
func EncodeBCDPrice3(price float64) []byte {
	converter := &bcdConverter{}
	return converter.EncodePrice(price, 3, 3)
}

// DecodeBCDPrice3 decodes a 3-byte BCD price
// 修复：直接返回解码后的价格值，不做任何倍数或小数处理
func DecodeBCDPrice3(data []byte) float64 {
	converter := &bcdConverter{}
	return converter.DecodePrice(data, 3)
}

// EncodeBCDVolume4 encodes a volume to 4-byte BCD format (common in Wayne protocol)
func EncodeBCDVolume4(volume float64) []byte {
	converter := &bcdConverter{}
	return converter.EncodeVolume(volume, 3, 4)
}

// DecodeBCDVolume4 decodes a 4-byte BCD volume
func DecodeBCDVolume4(data []byte) float64 {
	converter := &bcdConverter{}
	return converter.DecodeVolume(data, 3)
}

// EncodeBCDAmount4 encodes an amount to 4-byte BCD format (common in Wayne protocol)
// 修复：直接使用传入的金额值，不做任何倍数或小数处理
func EncodeBCDAmount4(amount float64) []byte {
	converter := &bcdConverter{}
	return converter.EncodeAmount(amount, 2, 4)
}

// DecodeBCDAmount4 decodes a 4-byte BCD amount
// 修复：直接返回解码后的金额值，不做任何倍数或小数处理
func DecodeBCDAmount4(data []byte) float64 {
	converter := &bcdConverter{}
	return converter.DecodeAmount(data, 2)
}

// ValidateBCD checks if the given bytes contain valid BCD data
func ValidateBCD(data []byte) error {
	for i, b := range data {
		highDigit := (b >> 4) & 0x0F
		lowDigit := b & 0x0F

		if highDigit > 9 {
			return fmt.Errorf("invalid BCD digit %X in high nibble at byte %d", highDigit, i)
		}
		if lowDigit > 9 {
			return fmt.Errorf("invalid BCD digit %X in low nibble at byte %d", lowDigit, i)
		}
	}
	return nil
}

// DC101 累计计数器专用方法实现 - Wayne DART协议扩展

// EncodeTotalCounter converts a total counter value to 5-byte BCD format
// Used for DC101 total counter transactions (5 bytes MSB first)
func (c *bcdConverter) EncodeTotalCounter(value int64) []byte {
	// DC101 累计计数器固定使用5字节
	const DC101_COUNTER_BYTES = 5

	if value < 0 {
		value = 0 // 累计计数器不能为负值
	}

	// 使用基础EncodeBCD方法，固定5字节
	return c.EncodeBCD(value, DC101_COUNTER_BYTES)
}

// DecodeTotalCounter converts 5-byte BCD to total counter value
// Used for DC101 total counter transactions (5 bytes MSB first)
func (c *bcdConverter) DecodeTotalCounter(data []byte) int64 {
	const DC101_COUNTER_BYTES = 5

	if len(data) == 0 {
		return 0
	}

	// 如果不是5字节，截取或填充
	if len(data) != DC101_COUNTER_BYTES {
		normalizedData := make([]byte, DC101_COUNTER_BYTES)
		if len(data) < DC101_COUNTER_BYTES {
			// 数据不足5字节，前面补零
			copy(normalizedData[DC101_COUNTER_BYTES-len(data):], data)
		} else {
			// 数据超过5字节，取后面5字节
			copy(normalizedData, data[len(data)-DC101_COUNTER_BYTES:])
		}
		data = normalizedData
	}

	return c.DecodeBCD(data)
}

// DecodeTotalCounterVolume converts 5-byte BCD to volume with specified decimals
// Used for DC101 volume counter (01H-08H, 09H types)
func (c *bcdConverter) DecodeTotalCounterVolume(data []byte, decimals int) float64 {
	intValue := c.DecodeTotalCounter(data)

	// 转换为浮点数，除以10^decimals
	divisor := math.Pow(10, float64(decimals))
	return float64(intValue) / divisor
}

// DecodeTotalCounterAmount converts 5-byte BCD to amount with specified decimals
// Used for DC101 amount counter (11H-18H, 19H types)
func (c *bcdConverter) DecodeTotalCounterAmount(data []byte, decimals int) float64 {
	intValue := c.DecodeTotalCounter(data)

	// 转换为浮点数，除以10^decimals
	divisor := math.Pow(10, float64(decimals))
	return float64(intValue) / divisor
}

// ValidateTotalCounterData validates 5-byte BCD data for DC101
func (c *bcdConverter) ValidateTotalCounterData(data []byte) error {
	const DC101_COUNTER_BYTES = 5

	if len(data) != DC101_COUNTER_BYTES {
		return fmt.Errorf("DC101 counter data must be exactly %d bytes, got %d", DC101_COUNTER_BYTES, len(data))
	}

	// 验证BCD格式
	if err := ValidateBCD(data); err != nil {
		return fmt.Errorf("invalid DC101 counter BCD data: %w", err)
	}

	return nil
}

// DC101相关的便利函数

// DecodeBCDTotalCounter5 decodes a 5-byte BCD total counter (便利函数)
func DecodeBCDTotalCounter5(data []byte) int64 {
	converter := &bcdConverter{}
	return converter.DecodeTotalCounter(data)
}

// DecodeBCDTotalCounterVolume5 decodes a 5-byte BCD total counter as volume (便利函数)
func DecodeBCDTotalCounterVolume5(data []byte, decimals int) float64 {
	converter := &bcdConverter{}
	return converter.DecodeTotalCounterVolume(data, decimals)
}

// DecodeBCDTotalCounterAmount5 decodes a 5-byte BCD total counter as amount (便利函数)
func DecodeBCDTotalCounterAmount5(data []byte, decimals int) float64 {
	converter := &bcdConverter{}
	return converter.DecodeTotalCounterAmount(data, decimals)
}

// EncodeBCDTotalCounter5 encodes a value to 5-byte BCD total counter (便利函数)
func EncodeBCDTotalCounter5(value int64) []byte {
	converter := &bcdConverter{}
	return converter.EncodeTotalCounter(value)
}
