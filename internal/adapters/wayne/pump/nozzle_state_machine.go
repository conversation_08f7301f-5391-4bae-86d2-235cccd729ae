package pump

import (
	"fmt"
	"sync"
	"time"
)

// NozzleStatus 油枪状态枚举
type NozzleStatus byte

const (
	NozzleStatusUnknown     NozzleStatus = 0x00 // 未知状态
	NozzleStatusIn          NozzleStatus = 0x01 // 油枪插入
	NozzleStatusOut         NozzleStatus = 0x02 // 油枪拔出
	NozzleStatusDispensing  NozzleStatus = 0x03 // 正在加油
	NozzleStatusCompleted   NozzleStatus = 0x04 // 加油完成
	NozzleStatusError       NozzleStatus = 0x05 // 故障状态
	NozzleStatusMaintenance NozzleStatus = 0x06 // 维护状态
)

// NozzleEvent 油枪事件枚举
type NozzleEvent string

const (
	NozzleEventIn          NozzleEvent = "nozzle_in"          // 油枪插入
	NozzleEventOut         NozzleEvent = "nozzle_out"         // 油枪拔出
	NozzleEventStartFlow   NozzleEvent = "start_flow"         // 开始出油
	NozzleEventStopFlow    NozzleEvent = "stop_flow"          // 停止出油
	NozzleEventComplete    NozzleEvent = "complete"           // 完成加油
	NozzleEventError       NozzleEvent = "error"              // 错误
	NozzleEventRecover     NozzleEvent = "recover"            // 恢复
	NozzleEventMaintenance NozzleEvent = "maintenance"        // 进入维护
	NozzleEventReset       NozzleEvent = "reset"              // 重置
)

// NozzleStateTransition 油枪状态转换记录
type NozzleStateTransition struct {
	FromStatus NozzleStatus `json:"from_status"`
	ToStatus   NozzleStatus `json:"to_status"`
	Event      NozzleEvent  `json:"event"`
	Timestamp  time.Time    `json:"timestamp"`
	Trigger    string       `json:"trigger"`
}

// NozzleStateMachine 油枪状态机
type NozzleStateMachine struct {
	// 基本信息
	nozzleNumber  byte         // 油枪编号 (1-15)
	currentStatus NozzleStatus // 当前状态
	
	// 业务状态
	isSelected       bool    // 是否被选中
	currentGrade     byte    // 当前油品等级
	currentPrice     float64 // 当前价格
	isFlowing        bool    // 是否正在出油
	
	// 累计数据
	currentVolume float64 // 当前加油体积
	currentAmount float64 // 当前加油金额
	totalVolume   float64 // 累计体积
	totalAmount   float64 // 累计金额
	fillCount     int64   // 加油次数
	
	// 状态历史
	stateHistory []NozzleStateTransition
	
	// 并发控制
	mutex sync.RWMutex
	
	// 配置
	maxHistorySize int
}

// NozzleStateMachineInterface 油枪状态机接口
type NozzleStateMachineInterface interface {
	// 状态管理
	GetCurrentStatus() NozzleStatus
	GetNozzleNumber() byte
	IsSelected() bool
	IsFlowing() bool
	
	// 事件处理
	ProcessEvent(event NozzleEvent, trigger string) error
	ProcessNozzleIO(nozzleOut bool) error
	ProcessVolumeUpdate(volume float64) error
	ProcessAmountUpdate(amount float64) error
	
	// 业务管理
	SetSelected(selected bool, grade byte) error
	SetPrice(price float64) error
	StartFlow() error
	StopFlow() error
	CompleteTransaction() error
	
	// 状态查询
	GetStateHistory() []NozzleStateTransition
	GetCurrentTransaction() *NozzleTransaction
	GetTotals() *NozzleTotals
	
	// 控制操作
	Reset() error
	EnterMaintenance() error
	ExitMaintenance() error
}

// NozzleTransaction 油枪交易信息
type NozzleTransaction struct {
	NozzleNumber  byte      `json:"nozzle_number"`
	Grade         byte      `json:"grade"`
	Price         float64   `json:"price"`
	Volume        float64   `json:"volume"`
	Amount        float64   `json:"amount"`
	StartTime     time.Time `json:"start_time"`
	EndTime       time.Time `json:"end_time"`
	Status        string    `json:"status"`
}

// NozzleTotals 油枪累计数据
type NozzleTotals struct {
	NozzleNumber byte    `json:"nozzle_number"`
	TotalVolume  float64 `json:"total_volume"`
	TotalAmount  float64 `json:"total_amount"`
	FillCount    int64   `json:"fill_count"`
	LastUpdate   time.Time `json:"last_update"`
}

// NewNozzleStateMachine 创建新的油枪状态机
func NewNozzleStateMachine(nozzleNumber byte) NozzleStateMachineInterface {
	if nozzleNumber < 1 || nozzleNumber > 15 {
		nozzleNumber = 1 // 默认油枪1
	}
	
	sm := &NozzleStateMachine{
		nozzleNumber:   nozzleNumber,
		currentStatus:  NozzleStatusIn, // 默认插入状态
		isSelected:     false,
		currentGrade:   0,
		currentPrice:   0.0,
		isFlowing:      false,
		currentVolume:  0.0,
		currentAmount:  0.0,
		totalVolume:    0.0,
		totalAmount:    0.0,
		fillCount:      0,
		stateHistory:   make([]NozzleStateTransition, 0),
		maxHistorySize: 1000, // 保留最近1000条状态变更记录
	}
	
	// 记录初始状态
	sm.recordStateTransition(NozzleStatusUnknown, NozzleStatusIn, NozzleEventReset, "initialization")
	
	return sm
}

// GetCurrentStatus 获取当前状态
func (sm *NozzleStateMachine) GetCurrentStatus() NozzleStatus {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.currentStatus
}

// GetNozzleNumber 获取油枪编号
func (sm *NozzleStateMachine) GetNozzleNumber() byte {
	return sm.nozzleNumber
}

// IsSelected 是否被选中
func (sm *NozzleStateMachine) IsSelected() bool {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.isSelected
}

// IsFlowing 是否正在出油
func (sm *NozzleStateMachine) IsFlowing() bool {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.isFlowing
}

// ProcessEvent 处理油枪事件
func (sm *NozzleStateMachine) ProcessEvent(event NozzleEvent, trigger string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	oldStatus := sm.currentStatus
	newStatus, err := sm.getNextState(sm.currentStatus, event)
	if err != nil {
		return err
	}
	
	// 执行状态转换
	sm.currentStatus = newStatus
	sm.recordStateTransition(oldStatus, newStatus, event, trigger)
	
	// 处理特殊事件的副作用
	switch event {
	case NozzleEventOut:
		// 油枪拔出时重置流动状态
		sm.isFlowing = false
	case NozzleEventIn:
		// 油枪插入时停止流动
		sm.isFlowing = false
	case NozzleEventStartFlow:
		sm.isFlowing = true
	case NozzleEventStopFlow, NozzleEventComplete:
		sm.isFlowing = false
	case NozzleEventReset:
		sm.resetInternalState()
	}
	
	return nil
}

// ProcessNozzleIO 处理油枪插拔事件
func (sm *NozzleStateMachine) ProcessNozzleIO(nozzleOut bool) error {
	if nozzleOut {
		return sm.ProcessEvent(NozzleEventOut, "nozzle_physical_out")
	} else {
		return sm.ProcessEvent(NozzleEventIn, "nozzle_physical_in")
	}
}

// ProcessVolumeUpdate 处理体积更新
func (sm *NozzleStateMachine) ProcessVolumeUpdate(volume float64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	if volume < 0 {
		return fmt.Errorf("invalid volume: %f", volume)
	}
	
	sm.currentVolume = volume
	
	// 如果有体积变化且状态为拔出，自动开始加油
	if volume > 0 && sm.currentStatus == NozzleStatusOut && !sm.isFlowing {
		sm.mutex.Unlock()
		return sm.StartFlow()
	}
	
	return nil
}

// ProcessAmountUpdate 处理金额更新
func (sm *NozzleStateMachine) ProcessAmountUpdate(amount float64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	if amount < 0 {
		return fmt.Errorf("invalid amount: %f", amount)
	}
	
	sm.currentAmount = amount
	return nil
}

// SetSelected 设置选中状态
func (sm *NozzleStateMachine) SetSelected(selected bool, grade byte) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sm.isSelected = selected
	if selected {
		sm.currentGrade = grade
	} else {
		sm.currentGrade = 0
	}
	
	return nil
}

// SetPrice 设置价格
func (sm *NozzleStateMachine) SetPrice(price float64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	if price < 0 {
		return fmt.Errorf("invalid price: %f", price)
	}
	
	sm.currentPrice = price
	return nil
}

// StartFlow 开始出油
func (sm *NozzleStateMachine) StartFlow() error {
	return sm.ProcessEvent(NozzleEventStartFlow, "start_dispensing")
}

// StopFlow 停止出油
func (sm *NozzleStateMachine) StopFlow() error {
	return sm.ProcessEvent(NozzleEventStopFlow, "stop_dispensing")
}

// CompleteTransaction 完成交易
func (sm *NozzleStateMachine) CompleteTransaction() error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	// 更新累计数据
	sm.totalVolume += sm.currentVolume
	sm.totalAmount += sm.currentAmount
	sm.fillCount++
	
	sm.mutex.Unlock()
	return sm.ProcessEvent(NozzleEventComplete, "transaction_completed")
}

// Reset 重置状态机
func (sm *NozzleStateMachine) Reset() error {
	return sm.ProcessEvent(NozzleEventReset, "manual_reset")
}

// EnterMaintenance 进入维护模式
func (sm *NozzleStateMachine) EnterMaintenance() error {
	return sm.ProcessEvent(NozzleEventMaintenance, "maintenance_mode")
}

// ExitMaintenance 退出维护模式
func (sm *NozzleStateMachine) ExitMaintenance() error {
	return sm.ProcessEvent(NozzleEventRecover, "exit_maintenance")
}

// GetStateHistory 获取状态历史
func (sm *NozzleStateMachine) GetStateHistory() []NozzleStateTransition {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	// 返回副本
	history := make([]NozzleStateTransition, len(sm.stateHistory))
	copy(history, sm.stateHistory)
	return history
}

// GetCurrentTransaction 获取当前交易信息
func (sm *NozzleStateMachine) GetCurrentTransaction() *NozzleTransaction {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	return &NozzleTransaction{
		NozzleNumber: sm.nozzleNumber,
		Grade:        sm.currentGrade,
		Price:        sm.currentPrice,
		Volume:       sm.currentVolume,
		Amount:       sm.currentAmount,
		StartTime:    time.Now(), // 实际应该记录开始时间
		Status:       sm.getStatusString(),
	}
}

// GetTotals 获取累计数据
func (sm *NozzleStateMachine) GetTotals() *NozzleTotals {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	return &NozzleTotals{
		NozzleNumber: sm.nozzleNumber,
		TotalVolume:  sm.totalVolume,
		TotalAmount:  sm.totalAmount,
		FillCount:    sm.fillCount,
		LastUpdate:   time.Now(),
	}
}

// 私有方法

// getNextState 获取下一个状态
func (sm *NozzleStateMachine) getNextState(current NozzleStatus, event NozzleEvent) (NozzleStatus, error) {
	// 定义状态转换表
	transitions := map[NozzleStatus]map[NozzleEvent]NozzleStatus{
		NozzleStatusUnknown: {
			NozzleEventReset: NozzleStatusIn,
		},
		NozzleStatusIn: {
			NozzleEventOut:         NozzleStatusOut,
			NozzleEventMaintenance: NozzleStatusMaintenance,
			NozzleEventError:       NozzleStatusError,
		},
		NozzleStatusOut: {
			NozzleEventIn:        NozzleStatusIn,
			NozzleEventStartFlow: NozzleStatusDispensing,
			NozzleEventError:     NozzleStatusError,
		},
		NozzleStatusDispensing: {
			NozzleEventStopFlow:  NozzleStatusOut,
			NozzleEventComplete:  NozzleStatusCompleted,
			NozzleEventIn:        NozzleStatusCompleted, // 油枪插入自动完成
			NozzleEventError:     NozzleStatusError,
		},
		NozzleStatusCompleted: {
			NozzleEventIn:    NozzleStatusIn,
			NozzleEventOut:   NozzleStatusOut,
			NozzleEventReset: NozzleStatusIn,
			NozzleEventError: NozzleStatusError,
		},
		NozzleStatusError: {
			NozzleEventRecover: NozzleStatusIn,
			NozzleEventReset:   NozzleStatusIn,
		},
		NozzleStatusMaintenance: {
			NozzleEventRecover: NozzleStatusIn,
			NozzleEventReset:   NozzleStatusIn,
		},
	}
	
	if validTransitions, exists := transitions[current]; exists {
		if nextStatus, valid := validTransitions[event]; valid {
			return nextStatus, nil
		}
	}
	
	return current, fmt.Errorf("invalid state transition from %s with event %s", 
		sm.statusToString(current), string(event))
}

// recordStateTransition 记录状态转换
func (sm *NozzleStateMachine) recordStateTransition(from, to NozzleStatus, event NozzleEvent, trigger string) {
	transition := NozzleStateTransition{
		FromStatus: from,
		ToStatus:   to,
		Event:      event,
		Timestamp:  time.Now(),
		Trigger:    trigger,
	}
	
	sm.stateHistory = append(sm.stateHistory, transition)
	
	// 限制历史记录大小
	if len(sm.stateHistory) > sm.maxHistorySize {
		sm.stateHistory = sm.stateHistory[len(sm.stateHistory)-sm.maxHistorySize:]
	}
}

// resetInternalState 重置内部状态
func (sm *NozzleStateMachine) resetInternalState() {
	sm.currentVolume = 0.0
	sm.currentAmount = 0.0
	sm.isSelected = false
	sm.currentGrade = 0
	sm.isFlowing = false
}

// statusToString 状态转字符串
func (sm *NozzleStateMachine) statusToString(status NozzleStatus) string {
	switch status {
	case NozzleStatusUnknown:
		return "UNKNOWN"
	case NozzleStatusIn:
		return "IN"
	case NozzleStatusOut:
		return "OUT"
	case NozzleStatusDispensing:
		return "DISPENSING"
	case NozzleStatusCompleted:
		return "COMPLETED"
	case NozzleStatusError:
		return "ERROR"
	case NozzleStatusMaintenance:
		return "MAINTENANCE"
	default:
		return fmt.Sprintf("UNKNOWN(%d)", status)
	}
}

// getStatusString 获取当前状态字符串
func (sm *NozzleStateMachine) getStatusString() string {
	return sm.statusToString(sm.currentStatus)
}