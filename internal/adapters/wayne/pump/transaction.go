package pump

import (
	"fmt"
	"time"
)

// baseTransaction provides common functionality for all transaction types
type baseTransaction struct {
	transactionType TransactionType
	length          byte
	data            []byte
	timestamp       time.Time
}

// GetType returns the transaction type
func (t *baseTransaction) GetType() TransactionType {
	return t.transactionType
}

// GetLength returns the length of transaction data
func (t *baseTransaction) GetLength() byte {
	return t.length
}

// GetData returns the transaction data bytes
func (t *baseTransaction) GetData() []byte {
	return t.data
}

// Validate validates the transaction data
func (t *baseTransaction) Validate() error {
	if t.length != byte(len(t.data)) {
		return &ValidationError{
			Field:   "length",
			Message: fmt.Sprintf("length field (%d) doesn't match data length (%d)", t.length, len(t.data)),
			Value:   t.length,
		}
	}
	return nil
}

// cdTransaction implements CDTransaction interface
type cdTransaction struct {
	baseTransaction
	commandCode byte
}

// GetCommandCode returns the command code for CD transactions
func (t *cdTransaction) GetCommandCode() byte {
	return t.commandCode
}

// Encode encodes the CD transaction to bytes for transmission
func (t *cdTransaction) Encode() []byte {
	result := []byte{byte(t.transactionType), t.length}
	result = append(result, t.data...)
	return result
}

// Decode decodes bytes into CD transaction data
func (t *cdTransaction) Decode(data []byte) error {
	if len(data) < 2 {
		return &ValidationError{
			Field:   "data",
			Message: "CD transaction requires at least 2 bytes (TRANS + LNG)",
			Value:   len(data),
		}
	}

	t.transactionType = TransactionType(data[0])
	t.length = data[1]

	if len(data) < int(2+t.length) {
		return &ValidationError{
			Field:   "data",
			Message: fmt.Sprintf("insufficient data: expected %d bytes, got %d", 2+t.length, len(data)),
			Value:   len(data),
		}
	}

	t.data = make([]byte, t.length)
	copy(t.data, data[2:2+t.length])

	// For CD1 transactions, extract command code
	if t.transactionType == TransactionTypeCD1 && len(t.data) > 0 {
		t.commandCode = t.data[0]
	}

	return t.Validate()
}

// dcTransaction implements DCTransaction interface
type dcTransaction struct {
	baseTransaction
	statusCode byte
}

// GetStatusCode returns the status code for DC transactions
func (t *dcTransaction) GetStatusCode() byte {
	return t.statusCode
}

// GetTimestamp returns when this transaction was created/received
func (t *dcTransaction) GetTimestamp() time.Time {
	return t.timestamp
}

// Encode encodes the DC transaction to bytes for transmission
func (t *dcTransaction) Encode() []byte {
	result := []byte{byte(t.transactionType), t.length}
	result = append(result, t.data...)
	return result
}

// Decode decodes bytes into DC transaction data
func (t *dcTransaction) Decode(data []byte) error {
	if len(data) < 2 {
		return &ValidationError{
			Field:   "data",
			Message: "DC transaction requires at least 2 bytes (TRANS + LNG)",
			Value:   len(data),
		}
	}

	t.transactionType = TransactionType(data[0])
	t.length = data[1]

	if len(data) < int(2+t.length) {
		return &ValidationError{
			Field:   "data",
			Message: fmt.Sprintf("insufficient data: expected %d bytes, got %d", 2+t.length, len(data)),
			Value:   len(data),
		}
	}

	t.data = make([]byte, t.length)
	copy(t.data, data[2:2+t.length])
	t.timestamp = time.Now()

	// For DC1 transactions, extract status code
	if t.transactionType == TransactionTypeDC1 && len(t.data) > 0 {
		t.statusCode = t.data[0]
	}

	return t.Validate()
}

// transactionBuilder implements TransactionBuilder interface
type transactionBuilder struct {
	bcdConverter BCDConverter
}

// NewTransactionBuilder creates a new transaction builder instance
func NewTransactionBuilder() TransactionBuilder {
	return &transactionBuilder{
		bcdConverter: NewBCDConverter(),
	}
}

// BuildCD1 creates a CD1 (Command to pump) transaction
func (b *transactionBuilder) BuildCD1(command PumpCommand) (CDTransaction, error) {
	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD1,
			length:          1, // Command code is 1 byte
			data:            []byte{byte(command)},
		},
		commandCode: byte(command),
	}

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD2 creates a CD2 (Allowed nozzle numbers) transaction
func (b *transactionBuilder) BuildCD2(nozzleNumbers []byte) (CDTransaction, error) {
	if len(nozzleNumbers) == 0 {
		return nil, &ValidationError{
			Field:   "nozzleNumbers",
			Message: "at least one nozzle number is required",
			Value:   len(nozzleNumbers),
		}
	}

	if len(nozzleNumbers) > 8 {
		return nil, &ValidationError{
			Field:   "nozzleNumbers",
			Message: "maximum 8 nozzles supported",
			Value:   len(nozzleNumbers),
		}
	}

	// Validate nozzle numbers (1-15, 0x01-0x0F)
	for i, nozzle := range nozzleNumbers {
		if nozzle < 1 || nozzle > 15 {
			return nil, &ValidationError{
				Field:   fmt.Sprintf("nozzleNumbers[%d]", i),
				Message: "nozzle number must be between 1 and 15",
				Value:   nozzle,
			}
		}
	}

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD2,
			length:          byte(len(nozzleNumbers)),
			data:            make([]byte, len(nozzleNumbers)),
		},
	}

	copy(transaction.data, nozzleNumbers)

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD3 creates a CD3 (Preset volume) transaction
func (b *transactionBuilder) BuildCD3(volume []byte) (CDTransaction, error) {
	if len(volume) != 4 {
		return nil, &ValidationError{
			Field:   "volume",
			Message: "volume must be 4 bytes in BCD format",
			Value:   len(volume),
		}
	}

	// Validate BCD format
	if err := ValidateBCD(volume); err != nil {
		return nil, &TransactionError{
			Type:    TransactionTypeCD3,
			Message: "invalid BCD format in volume",
			Cause:   err,
		}
	}

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD3,
			length:          4, // Volume is always 4 bytes
			data:            make([]byte, 4),
		},
	}

	copy(transaction.data, volume)

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD4 creates a CD4 (Preset amount) transaction
func (b *transactionBuilder) BuildCD4(amount []byte) (CDTransaction, error) {
	if len(amount) != 4 {
		return nil, &ValidationError{
			Field:   "amount",
			Message: "amount must be 4 bytes in BCD format",
			Value:   len(amount),
		}
	}

	// Validate BCD format
	if err := ValidateBCD(amount); err != nil {
		return nil, &TransactionError{
			Type:    TransactionTypeCD4,
			Message: "invalid BCD format in amount",
			Cause:   err,
		}
	}

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD4,
			length:          4, // Amount is always 4 bytes
			data:            make([]byte, 4),
		},
	}

	copy(transaction.data, amount)

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD5 creates a CD5 (Price update) transaction
func (b *transactionBuilder) BuildCD5(prices [][]byte) (CDTransaction, error) {
	if len(prices) == 0 {
		return nil, &ValidationError{
			Field:   "prices",
			Message: "at least one price is required",
			Value:   len(prices),
		}
	}

	if len(prices) > 8 {
		return nil, &ValidationError{
			Field:   "prices",
			Message: "maximum 8 prices supported",
			Value:   len(prices),
		}
	}

	// Validate each price is 3 bytes and in BCD format
	totalDataLen := 0
	for i, price := range prices {
		if len(price) != 3 {
			return nil, &ValidationError{
				Field:   fmt.Sprintf("prices[%d]", i),
				Message: "each price must be 3 bytes in BCD format",
				Value:   len(price),
			}
		}

		if err := ValidateBCD(price); err != nil {
			return nil, &TransactionError{
				Type:    TransactionTypeCD5,
				Message: fmt.Sprintf("invalid BCD format in price[%d]", i),
				Cause:   err,
			}
		}

		totalDataLen += 3
	}

	// 添加调试日志

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD5,
			length:          byte(totalDataLen), // 确认：长度 = 价格数量 × 3
			data:            make([]byte, 0, totalDataLen),
			timestamp:       time.Now(),
		},
	}

	// Concatenate all prices
	for _, price := range prices {
		transaction.data = append(transaction.data, price...)
		// 添加调试日志
	}

	// 最终验证
	if len(transaction.data) != totalDataLen {
		return nil, fmt.Errorf("CD5 data length mismatch: expected %d, got %d", totalDataLen, len(transaction.data))
	}

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD14 creates a CD14 (Suspend request) transaction
func (b *transactionBuilder) BuildCD14(nozzleNumber byte) (CDTransaction, error) {
	if nozzleNumber > 15 {
		return nil, &ValidationError{
			Field:   "nozzleNumber",
			Message: "nozzle number must be between 0 and 15",
			Value:   nozzleNumber,
		}
	}

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD14,
			length:          1, // Nozzle number is 1 byte
			data:            []byte{nozzleNumber},
		},
	}

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD15 creates a CD15 (Resume request) transaction
func (b *transactionBuilder) BuildCD15(nozzleNumber byte) (CDTransaction, error) {
	if nozzleNumber > 15 {
		return nil, &ValidationError{
			Field:   "nozzleNumber",
			Message: "nozzle number must be between 0 and 15",
			Value:   nozzleNumber,
		}
	}

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD15,
			length:          1, // Nozzle number is 1 byte
			data:            []byte{nozzleNumber},
		},
	}

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// BuildCD101 creates a CD101 (Request total counters) transaction
func (b *transactionBuilder) BuildCD101(counter byte) (CDTransaction, error) {
	// Validate counter type according to Wayne protocol
	validCounters := []byte{
		0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, // Volume counters
		0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, // Amount counters
	}

	isValid := false
	for _, valid := range validCounters {
		if counter == valid {
			isValid = true
			break
		}
	}

	if !isValid {
		return nil, &ValidationError{
			Field:   "counter",
			Message: "invalid counter type",
			Value:   counter,
		}
	}

	transaction := &cdTransaction{
		baseTransaction: baseTransaction{
			transactionType: TransactionTypeCD101,
			length:          1, // Counter type is 1 byte
			data:            []byte{counter},
		},
	}

	if err := transaction.Validate(); err != nil {
		return nil, err
	}

	return transaction, nil
}

// ParseDC1 parses a DC1 (Pump status) transaction
func (b *transactionBuilder) ParseDC1(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC1 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC1 transaction",
			Value:   transaction.transactionType,
		}
	}

	if transaction.length != 1 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC1 transaction must have 1 byte of data",
			Value:   transaction.length,
		}
	}

	return transaction, nil
}

// ParseDC2 parses a DC2 (Filled volume and amount) transaction
func (b *transactionBuilder) ParseDC2(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC2 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC2 transaction",
			Value:   transaction.transactionType,
		}
	}

	if transaction.length != 8 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC2 transaction must have 8 bytes of data (4 for volume + 4 for amount)",
			Value:   transaction.length,
		}
	}

	// Validate BCD format for volume and amount
	volumeData := transaction.data[0:4]
	amountData := transaction.data[4:8]

	if err := ValidateBCD(volumeData); err != nil {
		return nil, &TransactionError{
			Type:    TransactionTypeDC2,
			Message: "invalid BCD format in volume data",
			Cause:   err,
		}
	}

	if err := ValidateBCD(amountData); err != nil {
		return nil, &TransactionError{
			Type:    TransactionTypeDC2,
			Message: "invalid BCD format in amount data",
			Cause:   err,
		}
	}

	return transaction, nil
}

// ParseDC3 parses a DC3 (Nozzle status and filling price) transaction
func (b *transactionBuilder) ParseDC3(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC3 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC3 transaction",
			Value:   transaction.transactionType,
		}
	}

	if transaction.length != 4 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC3 transaction must have 4 bytes of data (3 for price + 1 for NOZIO)",
			Value:   transaction.length,
		}
	}

	// Validate BCD format for price (first 3 bytes)
	priceData := transaction.data[0:3]
	if err := ValidateBCD(priceData); err != nil {
		return nil, &TransactionError{
			Type:    TransactionTypeDC3,
			Message: "invalid BCD format in price data",
			Cause:   err,
		}
	}

	// Validate NOZIO byte (nozzle number should be 0-15)
	nozio := transaction.data[3]
	nozzleNumber := nozio & 0x0F
	if nozzleNumber > 15 {
		return nil, &ValidationError{
			Field:   "nozzleNumber",
			Message: "invalid nozzle number in NOZIO",
			Value:   nozzleNumber,
		}
	}

	return transaction, nil
}

// ParseDC5 parses a DC5 (Alarm code) transaction
func (b *transactionBuilder) ParseDC5(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC5 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC5 transaction",
			Value:   transaction.transactionType,
		}
	}

	if transaction.length != 1 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC5 transaction must have 1 byte of data",
			Value:   transaction.length,
		}
	}

	return transaction, nil
}

// ParseDC7 parses a DC7 (Pump parameters) transaction
func (b *transactionBuilder) ParseDC7(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC7 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC7 transaction",
			Value:   transaction.transactionType,
		}
	}

	// DC7 has variable length but specific structure according to Wayne protocol
	// We'll accept it if the basic structure is valid
	if transaction.length < 33 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC7 transaction must have at least 33 bytes of data",
			Value:   transaction.length,
		}
	}

	return transaction, nil
}

// ParseDC14 parses a DC14 (Suspend reply) transaction
func (b *transactionBuilder) ParseDC14(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC14 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC14 transaction",
			Value:   transaction.transactionType,
		}
	}

	if transaction.length != 1 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC14 transaction must have 1 byte of data",
			Value:   transaction.length,
		}
	}

	return transaction, nil
}

// ParseDC15 parses a DC15 (Resume reply) transaction
func (b *transactionBuilder) ParseDC15(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC15 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC15 transaction",
			Value:   transaction.transactionType,
		}
	}

	if transaction.length != 1 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC15 transaction must have 1 byte of data",
			Value:   transaction.length,
		}
	}

	return transaction, nil
}

// ParseDC101 parses a DC101 (Total counters) transaction
func (b *transactionBuilder) ParseDC101(data []byte) (DCTransaction, error) {
	transaction := &dcTransaction{}

	if err := transaction.Decode(data); err != nil {
		return nil, err
	}

	if transaction.transactionType != TransactionTypeDC101 {
		return nil, &ValidationError{
			Field:   "transactionType",
			Message: "expected DC101 transaction",
			Value:   transaction.transactionType,
		}
	}

	// DC101 has variable length depending on counter type
	// Minimum is 6 bytes (1 byte COUN + 5 bytes TOTVAL)
	if transaction.length < 6 {
		return nil, &ValidationError{
			Field:   "length",
			Message: "DC101 transaction must have at least 6 bytes of data",
			Value:   transaction.length,
		}
	}

	return transaction, nil
}
