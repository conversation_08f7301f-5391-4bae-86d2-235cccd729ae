package pump

import (
	"fmt"
	"sync"
	"time"
)

// StateTransition is already defined in interfaces.go

// StateTransitionError represents an invalid state transition error
type StateTransitionError struct {
	FromStatus PumpStatus
	ToStatus   PumpStatus
	Command    PumpCommand
	Message    string
}

func (e *StateTransitionError) Error() string {
	return fmt.Sprintf("invalid state transition from %d to %d via command %d: %s",
		e.FromStatus, e.ToStatus, e.Command, e.Message)
}

// PumpStateMachine manages pump state according to Wayne DART protocol
type PumpStateMachine struct {
	// Current state
	currentStatus PumpStatus
	suspended     bool

	// State history
	stateHistory []StateTransition

	// Preset limits
	presetVolume int64 // In BCD format units
	presetAmount int64 // In BCD format units

	// Current values
	currentVolume int64
	currentAmount int64

	// Nozzle state
	selectedNozzle byte
	nozzleOut      bool

	// Price information
	pricesReceived bool // Track if prices have been received

	// Transaction processing
	transactionBuilder TransactionBuilder

	// Concurrency control
	mutex sync.RWMutex
}

// PumpStateMachineInterface is already defined in interfaces.go

// NewPumpStateMachine creates a new pump state machine instance
func NewPumpStateMachine() PumpStateMachineInterface {
	sm := &PumpStateMachine{
		currentStatus:      StatusPumpNotProgrammed,
		suspended:          false,
		stateHistory:       make([]StateTransition, 0),
		transactionBuilder: NewTransactionBuilder(),
	}

	// Record initial state
	sm.recordStateTransition(StatusPumpNotProgrammed, StatusPumpNotProgrammed, "initialization")

	return sm
}

// GetCurrentStatus returns the current pump status
func (sm *PumpStateMachine) GetCurrentStatus() PumpStatus {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	if sm.suspended {
		return StatusSuspended
	}
	return sm.currentStatus
}

// SetState sets the pump state directly (for testing and initialization)
func (sm *PumpStateMachine) SetState(status PumpStatus) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	oldStatus := sm.currentStatus
	sm.currentStatus = status
	sm.recordStateTransition(oldStatus, status, "direct_set")

	return nil
}

// IsSuspended returns whether the pump is currently suspended
func (sm *PumpStateMachine) IsSuspended() bool {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.suspended
}

// GetStateHistory returns the state transition history
func (sm *PumpStateMachine) GetStateHistory() []StateTransition {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	// Return a copy to prevent external modification
	history := make([]StateTransition, len(sm.stateHistory))
	copy(history, sm.stateHistory)
	return history
}

// ProcessCommand processes a pump command and updates state accordingly
func (sm *PumpStateMachine) ProcessCommand(command PumpCommand) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	oldStatus := sm.currentStatus
	newStatus, err := sm.getNextState(sm.currentStatus, command)
	if err != nil {
		return err
	}

	// Handle special commands
	switch command {
	case CmdSuspendFuellingPoint:
		if sm.currentStatus == StatusAuthorized || sm.currentStatus == StatusFilling {
			sm.suspended = true
			sm.recordStateTransition(oldStatus, sm.currentStatus, fmt.Sprintf("suspend_command_%d", command))
			return nil
		}
		return &StateTransitionError{
			FromStatus: sm.currentStatus,
			ToStatus:   sm.currentStatus,
			Command:    command,
			Message:    "suspend only allowed in AUTHORIZED or FILLING state",
		}

	case CmdResumeFuellingPoint:
		if sm.suspended {
			sm.suspended = false
			sm.recordStateTransition(oldStatus, sm.currentStatus, fmt.Sprintf("resume_command_%d", command))
			return nil
		}
		return &StateTransitionError{
			FromStatus: sm.currentStatus,
			ToStatus:   sm.currentStatus,
			Command:    command,
			Message:    "resume only allowed when suspended",
		}

	case CmdReset:
		// Reset clears volume, amount, and preset values
		sm.currentVolume = 0
		sm.currentAmount = 0
		sm.presetVolume = 0
		sm.presetAmount = 0
		sm.selectedNozzle = 0
		sm.nozzleOut = false
		sm.suspended = false
	}

	// Update state
	sm.currentStatus = newStatus
	sm.recordStateTransition(oldStatus, newStatus, fmt.Sprintf("command_%d", command))

	return nil
}

// ProcessCDTransaction processes a CD (Controller to Device) transaction
func (sm *PumpStateMachine) ProcessCDTransaction(transaction CDTransaction) (DCTransaction, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// Extract command from CD1 transaction
	if transaction.GetType() == TransactionTypeCD1 {
		data := transaction.GetData()
		if len(data) > 0 {
			command := PumpCommand(data[0])

			// Process the command
			err := sm.processCommandInternal(command)
			if err != nil {
				return nil, err
			}

			// Create DC1 response with current status
			return sm.transactionBuilder.ParseDC1([]byte{
				byte(TransactionTypeDC1), // TRANS
				1,                        // LNG
				byte(sm.currentStatus),   // STATUS
			})
		}
	}

	// For other transaction types, just acknowledge
	return sm.createStatusResponse()
}

// ProcessDCTransaction processes a DC (Device to Controller) transaction
func (sm *PumpStateMachine) ProcessDCTransaction(transaction DCTransaction) error {
	// DC transactions are typically from device to controller
	// In our case, we might use this for simulation or testing
	return nil
}

// ProcessNozzleEvent processes nozzle in/out events
func (sm *PumpStateMachine) ProcessNozzleEvent(nozzleOut bool, nozzleNumber byte) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// Validate nozzle number
	if nozzleNumber > 15 {
		return &ValidationError{
			Field:   "nozzleNumber",
			Message: "nozzle number must be between 0 and 15",
			Value:   nozzleNumber,
		}
	}

	sm.selectedNozzle = nozzleNumber
	sm.nozzleOut = nozzleOut

	oldStatus := sm.currentStatus

	// State transitions based on nozzle events
	if nozzleOut && sm.currentStatus == StatusAuthorized {
		// Nozzle out in authorized state -> start filling
		sm.currentStatus = StatusFilling
		sm.recordStateTransition(oldStatus, StatusFilling, "nozzle_out")
	} else if !nozzleOut && sm.currentStatus == StatusFilling {
		// Nozzle in during filling -> filling completed
		sm.currentStatus = StatusFillingCompleted
		sm.recordStateTransition(oldStatus, StatusFillingCompleted, "nozzle_in")
	}

	return nil
}

// ProcessVolumeUpdate processes volume updates and checks preset limits
func (sm *PumpStateMachine) ProcessVolumeUpdate(volume int64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.currentVolume = volume

	// Check preset volume limit
	if sm.presetVolume > 0 && volume >= sm.presetVolume && sm.currentStatus == StatusFilling {
		oldStatus := sm.currentStatus
		sm.currentStatus = StatusMaxAmountReached
		sm.recordStateTransition(oldStatus, StatusMaxAmountReached, "preset_volume_reached")
	}

	return nil
}

// ProcessAmountUpdate processes amount updates and checks preset limits
func (sm *PumpStateMachine) ProcessAmountUpdate(amount int64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.currentAmount = amount

	// Check preset amount limit
	if sm.presetAmount > 0 && amount >= sm.presetAmount && sm.currentStatus == StatusFilling {
		oldStatus := sm.currentStatus
		sm.currentStatus = StatusMaxAmountReached
		sm.recordStateTransition(oldStatus, StatusMaxAmountReached, "preset_amount_reached")
	}

	return nil
}

// ProcessAlarm processes alarm codes and updates state accordingly
func (sm *PumpStateMachine) ProcessAlarm(alarmCode byte) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	oldStatus := sm.currentStatus

	// Critical alarms that require pump shutdown
	criticalAlarms := []byte{
		0x01, // CPU reset
		0x03, // RAM error
		0x04, // PROM checksum error
		0x06, // Pulser error
		0x07, // Pulser current error
		0x09, // Emergency stop
		0x0A, // Power failure
		0x0B, // Pressure lost
		0x0C, // Blend ratio error
		0x0D, // Low leak error
		0x0E, // High leak error
		0x0F, // Hose leak error
		0x12, // VR monitor, shut down pump
		0x13, // VR monitor, internal error
		0x19, // VR control error
		0x20, // VR control error consecutive
	}

	// Check if this is a critical alarm
	for _, critical := range criticalAlarms {
		if alarmCode == critical {
			sm.currentStatus = StatusSwitchedOff
			sm.recordStateTransition(oldStatus, StatusSwitchedOff, fmt.Sprintf("critical_alarm_0x%02X", alarmCode))
			return nil
		}
	}

	// Non-critical alarms (0x14, 0x15, etc.) don't change state
	sm.recordStateTransition(oldStatus, sm.currentStatus, fmt.Sprintf("non_critical_alarm_0x%02X", alarmCode))

	return nil
}

// SetPresetVolume sets the preset volume limit
func (sm *PumpStateMachine) SetPresetVolume(volume int64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.presetVolume = volume
	return nil
}

// SetPresetAmount sets the preset amount limit
func (sm *PumpStateMachine) SetPresetAmount(amount int64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.presetAmount = amount
	return nil
}

// SetPricesReceived sets whether prices have been received
func (sm *PumpStateMachine) SetPricesReceived(received bool) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	oldStatus := sm.currentStatus
	sm.pricesReceived = received

	// ✅ 按Wayne DART协议：接收价格后从PUMP NOT PROGRAMMED转到可操作状态
	if received && sm.currentStatus == StatusPumpNotProgrammed {
		// 📖 协议：接收价格配置后，设备可以进入RESET状态，准备接受业务命令
		sm.currentStatus = StatusReset
		sm.recordStateTransition(oldStatus, StatusReset, "prices_received")
	}
}

// Private helper methods

// getNextState determines the next state based on current state and command
func (sm *PumpStateMachine) getNextState(currentStatus PumpStatus, command PumpCommand) (PumpStatus, error) {
	// ✅ 按Wayne DART协议处理Reset命令
	if command == CmdReset {
		// 📖 协议：RESET命令只能在特定状态下执行
		switch currentStatus {
		case StatusFillingCompleted, StatusMaxAmountReached:
			// 📖 协议允许的reset状态
			return StatusReset, nil
		case StatusSwitchedOff:
			return currentStatus, &StateTransitionError{
				FromStatus: currentStatus,
				ToStatus:   currentStatus,
				Command:    command,
				Message:    "no transitions allowed from switched off state",
			}
		case StatusPumpNotProgrammed:
			return currentStatus, &StateTransitionError{
				FromStatus: currentStatus,
				ToStatus:   currentStatus,
				Command:    command,
				Message:    "reset not allowed from pump not programmed state per Wayne DART protocol",
			}
		default:
			return currentStatus, &StateTransitionError{
				FromStatus: currentStatus,
				ToStatus:   currentStatus,
				Command:    command,
				Message:    "reset only allowed from FILLING COMPLETED or MAX AMOUNT/VOLUME REACHED state per Wayne DART protocol",
			}
		}
	}

	// ✅ 严格按照Wayne DART协议规范定义状态转换
	transitions := map[PumpStatus]map[PumpCommand]PumpStatus{
		StatusPumpNotProgrammed: {
			// 📖 协议：PUMP NOT PROGRAMMED状态主要等待价格配置，不执行业务命令
			// 只允许状态查询命令，不允许业务状态转换
		},
		StatusReset: {
			// 📖 协议：AUTHORIZE命令只能在RESET状态下执行 (RESET状态对应数据库idle)
			CmdAuthorize: StatusAuthorized, // 修正：授权后状态为0x02
			CmdStop:      StatusFillingCompleted, // 📖 协议：STOP可以在RESET状态执行
			CmdSwitchOff: StatusSwitchedOff,
		},
		StatusAuthorized: { // 修正：处理0x02授权状态
			CmdStop:      StatusFillingCompleted, // 📖 协议：STOP可以在AUTHORIZED状态执行
			CmdSwitchOff: StatusSwitchedOff,
			// 注意：FILLING状态通常由喷嘴事件触发，不是命令触发
		},
		StatusFilling: {
			CmdStop:      StatusFillingCompleted, // 📖 协议：STOP可以在FILLING状态执行
			CmdSwitchOff: StatusSwitchedOff,
		},
		StatusFillingCompleted: {
			// 📖 协议：RESET命令只能在FILLING COMPLETED状态下执行
			CmdReset:     StatusReset,
			CmdSwitchOff: StatusSwitchedOff,
		},
		StatusMaxAmountReached: {
			// 📖 协议：RESET命令只能在MAX AMOUNT/VOLUME REACHED状态下执行
			CmdReset:     StatusReset,
			CmdStop:      StatusFillingCompleted, // 📖 协议：STOP可以在MAX AMOUNT状态执行
			CmdSwitchOff: StatusSwitchedOff,
		},
		StatusSwitchedOff: {
			// 📖 协议：SWITCH OFF是终止状态，不允许任何转换
		},
	}

	// Check if transition is valid
	if validTransitions, exists := transitions[currentStatus]; exists {
		if nextStatus, valid := validTransitions[command]; valid {
			return nextStatus, nil
		}
	}

	// Handle suspend/resume commands (special handling)
	if command == CmdSuspendFuellingPoint || command == CmdResumeFuellingPoint {
		return currentStatus, nil // These are handled specially in ProcessCommand
	}

	// Handle status query commands (don't change state)
	statusQueryCommands := []PumpCommand{
		CmdReturnStatus,
		CmdReturnPumpParameters,
		CmdReturnPumpIdentity,
		CmdReturnFillingInformation,
		CmdReturnPricesOfAllGrades,
	}

	for _, queryCmd := range statusQueryCommands {
		if command == queryCmd {
			return currentStatus, nil // No state change
		}
	}

	return currentStatus, &StateTransitionError{
		FromStatus: currentStatus,
		ToStatus:   currentStatus,
		Command:    command,
		Message:    "transition not allowed",
	}
}

// processCommandInternal processes a command without locking (assumes lock is held)
func (sm *PumpStateMachine) processCommandInternal(command PumpCommand) error {
	oldStatus := sm.currentStatus
	newStatus, err := sm.getNextState(sm.currentStatus, command)
	if err != nil {
		return err
	}

	// Handle special commands
	switch command {
	case CmdReset:
		sm.currentVolume = 0
		sm.currentAmount = 0
		sm.presetVolume = 0
		sm.presetAmount = 0
		sm.selectedNozzle = 0
		sm.nozzleOut = false
		sm.suspended = false
	}

	sm.currentStatus = newStatus
	sm.recordStateTransition(oldStatus, newStatus, fmt.Sprintf("command_%d", command))

	return nil
}

// recordStateTransition records a state transition in history
func (sm *PumpStateMachine) recordStateTransition(fromStatus, toStatus PumpStatus, trigger string) {
	transition := StateTransition{
		FromStatus: fromStatus,
		ToStatus:   toStatus,
		Timestamp:  time.Now(),
		Trigger:    trigger,
	}

	sm.stateHistory = append(sm.stateHistory, transition)

	// Limit history size to prevent memory growth
	if len(sm.stateHistory) > 1000 {
		sm.stateHistory = sm.stateHistory[len(sm.stateHistory)-1000:]
	}
}

// createStatusResponse creates a DC1 status response transaction
func (sm *PumpStateMachine) createStatusResponse() (DCTransaction, error) {
	status := sm.currentStatus
	if sm.suspended {
		status = StatusSuspended
	}

	return sm.transactionBuilder.ParseDC1([]byte{
		byte(TransactionTypeDC1), // TRANS
		1,                        // LNG
		byte(status),             // STATUS
	})
}
