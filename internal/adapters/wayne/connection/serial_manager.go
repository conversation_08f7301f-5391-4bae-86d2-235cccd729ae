package connection

import (
	"context"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"go.bug.st/serial"
	"go.uber.org/zap"

	"fcc-service/pkg/errors"
)

// BytePool 字节数组对象池，用于减少频繁的内存分配
var BytePool = sync.Pool{
	New: func() interface{} {
		// 预分配256字节容量，适合大部分DART帧大小
		return make([]byte, 0, 256)
	},
}

// GetPooledBytes 从对象池获取字节数组
func GetPooledBytes(size int) []byte {
	buf := BytePool.Get().([]byte)
	// 重新切片到需要的长度，但保持底层容量
	if cap(buf) < size {
		// 如果容量不够，重新分配一个更大的
		BytePool.Put(buf[:0]) // 归还原来的
		return make([]byte, size)
	}
	return buf[:size]
}

// PutPooledBytes 将字节数组归还到对象池
func PutPooledBytes(buf []byte) {
	if buf == nil {
		return
	}
	// 重置长度为0，但保持容量，供下次使用
	BytePool.Put(buf[:0])
}

// SerialConfig 串口配置
type SerialConfig struct {
	// 基本配置
	Port     string     `json:"port"`     // 串口端口号 (/dev/ttyUSB0, COM1等)
	BaudRate int        `json:"baudrate"` // 波特率 (9600默认, 19200可选)
	DataBits int        `json:"databits"` // 数据位 (DART协议固定8位)
	StopBits int        `json:"stopbits"` // 停止位 (DART协议固定1位)
	Parity   ParityType `json:"parity"`   // 校验位 (DART协议要求奇校验)

	// 超时配置
	Timeout      time.Duration `json:"timeout"`       // 通信超时
	ReadTimeout  time.Duration `json:"read_timeout"`  // 读取超时
	WriteTimeout time.Duration `json:"write_timeout"` // 写入超时

	// 重试配置
	MaxRetries    int           `json:"max_retries"`    // 最大重试次数
	RetryInterval time.Duration `json:"retry_interval"` // 重试间隔

	// 缓冲区配置
	ReadBufferSize  int `json:"read_buffer_size"`  // 读缓冲区大小
	WriteBufferSize int `json:"write_buffer_size"` // 写缓冲区大小

	// 调试配置
	EnableRawDataLogging bool `json:"enable_raw_data_logging"` // 启用原始数据日志记录
}

// ParityType 校验类型枚举
type ParityType string

const (
	ParityNone  ParityType = "none"  // 无校验
	ParityOdd   ParityType = "odd"   // 奇校验
	ParityEven  ParityType = "even"  // 偶校验
	ParityMark  ParityType = "mark"  // 标记校验
	ParitySpace ParityType = "space" // 空格校验
)

// ConnectionStatus 连接状态枚举
type ConnectionStatus string

const (
	ConnectionStatusDisconnected ConnectionStatus = "disconnected" // 已断开
	ConnectionStatusConnecting   ConnectionStatus = "connecting"   // 连接中
	ConnectionStatusConnected    ConnectionStatus = "connected"    // 已连接
	ConnectionStatusError        ConnectionStatus = "error"        // 错误状态
)

// SerialStatistics 串口通信统计信息
type SerialStatistics struct {
	// ✅ 修复：确保所有原子操作字段8字节对齐，并添加明确的对齐注解
	// 在64位系统上，这些字段必须8字节对齐
	_ [0]uint64 // 强制对齐到8字节边界

	BytesSent     int64 `json:"bytes_sent"`     // 发送字节数 - 原子操作
	BytesReceived int64 `json:"bytes_received"` // 接收字节数 - 原子操作
	ErrorCount    int64 `json:"error_count"`    // 错误次数 - 原子操作
	RetryCount    int64 `json:"retry_count"`    // 重试次数 - 原子操作

	// ✅ 新增：对象池统计信息 - 原子操作
	PoolAllocations int64 `json:"pool_allocations"` // 对象池分配次数
	PoolReturns     int64 `json:"pool_returns"`     // 对象池归还次数
	PoolDropped     int64 `json:"pool_dropped"`     // 对象池丢弃次数(队列满时)

	// ✅ 修复：确保非原子字段不干扰对齐
	// 非原子操作字段放在后面
	LastError     string    `json:"last_error"`     // 最后错误信息
	ConnectedAt   time.Time `json:"connected_at"`   // 连接时间
	LastActivity  time.Time `json:"last_activity"`  // 最后活动时间
	ResponseTimes []int64   `json:"response_times"` // 响应时间记录(毫秒)
}

// DataReceivedCallback 数据接收回调函数类型
type DataReceivedCallback func(data []byte)

// SerialManager RS485串口连接管理器
// 只负责原始数据的收发，不关注业务协议
type SerialManager struct {
	// 配置信息
	config SerialConfig

	// 连接对象
	port   serial.Port
	mutex  sync.RWMutex
	status ConnectionStatus

	// 统计信息
	stats    SerialStatistics
	statsMux sync.RWMutex

	// 生命周期控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// 日志器
	logger *zap.Logger

	// 数据接收回调
	dataCallback DataReceivedCallback
	callbackMux  sync.RWMutex

	// 序列化回调队列，保证数据顺序且不阻塞接收
	callbackQueue chan []byte
	callbackWg    sync.WaitGroup

	// 持续监听状态
	isListening bool
	listenMux   sync.RWMutex

	// 日志格式化器
	logFormatter *SerialLogFormatter
}

// NewSerialManager 创建新的串口管理器
func NewSerialManager(config SerialConfig) (*SerialManager, error) {
	// 验证配置参数
	if err := validateSerialConfig(config); err != nil {
		return nil, fmt.Errorf("无效的串口配置: %w", err)
	}

	// 设置默认值
	if config.ReadBufferSize == 0 {
		config.ReadBufferSize = 256
	}
	if config.WriteBufferSize == 0 {
		config.WriteBufferSize = 256
	}
	if config.RetryInterval == 0 {
		config.RetryInterval = 10 * time.Millisecond
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &SerialManager{
		config: config,
		status: ConnectionStatusDisconnected,
		ctx:    ctx,
		cancel: cancel,
		stats: SerialStatistics{
			ResponseTimes: make([]int64, 0, 100),
		},
		logger:        zap.NewNop(),
		isListening:   false,
		logFormatter:  NewSerialLogFormatter(config.Port),
		callbackQueue: make(chan []byte, 256), // 缓冲256个数据包，避免阻塞
	}

	// 启动回调处理goroutine，确保串行处理
	manager.callbackWg.Add(1)
	go manager.callbackProcessor()

	// 启动性能监控goroutine（可选）
	if config.EnableRawDataLogging {
		manager.wg.Add(1)
		go manager.performanceMonitor()
	}

	return manager, nil
}

// validateSerialConfig 验证串口配置
func validateSerialConfig(config SerialConfig) error {
	// 验证端口
	if config.Port == "" {
		return errors.NewValidationError("串口端口不能为空")
	}

	// 验证波特率
	if config.BaudRate != 9600 && config.BaudRate != 19200 {
		return errors.NewValidationError(fmt.Sprintf(
			"不支持的波特率 %d，只支持9600或19200", config.BaudRate))
	}

	// 验证数据位
	if config.DataBits != 8 {
		return errors.NewValidationError(fmt.Sprintf(
			"不支持的数据位 %d，要求8数据位", config.DataBits))
	}

	// 验证停止位
	if config.StopBits != 1 {
		return errors.NewValidationError(fmt.Sprintf(
			"不支持的停止位 %d，要求1停止位", config.StopBits))
	}

	// 验证校验位
	if config.Parity != ParityOdd {
		return errors.NewValidationError(fmt.Sprintf(
			"不支持的校验位 %s，要求奇校验", config.Parity))
	}

	// 验证重试次数
	if config.MaxRetries < 0 || config.MaxRetries > 10 {
		return errors.NewValidationError(fmt.Sprintf(
			"重试次数 %d 超出合理范围[0-10]", config.MaxRetries))
	}

	return nil
}

// Connect 建立串口连接
func (sm *SerialManager) Connect(ctx context.Context) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if sm.status == ConnectionStatusConnected {
		sm.logger.Info("串口已经连接，无需重复连接",
			zap.String("串口", sm.config.Port))
		return nil
	}

	sm.logger.Info("开始连接串口",
		zap.String("串口", sm.config.Port),
		zap.Int("数据位", sm.config.DataBits),
		zap.Int("停止位", sm.config.StopBits),
		zap.String("校验位", string(sm.config.Parity)),
		zap.Int("波特率", sm.config.BaudRate))

	sm.status = ConnectionStatusConnecting

	// 配置串口参数
	mode := &serial.Mode{
		BaudRate: sm.config.BaudRate,
		DataBits: sm.config.DataBits,
		StopBits: serial.OneStopBit,
		Parity:   sm.convertParity(sm.config.Parity),
	}

	// 打开串口
	port, err := serial.Open(sm.config.Port, mode)
	if err != nil {
		sm.status = ConnectionStatusError
		sm.updateErrorStats(fmt.Sprintf("串口打开失败: %v", err))
		sm.logger.Error("串口连接失败",
			zap.String("串口", sm.config.Port),
			zap.Error(err))
		return fmt.Errorf("无法打开串口 %s: %w", sm.config.Port, err)
	}

	sm.port = port
	sm.status = ConnectionStatusConnected

	// 更新统计信息
	sm.statsMux.Lock()
	sm.stats.ConnectedAt = time.Now()
	sm.stats.LastActivity = time.Now()
	sm.statsMux.Unlock()

	sm.logger.Info("串口连接成功",
		zap.String("串口", sm.config.Port),
		zap.String("状态", string(sm.status)))

	// 启动持续监听
	if err := sm.startContinuousListening(); err != nil {
		sm.logger.Error("启动持续监听失败", zap.Error(err))
		return fmt.Errorf("启动持续监听失败: %w", err)
	}

	return nil
}

// Disconnect 断开串口连接
func (sm *SerialManager) Disconnect(ctx context.Context) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if sm.status == ConnectionStatusDisconnected {
		return nil
	}

	sm.logger.Info("开始断开串口连接",
		zap.String("串口", sm.config.Port))

	// 停止持续监听
	sm.stopContinuousListening()

	if sm.port != nil {
		err := sm.port.Close()
		sm.port = nil
		if err != nil {
			sm.updateErrorStats(fmt.Sprintf("串口关闭失败: %v", err))
			sm.logger.Error("串口关闭失败",
				zap.String("串口", sm.config.Port),
				zap.Error(err))
			return fmt.Errorf("串口关闭失败: %w", err)
		}
	}

	sm.status = ConnectionStatusDisconnected
	sm.logger.Info("串口连接已断开", zap.String("串口", sm.config.Port))

	return nil
}

// SetDataReceivedCallback 设置数据接收回调
func (sm *SerialManager) SetDataReceivedCallback(callback DataReceivedCallback) {
	sm.callbackMux.Lock()
	defer sm.callbackMux.Unlock()
	sm.dataCallback = callback
}

// Write 写入数据到串口
func (sm *SerialManager) Write(ctx context.Context, data []byte) (int, error) {
	if !sm.IsConnected() {
		return 0, errors.NewConnectionError("串口未连接")
	}

	sm.mutex.RLock()
	port := sm.port
	sm.mutex.RUnlock()

	if port == nil {
		return 0, errors.NewConnectionError("串口端口为空")
	}

	// 使用优化的日志格式记录发送数据
	// logFields := sm.logFormatter.FormatTxLog(data, sm.config.EnableRawDataLogging)
	// sm.logger.Debug("[SerialManager]串口数据发送", logFields...)

	// writeStartTime := time.Now()
	n, err := port.Write(data)
	// writeDuration := time.Since(writeStartTime) // writeDuration 是为了计算发送时间

	if err != nil {
		sm.updateErrorStats(fmt.Sprintf("写入失败: %v", err))
		sm.logger.Error("串口数据写入失败",
			zap.String("串口", sm.config.Port),
			zap.Error(err))
		return n, fmt.Errorf("串口写入失败: %w", err)
	}

	// 更新统计信息 - 使用安全的原子操作
	sm.safeAddInt64(&sm.stats.BytesSent, int64(n))
	sm.statsMux.Lock()
	sm.stats.LastActivity = time.Now()
	sm.statsMux.Unlock()

	// 发送完成（详细日志已禁用）
	// 记录发送完成日志
	// if sm.config.EnableRawDataLogging {
	// 	sm.logger.Debug("[SerialManager]串口数据发送完成",
	// 		zap.String("direction", "TX✓"),
	// 		zap.String("port", sm.config.Port),
	// 		zap.String("session", sm.logFormatter.sessionID),
	// 		zap.Int("bytes", n),
	// 		zap.Duration("duration", writeDuration),
	// 		zap.String("result", "success"))
	// }

	return n, nil
}

// SendFrame 发送数据帧（兼容性方法）
func (sm *SerialManager) SendFrame(ctx context.Context, frame []byte) error {
	_, err := sm.Write(ctx, frame)
	return err
}

// ReceiveFrame 接收数据帧（兼容性方法，但建议使用回调）
func (sm *SerialManager) ReceiveFrame(ctx context.Context) ([]byte, error) {
	return nil, errors.NewValidationError("请使用SetDataReceivedCallback设置回调函数接收数据")
}

// startContinuousListening 启动持续监听
func (sm *SerialManager) startContinuousListening() error {
	sm.listenMux.Lock()
	defer sm.listenMux.Unlock()

	if sm.isListening {
		return nil
	}

	sm.logger.Info("启动串口持续监听", zap.String("串口", sm.config.Port))

	sm.wg.Add(1)
	go sm.dataListenerRoutine()

	sm.isListening = true
	return nil
}

// stopContinuousListening 停止持续监听
func (sm *SerialManager) stopContinuousListening() {
	sm.listenMux.Lock()
	defer sm.listenMux.Unlock()

	if !sm.isListening {
		return
	}

	sm.logger.Info("停止串口持续监听", zap.String("串口", sm.config.Port))
	sm.isListening = false
}

// dataListenerRoutine 数据监听goroutine
func (sm *SerialManager) dataListenerRoutine() {
	defer sm.wg.Done()

	sm.logger.Info("数据监听goroutine已启动", zap.String("串口", sm.config.Port), zap.Int("读缓冲区大小", sm.config.ReadBufferSize))

	buffer := make([]byte, sm.config.ReadBufferSize)

	for {
		select {
		case <-sm.ctx.Done():
			sm.logger.Info("数据监听goroutine收到停止信号")
			return
		default:
			// 检查监听状态
			sm.listenMux.RLock()
			isListening := sm.isListening
			sm.listenMux.RUnlock()

			if !isListening {
				sm.logger.Info("监听已停止，数据监听goroutine退出")
				return
			}

			if !sm.IsConnected() {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// 获取端口引用
			sm.mutex.RLock()
			port := sm.port
			sm.mutex.RUnlock()

			if port == nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// 执行读取操作（无超时限制，持续监听）
			n, err := port.Read(buffer)

			if err != nil {
				if err == io.EOF {
					continue
				}
				// 串口读取错误（非严重错误不记录详细信息）
				time.Sleep(50 * time.Millisecond)
				continue
			}

			if n > 0 {
				// ✅ 优化：使用对象池减少内存分配
				receivedData := GetPooledBytes(n)
				copy(receivedData, buffer[:n])

				// 更新对象池统计信息
				sm.safeAddInt64(&sm.stats.PoolAllocations, 1)

				// 串口数据接收（详细日志已禁用）

				// 使用优化的日志格式记录接收数据
				// logFields := sm.logFormatter.FormatRxLog(receivedData, sm.config.EnableRawDataLogging)
				// sm.logger.Debug("[SerialManager]串口数据接收", logFields...)

				// 更新统计信息 - 使用安全的原子操作
				sm.safeAddInt64(&sm.stats.BytesReceived, int64(n))
				sm.statsMux.Lock()
				sm.stats.LastActivity = time.Now()
				sm.statsMux.Unlock()

				// 数据接收回调 - 使用队列保证顺序且不阻塞
				select {
				case sm.callbackQueue <- receivedData:
					// ✅ 成功加入队列，不阻塞串口接收
					// 注意：receivedData的归还由接收方(onDataReceived)负责
					sm.safeAddInt64(&sm.stats.PoolReturns, 1) // 预记录归还（实际归还在onDataReceived）
				default:
					// ⚠️ 队列满，立即归还对象池并记录警告
					PutPooledBytes(receivedData)
					sm.safeAddInt64(&sm.stats.PoolDropped, 1) // 记录对象池丢弃次数
					sm.logger.Error("回调队列已满，丢弃数据",
						zap.String("串口", sm.config.Port),
						zap.Int("丢弃字节数", n),
						zap.String("建议", "检查传输层处理速度"))
				}
			}
		}
	}
}

// IsConnected 检查连接状态
func (sm *SerialManager) IsConnected() bool {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.status == ConnectionStatusConnected && sm.port != nil
}

// GetStatus 获取连接状态
func (sm *SerialManager) GetStatus() ConnectionStatus {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.status
}

// GetStatistics 获取通信统计信息
func (sm *SerialManager) GetStatistics() interface{} {
	sm.statsMux.RLock()
	defer sm.statsMux.RUnlock()

	// 深拷贝统计信息
	stats := sm.stats
	stats.ResponseTimes = make([]int64, len(sm.stats.ResponseTimes))
	copy(stats.ResponseTimes, sm.stats.ResponseTimes)

	return stats
}

// callbackProcessor 回调处理goroutine - 确保数据按顺序处理
func (sm *SerialManager) callbackProcessor() {
	defer sm.callbackWg.Done()

	// 回调处理goroutine已启动

	for {
		select {
		case <-sm.ctx.Done():
			// 回调处理goroutine收到停止信号
			sm.logger.Info("回调处理goroutine收到停止信号")
			return
		case data := <-sm.callbackQueue:
			// ✅ 按队列顺序串行处理回调
			sm.callbackMux.RLock()
			callback := sm.dataCallback
			sm.callbackMux.RUnlock()

			if callback != nil {
				callback(data) // 在专用goroutine中调用，不阻塞串口接收
			}
		}
	}
}

// Close 关闭串口管理器
func (sm *SerialManager) Close() error {
	sm.cancel()

	// 等待回调处理完成
	close(sm.callbackQueue)
	sm.callbackWg.Wait()

	sm.wg.Wait()
	return sm.Disconnect(context.Background())
}

// convertParity 转换校验类型
func (sm *SerialManager) convertParity(parity ParityType) serial.Parity {
	switch parity {
	case ParityNone:
		return serial.NoParity
	case ParityOdd:
		return serial.OddParity
	case ParityEven:
		return serial.EvenParity
	case ParityMark:
		return serial.MarkParity
	case ParitySpace:
		return serial.SpaceParity
	default:
		return serial.OddParity
	}
}

// updateErrorStats 更新错误统计信息
func (sm *SerialManager) updateErrorStats(errorMsg string) {
	sm.safeAddInt64(&sm.stats.ErrorCount, 1)
	sm.statsMux.Lock()
	sm.stats.LastError = errorMsg
	sm.statsMux.Unlock()
}

// safeAddInt64 安全的64位原子操作，防止对齐panic
func (sm *SerialManager) safeAddInt64(addr *int64, delta int64) {
	// ✅ 彻底修复：直接使用mutex保护，避免原子操作
	// 这样可以确保在所有平台和对齐情况下都不会panic
	sm.statsMux.Lock()
	*addr += delta
	sm.statsMux.Unlock()
}

// SetLogger 设置日志器
func (sm *SerialManager) SetLogger(logger *zap.Logger) {
	sm.logger = logger
}

// formatASCII 格式化数据为ASCII字符串
func (sm *SerialManager) formatASCII(data []byte) string {
	result := make([]byte, len(data))
	for i, b := range data {
		if b < 32 || b > 126 {
			result[i] = '.'
		} else {
			result[i] = b
		}
	}
	return string(result)
}

// formatBinary 格式化数据为二进制字符串
func (sm *SerialManager) formatBinary(data []byte) string {
	result := make([]byte, len(data)*8)
	for i, b := range data {
		for j := 0; j < 8; j++ {
			result[i*8+j] = '0' + byte((b>>uint(7-j))&1)
		}
	}
	return string(result)
}

// 串口日志格式化工具
type SerialLogFormatter struct {
	port      string
	sessionID string
	txCounter int64
	rxCounter int64
	mutex     sync.Mutex
}

// NewSerialLogFormatter 创建日志格式化器
func NewSerialLogFormatter(port string) *SerialLogFormatter {
	return &SerialLogFormatter{
		port:      port,
		sessionID: fmt.Sprintf("%d", time.Now().Unix()%10000), // 4位会话ID
	}
}

// FormatTxLog 格式化发送日志
func (slf *SerialLogFormatter) FormatTxLog(data []byte, enableDetails bool) []zap.Field {
	slf.mutex.Lock()
	slf.txCounter++
	txSeq := slf.txCounter
	slf.mutex.Unlock()

	timestamp := time.Now().Format("15:04:05.000")

	fields := []zap.Field{
		zap.String("direction", "TX→"),
		zap.String("port", slf.port),
		zap.String("session", slf.sessionID),
		zap.Int64("seq", txSeq),
		zap.Int("bytes", len(data)),
		zap.String("timestamp", timestamp),
	}

	if enableDetails {
		fields = append(fields,
			zap.String("hex", slf.formatHexData(data)),
			zap.String("ascii", slf.formatASCII(data)),
		)

		// 只有当数据较小时才显示二进制
		if len(data) <= 8 {
			fields = append(fields, zap.String("binary", slf.formatBinary(data)))
		}
	}

	return fields
}

// FormatRxLog 格式化接收日志
func (slf *SerialLogFormatter) FormatRxLog(data []byte, enableDetails bool) []zap.Field {
	slf.mutex.Lock()
	slf.rxCounter++
	rxSeq := slf.rxCounter
	slf.mutex.Unlock()

	timestamp := time.Now().Format("15:04:05.000")

	fields := []zap.Field{
		zap.String("direction", "←RX"),
		zap.String("port", slf.port),
		zap.String("session", slf.sessionID),
		zap.Int64("seq", rxSeq),
		zap.Int("bytes", len(data)),
		zap.String("timestamp", timestamp),
	}

	if enableDetails {
		fields = append(fields,
			zap.String("hex", slf.formatHexData(data)),
			zap.String("ascii", slf.formatASCII(data)),
		)

		// 只有当数据较小时才显示二进制
		if len(data) <= 8 {
			fields = append(fields, zap.String("binary", slf.formatBinary(data)))
		}
	}

	return fields
}

// formatHexData 格式化十六进制数据（优化版）
func (slf *SerialLogFormatter) formatHexData(data []byte) string {
	const maxDisplayBytes = 32 // 最多显示32字节

	displayData := data
	truncated := false
	if len(data) > maxDisplayBytes {
		displayData = data[:maxDisplayBytes]
		truncated = true
	}

	// 每4字节一组，用空格分隔
	var result strings.Builder
	for i, b := range displayData {
		if i > 0 && i%4 == 0 {
			result.WriteString(" ")
		}
		result.WriteString(fmt.Sprintf("%02X", b))
	}

	if truncated {
		result.WriteString("...")
		result.WriteString(fmt.Sprintf("[+%d]", len(data)-maxDisplayBytes))
	}

	return result.String()
}

// formatASCII 格式化ASCII数据
func (slf *SerialLogFormatter) formatASCII(data []byte) string {
	const maxDisplayBytes = 32

	displayData := data
	if len(data) > maxDisplayBytes {
		displayData = data[:maxDisplayBytes]
	}

	result := make([]byte, len(displayData))
	for i, b := range displayData {
		if b >= 32 && b <= 126 {
			result[i] = b
		} else {
			result[i] = '.'
		}
	}

	resultStr := string(result)
	if len(data) > maxDisplayBytes {
		resultStr += "..."
	}

	return resultStr
}

// formatBinary 格式化二进制数据（限制长度）
func (slf *SerialLogFormatter) formatBinary(data []byte) string {
	if len(data) > 4 { // 最多显示4字节的二进制
		data = data[:4]
	}

	var result strings.Builder
	for i, b := range data {
		if i > 0 {
			result.WriteString(" ")
		}
		result.WriteString(fmt.Sprintf("%08b", b))
	}

	return result.String()
}

// GetSessionID 获取会话ID
func (slf *SerialLogFormatter) GetSessionID() string {
	return slf.sessionID
}

// performanceMonitor 性能监控goroutine
// 定期输出对象池和串口性能统计信息
func (sm *SerialManager) performanceMonitor() {
	defer sm.wg.Done()

	// 每10秒输出一次统计信息
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-sm.ctx.Done():
			sm.logger.Debug("性能监控停止", zap.String("串口", sm.config.Port))
			return
		case <-ticker.C:
			// 获取统计信息
			sm.statsMux.RLock()
			stats := sm.stats
			sm.statsMux.RUnlock()

			// 输出性能统计
			sm.logger.Info("串口性能统计",
				zap.String("串口", sm.config.Port),
				zap.Int64("发送字节", stats.BytesSent),
				zap.Int64("接收字节", stats.BytesReceived),
				zap.Int64("错误次数", stats.ErrorCount),
				zap.Int64("对象池分配", stats.PoolAllocations),
				zap.Int64("对象池归还", stats.PoolReturns),
				zap.Int64("对象池丢弃", stats.PoolDropped),
				zap.Float64("对象池利用率", func() float64 {
					if stats.PoolAllocations > 0 {
						return float64(stats.PoolReturns) / float64(stats.PoolAllocations)
					}
					return 0.0
				}()),
				zap.String("最后活动", stats.LastActivity.Format("15:04:05")),
			)
		}
	}
}
