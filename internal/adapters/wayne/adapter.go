package wayne

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"fcc-service/internal/adapters/wayne/connection"
	"fcc-service/internal/adapters/wayne/dartline"
	"fcc-service/internal/adapters/wayne/pump"
	"fcc-service/pkg/api"
	"fcc-service/pkg/errors"
	"fcc-service/pkg/models"

	"go.uber.org/zap"
)

// ✅ Phase 2: 结构化命令Key，类型安全且高效
type CommandKey struct {
	TxNum byte // TX#序列号
	Addr  byte // 设备地址
}

// WayneAdapter Wayne DART协议适配器 - 精简版
type WayneAdapter struct {
	// 配置信息
	config api.AdapterConfig

	// 连接管理
	serialManager *connection.SerialManager

	// 设备状态管理
	devices    map[string]*models.Device
	devicesMux sync.RWMutex

	// ✅ Phase 1: 双向TX#管理 - Master和Slave各自独立的TX#序列
	masterTxSequence map[byte]byte // 设备地址 -> Master发送的当前TX#
	slaveTxSequence  map[byte]byte // 设备地址 -> 记录的Slave当前TX#
	sequenceMux      sync.RWMutex  // 改为读写锁，提高并发性能

	// ✅ 半双工模式：临时响应处理器
	temporaryResponseHandler func(*dartline.Frame)
	tempHandlerMux           sync.RWMutex

	// ✅ Phase 2: 数据处理协程控制
	stopDataProcessorChan chan struct{}
	dataProcessorWG       sync.WaitGroup

	// 串口通讯互斥锁 - 确保上位机-下位机应答模式
	serialCommMux sync.Mutex

	// ✅ Phase 3: 使用状态机替代简单状态缓存 - 更符合Wayne DART协议设计
	deviceStates        map[byte]*DeviceState                   // 设备地址 -> 简化状态
	stateMux            sync.RWMutex                            // 状态访问锁
	deviceStateMachines map[byte]pump.PumpStateMachineInterface // 设备地址 -> 状态机
	stateMachinesMux    sync.RWMutex                            // 状态机访问锁

	// 连接状态
	connected bool
	connMux   sync.RWMutex

	// 数据接收缓冲
	receivedDataChan chan []byte
	dataBuffer       []byte // 数据重组缓冲区
	dataBufferMux    sync.Mutex

	logger       *zap.Logger
	controllerID string
}

// ✅ Phase 3: 简化设备状态结构 - 替换复杂的DeviceInitState
type DeviceState struct {
	Address        byte      // 设备地址
	LastSeen       time.Time // 最后通信时间
	IsReady        bool      // 设备是否就绪
	HasBasicConfig bool      // 是否有基本配置
	LastStatus     string    // 最后已知状态
}

// DeviceStatus 设备状态分析结果
type DeviceStatus struct {
	PumpStatus   string                 // 泵状态
	PriceConfig  map[string]interface{} // 价格配置
	NozzleConfig map[string]interface{} // 喷嘴配置
	NeedsReset   bool                   // 是否需要重置
	IsConfigured bool                   // 是否已配置
}

// NewWayneAdapter 创建Wayne适配器实例
func NewWayneAdapter(config api.AdapterConfig) (api.DeviceAdapter, error) {
	if config.Protocol != models.ProtocolTypeWayneDart {
		return nil, errors.NewValidationError("不支持的协议类型: " + string(config.Protocol))
	}

	adapter := &WayneAdapter{
		config:           config,
		devices:          make(map[string]*models.Device),
		masterTxSequence: make(map[byte]byte),
		slaveTxSequence:  make(map[byte]byte),
		// ✅ Phase 2: 初始化数据处理协程控制
		stopDataProcessorChan: make(chan struct{}),
		// ✅ Phase 3: 使用状态机替代简单状态缓存
		deviceStates:        make(map[byte]*DeviceState),
		deviceStateMachines: make(map[byte]pump.PumpStateMachineInterface), // 初始化状态机映射
		receivedDataChan:    make(chan []byte, 10),                         // 缓冲10个完整帧
		dataBuffer:          make([]byte, 0, 512),                          // 数据重组缓冲区
		logger:              zap.NewNop(),
	}

	return adapter, nil
}

// SetLogger 设置日志器
func (w *WayneAdapter) SetLogger(logger *zap.Logger) {
	w.logger = logger
}

// SetControllerID 设置控制器ID
func (w *WayneAdapter) SetControllerID(controllerID string) {
	w.controllerID = controllerID
}

// Connect 连接到Wayne设备
func (w *WayneAdapter) Connect(ctx context.Context, config api.AdapterConfig) error {
	w.connMux.Lock()
	defer w.connMux.Unlock()

	if w.connected {
		return nil // 已连接
	}

	w.logger.Info("开始连接Wayne DART适配器",
		zap.String("串口", config.Connection.SerialPort),
		zap.Int("波特率", config.Connection.BaudRate))

	// 创建串口配置
	serialConfig := connection.SerialConfig{
		Port:            config.Connection.SerialPort,
		BaudRate:        w.getBaudRate(config),
		DataBits:        8,
		StopBits:        1,
		Parity:          connection.ParityOdd,
		Timeout:         25 * time.Millisecond, // DART协议要求
		ReadTimeout:     50 * time.Millisecond,
		WriteTimeout:    50 * time.Millisecond,
		MaxRetries:      w.getMaxRetries(config),
		RetryInterval:   10 * time.Millisecond,
		ReadBufferSize:  256, // DART协议最大缓冲区
		WriteBufferSize: 256,
	}

	// 创建串口管理器
	var err error
	w.serialManager, err = connection.NewSerialManager(serialConfig)
	if err != nil {
		return fmt.Errorf("创建串口管理器失败: %w", err)
	}

	w.serialManager.SetLogger(w.logger)

	// 设置数据接收回调
	w.serialManager.SetDataReceivedCallback(w.onDataReceived)

	// 连接串口
	if err := w.serialManager.Connect(ctx); err != nil {
		return fmt.Errorf("串口连接失败: %w", err)
	}

	w.connected = true
	w.config = config

	// ✅ Phase 2: 启动数据处理协程
	w.startDataProcessor()

	w.logger.Info("Wayne DART适配器连接成功",
		zap.String("串口", config.Connection.SerialPort),
		zap.Int("波特率", serialConfig.BaudRate))

	// ✅ 为已注册的设备启动初始化流程
	w.devicesMux.RLock()
	registeredDevices := make([]*models.Device, 0, len(w.devices))
	for _, device := range w.devices {
		registeredDevices = append(registeredDevices, device)
	}
	w.devicesMux.RUnlock()

	if len(registeredDevices) > 0 {
		w.logger.Info("适配器连接后，为已注册设备启动初始化",
			zap.Int("设备数量", len(registeredDevices)))

		// 异步为每个设备进行初始化
		go func() {
			// 延迟一段时间，确保适配器完全启动
			time.Sleep(200 * time.Millisecond)

			for _, device := range registeredDevices {
				deviceAddr := byte(device.DeviceAddress)
				w.logger.Info("为已注册设备启动初始化",
					zap.String("设备ID", device.ID),
					zap.Uint8("设备地址", deviceAddr))

				w.handleFirstTimeDeviceConnection(deviceAddr)

				// 设备间隔一小段时间，避免冲突
				time.Sleep(100 * time.Millisecond)
			}
		}()
	}

	return nil
}

// Disconnect 断开连接
func (w *WayneAdapter) Disconnect(ctx context.Context) error {
	w.connMux.Lock()
	defer w.connMux.Unlock()

	if !w.connected {
		return nil // 已断开
	}

	w.logger.Info("断开Wayne DART适配器连接")

	// ✅ Phase 2: 停止数据处理协程
	w.stopDataProcessor()

	// 关闭串口连接
	if w.serialManager != nil {
		if err := w.serialManager.Close(); err != nil {
			w.logger.Warn("关闭串口管理器失败", zap.Error(err))
		}
		w.serialManager = nil
	}

	// 清除设备信息
	w.devicesMux.Lock()
	w.devices = make(map[string]*models.Device)
	w.devicesMux.Unlock()

	w.connected = false

	w.logger.Info("Wayne DART适配器已断开连接")
	return nil
}

// IsConnected 检查连接状态
func (w *WayneAdapter) IsConnected() bool {
	w.connMux.RLock()
	defer w.connMux.RUnlock()
	return w.connected && w.serialManager != nil && w.serialManager.IsConnected()
}

// ExecuteCommand 执行设备命令 - 统一命令执行入口
func (w *WayneAdapter) ExecuteCommand(ctx context.Context, deviceID string, command api.Command) (*api.CommandResult, error) {
	if !w.IsConnected() {
		w.logger.Error("Wayne适配器执行命令失败：适配器未连接",
			zap.String("设备ID", deviceID),
			zap.String("命令类型", command.Type))
		return nil, errors.NewConnectionError("适配器未连接")
	}

	w.logger.Debug("开始执行Wayne设备命令",
		zap.String("设备ID", deviceID),
		zap.String("命令类型", command.Type),
		zap.Any("命令参数", command.Parameters),
		zap.Duration("超时时间", command.Timeout))

	// 获取设备信息
	w.devicesMux.RLock()
	device, exists := w.devices[deviceID]
	w.devicesMux.RUnlock()

	if !exists {
		return nil, errors.NewDeviceNotFoundError(deviceID)
	}

	// 新增：检查设备是否正在初始化，如果是则等待或拒绝执行
	deviceAddr := byte(device.DeviceAddress)
	if w.isDeviceInitializing(deviceAddr) {
		// 对于非紧急命令，等待初始化完成
		if w.isNonCriticalCommand(command.Type) {
			w.logger.Info("设备正在初始化，等待初始化完成",
				zap.String("设备ID", deviceID),
				zap.String("命令类型", command.Type),
				zap.Uint8("设备地址", deviceAddr))

			// 等待初始化完成，最多等待5秒
			if err := w.waitForInitializationComplete(deviceAddr, 5*time.Second); err != nil {
				return &api.CommandResult{
					Success:   false,
					Error:     fmt.Sprintf("设备初始化超时，无法执行命令: %v", err),
					Timestamp: time.Now(),
				}, nil
			}
		} else {
			// 对于紧急命令（如reset），可以执行但记录警告
			w.logger.Warn("设备正在初始化但执行紧急命令",
				zap.String("设备ID", deviceID),
				zap.String("命令类型", command.Type),
				zap.Uint8("设备地址", deviceAddr))
		}
	}

	// 根据命令类型直接执行，不再转换为models.Command
	startTime := time.Now()
	var result *api.CommandResult
	var err error

	switch command.Type {
	case "authorize":
		result, err = w.executeAuthorizeCommand(ctx, device, command)
	case "reset":
		result, err = w.executeResetCommand(ctx, device, command)
	case "stop":
		result, err = w.executeStopCommand(ctx, device, command)
	case "status_query":
		result, err = w.executeQueryCommand(ctx, device, command)
	case "pump_status_poll":
		result, err = w.executeStatusQuery(ctx, device, command)
	case "request_total_counters":
		result, err = w.executeRequestTotalCounters(ctx, device, command)
	case "request_filling_info":
		result, err = w.executeRequestFillingInfo(ctx, device, command)
	case "set_price":
		result, err = w.executeSetPrice(ctx, device, command)
	case "preset_volume":
		result, err = w.executePresetVolume(ctx, device, command)
	case "preset_amount":
		result, err = w.executePresetAmount(ctx, device, command)
	case "configure_nozzles":
		result, err = w.executeConfigureNozzles(ctx, device, command)
	default:
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("不支持的命令类型: %s", command.Type),
			Timestamp: time.Now(),
		}, nil
	}

	if err != nil {
		w.logger.Error("Wayne适配器命令执行失败",
			zap.String("设备ID", deviceID),
			zap.String("命令类型", command.Type),
			zap.Error(err))

		return &api.CommandResult{
			Success:   false,
			Error:     err.Error(),
			Timestamp: time.Now(),
		}, nil
	}

	// 添加执行时间
	if result.Data == nil {
		result.Data = make(map[string]interface{})
	}
	result.Data["execution_time_ms"] = time.Since(startTime).Milliseconds()

	w.logger.Info("Wayne适配器命令完成",
		zap.String("设备ID", deviceID),
		zap.String("命令类型", command.Type),
		zap.Bool("成功", result.Success),
		zap.Duration("执行时间", time.Since(startTime)))

	return result, nil
}

// GetDeviceStatus 获取设备状态
func (w *WayneAdapter) GetDeviceStatus(ctx context.Context, deviceID string) (*api.DeviceStatusInfo, error) {
	if !w.IsConnected() {
		return nil, errors.NewConnectionError("适配器未连接")
	}

	// 获取设备信息
	w.devicesMux.RLock()
	device, exists := w.devices[deviceID]
	w.devicesMux.RUnlock()

	if !exists {
		return nil, errors.NewDeviceNotFoundError(deviceID)
	}

	// 发送状态查询命令
	statusCmd := api.Command{
		Type:    "pump_status_poll",
		Timeout: 100 * time.Millisecond, // 适应真实设备响应时间
	}

	result, err := w.ExecuteCommand(ctx, deviceID, statusCmd)
	if err != nil {
		return &api.DeviceStatusInfo{
			DeviceID:   deviceID,
			DeviceType: device.Type,
			Status:     models.DeviceStatusOffline,
			Health:     models.DeviceHealthError,
			LastUpdate: time.Now(),
		}, nil
	}

	// 解析状态信息
	status := models.DeviceStatusOnline
	health := models.DeviceHealthGood
	if !result.Success {
		status = models.DeviceStatusOffline
		health = models.DeviceHealthError
	}

	return &api.DeviceStatusInfo{
		DeviceID:   deviceID,
		DeviceType: device.Type,
		Status:     status,
		Health:     health,
		Properties: result.Data,
		LastUpdate: time.Now(),
		LastSeen:   &result.Timestamp,
	}, nil
}

// DiscoverDevices 发现设备
func (w *WayneAdapter) DiscoverDevices(ctx context.Context) ([]*models.Device, error) {
	if !w.IsConnected() {
		return nil, errors.NewConnectionError("适配器未连接")
	}

	w.logger.Info("开始Wayne设备发现",
		zap.String("串口", w.config.Connection.SerialPort))

	var discoveredDevices []*models.Device

	return discoveredDevices, nil
}

// GetProtocolInfo 获取协议信息
func (w *WayneAdapter) GetProtocolInfo() api.ProtocolInfo {
	return api.ProtocolInfo{
		Name:        "Wayne DART",
		Version:     "v1.3",
		Description: "Wayne DART protocol implementation for FCC service - Optimized",
	}
}

// GetSupportedCommands 获取支持的命令列表
func (w *WayneAdapter) GetSupportedCommands() []string {
	return []string{
		"authorize",              // 授权
		"reset",                  // 重置
		"stop",                   // 停止
		"status_query",           // 状态查询
		"pump_status_poll",       // 泵状态轮询
		"request_total_counters", // 请求总计数器
		"request_filling_info",   // 请求加油信息
		"set_price",              // 设置价格
		"preset_volume",          // 预设体积
		"preset_amount",          // 预设金额
		"configure_nozzles",      // 配置喷嘴(CD2)
	}
}

// RegisterDevice 注册预配置的设备到适配器
func (w *WayneAdapter) RegisterDevice(device *models.Device) error {
	if device == nil {
		return errors.NewValidationError("device cannot be nil")
	}

	w.devicesMux.Lock()
	// 检查是否为首次注册
	_, exists := w.devices[device.ID]
	w.devices[device.ID] = device
	w.devicesMux.Unlock()

	w.logger.Info("设备已注册到Wayne适配器",
		zap.String("设备ID", device.ID),
		zap.String("设备名称", device.Name),
		zap.Int("设备地址", device.DeviceAddress),
		zap.String("设备类型", string(device.Type)),
		zap.Bool("首次注册", !exists))

	// ✅ 如果是首次注册且适配器已连接，进行设备初始化
	if !exists && w.IsConnected() {
		deviceAddr := byte(device.DeviceAddress)
		w.logger.Info("首次注册设备，启动初始化流程",
			zap.String("设备ID", device.ID),
			zap.Uint8("设备地址", deviceAddr))

		// 异步执行初始化，避免阻塞注册过程
		go func() {
			// 稍微延迟，确保注册完成
			time.Sleep(100 * time.Millisecond)
			w.handleFirstTimeDeviceConnection(deviceAddr)
		}()
	}

	return nil
}

// UnregisterDevice 从适配器注销设备
func (w *WayneAdapter) UnregisterDevice(deviceID string) error {
	w.devicesMux.Lock()
	defer w.devicesMux.Unlock()

	if _, exists := w.devices[deviceID]; !exists {
		return errors.NewNotFoundError("device not found: " + deviceID)
	}

	delete(w.devices, deviceID)

	w.logger.Info("设备已从Wayne适配器注销",
		zap.String("设备ID", deviceID))

	return nil
}

// 私有辅助方法

func (w *WayneAdapter) getBaudRate(config api.AdapterConfig) int {
	if config.Connection.BaudRate == 9600 || config.Connection.BaudRate == 19200 {
		return config.Connection.BaudRate
	}
	return 9600 // 默认使用9600
}

func (w *WayneAdapter) getMaxRetries(config api.AdapterConfig) int {
	if config.Connection.MaxRetries > 0 {
		return config.Connection.MaxRetries
	}
	return 3 // 默认重试3次
}

// ✅ Phase 1: 设备专用TX#管理函数

// ✅ 双向TX#管理：Master发送数据时获取Master的TX#
func (w *WayneAdapter) getNextMasterSequence(deviceAddr byte) byte {
	w.sequenceMux.Lock()
	defer w.sequenceMux.Unlock()

	current, exists := w.masterTxSequence[deviceAddr]

	if !exists {
		// 第一次调用：初始化并返回0
		w.logger.Info("初始化Master TX#序列号",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("Master_TX#", 0),
			zap.String("说明", "Wayne DART: 重启后第一次发送使用TX#=0作为重启信号"))
		w.masterTxSequence[deviceAddr] = 255 // ✅ 修复：使用255标记已发送过重启信号
		return 0                             // 第一次发送返回0
	}

	// ✅ 修复：检查是否是刚重置后的第一次发送
	if current == 0 {
		w.logger.Info("重启后第一次发送",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("Master_TX#", 0),
			zap.String("说明", "Wayne DART: 发送TX#=0重启信号"))
		w.masterTxSequence[deviceAddr] = 255 // ✅ 修复：标记已发送过重启信号
		return 0                             // 第一次发送返回0
	}

	// ✅ 修复：如果是255，说明刚发送过重启信号，现在开始正常序列
	if current == 255 {
		w.logger.Info("重启信号已发送，开始正常TX#序列",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("Master_TX#", 1),
			zap.String("说明", "Wayne DART: 重启后正常递增TX#序列"))
		w.masterTxSequence[deviceAddr] = 1
		return 1
	}

	// ✅ 后续TX#递增：1-15循环
	w.masterTxSequence[deviceAddr]++
	if w.masterTxSequence[deviceAddr] > 15 {
		w.masterTxSequence[deviceAddr] = 1 // 1-15循环
	}

	newCurrent := w.masterTxSequence[deviceAddr]

	w.logger.Info("获取Master TX#序列号",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("Master_TX#", newCurrent),
		zap.String("说明", "Wayne DART: 正常递增TX#序列"))

	return newCurrent
}

// ✅ 双向TX#管理：跟踪Slave的TX#状态
func (w *WayneAdapter) updateSlaveSequence(deviceAddr, slaveTX byte) {
	w.sequenceMux.Lock()
	defer w.sequenceMux.Unlock()

	lastSlaveTX, exists := w.slaveTxSequence[deviceAddr]
	w.slaveTxSequence[deviceAddr] = slaveTX

	w.logger.Debug("更新Slave TX#状态",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("上次Slave_TX#", lastSlaveTX),
		zap.Uint8("当前Slave_TX#", slaveTX),
		zap.Bool("首次记录", !exists),
		zap.String("说明", "用于ACK验证和重复检测"))
}

// ✅ 双向TX#管理：获取Slave最后的TX#
func (w *WayneAdapter) getLastSlaveSequence(deviceAddr byte) byte {
	w.sequenceMux.RLock()
	defer w.sequenceMux.RUnlock()

	if lastTX, exists := w.slaveTxSequence[deviceAddr]; exists {
		return lastTX
	}
	return 0 // 默认为0，表示未知状态
}

// ✅ 双向TX#管理：验证Slave发送的TX#是否符合协议要求
func (w *WayneAdapter) validateSlaveSequence(deviceAddr, slaveTX byte) error {
	lastSlaveTX := w.getLastSlaveSequence(deviceAddr)

	w.logger.Debug("验证Slave TX#序列",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("当前Slave_TX#", slaveTX),
		zap.Uint8("上次Slave_TX#", lastSlaveTX),
		zap.String("协议依据", "Wayne DART V1.3 双向TX#同步"))

	// 1. TX# = 0 → Slave设备重启
	if slaveTX == 0 {
		w.logger.Info("检测到Slave设备重启",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("重启信号", slaveTX),
			zap.String("处理", "重置双向TX#状态"))
		w.resetBothSequences(deviceAddr)
		return nil
	}

	// 2. 首次通信（上次TX#=0）→ 接受任何有效TX#
	if lastSlaveTX == 0 {
		w.logger.Info("首次接收Slave数据或Master重启后同步",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("Slave_TX#", slaveTX),
			zap.String("处理", "同步到Slave的TX#状态"))
		return nil
	}

	// 3. TX# = 上次TX# → 重复传输（Slave未收到ACK）
	if slaveTX == lastSlaveTX {
		w.logger.Warn("检测到重复Slave传输",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("重复TX#", slaveTX),
			zap.String("原因", "Slave可能未收到上次ACK"),
			zap.String("处理", "重新发送ACK"))
		return nil // 合法的重复传输
	}

	// 4. 验证TX#递增是否正确（期望递增1，考虑1-15循环）
	expectedTX := lastSlaveTX + 1
	if expectedTX > 15 {
		expectedTX = 1 // TX#范围1-15循环
	}

	if slaveTX == expectedTX {
		w.logger.Debug("Slave TX#正常递增",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("期望TX#", expectedTX),
			zap.Uint8("实际TX#", slaveTX))
		return nil
	}

	// 5. TX#不匹配 → 可能的协议错误
	w.logger.Warn("Slave TX#序列异常",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("期望TX#", expectedTX),
		zap.Uint8("实际TX#", slaveTX),
		zap.Uint8("上次TX#", lastSlaveTX),
		zap.String("处理", "接受数据但记录异常"))

	// 注意：Wayne DART协议在这种情况下通常仍然接受数据
	// 只是记录异常，不中断通信
	return fmt.Errorf("Slave TX#序列异常: 期望=%d, 实际=%d, 上次=%d", expectedTX, slaveTX, lastSlaveTX)
}

// ✅ 双向TX#管理：设备重启时重置双向TX#
func (w *WayneAdapter) resetBothSequences(deviceAddr byte) {
	w.sequenceMux.Lock()
	defer w.sequenceMux.Unlock()

	// ✅ 修复：检查是否已经重置过，避免重复重置
	currentMaster, masterExists := w.masterTxSequence[deviceAddr]
	currentSlave, slaveExists := w.slaveTxSequence[deviceAddr]

	// ✅ 修复：如果TX#序列已经是初始状态或刚重置过，跳过重复重置
	if masterExists && slaveExists && (currentMaster == 0 || currentMaster == 255) && currentSlave == 0 {
		w.logger.Debug("TX#序列已经是重置状态，跳过重复重置",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("当前Master_TX#", currentMaster),
			zap.Uint8("当前Slave_TX#", currentSlave))
		return
	}

	w.logger.Info("重置双向TX#序列号",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("原因", "设备重启或首次连接"),
		zap.Uint8("重置前Master_TX#", currentMaster),
		zap.Uint8("重置前Slave_TX#", currentSlave))

	w.masterTxSequence[deviceAddr] = 0 // Master TX#重置
	w.slaveTxSequence[deviceAddr] = 0  // Slave TX#重置
}

// sendACKForReceivedData 自动发送ACK（使用接收到的TX#）
func (w *WayneAdapter) sendACKForReceivedData(deviceAddr, receivedTxNum byte) error {
	ackFrame, err := dartline.CreateAckFrame(deviceAddr, receivedTxNum)
	if err != nil {
		return fmt.Errorf("创建ACK帧失败: %w", err)
	}

	// 发送ACK确认
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	return w.serialManager.SendFrame(ctx, ackFrame.Encode())
}

// validateTXNumberSync 验证TX#同步 - Wayne DART协议要求
// 根据协议规范第16章：Error Recovery中的TX#检查序列
func (w *WayneAdapter) validateTXNumberSync(sentTX, receivedTX, deviceAddr byte) error {
	w.logger.Debug("开始TX#同步验证",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("发送TX#", sentTX),
		zap.Uint8("接收TX#", receivedTX),
		zap.String("协议依据", "Wayne DART V1.3 第16章"))

	// 获取当前设备的预期TX#（最后成功接收的TX#）
	expectedTX := w.getLastReceivedTX(deviceAddr)

	// 🔥 Wayne DART协议规定的TX#检查序列

	// 1. TX# = 最后接收的TX# → 发送单位没有收到我的ACK
	if receivedTX == expectedTX && receivedTX != 0 {
		w.logger.Warn("检测到重复TX#，设备可能未收到ACK",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("重复TX#", receivedTX),
			zap.String("处理", "接受数据但记录为重复"))
		// 接受数据，这是合法的重复传输
		return nil
	}

	// 2. TX# = 0 → 发送单位已重启
	if receivedTX == 0 {
		w.logger.Info("检测到设备重启信号",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("重启TX#", receivedTX),
			zap.String("处理", "重置预期TX#并接受数据"))

		// 重置设备的双向TX#状态
		w.resetBothSequences(deviceAddr)
		w.updateSlaveSequence(deviceAddr, 0)
		return nil
	}

	// 3. 正常情况：验证TX#是否与发送的匹配
	if receivedTX == sentTX {
		// ✅ TX#匹配，更新最后接收的TX#
		w.setLastReceivedTX(deviceAddr, receivedTX)
		w.logger.Debug("TX#同步正常",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("匹配TX#", receivedTX))
		return nil
	}

	// 4. TX# ≠ 期望值 → 检查是否为本单位重启情况
	if expectedTX == 0 {
		w.logger.Info("本机重启后首次通信，同步到设备TX#",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("设备TX#", receivedTX),
			zap.String("处理", "同步TX#并接受数据"))

		// 同步到设备的TX#
		w.setLastReceivedTX(deviceAddr, receivedTX)
		return nil
	}

	// 5. 其他情况 → 协议错误
	w.logger.Error("TX#同步错误",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("发送TX#", sentTX),
		zap.Uint8("接收TX#", receivedTX),
		zap.Uint8("预期TX#", expectedTX),
		zap.String("协议要求", "应该发送NAK"))

	return fmt.Errorf("TX#同步失败: 发送=%d, 接收=%d, 预期=%d", sentTX, receivedTX, expectedTX)
}

// getLastReceivedTX 获取设备最后接收的TX#
func (w *WayneAdapter) getLastReceivedTX(deviceAddr byte) byte {
	w.sequenceMux.RLock()
	defer w.sequenceMux.RUnlock()

	// 使用deviceStates存储最后接收的TX#
	w.stateMux.RLock()
	defer w.stateMux.RUnlock()

	if state, exists := w.deviceStates[deviceAddr]; exists {
		// 将LastStatus用作存储最后接收TX#的临时解决方案
		// 在生产代码中应该添加专门的字段
		if state.LastStatus != "" && len(state.LastStatus) > 0 {
			// 简单解析存储的TX#
			var lastTX byte
			if n, _ := fmt.Sscanf(state.LastStatus, "TX_%d", &lastTX); n == 1 {
				return lastTX
			}
		}
	}

	return 0 // 默认为0，表示未知或重启
}

// setLastReceivedTX 设置设备最后接收的TX#
func (w *WayneAdapter) setLastReceivedTX(deviceAddr, txNumber byte) {
	w.stateMux.Lock()
	defer w.stateMux.Unlock()

	// 获取或创建设备状态
	state, exists := w.deviceStates[deviceAddr]
	if !exists {
		state = &DeviceState{
			Address:  deviceAddr,
			LastSeen: time.Now(),
		}
		w.deviceStates[deviceAddr] = state
	}

	// 更新最后接收的TX#（临时存储在LastStatus中）
	state.LastStatus = fmt.Sprintf("TX_%d", txNumber)
	state.LastSeen = time.Now()

	w.logger.Debug("更新设备最后接收TX#",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("TX#", txNumber))
}

// ✅ Phase 2: 独立数据处理协程

// startDataProcessor 启动独立的数据处理协程
func (w *WayneAdapter) startDataProcessor() {
	w.dataProcessorWG.Add(1)
	go w.dataProcessorLoop()

	w.logger.Info("数据处理协程已启动",
		zap.String("说明", "实现读取发送分离，支持异步通信"))
}

// dataProcessorLoop 数据处理协程主循环
func (w *WayneAdapter) dataProcessorLoop() {
	defer w.dataProcessorWG.Done()

	w.logger.Debug("数据处理协程开始运行")

	for {
		select {
		case frameData := <-w.receivedDataChan:
			// ✅ 持续处理所有接收到的帧
			w.processReceivedFrame(frameData)
		case <-w.stopDataProcessorChan:
			w.logger.Info("数据处理协程接收到停止信号")
			return
		}
	}
}

// processReceivedFrame 按帧类型分流处理接收到的帧
func (w *WayneAdapter) processReceivedFrame(frameData []byte) {
	// 解码帧数据
	frame, err := dartline.DecodeFrame(frameData)
	if err != nil {
		w.logger.Error("解码DART帧失败",
			zap.String("帧数据(HEX)", fmt.Sprintf("%X", frameData)),
			zap.Error(err))
		return
	}

	w.logger.Debug("处理接收到的DART帧",
		zap.String("帧类型", w.getFrameTypeString(frame)),
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()),
		zap.Int("数据长度", len(frame.Data)))

	// 🔥 核心：按帧类型分流处理
	switch {
	case frame.IsAckFrame() || frame.IsNakFrame():
		// ✅ ACK/NAK：通过TX#找到等待响应的命令
		w.handleCommandResponse(frame)

	case frame.IsDataFrame():
		// ✅ 数据帧：自动ACK + 状态机/业务处理
		w.handleDataFrame(frame)

	case frame.IsEotFrame():
		// ✅ EOT：协议层处理
		w.handleEOTFrame(frame)

	case frame.IsPollFrame():
		// POLL帧通常不会被Master接收，记录警告
		w.logger.Warn("接收到POLL帧，这通常不应该发生",
			zap.Uint8("设备地址", frame.Address))

	default:
		w.logger.Warn("接收到未知类型的帧",
			zap.String("帧数据(HEX)", fmt.Sprintf("%X", frameData)))
	}
}

// handleCommandResponse ACK/NAK响应处理（命令完成）
func (w *WayneAdapter) handleCommandResponse(frame *dartline.Frame) {
	w.logger.Debug("处理命令响应(半双工模式)",
		zap.String("帧类型", w.getFrameTypeString(frame)),
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()),
		zap.String("原始帧数据", fmt.Sprintf("%X", frame.Encode())),
		zap.Uint8("原始命令字节", frame.Command))

	// ✅ 半双工模式：直接调用临时响应处理器
	w.tempHandlerMux.RLock()
	handler := w.temporaryResponseHandler
	w.tempHandlerMux.RUnlock()

	if handler != nil {
		w.logger.Debug("✅ 半双工响应已转发给处理器",
			zap.Uint8("设备地址", frame.Address),
			zap.Uint8("TX#", frame.GetTxNumber()),
			zap.String("响应类型", w.getFrameTypeString(frame)))
		handler(frame)
	} else {
		w.logger.Debug("半双工模式：无活跃的响应处理器，可能是自发数据",
			zap.Uint8("设备地址", frame.Address),
			zap.Uint8("TX#", frame.GetTxNumber()),
			zap.String("说明", "设备主动发送的数据将由数据处理流程处理"))
	}
}

// handleDataFrame 数据帧处理（状态机+业务逻辑）
func (w *WayneAdapter) handleDataFrame(frame *dartline.Frame) {
	deviceAddr := frame.Address
	slaveTxNum := frame.GetTxNumber()

	w.logger.Info("收到Slave数据帧",
		zap.Uint8("设备地址", deviceAddr),
		zap.Uint8("Slave_TX#", slaveTxNum),
		zap.Int("数据长度", len(frame.Data)),
		zap.String("协议说明", "Wayne DART: Slave主动发送数据"))

	// 1️⃣ ✅ 双向TX#同步：验证和更新Slave的TX#状态
	if err := w.validateSlaveSequence(deviceAddr, slaveTxNum); err != nil {
		w.logger.Warn("Slave TX#验证失败，但继续处理",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("Slave_TX#", slaveTxNum),
			zap.Error(err),
			zap.String("处理方式", "发送ACK但记录验证失败"))
	} else {
		w.logger.Debug("Slave TX#验证成功",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("Slave_TX#", slaveTxNum))
	}

	// 2️⃣ 协议层：发送ACK确认（必须使用Slave的TX#）
	if err := w.sendACKForReceivedData(deviceAddr, slaveTxNum); err != nil {
		w.logger.Error("发送ACK确认失败",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("ACK_TX#", slaveTxNum),
			zap.Error(err))
		// ACK发送失败不应该阻止数据处理
	} else {
		w.logger.Info("已发送ACK确认",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("ACK_TX#", slaveTxNum),
			zap.String("协议说明", "Master确认收到Slave的数据"))
	}

	// 3️⃣ 更新Slave TX#状态（无论验证是否成功都要更新）
	w.updateSlaveSequence(deviceAddr, slaveTxNum)

	// 4️⃣ 业务层：解析数据内容
	transactions, err := w.parseDARTFrameToMultipleTransactions(frame)
	if err != nil {
		w.logger.Error("解析数据帧为事务失败",
			zap.Uint8("设备地址", deviceAddr),
			zap.Error(err))
		return
	}

	// 5️⃣ 状态机处理：根据设备状态和数据内容决定后续动作
	w.processDeviceStateTransitions(deviceAddr, transactions, slaveTxNum)
}

// handleEOTFrame EOT帧处理
func (w *WayneAdapter) handleEOTFrame(frame *dartline.Frame) {
	w.logger.Debug("处理EOT帧",
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()))

	// EOT通常表示设备无数据发送，可以视为正常的轮询响应
	// 将其视为命令响应处理
	w.handleCommandResponse(frame)
}

// processDeviceStateTransitions 处理设备状态转换
func (w *WayneAdapter) processDeviceStateTransitions(deviceAddr byte, transactions []pump.DCTransaction, slaveTxNum byte) {
	w.logger.Debug("处理设备状态转换",
		zap.Uint8("设备地址", deviceAddr),
		zap.Int("事务数量", len(transactions)),
		zap.Uint8("Slave_TX#", slaveTxNum))

	// 检查是否为设备重启（TX#=0）
	if slaveTxNum == 0 {
		w.logger.Info("检测到设备重启",
			zap.Uint8("设备地址", deviceAddr),
			zap.String("说明", "TX#=0表示设备重启"))
		w.handleDeviceInitialization(deviceAddr, transactions, "设备重启")
		return
	}

	// 处理每个事务
	for _, transaction := range transactions {
		switch transaction.GetType() {
		case pump.TransactionTypeDC1:
			w.handleDC1Status(transaction, deviceAddr)
		case pump.TransactionTypeDC2:
			w.handleDC2VolumeAmount(transaction, deviceAddr)
		case pump.TransactionTypeDC3:
			w.handleDC3PriceNozzle(transaction, deviceAddr)
		case pump.TransactionTypeDC101:
			w.handleDC101Counters(transaction, deviceAddr)
		default:
			w.logger.Debug("跳过未知类型的事务",
				zap.Uint8("事务类型", uint8(transaction.GetType())),
				zap.Uint8("设备地址", deviceAddr))
		}
	}
}

// stopDataProcessor 停止数据处理协程
func (w *WayneAdapter) stopDataProcessor() {
	w.logger.Info("停止数据处理协程")
	close(w.stopDataProcessorChan)
	w.dataProcessorWG.Wait()
	w.logger.Info("数据处理协程已停止")
}

// isValidDeviceResponse 验证设备响应有效性
func (w *WayneAdapter) isValidDeviceResponse(response *dartline.Frame, expectedAddress byte) bool {
	if response == nil || response.Address != expectedAddress {
		return false
	}
	// ACK、NAK、EOT、DATA帧都算有效响应
	return response.IsAckFrame() || response.IsNakFrame() ||
		response.IsEotFrame() || response.IsDataFrame()
}

// executeAuthorizeCommand 执行授权命令
func (w *WayneAdapter) executeAuthorizeCommand(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	w.logger.Info("开始执行授权命令",
		zap.String("设备ID", device.ID),
		zap.Uint8("设备地址", byte(device.DeviceAddress)),
		zap.String("协议", "Wayne DART - CD1 AUTHORIZE"))

	// 构建CD1授权事务
	cdTransaction, err := builder.BuildCD1(pump.CmdAuthorize)
	if err != nil {
		return nil, fmt.Errorf("构建CD1授权事务失败: %w", err)
	}

	// 发送事务并等待响应（增加超时时间适应真实设备响应时间）
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 100*time.Millisecond)
	if err != nil {
		w.logger.Error("授权命令发送失败",
			zap.String("设备ID", device.ID),
			zap.Error(err))

		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("授权命令执行失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// ✅ 兼容NAK场景：根据响应类型决定结果
	if response != nil {
		// 对于DC事务，我们需要检查响应的状态码或类型
		responseType := response.GetType()
		statusCode := response.GetStatusCode()

		// 根据响应数据判断是否为成功响应
		isSuccess := statusCode == 0x00 || responseType != pump.TransactionType(0xFF)

		// ✅ 使用状态机更新设备状态
		deviceAddr := byte(device.DeviceAddress)
		stateMachine := w.getOrCreateStateMachine(deviceAddr)

		if isSuccess {
			// 授权成功，更新状态机
			if err := stateMachine.ProcessCommand(pump.CmdAuthorize); err != nil {
				w.logger.Warn("状态机处理授权命令失败",
					zap.String("设备ID", device.ID),
					zap.Uint8("设备地址", deviceAddr),
					zap.Error(err))
			} else {
				w.logger.Info("设备授权成功，状态机已更新",
					zap.String("设备ID", device.ID),
					zap.Uint8("设备地址", deviceAddr),
					zap.String("当前状态", w.getPumpStatusString(stateMachine.GetCurrentStatus())))
			}
		} else {
			w.logger.Warn("设备授权被拒绝，状态机保持当前状态",
				zap.String("设备ID", device.ID),
				zap.Uint8("设备地址", deviceAddr),
				zap.String("当前状态", w.getPumpStatusString(stateMachine.GetCurrentStatus())))
		}

		return &api.CommandResult{
			Success: true, // ✅ 收到响应就算成功的通信
			Data: map[string]interface{}{
				"status":        map[bool]string{true: "authorized", false: "authorization_rejected"}[isSuccess],
				"message":       map[bool]string{true: "Device responded to authorization", false: "Device rejected authorization"}[isSuccess],
				"device_id":     device.ID,
				"address":       device.DeviceAddress,
				"response_type": fmt.Sprintf("DC%d", int(responseType)),
				"status_code":   statusCode,
				"response_data": fmt.Sprintf("%X", response.GetData()),
				"note":          "使用DC事务响应，无论ACK/NAK都表示成功通信",
				"pump_status":   w.getPumpStatusString(stateMachine.GetCurrentStatus()), // 添加状态机状态
			},
			Timestamp: time.Now(),
		}, nil
	}

	w.logger.Warn("授权命令无响应",
		zap.String("设备ID", device.ID))

	return &api.CommandResult{
		Success:   false,
		Error:     "No response received",
		Timestamp: time.Now(),
	}, nil
}

// executeResetCommand 执行重置命令
func (w *WayneAdapter) executeResetCommand(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// 构建CD1重置事务
	cdTransaction, err := builder.BuildCD1(pump.CmdReset)
	if err != nil {
		return nil, fmt.Errorf("构建CD1重置事务失败: %w", err)
	}

	// 发送事务并等待响应（增加超时时间适应真实设备响应时间）
	_, err = w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 100*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("重置命令执行失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	return &api.CommandResult{
		Success: true,
		Data: map[string]interface{}{
			"status":  "reset",
			"message": "Device reset successfully",
		},
		Timestamp: time.Now(),
	}, nil
}

// executeQueryCommand
func (w *WayneAdapter) executeQueryCommand(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	//
	cdTransaction, err := builder.BuildCD1(pump.CmdReturnStatus)
	if err != nil {
		return nil, fmt.Errorf("构建CD1事务失败: %w", err)
	}

	// 发送事务并等待响应（增加超时时间适应真实设备响应时间）
	_, err = w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 100*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("查询命令执行失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	return &api.CommandResult{
		Success: true,
		Data: map[string]interface{}{
			"status":  "status_query",
			"message": "Device status_query successfully",
		},
		Timestamp: time.Now(),
	}, nil
}

// executeStopCommand 执行停止命令
func (w *WayneAdapter) executeStopCommand(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// 构建CD1停止事务
	cdTransaction, err := builder.BuildCD1(pump.CmdStop)
	if err != nil {
		return nil, fmt.Errorf("构建CD1停止事务失败: %w", err)
	}

	// 发送事务并等待响应（增加超时时间适应真实设备响应时间）
	_, err = w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 100*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("停止命令执行失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	return &api.CommandResult{
		Success: true,
		Data: map[string]interface{}{
			"status":  "stopped",
			"message": "Device stopped successfully",
		},
		Timestamp: time.Now(),
	}, nil
}

// executeStatusQuery 执行状态查询
func (w *WayneAdapter) executeStatusQuery(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {

	// ✅ Phase 1修复: POLL命令使用纯20H格式，不使用TX#
	// 创建简单的状态轮询帧
	frame, err := dartline.CreatePollFrame(byte(device.DeviceAddress), 0) // POLL不使用TX#
	if err != nil {
		return nil, fmt.Errorf("创建状态查询帧失败: %w", err)
	}

	w.logger.Debug("执行状态查询",
		zap.String("设备ID", device.ID),
		zap.Uint8("设备地址", byte(device.DeviceAddress)))

	// 发送并等待响应（增加超时时间适应真实设备响应时间）
	response, err := w.sendFrameAndWaitResponse(ctx, frame, 100*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("状态查询失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 使用新的多事务解析方法处理响应
	result, err := w.parseStatusResponseWithMultiTransaction(response, device)
	if err != nil {
		w.logger.Error("解析状态响应失败", zap.Error(err))
		// 回退到简单解析
		result = w.parseStatusResponse(response)
	}

	return &api.CommandResult{
		Success:   true,
		Data:      result,
		Timestamp: time.Now(),
	}, nil
}

// executeRequestTotalCounters 执行请求总计数器
func (w *WayneAdapter) executeRequestTotalCounters(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// 构建CD101总计数器事务
	cdTransaction, err := builder.BuildCD101(0x19) // 请求金额总计
	if err != nil {
		return nil, fmt.Errorf("构建CD101事务失败: %w", err)
	}

	// 发送事务并等待响应
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 100*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("请求总计数器失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 解析响应
	result := map[string]interface{}{
		"device_id":     device.ID,
		"counter_type":  "total_counters",
		"data_source":   "dart_protocol",
		"response_data": fmt.Sprintf("%X", response.GetData()),
	}

	return &api.CommandResult{
		Success:   true,
		Data:      result,
		Timestamp: time.Now(),
	}, nil
}

// executeRequestFillingInfo 执行请求加油信息
func (w *WayneAdapter) executeRequestFillingInfo(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// 构建CD1加油信息查询事务
	cdTransaction, err := builder.BuildCD1(pump.CmdReturnFillingInformation)
	if err != nil {
		return nil, fmt.Errorf("构建CD1事务失败: %w", err)
	}

	w.logger.Info("请求加油交易信息",
		zap.String("设备ID", device.ID),
		zap.String("设备地址", fmt.Sprintf("0x%02X", device.DeviceAddress)),
		zap.String("协议", "Wayne DART - RETURN FILLING INFORMATION"))

	// 发送事务并等待响应
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 300*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("请求加油信息失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 改进：解析加油交易数据
	transactionData, err := w.parseFillingInfoResponse(response, device)
	if err != nil {
		w.logger.Warn("解析加油交易数据失败，返回原始数据",
			zap.String("设备ID", device.ID),
			zap.Error(err))

		// 即使解析失败，也返回原始数据
		result := map[string]interface{}{
			"device_id":     device.ID,
			"info_type":     "filling_info_raw",
			"data_source":   "dart_protocol",
			"response_data": fmt.Sprintf("%X", response.GetData()),
			"parse_error":   err.Error(),
		}

		return &api.CommandResult{
			Success:   true,
			Data:      result,
			Timestamp: time.Now(),
		}, nil
	}

	return &api.CommandResult{
		Success:   true,
		Data:      transactionData,
		Timestamp: time.Now(),
	}, nil
}

// 新增：解析加油交易信息响应
func (w *WayneAdapter) parseFillingInfoResponse(response pump.DCTransaction, device *models.Device) (map[string]interface{}, error) {
	// 基础交易信息
	transactionData := map[string]interface{}{
		"device_id":   device.ID,
		"device_addr": fmt.Sprintf("0x%02X", device.DeviceAddress),
		"info_type":   "filling_transaction",
		"data_source": "dart_protocol",
		"timestamp":   time.Now().Format(time.RFC3339),
		"raw_data":    fmt.Sprintf("%X", response.GetData()),
	}

	// 根据响应类型解析数据
	switch response.GetType() {
	case pump.TransactionTypeDC2:
		// DC2: Filled volume and amount
		if err := w.parseFillingDC2Data(response, transactionData); err != nil {
			return nil, fmt.Errorf("解析DC2体积金额数据失败: %w", err)
		}

	case pump.TransactionTypeDC3:
		// DC3: Nozzle status and filling price
		if err := w.parseFillingDC3Data(response, transactionData); err != nil {
			return nil, fmt.Errorf("解析DC3喷嘴价格数据失败: %w", err)
		}

	default:
		// 其他类型，尝试解析为多事务响应
		return w.parseMultiTransactionFillingInfo(response, device)
	}

	w.logger.Info("成功解析加油交易数据",
		zap.String("设备ID", device.ID),
		zap.String("交易类型", fmt.Sprintf("DC%d", int(response.GetType()))),
		zap.Any("交易数据", transactionData))

	return transactionData, nil
}

// 新增：解析DC2体积和金额数据
func (w *WayneAdapter) parseFillingDC2Data(response pump.DCTransaction, transactionData map[string]interface{}) error {
	data := response.GetData()
	if len(data) < 8 {
		return fmt.Errorf("DC2数据长度不足，期望8字节，实际%d字节", len(data))
	}

	// DC2格式：VOL(4字节BCD) + AMO(4字节BCD)
	volumeBCD := data[0:4] // 体积BCD数据
	amountBCD := data[4:8] // 金额BCD数据

	// 解码BCD为实际数值
	volume := pump.DecodeBCDVolume4(volumeBCD)
	transactionData["volume"] = volume
	transactionData["volume_bcd"] = fmt.Sprintf("%X", volumeBCD)

	amount := pump.DecodeBCDAmount4(amountBCD)
	transactionData["amount"] = amount
	transactionData["amount_bcd"] = fmt.Sprintf("%X", amountBCD)

	transactionData["transaction_type"] = "DC2_volume_amount"
	return nil
}

// 新增：解析DC3喷嘴状态和价格数据
func (w *WayneAdapter) parseFillingDC3Data(response pump.DCTransaction, transactionData map[string]interface{}) error {
	data := response.GetData()
	if len(data) < 4 {
		return fmt.Errorf("DC3数据长度不足，期望4字节，实际%d字节", len(data))
	}

	// DC3格式：PRI(3字节BCD) + NOZIO(1字节)
	priceBCD := data[0:3] // 价格BCD数据
	nozzleIO := data[3]   // 喷嘴状态

	// 解码价格BCD
	price := pump.DecodeBCDPrice3(priceBCD)
	transactionData["price"] = price
	transactionData["price_bcd"] = fmt.Sprintf("%X", priceBCD)

	// 解码喷嘴状态
	nozzleNumber := nozzleIO & 0x0F        // 低4位：喷嘴号码
	insertStatus := (nozzleIO & 0xF0) >> 4 // 高4位：插入状态

	transactionData["nozzle_number"] = int(nozzleNumber)
	transactionData["nozzle_insert_status"] = w.decodeNozzleInsertStatus(insertStatus)
	transactionData["nozzle_io_raw"] = fmt.Sprintf("0x%02X", nozzleIO)
	transactionData["transaction_type"] = "DC3_price_nozzle"

	return nil
}

// 新增：解析多事务加油信息响应
func (w *WayneAdapter) parseMultiTransactionFillingInfo(response pump.DCTransaction, device *models.Device) (map[string]interface{}, error) {
	w.logger.Info("尝试解析多事务加油信息响应",
		zap.String("设备ID", device.ID),
		zap.String("响应数据", fmt.Sprintf("%X", response.GetData())))

	// 创建基础响应数据
	transactionData := map[string]interface{}{
		"device_id":   device.ID,
		"device_addr": fmt.Sprintf("0x%02X", device.DeviceAddress),
		"info_type":   "filling_transaction_multi",
		"data_source": "dart_protocol",
		"timestamp":   time.Now().Format(time.RFC3339),
		"raw_data":    fmt.Sprintf("%X", response.GetData()),
	}

	// 对于无法直接解析的响应，记录为原始数据供后续分析
	transactionData["parse_status"] = "raw_data_preserved"
	transactionData["note"] = "需要进一步分析多事务响应格式"

	return transactionData, nil
}

// executeSetPrice 执行设置价格
func (w *WayneAdapter) executeSetPrice(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// 从命令参数中获取价格信息，如果没有则使用默认价格
	// prices, err := w.extractPricesFromCommand(command)
	// if err != nil {
	// 	// 使用4个不同的默认价格，方便测试和排查
	// 	// 价格1: 1.333元/升, 价格2: 2.333元/升, 价格3: 5.333元/升, 价格4: 6.333元/升
	// 	prices = [][]byte{
	// 		{0x00, 0x13, 0x33}, // 1.333元/升
	// 		{0x00, 0x23, 0x33}, // 2.333元/升
	// 		{0x00, 0x53, 0x33}, // 5.333元/升
	// 		{0x00, 0x63, 0x33}, // 6.333元/升
	// 	}
	// }
	// TODO 测试测试测试 TODOTODOTODOTODOTODO
	prices := [][]byte{
		{0x00, 0x13, 0x33}, // 1.333元/升
		{0x00, 0x23, 0x33}, // 2.333元/升
		// {0x00, 0x53, 0x33}, // 5.333元/升
		// {0x00, 0x63, 0x33}, // 6.333元/升
	}

	w.logger.Info("执行价格设置",
		zap.String("设备ID", device.ID),
		zap.Int("价格数量", len(prices)),
		zap.String("价格数据", fmt.Sprintf("%X", prices)))

	// 构建CD5价格设置事务
	cdTransaction, err := builder.BuildCD5(prices)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("构建CD5价格事务失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 发送事务并等待响应（价格设置需要较长时间）
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 200*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("价格设置失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 解析响应并验证设置结果
	result := map[string]interface{}{
		"device_id":     device.ID,
		"command_type":  "set_price",
		"prices_count":  len(prices),
		"data_source":   "dart_protocol",
		"response_data": fmt.Sprintf("%X", response.GetData()),
	}

	return &api.CommandResult{
		Success:   true,
		Data:      result,
		Timestamp: time.Now(),
	}, nil
}

// executePresetVolume 执行预设体积命令
func (w *WayneAdapter) executePresetVolume(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// ✅ Wayne DART协议要求：CD3预设体积命令需要设备处于AUTHORIZED状态
	w.logger.Info("检查设备状态以支持预设体积",
		zap.String("设备ID", device.ID),
		zap.String("协议要求", "CD3预设体积需要设备先处于AUTHORIZED状态"))

	// 首先查询设备当前状态
	statusResult, err := w.checkDeviceStatusForPreset(ctx, device)
	if err != nil {
		w.logger.Warn("无法获取设备状态，尝试先发送AUTHORIZE命令",
			zap.String("设备ID", device.ID),
			zap.Error(err))

		// 尝试自动授权
		if authErr := w.autoAuthorizeDevice(ctx, device); authErr != nil {
			return &api.CommandResult{
				Success:   false,
				Error:     fmt.Sprintf("设备状态检查失败且自动授权失败: %v, %v", err, authErr),
				Timestamp: time.Now(),
			}, nil
		}
		w.logger.Info("自动授权完成，继续预设体积",
			zap.String("设备ID", device.ID))
	} else if !statusResult {
		w.logger.Info("设备状态不支持预设，先发送AUTHORIZE命令",
			zap.String("设备ID", device.ID))

		// 自动授权设备
		if authErr := w.autoAuthorizeDevice(ctx, device); authErr != nil {
			return &api.CommandResult{
				Success:   false,
				Error:     fmt.Sprintf("设备需要先授权但授权失败: %v", authErr),
				Timestamp: time.Now(),
			}, nil
		}
		w.logger.Info("设备授权完成，继续预设体积",
			zap.String("设备ID", device.ID))
	}

	// 从命令参数中获取体积信息
	volumeBCD, err := w.extractVolumeFromCommand(command)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("提取体积参数失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	w.logger.Info("执行预设体积",
		zap.String("设备ID", device.ID),
		zap.String("体积数据(BCD)", fmt.Sprintf("%X", volumeBCD)))

	// 构建CD3预设体积事务
	cdTransaction, err := builder.BuildCD3(volumeBCD)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("构建CD3预设体积事务失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 发送事务并等待响应 - 使用DART协议标准25ms超时
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 25*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("预设体积失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 解析响应
	result := map[string]interface{}{
		"device_id":     device.ID,
		"command_type":  "preset_volume",
		"volume_bcd":    fmt.Sprintf("%X", volumeBCD),
		"data_source":   "dart_protocol",
		"response_data": fmt.Sprintf("%X", response.GetData()),
	}

	return &api.CommandResult{
		Success:   true,
		Data:      result,
		Timestamp: time.Now(),
	}, nil
}

// executePresetAmount 执行预设金额命令
func (w *WayneAdapter) executePresetAmount(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// ✅ Wayne DART协议要求：CD4预设金额命令需要设备处于AUTHORIZED状态
	w.logger.Info("检查设备状态以支持预设金额",
		zap.String("设备ID", device.ID),
		zap.String("协议要求", "CD4预设金额需要设备先处于AUTHORIZED状态"))

	// 首先查询设备当前状态
	statusResult, err := w.checkDeviceStatusForPreset(ctx, device)
	if err != nil {
		w.logger.Warn("无法获取设备状态，尝试先发送AUTHORIZE命令",
			zap.String("设备ID", device.ID),
			zap.Error(err))

		// 尝试自动授权
		if authErr := w.autoAuthorizeDevice(ctx, device); authErr != nil {
			return &api.CommandResult{
				Success:   false,
				Error:     fmt.Sprintf("设备状态检查失败且自动授权失败: %v, %v", err, authErr),
				Timestamp: time.Now(),
			}, nil
		}
		w.logger.Info("自动授权完成，继续预设金额",
			zap.String("设备ID", device.ID))
	} else if !statusResult {
		w.logger.Info("设备状态不支持预设，先发送AUTHORIZE命令",
			zap.String("设备ID", device.ID))

		// 自动授权设备
		if authErr := w.autoAuthorizeDevice(ctx, device); authErr != nil {
			return &api.CommandResult{
				Success:   false,
				Error:     fmt.Sprintf("设备需要先授权但授权失败: %v", authErr),
				Timestamp: time.Now(),
			}, nil
		}
		w.logger.Info("设备授权完成，继续预设金额",
			zap.String("设备ID", device.ID))
	}

	// 从命令参数中获取金额信息
	amountBCD, err := w.extractAmountFromCommand(command)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("提取金额参数失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	w.logger.Info("执行预设金额",
		zap.String("设备ID", device.ID),
		zap.String("金额数据(BCD)", fmt.Sprintf("%X", amountBCD)))

	// 构建CD4预设金额事务
	cdTransaction, err := builder.BuildCD4(amountBCD)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("构建CD4预设金额事务失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 发送事务并等待响应 - 使用DART协议标准25ms超时
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 25*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("预设金额失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 解析响应
	result := map[string]interface{}{
		"device_id":     device.ID,
		"command_type":  "preset_amount",
		"amount_bcd":    fmt.Sprintf("%X", amountBCD),
		"data_source":   "dart_protocol",
		"response_data": fmt.Sprintf("%X", response.GetData()),
	}

	return &api.CommandResult{
		Success:   true,
		Data:      result,
		Timestamp: time.Now(),
	}, nil
}

// extractPricesFromCommand 从命令参数中提取价格信息
func (w *WayneAdapter) extractPricesFromCommand(command api.Command) ([][]byte, error) {
	// 检查命令参数中是否有价格信息
	if command.Parameters != nil {
		if pricesParam, exists := command.Parameters["prices"]; exists {
			// 尝试解析价格参数
			switch prices := pricesParam.(type) {
			case []interface{}:
				var result [][]byte
				for i, priceVal := range prices {
					priceStr, ok := priceVal.(string)
					if !ok {
						return nil, fmt.Errorf("价格参数[%d]不是字符串格式", i)
					}

					// 将价格字符串转换为BCD格式
					// 例如: "3.333" -> []byte{0x00, 0x33, 0x33}
					priceBCD, err := w.convertPriceToBCD(priceStr)
					if err != nil {
						return nil, fmt.Errorf("价格[%d]转换失败: %v", i, err)
					}
					result = append(result, priceBCD)
				}
				return result, nil
			case string:
				// 单个价格字符串
				priceBCD, err := w.convertPriceToBCD(prices)
				if err != nil {
					return nil, fmt.Errorf("价格转换失败: %v", err)
				}
				return [][]byte{priceBCD}, nil
			}
		}
	}

	return nil, fmt.Errorf("命令参数中未找到价格信息")
}

// convertPriceToBCD 将价格字符串转换为BCD格式
func (w *WayneAdapter) convertPriceToBCD(priceStr string) ([]byte, error) {
	// 解析价格字符串为浮点数
	var price float64
	var err error

	// 尝试解析价格字符串
	if _, err = fmt.Sscanf(priceStr, "%f", &price); err != nil {
		w.logger.Warn("价格字符串解析失败，使用默认价格3.333",
			zap.String("priceStr", priceStr),
			zap.Error(err))
		price = 3.333
	}

	// 验证价格范围（Wayne协议通常支持0.001-999.999范围）
	if price < 0.001 || price > 999.999 {
		w.logger.Warn("价格超出有效范围，使用默认价格3.333",
			zap.String("priceStr", priceStr),
			zap.Float64("price", price))
		price = 3.333
	}

	// 使用pump包的BCD转换函数，Wayne协议使用3字节BCD格式，3位小数
	bcdBytes := pump.EncodeBCDPrice3(price)

	w.logger.Debug("价格BCD转换",
		zap.String("原始价格", priceStr),
		zap.Float64("解析价格", price),
		zap.String("BCD数据", fmt.Sprintf("%X", bcdBytes)))

	return bcdBytes, nil
}

// extractVolumeFromCommand 从命令参数中提取体积信息
func (w *WayneAdapter) extractVolumeFromCommand(command api.Command) ([]byte, error) {
	// 检查命令参数中是否有体积信息
	if command.Parameters != nil {
		if volumeParam, exists := command.Parameters["volume"]; exists {
			volumeStr, ok := volumeParam.(string)
			if !ok {
				// 尝试从浮点数转换
				if volumeFloat, ok := volumeParam.(float64); ok {
					volumeStr = fmt.Sprintf("%.2f", volumeFloat)
				} else {
					return nil, fmt.Errorf("体积参数不是字符串或数字格式")
				}
			}

			// 将体积字符串转换为BCD格式
			return w.convertVolumeToBCD(volumeStr)
		}
	}

	return nil, fmt.Errorf("命令参数中未找到体积信息")
}

// extractAmountFromCommand 从命令参数中提取金额信息
func (w *WayneAdapter) extractAmountFromCommand(command api.Command) ([]byte, error) {
	// 检查命令参数中是否有金额信息
	if command.Parameters != nil {
		if amountParam, exists := command.Parameters["amount"]; exists {
			amountStr, ok := amountParam.(string)
			if !ok {
				// 尝试从浮点数转换
				if amountFloat, ok := amountParam.(float64); ok {
					amountStr = fmt.Sprintf("%.2f", amountFloat)
				} else {
					return nil, fmt.Errorf("金额参数不是字符串或数字格式")
				}
			}

			// 将金额字符串转换为BCD格式
			return w.convertAmountToBCD(amountStr)
		}
	}

	return nil, fmt.Errorf("命令参数中未找到金额信息")
}

// convertVolumeToBCD 将体积字符串转换为BCD格式
func (w *WayneAdapter) convertVolumeToBCD(volumeStr string) ([]byte, error) {
	// 解析体积字符串为浮点数
	var volume float64
	var err error

	// 尝试解析体积字符串
	if _, err = fmt.Sscanf(volumeStr, "%f", &volume); err != nil {
		return nil, fmt.Errorf("体积字符串解析失败: %v", err)
	}

	// 验证体积范围（Wayne协议通常支持0.01-9999.99范围）
	if volume < 0.01 || volume > 9999.99 {
		return nil, fmt.Errorf("体积超出有效范围(0.01-9999.99): %.2f", volume)
	}

	// 使用pump包的BCD转换函数，Wayne协议使用4字节BCD格式，3位小数
	bcdBytes := pump.EncodeBCDVolume4(volume)

	w.logger.Debug("体积BCD转换",
		zap.String("原始体积", volumeStr),
		zap.Float64("解析体积", volume),
		zap.String("BCD数据", fmt.Sprintf("%X", bcdBytes)))

	return bcdBytes, nil
}

// convertAmountToBCD 将金额字符串转换为BCD格式
func (w *WayneAdapter) convertAmountToBCD(amountStr string) ([]byte, error) {
	// 解析金额字符串为浮点数
	var amount float64
	var err error

	// 尝试解析金额字符串
	if _, err = fmt.Sscanf(amountStr, "%f", &amount); err != nil {
		return nil, fmt.Errorf("金额字符串解析失败: %v", err)
	}

	// 验证金额范围（Wayne协议通常支持0.01-99999.99范围）
	if amount < 0.01 || amount > 99999.99 {
		return nil, fmt.Errorf("金额超出有效范围(0.01-99999.99): %.2f", amount)
	}

	// 使用pump包的BCD转换函数，Wayne协议使用4字节BCD格式，2位小数
	bcdBytes := pump.EncodeBCDAmount4(amount)

	w.logger.Debug("金额BCD转换",
		zap.String("原始金额", amountStr),
		zap.Float64("解析金额", amount),
		zap.String("BCD数据", fmt.Sprintf("%X", bcdBytes)))

	return bcdBytes, nil
}

// checkDeviceStatusForPreset 检查设备状态是否支持预设操作
func (w *WayneAdapter) checkDeviceStatusForPreset(ctx context.Context, device *models.Device) (bool, error) {
	deviceAddr := byte(device.DeviceAddress)

	// ✅ 使用状态机检查设备状态
	currentStatus := w.getDevicePumpStatus(deviceAddr)
	statusStr := w.getPumpStatusString(currentStatus)

	w.logger.Debug("使用状态机检查设备状态",
		zap.String("设备ID", device.ID),
		zap.Uint8("设备地址", deviceAddr),
		zap.String("当前状态", statusStr))

	// AUTHORIZED状态支持预设操作
	if currentStatus == pump.StatusAuthorized {
		return true, nil
	}

	// 其他状态需要先授权
	w.logger.Debug("设备状态不支持预设操作，需要先授权",
		zap.String("设备ID", device.ID),
		zap.String("当前状态", statusStr),
		zap.String("要求状态", "AUTHORIZED"))

	return false, nil
}

// autoAuthorizeDevice 自动授权设备 - 按照Wayne DART协议流程
func (w *WayneAdapter) autoAuthorizeDevice(ctx context.Context, device *models.Device) error {
	w.logger.Info("自动授权设备",
		zap.String("设备ID", device.ID),
		zap.String("原因", "预设操作需要设备处于AUTHORIZED状态"))

	deviceAddr := byte(device.DeviceAddress)
	stateMachine := w.getOrCreateStateMachine(deviceAddr)
	currentStatus := stateMachine.GetCurrentStatus()

	w.logger.Info("检查当前设备状态机状态",
		zap.String("设备ID", device.ID),
		zap.String("当前状态", w.getPumpStatusString(currentStatus)))

	// ✅ 按照Wayne DART协议处理PUMP NOT PROGRAMMED状态
	if currentStatus == pump.StatusPumpNotProgrammed {
		w.logger.Warn("设备处于PUMP NOT PROGRAMMED状态，需要先配置价格",
			zap.String("设备ID", device.ID),
			zap.String("协议要求", "Wayne DART: PUMP NOT PROGRAMMED状态需要先接收价格配置"),
			zap.String("解决方案", "发送价格配置，然后等待设备状态变化"))

		// 📖 协议：PUMP NOT PROGRAMMED状态需要先配置价格
		if err := w.sendPriceConfigurationForDevice(ctx, device); err != nil {
			w.logger.Error("价格配置失败",
				zap.String("设备ID", device.ID),
				zap.Error(err))
			return fmt.Errorf("设备需要价格配置但配置失败: %w", err)
		}

		// 等待设备处理价格配置
		time.Sleep(100 * time.Millisecond)

		// 重新检查状态
		currentStatus = stateMachine.GetCurrentStatus()
		w.logger.Info("价格配置后重新检查设备状态",
			zap.String("设备ID", device.ID),
			zap.String("新状态", w.getPumpStatusString(currentStatus)))

		// 如果仍然是PUMP NOT PROGRAMMED，则无法继续
		if currentStatus == pump.StatusPumpNotProgrammed {
			return fmt.Errorf("设备配置价格后仍处于PUMP NOT PROGRAMMED状态，无法授权")
		}
	}

	// 📖 协议：只有在RESET状态才能执行authorize
	if currentStatus != pump.StatusReset {
		// 如果不在RESET状态，检查是否可以通过其他方式到达RESET状态
		if currentStatus == pump.StatusFillingCompleted || currentStatus == pump.StatusMaxAmountReached {
			w.logger.Info("设备处于可reset状态，先执行reset命令",
				zap.String("设备ID", device.ID),
				zap.String("当前状态", w.getPumpStatusString(currentStatus)),
				zap.String("协议要求", "Wayne DART: 先RESET再AUTHORIZE"))

			// 执行reset命令 - 使用DART协议标准25ms超时
			resetCommand := api.Command{
				Type:    "reset",
				Timeout: 25 * time.Millisecond,
			}

			resetResult, err := w.executeResetCommand(ctx, device, resetCommand)
			if err != nil {
				return fmt.Errorf("reset命令执行失败: %w", err)
			}

			if resetResult == nil || !resetResult.Success {
				errorMsg := "reset结果无效"
				if resetResult != nil {
					errorMsg = resetResult.Error
				}
				return fmt.Errorf("reset失败: %s", errorMsg)
			}

			w.logger.Info("reset命令执行成功，继续执行authorize命令",
				zap.String("设备ID", device.ID))

			// 短暂等待让设备状态稳定
			time.Sleep(50 * time.Millisecond)
		} else {
			return fmt.Errorf("设备状态(%s)不支持直接授权，且无法通过reset到达可授权状态", w.getPumpStatusString(currentStatus))
		}
	}

	// 执行授权命令 - 使用DART协议标准25ms超时
	authCommand := api.Command{
		Type:    "authorize",
		Timeout: 25 * time.Millisecond,
	}

	authResult, err := w.executeAuthorizeCommand(ctx, device, authCommand)
	if err != nil {
		return fmt.Errorf("授权命令执行失败: %w", err)
	}

	if authResult == nil || !authResult.Success {
		errorMsg := "授权结果无效"
		if authResult != nil {
			errorMsg = authResult.Error
		}
		return fmt.Errorf("授权失败: %s", errorMsg)
	}

	w.logger.Info("设备授权成功",
		zap.String("设备ID", device.ID),
		zap.Any("授权结果", authResult.Data))

	// 等待一小段时间让设备状态稳定
	time.Sleep(50 * time.Millisecond)

	return nil
}

// sendTransactionAndWaitResponse 发送事务并等待响应
func (w *WayneAdapter) sendTransactionAndWaitResponse(ctx context.Context, device *models.Device, cdTransaction pump.CDTransaction, timeout time.Duration) (pump.DCTransaction, error) {
	// 🔒 确保串口通信互斥，防止poll命令并行
	w.serialCommMux.Lock()
	defer w.serialCommMux.Unlock()

	w.logger.Info("开始发送事务并等待响应",
		zap.String("设备ID", device.ID),
		zap.String("事务类型", fmt.Sprintf("CD%d", int(cdTransaction.GetType()))),
		zap.Uint8("设备地址", byte(device.DeviceAddress)),
		zap.Duration("超时时间", timeout))

	// 将pump事务转换为DART帧
	frame, err := w.buildDARTFrameFromTransaction(device, cdTransaction)
	if err != nil {
		return nil, fmt.Errorf("构建DART帧失败: %w", err)
	}

	w.logger.Info("DART帧构建完成",
		zap.String("设备ID", device.ID),
		zap.String("事务类型", fmt.Sprintf("CD%d", int(cdTransaction.GetType()))),
		zap.Uint8("设备地址", byte(device.DeviceAddress)),
		zap.Uint8("TX#", frame.GetTxNumber()),
		zap.String("帧数据(HEX)", fmt.Sprintf("%X", frame.Encode())))

	// 发送帧并等待响应（使用半双工机制避免通道竞争，不加锁版本）
	responseFrame, err := w.sendFrameAndWaitResponseWithoutLock(ctx, frame, timeout)
	if err != nil {
		w.logger.Error("发送DART帧失败",
			zap.String("设备ID", device.ID),
			zap.Error(err))
		return nil, fmt.Errorf("发送DART帧失败: %w", err)
	}

	w.logger.Info("收到DART响应帧",
		zap.String("设备ID", device.ID),
		zap.String("响应帧类型", w.getFrameTypeString(responseFrame)),
		zap.Uint8("响应设备地址", responseFrame.Address),
		zap.Uint8("响应TX#", responseFrame.GetTxNumber()),
		zap.String("响应帧数据", fmt.Sprintf("%X", responseFrame.Encode())))

	// ✅ Wayne DART协议要求：验证TX#同步
	sentTX := frame.GetTxNumber()
	receivedTX := responseFrame.GetTxNumber()

	if err := w.validateTXNumberSync(sentTX, receivedTX, byte(device.DeviceAddress)); err != nil {
		w.logger.Error("TX#同步验证失败",
			zap.String("设备ID", device.ID),
			zap.Uint8("发送TX#", sentTX),
			zap.Uint8("响应TX#", receivedTX),
			zap.Error(err))
		return nil, fmt.Errorf("TX#同步验证失败: %w", err)
	}

	w.logger.Debug("TX#同步验证成功",
		zap.String("设备ID", device.ID),
		zap.Uint8("TX#", sentTX))

	// 根据发送的命令类型和响应帧类型来处理响应
	dcTransaction, err := w.handleTransactionResponse(cdTransaction, responseFrame)
	if err != nil {
		w.logger.Error("处理事务响应失败",
			zap.String("设备ID", device.ID),
			zap.String("发送事务类型", fmt.Sprintf("CD%d", int(cdTransaction.GetType()))),
			zap.String("响应帧类型", w.getFrameTypeString(responseFrame)),
			zap.Error(err))
		return nil, err
	}

	w.logger.Info("事务响应处理成功",
		zap.String("设备ID", device.ID),
		zap.String("发送事务类型", fmt.Sprintf("CD%d", int(cdTransaction.GetType()))),
		zap.String("响应事务类型", fmt.Sprintf("DC%d", int(dcTransaction.GetType()))),
		zap.Uint8("响应状态码", dcTransaction.GetStatusCode()))

	return dcTransaction, nil
}

// sendFrameWithoutLock 发送帧并等待响应（不加锁版本，供已加锁的方法调用）
func (w *WayneAdapter) sendFrameWithoutLock(ctx context.Context, frame *dartline.Frame, timeout time.Duration) (*dartline.Frame, error) {
	if w.serialManager == nil {
		return nil, errors.NewConnectionError("串口管理器未初始化")
	}

	// 编码帧
	frameData := frame.Encode()

	w.logger.Debug("发送DART帧",
		zap.String("帧数据(HEX)", fmt.Sprintf("%X", frameData)),
		zap.Duration("超时时间", timeout))

	// 发送帧
	if err := w.serialManager.SendFrame(ctx, frameData); err != nil {
		return nil, fmt.Errorf("发送帧失败: %w", err)
	}

	// 等待回调接收的响应数据
	responseCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	select {
	case responseData := <-w.receivedDataChan:
		w.logger.Debug("从回调接收到响应数据",
			zap.String("响应数据(HEX)", fmt.Sprintf("%X", responseData)))

		// 解码响应帧
		response, err := dartline.DecodeFrame(responseData)
		if err != nil {
			return nil, fmt.Errorf("解码响应帧失败: %w", err)
		}

		return response, nil

	case <-responseCtx.Done():
		return nil, fmt.Errorf("等待响应超时: %v", timeout)
	}
}

// sendFrameAndWaitResponseWithoutLock 发送帧并等待响应（半双工机制，不加锁版本）
func (w *WayneAdapter) sendFrameAndWaitResponseWithoutLock(ctx context.Context, frame *dartline.Frame, timeout time.Duration) (*dartline.Frame, error) {
	if w.serialManager == nil {
		return nil, errors.NewConnectionError("串口管理器未初始化")
	}

	w.logger.Debug("发送DART帧并等待响应(半双工模式，不加锁)",
		zap.String("帧类型", w.getFrameTypeString(frame)),
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()),
		zap.Duration("超时时间", timeout))

	// 创建专用的响应等待通道
	responseChan := make(chan *dartline.Frame, 1)
	defer close(responseChan)

	// 临时注册响应处理器
	responseHandler := func(receivedFrame *dartline.Frame) {
		select {
		case responseChan <- receivedFrame:
			w.logger.Debug("✅ 半双工响应已接收(不加锁)",
				zap.String("响应类型", w.getFrameTypeString(receivedFrame)),
				zap.Uint8("设备地址", receivedFrame.Address),
				zap.Uint8("TX#", receivedFrame.GetTxNumber()))
		default:
			w.logger.Warn("响应通道已满，丢弃响应(不加锁)")
		}
	}

	// 设置临时响应处理器
	w.setTemporaryResponseHandler(responseHandler)
	defer w.clearTemporaryResponseHandler()

	// 发送帧
	frameData := frame.Encode()
	sendErr := w.serialManager.SendFrame(ctx, frameData)
	if sendErr != nil {
		return nil, fmt.Errorf("发送帧失败: %w", sendErr)
	}

	w.logger.Debug("DART帧已发送，等待响应(不加锁)",
		zap.String("帧数据(HEX)", fmt.Sprintf("%X", frameData)),
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()))

	// ✅ 半双工等待：直接等待下一个收到的帧
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	select {
	case response := <-responseChan:
		w.logger.Info("收到半双工响应(不加锁)",
			zap.String("响应类型", w.getFrameTypeString(response)),
			zap.Uint8("设备地址", response.Address),
			zap.Uint8("TX#", response.GetTxNumber()),
			zap.String("响应数据", fmt.Sprintf("%X", response.Encode())))
		return response, nil
	case <-timeoutCtx.Done():
		w.logger.Warn("半双工响应超时(不加锁)",
			zap.Uint8("设备地址", frame.Address),
			zap.Uint8("TX#", frame.GetTxNumber()),
			zap.Duration("超时时间", timeout))
		return nil, fmt.Errorf("等待半双工响应超时: %w", timeoutCtx.Err())
	}
}

// handleTransactionResponse 根据命令类型处理不同的响应
func (w *WayneAdapter) handleTransactionResponse(cdTransaction pump.CDTransaction, responseFrame *dartline.Frame) (pump.DCTransaction, error) {
	commandType := cdTransaction.GetType()

	w.logger.Debug("处理事务响应",
		zap.String("命令类型", fmt.Sprintf("CD%d", int(commandType))),
		zap.String("响应帧类型", w.getFrameTypeString(responseFrame)),
		zap.String("响应数据(HEX)", fmt.Sprintf("%X", responseFrame.Encode())))

	// 根据Wayne DART协议，不同命令期望不同类型的响应
	switch commandType {
	case pump.TransactionTypeCD1:
		// CD1命令需要根据具体的命令代码判断
		return w.handleCD1Response(cdTransaction, responseFrame)

	case pump.TransactionTypeCD5: // 价格设置
		// CD5价格设置命令期望ACK响应
		return w.handleAckResponse(responseFrame, "price_set")

	case pump.TransactionTypeCD2, pump.TransactionTypeCD3, pump.TransactionTypeCD4: // 预设命令
		// 预设命令期望ACK响应
		return w.handleAckResponse(responseFrame, "preset_accepted")

	case pump.TransactionTypeCD101: // 请求总计数器
		// CD101请求命令期望数据帧响应
		return w.handleDataFrameResponse(responseFrame)

	default:
		// 其他命令默认处理ACK响应
		w.logger.Warn("未知命令类型，默认期望ACK响应",
			zap.String("命令类型", fmt.Sprintf("CD%d", int(commandType))))
		return w.handleAckResponse(responseFrame, "command_accepted")
	}
}

// handleCD1Response 处理CD1命令的响应
func (w *WayneAdapter) handleCD1Response(cdTransaction pump.CDTransaction, responseFrame *dartline.Frame) (pump.DCTransaction, error) {
	// CD1命令根据具体的命令代码有不同的响应类型
	commandCode := cdTransaction.GetCommandCode()

	switch commandCode {
	case byte(pump.CmdReset), byte(pump.CmdAuthorize), byte(pump.CmdStop):
		// 控制命令期望ACK响应
		commandName := w.getCommandName(commandCode)
		return w.handleAckResponse(responseFrame, commandName)

	case byte(pump.CmdReturnFillingInformation):
		// 查询命令期望数据帧响应
		return w.handleDataFrameResponse(responseFrame)

	default:
		// 状态查询等其他命令期望数据帧响应
		return w.handleDataFrameResponse(responseFrame)
	}
}

// handleAckResponse 处理ACK响应
func (w *WayneAdapter) handleAckResponse(responseFrame *dartline.Frame, operation string) (pump.DCTransaction, error) {
	if responseFrame.IsAckFrame() {
		// 创建一个虚拟的成功事务表示ACK响应
		return w.createSuccessTransaction(operation), nil
	} else if responseFrame.IsNakFrame() {
		return nil, fmt.Errorf("设备拒绝命令 (NAK): %s", operation)
	} else if responseFrame.IsDataFrame() {
		w.logger.Warn("期望ACK响应但收到数据帧，尝试解析数据内容", zap.String("操作", operation))
		// 某些设备可能返回数据帧而不是ACK，尝试解析
		return w.handleDataFrameResponse(responseFrame)
	} else {
		return nil, fmt.Errorf("期望ACK响应但收到未知帧类型: %s", w.getFrameTypeString(responseFrame))
	}
}

// handleDataFrameResponse 处理数据帧响应
func (w *WayneAdapter) handleDataFrameResponse(responseFrame *dartline.Frame) (pump.DCTransaction, error) {
	if !responseFrame.IsDataFrame() {
		return nil, fmt.Errorf("期望数据帧，但收到: %s", w.getFrameTypeString(responseFrame))
	}

	// 解析数据帧中的多个DC事务
	dcTransactions, err := w.parseDARTFrameToMultipleTransactions(responseFrame)
	if err != nil {
		return nil, fmt.Errorf("解析数据帧失败: %w", err)
	}

	// 返回第一个事务以保持向后兼容性
	if len(dcTransactions) > 0 {
		return dcTransactions[0], nil
	}

	return nil, fmt.Errorf("数据帧中未解析到任何有效事务")
}

// createSuccessTransaction 创建表示成功的虚拟事务
func (w *WayneAdapter) createSuccessTransaction(operation string) pump.DCTransaction {
	// 创建一个简单的成功事务，用于表示ACK响应
	return &successTransaction{
		operation: operation,
		timestamp: time.Now(),
	}
}

// successTransaction 实现DCTransaction接口的成功事务
type successTransaction struct {
	operation string
	timestamp time.Time
}

func (t *successTransaction) GetType() pump.TransactionType { return pump.TransactionType(0xFF) } // 特殊类型表示成功
func (t *successTransaction) GetLength() byte               { return 0 }
func (t *successTransaction) GetData() []byte               { return []byte(t.operation) }
func (t *successTransaction) GetStatusCode() byte           { return 0x00 } // 0表示成功
func (t *successTransaction) GetTimestamp() time.Time       { return t.timestamp }
func (t *successTransaction) Validate() error               { return nil }
func (t *successTransaction) Decode(data []byte) error      { return nil }                // 成功事务不需要解码
func (t *successTransaction) Encode() []byte                { return []byte{0xFF, 0x00} } // 特殊编码表示成功

// getFrameTypeString 获取帧类型字符串
func (w *WayneAdapter) getFrameTypeString(frame *dartline.Frame) string {
	switch {
	case frame.IsPollFrame():
		return "POLL"
	case frame.IsDataFrame():
		return "DATA"
	case frame.IsAckFrame():
		return "ACK"
	case frame.IsNakFrame():
		return "NAK"
	case frame.IsEotFrame():
		return "EOT"
	default:
		return "UNKNOWN"
	}
}

// getCommandName 获取命令名称
func (w *WayneAdapter) getCommandName(commandCode byte) string {
	switch commandCode {
	case byte(pump.CmdReset):
		return "reset"
	case byte(pump.CmdAuthorize):
		return "authorize"
	case byte(pump.CmdStop):
		return "stop"
	case byte(pump.CmdReturnFillingInformation):
		return "filling_info"
	default:
		return fmt.Sprintf("command_0x%02X", commandCode)
	}
}

// buildDARTFrameFromTransaction 将pump事务转换为DART帧
func (w *WayneAdapter) buildDARTFrameFromTransaction(device *models.Device, cdTransaction pump.CDTransaction) (*dartline.Frame, error) {
	address := byte(device.DeviceAddress)
	// ✅ 双向TX#管理: 使用Master的TX#序列
	txNum := w.getNextMasterSequence(address)

	// 获取事务编码数据
	transactionData := cdTransaction.Encode()

	// 创建DART数据帧
	return dartline.CreateDataFrame(address, txNum, transactionData)
}

// parseDARTFrameToTransaction 将DART响应帧转换为pump事务列表
func (w *WayneAdapter) parseDARTFrameToMultipleTransactions(frame *dartline.Frame) ([]pump.DCTransaction, error) {
	if !frame.IsDataFrame() {
		return nil, fmt.Errorf("期望数据帧，但收到其他类型帧")
	}

	// 检查设备重启标志
	txNumber := frame.GetTxNumber()
	if txNumber == 0 {
		w.logger.Warn("检测到设备重启标志",
			zap.Uint8("设备地址", frame.Address),
			zap.Uint8("TX#", txNumber))
	}

	// 获取帧数据
	frameData := frame.Data
	if len(frameData) < 2 {
		return nil, fmt.Errorf("帧数据长度不足")
	}

	var transactions []pump.DCTransaction
	builder := pump.NewTransactionBuilder()
	dataOffset := 0

	// 解析多个连续的事务
	for dataOffset < len(frameData) {
		if dataOffset+2 > len(frameData) {
			break // 数据不足，无法读取事务头
		}

		// 读取事务类型和长度
		transType := pump.TransactionType(frameData[dataOffset])
		transLength := frameData[dataOffset+1]

		// 检查数据是否足够
		totalTransSize := int(transLength) + 2 // +2 for TRANS and LNG
		remainingData := len(frameData) - dataOffset

		if dataOffset+totalTransSize > len(frameData) {
			w.logger.Error("事务数据不完整",
				zap.Uint8("事务类型", uint8(transType)),
				zap.Uint8("LNG字段长度", transLength),
				zap.Int("总需要字节", totalTransSize),
				zap.Int("剩余数据字节", remainingData),
				zap.Int("数据偏移", dataOffset),
				zap.Int("帧总长度", len(frameData)),
				zap.String("剩余数据HEX", fmt.Sprintf("%X", frameData[dataOffset:])))

			// ✅ 对于DC101，实现宽松的长度处理
			if transType == pump.TransactionTypeDC101 && remainingData >= 6 {
				w.logger.Warn("DC101事务数据长度不足，尝试宽松解析",
					zap.Uint8("事务类型", uint8(transType)),
					zap.Int("期望长度", int(transLength)),
					zap.Int("可用数据", remainingData),
					zap.String("说明", "DC101为变长事务，尝试解析可用数据"))

				// 创建适配的事务数据：只使用实际可用的数据
				availableTransData := frameData[dataOffset:]

				// 修正LNG字段为实际可用数据长度
				if len(availableTransData) >= 2 {
					adjustedData := make([]byte, len(availableTransData))
					copy(adjustedData, availableTransData)
					// 修正LNG字段为实际数据长度-2
					adjustedData[1] = byte(len(availableTransData) - 2)

					w.logger.Info("DC101数据长度已调整",
						zap.Uint8("原始LNG", transLength),
						zap.Uint8("调整后LNG", adjustedData[1]),
						zap.String("调整后数据", fmt.Sprintf("%X", adjustedData)))

					// 尝试解析调整后的数据
					if transaction, err := builder.ParseDC101(adjustedData); err == nil {
						w.logger.Info("DC101宽松解析成功",
							zap.Uint8("设备地址", frame.Address))
						transactions = append(transactions, transaction)
						w.handleDC101Counters(transaction, frame.Address)
					} else {
						w.logger.Warn("DC101宽松解析失败",
							zap.Error(err))
					}
				}
			}
			break
		}

		// 提取当前事务的完整数据
		transactionData := frameData[dataOffset : dataOffset+totalTransSize]

		w.logger.Debug("解析事务",
			zap.Uint8("事务类型", uint8(transType)),
			zap.Uint8("数据长度", transLength),
			zap.String("事务数据(HEX)", fmt.Sprintf("%X", transactionData)))

		// 根据事务类型解析
		var transaction pump.DCTransaction
		var err error

		switch transType {
		case pump.TransactionTypeDC1:
			transaction, err = builder.ParseDC1(transactionData)
			if err == nil {
				w.handleDC1Status(transaction, frame.Address)
			}
		case pump.TransactionTypeDC2:
			transaction, err = builder.ParseDC2(transactionData)
			if err == nil {
				w.handleDC2VolumeAmount(transaction, frame.Address)
			}
		case pump.TransactionTypeDC3:
			transaction, err = builder.ParseDC3(transactionData)
			if err == nil {
				w.handleDC3PriceNozzle(transaction, frame.Address)
			}
		case pump.TransactionTypeDC101:
			transaction, err = builder.ParseDC101(transactionData)
			if err == nil {
				w.handleDC101Counters(transaction, frame.Address)
			}
		default:
			w.logger.Warn("跳过不支持的DC事务类型",
				zap.Uint8("事务类型", uint8(transType)))
			dataOffset += totalTransSize
			continue
		}

		if err != nil {
			w.logger.Error("解析事务失败",
				zap.Uint8("事务类型", uint8(transType)),
				zap.Error(err))
		} else {
			transactions = append(transactions, transaction)
		}

		// 移动到下一个事务
		dataOffset += totalTransSize
	}

	// 处理设备重启后的初始化流程 - 改为同步调用，避免并发问题
	if txNumber == 0 && len(transactions) > 0 {
		w.handleDeviceInitialization(frame.Address, transactions, "数据帧TX#=0")
	}

	return transactions, nil
}

// handleDC1Status 处理DC1泵状态事务
func (w *WayneAdapter) handleDC1Status(transaction pump.DCTransaction, deviceAddr byte) {
	if len(transaction.GetData()) > 0 {
		status := transaction.GetData()[0]
		statusStr := w.decodePumpStatus(status)

		w.logger.Info("DC1 泵状态解析",
			zap.Uint8("设备地址", deviceAddr),
			zap.Uint8("状态码", status),
			zap.String("状态", statusStr))

		// 重要修复：检查TX#是否为0（设备重启）
		// 如果是设备重启，则当前状态可能不可靠，需要重新初始化
		if w.isDeviceJustRestarted(deviceAddr) {
			w.logger.Warn("设备刚重启，状态可能不可靠",
				zap.Uint8("设备地址", deviceAddr),
				zap.String("当前状态", statusStr),
				zap.String("建议", "等待设备初始化完成后再信任状态"))

			// 设备重启后，即使显示COMPLETED，也不应该立即获取交易信息
			if status == 0x05 { // COMPLETED状态
				w.logger.Warn("设备重启后显示COMPLETED状态，可能是残留数据，跳过交易数据收集",
					zap.Uint8("设备地址", deviceAddr),
					zap.String("原因", "TX#=0表示设备重启，需要重新初始化"))
				return
			}
		}

		// 修复：只有在设备稳定且确实完成交易时才收集数据
		if status == 0x05 { // COMPLETED状态
			// 检查设备是否已经正确初始化
			if w.isDeviceProperlyInitialized(deviceAddr) {
				w.logger.Info("检测到加油完成状态，设备已初始化，准备获取交易数据",
					zap.Uint8("设备地址", deviceAddr),
					zap.String("协议说明", "Wayne DART协议规定COMPLETED状态下可以获取交易信息"))

				// 修复：延迟收集，确保设备状态稳定
				go func() {
					// 等待500ms确保设备状态稳定
					time.Sleep(500 * time.Millisecond)

					// 再次确认设备状态
					if w.isDeviceReadyForDataCollection(deviceAddr) {
						w.autoCollectTransactionData(deviceAddr)
					} else {
						w.logger.Warn("设备状态检查失败，跳过交易数据收集",
							zap.Uint8("设备地址", deviceAddr))
					}
				}()
			} else {
				w.logger.Warn("设备未正确初始化，跳过交易数据收集",
					zap.Uint8("设备地址", deviceAddr),
					zap.String("建议", "等待设备初始化完成"))
			}
		}
	}
}

// ✅ Phase 3: 简化设备状态检查
func (w *WayneAdapter) isDeviceJustRestarted(deviceAddr byte) bool {
	w.stateMux.RLock()
	defer w.stateMux.RUnlock()

	state, exists := w.deviceStates[deviceAddr]
	if !exists {
		return false
	}

	// 如果设备在过去10秒内通信过，认为是刚重启
	return time.Since(state.LastSeen) < 10*time.Second
}

// ✅ Phase 3: 简化设备初始化状态检查
func (w *WayneAdapter) isDeviceProperlyInitialized(deviceAddr byte) bool {
	w.stateMux.RLock()
	defer w.stateMux.RUnlock()

	state, exists := w.deviceStates[deviceAddr]
	if !exists {
		return false
	}

	// 设备必须就绪且有基本配置
	return state.IsReady && state.HasBasicConfig
}

// ✅ Phase 3: 简化数据收集就绪检查
func (w *WayneAdapter) isDeviceReadyForDataCollection(deviceAddr byte) bool {
	// 直接检查设备是否就绪
	return w.isDeviceProperlyInitialized(deviceAddr)
}

// 修复：改进自动收集交易数据逻辑
func (w *WayneAdapter) autoCollectTransactionData(deviceAddr byte) {
	ctx := context.Background()

	// 创建虚拟设备对象用于命令执行
	device := &models.Device{
		ID:            fmt.Sprintf("auto_collect_device_%02X", deviceAddr),
		DeviceAddress: int(deviceAddr),
		Type:          models.DeviceTypePump,
	}

	w.logger.Info("开始自动收集交易数据",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("协议依据", "Wayne DART: RETURN FILLING INFORMATION"))

	// 修复：先进行状态确认，确保设备确实处于COMPLETED状态
	statusCommand := api.Command{
		Type: "status_query",
		Parameters: map[string]interface{}{
			"device_address": int(deviceAddr),
		},
		Timeout: 300 * time.Millisecond,
	}

	statusResult, err := w.executeStatusQuery(ctx, device, statusCommand)
	if err != nil {
		w.logger.Error("状态确认失败，终止交易数据收集",
			zap.Uint8("设备地址", deviceAddr),
			zap.Error(err))
		return
	}

	// 修复：检查状态确认结果
	if statusResult == nil || !statusResult.Success {
		w.logger.Warn("状态确认结果异常，终止交易数据收集",
			zap.Uint8("设备地址", deviceAddr),
			zap.String("状态结果", fmt.Sprintf("%+v", statusResult)))
		return
	}

	// 解析状态，确认是否为COMPLETED
	var statusData map[string]interface{}
	if statusResult.Data != nil {
		statusData = statusResult.Data
	} else {
		w.logger.Error("状态数据为空",
			zap.Uint8("设备地址", deviceAddr))
		return
	}

	pumpStatus, hasStatus := statusData["pump_status"]
	if !hasStatus || pumpStatus != "COMPLETED" {
		w.logger.Warn("设备状态已变化，不再是COMPLETED，终止交易数据收集",
			zap.Uint8("设备地址", deviceAddr),
			zap.Any("当前状态", pumpStatus))
		return
	}

	w.logger.Info("状态确认成功，设备确实处于COMPLETED状态，继续收集交易数据",
		zap.Uint8("设备地址", deviceAddr))

	// 修复：使用更长的超时时间，并添加重试机制
	fillingInfoCommand := api.Command{
		Type: "request_filling_info",
		Parameters: map[string]interface{}{
			"device_address": int(deviceAddr),
		},
		Timeout: 800 * time.Millisecond, // 增加超时时间
	}

	// 新增：重试机制
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		w.logger.Info("尝试获取交易信息",
			zap.Uint8("设备地址", deviceAddr),
			zap.Int("尝试次数", attempt),
			zap.Int("最大尝试", maxRetries))

		result, err := w.executeRequestFillingInfo(ctx, device, fillingInfoCommand)

		if err != nil {
			w.logger.Warn("获取交易信息失败",
				zap.Uint8("设备地址", deviceAddr),
				zap.Int("尝试次数", attempt),
				zap.Error(err))

			if attempt < maxRetries {
				// 等待后重试
				time.Sleep(time.Duration(attempt) * 200 * time.Millisecond)
				continue
			} else {
				w.logger.Error("所有尝试都失败，终止交易数据收集",
					zap.Uint8("设备地址", deviceAddr),
					zap.Error(err))
				return
			}
		}

		if result == nil || !result.Success {
			errorMsg := "结果为空"
			if result != nil {
				errorMsg = result.Error
			}

			w.logger.Warn("交易数据收集未成功",
				zap.Uint8("设备地址", deviceAddr),
				zap.Int("尝试次数", attempt),
				zap.String("错误", errorMsg))

			// 修复：分析错误类型，决定是否重试
			if attempt < maxRetries && w.shouldRetryDataCollection(errorMsg) {
				time.Sleep(time.Duration(attempt) * 300 * time.Millisecond)
				continue
			} else {
				w.logger.Error("交易数据收集最终失败",
					zap.Uint8("设备地址", deviceAddr),
					zap.String("最终错误", errorMsg))
				return
			}
		}

		// 成功获取交易数据
		w.logger.Info("成功自动收集交易数据",
			zap.Uint8("设备地址", deviceAddr),
			zap.Int("尝试次数", attempt),
			zap.Any("交易数据", result.Data),
			zap.Time("收集时间", result.Timestamp))

		w.logTransactionSummary(deviceAddr, result.Data)
		return
	}
}

// 新增：判断是否应该重试数据收集
func (w *WayneAdapter) shouldRetryDataCollection(errorMsg string) bool {
	// 如果是ACK响应（期望数据但收到ACK），应该重试
	if strings.Contains(errorMsg, "期望数据帧，但收到: ACK") {
		return true
	}

	// 如果是超时，应该重试
	if strings.Contains(errorMsg, "超时") || strings.Contains(errorMsg, "timeout") {
		return true
	}

	// 如果是请求失败，应该重试
	if strings.Contains(errorMsg, "请求加油信息失败") {
		return true
	}

	// 其他错误不重试
	return false
}

// handleDC2VolumeAmount 处理DC2体积和金额事务
func (w *WayneAdapter) handleDC2VolumeAmount(transaction pump.DCTransaction, deviceAddr byte) {
	data := transaction.GetData()
	if len(data) == 8 {
		volumeData := data[0:4]
		amountData := data[4:8]

		w.logger.Info("DC2 体积和金额解析",
			zap.Uint8("设备地址", deviceAddr),
			zap.String("体积BCD", fmt.Sprintf("%X", volumeData)),
			zap.String("金额BCD", fmt.Sprintf("%X", amountData)))
	}
}

// handleDC3PriceNozzle 处理DC3价格和喷嘴状态事务
func (w *WayneAdapter) handleDC3PriceNozzle(transaction pump.DCTransaction, deviceAddr byte) {
	data := transaction.GetData()
	if len(data) == 4 {
		priceData := data[0:3]
		nozzleStatus := data[3]

		nozzleNumber := nozzleStatus & 0x0F        // 低4位：喷嘴号码
		insertStatus := (nozzleStatus & 0xF0) >> 4 // 高4位：插入状态

		w.logger.Info("DC3 价格和喷嘴状态解析",
			zap.Uint8("设备地址", deviceAddr),
			zap.String("价格BCD", fmt.Sprintf("%X", priceData)),
			zap.Uint8("喷嘴号码", nozzleNumber),
			zap.String("插入状态", w.decodeNozzleInsertStatus(insertStatus)))
	}
}

// handleDC101Counters 处理DC101总计数器事务
func (w *WayneAdapter) handleDC101Counters(transaction pump.DCTransaction, deviceAddr byte) {
	data := transaction.GetData()
	w.logger.Info("DC101 总计数器解析",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("计数器数据", fmt.Sprintf("%X", data)))
}

// decodePumpStatus 解码泵状态
func (w *WayneAdapter) decodePumpStatus(status byte) string {
	switch status {
	case 0x00:
		return "PUMP NOT PROGRAMMED"
	case 0x01:
		return "RESET" // RESET状态对应数据库中的idle状态
	case 0x02:
		return "AUTHORIZED" // 修正：0x02是授权状态
	case 0x04:
		return "FILLING"
	case 0x05:
		return "COMPLETED"
	case 0x06:
		return "SUSPENDED"
	case 0x07:
		return "ERROR"
	default:
		return fmt.Sprintf("UNKNOWN(0x%02X)", status)
	}
}

// decodeNozzleInsertStatus 解码喷嘴插入状态
func (w *WayneAdapter) decodeNozzleInsertStatus(status byte) string {
	switch status {
	case 0x00:
		return "插入(INSERTED)"
	case 0x01:
		return "拔出(REMOVED)"
	default:
		return fmt.Sprintf("未知(0x%X)", status)
	}
}

// handleDeviceInitialization 处理设备初始化流程
// 适用场景：
// 1. 设备重启后 (TX#=0)
// 2. FCC首次连接设备
// 3. 设备状态需要重新初始化的场景
func (w *WayneAdapter) handleDeviceInitialization(deviceAddr byte, transactions []pump.DCTransaction, context string) {
	// ✅ 重置双向TX#序列（重启或首次连接都需要）
	w.resetBothSequences(deviceAddr)
	w.logger.Info("设备初始化：重置双向TX#序列",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("初始化场景", context),
		zap.String("说明", "重置Master和Slave的TX#序列，确保双向通信同步"))

	// ✅ 解析设备当前状态
	deviceStatus := w.analyzeDeviceStatus(transactions)

	w.logger.Info("设备状态分析完成",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("初始化场景", context),
		zap.Any("设备状态", deviceStatus))

	// ✅ 智能决策：根据设备状态决定是否需要配置
	if w.needsConfiguration(deviceStatus) {
		w.logger.Info("设备需要配置，开始发送基础配置",
			zap.Uint8("设备地址", deviceAddr),
			zap.String("初始化场景", context))
		w.sendBasicConfiguration(deviceAddr)
	} else {
		w.logger.Info("设备状态良好，跳过配置步骤",
			zap.Uint8("设备地址", deviceAddr),
			zap.String("初始化场景", context))
	}

	// 标记设备为可用状态
	w.markDeviceReady(deviceAddr)

	w.logger.Info("设备初始化完成",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("初始化场景", context))
}

// handleFirstTimeDeviceConnection 处理首次连接到设备的初始化
// 当FCC首次发现或连接到设备时调用
// 实现Wayne DART协议标准初始化流程
func (w *WayneAdapter) handleFirstTimeDeviceConnection(deviceAddr byte) {
	w.logger.Info("检测到首次设备连接，开始Wayne DART标准初始化流程",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("协议依据", "Wayne DART: RETURN STATUS command when pump is connected first time"))

	// ✅ 按照Wayne DART协议规范的初始化序列
	ctx := context.Background()
	device := &models.Device{
		ID:            fmt.Sprintf("first_connect_device_%02X", deviceAddr),
		DeviceAddress: int(deviceAddr),
		Type:          models.DeviceTypePump,
	}

	// 步骤1: 重置TX#序列（协议层初始化）
	w.logger.Info("步骤1: 重置TX#序列",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("协议说明", "TX# initiated to 0 after restart of protocol"))
	w.resetBothSequences(deviceAddr)

	// 步骤2: 发送RETURN STATUS命令（Wayne DART标准要求）
	w.logger.Info("步骤2: 发送RETURN STATUS命令",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("协议说明", "Wayne DART: first time connection requires RETURN STATUS"))

	statusCommand := api.Command{
		Type: "status_query", // 对应Wayne DART的RETURN STATUS (CD1-00H)
		Parameters: map[string]interface{}{
			"device_address": int(deviceAddr),
		},
		Timeout: 200 * time.Millisecond,
	}

	statusResult, err := w.executeQueryCommand(ctx, device, statusCommand)
	var transactions []pump.DCTransaction

	if err != nil {
		w.logger.Warn("RETURN STATUS命令失败，但继续初始化流程",
			zap.Uint8("设备地址", deviceAddr),
			zap.Error(err),
			zap.String("说明", "可能设备尚未准备好，后续会重试"))
		transactions = []pump.DCTransaction{} // 空事务列表
	} else if statusResult != nil && statusResult.Success {
		w.logger.Info("RETURN STATUS命令成功",
			zap.Uint8("设备地址", deviceAddr),
			zap.Any("状态数据", statusResult.Data))
		transactions = []pump.DCTransaction{} // 暂时使用空列表，后续可以改进
	} else {
		w.logger.Warn("RETURN STATUS命令无效结果",
			zap.Uint8("设备地址", deviceAddr))
		transactions = []pump.DCTransaction{}
	}

	// 步骤3: 执行Wayne DART标准初始化流程
	w.logger.Info("步骤3: 执行完整初始化流程",
		zap.Uint8("设备地址", deviceAddr))
	w.handleDeviceInitialization(deviceAddr, transactions, "首次连接-Wayne DART标准")
}

// onDataReceived 数据接收回调函数
func (w *WayneAdapter) onDataReceived(data []byte) {
	w.dataBufferMux.Lock()
	defer w.dataBufferMux.Unlock()

	// 记录接收前的状态
	beforeBufferSize := len(w.dataBuffer)

	// 将新数据追加到缓冲区
	w.dataBuffer = append(w.dataBuffer, data...)

	w.logger.Info("适配器收到串口数据",
		zap.Int("新数据字节", len(data)),
		zap.String("新数据(HEX)", fmt.Sprintf("%X", data)),
		zap.Int("接收前缓冲区", beforeBufferSize),
		zap.Int("接收后缓冲区", len(w.dataBuffer)),
		zap.String("完整缓冲区(HEX)", fmt.Sprintf("%X", w.dataBuffer)))

	// 尝试从缓冲区提取完整的DART帧
	frames := w.extractCompleteFrames()

	w.logger.Info("帧提取结果",
		zap.Int("提取帧数", len(frames)),
		zap.Int("提取后剩余缓冲", len(w.dataBuffer)))

	for i, frame := range frames {
		w.logger.Info("发送DART帧到处理通道",
			zap.Int("帧序号", i+1),
			zap.Int("帧长度", len(frame)),
			zap.String("帧数据(HEX)", fmt.Sprintf("%X", frame)))

		// 非阻塞方式发送完整帧到通道
		select {
		case w.receivedDataChan <- frame:
			w.logger.Debug("帧已成功发送到处理通道",
				zap.Int("帧序号", i+1))
		default:
			w.logger.Warn("适配器接收缓冲区已满，丢弃完整帧",
				zap.Int("帧序号", i+1),
				zap.String("丢弃帧数据", fmt.Sprintf("%X", frame)))
		}
	}
}

// extractCompleteFrames 从缓冲区提取完整的DART帧
// ✅ 修复3: 解决帧提取逻辑错误，确保正确识别独立帧
func (w *WayneAdapter) extractCompleteFrames() [][]byte {
	var frames [][]byte

	w.logger.Debug("开始帧提取",
		zap.Int("缓冲区大小", len(w.dataBuffer)),
		zap.String("缓冲区数据", fmt.Sprintf("%X", w.dataBuffer)))

	// 🔄 持续提取，直到没有完整帧
	for len(w.dataBuffer) >= 3 { // DART最小帧长度: ADR + CTRL + SF

		// 验证起始位置是否为有效设备地址
		candidateAddr := w.dataBuffer[0]
		if candidateAddr < 0x50 || candidateAddr > 0x6F {
			// 无效地址，向前搜索有效帧头
			found := false
			for i := 1; i < len(w.dataBuffer)-2; i++ {
				if w.dataBuffer[i] >= 0x50 && w.dataBuffer[i] <= 0x6F {
					w.logger.Debug("丢弃无效数据，找到可能的帧头",
						zap.String("丢弃数据", fmt.Sprintf("%X", w.dataBuffer[0:i])),
						zap.Uint8("新帧头地址", w.dataBuffer[i]))
					w.dataBuffer = w.dataBuffer[i:]
					found = true
					break
				}
			}
			if !found {
				// 整个缓冲区都没有有效帧头
				w.logger.Warn("缓冲区无有效帧头，清空缓冲区",
					zap.String("丢弃数据", fmt.Sprintf("%X", w.dataBuffer)))
				w.dataBuffer = w.dataBuffer[:0]
				break
			}
			continue
		}

		// 🎯 查找停止标志 SF (0xFA)，处理DLE转义
		sfIndex := w.findStopFlag(0)

		if sfIndex == -1 {
			// 没有找到完整帧，等待更多数据
			w.logger.Debug("未找到完整帧，等待更多数据",
				zap.Int("当前缓冲区大小", len(w.dataBuffer)),
				zap.String("当前数据", fmt.Sprintf("%X", w.dataBuffer)))

			// 防止缓冲区无限增长
			if len(w.dataBuffer) > 1024 {
				w.logger.Warn("数据缓冲区过大，执行清理",
					zap.Int("缓冲区大小", len(w.dataBuffer)))
				// 保留最后256字节，防止丢失部分有效帧
				if len(w.dataBuffer) > 256 {
					w.dataBuffer = w.dataBuffer[len(w.dataBuffer)-256:]
				}
			}
			break
		}

		// ✅ 提取完整帧 (包含SF)
		frameLength := sfIndex + 1
		frameData := make([]byte, frameLength)
		copy(frameData, w.dataBuffer[0:frameLength])

		w.logger.Debug("提取到候选帧",
			zap.Int("帧长度", frameLength),
			zap.String("帧数据", fmt.Sprintf("%X", frameData)),
			zap.Int("SF位置", sfIndex))

		// 🗑️ 从缓冲区移除已提取的帧
		w.dataBuffer = w.dataBuffer[frameLength:]

		// 验证帧的基本有效性
		if w.isValidFrameCandidate(frameData) {
			frames = append(frames, frameData)

			w.logger.Info("成功提取有效DART帧",
				zap.Int("帧长度", len(frameData)),
				zap.String("帧数据", fmt.Sprintf("%X", frameData)),
				zap.Int("剩余缓冲", len(w.dataBuffer)))
		} else {
			w.logger.Warn("丢弃无效帧候选",
				zap.String("帧数据", fmt.Sprintf("%X", frameData)),
				zap.String("原因", "帧验证失败"))
		}
	}

	w.logger.Debug("帧提取完成",
		zap.Int("提取帧数", len(frames)),
		zap.Int("剩余缓冲", len(w.dataBuffer)))

	return frames
}

// 查找停止标志，处理DLE转义
func (w *WayneAdapter) findStopFlag(startPos int) int {
	for i := startPos + 2; i < len(w.dataBuffer); i++ { // 至少需要ADR+CTRL，从第3字节开始
		if w.dataBuffer[i] == 0xFA {
			// 检查是否被DLE转义
			if i > 0 && w.dataBuffer[i-1] == 0x10 {
				// DLE + SF 是转义序列，继续查找
				continue
			}
			// 找到真正的SF
			return i
		}
	}
	return -1
}

// ✅ 验证帧候选的基本有效性
func (w *WayneAdapter) isValidFrameCandidate(frameData []byte) bool {

	if len(frameData) < 3 {
		return false
	}

	// 验证地址范围
	address := frameData[0]
	if address < 0x50 || address > 0x6F {
		return false
	}

	// 验证命令字节格式
	command := frameData[1]
	commandType := command & 0xF0
	_ = command & 0x0F // txNumber (未使用，但保留以备将来使用)
	validCommands := []byte{0x20, 0x30, 0x50, 0x70, 0xC0, 0xE0} // POLL, DATA, NAK, EOT, ACK, ACKPOLL

	isValidCommand := false
	_ = "UNKNOWN" // commandName (未使用，但保留以备将来使用)
	for _, validCmd := range validCommands {
		if commandType == validCmd {
			isValidCommand = true
			// 注释掉未使用的代码，但保留逻辑以备将来使用
			// switch validCmd {
			// case 0x20:
			// 	commandName = "POLL"
			// case 0x30:
			// 	commandName = "DATA"
			// case 0x50:
			// 	commandName = "NAK"
			// case 0x70:
			// 	commandName = "EOT"
			// case 0xC0:
			// 	commandName = "ACK"
			// case 0xE0:
			// 	commandName = "ACKPOLL"
			// }
			break
		}
	}

	if !isValidCommand {
		return false
	}

	// 验证最后一字节是SF
	lastByte := frameData[len(frameData)-1]
	if lastByte != 0xFA {
		return false
	}


	return true
}

// 辅助函数：取最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// ✅ Phase 2: 修改命令发送等待逻辑 - 使用结构化Key和context控制
func (w *WayneAdapter) sendFrameAndWaitResponse(ctx context.Context, frame *dartline.Frame, timeout time.Duration) (*dartline.Frame, error) {
	if w.serialManager == nil {
		return nil, errors.NewConnectionError("串口管理器未初始化")
	}

	w.logger.Debug("发送DART帧并等待响应(半双工模式)",
		zap.String("帧类型", w.getFrameTypeString(frame)),
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()),
		zap.Duration("超时时间", timeout))

	// ✅ 半双工模式：使用全局互斥锁确保严格的发送-等待-接收循环
	w.serialCommMux.Lock()
	defer w.serialCommMux.Unlock()

	// 创建专用的响应等待通道
	responseChan := make(chan *dartline.Frame, 1)
	defer close(responseChan)

	// 临时注册响应处理器
	responseHandler := func(receivedFrame *dartline.Frame) {
		select {
		case responseChan <- receivedFrame:
			w.logger.Debug("✅ 半双工响应已接收",
				zap.String("响应类型", w.getFrameTypeString(receivedFrame)),
				zap.Uint8("设备地址", receivedFrame.Address),
				zap.Uint8("TX#", receivedFrame.GetTxNumber()))
		default:
			w.logger.Warn("响应通道已满，丢弃响应")
		}
	}

	// 设置临时响应处理器
	w.setTemporaryResponseHandler(responseHandler)
	defer w.clearTemporaryResponseHandler()

	// 发送帧
	frameData := frame.Encode()
	sendErr := w.serialManager.SendFrame(ctx, frameData)
	if sendErr != nil {
		return nil, fmt.Errorf("发送帧失败: %w", sendErr)
	}

	w.logger.Debug("DART帧已发送，等待响应",
		zap.String("帧数据(HEX)", fmt.Sprintf("%X", frameData)),
		zap.Uint8("设备地址", frame.Address),
		zap.Uint8("TX#", frame.GetTxNumber()))

	// ✅ 半双工等待：直接等待下一个收到的帧
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	select {
	case response := <-responseChan:
		w.logger.Debug("收到半双工响应",
			zap.String("响应类型", w.getFrameTypeString(response)),
			zap.Uint8("设备地址", response.Address),
			zap.Uint8("TX#", response.GetTxNumber()),
			zap.String("响应数据", fmt.Sprintf("%X", response.Encode())))
		return response, nil
	case <-timeoutCtx.Done():
		w.logger.Warn("半双工响应超时",
			zap.Uint8("设备地址", frame.Address),
			zap.Uint8("TX#", frame.GetTxNumber()),
			zap.Duration("超时时间", timeout))
		return nil, fmt.Errorf("等待半双工响应超时: %w", timeoutCtx.Err())
	}
}

// setTemporaryResponseHandler 设置临时响应处理器
func (w *WayneAdapter) setTemporaryResponseHandler(handler func(*dartline.Frame)) {
	w.tempHandlerMux.Lock()
	defer w.tempHandlerMux.Unlock()
	w.temporaryResponseHandler = handler
}

// clearTemporaryResponseHandler 清除临时响应处理器
func (w *WayneAdapter) clearTemporaryResponseHandler() {
	w.tempHandlerMux.Lock()
	defer w.tempHandlerMux.Unlock()
	w.temporaryResponseHandler = nil
}

// parseStatusResponseWithMultiTransaction 使用多事务解析的状态响应处理
func (w *WayneAdapter) parseStatusResponseWithMultiTransaction(response *dartline.Frame, device *models.Device) (map[string]interface{}, error) {
	result := map[string]interface{}{
		"status":         "online",
		"address":        response.Address,
		"device_id":      device.ID,
		"raw_data":       fmt.Sprintf("%X", response.Encode()),
		"timestamp":      time.Now(),
		"tx_number":      response.GetTxNumber(),
		"device_restart": response.GetTxNumber() == 0,
	}

	// 如果是数据帧，使用多事务解析
	if response.IsDataFrame() {
		result["frame_type"] = "data"

		// 解析多个DC事务
		transactions, err := w.parseDARTFrameToMultipleTransactions(response)
		if err != nil {
			return nil, fmt.Errorf("多事务解析失败: %w", err)
		}

		result["transactions_count"] = len(transactions)
		result["transactions"] = make([]map[string]interface{}, 0, len(transactions))

		// 提取具体的状态信息
		var pumpStatus string
		var volumeAmount map[string]interface{}
		var priceNozzle map[string]interface{}

		for _, trans := range transactions {
			transInfo := map[string]interface{}{
				"type":   fmt.Sprintf("DC%d", int(trans.GetType())),
				"length": trans.GetLength(),
				"data":   fmt.Sprintf("%X", trans.GetData()),
			}

			switch trans.GetType() {
			case pump.TransactionTypeDC1:
				// 泵状态
				if len(trans.GetData()) > 0 {
					statusCode := trans.GetData()[0]
					pumpStatus = w.decodePumpStatus(statusCode)
					transInfo["status_code"] = statusCode
					transInfo["status_text"] = pumpStatus
				}
			case pump.TransactionTypeDC2:
				// 体积和金额
				data := trans.GetData()
				if len(data) == 8 {
					volumeAmount = map[string]interface{}{
						"volume_bcd":   fmt.Sprintf("%X", data[0:4]),
						"amount_bcd":   fmt.Sprintf("%X", data[4:8]),
						"volume_value": 0.0, // TODO: 实现BCD解码
						"amount_value": 0.0, // TODO: 实现BCD解码
					}
					transInfo["volume_amount"] = volumeAmount
				}
			case pump.TransactionTypeDC3:
				// 价格和喷嘴状态
				data := trans.GetData()
				if len(data) == 4 {
					priceData := data[0:3]
					nozzleStatus := data[3]
					nozzleNumber := nozzleStatus & 0x0F
					insertStatus := (nozzleStatus & 0xF0) >> 4

					priceNozzle = map[string]interface{}{
						"price_bcd":     fmt.Sprintf("%X", priceData),
						"price_text":    "3.333元/升", // TODO: 实现BCD解码
						"nozzle_number": nozzleNumber,
						"insert_status": w.decodeNozzleInsertStatus(insertStatus),
					}
					transInfo["price_nozzle"] = priceNozzle
				}
			}

			result["transactions"] = append(result["transactions"].([]map[string]interface{}), transInfo)
		}

		// 汇总状态信息到根级别，便于上层业务使用
		if pumpStatus != "" {
			result["pump_status"] = pumpStatus
		}
		if volumeAmount != nil {
			result["current_transaction"] = volumeAmount
		}
		if priceNozzle != nil {
			result["nozzle_info"] = priceNozzle
		}

		// 设备重启处理提示
		if response.GetTxNumber() == 0 {
			result["restart_detected"] = true
			result["recommended_actions"] = []string{
				"发送RESET命令",
				"配置价格设置",
				"重新查询状态确认",
			}
		}

	} else {
		// 非数据帧的简单处理
		if response.IsAckFrame() {
			result["frame_type"] = "ack"
			result["device_status"] = "acknowledged"
		} else if response.IsNakFrame() {
			result["frame_type"] = "nak"
			result["device_status"] = "negative_acknowledgment"
		} else {
			result["frame_type"] = "unknown"
			result["device_status"] = "unknown"
		}
	}

	return result, nil
}

// parseStatusResponse 解析状态响应（简单版本，用作回退）
func (w *WayneAdapter) parseStatusResponse(response *dartline.Frame) map[string]interface{} {
	result := map[string]interface{}{
		"status":    "online",
		"address":   response.Address,
		"raw_data":  fmt.Sprintf("%X", response.Encode()),
		"timestamp": time.Now(),
	}

	// 根据帧类型设置设备状态
	if response.IsAckFrame() {
		result["device_status"] = "acknowledged"
	} else if response.IsDataFrame() {
		result["device_status"] = "data_received"
	} else if response.IsNakFrame() {
		result["device_status"] = "negative_acknowledgment"
	} else {
		result["device_status"] = "unknown"
	}

	return result
}

// getPortName 获取端口名称（用于生成设备ID）
func (w *WayneAdapter) getPortName() string {
	if w.config.Connection.SerialPort != "" {
		// 提取端口号，如从"COM7"提取"7"
		portName := w.config.Connection.SerialPort
		if len(portName) > 3 && portName[:3] == "COM" {
			return portName[3:]
		}
		return portName
	}
	return "unknown"
}

// 新增：记录交易汇总信息
func (w *WayneAdapter) logTransactionSummary(deviceAddr byte, transactionData map[string]interface{}) {
	summary := map[string]interface{}{
		"device_addr": fmt.Sprintf("0x%02X", deviceAddr),
		"timestamp":   time.Now().Format(time.RFC3339),
		"event_type":  "transaction_completed",
	}

	// 提取关键交易信息
	if volume, exists := transactionData["volume"]; exists {
		summary["volume"] = volume
	}
	if amount, exists := transactionData["amount"]; exists {
		summary["amount"] = amount
	}
	if price, exists := transactionData["price"]; exists {
		summary["price"] = price
	}
	if nozzleNumber, exists := transactionData["nozzle_number"]; exists {
		summary["nozzle"] = nozzleNumber
	}

	w.logger.Info("交易完成汇总",
		zap.Uint8("设备地址", deviceAddr),
		zap.Any("交易汇总", summary),
		zap.String("说明", "Wayne DART协议自动收集的交易数据"))
}

// 新增：检查设备是否正在初始化
func (w *WayneAdapter) isDeviceInitializing(deviceAddr byte) bool {
	// ✅ Phase 3: 简化设备初始化状态检查
	// 不再追踪复杂的初始化状态，简化为就绪检查
	return false // 简化：不再有复杂的初始化过程
}

// 新增：判断命令是否为非关键命令（可以等待初始化完成的命令）
func (w *WayneAdapter) isNonCriticalCommand(commandType string) bool {
	// 非关键命令：可以等待初始化完成再执行
	nonCriticalCommands := map[string]bool{
		"authorize":              true, // 授权
		"status_query":           true, // 状态查询
		"pump_status_poll":       true, // 泵状态轮询
		"request_total_counters": true, // 请求总计数器
		"request_filling_info":   true, // 请求加油信息
		"preset_volume":          true, // 预设体积
		"preset_amount":          true, // 预设金额
		"configure_nozzles":      true, // 配置喷嘴
	}

	return nonCriticalCommands[commandType]
}

// 新增：等待设备初始化完成
func (w *WayneAdapter) waitForInitializationComplete(deviceAddr byte, timeout time.Duration) error {
	startTime := time.Now()

	for time.Since(startTime) < timeout {
		if !w.isDeviceInitializing(deviceAddr) {
			w.logger.Info("设备初始化完成，继续执行命令",
				zap.Uint8("设备地址", deviceAddr),
				zap.Duration("等待时间", time.Since(startTime)))
			return nil
		}

		// 每100ms检查一次
		time.Sleep(100 * time.Millisecond)
	}

	return fmt.Errorf("设备初始化超时，等待时间: %v", timeout)
}

// executeConfigureNozzles 执行配置喷嘴命令(CD2)
func (w *WayneAdapter) executeConfigureNozzles(ctx context.Context, device *models.Device, command api.Command) (*api.CommandResult, error) {
	builder := pump.NewTransactionBuilder()

	// 从命令参数中获取喷嘴配置，如果没有则使用默认配置
	nozzleNumbers, err := w.extractNozzleNumbersFromCommand(command)
	if err != nil {
		// 使用固定的默认配置：0x01和0x02两把枪
		nozzleNumbers = []byte{0x01, 0x02}
		w.logger.Info("使用默认喷嘴配置",
			zap.String("设备ID", device.ID),
			zap.String("喷嘴号码", fmt.Sprintf("%X", nozzleNumbers)))
	}

	w.logger.Info("执行喷嘴配置",
		zap.String("设备ID", device.ID),
		zap.String("喷嘴号码", fmt.Sprintf("%X", nozzleNumbers)),
		zap.Int("喷嘴数量", len(nozzleNumbers)))

	// 构建CD2喷嘴配置事务
	cdTransaction, err := builder.BuildCD2(nozzleNumbers)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("构建CD2喷嘴配置事务失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 发送事务并等待响应
	response, err := w.sendTransactionAndWaitResponse(ctx, device, cdTransaction, 200*time.Millisecond)
	if err != nil {
		return &api.CommandResult{
			Success:   false,
			Error:     fmt.Sprintf("喷嘴配置失败: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 解析响应
	result := map[string]interface{}{
		"device_id":      device.ID,
		"command_type":   "configure_nozzles",
		"nozzle_numbers": nozzleNumbers,
		"nozzle_count":   len(nozzleNumbers),
		"data_source":    "dart_protocol",
		"response_data":  fmt.Sprintf("%X", response.GetData()),
	}

	return &api.CommandResult{
		Success:   true,
		Data:      result,
		Timestamp: time.Now(),
	}, nil
}

// extractNozzleNumbersFromCommand 从命令参数中提取喷嘴号码
func (w *WayneAdapter) extractNozzleNumbersFromCommand(command api.Command) ([]byte, error) {
	// 检查命令参数中是否有喷嘴配置信息
	if command.Parameters != nil {
		if nozzlesParam, exists := command.Parameters["nozzles"]; exists {
			// 尝试解析喷嘴参数
			switch nozzles := nozzlesParam.(type) {
			case []interface{}:
				var result []byte
				for i, nozzleVal := range nozzles {
					var nozzleNum byte
					switch val := nozzleVal.(type) {
					case int:
						nozzleNum = byte(val)
					case float64:
						nozzleNum = byte(val)
					case string:
						// 尝试解析十六进制字符串
						if len(val) == 2 && val[:2] == "0x" {
							// 0x01 格式
							if parsed, err := fmt.Sscanf(val, "0x%02x", &nozzleNum); err != nil || parsed != 1 {
								return nil, fmt.Errorf("喷嘴参数[%d]解析失败: %s", i, val)
							}
						} else {
							// 普通数字字符串
							if parsed, err := fmt.Sscanf(val, "%d", &nozzleNum); err != nil || parsed != 1 {
								return nil, fmt.Errorf("喷嘴参数[%d]解析失败: %s", i, val)
							}
						}
					default:
						return nil, fmt.Errorf("喷嘴参数[%d]类型不支持", i)
					}

					// 验证喷嘴号码范围
					if nozzleNum < 1 || nozzleNum > 15 {
						return nil, fmt.Errorf("喷嘴号码[%d]超出有效范围(1-15): %d", i, nozzleNum)
					}

					result = append(result, nozzleNum)
				}
				return result, nil

			case []byte:
				// 直接的字节数组
				for i, nozzle := range nozzles {
					if nozzle < 1 || nozzle > 15 {
						return nil, fmt.Errorf("喷嘴号码[%d]超出有效范围(1-15): %d", i, nozzle)
					}
				}
				return nozzles, nil
			}
		}
	}

	return nil, fmt.Errorf("命令参数中未找到喷嘴配置信息")
}

// 新增：设备状态分析
func (w *WayneAdapter) analyzeDeviceStatus(transactions []pump.DCTransaction) *DeviceStatus {
	status := &DeviceStatus{
		PumpStatus:   "UNKNOWN",
		PriceConfig:  nil,
		NozzleConfig: nil,
		NeedsReset:   false,
		IsConfigured: false,
	}

	for _, trans := range transactions {
		switch trans.GetType() {
		case pump.TransactionTypeDC1:
			if len(trans.GetData()) > 0 {
				statusCode := trans.GetData()[0]
				status.PumpStatus = w.decodePumpStatus(statusCode)

				// 判断是否需要重置
				switch statusCode {
				case 0x00: // PUMP NOT PROGRAMMED
					status.NeedsReset = true
				case 0x05: // FILLING COMPLETED
					status.NeedsReset = true
				case 0x04: // FILLING (异常状态)
					status.NeedsReset = true
				}
			}
		case pump.TransactionTypeDC3:
			// DC3包含价格和喷嘴信息
			status.PriceConfig = map[string]interface{}{
				"has_price_data": true,
				"data_length":    len(trans.GetData()),
			}
			status.NozzleConfig = map[string]interface{}{
				"has_nozzle_data": true,
			}
		}
	}

	// 简单判断是否已配置
	status.IsConfigured = status.PriceConfig != nil && status.NozzleConfig != nil

	return status
}

// 新增：判断是否需要配置
func (w *WayneAdapter) needsConfiguration(status *DeviceStatus) bool {
	// 简单判断：只有明确需要配置才介入
	return status.PumpStatus == "PUMP NOT PROGRAMMED" &&
		(status.PriceConfig == nil || status.NozzleConfig == nil)
}

// 新增：发送基本配置
func (w *WayneAdapter) sendBasicConfiguration(deviceAddr byte) {
	w.logger.Info("开始发送基本配置",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("说明", "利用Phase 2的智能响应机制"))

	// ✅ 利用Phase 2的智能响应：发送后不强求立即成功
	if w.needsPriceConfig(deviceAddr) {
		w.sendPriceConfigurationCommand(deviceAddr) // 接受ACK或DATA
	}

	if w.needsNozzleConfig(deviceAddr) {
		w.sendNozzleConfigurationCommand(deviceAddr) // 接受ACK或DATA
	}
}

// 新增：检查是否需要价格配置
func (w *WayneAdapter) needsPriceConfig(deviceAddr byte) bool {
	// 简化逻辑：默认需要价格配置
	return true
}

// 新增：检查是否需要喷嘴配置
func (w *WayneAdapter) needsNozzleConfig(deviceAddr byte) bool {
	// 简化逻辑：默认需要喷嘴配置
	return true
}

// ✅ 为特定设备发送价格配置（Wayne DART协议要求）
func (w *WayneAdapter) sendPriceConfigurationForDevice(ctx context.Context, device *models.Device) error {
	w.logger.Info("为设备发送价格配置",
		zap.String("设备ID", device.ID),
		zap.String("协议要求", "Wayne DART: PUMP NOT PROGRAMMED状态需要接收价格"))

	priceCommand := api.Command{
		Type: "set_price",
		Parameters: map[string]interface{}{
			"device_address": device.DeviceAddress,
			"prices":         []string{"1.333", "2.333", "5.333", "6.333"},
		},
		Timeout: 300 * time.Millisecond,
	}

	result, err := w.executeSetPrice(ctx, device, priceCommand)
	if err != nil {
		return fmt.Errorf("价格配置命令执行失败: %w", err)
	}

	if result == nil || !result.Success {
		errorMsg := "价格配置结果无效"
		if result != nil {
			errorMsg = result.Error
		}
		return fmt.Errorf("价格配置失败: %s", errorMsg)
	}

	// ✅ 更新状态机，标记价格已接收
	deviceAddr := byte(device.DeviceAddress)
	stateMachine := w.getOrCreateStateMachine(deviceAddr)
	stateMachine.SetPricesReceived(true)

	w.logger.Info("设备价格配置成功",
		zap.String("设备ID", device.ID),
		zap.Any("配置结果", result.Data))

	return nil
}

// 新增：发送价格配置命令
func (w *WayneAdapter) sendPriceConfigurationCommand(deviceAddr byte) {
	ctx := context.Background()
	device := &models.Device{
		ID:            fmt.Sprintf("config_device_%02X", deviceAddr),
		DeviceAddress: int(deviceAddr),
		Type:          models.DeviceTypePump,
	}

	priceCommand := api.Command{
		Type: "set_price",
		Parameters: map[string]interface{}{
			"device_address": int(deviceAddr),
			"prices":         []string{"1.333", "2.333", "5.333", "6.333"},
		},
		Timeout: 300 * time.Millisecond,
	}

	_, err := w.executeSetPrice(ctx, device, priceCommand)
	if err != nil {
		w.logger.Debug("价格配置命令发送失败（预期可能失败）",
			zap.Uint8("设备地址", deviceAddr),
			zap.Error(err))
	} else {
		w.logger.Debug("价格配置命令已发送",
			zap.Uint8("设备地址", deviceAddr))
	}
}

// 新增：发送喷嘴配置命令
func (w *WayneAdapter) sendNozzleConfigurationCommand(deviceAddr byte) {
	ctx := context.Background()
	device := &models.Device{
		ID:            fmt.Sprintf("config_device_%02X", deviceAddr),
		DeviceAddress: int(deviceAddr),
		Type:          models.DeviceTypePump,
	}

	nozzleCommand := api.Command{
		Type: "configure_nozzles",
		Parameters: map[string]interface{}{
			"device_address": int(deviceAddr),
			"nozzles":        []int{1, 2},
		},
		Timeout: 300 * time.Millisecond,
	}

	_, err := w.executeConfigureNozzles(ctx, device, nozzleCommand)
	if err != nil {
		w.logger.Debug("喷嘴配置命令发送失败（预期可能失败）",
			zap.Uint8("设备地址", deviceAddr),
			zap.Error(err))
	} else {
		w.logger.Debug("喷嘴配置命令已发送",
			zap.Uint8("设备地址", deviceAddr))
	}
}

// 新增：标记设备为就绪状态
func (w *WayneAdapter) markDeviceReady(deviceAddr byte) {
	w.stateMux.Lock()
	defer w.stateMux.Unlock()

	// 获取或创建设备状态
	state, exists := w.deviceStates[deviceAddr]
	if !exists {
		state = &DeviceState{
			Address: deviceAddr,
		}
		w.deviceStates[deviceAddr] = state
	}

	// 更新状态
	state.LastSeen = time.Now()
	state.IsReady = true
	state.HasBasicConfig = true
	state.LastStatus = "READY"

	w.logger.Info("设备已标记为就绪",
		zap.Uint8("设备地址", deviceAddr),
		zap.Time("最后通信", state.LastSeen))
}

// ✅ 使用状态机：获取或创建设备状态机
func (w *WayneAdapter) getOrCreateStateMachine(deviceAddr byte) pump.PumpStateMachineInterface {
	w.stateMachinesMux.Lock()
	defer w.stateMachinesMux.Unlock()

	// 检查是否已存在状态机
	if stateMachine, exists := w.deviceStateMachines[deviceAddr]; exists {
		return stateMachine
	}

	// 创建新的状态机
	stateMachine := pump.NewPumpStateMachine()
	w.deviceStateMachines[deviceAddr] = stateMachine

	w.logger.Info("为设备创建新的状态机",
		zap.Uint8("设备地址", deviceAddr),
		zap.String("初始状态", w.getPumpStatusString(stateMachine.GetCurrentStatus())))

	return stateMachine
}

// ✅ 使用状态机：获取设备当前状态
func (w *WayneAdapter) getDevicePumpStatus(deviceAddr byte) pump.PumpStatus {
	w.stateMachinesMux.RLock()
	defer w.stateMachinesMux.RUnlock()

	if stateMachine, exists := w.deviceStateMachines[deviceAddr]; exists {
		return stateMachine.GetCurrentStatus()
	}

	// 如果状态机不存在，返回未编程状态
	return pump.StatusPumpNotProgrammed
}

// ✅ 使用状态机：将泵状态转换为字符串
func (w *WayneAdapter) getPumpStatusString(status pump.PumpStatus) string {
	switch status {
	case pump.StatusPumpNotProgrammed:
		return "PUMP NOT PROGRAMMED"
	case pump.StatusReset:
		return "RESET"
	case pump.StatusAuthorized:
		return "AUTHORIZED"
	case pump.StatusFilling:
		return "FILLING"
	case pump.StatusFillingCompleted:
		return "COMPLETED"
	case pump.StatusMaxAmountReached:
		return "MAX_AMOUNT_REACHED"
	case pump.StatusSuspended:
		return "SUSPENDED"
	case pump.StatusSwitchedOff:
		return "SWITCHED_OFF"
	default:
		return fmt.Sprintf("UNKNOWN(%d)", int(status))
	}
}
