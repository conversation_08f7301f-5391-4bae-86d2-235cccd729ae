package core

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	"fcc-service/pkg/api"
)

// Container FCC依赖注入容器
type Container struct {
	services    map[string]*ServiceDefinition
	instances   map[string]interface{}
	mu          sync.RWMutex
	initialized bool
}

// ServiceDefinition 服务定义
type ServiceDefinition struct {
	Name         string
	ServiceType  reflect.Type
	Factory      ServiceFactory
	Singleton    bool
	Dependencies []string
	instance     interface{}
	created      bool
}

// ServiceFactory 服务工厂函数类型
type ServiceFactory func(container *Container) (interface{}, error)

// NewContainer 创建新的依赖注入容器
func NewContainer() *Container {
	return &Container{
		services:  make(map[string]*ServiceDefinition),
		instances: make(map[string]interface{}),
	}
}

// RegisterService 注册服务
func (c *Container) RegisterService(name string, serviceType reflect.Type, factory ServiceFactory, singleton bool, dependencies ...string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.initialized {
		return fmt.Errorf("cannot register service after container initialization")
	}

	if _, exists := c.services[name]; exists {
		return fmt.Errorf("service %s already registered", name)
	}

	c.services[name] = &ServiceDefinition{
		Name:         name,
		ServiceType:  serviceType,
		Factory:      factory,
		Singleton:    singleton,
		Dependencies: dependencies,
	}

	return nil
}

// RegisterSingleton 注册单例服务
func (c *Container) RegisterSingleton(name string, serviceType reflect.Type, factory ServiceFactory, dependencies ...string) error {
	return c.RegisterService(name, serviceType, factory, true, dependencies...)
}

// RegisterTransient 注册瞬态服务
func (c *Container) RegisterTransient(name string, serviceType reflect.Type, factory ServiceFactory, dependencies ...string) error {
	return c.RegisterService(name, serviceType, factory, false, dependencies...)
}

// RegisterInstance 注册实例
func (c *Container) RegisterInstance(name string, instance interface{}) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.initialized {
		return fmt.Errorf("cannot register instance after container initialization")
	}

	serviceType := reflect.TypeOf(instance)
	c.services[name] = &ServiceDefinition{
		Name:        name,
		ServiceType: serviceType,
		Singleton:   true,
		instance:    instance,
		created:     true,
	}

	c.instances[name] = instance
	return nil
}

// Resolve 解析服务
func (c *Container) Resolve(name string) (interface{}, error) {
	c.mu.RLock()
	def, exists := c.services[name]
	c.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("service %s not registered", name)
	}

	return c.createInstance(def)
}

// ResolveAs 解析服务并转换为指定类型
func (c *Container) ResolveAs(name string, target interface{}) error {
	instance, err := c.Resolve(name)
	if err != nil {
		return err
	}

	targetValue := reflect.ValueOf(target)
	if targetValue.Kind() != reflect.Ptr {
		return fmt.Errorf("target must be a pointer")
	}

	instanceValue := reflect.ValueOf(instance)
	if !instanceValue.Type().AssignableTo(targetValue.Elem().Type()) {
		return fmt.Errorf("service type %v not assignable to target type %v",
			instanceValue.Type(), targetValue.Elem().Type())
	}

	targetValue.Elem().Set(instanceValue)
	return nil
}

// MustResolve 解析服务，失败时panic
func (c *Container) MustResolve(name string) interface{} {
	instance, err := c.Resolve(name)
	if err != nil {
		panic(fmt.Sprintf("failed to resolve service %s: %v", name, err))
	}
	return instance
}

// Initialize 初始化容器，解析所有依赖关系
func (c *Container) Initialize() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.initialized {
		return fmt.Errorf("container already initialized")
	}

	// 检查循环依赖
	if err := c.checkCircularDependencies(); err != nil {
		return err
	}

	c.initialized = true
	return nil
}

// Shutdown 关闭容器，清理资源
func (c *Container) Shutdown(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 清理实现了Closer接口的服务
	for _, instance := range c.instances {
		if closer, ok := instance.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				// 记录错误但继续清理其他服务
				// 服务关闭失败，忽略错误继续清理
				_ = err
			}
		}
	}

	// 清理实现了Shutdown接口的服务
	for _, instance := range c.instances {
		if shutdowner, ok := instance.(interface{ Shutdown(context.Context) error }); ok {
			if err := shutdowner.Shutdown(ctx); err != nil {
				// 服务停止失败，忽略错误继续清理
				_ = err
			}
		}
	}

	// 清理容器状态
	c.instances = make(map[string]interface{})
	c.initialized = false

	return nil
}

// createInstance 创建服务实例
func (c *Container) createInstance(def *ServiceDefinition) (interface{}, error) {
	// 如果是单例且已创建，返回现有实例
	if def.Singleton && def.created {
		c.mu.RLock()
		instance := c.instances[def.Name]
		c.mu.RUnlock()
		return instance, nil
	}

	// 解析依赖
	dependencies := make([]interface{}, len(def.Dependencies))
	for i, depName := range def.Dependencies {
		dep, err := c.Resolve(depName)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve dependency %s for service %s: %v",
				depName, def.Name, err)
		}
		dependencies[i] = dep
	}

	// 创建实例
	var instance interface{}
	var err error

	if def.Factory != nil {
		instance, err = def.Factory(c)
		if err != nil {
			return nil, fmt.Errorf("failed to create service %s: %v", def.Name, err)
		}
	} else if def.instance != nil {
		instance = def.instance
	} else {
		return nil, fmt.Errorf("no factory or instance provided for service %s", def.Name)
	}

	// 如果是单例，缓存实例
	if def.Singleton {
		c.mu.Lock()
		c.instances[def.Name] = instance
		def.instance = instance
		def.created = true
		c.mu.Unlock()
	}

	return instance, nil
}

// checkCircularDependencies 检查循环依赖
func (c *Container) checkCircularDependencies() error {
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	for name := range c.services {
		if !visited[name] {
			if c.hasCycle(name, visited, recStack) {
				return fmt.Errorf("circular dependency detected involving service %s", name)
			}
		}
	}
	return nil
}

// hasCycle 检查是否存在循环
func (c *Container) hasCycle(name string, visited, recStack map[string]bool) bool {
	visited[name] = true
	recStack[name] = true

	def, exists := c.services[name]
	if !exists {
		return false
	}

	for _, depName := range def.Dependencies {
		if !visited[depName] {
			if c.hasCycle(depName, visited, recStack) {
				return true
			}
		} else if recStack[depName] {
			return true
		}
	}

	recStack[name] = false
	return false
}

// GetRegisteredServices 获取已注册的服务列表
func (c *Container) GetRegisteredServices() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	names := make([]string, 0, len(c.services))
	for name := range c.services {
		names = append(names, name)
	}
	return names
}

// IsInitialized 检查容器是否已初始化
func (c *Container) IsInitialized() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.initialized
}

// 预定义的服务名称常量
const (
	ServiceNameDeviceManager   = "device_manager"
	ServiceNameCommandExecutor = "command_executor"
	ServiceNameStatusMonitor   = "status_monitor"
	ServiceNameAdapterFactory  = "adapter_factory"
	ServiceNameConfig          = "config"
	ServiceNameLogger          = "logger"
	ServiceNameDatabase        = "database"
	ServiceNameCache           = "cache"
)

// RegisterCoreServices 注册FCC核心服务
func (c *Container) RegisterCoreServices() error {
	// 注册设备管理器
	if err := c.RegisterSingleton(
		ServiceNameDeviceManager,
		reflect.TypeOf((*api.DeviceManager)(nil)).Elem(),
		nil, // 工厂函数稍后实现
	); err != nil {
		return fmt.Errorf("failed to register device manager: %v", err)
	}

	// 注册命令执行器
	if err := c.RegisterSingleton(
		ServiceNameCommandExecutor,
		reflect.TypeOf((*api.CommandExecutor)(nil)).Elem(),
		nil, // 工厂函数稍后实现
		ServiceNameDeviceManager,
	); err != nil {
		return fmt.Errorf("failed to register command executor: %v", err)
	}

	// 注册状态监控器
	if err := c.RegisterSingleton(
		ServiceNameStatusMonitor,
		reflect.TypeOf((*api.StatusMonitor)(nil)).Elem(),
		nil, // 工厂函数稍后实现
		ServiceNameDeviceManager,
	); err != nil {
		return fmt.Errorf("failed to register status monitor: %v", err)
	}

	// 注册适配器工厂
	if err := c.RegisterSingleton(
		ServiceNameAdapterFactory,
		reflect.TypeOf((*api.AdapterFactory)(nil)).Elem(),
		nil, // 工厂函数稍后实现
	); err != nil {
		return fmt.Errorf("failed to register adapter factory: %v", err)
	}

	return nil
}
