package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	"fcc-service/internal/config"
	"fcc-service/pkg/errors"
)

// Cache 缓存接口
type Cache interface {
	// 连接管理
	Connect(ctx context.Context) error
	Disconnect(ctx context.Context) error
	IsConnected() bool
	Ping(ctx context.Context) error

	// 基础操作
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	GetJSON(ctx context.Context, key string, dest interface{}) error
	Del(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, key string) (bool, error)

	// 高级操作
	SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	TTL(ctx context.Context, key string) (time.Duration, error)

	// 批量操作
	MSet(ctx context.Context, pairs ...interface{}) error
	MGet(ctx context.Context, keys ...string) ([]interface{}, error)

	// 计数器操作
	Incr(ctx context.Context, key string) (int64, error)
	IncrBy(ctx context.Context, key string, value int64) (int64, error)
	Decr(ctx context.Context, key string) (int64, error)
	DecrBy(ctx context.Context, key string, value int64) (int64, error)

	// 健康检查
	HealthCheck(ctx context.Context) error

	// 获取原始客户端（用于高级操作）
	GetClient() *redis.Client
}

// RedisCache Redis缓存实现
type RedisCache struct {
	client *redis.Client
	config *config.RedisConfig
	logger *zap.Logger
}

// NewRedisCache 创建Redis缓存实例
func NewRedisCache(cfg *config.RedisConfig, logger *zap.Logger) Cache {
	return &RedisCache{
		config: cfg,
		logger: logger,
	}
}

// Connect 连接Redis
func (r *RedisCache) Connect(ctx context.Context) error {
	if r.client != nil {
		return errors.NewInternalError("redis already connected")
	}

	addr := fmt.Sprintf("%s:%d", r.config.Host, r.config.Port)
	r.logger.Info("Connecting to Redis", zap.String("addr", addr))

	// 创建Redis客户端
	r.client = redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     r.config.Password,
		DB:           r.config.DB,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 5,
		MaxConnAge:   30 * time.Minute,
		IdleTimeout:  5 * time.Minute,
	})

	// 测试连接
	if err := r.client.Ping(ctx).Err(); err != nil {
		r.client.Close()
		r.client = nil
		return errors.NewInternalError("failed to ping redis", err.Error()).WithCause(err)
	}

	r.logger.Info("Successfully connected to Redis")
	return nil
}

// Disconnect 断开Redis连接
func (r *RedisCache) Disconnect(ctx context.Context) error {
	if r.client == nil {
		return nil
	}

	r.logger.Info("Disconnecting from Redis")

	if err := r.client.Close(); err != nil {
		return errors.NewInternalError("failed to close redis connection", err.Error()).WithCause(err)
	}

	r.client = nil
	r.logger.Info("Successfully disconnected from Redis")
	return nil
}

// IsConnected 检查Redis是否连接
func (r *RedisCache) IsConnected() bool {
	return r.client != nil
}

// Ping 测试Redis连接
func (r *RedisCache) Ping(ctx context.Context) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}

	if err := r.client.Ping(ctx).Err(); err != nil {
		return errors.NewInternalError("redis ping failed", err.Error()).WithCause(err)
	}

	return nil
}

// Set 设置键值
func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}



	var val interface{}
	switch v := value.(type) {
	case string, []byte, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
		val = v
	default:
		// 序列化为JSON
		jsonData, err := json.Marshal(value)
		if err != nil {
			return errors.NewInternalError("failed to marshal value to JSON", err.Error()).WithCause(err)
		}
		val = jsonData
	}

	if err := r.client.Set(ctx, key, val, expiration).Err(); err != nil {
		r.logger.Error("Failed to set cache value", zap.String("key", key), zap.Error(err))
		return errors.NewInternalError("failed to set cache value", err.Error()).WithCause(err)
	}

	return nil
}

// Get 获取字符串值
func (r *RedisCache) Get(ctx context.Context, key string) (string, error) {
	if r.client == nil {
		return "", errors.NewInternalError("redis not connected")
	}



	val, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", &errors.FCCError{
				Code:       errors.ErrCodeDataNotFound,
				Message:    "cache key not found",
				Details:    "key: " + key,
				Severity:   errors.SeverityWarning,
				StatusCode: 404,
			}
		}
		r.logger.Error("Failed to get cache value", zap.String("key", key), zap.Error(err))
		return "", errors.NewInternalError("failed to get cache value", err.Error()).WithCause(err)
	}

	return val, nil
}

// GetJSON 获取JSON值并反序列化
func (r *RedisCache) GetJSON(ctx context.Context, key string, dest interface{}) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}

	val, err := r.Get(ctx, key)
	if err != nil {
		return err
	}

	if err := json.Unmarshal([]byte(val), dest); err != nil {
		return errors.NewInternalError("failed to unmarshal JSON value", err.Error()).WithCause(err)
	}

	return nil
}

// Del 删除键
func (r *RedisCache) Del(ctx context.Context, keys ...string) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}



	if err := r.client.Del(ctx, keys...).Err(); err != nil {
		r.logger.Error("Failed to delete cache keys", zap.Strings("keys", keys), zap.Error(err))
		return errors.NewInternalError("failed to delete cache keys", err.Error()).WithCause(err)
	}

	return nil
}

// Exists 检查键是否存在
func (r *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	if r.client == nil {
		return false, errors.NewInternalError("redis not connected")
	}

	count, err := r.client.Exists(ctx, key).Result()
	if err != nil {
		return false, errors.NewInternalError("failed to check key existence", err.Error()).WithCause(err)
	}

	return count > 0, nil
}

// SetNX 仅在键不存在时设置
func (r *RedisCache) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	if r.client == nil {
		return false, errors.NewInternalError("redis not connected")
	}

	var val interface{}
	switch v := value.(type) {
	case string, []byte, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
		val = v
	default:
		jsonData, err := json.Marshal(value)
		if err != nil {
			return false, errors.NewInternalError("failed to marshal value to JSON", err.Error()).WithCause(err)
		}
		val = jsonData
	}

	success, err := r.client.SetNX(ctx, key, val, expiration).Result()
	if err != nil {
		return false, errors.NewInternalError("failed to set NX cache value", err.Error()).WithCause(err)
	}

	return success, nil
}

// Expire 设置键的过期时间
func (r *RedisCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}

	if err := r.client.Expire(ctx, key, expiration).Err(); err != nil {
		return errors.NewInternalError("failed to set key expiration", err.Error()).WithCause(err)
	}

	return nil
}

// TTL 获取键的剩余生存时间
func (r *RedisCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	if r.client == nil {
		return 0, errors.NewInternalError("redis not connected")
	}

	ttl, err := r.client.TTL(ctx, key).Result()
	if err != nil {
		return 0, errors.NewInternalError("failed to get key TTL", err.Error()).WithCause(err)
	}

	return ttl, nil
}

// MSet 批量设置键值
func (r *RedisCache) MSet(ctx context.Context, pairs ...interface{}) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}

	if err := r.client.MSet(ctx, pairs...).Err(); err != nil {
		return errors.NewInternalError("failed to execute MSET", err.Error()).WithCause(err)
	}

	return nil
}

// MGet 批量获取键值
func (r *RedisCache) MGet(ctx context.Context, keys ...string) ([]interface{}, error) {
	if r.client == nil {
		return nil, errors.NewInternalError("redis not connected")
	}

	vals, err := r.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, errors.NewInternalError("failed to execute MGET", err.Error()).WithCause(err)
	}

	return vals, nil
}

// Incr 自增计数器
func (r *RedisCache) Incr(ctx context.Context, key string) (int64, error) {
	if r.client == nil {
		return 0, errors.NewInternalError("redis not connected")
	}

	val, err := r.client.Incr(ctx, key).Result()
	if err != nil {
		return 0, errors.NewInternalError("failed to increment counter", err.Error()).WithCause(err)
	}

	return val, nil
}

// IncrBy 按指定值自增计数器
func (r *RedisCache) IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	if r.client == nil {
		return 0, errors.NewInternalError("redis not connected")
	}

	val, err := r.client.IncrBy(ctx, key, value).Result()
	if err != nil {
		return 0, errors.NewInternalError("failed to increment counter by value", err.Error()).WithCause(err)
	}

	return val, nil
}

// Decr 自减计数器
func (r *RedisCache) Decr(ctx context.Context, key string) (int64, error) {
	if r.client == nil {
		return 0, errors.NewInternalError("redis not connected")
	}

	val, err := r.client.Decr(ctx, key).Result()
	if err != nil {
		return 0, errors.NewInternalError("failed to decrement counter", err.Error()).WithCause(err)
	}

	return val, nil
}

// DecrBy 按指定值自减计数器
func (r *RedisCache) DecrBy(ctx context.Context, key string, value int64) (int64, error) {
	if r.client == nil {
		return 0, errors.NewInternalError("redis not connected")
	}

	val, err := r.client.DecrBy(ctx, key, value).Result()
	if err != nil {
		return 0, errors.NewInternalError("failed to decrement counter by value", err.Error()).WithCause(err)
	}

	return val, nil
}

// HealthCheck 健康检查
func (r *RedisCache) HealthCheck(ctx context.Context) error {
	if r.client == nil {
		return errors.NewInternalError("redis not connected")
	}

	// 执行PING命令测试连接
	if err := r.client.Ping(ctx).Err(); err != nil {
		r.logger.Error("Redis health check failed", zap.Error(err))
		return errors.NewInternalError("redis health check failed", err.Error()).WithCause(err)
	}

	return nil
}

// GetClient 获取原始Redis客户端
func (r *RedisCache) GetClient() *redis.Client {
	return r.client
}

// CacheManager 缓存管理器
type CacheManager struct {
	cache  Cache
	config *config.RedisConfig
	logger *zap.Logger
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(cfg *config.RedisConfig, logger *zap.Logger) *CacheManager {
	return &CacheManager{
		cache:  NewRedisCache(cfg, logger),
		config: cfg,
		logger: logger,
	}
}

// Initialize 初始化缓存连接
func (cm *CacheManager) Initialize(ctx context.Context) error {
	cm.logger.Info("Initializing cache manager")

	if err := cm.cache.Connect(ctx); err != nil {
		return fmt.Errorf("failed to connect to cache: %w", err)
	}

	if err := cm.cache.HealthCheck(ctx); err != nil {
		return fmt.Errorf("cache health check failed: %w", err)
	}

	cm.logger.Info("Cache manager initialized successfully")
	return nil
}

// Shutdown 关闭缓存连接
func (cm *CacheManager) Shutdown(ctx context.Context) error {
	cm.logger.Info("Shutting down cache manager")

	if err := cm.cache.Disconnect(ctx); err != nil {
		return fmt.Errorf("failed to disconnect from cache: %w", err)
	}

	cm.logger.Info("Cache manager shutdown completed")
	return nil
}

// GetCache 获取缓存实例
func (cm *CacheManager) GetCache() Cache {
	return cm.cache
}

// 定义一些错误，为了保持兼容性
func NewDataNotFoundError(message string) *errors.FCCError {
	return &errors.FCCError{
		Code:       errors.ErrCodeDataNotFound,
		Message:    message,
		Severity:   errors.SeverityWarning,
		StatusCode: 404,
	}
}
