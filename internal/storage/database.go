package storage

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"fcc-service/internal/config"
	"fcc-service/pkg/errors"
)

// Database 数据库接口
type Database interface {
	// 连接管理
	Connect(ctx context.Context) error
	Disconnect(ctx context.Context) error
	IsConnected() bool
	Ping(ctx context.Context) error

	// 查询执行
	Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	QueryRow(ctx context.Context, query string, args ...interface{}) *sql.Row
	Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error)

	// 事务管理
	BeginTx(ctx context.Context, opts *sql.TxOptions) (*sql.Tx, error)

	// 健康检查
	HealthCheck(ctx context.Context) error

	// 获取原始连接（用于高级操作）
	GetDB() *sql.DB
}

// PostgreSQLDatabase PostgreSQL数据库实现
type PostgreSQLDatabase struct {
	db     *sql.DB
	gormDB *gorm.DB
	config *config.DatabaseConfig
	logger *zap.Logger
}

// NewPostgreSQLDatabase 创建PostgreSQL数据库实例
func NewPostgreSQLDatabase(cfg *config.DatabaseConfig, logger *zap.Logger) Database {
	return &PostgreSQLDatabase{
		config: cfg,
		logger: logger,
	}
}

// Connect 连接数据库
func (p *PostgreSQLDatabase) Connect(ctx context.Context) error {
	if p.db != nil {
		return errors.NewInternalError("database already connected")
	}

	dsn := p.buildDSN()
	p.logger.Info("Connecting to PostgreSQL", zap.String("host", p.config.Host), zap.Int("port", p.config.Port))

	// 连接原始 SQL 数据库
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return errors.NewInternalError("failed to open database connection", err.Error()).WithCause(err)
	}

	// 配置连接池
	db.SetMaxOpenConns(p.config.MaxOpenConns)
	db.SetMaxIdleConns(p.config.MaxIdleConns)
	db.SetConnMaxLifetime(10 * time.Minute) // 🔧 减少连接生命周期
	db.SetConnMaxIdleTime(2 * time.Minute)  // 🔧 减少空闲超时时间

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return errors.NewInternalError("failed to ping database", err.Error()).WithCause(err)
	}

	p.db = db

	// 初始化 GORM
	gormDB, err := gorm.Open(postgres.New(postgres.Config{
		Conn: db,
	}), &gorm.Config{
		Logger: NewGormLogger(p.logger),
		// 🔧 修复时区问题：与数据库时区保持一致
		NowFunc: func() time.Time {
			// 加载Jakarta时区
			jakartaLocation, err := time.LoadLocation("Asia/Jakarta")
			if err != nil {
				// 如果加载失败，使用系统默认时区
				return time.Now()
			}
			return time.Now().In(jakartaLocation)
		},
		// 🔧 临时禁用准备语句缓存以解决表结构变更问题
		PrepareStmt: false,
	})
	if err != nil {
		db.Close()
		return errors.NewInternalError("failed to initialize GORM", err.Error()).WithCause(err)
	}

	p.gormDB = gormDB

	p.logger.Info("Successfully connected to PostgreSQL",
		zap.Int("max_open_conns", p.config.MaxOpenConns),
		zap.Int("max_idle_conns", p.config.MaxIdleConns),
		zap.Duration("max_lifetime", 10*time.Minute),
		zap.Duration("idle_timeout", 2*time.Minute))

	return nil
}

// Disconnect 断开数据库连接
func (p *PostgreSQLDatabase) Disconnect(ctx context.Context) error {
	if p.db == nil {
		return nil
	}

	p.logger.Info("Disconnecting from PostgreSQL")

	if err := p.db.Close(); err != nil {
		return errors.NewInternalError("failed to close database connection", err.Error()).WithCause(err)
	}

	p.db = nil
	p.gormDB = nil
	p.logger.Info("Successfully disconnected from PostgreSQL")
	return nil
}

// IsConnected 检查数据库是否连接
func (p *PostgreSQLDatabase) IsConnected() bool {
	return p.db != nil
}

// Ping 测试数据库连接
func (p *PostgreSQLDatabase) Ping(ctx context.Context) error {
	if p.db == nil {
		return errors.NewInternalError("database not connected")
	}

	if err := p.db.PingContext(ctx); err != nil {
		return errors.NewInternalError("database ping failed", err.Error()).WithCause(err)
	}

	return nil
}

// Query 执行查询
func (p *PostgreSQLDatabase) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	if p.db == nil {
		return nil, errors.NewInternalError("database not connected")
	}

	// 数据库查询执行

	rows, err := p.db.QueryContext(ctx, query, args...)
	if err != nil {
		p.logger.Error("Query execution failed", zap.String("query", query), zap.Error(err))
		return nil, errors.NewInternalError("query execution failed", err.Error()).WithCause(err)
	}

	return rows, nil
}

// QueryRow 执行单行查询
func (p *PostgreSQLDatabase) QueryRow(ctx context.Context, query string, args ...interface{}) *sql.Row {
	if p.db == nil {
		// 返回一个会产生错误的Row
		return &sql.Row{}
	}

	// 数据库单行查询执行
	return p.db.QueryRowContext(ctx, query, args...)
}

// Exec 执行命令
func (p *PostgreSQLDatabase) Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	if p.db == nil {
		return nil, errors.NewInternalError("database not connected")
	}

	// 数据库命令执行

	result, err := p.db.ExecContext(ctx, query, args...)
	if err != nil {
		p.logger.Error("Command execution failed", zap.String("query", query), zap.Error(err))
		return nil, errors.NewInternalError("command execution failed", err.Error()).WithCause(err)
	}

	return result, nil
}

// BeginTx 开始事务
func (p *PostgreSQLDatabase) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sql.Tx, error) {
	if p.db == nil {
		return nil, errors.NewInternalError("database not connected")
	}

	// 开始数据库事务

	tx, err := p.db.BeginTx(ctx, opts)
	if err != nil {
		p.logger.Error("Failed to begin transaction", zap.Error(err))
		return nil, errors.NewInternalError("failed to begin transaction", err.Error()).WithCause(err)
	}

	return tx, nil
}

// HealthCheck 健康检查
func (p *PostgreSQLDatabase) HealthCheck(ctx context.Context) error {
	if p.db == nil {
		return errors.NewInternalError("database not connected")
	}

	// 执行简单查询测试连接
	row := p.db.QueryRowContext(ctx, "SELECT 1")
	var result int
	if err := row.Scan(&result); err != nil {
		p.logger.Error("Database health check failed", zap.Error(err))
		return errors.NewInternalError("database health check failed", err.Error()).WithCause(err)
	}

	if result != 1 {
		return errors.NewInternalError("database health check returned unexpected result")
	}

	// 检查连接池状态（健康检查完成）

	return nil
}

// GetDB 获取原始数据库连接
func (p *PostgreSQLDatabase) GetDB() *sql.DB {
	return p.db
}

// GetGormDB 获取 GORM 数据库连接
func (p *PostgreSQLDatabase) GetGormDB() *gorm.DB {
	return p.gormDB
}

// GetConnectionStats 获取数据库连接池统计信息
func (p *PostgreSQLDatabase) GetConnectionStats() map[string]interface{} {
	if p.db == nil {
		return map[string]interface{}{
			"status": "not_connected",
		}
	}

	stats := p.db.Stats()
	return map[string]interface{}{
		"status":               "connected",
		"max_open_conns":       stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration_ms":     stats.WaitDuration.Milliseconds(),
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
		"config": map[string]interface{}{
			"max_open_conns": p.config.MaxOpenConns,
			"max_idle_conns": p.config.MaxIdleConns,
		},
	}
}

// LogConnectionStats 记录连接池统计信息到日志
func (p *PostgreSQLDatabase) LogConnectionStats() {
	if p.db == nil {
		return
	}

	stats := p.db.Stats()

	// 计算连接池使用率
	openRate := float64(stats.OpenConnections) / float64(stats.MaxOpenConnections) * 100
	idleRate := float64(stats.Idle) / float64(stats.OpenConnections) * 100

	// 判断是否有性能问题
	hasIssues := false
	issues := make([]string, 0)

	if stats.WaitCount > 0 {
		hasIssues = true
		issues = append(issues, fmt.Sprintf("连接等待次数: %d", stats.WaitCount))
	}

	if idleRate > 80 {
		hasIssues = true
		issues = append(issues, fmt.Sprintf("空闲连接过多: %.1f%%", idleRate))
	}

	if openRate > 90 {
		hasIssues = true
		issues = append(issues, fmt.Sprintf("连接池使用率过高: %.1f%%", openRate))
	}

	logLevel := zap.InfoLevel
	if hasIssues {
		logLevel = zap.WarnLevel
	}

	p.logger.Log(logLevel, "数据库连接池统计",
		zap.Int("max_open", stats.MaxOpenConnections),
		zap.Int("open", stats.OpenConnections),
		zap.Int("in_use", stats.InUse),
		zap.Int("idle", stats.Idle),
		zap.Float64("open_rate_percent", openRate),
		zap.Float64("idle_rate_percent", idleRate),
		zap.Int64("wait_count", stats.WaitCount),
		zap.Duration("wait_duration", stats.WaitDuration),
		zap.Bool("has_issues", hasIssues),
		zap.Strings("issues", issues))
}

// buildDSN 构建数据源名称
func (p *PostgreSQLDatabase) buildDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=Asia/Jakarta",
		p.config.Host,
		p.config.Port,
		p.config.Username,
		p.config.Password,
		p.config.Database,
		p.config.SSLMode)
}

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	database Database
	config   *config.DatabaseConfig
	logger   *zap.Logger
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(cfg *config.DatabaseConfig, logger *zap.Logger) *DatabaseManager {
	return &DatabaseManager{
		database: NewPostgreSQLDatabase(cfg, logger),
		config:   cfg,
		logger:   logger,
	}
}

// Initialize 初始化数据库连接
func (dm *DatabaseManager) Initialize(ctx context.Context) error {
	dm.logger.Info("Initializing database manager")

	if err := dm.database.Connect(ctx); err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	if err := dm.database.HealthCheck(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	dm.logger.Info("Database manager initialized successfully")
	return nil
}

// Shutdown 关闭数据库连接
func (dm *DatabaseManager) Shutdown(ctx context.Context) error {
	dm.logger.Info("Shutting down database manager")

	if err := dm.database.Disconnect(ctx); err != nil {
		return fmt.Errorf("failed to disconnect from database: %w", err)
	}

	dm.logger.Info("Database manager shutdown completed")
	return nil
}

// GetDatabase 获取数据库实例
func (dm *DatabaseManager) GetDatabase() Database {
	return dm.database
}

// GormLogger GORM 日志适配器
type GormLogger struct {
	logger *zap.Logger
}

// NewGormLogger 创建 GORM 日志适配器
func NewGormLogger(logger *zap.Logger) logger.Interface {
	return &GormLogger{logger: logger}
}

// LogMode 实现 gorm.Logger 接口
func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

// Info 实现 gorm.Logger 接口
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Info(fmt.Sprintf(msg, data...))
}

// Warn 实现 gorm.Logger 接口
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Warn(fmt.Sprintf(msg, data...))
}

// Error 实现 gorm.Logger 接口
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Error(fmt.Sprintf(msg, data...))
}

// Trace 实现 gorm.Logger 接口
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()

	if err != nil {
		l.logger.Error("SQL execution failed",
			zap.Duration("elapsed", elapsed),
			zap.String("sql", sql),
			zap.Int64("rows", rows),
			zap.Error(err))
	}
	// SQL执行完成（正常情况不记录详细信息）
}
