package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	"fcc-service/pkg/errors"
)

// MemoryCache 内存缓存实现，作为 Redis 的降级方案
type MemoryCache struct {
	data   map[string][]byte
	expiry map[string]time.Time
	logger *zap.Logger
	mu     sync.RWMutex
}

// NewMemoryCache 创建新的内存缓存实例
func NewMemoryCache(logger *zap.Logger) Cache {
	cache := &MemoryCache{
		data:   make(map[string][]byte),
		expiry: make(map[string]time.Time),
		logger: logger,
	}

	// 启动过期清理协程
	go cache.cleanupExpired()

	logger.Info("Memory cache initialized successfully")
	return cache
}

// 连接管理
func (c *MemoryCache) Connect(ctx context.Context) error {
	// 内存缓存无需连接
	return nil
}

func (c *MemoryCache) Disconnect(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 清理所有数据
	c.data = make(map[string][]byte)
	c.expiry = make(map[string]time.Time)

	c.logger.Info("Memory cache disconnected and cleaned up")
	return nil
}

func (c *MemoryCache) IsConnected() bool {
	// 内存缓存总是连接状态
	return true
}

func (c *MemoryCache) Ping(ctx context.Context) error {
	// 内存缓存总是可用
	return nil
}

// 基础操作
func (c *MemoryCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	var data []byte
	var err error

	switch v := value.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = make([]byte, len(v))
		copy(data, v)
	default:
		// 序列化为 JSON
		data, err = json.Marshal(value)
		if err != nil {
			return errors.NewInternalError("failed to marshal value to JSON", err.Error()).WithCause(err)
		}
	}

	c.data[key] = data

	if expiration > 0 {
		c.expiry[key] = time.Now().Add(expiration)
	} else {
		delete(c.expiry, key) // 无过期时间
	}

	return nil
}

func (c *MemoryCache) Get(ctx context.Context, key string) (string, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 检查过期时间
	if expiry, exists := c.expiry[key]; exists && time.Now().After(expiry) {
		// 过期了，删除
		delete(c.data, key)
		delete(c.expiry, key)
		return "", &errors.FCCError{
			Code:       errors.ErrCodeDataNotFound,
			Message:    "cache key not found",
			Details:    "key: " + key,
			Severity:   errors.SeverityWarning,
			StatusCode: 404,
		}
	}

	value, exists := c.data[key]
	if !exists {
		return "", &errors.FCCError{
			Code:       errors.ErrCodeDataNotFound,
			Message:    "cache key not found",
			Details:    "key: " + key,
			Severity:   errors.SeverityWarning,
			StatusCode: 404,
		}
	}

	return string(value), nil
}

func (c *MemoryCache) GetJSON(ctx context.Context, key string, dest interface{}) error {
	val, err := c.Get(ctx, key)
	if err != nil {
		return err
	}

	if err := json.Unmarshal([]byte(val), dest); err != nil {
		return errors.NewInternalError("failed to unmarshal JSON value", err.Error()).WithCause(err)
	}

	return nil
}

func (c *MemoryCache) Del(ctx context.Context, keys ...string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	for _, key := range keys {
		delete(c.data, key)
		delete(c.expiry, key)
	}

	return nil
}

func (c *MemoryCache) Exists(ctx context.Context, key string) (bool, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 检查过期时间
	if expiry, exists := c.expiry[key]; exists && time.Now().After(expiry) {
		return false, nil
	}

	_, exists := c.data[key]
	return exists, nil
}

func (c *MemoryCache) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 检查键是否已存在且未过期
	if expiry, exists := c.expiry[key]; exists && time.Now().After(expiry) {
		// 过期了，删除
		delete(c.data, key)
		delete(c.expiry, key)
	} else if _, exists := c.data[key]; exists {
		// 键已存在
		return false, nil
	}

	// 设置新值
	var data []byte
	var err error

	switch v := value.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = make([]byte, len(v))
		copy(data, v)
	default:
		data, err = json.Marshal(value)
		if err != nil {
			return false, errors.NewInternalError("failed to marshal value to JSON", err.Error()).WithCause(err)
		}
	}

	c.data[key] = data
	if expiration > 0 {
		c.expiry[key] = time.Now().Add(expiration)
	}

	return true, nil
}

func (c *MemoryCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if _, exists := c.data[key]; !exists {
		return &errors.FCCError{
			Code:       errors.ErrCodeDataNotFound,
			Message:    "cache key not found",
			Details:    "key: " + key,
			Severity:   errors.SeverityWarning,
			StatusCode: 404,
		}
	}

	c.expiry[key] = time.Now().Add(expiration)
	return nil
}

func (c *MemoryCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if _, exists := c.data[key]; !exists {
		return 0, &errors.FCCError{
			Code:       errors.ErrCodeDataNotFound,
			Message:    "cache key not found",
			Details:    "key: " + key,
			Severity:   errors.SeverityWarning,
			StatusCode: 404,
		}
	}

	if expiry, exists := c.expiry[key]; exists {
		remaining := time.Until(expiry)
		if remaining <= 0 {
			return 0, nil // 已过期
		}
		return remaining, nil
	}

	return -1, nil // 无过期时间
}

func (c *MemoryCache) MSet(ctx context.Context, pairs ...interface{}) error {
	if len(pairs)%2 != 0 {
		return errors.NewInternalError("invalid number of pairs for MSET")
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	for i := 0; i < len(pairs); i += 2 {
		key, ok := pairs[i].(string)
		if !ok {
			return errors.NewInternalError("key must be string")
		}

		value := pairs[i+1]
		var data []byte
		var err error

		switch v := value.(type) {
		case string:
			data = []byte(v)
		case []byte:
			data = make([]byte, len(v))
			copy(data, v)
		default:
			data, err = json.Marshal(value)
			if err != nil {
				return errors.NewInternalError("failed to marshal value to JSON", err.Error()).WithCause(err)
			}
		}

		c.data[key] = data
	}

	return nil
}

func (c *MemoryCache) MGet(ctx context.Context, keys ...string) ([]interface{}, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make([]interface{}, len(keys))
	for i, key := range keys {
		if expiry, exists := c.expiry[key]; exists && time.Now().After(expiry) {
			result[i] = nil
			continue
		}

		if value, exists := c.data[key]; exists {
			result[i] = string(value)
		} else {
			result[i] = nil
		}
	}

	return result, nil
}

func (c *MemoryCache) Incr(ctx context.Context, key string) (int64, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 获取当前值
	var current int64
	if data, exists := c.data[key]; exists {
		if val, err := parseInt64(string(data)); err == nil {
			current = val
		}
	}

	// 增加1
	current++

	// 保存新值
	c.data[key] = []byte(fmt.Sprintf("%d", current))

	return current, nil
}

func (c *MemoryCache) IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 获取当前值
	var current int64
	if data, exists := c.data[key]; exists {
		if val, err := parseInt64(string(data)); err == nil {
			current = val
		}
	}

	// 增加指定值
	current += value

	// 保存新值
	c.data[key] = []byte(fmt.Sprintf("%d", current))

	return current, nil
}

func (c *MemoryCache) Decr(ctx context.Context, key string) (int64, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 获取当前值
	var current int64
	if data, exists := c.data[key]; exists {
		if val, err := parseInt64(string(data)); err == nil {
			current = val
		}
	}

	// 减少1
	current--

	// 保存新值
	c.data[key] = []byte(fmt.Sprintf("%d", current))

	return current, nil
}

func (c *MemoryCache) DecrBy(ctx context.Context, key string, value int64) (int64, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 获取当前值
	var current int64
	if data, exists := c.data[key]; exists {
		if val, err := parseInt64(string(data)); err == nil {
			current = val
		}
	}

	// 减少指定值
	current -= value

	// 保存新值
	c.data[key] = []byte(fmt.Sprintf("%d", current))

	return current, nil
}

func (c *MemoryCache) HealthCheck(ctx context.Context) error {
	// 内存缓存总是健康
	return nil
}

// GetClient 获取客户端（内存缓存返回 nil）
func (c *MemoryCache) GetClient() *redis.Client {
	return nil
}

// cleanupExpired 清理过期的缓存项
func (c *MemoryCache) cleanupExpired() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.performCleanup()
		}
	}
}

func (c *MemoryCache) performCleanup() {
	c.mu.Lock()
	defer c.mu.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	for key, expiry := range c.expiry {
		if now.After(expiry) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(c.data, key)
		delete(c.expiry, key)
	}

	if len(expiredKeys) > 0 {
		c.logger.Info("Memory cache cleanup completed",
			zap.Int("expired_keys", len(expiredKeys)),
			zap.Int("remaining_keys", len(c.data)))
	}
}

// GetStats 获取内存缓存统计信息
func (c *MemoryCache) GetStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	expiredCount := 0
	now := time.Now()
	for _, expiry := range c.expiry {
		if now.After(expiry) {
			expiredCount++
		}
	}

	return map[string]interface{}{
		"type":         "memory",
		"total_keys":   len(c.data),
		"expired_keys": expiredCount,
		"active_keys":  len(c.data) - expiredCount,
		"memory_usage": c.calculateMemoryUsage(),
	}
}

func (c *MemoryCache) calculateMemoryUsage() int {
	total := 0
	for key, value := range c.data {
		total += len(key) + len(value)
	}
	return total
}

// 辅助函数
func parseInt64(s string) (int64, error) {
	var result int64
	_, err := fmt.Sscanf(s, "%d", &result)
	return result, err
}
