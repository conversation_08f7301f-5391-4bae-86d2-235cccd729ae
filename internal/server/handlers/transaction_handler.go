package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/services/transaction"
	"fcc-service/pkg/models"
)

// TransactionHandler 交易API处理器
type TransactionHandler struct {
	transactionService transaction.TransactionService
	logger             *zap.Logger
}

// NewTransactionHandler 创建交易处理器
func NewTransactionHandler(transactionService transaction.TransactionService, logger *zap.Logger) *TransactionHandler {
	return &TransactionHandler{
		transactionService: transactionService,
		logger:             logger,
	}
}

// ListTransactions 查询交易列表
// GET /api/v1/transactions
func (h *TransactionHandler) ListTransactions(c echo.Context) error {
	ctx := c.Request().Context()

	// 解析查询参数
	filter, err := h.parseTransactionFilter(c)
	if err != nil {
		h.logger.Warn("Invalid query parameters", zap.Error(err))
		return c.<PERSON>(http.StatusBadRequest, map[string]interface{}{
			"error":   "invalid_parameters",
			"message": err.Error(),
		})
	}

	// 查询交易列表
	result, err := h.transactionService.GetTransactionHistory(ctx, filter)
	if err != nil {
		h.logger.Error("Failed to get transaction history", zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get transaction history",
		})
	}

	// 构建响应
	response := map[string]interface{}{
		"transactions": result.Transactions,
		"pagination": map[string]interface{}{
			"total":    result.Total,
			"page":     result.Page,
			"limit":    result.Limit,
			"has_more": result.HasMore,
		},
	}

	return c.JSON(http.StatusOK, response)
}

// GetTransaction 获取单个交易
// GET /api/v1/transactions/:id
func (h *TransactionHandler) GetTransaction(c echo.Context) error {
	ctx := c.Request().Context()
	transactionID := c.Param("id")

	if transactionID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "missing_parameter",
			"message": "Transaction ID is required",
		})
	}

	// 获取交易
	transaction, err := h.transactionService.GetTransaction(ctx, transactionID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"error":   "transaction_not_found",
				"message": "Transaction not found",
			})
		}

		h.logger.Error("Failed to get transaction", zap.String("id", transactionID), zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get transaction",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"transaction": transaction,
	})
}

// GetDeviceTransactions 获取设备交易历史
// GET /api/v1/devices/:deviceId/transactions
func (h *TransactionHandler) GetDeviceTransactions(c echo.Context) error {
	ctx := c.Request().Context()
	deviceID := c.Param("deviceId")

	if deviceID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "missing_parameter",
			"message": "Device ID is required",
		})
	}

	// 解析查询参数
	filter, err := h.parseTransactionFilter(c)
	if err != nil {
		h.logger.Warn("Invalid query parameters", zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "invalid_parameters",
			"message": err.Error(),
		})
	}

	// 查询设备交易历史
	result, err := h.transactionService.GetTransactionsByDevice(ctx, deviceID, filter)
	if err != nil {
		h.logger.Error("Failed to get device transactions",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get device transactions",
		})
	}

	// 构建响应
	response := map[string]interface{}{
		"device_id":    deviceID,
		"transactions": result.Transactions,
		"pagination": map[string]interface{}{
			"total":    result.Total,
			"page":     result.Page,
			"limit":    result.Limit,
			"has_more": result.HasMore,
		},
	}

	return c.JSON(http.StatusOK, response)
}

// GetTransactionStats 获取交易统计
// GET /api/v1/transactions/stats
func (h *TransactionHandler) GetTransactionStats(c echo.Context) error {
	ctx := c.Request().Context()

	// 解析统计过滤参数
	filter, err := h.parseTransactionStatsFilter(c)
	if err != nil {
		h.logger.Warn("Invalid stats parameters", zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "invalid_parameters",
			"message": err.Error(),
		})
	}

	// 获取统计数据
	stats, err := h.transactionService.GetTransactionStats(ctx, filter)
	if err != nil {
		h.logger.Error("Failed to get transaction stats", zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get transaction statistics",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"stats": stats,
	})
}

// GetDeviceTransactionSummary 获取设备交易汇总
// GET /api/v1/devices/:deviceId/transactions/summary
func (h *TransactionHandler) GetDeviceTransactionSummary(c echo.Context) error {
	ctx := c.Request().Context()
	deviceID := c.Param("deviceId")

	if deviceID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "missing_parameter",
			"message": "Device ID is required",
		})
	}

	// 解析时间范围参数
	timeRange, err := h.parseTimeRange(c)
	if err != nil {
		h.logger.Warn("Invalid time range parameters", zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "invalid_parameters",
			"message": err.Error(),
		})
	}

	// 获取设备交易汇总
	summary, err := h.transactionService.GetDeviceTransactionSummary(ctx, deviceID, timeRange)
	if err != nil {
		h.logger.Error("Failed to get device transaction summary",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get device transaction summary",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"summary": summary,
	})
}

// GetTransactionPumpReadings 获取交易泵码数据详情（任务7新增）
// GET /api/v1/transactions/:id/pump-readings
func (h *TransactionHandler) GetTransactionPumpReadings(c echo.Context) error {
	ctx := c.Request().Context()
	transactionID := c.Param("id")

	if transactionID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "missing_parameter",
			"message": "Transaction ID is required",
		})
	}

	// 获取交易
	transaction, err := h.transactionService.GetTransaction(ctx, transactionID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"error":   "transaction_not_found",
				"message": "Transaction not found",
			})
		}

		h.logger.Error("Failed to get transaction for pump readings",
			zap.String("id", transactionID),
			zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get transaction",
		})
	}

	// 构建泵码数据响应
	pumpReadings := h.buildPumpReadingsResponse(transaction)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"transaction_id": transactionID,
		"pump_readings":  pumpReadings,
	})
}

// GetTransactionsWithPumpIssues 获取有泵码问题的交易列表（任务7新增）
// GET /api/v1/transactions/pump-issues
func (h *TransactionHandler) GetTransactionsWithPumpIssues(c echo.Context) error {
	ctx := c.Request().Context()

	// 解析查询参数
	filter, err := h.parseTransactionFilter(c)
	if err != nil {
		h.logger.Warn("Invalid query parameters for pump issues", zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "invalid_parameters",
			"message": err.Error(),
		})
	}

	// 设置默认过滤条件：只查询有泵码问题的交易
	if filter.PumpReadingQuality == nil {
		// 默认查询质量为poor或bad的交易，这里暂时使用poor作为示例
		poorQuality := "poor"
		filter.PumpReadingQuality = &poorQuality
	}

	// 查询交易列表
	result, err := h.transactionService.GetTransactionHistory(ctx, filter)
	if err != nil {
		h.logger.Error("Failed to get transactions with pump issues", zap.Error(err))
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "internal_error",
			"message": "Failed to get transactions with pump issues",
		})
	}

	// 为每个交易添加泵码分析
	var enhancedTransactions []map[string]interface{}
	for _, tx := range result.Transactions {
		enhanced := map[string]interface{}{
			"transaction":   tx,
			"pump_analysis": h.analyzePumpReadings(tx),
		}
		enhancedTransactions = append(enhancedTransactions, enhanced)
	}

	// 构建响应
	response := map[string]interface{}{
		"transactions": enhancedTransactions,
		"pagination": map[string]interface{}{
			"total":    result.Total,
			"page":     result.Page,
			"limit":    result.Limit,
			"has_more": result.HasMore,
		},
		"summary": h.buildPumpIssuesSummary(result.Transactions),
	}

	return c.JSON(http.StatusOK, response)
}

// 解析方法

// parseTransactionFilter 解析交易过滤参数
func (h *TransactionHandler) parseTransactionFilter(c echo.Context) (transaction.TransactionFilter, error) {
	filter := transaction.TransactionFilter{
		Page:  1,
		Limit: 20,
	}

	// 基础过滤参数
	if deviceID := c.QueryParam("device_id"); deviceID != "" {
		filter.DeviceID = &deviceID
	}

	if nozzleID := c.QueryParam("nozzle_id"); nozzleID != "" {
		filter.NozzleID = &nozzleID
	}

	if status := c.QueryParam("status"); status != "" {
		txStatus := models.TransactionStatus(status)
		filter.Status = &txStatus
	}

	if txType := c.QueryParam("type"); txType != "" {
		transactionType := models.TransactionType(txType)
		filter.Type = &transactionType
	}

	if operatorID := c.QueryParam("operator_id"); operatorID != "" {
		filter.OperatorID = &operatorID
	}

	if stationID := c.QueryParam("station_id"); stationID != "" {
		filter.StationID = &stationID
	}

	// 时间范围
	if startTime := c.QueryParam("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			filter.StartTime = &t
		} else {
			return filter, err
		}
	}

	if endTime := c.QueryParam("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			filter.EndTime = &t
		} else {
			return filter, err
		}
	}

	// 金额范围
	if minAmount := c.QueryParam("min_amount"); minAmount != "" {
		if amount, err := decimal.NewFromString(minAmount); err == nil {
			filter.MinAmount = &amount
		} else {
			return filter, err
		}
	}

	if maxAmount := c.QueryParam("max_amount"); maxAmount != "" {
		if amount, err := decimal.NewFromString(maxAmount); err == nil {
			filter.MaxAmount = &amount
		} else {
			return filter, err
		}
	}

	// 体积范围
	if minVolume := c.QueryParam("min_volume"); minVolume != "" {
		if volume, err := decimal.NewFromString(minVolume); err == nil {
			filter.MinVolume = &volume
		} else {
			return filter, err
		}
	}

	if maxVolume := c.QueryParam("max_volume"); maxVolume != "" {
		if volume, err := decimal.NewFromString(maxVolume); err == nil {
			filter.MaxVolume = &volume
		} else {
			return filter, err
		}
	}

	// 任务7新增：泵码数据过滤器
	if pumpReadingSource := c.QueryParam("pump_reading_source"); pumpReadingSource != "" {
		filter.PumpReadingSource = &pumpReadingSource
	}

	if pumpReadingQuality := c.QueryParam("pump_reading_quality"); pumpReadingQuality != "" {
		filter.PumpReadingQuality = &pumpReadingQuality
	}

	if pumpReadingValidated := c.QueryParam("pump_reading_validated"); pumpReadingValidated != "" {
		if validated, err := strconv.ParseBool(pumpReadingValidated); err == nil {
			filter.PumpReadingValidated = &validated
		} else {
			return filter, fmt.Errorf("invalid pump_reading_validated parameter: %s", pumpReadingValidated)
		}
	}

	// 泵码差异范围过滤
	if maxPumpDiscrepancy := c.QueryParam("max_pump_discrepancy"); maxPumpDiscrepancy != "" {
		if discrepancy, err := decimal.NewFromString(maxPumpDiscrepancy); err == nil {
			filter.MaxPumpDiscrepancy = &discrepancy
		} else {
			return filter, err
		}
	}

	// 是否包含泵码数据过滤
	if hasPumpReadings := c.QueryParam("has_pump_readings"); hasPumpReadings != "" {
		if hasReadings, err := strconv.ParseBool(hasPumpReadings); err == nil {
			filter.HasPumpReadings = &hasReadings
		} else {
			return filter, fmt.Errorf("invalid has_pump_readings parameter: %s", hasPumpReadings)
		}
	}

	// 分页参数
	if page := c.QueryParam("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if limit := c.QueryParam("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			filter.Limit = l
		}
	}

	// 排序参数
	if sortBy := c.QueryParam("sort_by"); sortBy != "" {
		filter.SortBy = sortBy
	}

	if sortOrder := c.QueryParam("sort_order"); sortOrder != "" {
		filter.SortOrder = sortOrder
	}

	return filter, nil
}

// parseTransactionStatsFilter 解析交易统计过滤参数
func (h *TransactionHandler) parseTransactionStatsFilter(c echo.Context) (transaction.TransactionStatsFilter, error) {
	filter := transaction.TransactionStatsFilter{}

	// 基础过滤参数
	if deviceID := c.QueryParam("device_id"); deviceID != "" {
		filter.DeviceID = &deviceID
	}

	if stationID := c.QueryParam("station_id"); stationID != "" {
		filter.StationID = &stationID
	}

	if operatorID := c.QueryParam("operator_id"); operatorID != "" {
		filter.OperatorID = &operatorID
	}

	// 时间范围
	if startTime := c.QueryParam("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			filter.StartTime = &t
		} else {
			return filter, err
		}
	}

	if endTime := c.QueryParam("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			filter.EndTime = &t
		} else {
			return filter, err
		}
	}

	// 分组参数
	if groupBy := c.QueryParam("group_by"); groupBy != "" {
		filter.GroupBy = groupBy
	}

	return filter, nil
}

// parseTimeRange 解析时间范围参数
func (h *TransactionHandler) parseTimeRange(c echo.Context) (transaction.TimeRange, error) {
	timeRange := transaction.TimeRange{
		Start: time.Now().AddDate(0, 0, -7), // 默认最近7天
		End:   time.Now(),
	}

	if startTime := c.QueryParam("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			timeRange.Start = t
		} else {
			return timeRange, err
		}
	}

	if endTime := c.QueryParam("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			timeRange.End = t
		} else {
			return timeRange, err
		}
	}

	// 验证时间范围合理性
	if timeRange.End.Before(timeRange.Start) {
		return timeRange, fmt.Errorf("end time cannot be before start time")
	}

	// 限制查询范围（例如最多查询1年）
	if timeRange.End.Sub(timeRange.Start) > 365*24*time.Hour {
		return timeRange, fmt.Errorf("time range cannot exceed 1 year")
	}

	return timeRange, nil
}

// 任务7新增：泵码数据相关辅助方法

// buildPumpReadingsResponse 构建泵码数据响应
func (h *TransactionHandler) buildPumpReadingsResponse(tx *models.Transaction) map[string]interface{} {
	response := map[string]interface{}{
		"source":            tx.PumpReadingSource,
		"quality":           tx.PumpReadingQuality,
		"validated":         tx.PumpReadingValidated,
		"has_complete_data": tx.HasPumpReadings(),
	}

	// 起始泵码数据
	if tx.StartPumpVolumeReading != nil || tx.StartPumpAmountReading != nil {
		startReadings := map[string]interface{}{}
		if tx.StartPumpVolumeReading != nil {
			startReadings["volume"] = tx.StartPumpVolumeReading.String()
		}
		if tx.StartPumpAmountReading != nil {
			startReadings["amount"] = tx.StartPumpAmountReading.String()
		}
		response["start_readings"] = startReadings
	}

	// 结束泵码数据
	if tx.EndPumpVolumeReading != nil || tx.EndPumpAmountReading != nil {
		endReadings := map[string]interface{}{}
		if tx.EndPumpVolumeReading != nil {
			endReadings["volume"] = tx.EndPumpVolumeReading.String()
		}
		if tx.EndPumpAmountReading != nil {
			endReadings["amount"] = tx.EndPumpAmountReading.String()
		}
		response["end_readings"] = endReadings
	}

	// 计算值
	if tx.HasPumpReadings() {
		calculated := map[string]interface{}{
			"volume": tx.CalculatedVolume().String(),
			"amount": tx.CalculatedAmount().String(),
		}
		response["calculated"] = calculated

		// 差异分析
		if tx.PumpReadingDiscrepancy != nil {
			response["discrepancy"] = map[string]interface{}{
				"volume":           tx.PumpReadingDiscrepancy.String(),
				"within_tolerance": tx.PumpReadingDiscrepancy.LessThan(decimal.NewFromFloat(0.01)), // 10ml容差
			}
		}
	}

	// 验证结果
	if validationErr := tx.ValidatePumpReadings(); validationErr != nil {
		response["validation_error"] = validationErr.Error()
	} else {
		response["validation_status"] = "passed"
	}

	return response
}

// analyzePumpReadings 分析交易泵码数据
func (h *TransactionHandler) analyzePumpReadings(tx *models.Transaction) map[string]interface{} {
	analysis := map[string]interface{}{
		"has_readings": tx.HasPumpReadings(),
		"quality":      tx.PumpReadingQuality,
		"source":       tx.PumpReadingSource,
		"validated":    tx.PumpReadingValidated,
	}

	// 完整性分析
	completeness := map[string]bool{
		"has_start_volume": tx.StartPumpVolumeReading != nil,
		"has_end_volume":   tx.EndPumpVolumeReading != nil,
		"has_start_amount": tx.StartPumpAmountReading != nil,
		"has_end_amount":   tx.EndPumpAmountReading != nil,
	}
	analysis["completeness"] = completeness

	// 一致性分析
	if tx.HasPumpReadings() {
		consistency := map[string]interface{}{
			"calculated_volume": tx.CalculatedVolume().String(),
			"actual_volume":     tx.ActualVolume.String(),
			"calculated_amount": tx.CalculatedAmount().String(),
			"actual_amount":     tx.ActualAmount.String(),
		}

		// 差异计算
		if tx.PumpReadingDiscrepancy != nil {
			consistency["volume_discrepancy"] = tx.PumpReadingDiscrepancy.String()
			consistency["discrepancy_percentage"] = h.calculateDiscrepancyPercentage(
				tx.CalculatedVolume(), tx.ActualVolume)
		}

		analysis["consistency"] = consistency
	}

	// 问题识别
	issues := []string{}
	if tx.PumpReadingQuality == "poor" || tx.PumpReadingQuality == "bad" {
		issues = append(issues, "质量问题: "+tx.PumpReadingQuality)
	}
	if !tx.PumpReadingValidated {
		issues = append(issues, "未验证")
	}
	if tx.PumpReadingDiscrepancy != nil && tx.PumpReadingDiscrepancy.GreaterThan(decimal.NewFromFloat(0.01)) {
		issues = append(issues, "差异超出容差")
	}
	if !tx.HasPumpReadings() {
		issues = append(issues, "缺少泵码数据")
	}

	analysis["issues"] = issues
	analysis["has_issues"] = len(issues) > 0

	return analysis
}

// buildPumpIssuesSummary 构建泵码问题汇总
func (h *TransactionHandler) buildPumpIssuesSummary(transactions []*models.Transaction) map[string]interface{} {
	summary := map[string]interface{}{
		"total_transactions": len(transactions),
		"issues_breakdown":   make(map[string]int),
		"quality_breakdown":  make(map[string]int),
		"source_breakdown":   make(map[string]int),
	}

	issuesBreakdown := make(map[string]int)
	qualityBreakdown := make(map[string]int)
	sourceBreakdown := make(map[string]int)

	var totalDiscrepancy decimal.Decimal
	discrepancyCount := 0

	for _, tx := range transactions {
		// 统计质量分布
		if tx.PumpReadingQuality != "" {
			qualityBreakdown[tx.PumpReadingQuality]++
		} else {
			qualityBreakdown["unknown"]++
		}

		// 统计来源分布
		if tx.PumpReadingSource != "" {
			sourceBreakdown[tx.PumpReadingSource]++
		} else {
			sourceBreakdown["unknown"]++
		}

		// 统计问题类型
		if tx.PumpReadingQuality == "poor" || tx.PumpReadingQuality == "bad" {
			issuesBreakdown["quality_issues"]++
		}
		if !tx.PumpReadingValidated {
			issuesBreakdown["unvalidated"]++
		}
		if !tx.HasPumpReadings() {
			issuesBreakdown["missing_data"]++
		}
		if tx.PumpReadingDiscrepancy != nil && tx.PumpReadingDiscrepancy.GreaterThan(decimal.NewFromFloat(0.01)) {
			issuesBreakdown["high_discrepancy"]++
		}

		// 累计差异统计
		if tx.PumpReadingDiscrepancy != nil {
			totalDiscrepancy = totalDiscrepancy.Add(*tx.PumpReadingDiscrepancy)
			discrepancyCount++
		}
	}

	summary["issues_breakdown"] = issuesBreakdown
	summary["quality_breakdown"] = qualityBreakdown
	summary["source_breakdown"] = sourceBreakdown

	// 平均差异
	if discrepancyCount > 0 {
		avgDiscrepancy := totalDiscrepancy.Div(decimal.NewFromInt(int64(discrepancyCount)))
		summary["average_discrepancy"] = avgDiscrepancy.String()
	}

	return summary
}

// calculateDiscrepancyPercentage 计算差异百分比
func (h *TransactionHandler) calculateDiscrepancyPercentage(calculated, actual decimal.Decimal) string {
	if actual.IsZero() {
		return "N/A"
	}

	discrepancy := calculated.Sub(actual).Abs()
	percentage := discrepancy.Div(actual).Mul(decimal.NewFromInt(100))
	return percentage.StringFixed(2) + "%"
}
