package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	"fcc-service/pkg/api"
	"fcc-service/pkg/models"
)

// ControllerEchoHandler Echo版本的控制器处理器
type ControllerEchoHandler struct {
	deviceManager api.DeviceManager
	logger        *zap.Logger
}

// NewControllerEchoHandler 创建Echo控制器处理器
func NewControllerEchoHandler(deviceManager api.DeviceManager, logger *zap.Logger) *ControllerEchoHandler {
	return &ControllerEchoHandler{
		deviceManager: deviceManager,
		logger:        logger,
	}
}

// ListControllers 列出所有控制器 (Echo版本)
func (h *ControllerEchoHandler) ListControllers(c echo.Context) error {
	ctx := c.Request().Context()
	h.logger.Info("Processing Echo listControllers request")

	// 解析查询参数
	filter := api.ControllerFilter{}
	if stationID := c.QueryParam("station_id"); stationID != "" {
		filter.StationID = stationID
	}
	if status := c.QueryParam("status"); status != "" {
		filter.Status = models.ControllerStatus(status)
	}
	if controllerType := c.QueryParam("type"); controllerType != "" {
		filter.ControllerType = models.ControllerType(controllerType)
	}

	// 分页参数
	page := 1
	pageSize := 50
	if p := c.QueryParam("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if ps := c.QueryParam("page_size"); ps != "" {
		if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 && parsed <= 100 {
			pageSize = parsed
		}
	}

	h.logger.Info("Parsed Echo request parameters",
		zap.Any("filter", filter),
		zap.Int("page", page),
		zap.Int("page_size", pageSize))

	// 获取控制器列表
	controllers, err := h.deviceManager.ListControllers(ctx, filter)
	if err != nil {
		h.logger.Error("Failed to list controllers via Echo", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve controllers")
	}

	h.logger.Info("Retrieved controllers from manager via Echo",
		zap.Int("count", len(controllers)))

	// 转换响应
	response := &dto.ControllerListResponse{
		Controllers: make([]dto.ControllerResponse, len(controllers)),
		Total:       len(controllers),
		Page:        page,
		PageSize:    pageSize,
	}

	for i, controller := range controllers {
		response.Controllers[i] = *new(dto.ControllerResponse).FromModel(controller)
	}

	h.logger.Info("Prepared Echo response",
		zap.Int("controllers_count", len(response.Controllers)),
		zap.Int("total", response.Total),
		zap.Int("page", response.Page),
		zap.Int("page_size", response.PageSize))

	return c.JSON(http.StatusOK, response)
}

// CreateController 创建控制器 (Echo版本)
func (h *ControllerEchoHandler) CreateController(c echo.Context) error {
	ctx := c.Request().Context()

	var req dto.ControllerRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind Echo request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 转换为模型对象
	controller := req.ToModel()

	// 注册控制器
	if err := h.deviceManager.RegisterController(ctx, controller); err != nil {
		h.logger.Error("Failed to register controller via Echo",
			zap.String("controller_id", req.ID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to register controller")
	}

	// 返回创建的控制器
	response := new(dto.ControllerResponse).FromModel(controller)
	return c.JSON(http.StatusCreated, response)
}

// GetController 获取单个控制器 (Echo版本)
func (h *ControllerEchoHandler) GetController(c echo.Context) error {
	ctx := c.Request().Context()
	controllerID := c.Param("id")

	if controllerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Controller ID is required")
	}

	controller, err := h.deviceManager.GetController(ctx, controllerID)
	if err != nil {
		h.logger.Error("Failed to get controller via Echo",
			zap.String("controller_id", controllerID),
			zap.Error(err))
		if err == api.ErrControllerNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Controller not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve controller")
	}

	response := new(dto.ControllerResponse).FromModel(controller)
	return c.JSON(http.StatusOK, response)
}

// UpdateController 更新控制器 (Echo版本)
func (h *ControllerEchoHandler) UpdateController(c echo.Context) error {
	ctx := c.Request().Context()
	controllerID := c.Param("id")

	if controllerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Controller ID is required")
	}

	var req dto.ControllerRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 转换为模型对象
	controller := req.ToModel()
	controller.ID = controllerID

	// 更新控制器
	if err := h.deviceManager.UpdateController(ctx, controller); err != nil {
		h.logger.Error("Failed to update controller via Echo",
			zap.String("controller_id", controllerID),
			zap.Error(err))
		if err == api.ErrControllerNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Controller not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update controller")
	}

	response := new(dto.ControllerResponse).FromModel(controller)
	return c.JSON(http.StatusOK, response)
}

// DeleteController 删除控制器 (Echo版本)
func (h *ControllerEchoHandler) DeleteController(c echo.Context) error {
	ctx := c.Request().Context()
	controllerID := c.Param("id")

	if controllerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Controller ID is required")
	}

	if err := h.deviceManager.UnregisterController(ctx, controllerID); err != nil {
		h.logger.Error("Failed to unregister controller via Echo",
			zap.String("controller_id", controllerID),
			zap.Error(err))
		if err == api.ErrControllerNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Controller not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete controller")
	}

	return c.NoContent(http.StatusNoContent)
}

// GetControllerStatus 获取控制器状态 (Echo版本)
func (h *ControllerEchoHandler) GetControllerStatus(c echo.Context) error {
	ctx := c.Request().Context()
	controllerID := c.Param("id")

	if controllerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Controller ID is required")
	}

	status, err := h.deviceManager.GetControllerStatus(ctx, controllerID)
	if err != nil {
		h.logger.Error("Failed to get controller status via Echo",
			zap.String("controller_id", controllerID),
			zap.Error(err))
		if err == api.ErrControllerNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Controller not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve controller status")
	}

	response := dto.ControllerStatusResponse{
		ControllerID: controllerID,
		Status:       string(status.Status),
		LastSeen:     status.LastSeen,
	}

	return c.JSON(http.StatusOK, response)
}

// AddDevice 添加设备到控制器 (Echo版本)
func (h *ControllerEchoHandler) AddDevice(c echo.Context) error {
	ctx := c.Request().Context()
	controllerID := c.Param("id")

	if controllerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Controller ID is required")
	}

	var req dto.AddDeviceRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 添加设备到控制器
	if err := h.deviceManager.AddDeviceToController(ctx, controllerID, req.DeviceID); err != nil {
		h.logger.Error("Failed to add device to controller via Echo",
			zap.String("controller_id", controllerID),
			zap.String("device_id", req.DeviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to add device to controller")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message":       "Device added to controller successfully",
		"controller_id": controllerID,
		"device_id":     req.DeviceID,
	})
}

// RemoveDevice 从控制器移除设备 (Echo版本)
func (h *ControllerEchoHandler) RemoveDevice(c echo.Context) error {
	ctx := c.Request().Context()
	controllerID := c.Param("id")
	deviceID := c.Param("deviceId")

	if controllerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Controller ID is required")
	}

	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	// 从控制器移除设备
	if err := h.deviceManager.RemoveDeviceFromController(ctx, controllerID, deviceID); err != nil {
		h.logger.Error("Failed to remove device from controller via Echo",
			zap.String("controller_id", controllerID),
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to remove device from controller")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message":       "Device removed from controller successfully",
		"controller_id": controllerID,
		"device_id":     deviceID,
	})
}
