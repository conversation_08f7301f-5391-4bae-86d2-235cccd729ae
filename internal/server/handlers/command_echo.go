package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	"fcc-service/pkg/api"
)

// CommandEchoHandler echo版本的命令处理器
type CommandEchoHandler struct {
	commandExecutor api.CommandExecutor
	logger          *zap.Logger
}

// NewCommandEchoHandler 创建echo命令处理器
func NewCommandEchoHandler(commandExecutor api.CommandExecutor, logger *zap.Logger) *CommandEchoHandler {
	return &CommandEchoHandler{
		commandExecutor: commandExecutor,
		logger:          logger,
	}
}

// ExecuteCommand 执行单个命令
func (h *CommandEchoHandler) ExecuteCommand(c echo.Context) error {
	ctx := c.Request().Context()

	var req dto.CommandRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid request body", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 转换为API请求对象
	apiReq := req.ToAPIRequest()

	// 如果没有指定命令ID，生成一个
	if apiReq.CommandID == "" {
		apiReq.CommandID = fmt.Sprintf("cmd_%d", time.Now().UnixNano())
	}

	// 检查是否是异步执行
	if req.Async {
		// 异步执行
		go func() {
			execCtx, cancel := context.WithTimeout(context.Background(), apiReq.Timeout)
			defer cancel()

			_, err := h.commandExecutor.ExecuteCommand(execCtx, apiReq)
			if err != nil {
				h.logger.Error("Async command execution failed",
					zap.String("command_id", apiReq.CommandID),
					zap.Error(err))
			}
		}()

		// 立即返回命令ID
		response := &dto.AsyncCommandResponse{
			CommandID: apiReq.CommandID,
			Status:    "submitted",
			Message:   "Command submitted for async execution",
		}
		return c.JSON(http.StatusAccepted, response)
	}

	// 同步执行
	apiResp, err := h.commandExecutor.ExecuteCommand(ctx, apiReq)
	if err != nil {
		h.logger.Error("Command execution failed",
			zap.String("command_id", apiReq.CommandID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("Command execution failed: %v", err))
	}

	// 转换响应
	response := new(dto.CommandResponse).FromAPIResponse(apiResp)
	response.CommandType = req.CommandType
	return c.JSON(http.StatusOK, response)
}

// ExecuteBatchCommands 执行批量命令
func (h *CommandEchoHandler) ExecuteBatchCommands(c echo.Context) error {
	ctx := c.Request().Context()

	var req dto.BatchCommandRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid request body", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	if len(req.Commands) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, "No commands provided")
	}

	startTime := time.Now()
	batchID := fmt.Sprintf("batch_%d", startTime.UnixNano())

	// 转换为API请求对象
	apiRequests := make([]*api.CommandRequest, len(req.Commands))
	for i, cmd := range req.Commands {
		apiRequests[i] = cmd.ToAPIRequest()
		if apiRequests[i].CommandID == "" {
			apiRequests[i].CommandID = fmt.Sprintf("%s_cmd_%d", batchID, i)
		}
	}

	var results []dto.CommandResponse
	successful := 0
	failed := 0

	if req.Parallel {
		// 并行执行
		results = h.executeCommandsParallel(ctx, apiRequests)
	} else {
		// 串行执行
		results = h.executeCommandsSequential(ctx, apiRequests)
	}

	// 统计结果
	for _, result := range results {
		if result.Success {
			successful++
		} else {
			failed++
		}
	}

	response := &dto.BatchCommandResponse{
		BatchID:     batchID,
		Total:       len(req.Commands),
		Successful:  successful,
		Failed:      failed,
		Results:     results,
		TotalTime:   time.Since(startTime).Milliseconds(),
		SubmittedAt: startTime,
		CompletedAt: time.Now(),
	}

	return c.JSON(http.StatusOK, response)
}

// GetCommand 获取命令状态
func (h *CommandEchoHandler) GetCommand(c echo.Context) error {
	ctx := c.Request().Context()
	commandID := c.Param("id")

	if commandID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Command ID is required")
	}

	// 获取命令状态
	status, err := h.commandExecutor.GetCommandStatus(ctx, commandID)
	if err != nil {
		h.logger.Error("Failed to get command status",
			zap.String("command_id", commandID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Command not found")
	}

	// 转换响应
	response := &dto.CommandStatusResponse{
		CommandID:   commandID,
		Status:      string(status.State),
		DeviceID:    status.DeviceID,
		CommandType: status.CommandID,
		SubmittedAt: status.CreatedAt,
	}

	if status.Result != nil {
		if resultMap, ok := status.Result.(map[string]interface{}); ok {
			response.Data = resultMap
		}
	}
	if status.Error != "" {
		response.Error = status.Error
	}
	if !status.UpdatedAt.IsZero() {
		response.StartedAt = &status.UpdatedAt
	}
	if status.CompletedAt != nil {
		response.CompletedAt = status.CompletedAt
	}

	return c.JSON(http.StatusOK, response)
}

// CancelCommand 取消命令
func (h *CommandEchoHandler) CancelCommand(c echo.Context) error {
	ctx := c.Request().Context()
	commandID := c.Param("id")

	if commandID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Command ID is required")
	}

	if err := h.commandExecutor.CancelCommand(ctx, commandID); err != nil {
		h.logger.Error("Failed to cancel command",
			zap.String("command_id", commandID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("Failed to cancel command: %v", err))
	}

	return c.NoContent(http.StatusNoContent)
}

// ListCommands 列出命令历史
func (h *CommandEchoHandler) ListCommands(c echo.Context) error {
	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))
	if pageSize <= 0 {
		pageSize = 50
	}

	// 由于当前CommandExecutor接口不支持ListCommands，返回空列表
	response := &dto.CommandHistoryResponse{
		Commands: []dto.CommandResponse{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
	}

	return c.JSON(http.StatusOK, response)
}

// executeCommandsParallel 并行执行命令
func (h *CommandEchoHandler) executeCommandsParallel(ctx context.Context, requests []*api.CommandRequest) []dto.CommandResponse {
	results := make([]dto.CommandResponse, len(requests))
	done := make(chan int, len(requests))

	for i, req := range requests {
		go func(index int, apiReq *api.CommandRequest) {
			defer func() { done <- index }()

			apiResp, err := h.commandExecutor.ExecuteCommand(ctx, apiReq)
			if err != nil {
				results[index] = dto.CommandResponse{
					CommandID:     apiReq.CommandID,
					DeviceID:      apiReq.DeviceID,
					CommandType:   apiReq.CommandType,
					Success:       false,
					Error:         err.Error(),
					ExecutionTime: 0,
					SubmittedAt:   time.Now(),
				}
			} else {
				results[index] = *new(dto.CommandResponse).FromAPIResponse(apiResp)
				results[index].CommandType = apiReq.CommandType
			}
		}(i, req)
	}

	// 等待所有命令完成
	for i := 0; i < len(requests); i++ {
		<-done
	}

	return results
}

// executeCommandsSequential 串行执行命令
func (h *CommandEchoHandler) executeCommandsSequential(ctx context.Context, requests []*api.CommandRequest) []dto.CommandResponse {
	results := make([]dto.CommandResponse, len(requests))

	for i, req := range requests {
		apiResp, err := h.commandExecutor.ExecuteCommand(ctx, req)
		if err != nil {
			results[i] = dto.CommandResponse{
				CommandID:     req.CommandID,
				DeviceID:      req.DeviceID,
				CommandType:   req.CommandType,
				Success:       false,
				Error:         err.Error(),
				ExecutionTime: 0,
				SubmittedAt:   time.Now(),
			}
		} else {
			results[i] = *new(dto.CommandResponse).FromAPIResponse(apiResp)
			results[i].CommandType = req.CommandType
		}
	}

	return results
}
