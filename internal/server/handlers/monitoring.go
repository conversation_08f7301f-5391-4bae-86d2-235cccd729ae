package handlers

import (
	"go.uber.org/zap"

	"fcc-service/internal/storage"
)

// MonitoringHandler 监控相关的HTTP处理器
type MonitoringHandler struct {
	database storage.Database
	logger   *zap.Logger
}

// NewMonitoringHandler 创建监控处理器
func NewMonitoringHandler(database storage.Database, logger *zap.Logger) *MonitoringHandler {
	return &MonitoringHandler{
		database: database,
		logger:   logger,
	}
}

// GetDatabaseStats 获取数据库连接池统计信息
func (h *MonitoringHandler) GetDatabaseStats() map[string]interface{} {
	// 获取连接池统计信息
	var stats map[string]interface{}

	// 记录到日志
	if pgDB, ok := h.database.(*storage.PostgreSQLDatabase); ok {
		stats = pgDB.GetConnectionStats()
		pgDB.LogConnectionStats()
	} else {
		stats = map[string]interface{}{
			"error": "database type not supported",
		}
	}

	return stats
}

// GetDatabaseHealth 获取数据库健康状态
func (h *MonitoringHandler) GetDatabaseHealth() error {
	// 执行健康检查
	return h.database.HealthCheck(nil)
}
