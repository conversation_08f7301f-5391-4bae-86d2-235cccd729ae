package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	"fcc-service/internal/services/nozzle"
	v2 "fcc-service/internal/services/polling/v2"
)

// PreAuthHandler 预授权API处理器
type PreAuthHandler struct {
	dispatchTask  *v2.DispatchTask // 🔧 修改：通过DispatchTask访问设备级别的OperatorIDCache
	nozzleService nozzle.ServiceV2 // 🎯 新增：喷嘴服务依赖
	logger        *zap.Logger
}

// NewPreAuthHandler 创建预授权处理器
func NewPreAuthHandler(
	dispatchTask *v2.DispatchTask, // 🔧 修改：接收DispatchTask而不是全局OperatorIDCache
	nozzleService nozzle.ServiceV2, // 🎯 新增：喷嘴服务参数
	logger *zap.Logger,
) *PreAuthHandler {
	return &PreAuthHandler{
		dispatchTask:  dispatchTask,  // 🔧 修改：保存DispatchTask引用
		nozzleService: nozzleService, // 🎯 新增：注入喷嘴服务
		logger:        logger,
	}
}

// PreAuthResponse 预授权响应结构
type PreAuthResponse struct {
	Success   bool    `json:"success"`
	Message   string  `json:"message"`
	ExpiresIn float64 `json:"expires_in"` // 过期时间（秒）
}

// CreatePreAuth 创建预授权
// @Summary 创建预授权
// @Description 创建预授权记录，缓存30秒，用于后续DC3事件匹配
// @Tags Wayne预授权
// @Accept json
// @Produce json
// @Param request body dto.PreAuthRequest true "预授权请求"
// @Success 200 {object} PreAuthResponse "预授权创建成功"
// @Failure 400 {object} map[string]string "参数错误"
// @Failure 403 {object} map[string]string "预授权功能未启用"
// @Failure 500 {object} map[string]string "内部错误"
// @Router /api/v2/wayne/preauth [post]
func (h *PreAuthHandler) CreatePreAuth(c echo.Context) error {
	ctx := c.Request().Context()

	// 🔧 修改：需要先解析请求获取device_id，然后获取对应设备的OperatorIDCache
	var req dto.PreAuthRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("预授权请求参数绑定失败", zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "无效的请求参数",
		})
	}

	// 🆕 检查同一个 Pump 下有没有有效时间内的 preauth
	hasActivePreauth, err := h.nozzleService.CheckActivePreAuthOnDevice(c.Request().Context(), req.DeviceID)
	if err != nil {
		h.logger.Error("检查设备预授权状态失败",
			zap.String("device_id", req.DeviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "检查预授权状态失败: "+err.Error())
	}

	if hasActivePreauth {
		h.logger.Warn("预授权被拒绝：设备上已存在有效的预授权",
			zap.String("device_id", req.DeviceID),
			zap.String("requested_nozzle_id", *req.NozzleID))
		return echo.NewHTTPError(http.StatusConflict, "PreAuth rejected: device already has active preauth")
	}

	// 验证 NozzleID 是否提供
	if req.NozzleID == nil || *req.NozzleID == "" {
		h.logger.Error("预授权请求缺少 NozzleID")
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "nozzle_id is required for preauth",
		})
	}

	// 检查 Pump 和 Nozzle 状态并锁 Nozzle
	err = h.nozzleService.CheckFillingStatusAndLockNozzle(c.Request().Context(), req.DeviceID, *req.NozzleID)
	if err != nil {
		return echo.NewHTTPError(http.StatusConflict, "PreAuth rejected: "+err.Error())
	}

	// 🆕 检查泵码完整性 - 确保有体积和金额两个泵码才允许授权
	err = h.nozzleService.CheckPumpReadingIntegrity(c.Request().Context(), req.DeviceID, *req.NozzleID)
	if err != nil {
		h.logger.Warn("PreAuth rejected due to incomplete pump readings",
			zap.String("device_id", req.DeviceID),
			zap.String("nozzle_id", *req.NozzleID),
			zap.Error(err))

		// 🚀 触发CD101请求获取缺失的泵码
		go func() {
			h.logger.Info("Triggering CD101 request for missing pump readings",
				zap.String("device_id", req.DeviceID),
				zap.String("nozzle_id", *req.NozzleID))

			// 通过设备轮询器发送CD101请求
			if devicePoller, err := h.dispatchTask.GetDevice(req.DeviceID); err == nil {
				// 发送两个计数器请求：体积和金额
				nozzle, err := h.nozzleService.GetNozzleByID(c.Request().Context(), *req.NozzleID)
				if err == nil {
					volumeCounterType := byte(nozzle.Number)      // 0x01-0x08
					amountCounterType := byte(nozzle.Number + 16) // 0x11-0x18

					// 发送体积计数器请求
					if err := devicePoller.RequestCounters(v2.CounterTypeEnum(volumeCounterType)); err != nil {
						h.logger.Error("Failed to request volume counter",
							zap.String("device_id", req.DeviceID),
							zap.Uint8("counter_type", volumeCounterType),
							zap.Error(err))
					}

					// 发送金额计数器请求
					if err := devicePoller.RequestCounters(v2.CounterTypeEnum(amountCounterType)); err != nil {
						h.logger.Error("Failed to request amount counter",
							zap.String("device_id", req.DeviceID),
							zap.Uint8("counter_type", amountCounterType),
							zap.Error(err))
					}
				}
			} else {
				h.logger.Error("Failed to get device poller for CD101 request",
					zap.String("device_id", req.DeviceID),
					zap.Error(err))
			}
		}()

		return echo.NewHTTPError(http.StatusPreconditionFailed,
			"PreAuth rejected: incomplete pump readings. CD101 request sent to retrieve missing data. Please retry after a few seconds.")
	}

	// 获取设备的DevicePoller
	devicePoller, err := h.dispatchTask.GetDevice(req.PresetVolumeRequest.DeviceID)
	if err != nil {
		h.logger.Error("设备不存在或未启动",
			zap.String("device_id", req.PresetVolumeRequest.DeviceID),
			zap.Error(err))
		return c.JSON(http.StatusNotFound, map[string]string{
			"error": "设备不存在或未启动",
		})
	}

	// 获取设备的OperatorIDCache
	operatorIDCache := devicePoller.GetOperatorIDCache()
	if operatorIDCache == nil {
		h.logger.Error("设备的OperatorIDCache未初始化",
			zap.String("device_id", req.PresetVolumeRequest.DeviceID))
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error": "设备缓存未初始化",
		})
	}

	//
	operatorIDCache.Set(req.EmployeeID)

	// 检查功能是否启用
	if !operatorIDCache.IsPreAuthEnabled() {
		h.logger.Warn("预授权功能未启用", zap.String("remote_addr", c.Request().RemoteAddr))
		return c.JSON(http.StatusForbidden, map[string]string{
			"error": "预授权功能未启用",
		})
	}

	// 注释：请求参数已经在上面绑定过了，这里不需要重复绑定

	// 验证PreAuthRequest
	if err := req.Validate(); err != nil {
		h.logger.Error("预授权请求参数验证失败", zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("预授权参数错误: %v", err),
		})
	}

	// 🎯 关键：通过 NozzleID 查表获取 NozzleNumber（与 wayne_command_handler.go 相同的逻辑）
	h.logger.Info("Looking up nozzle by ID for preauth", zap.String("nozzle_id", *req.NozzleID))

	// 设置查询超时
	queryCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	nozzle, err := h.nozzleService.GetNozzleByID(queryCtx, *req.NozzleID)
	if err != nil {
		h.logger.Error("Failed to get nozzle by ID for preauth",
			zap.String("nozzle_id", *req.NozzleID),
			zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("查询喷嘴失败: %v", err),
		})
	}

	h.logger.Info("Nozzle found successfully for preauth",
		zap.String("nozzle_id", *req.NozzleID),
		zap.String("device_id", nozzle.DeviceID),
		zap.Uint8("nozzle_number", nozzle.Number),
		zap.Bool("is_enabled", nozzle.IsEnabled))

	// 验证喷嘴是否属于当前设备
	if nozzle.DeviceID != req.PresetVolumeRequest.DeviceID {
		h.logger.Error("Nozzle device mismatch for preauth",
			zap.String("nozzle_id", *req.NozzleID),
			zap.String("nozzle_device_id", nozzle.DeviceID),
			zap.String("request_device_id", req.PresetVolumeRequest.DeviceID))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("喷嘴 %s 不属于设备 %s", *req.NozzleID, req.PresetVolumeRequest.DeviceID),
		})
	}

	// 存储预授权数据
	if err := operatorIDCache.SetPreAuth(ctx, nozzle.ID, &req); err != nil {
		h.logger.Error("预授权存储失败",
			zap.Error(err),
			zap.String("device_id", req.PresetVolumeRequest.DeviceID),
			zap.String("nozzle_id", *req.NozzleID),
			zap.Uint8("nozzle_number", nozzle.Number))
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error": "预授权存储失败",
		})
	}

	// 发送 reset
	devicePoller.ResetDevice()

	h.logger.Info("预授权创建成功",
		zap.String("device_id", req.PresetVolumeRequest.DeviceID),
		zap.String("nozzle_id", *req.NozzleID),
		zap.Uint8("nozzle_number", nozzle.Number),
		zap.String("auth_type", req.AuthType),
		zap.String("volume", req.Volume.String()),
		zap.Duration("ttl", operatorIDCache.GetPreAuthTTL()))

	// 🎯 预授权成功后，主动更新喷嘴，把授权数量，状态和时间戳同步到数据库
	expiresAt := time.Now().Add(operatorIDCache.GetPreAuthTTL() - 2*time.Second)

	// 确定预授权数字
	var preAuthNumber decimal.Decimal
	if req.AuthType == "preset_full" {
		preAuthNumber = req.Volume // preset full tank 是用一个大升数实现
	} else if req.AuthType == "preset_volume" {
		preAuthNumber = req.Volume
	} else if req.AuthType == "preset_amount" && req.Amount != nil {
		preAuthNumber = *req.Amount
	} else {
		h.logger.Error("预授权类型错误或缺少金额",
			zap.String("auth_type", req.AuthType),
			zap.Any("amount", req.Amount))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "预授权类型错误或缺少金额",
		})
	}

	// 更新喷嘴预授权信息到数据库
	if err := h.nozzleService.UpdateNozzlePreAuth(ctx, nozzle.ID, req.AuthType, preAuthNumber, expiresAt); err != nil {
		h.logger.Error("更新喷嘴预授权信息失败",
			zap.Error(err),
			zap.String("device_id", req.PresetVolumeRequest.DeviceID),
			zap.String("nozzle_id", nozzle.ID),
			zap.String("auth_type", req.AuthType),
			zap.String("preauth_number", preAuthNumber.String()))
		// 注意：这里不返回错误，因为预授权已经存储在缓存中，数据库更新失败不应该影响预授权功能
		// 但会记录错误日志用于监控和调试
	} else {
		h.logger.Info("喷嘴预授权信息已同步到数据库",
			zap.String("device_id", req.PresetVolumeRequest.DeviceID),
			zap.String("nozzle_id", nozzle.ID),
			zap.String("auth_type", req.AuthType),
			zap.String("preauth_number", preAuthNumber.String()),
			zap.Time("expires_at", expiresAt))
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, PreAuthResponse{
		Success:   true,
		Message:   "预授权创建成功:req.EmployeeID operatorIDC," + req.EmployeeID,
		ExpiresIn: operatorIDCache.GetPreAuthTTL().Seconds(),
	})
}

// GetPreAuth 获取预授权信息（用于调试）
// @Summary 获取预授权信息
// @Description 获取指定设备和喷嘴的预授权信息
// @Tags Wayne预授权
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param nozzle_id path string true "喷嘴ID"
// @Success 200 {object} dto.PresetVolumeRequest "预授权信息"
// @Failure 404 {object} map[string]string "预授权不存在"
// @Failure 500 {object} map[string]string "内部错误"
// @Router /api/v2/wayne/preauth/{device_id}/{nozzle_id} [get]
func (h *PreAuthHandler) GetPreAuth(c echo.Context) error {
	ctx := c.Request().Context()

	deviceID := c.Param("device_id")
	nozzleID := c.Param("nozzle_id")

	if deviceID == "" || nozzleID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "device_id 和 nozzle_id 都是必需的",
		})
	}

	// 获取设备的DevicePoller
	devicePoller, err := h.dispatchTask.GetDevice(deviceID)
	if err != nil {
		h.logger.Error("设备不存在或未启动",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return c.JSON(http.StatusNotFound, map[string]string{
			"error": "设备不存在或未启动",
		})
	}

	// 获取设备的OperatorIDCache
	operatorIDCache := devicePoller.GetOperatorIDCache()
	if operatorIDCache == nil {
		h.logger.Error("设备的OperatorIDCache未初始化",
			zap.String("device_id", deviceID))
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error": "设备缓存未初始化",
		})
	}

	// 🎯 通过 NozzleID 查表获取 NozzleNumber
	h.logger.Debug("Looking up nozzle by ID for GetPreAuth", zap.String("nozzle_id", nozzleID))

	queryCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	nozzle, err := h.nozzleService.GetNozzleByID(queryCtx, nozzleID)
	if err != nil {
		h.logger.Error("Failed to get nozzle by ID for GetPreAuth",
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("查询喷嘴失败: %v", err),
		})
	}

	// 验证喷嘴是否属于当前设备
	if nozzle.DeviceID != deviceID {
		h.logger.Error("Nozzle device mismatch for GetPreAuth",
			zap.String("nozzle_id", nozzleID),
			zap.String("nozzle_device_id", nozzle.DeviceID),
			zap.String("request_device_id", deviceID))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("喷嘴 %s 不属于设备 %s", nozzleID, deviceID),
		})
	}

	// 从缓存获取预授权数据
	preauth := operatorIDCache.GetPreAuth(ctx, nozzle.ID)
	if preauth == nil {
		h.logger.Debug("预授权不存在",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return c.JSON(http.StatusNotFound, map[string]string{
			"error": "预授权不存在或已过期",
		})
	}

	h.logger.Info("预授权获取成功",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("auth_type", preauth.AuthType),
		zap.String("volume", preauth.Volume.String()))

	return c.JSON(http.StatusOK, preauth)
}

// DeletePreAuth 删除预授权（用于调试）
// @Summary 删除预授权
// @Description 删除指定设备和喷嘴的预授权信息
// @Tags Wayne预授权
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param nozzle_id path string true "喷嘴ID"
// @Success 200 {object} map[string]string "删除成功"
// @Failure 500 {object} map[string]string "内部错误"
// @Router /api/v2/wayne/preauth/{device_id}/{nozzle_id} [delete]
func (h *PreAuthHandler) DeletePreAuth(c echo.Context) error {
	ctx := c.Request().Context()

	deviceID := c.Param("device_id")
	nozzleID := c.Param("nozzle_id")

	if deviceID == "" || nozzleID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "device_id 和 nozzle_id 都是必需的",
		})
	}

	// 获取设备的DevicePoller
	devicePoller, err := h.dispatchTask.GetDevice(deviceID)
	if err != nil {
		h.logger.Error("设备不存在或未启动",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return c.JSON(http.StatusNotFound, map[string]string{
			"error": "设备不存在或未启动",
		})
	}

	// 获取设备的OperatorIDCache
	operatorIDCache := devicePoller.GetOperatorIDCache()
	if operatorIDCache == nil {
		h.logger.Error("设备的OperatorIDCache未初始化",
			zap.String("device_id", deviceID))
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error": "设备缓存未初始化",
		})
	}

	// 🎯 通过 NozzleID 查表获取 NozzleNumber
	h.logger.Debug("Looking up nozzle by ID for DeletePreAuth", zap.String("nozzle_id", nozzleID))

	queryCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	nozzle, err := h.nozzleService.GetNozzleByID(queryCtx, nozzleID)
	if err != nil {
		h.logger.Error("Failed to get nozzle by ID for DeletePreAuth",
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("查询喷嘴失败: %v", err),
		})
	}

	// 验证喷嘴是否属于当前设备
	if nozzle.DeviceID != deviceID {
		h.logger.Error("Nozzle device mismatch for DeletePreAuth",
			zap.String("nozzle_id", nozzleID),
			zap.String("nozzle_device_id", nozzle.DeviceID),
			zap.String("request_device_id", deviceID))
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": fmt.Sprintf("喷嘴 %s 不属于设备 %s", nozzleID, deviceID),
		})
	}

	// 消费预授权数据（等同于删除）
	preauth := operatorIDCache.ConsumePreAuth(ctx, nozzle.ID)
	if preauth == nil {
		h.logger.Debug("预授权不存在或已过期",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
		return c.JSON(http.StatusNotFound, map[string]string{
			"error": "预授权不存在或已过期",
		})
	}

	// 🆕 清理数据库中的预授权信息
	if err := h.nozzleService.ClearNozzlePreAuth(ctx, nozzle.ID); err != nil {
		h.logger.Error("清理数据库预授权信息失败",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID),
			zap.Error(err))
		// 注意：这里不返回错误，因为缓存中的预授权已经删除，数据库清理失败不应该影响删除操作
		// 但会记录错误日志用于监控和调试
	} else {
		h.logger.Info("数据库预授权信息已清理",
			zap.String("device_id", deviceID),
			zap.String("nozzle_id", nozzleID))
	}

	h.logger.Info("预授权删除成功",
		zap.String("device_id", deviceID),
		zap.String("nozzle_id", nozzleID),
		zap.String("auth_type", preauth.AuthType),
		zap.String("volume", preauth.Volume.String()))

	return c.JSON(http.StatusOK, map[string]string{
		"message": "预授权删除成功",
	})
}
