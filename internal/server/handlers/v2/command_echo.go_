package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	v2 "fcc-service/internal/services/polling/v2"
	"fcc-service/pkg/api"
	v2models "fcc-service/pkg/models/v2"
)

// CommandEchoHandlerV2 纯v2架构的命令处理器
type CommandEchoHandlerV2 struct {
	dispatchTask  *v2.DispatchTask            // v2调度任务
	stateManager  v2models.DeviceStateManager // v2状态管理器
	deviceManager api.DeviceManager           // 设备管理器（数据库+缓存）
	logger        *zap.Logger

	// 命令历史管理
	commandHistory map[string]*CommandRecordV2
	historyMutex   sync.RWMutex
}

// CommandRecordV2 v2命令记录
type CommandRecordV2 struct {
	ID            string                 `json:"id"`
	DeviceID      string                 `json:"device_id"`
	CommandType   string                 `json:"command_type"`
	BusinessType  string                 `json:"business_type"`
	Parameters    map[string]interface{} `json:"parameters"`
	Status        string                 `json:"status"` // submitted, executing, completed, failed, cancelled
	Success       bool                   `json:"success"`
	Data          map[string]interface{} `json:"data"`
	Error         string                 `json:"error"`
	SubmittedAt   time.Time              `json:"submitted_at"`
	StartedAt     *time.Time             `json:"started_at"`
	CompletedAt   *time.Time             `json:"completed_at"`
	ExecutionTime int64                  `json:"execution_time_ms"`
	IsAsync       bool                   `json:"is_async"`
	Priority      string                 `json:"priority"`
	V2Command     *v2.PollCommand        `json:"v2_command,omitempty"`
}

// NewCommandEchoHandlerV2 创建v2命令处理器
func NewCommandEchoHandlerV2(
	dispatchTask *v2.DispatchTask,
	stateManager v2models.DeviceStateManager,
	deviceManager api.DeviceManager,
	logger *zap.Logger,
) *CommandEchoHandlerV2 {
	return &CommandEchoHandlerV2{
		dispatchTask:   dispatchTask,
		stateManager:   stateManager,
		deviceManager:  deviceManager,
		logger:         logger,
		commandHistory: make(map[string]*CommandRecordV2),
	}
}

// ExecuteCommand 执行单个命令
func (h *CommandEchoHandlerV2) ExecuteCommand(c echo.Context) error {
	var req dto.CommandRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid command request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// 验证请求
	if err := h.validateCommandRequest(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 验证设备存在性
	ctx := context.Background()
	_, err := h.deviceManager.GetDevice(ctx, req.DeviceID)
	if err != nil {
		h.logger.Error("Device not found for command",
			zap.String("device_id", req.DeviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 生成命令ID
	if req.CommandID == "" {
		req.CommandID = fmt.Sprintf("v2_cmd_%d", time.Now().UnixNano())
	}

	h.logger.Info("Processing v2 command execution",
		zap.String("command_id", req.CommandID),
		zap.String("device_id", req.DeviceID),
		zap.String("command_type", req.CommandType),
		zap.Bool("async", req.Async))

	// 创建命令记录
	commandRecord := &CommandRecordV2{
		ID:          req.CommandID,
		DeviceID:    req.DeviceID,
		CommandType: req.CommandType,
		Parameters:  req.Parameters,
		Status:      "submitted",
		SubmittedAt: time.Now(),
		IsAsync:     req.Async,
		Priority:    req.Priority,
	}

	// 检查是否为Wayne协议命令
	// 🔧 修复：Wayne命令直接调用DevicePoller方法，不构建PollCommand
	if h.isWayneCommand(req.CommandType) {
		commandRecord.BusinessType = h.mapWayneCommandToBusinessType(req.CommandType)
		// Wayne命令通过sendWayneCommand直接处理，不需要预构建PollCommand
		commandRecord.V2Command = nil
	} else {
		commandRecord.BusinessType = req.CommandType
		v2Cmd := h.buildGenericCommand(&req)
		commandRecord.V2Command = &v2Cmd
	}

	// 存储命令记录
	h.storeCommandRecord(commandRecord)

	if req.Async {
		// 异步执行
		go h.executeCommandAsync(req.DeviceID, *commandRecord.V2Command, commandRecord)

		response := dto.AsyncCommandResponse{
			CommandID: req.CommandID,
			Status:    "submitted",
			Message:   "Command submitted for async execution",
		}
		return c.JSON(http.StatusAccepted, response)
	}

	// 同步执行
	return h.executeCommandSync(c, req.DeviceID, *commandRecord.V2Command, commandRecord)
}

// ExecuteBatchCommands 执行批量命令
func (h *CommandEchoHandlerV2) ExecuteBatchCommands(c echo.Context) error {
	var req dto.BatchCommandRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid batch command request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	if len(req.Commands) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, "No commands provided")
	}

	startTime := time.Now()
	batchID := fmt.Sprintf("batch_v2_%d", startTime.UnixNano())

	h.logger.Info("Processing v2 batch command execution",
		zap.String("batch_id", batchID),
		zap.Int("command_count", len(req.Commands)),
		zap.Bool("parallel", req.Parallel))

	var results []dto.CommandResponse
	successful := 0
	failed := 0

	if req.Parallel {
		// 并行执行
		results = h.executeCommandsParallel(req.Commands, batchID)
	} else {
		// 串行执行
		results = h.executeCommandsSequential(req.Commands, batchID)
	}

	// 统计结果
	for _, result := range results {
		if result.Success {
			successful++
		} else {
			failed++
		}
	}

	response := &dto.BatchCommandResponse{
		BatchID:     batchID,
		Total:       len(req.Commands),
		Successful:  successful,
		Failed:      failed,
		Results:     results,
		TotalTime:   time.Since(startTime).Milliseconds(),
		SubmittedAt: startTime,
		CompletedAt: time.Now(),
	}

	return c.JSON(http.StatusOK, response)
}

// GetCommand 获取命令状态
func (h *CommandEchoHandlerV2) GetCommand(c echo.Context) error {
	commandID := c.Param("id")
	if commandID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Command ID is required")
	}

	h.historyMutex.RLock()
	record, exists := h.commandHistory[commandID]
	h.historyMutex.RUnlock()

	if !exists {
		return echo.NewHTTPError(http.StatusNotFound, "Command not found")
	}

	response := &dto.CommandStatusResponse{
		CommandID:   record.ID,
		Status:      record.Status,
		DeviceID:    record.DeviceID,
		CommandType: record.CommandType,
		Success: func() *bool {
			if record.Status == "completed" || record.Status == "failed" {
				return &record.Success
			}
			return nil
		}(),
		Data:        record.Data,
		SubmittedAt: record.SubmittedAt,
		StartedAt:   record.StartedAt,
		CompletedAt: record.CompletedAt,
	}

	if record.Error != "" {
		response.Error = record.Error
	}
	if record.ExecutionTime > 0 {
		response.ExecutionTime = &record.ExecutionTime
	}

	return c.JSON(http.StatusOK, response)
}

// CancelCommand 取消命令
func (h *CommandEchoHandlerV2) CancelCommand(c echo.Context) error {
	commandID := c.Param("id")
	if commandID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Command ID is required")
	}

	h.historyMutex.Lock()
	record, exists := h.commandHistory[commandID]
	if exists && record.Status == "submitted" {
		record.Status = "cancelled"
		record.CompletedAt = &[]time.Time{time.Now()}[0]
		record.Error = "Command cancelled by user"
	}
	h.historyMutex.Unlock()

	if !exists {
		return echo.NewHTTPError(http.StatusNotFound, "Command not found")
	}

	h.logger.Info("Command cancelled",
		zap.String("command_id", commandID))

	return c.NoContent(http.StatusNoContent)
}

// ListCommands 列出命令历史
func (h *CommandEchoHandlerV2) ListCommands(c echo.Context) error {
	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))
	if pageSize <= 0 {
		pageSize = 50
	}

	deviceID := c.QueryParam("device_id")
	status := c.QueryParam("status")

	h.historyMutex.RLock()
	var filteredRecords []*CommandRecordV2
	for _, record := range h.commandHistory {
		if deviceID != "" && record.DeviceID != deviceID {
			continue
		}
		if status != "" && record.Status != status {
			continue
		}
		filteredRecords = append(filteredRecords, record)
	}
	h.historyMutex.RUnlock()

	// 转换为响应格式
	commands := make([]dto.CommandResponse, 0, len(filteredRecords))
	for _, record := range filteredRecords {
		commands = append(commands, dto.CommandResponse{
			CommandID:     record.ID,
			DeviceID:      record.DeviceID,
			CommandType:   record.CommandType,
			Success:       record.Success,
			Data:          record.Data,
			Error:         record.Error,
			ExecutionTime: record.ExecutionTime,
			SubmittedAt:   record.SubmittedAt,
			CompletedAt:   record.CompletedAt,
		})
	}

	// 应用分页
	start := (page - 1) * pageSize
	end := start + pageSize
	if start > len(commands) {
		start = len(commands)
	}
	if end > len(commands) {
		end = len(commands)
	}

	response := &dto.CommandHistoryResponse{
		Commands: commands[start:end],
		Total:    len(commands),
		Page:     page,
		PageSize: pageSize,
	}

	return c.JSON(http.StatusOK, response)
}

// 🚀 内部方法

// validateCommandRequest 验证命令请求
func (h *CommandEchoHandlerV2) validateCommandRequest(req *dto.CommandRequest) error {
	if req.DeviceID == "" {
		return fmt.Errorf("device_id is required")
	}
	if req.CommandType == "" {
		return fmt.Errorf("command_type is required")
	}
	return nil
}

// isWayneCommand 检查是否为Wayne协议命令
func (h *CommandEchoHandlerV2) isWayneCommand(commandType string) bool {
	wayneCommands := map[string]bool{
		"authorize":           true, // CD1: AUTHORIZE
		"reset":               true, // CD1: RESET
		"preset_volume":       true, // CD3: 预设体积
		"preset_amount":       true, // CD4: 预设金额
		"update_prices":       true, // CD5: 价格更新
		"configure_nozzles":   true, // CD2: 喷嘴配置 🚀 新增
		"suspend_nozzle":      true, // CD14: 暂停喷嘴
		"resume_nozzle":       true, // CD15: 恢复喷嘴
		"request_counters":    true, // CD101: 查询计数器
		"return_status":       true, // CD1: RETURN STATUS
		"return_filling_info": true, // CD1: RETURN FILLING INFORMATION
	}
	return wayneCommands[commandType]
}

// mapWayneCommandToBusinessType 映射Wayne命令到业务类型
func (h *CommandEchoHandlerV2) mapWayneCommandToBusinessType(commandType string) string {
	mapping := map[string]string{
		"authorize":           "authorize",
		"reset":               "reset",
		"preset_volume":       "preset",
		"preset_amount":       "preset",
		"update_prices":       "price_update",
		"suspend_nozzle":      "stop",
		"resume_nozzle":       "authorize",
		"request_counters":    "status",
		"return_status":       "status",
		"return_filling_info": "status",
	}
	if businessType, exists := mapping[commandType]; exists {
		return businessType
	}
	return "unknown"
}

// buildGenericCommand 构建通用命令
func (h *CommandEchoHandlerV2) buildGenericCommand(req *dto.CommandRequest) v2.PollCommand {
	timeout := 30 * time.Second
	if req.Timeout > 0 {
		timeout = time.Duration(req.Timeout) * time.Second
	}

	priority := v2.PriorityMedium
	switch req.Priority {
	case "high":
		priority = v2.PriorityHigh
	case "low":
		priority = v2.PriorityLow
	}

	return v2.PollCommand{
		Type:         "generic_command",
		Data:         req.Parameters,
		Timeout:      timeout,
		Timestamp:    time.Now(),
		BusinessType: req.CommandType,
		Priority:     int(priority),
		Retryable:    true,
		MaxRetries:   3,
	}
}

// isRetryableCommand 判断命令是否可重试
func (h *CommandEchoHandlerV2) isRetryableCommand(commandType string) bool {
	nonRetryableCommands := map[string]bool{
		"preset_volume": true, // 预设操作不应重试
		"preset_amount": true,
	}
	return !nonRetryableCommands[commandType]
}

// getMaxRetries 获取最大重试次数
func (h *CommandEchoHandlerV2) getMaxRetries(commandType string) int {
	retryConfig := map[string]int{
		"authorize":        3,
		"reset":            3,
		"preset_volume":    0,
		"preset_amount":    0,
		"update_prices":    2,
		"suspend_nozzle":   3,
		"resume_nozzle":    3,
		"request_counters": 2,
	}
	if retries, exists := retryConfig[commandType]; exists {
		return retries
	}
	return 1
}

// executeCommandSync 同步执行命令
func (h *CommandEchoHandlerV2) executeCommandSync(c echo.Context, deviceID string, cmd v2.PollCommand, record *CommandRecordV2) error {
	startTime := time.Now()
	record.StartedAt = &startTime
	record.Status = "executing"
	h.updateCommandRecord(record)

	// 执行命令
	if err := h.dispatchTask.SendCommand(deviceID, cmd); err != nil {
		h.logger.Error("Failed to execute command",
			zap.String("command_id", record.ID),
			zap.String("device_id", deviceID),
			zap.Error(err))

		// 更新失败记录
		completedTime := time.Now()
		record.CompletedAt = &completedTime
		record.Status = "failed"
		record.Success = false
		record.Error = err.Error()
		record.ExecutionTime = completedTime.Sub(startTime).Milliseconds()
		h.updateCommandRecord(record)

		return echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("Command execution failed: %v", err))
	}

	// 等待执行结果（简化版，实际应通过结果通道获取）
	time.Sleep(100 * time.Millisecond)

	// 更新成功记录
	completedTime := time.Now()
	record.CompletedAt = &completedTime
	record.Status = "completed"
	record.Success = true
	record.Data = map[string]interface{}{
		"message":       "Command executed successfully",
		"device_id":     deviceID,
		"command_id":    record.ID,
		"business_type": record.BusinessType,
		"protocol":      "Wayne DART v2",
	}
	record.ExecutionTime = completedTime.Sub(startTime).Milliseconds()
	h.updateCommandRecord(record)

	// 构建响应
	response := dto.CommandResponse{
		CommandID:     record.ID,
		DeviceID:      deviceID,
		CommandType:   record.CommandType,
		Success:       true,
		Data:          record.Data,
		ExecutionTime: record.ExecutionTime,
		SubmittedAt:   record.SubmittedAt,
		CompletedAt:   record.CompletedAt,
	}

	h.logger.Info("Command executed successfully",
		zap.String("command_id", record.ID),
		zap.String("device_id", deviceID),
		zap.Int64("execution_time_ms", record.ExecutionTime))

	return c.JSON(http.StatusOK, response)
}

// executeCommandAsync 异步执行命令
func (h *CommandEchoHandlerV2) executeCommandAsync(deviceID string, cmd v2.PollCommand, record *CommandRecordV2) {
	startTime := time.Now()
	record.StartedAt = &startTime
	record.Status = "executing"
	h.updateCommandRecord(record)

	// 执行命令
	if err := h.dispatchTask.SendCommand(deviceID, cmd); err != nil {
		completedTime := time.Now()
		record.CompletedAt = &completedTime
		record.Status = "failed"
		record.Success = false
		record.Error = err.Error()
		record.ExecutionTime = completedTime.Sub(startTime).Milliseconds()
		h.updateCommandRecord(record)

		h.logger.Error("Async command execution failed",
			zap.String("command_id", record.ID),
			zap.String("device_id", deviceID),
			zap.Error(err))
		return
	}

	// 等待执行结果
	time.Sleep(100 * time.Millisecond)

	// 更新成功记录
	completedTime := time.Now()
	record.CompletedAt = &completedTime
	record.Status = "completed"
	record.Success = true
	record.Data = map[string]interface{}{
		"message":       "Command executed successfully",
		"device_id":     deviceID,
		"command_id":    record.ID,
		"business_type": record.BusinessType,
	}
	record.ExecutionTime = completedTime.Sub(startTime).Milliseconds()
	h.updateCommandRecord(record)

	h.logger.Info("Async command executed successfully",
		zap.String("command_id", record.ID),
		zap.String("device_id", deviceID),
		zap.Int64("execution_time_ms", record.ExecutionTime))
}

// executeCommandsParallel 并行执行命令
func (h *CommandEchoHandlerV2) executeCommandsParallel(commands []dto.CommandRequest, batchID string) []dto.CommandResponse {
	results := make([]dto.CommandResponse, len(commands))
	var wg sync.WaitGroup

	for i, cmd := range commands {
		wg.Add(1)
		go func(index int, command dto.CommandRequest) {
			defer wg.Done()

			if command.CommandID == "" {
				command.CommandID = fmt.Sprintf("%s_cmd_%d", batchID, index)
			}

			// 执行单个命令（简化版）
			startTime := time.Now()
			err := h.executeIndividualCommand(&command)

			if err != nil {
				results[index] = dto.CommandResponse{
					CommandID:     command.CommandID,
					DeviceID:      command.DeviceID,
					CommandType:   command.CommandType,
					Success:       false,
					Error:         err.Error(),
					ExecutionTime: time.Since(startTime).Milliseconds(),
					SubmittedAt:   startTime,
				}
			} else {
				completedTime := time.Now()
				results[index] = dto.CommandResponse{
					CommandID:   command.CommandID,
					DeviceID:    command.DeviceID,
					CommandType: command.CommandType,
					Success:     true,
					Data: map[string]interface{}{
						"message": "Command executed successfully",
					},
					ExecutionTime: time.Since(startTime).Milliseconds(),
					SubmittedAt:   startTime,
					CompletedAt:   &completedTime,
				}
			}
		}(i, cmd)
	}

	wg.Wait()
	return results
}

// executeCommandsSequential 串行执行命令
func (h *CommandEchoHandlerV2) executeCommandsSequential(commands []dto.CommandRequest, batchID string) []dto.CommandResponse {
	results := make([]dto.CommandResponse, len(commands))

	for i, cmd := range commands {
		if cmd.CommandID == "" {
			cmd.CommandID = fmt.Sprintf("%s_cmd_%d", batchID, i)
		}

		startTime := time.Now()
		err := h.executeIndividualCommand(&cmd)

		if err != nil {
			results[i] = dto.CommandResponse{
				CommandID:     cmd.CommandID,
				DeviceID:      cmd.DeviceID,
				CommandType:   cmd.CommandType,
				Success:       false,
				Error:         err.Error(),
				ExecutionTime: time.Since(startTime).Milliseconds(),
				SubmittedAt:   startTime,
			}
		} else {
			completedTime := time.Now()
			results[i] = dto.CommandResponse{
				CommandID:   cmd.CommandID,
				DeviceID:    cmd.DeviceID,
				CommandType: cmd.CommandType,
				Success:     true,
				Data: map[string]interface{}{
					"message": "Command executed successfully",
				},
				ExecutionTime: time.Since(startTime).Milliseconds(),
				SubmittedAt:   startTime,
				CompletedAt:   &completedTime,
			}
		}
	}

	return results
}

// executeIndividualCommand 执行单个命令（简化版）
func (h *CommandEchoHandlerV2) executeIndividualCommand(req *dto.CommandRequest) error {
	// 验证设备存在性
	ctx := context.Background()
	_, err := h.deviceManager.GetDevice(ctx, req.DeviceID)
	if err != nil {
		return fmt.Errorf("device not found: %v", err)
	}

	// 🔧 修复：Wayne命令和通用命令分别处理
	if h.isWayneCommand(req.CommandType) {
		// Wayne命令：直接调用DevicePoller方法
		devicePoller, err := h.dispatchTask.GetDevice(req.DeviceID)
		if err != nil {
			return fmt.Errorf("device not found: %v", err)
		}
		return h.executeWayneCommandDirect(devicePoller, req)
	} else {
		// 通用命令：构建PollCommand发送
		cmd := h.buildGenericCommand(req)
		return h.dispatchTask.SendCommand(req.DeviceID, cmd)
	}
}

// storeCommandRecord 存储命令记录
func (h *CommandEchoHandlerV2) storeCommandRecord(record *CommandRecordV2) {
	h.historyMutex.Lock()
	h.commandHistory[record.ID] = record
	h.historyMutex.Unlock()
}

// updateCommandRecord 更新命令记录
func (h *CommandEchoHandlerV2) updateCommandRecord(record *CommandRecordV2) {
	h.historyMutex.Lock()
	h.commandHistory[record.ID] = record
	h.historyMutex.Unlock()
}

// executeWayneCommandDirect 直接执行Wayne命令（用于批量处理）
// 🚨 DEPRECATED: 这个方法已被弃用，请使用新的类型安全的Wayne命令处理器
// 新的处理器位于：internal/server/handlers/v2/wayne_command_handler.go
// 新的API端点：/api/v2/wayne/{command}
// 详细文档：docs/Wayne_Commands_API_Guide.md
func (h *CommandEchoHandlerV2) executeWayneCommandDirect(devicePoller *v2.DevicePoller, req *dto.CommandRequest) error {
	h.logger.Warn("Using deprecated Wayne command method",
		zap.String("device_id", req.DeviceID),
		zap.String("command_type", req.CommandType),
		zap.String("deprecation_notice", "Please migrate to /api/v2/wayne/{command} endpoints with type-safe DTOs"))

	// 临时向后兼容，但建议迁移到新的API
	return h.executeWayneCommandLegacy(devicePoller, req)
}

// executeWayneCommandLegacy 旧的复杂参数解析方法（仅用于向后兼容）
func (h *CommandEchoHandlerV2) executeWayneCommandLegacy(devicePoller *v2.DevicePoller, req *dto.CommandRequest) error {
	// 根据命令类型调用对应的DevicePoller方法
	switch req.CommandType {
	case "authorize":
		return devicePoller.AuthorizeDevice()

	case "reset":
		return devicePoller.ResetDevice()

	case "preset_volume":
		if volume, ok := req.Parameters["volume"].(float64); ok {
			volumeDecimal := decimal.NewFromFloat(volume)
			return devicePoller.PresetVolume(volumeDecimal)
		}
		return fmt.Errorf("invalid volume parameter - consider using /api/v2/wayne/preset-volume with typed DTO")

	case "preset_amount":
		if amount, ok := req.Parameters["amount"].(float64); ok {
			amountDecimal := decimal.NewFromFloat(amount)
			return devicePoller.PresetAmount(amountDecimal)
		}
		return fmt.Errorf("invalid amount parameter - consider using /api/v2/wayne/preset-amount with typed DTO")

	case "suspend_nozzle":
		if nozzleNum, ok := req.Parameters["nozzle_number"].(float64); ok {
			return devicePoller.SuspendNozzle(byte(nozzleNum))
		}
		return fmt.Errorf("invalid nozzle_number parameter - consider using /api/v2/wayne/suspend-nozzle with typed DTO")

	case "resume_nozzle":
		if nozzleNum, ok := req.Parameters["nozzle_number"].(float64); ok {
			return devicePoller.ResumeNozzle(byte(nozzleNum))
		}
		return fmt.Errorf("invalid nozzle_number parameter - consider using /api/v2/wayne/resume-nozzle with typed DTO")

	case "configure_nozzles":
		if nozzlesInterface, ok := req.Parameters["nozzles"]; ok {
			if nozzlesArray, ok := nozzlesInterface.([]interface{}); ok {
				nozzles := make([]byte, len(nozzlesArray))
				for i, nozzleVal := range nozzlesArray {
					if nozzleNum, ok := nozzleVal.(float64); ok {
						nozzles[i] = byte(nozzleNum)
					} else {
						return fmt.Errorf("invalid nozzle number at index %d - consider using /api/v2/wayne/configure-nozzles with typed DTO", i)
					}
				}
				return devicePoller.ConfigureNozzles(nozzles)
			}
		}
		return fmt.Errorf("invalid nozzles parameter - consider using /api/v2/wayne/configure-nozzles with typed DTO")

	case "update_prices":
		if pricesInterface, ok := req.Parameters["prices"]; ok {
			if pricesArray, ok := pricesInterface.([]interface{}); ok {
				prices := make([]v2.NozzlePriceInfo, len(pricesArray))
				for i, priceItem := range pricesArray {
					if priceMap, ok := priceItem.(map[string]interface{}); ok {
						if nozzleNum, ok := priceMap["nozzle_number"].(float64); ok {
							if price, ok := priceMap["price"].(float64); ok {
								decimals := 3 // 默认小数位数
								if d, ok := priceMap["decimals"].(float64); ok {
									decimals = int(d)
								}
								prices[i] = v2.NozzlePriceInfo{
									NozzleNumber: byte(nozzleNum),
									Price:        decimal.NewFromFloat(price),
									Decimals:     decimals,
								}
							} else {
								return fmt.Errorf("invalid price at index %d - consider using /api/v2/wayne/update-prices with typed DTO", i)
							}
						} else {
							return fmt.Errorf("invalid nozzle_number at index %d - consider using /api/v2/wayne/update-prices with typed DTO", i)
						}
					} else {
						return fmt.Errorf("invalid price item at index %d - consider using /api/v2/wayne/update-prices with typed DTO", i)
					}
				}
				return devicePoller.UpdatePrices(prices)
			}
		}
		return fmt.Errorf("invalid prices parameter - consider using /api/v2/wayne/update-prices with typed DTO")

	case "request_counters":
		counterType := byte(0x19) // 默认请求总金额
		if ct, ok := req.Parameters["counter_type"].(float64); ok {
			counterType = byte(ct)
		}
		return devicePoller.RequestCounters(counterType)

	default:
		return fmt.Errorf("unsupported Wayne command type: %s - consider using new /api/v2/wayne/{command} endpoints", req.CommandType)
	}
}
