package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	// "github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	v2 "fcc-service/internal/services/polling/v2"
	"fcc-service/pkg/api"
	"fcc-service/pkg/models"
	v2models "fcc-service/pkg/models/v2"
)

// DeviceEchoHandlerV2 纯v2架构的设备处理器
// 集成数据库和缓存访问，支持从存储中获取设备信息
type DeviceEchoHandlerV2 struct {
	dispatchTask  *v2.DispatchTask            // v2调度任务
	stateManager  v2models.DeviceStateManager // v2状态管理器
	deviceManager api.DeviceManager           // 设备管理器（数据库+缓存）
	logger        *zap.Logger
}

// NewDeviceEchoHandlerV2 创建v2设备处理器
func NewDeviceEchoHandlerV2(
	dispatchTask *v2.DispatchTask,
	stateManager v2models.DeviceStateManager,
	deviceManager api.DeviceManager,
	logger *zap.Logger,
) *DeviceEchoHandlerV2 {
	return &DeviceEchoHandlerV2{
		dispatchTask:  dispatchTask,
		stateManager:  stateManager,
		deviceManager: deviceManager,
		logger:        logger,
	}
}

// ListDevices 列出所有设备
func (h *DeviceEchoHandlerV2) ListDevices(c echo.Context) error {
	h.logger.Info("Processing v2 listDevices request")

	// 解析查询参数
	filter := h.parseDeviceFilter(c)
	page, pageSize := h.parsePageParams(c)

	// 构建设备过滤器
	deviceFilter := api.DeviceFilter{
		ControllerID: filter.ControllerID,
		StationID:    filter.StationID,
		// Type:         models.DeviceType(filter.DeviceType),
		Status: models.DeviceStatus(filter.Status),
		Offset: (page - 1) * pageSize,
		Limit:  pageSize,
	}

	// 从数据库/缓存获取设备列表
	ctx := context.Background()
	dbDevices, err := h.deviceManager.ListDevices(ctx, deviceFilter)
	if err != nil {
		h.logger.Error("Failed to load devices from database",
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to load devices")
	}

	// 转换为响应格式，并合并v2状态信息
	devices := make([]dto.DeviceResponse, 0, len(dbDevices))
	for _, dbDevice := range dbDevices {
		// 获取v2状态信息（如果存在）
		var deviceState *v2models.DeviceStateData
		var poller *v2.DevicePoller

		if v2State, err := h.stateManager.GetDeviceState(dbDevice.ID); err == nil {
			deviceState = v2State
		}

		if v2Poller, err := h.dispatchTask.GetDevice(dbDevice.ID); err == nil {
			poller = v2Poller
		}

		// 转换为DTO响应
		deviceResp := h.convertDBDeviceToResponse(dbDevice, deviceState, poller)
		devices = append(devices, deviceResp)
	}

	response := &dto.DeviceListResponse{
		Devices:  devices,
		Total:    len(devices),
		Page:     page,
		PageSize: pageSize,
	}

	h.logger.Info("v2 listDevices response prepared",
		zap.Int("devices_count", len(response.Devices)),
		zap.Int("total", response.Total))

	return c.JSON(http.StatusOK, response)
}

// CreateDevice 创建设备
func (h *DeviceEchoHandlerV2) CreateDevice(c echo.Context) error {
	var req dto.DeviceRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind create device request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	ctx := context.Background()

	// 首先验证控制器是否存在
	controller, err := h.deviceManager.GetController(ctx, req.ControllerID)
	if err != nil {
		h.logger.Error("Controller not found",
			zap.String("controller_id", req.ControllerID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Controller not found")
	}

	// 验证设备地址在控制器范围内（Wayne DART协议要求）
	if controller.IsDARTProtocol() && !controller.IsAddressInRange(req.DeviceAddress) {
		return echo.NewHTTPError(http.StatusBadRequest,
			fmt.Sprintf("Device address %d not in controller DART address range", req.DeviceAddress))
	}

	// 创建设备模型
	device := req.ToModel()
	device.Controller = *controller

	// 保存到数据库
	if err := h.deviceManager.RegisterDevice(ctx, device); err != nil {
		h.logger.Error("Failed to register device to database",
			zap.String("device_id", req.ID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to register device")
	}

	// 转换为v2设备信息（从数据库获取的完整信息）
	v2DeviceInfo := v2models.DeviceInfo{
		ID:           device.ID,
		Name:         device.Name,
		Type:         string(device.Type),
		Address:      byte(device.DeviceAddress),
		ControllerID: device.ControllerID,
		StationID:    device.StationID,
		IslandID:     device.IslandID,
		Position:     device.Position,
		CreatedAt:    device.CreatedAt,
		UpdatedAt:    device.UpdatedAt,
	}

	// 配置设备轮询器
	pollerConfig := v2.DevicePollerConfig{
		DeviceInfo:      v2DeviceInfo,
		PollInterval:    1 * time.Second,       // 1秒
		PollTimeout:     25 * time.Millisecond, // 25ms，符合DART协议要求
		MaxRetries:      3,
		WatchdogTimeout: 10 * time.Second, // 10秒
		BufferSize:      100,
	}

	// 注册到v2调度任务
	if err := h.dispatchTask.RegisterDevice(v2DeviceInfo, pollerConfig); err != nil {
		h.logger.Error("Failed to register device to v2 polling",
			zap.String("device_id", req.ID),
			zap.Error(err))
		// 如果v2注册失败，需要考虑是否要回滚数据库操作
		// 在MVP阶段，我们先记录错误但不回滚
	}

	// 启动设备轮询
	if err := h.dispatchTask.StartDevice(req.ID); err != nil {
		h.logger.Error("Failed to start device polling",
			zap.String("device_id", req.ID),
			zap.Error(err))
		// 轮询启动失败也只记录错误
	}

	// 构建响应（使用数据库中的设备信息）
	response := h.convertDBDeviceToResponse(device, nil, nil)

	h.logger.Info("Device created successfully",
		zap.String("device_id", req.ID),
		zap.Uint8("address", byte(device.DeviceAddress)))

	return c.JSON(http.StatusCreated, response)
}

// GetDevice 获取单个设备
func (h *DeviceEchoHandlerV2) GetDevice(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	ctx := context.Background()

	// 首先从数据库获取设备信息
	device, err := h.deviceManager.GetDevice(ctx, deviceID)
	if err != nil {
		h.logger.Error("Device not found in database",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 获取v2状态信息（可选）
	var deviceState *v2models.DeviceStateData
	var poller *v2.DevicePoller

	if v2State, err := h.stateManager.GetDeviceState(deviceID); err == nil {
		deviceState = v2State
	}

	if v2Poller, err := h.dispatchTask.GetDevice(deviceID); err == nil {
		poller = v2Poller
	}

	// 合并数据库信息和v2状态信息
	response := h.convertDBDeviceToResponse(device, deviceState, poller)
	return c.JSON(http.StatusOK, response)
}

// UpdateDevice 更新设备
func (h *DeviceEchoHandlerV2) UpdateDevice(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	var req dto.DeviceRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	ctx := context.Background()

	// 检查设备是否存在于数据库
	existingDevice, err := h.deviceManager.GetDevice(ctx, deviceID)
	if err != nil {
		h.logger.Error("Device not found for update",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 验证控制器是否存在（如果控制器发生变化）
	if req.ControllerID != existingDevice.ControllerID {
		controller, err := h.deviceManager.GetController(ctx, req.ControllerID)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "New controller not found")
		}

		// 验证新的设备地址在新控制器范围内
		if controller.IsDARTProtocol() && !controller.IsAddressInRange(req.DeviceAddress) {
			return echo.NewHTTPError(http.StatusBadRequest,
				fmt.Sprintf("Device address %d not in new controller DART address range", req.DeviceAddress))
		}
	}

	// 更新设备信息
	updatedDevice := req.ToModel()
	updatedDevice.ID = deviceID
	updatedDevice.CreatedAt = existingDevice.CreatedAt // 保持原创建时间
	updatedDevice.UpdatedAt = time.Now()

	// 保存到数据库
	if err := h.deviceManager.RegisterDevice(ctx, updatedDevice); err != nil {
		h.logger.Error("Failed to update device in database",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update device")
	}

	// 先停止原有的v2轮询
	if err := h.dispatchTask.StopDevice(deviceID); err != nil {
		h.logger.Warn("Failed to stop device polling for update",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 注销原有的v2设备
	if err := h.dispatchTask.UnregisterDevice(deviceID); err != nil {
		h.logger.Warn("Failed to unregister device for update",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 重新注册到v2系统
	v2DeviceInfo := v2models.DeviceInfo{
		ID:           updatedDevice.ID,
		Name:         updatedDevice.Name,
		Type:         string(updatedDevice.Type),
		Address:      byte(updatedDevice.DeviceAddress),
		ControllerID: updatedDevice.ControllerID,
		StationID:    updatedDevice.StationID,
		IslandID:     updatedDevice.IslandID,
		Position:     updatedDevice.Position,
		CreatedAt:    updatedDevice.CreatedAt,
		UpdatedAt:    updatedDevice.UpdatedAt,
	}

	pollerConfig := v2.DevicePollerConfig{
		DeviceInfo:      v2DeviceInfo,
		PollInterval:    1 * time.Second,       // 1秒
		PollTimeout:     25 * time.Millisecond, // 25ms，符合DART协议要求
		MaxRetries:      3,
		WatchdogTimeout: 10 * time.Second, // 10秒
		BufferSize:      100,
	}

	if err := h.dispatchTask.RegisterDevice(v2DeviceInfo, pollerConfig); err != nil {
		h.logger.Error("Failed to re-register updated device to v2 polling",
			zap.String("device_id", deviceID),
			zap.Error(err))
		// 不返回错误，因为数据库更新已成功
	}

	// 启动设备轮询
	if err := h.dispatchTask.StartDevice(deviceID); err != nil {
		h.logger.Error("Failed to start updated device polling",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 构建响应（使用数据库中的更新信息）
	response := h.convertDBDeviceToResponse(updatedDevice, nil, nil)
	return c.JSON(http.StatusOK, response)
}

// DeleteDevice 删除设备
func (h *DeviceEchoHandlerV2) DeleteDevice(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	ctx := context.Background()

	// 检查设备是否存在于数据库
	_, err := h.deviceManager.GetDevice(ctx, deviceID)
	if err != nil {
		h.logger.Error("Device not found for deletion",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 停止v2轮询
	if err := h.dispatchTask.StopDevice(deviceID); err != nil {
		h.logger.Warn("Failed to stop device polling for deletion",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 从v2架构注销
	if err := h.dispatchTask.UnregisterDevice(deviceID); err != nil {
		h.logger.Warn("Failed to unregister device from v2 polling",
			zap.String("device_id", deviceID),
			zap.Error(err))
	}

	// 从数据库删除设备
	if err := h.deviceManager.UnregisterDevice(ctx, deviceID); err != nil {
		h.logger.Error("Failed to delete device from database",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete device")
	}

	h.logger.Info("Device deleted successfully",
		zap.String("device_id", deviceID))

	return c.NoContent(http.StatusNoContent)
}

// GetDeviceStatus 获取设备状态
func (h *DeviceEchoHandlerV2) GetDeviceStatus(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	h.logger.Info("Processing getDeviceStatus request",
		zap.String("device_id", deviceID))

	// 获取设备状态
	deviceState, err := h.stateManager.GetDeviceState(deviceID)
	if err != nil {
		h.logger.Error("Failed to get device state",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 修复状态转换：将v2状态转换为标准设备状态
	var status models.DeviceStatus
	switch deviceState.State {
	case v2models.DeviceStateOnline, v2models.DeviceStateReady, v2models.DeviceStatePolling:
		status = models.DeviceStatusOnline
	case v2models.DeviceStateOffline:
		status = models.DeviceStatusOffline
	case v2models.DeviceStateError:
		status = models.DeviceStatusError
	case v2models.DeviceStateMaintenance:
		status = models.DeviceStatusMaintenance
	case v2models.DeviceStateInitializing:
		status = models.DeviceStatusOnline // 初始化状态视为在线
	default:
		status = models.DeviceStatusOffline
	}

	response := dto.DeviceStatusResponse{
		DeviceID: deviceID,
		Status:   string(status),
		LastSeen: &deviceState.LastResponse,
	}

	h.logger.Info("getDeviceStatus response prepared",
		zap.String("device_id", deviceID),
		zap.String("v2_state", string(deviceState.State)),
		zap.String("api_status", string(status)))

	return c.JSON(http.StatusOK, response)
}

// GetPumpStatus 获取泵状态
func (h *DeviceEchoHandlerV2) GetPumpStatus(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	// 获取设备状态
	deviceState, err := h.stateManager.GetDeviceState(deviceID)
	if err != nil {
		h.logger.Error("Failed to get pump status",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 构建泵状态响应
	pumpStatus := map[string]interface{}{
		"device_id":     deviceID,
		"pump_status":   deviceState.PumpStatus,
		"device_state":  deviceState.State,
		"is_online":     deviceState.IsOnline,
		"tx_sequence":   deviceState.TxSequence,
		"last_poll":     deviceState.LastPoll,
		"last_response": deviceState.LastResponse,
		"poll_count":    deviceState.PollCount,
		"error_count":   deviceState.ErrorCount,
		"response_time": deviceState.ResponseTime,
	}

	// 获取喷嘴状态
	nozzles := h.stateManager.GetDeviceNozzles(deviceID)
	nozzleStates := make(map[string]interface{})
	for nozzleID, nozzleSM := range nozzles {
		nozzleData := nozzleSM.GetStateData()
		nozzleStates[nozzleID] = map[string]interface{}{
			"state":          nozzleData.State,
			"is_out":         nozzleData.IsOut,
			"is_selected":    nozzleData.IsSelected,
			"current_volume": nozzleData.CurrentVolume,
			"current_amount": nozzleData.CurrentAmount,
			"current_price":  nozzleData.CurrentPrice,
			"transaction_id": nozzleData.TransactionID,
		}
	}
	pumpStatus["nozzles"] = nozzleStates

	return c.JSON(http.StatusOK, pumpStatus)
}

// GetPumpTotals 获取泵累计数据
func (h *DeviceEchoHandlerV2) GetPumpTotals(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	// 获取喷嘴数据
	nozzles := h.stateManager.GetDeviceNozzles(deviceID)
	if len(nozzles) == 0 {
		return echo.NewHTTPError(http.StatusNotFound, "Device not found or no nozzles")
	}

	totals := make(map[string]interface{})
	totalVolume := 0.0
	totalAmount := 0.0

	for nozzleID, nozzleSM := range nozzles {
		nozzleData := nozzleSM.GetStateData()
		volume, _ := nozzleData.TotalVolume.Float64()
		amount, _ := nozzleData.TotalAmount.Float64()

		totals[nozzleID] = map[string]interface{}{
			"total_volume": volume,
			"total_amount": amount,
		}

		totalVolume += volume
		totalAmount += amount
	}

	response := map[string]interface{}{
		"device_id":     deviceID,
		"total_volume":  totalVolume,
		"total_amount":  totalAmount,
		"nozzle_totals": totals,
	}

	return c.JSON(http.StatusOK, response)
}

// 🚀 辅助方法

// parseDeviceFilter 解析设备过滤参数
func (h *DeviceEchoHandlerV2) parseDeviceFilter(c echo.Context) DeviceFilterV2 {
	return DeviceFilterV2{
		ControllerID: c.QueryParam("controller_id"),
		StationID:    c.QueryParam("station_id"),
		Status:       c.QueryParam("status"),
		DeviceType:   c.QueryParam("type"),
	}
}

// parsePageParams 解析分页参数
func (h *DeviceEchoHandlerV2) parsePageParams(c echo.Context) (int, int) {
	page := 1
	pageSize := 50

	if p := c.QueryParam("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	if ps := c.QueryParam("page_size"); ps != "" {
		if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 && parsed <= 100 {
			pageSize = parsed
		}
	}

	return page, pageSize
}

// convertToDeviceResponse 转换为设备响应DTO
func (h *DeviceEchoHandlerV2) convertToDeviceResponse(deviceState *v2models.DeviceStateData, poller *v2.DevicePoller) dto.DeviceResponse {
	// 转换状态
	status := models.DeviceStatusOffline
	health := models.DeviceHealthUnknown

	switch deviceState.State {
	case v2models.DeviceStateOnline, v2models.DeviceStateReady, v2models.DeviceStatePolling:
		status = models.DeviceStatusOnline
		health = models.DeviceHealthGood
	case v2models.DeviceStateOffline:
		status = models.DeviceStatusOffline
		health = models.DeviceHealthError
	case v2models.DeviceStateError:
		status = models.DeviceStatusError
		health = models.DeviceHealthCritical
	case v2models.DeviceStateMaintenance:
		status = models.DeviceStatusMaintenance
		health = models.DeviceHealthWarning
	}

	// 构建设备配置
	config := models.DeviceConfig{
		Enabled:     true,
		Description: "v2 Device Poller Configuration",
		ExtraConfig: map[string]interface{}{
			"poll_interval":    1000,
			"timeout":          25,
			"retry_attempts":   3,
			"watchdog_timeout": 10000,
		},
	}

	// 构建设备能力
	capabilities := models.DeviceCapabilities{
		SupportedCommands: []string{"authorize", "reset", "preset_volume", "preset_amount"},
		Features: map[string]bool{
			"wayne_dart_protocol": true,
			"cd_commands":         true,
			"nozzle_management":   true,
		},
		Performance: map[string]interface{}{
			"max_concurrent_ops":  1,
			"supports_streaming":  false,
			"response_timeout_ms": 25,
		},
	}

	return dto.DeviceResponse{
		ID:            deviceState.DeviceInfo.ID,
		Name:          deviceState.DeviceInfo.Name,
		Type:          deviceState.DeviceInfo.Type,
		ControllerID:  deviceState.DeviceInfo.ControllerID,
		DeviceAddress: int(deviceState.DeviceInfo.Address),
		StationID:     deviceState.DeviceInfo.StationID,
		IslandID:      deviceState.DeviceInfo.IslandID,
		Position:      deviceState.DeviceInfo.Position,
		Config:        config,
		Status:        status,
		Health:        health,
		Capabilities:  capabilities,
		LastSeen:      &deviceState.LastResponse,
		CreatedAt:     deviceState.DeviceInfo.CreatedAt,
		UpdatedAt:     deviceState.DeviceInfo.UpdatedAt,
	}
}

// convertToV2DeviceInfo 转换为v2设备信息
func (h *DeviceEchoHandlerV2) convertToV2DeviceInfo(req *dto.DeviceRequest) v2models.DeviceInfo {
	return v2models.DeviceInfo{
		ID:           req.ID,
		Name:         req.Name,
		Type:         string(req.Type),
		Address:      byte(req.DeviceAddress),
		ControllerID: req.ControllerID,
		StationID:    req.StationID,
		IslandID:     req.IslandID,
		Position:     req.Position,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

// convertV2DeviceInfoToDeviceResponse 从v2设备信息转换为响应DTO
func (h *DeviceEchoHandlerV2) convertV2DeviceInfoToDeviceResponse(info v2models.DeviceInfo) dto.DeviceResponse {
	config := models.DeviceConfig{
		Enabled:     true,
		Description: "v2 Device Configuration",
		ExtraConfig: map[string]interface{}{
			"poll_interval":    1000,
			"timeout":          25,
			"retry_attempts":   3,
			"watchdog_timeout": 10000,
		},
	}

	capabilities := models.DeviceCapabilities{
		SupportedCommands: []string{"authorize", "reset", "preset_volume", "preset_amount"},
		Features: map[string]bool{
			"wayne_dart_protocol": true,
			"cd_commands":         true,
		},
		Performance: map[string]interface{}{
			"max_concurrent_ops": 1,
			"supports_streaming": false,
		},
	}

	return dto.DeviceResponse{
		ID:            info.ID,
		Name:          info.Name,
		Type:          info.Type,
		ControllerID:  info.ControllerID,
		DeviceAddress: int(info.Address),
		StationID:     info.StationID,
		IslandID:      info.IslandID,
		Position:      info.Position,
		Config:        config,
		Status:        models.DeviceStatusOnline, // 新创建默认在线
		Health:        models.DeviceHealthGood,   // 新创建默认健康
		Capabilities:  capabilities,
		CreatedAt:     info.CreatedAt,
		UpdatedAt:     info.UpdatedAt,
	}
}

// convertDBDeviceToResponse 从数据库设备转换为响应DTO
// 优先使用数据库信息，并合并v2状态信息
func (h *DeviceEchoHandlerV2) convertDBDeviceToResponse(
	dbDevice *models.Device,
	v2State *v2models.DeviceStateData,
	poller *v2.DevicePoller,
) dto.DeviceResponse {
	// 基础设备信息来自数据库
	response := dto.DeviceResponse{
		ID:            dbDevice.ID,
		Name:          dbDevice.Name,
		Type:          string(dbDevice.Type),
		ControllerID:  dbDevice.ControllerID,
		DeviceAddress: dbDevice.DeviceAddress,
		StationID:     dbDevice.StationID,
		IslandID:      dbDevice.IslandID,
		Position:      dbDevice.Position,
		Config:        dbDevice.Config,
		Capabilities:  dbDevice.Capabilities,
		CreatedAt:     dbDevice.CreatedAt,
		UpdatedAt:     dbDevice.UpdatedAt,
	}

	// 状态信息优先使用v2状态，否则使用数据库状态
	if v2State != nil {
		// 转换v2状态为标准状态
		switch v2State.State {
		case v2models.DeviceStateOnline, v2models.DeviceStateReady, v2models.DeviceStatePolling:
			response.Status = models.DeviceStatusOnline
			response.Health = models.DeviceHealthGood
		case v2models.DeviceStateOffline:
			response.Status = models.DeviceStatusOffline
			response.Health = models.DeviceHealthError
		case v2models.DeviceStateError:
			response.Status = models.DeviceStatusError
			response.Health = models.DeviceHealthCritical
		case v2models.DeviceStateMaintenance:
			response.Status = models.DeviceStatusMaintenance
			response.Health = models.DeviceHealthWarning
		default:
			response.Status = dbDevice.Status
			response.Health = dbDevice.Health
		}

		// 使用v2的最新响应时间
		response.LastSeen = &v2State.LastResponse
	} else {
		// 使用数据库中的状态信息
		response.Status = dbDevice.Status
		response.Health = dbDevice.Health
		response.LastSeen = dbDevice.LastSeen
	}

	return response
}

// DeviceFilterV2 设备过滤器
type DeviceFilterV2 struct {
	ControllerID string
	StationID    string
	Status       string
	DeviceType   string
}

// matchesFilter 检查设备是否匹配过滤条件
func (h *DeviceEchoHandlerV2) matchesFilter(device dto.DeviceResponse, filter DeviceFilterV2) bool {
	if filter.ControllerID != "" && device.ControllerID != filter.ControllerID {
		return false
	}
	if filter.StationID != "" && device.StationID != filter.StationID {
		return false
	}
	if filter.Status != "" && string(device.Status) != filter.Status {
		return false
	}
	if filter.DeviceType != "" && device.Type != filter.DeviceType {
		return false
	}
	return true
}

// isWayneCommand 检查是否为Wayne协议命令
func (h *DeviceEchoHandlerV2) isWayneCommand(commandType string) bool {
	wayneCommands := map[string]bool{
		"authorize":           true, // CD1: AUTHORIZE
		"reset":               true, // CD1: RESET
		"preset_volume":       true, // CD3: 预设体积
		"preset_amount":       true, // CD4: 预设金额
		"update_prices":       true, // CD5: 价格更新
		"suspend_nozzle":      true, // CD14: 暂停喷嘴
		"resume_nozzle":       true, // CD15: 恢复喷嘴
		"request_counters":    true, // CD101: 查询计数器
		"return_status":       true, // CD1: RETURN STATUS
		"return_filling_info": true, // CD1: RETURN FILLING INFORMATION
	}

	return wayneCommands[commandType]
}

// sendGenericCommand 发送通用命令
func (h *DeviceEchoHandlerV2) sendGenericCommand(c echo.Context, devicePoller *v2.DevicePoller, req *dto.CommandRequest) error {
	startTime := time.Now()

	// 转换为v2轮询命令
	timeout := 30 * time.Second
	if req.Timeout > 0 {
		timeout = time.Duration(req.Timeout) * time.Second
	}

	priority := v2.PriorityMedium
	switch req.Priority {
	case "high":
		priority = v2.PriorityHigh
	case "low":
		priority = v2.PriorityLow
	}

	v2Cmd := v2.PollCommand{
		Type:         "data", // 数据命令
		Data:         req.Parameters,
		Timeout:      timeout,
		Timestamp:    startTime,
		BusinessType: req.CommandType,
		Priority:     int(priority),
		Retryable:    true,
		MaxRetries:   3,
	}

	if err := devicePoller.SendCommand(v2Cmd); err != nil {
		h.logger.Error("Failed to send generic command",
			zap.String("device_id", req.DeviceID),
			zap.String("command_type", req.CommandType),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to send command")
	}

	response := dto.CommandResponse{
		DeviceID:    req.DeviceID,
		CommandType: req.CommandType,
		Success:     true,
		Data: map[string]interface{}{
			"message":        "Command sent successfully",
			"command_type":   req.CommandType,
			"execution_time": time.Since(startTime).Milliseconds(),
		},
		SubmittedAt:   startTime,
		ExecutionTime: time.Since(startTime).Milliseconds(),
	}

	return c.JSON(http.StatusOK, response)
}
