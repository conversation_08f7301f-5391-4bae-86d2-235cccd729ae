package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	"fcc-service/internal/services/nozzle"
	v2 "fcc-service/internal/services/polling/v2"
	"fcc-service/pkg/api"
	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
)

// 注释：移除了DispatchTaskAdapter和DevicePollerAdapter，因为不再需要state_validator

// WayneCommandHandler 专门的Wayne协议命令处理器
// 使用类型安全的DTO结构，避免复杂的参数解析
type WayneCommandHandler struct {
	dispatchTask  *v2.DispatchTask
	deviceManager api.DeviceManager
	logger        *zap.Logger
	nozzleService nozzle.ServiceV2
}

// NewWayneCommandHandler 创建Wayne命令处理器
func NewWayneCommandHandler(
	dispatchTask *v2.DispatchTask,
	deviceManager api.DeviceManager,
	logger *zap.Logger,
	nozzleService nozzle.ServiceV2,
) *WayneCommandHandler {
	return &WayneCommandHandler{
		dispatchTask:  dispatchTask,
		deviceManager: deviceManager,
		logger:        logger,
		nozzleService: nozzleService,
	}
}

// ExecuteAuthorize 执行授权命令 CD1(0x06)
func (h *WayneCommandHandler) ExecuteAuthorize(c echo.Context) error {
	h.logger.Info("ExecuteAuthorize called", zap.String("remote_addr", c.Request().RemoteAddr))

	var req dto.AuthorizeRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind authorize request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid authorize request: "+err.Error())
	}

	h.logger.Info("Authorize request bound successfully",
		zap.String("device_id", req.DeviceID),
		zap.String("employee_id", req.EmployeeID),
		zap.Bool("async", req.Async))

	if req.NozzleID != nil {
		h.logger.Info("Nozzle ID provided", zap.String("nozzle_id", *req.NozzleID))
	} else {
		h.logger.Info("No nozzle ID provided, will use device-level authorization")
	}

	if err := req.Validate(); err != nil {
		h.logger.Error("Request validation failed", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	h.logger.Info("Request validated successfully")

	// 检查 Pump 和 Nozzle 状态并锁 Nozzle
	if req.NozzleID != nil && *req.NozzleID != "" {
		err := h.nozzleService.CheckFillingStatusAndLockNozzle(c.Request().Context(), req.DeviceID, *req.NozzleID)
		if err != nil {
			return echo.NewHTTPError(http.StatusConflict, "Authorize rejected: "+err.Error())
		}

		// 🆕 检查泵码完整性 - 确保有体积和金额两个泵码才允许授权
		err = h.nozzleService.CheckPumpReadingIntegrity(c.Request().Context(), req.DeviceID, *req.NozzleID)
		if err != nil {
			h.logger.Warn("Authorization rejected due to incomplete pump readings",
				zap.String("device_id", req.DeviceID),
				zap.String("nozzle_id", *req.NozzleID),
				zap.Error(err))

			// 🚀 触发CD101请求获取缺失的泵码
			go func() {
				h.logger.Info("Triggering CD101 request for missing pump readings",
					zap.String("device_id", req.DeviceID),
					zap.String("nozzle_id", *req.NozzleID))

				// 通过设备轮询器发送CD101请求
				if devicePoller, err := h.dispatchTask.GetDevice(req.DeviceID); err == nil {
					// 发送两个计数器请求：体积和金额
					nozzle, err := h.nozzleService.GetNozzleByID(c.Request().Context(), *req.NozzleID)
					if err == nil {
						volumeCounterType := byte(nozzle.Number)      // 0x01-0x08
						amountCounterType := byte(nozzle.Number + 16) // 0x11-0x18

						// 发送体积计数器请求
						if err := devicePoller.RequestCounters(v2.CounterTypeEnum(volumeCounterType)); err != nil {
							h.logger.Error("Failed to request volume counter",
								zap.String("device_id", req.DeviceID),
								zap.Uint8("counter_type", volumeCounterType),
								zap.Error(err))
						}

						// 发送金额计数器请求
						if err := devicePoller.RequestCounters(v2.CounterTypeEnum(amountCounterType)); err != nil {
							h.logger.Error("Failed to request amount counter",
								zap.String("device_id", req.DeviceID),
								zap.Uint8("counter_type", amountCounterType),
								zap.Error(err))
						}
					}
				} else {
					h.logger.Error("Failed to get device poller for CD101 request",
						zap.String("device_id", req.DeviceID),
						zap.Error(err))
				}
			}()

			return echo.NewHTTPError(http.StatusPreconditionFailed,
				"Authorize rejected: incomplete pump readings. CD101 request sent to retrieve missing data. Please retry after a few seconds.")
		}
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		// 如果NozzleID为空，保持原有行为
		if req.NozzleID == nil || *req.NozzleID == "" {
			h.logger.Info("Authorizing entire device", zap.String("device_id", req.DeviceID))
			return devicePoller.AuthorizeDevice(req.EmployeeID)
		}

		// 通过NozzleID查询NozzleNumber
		h.logger.Info("Looking up nozzle by ID", zap.String("nozzle_id", *req.NozzleID))
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		nozzle, err := h.nozzleService.GetNozzleByID(ctx, *req.NozzleID)
		if err != nil {
			h.logger.Error("Failed to get nozzle by ID",
				zap.String("nozzle_id", *req.NozzleID),
				zap.Error(err))
			return fmt.Errorf("查询喷嘴失败: %w", err)
		}

		h.logger.Info("Nozzle found successfully",
			zap.String("nozzle_id", *req.NozzleID),
			zap.String("device_id", nozzle.DeviceID),
			zap.Uint8("nozzle_number", nozzle.Number),
			zap.Bool("is_enabled", nozzle.IsEnabled))

		// 验证喷嘴是否属于当前设备
		if nozzle.DeviceID != req.DeviceID {
			h.logger.Error("Nozzle device mismatch",
				zap.String("nozzle_id", *req.NozzleID),
				zap.String("nozzle_device_id", nozzle.DeviceID),
				zap.String("request_device_id", req.DeviceID))
			return fmt.Errorf("喷嘴 %s 不属于设备 %s", *req.NozzleID, req.DeviceID)
		}

		// 验证喷嘴是否启用
		// if !nozzle.IsEnabled {
		// 	h.logger.Error("Nozzle not enabled", zap.String("nozzle_id", *req.NozzleID))
		// 	return fmt.Errorf("喷嘴 %s 未启用", *req.NozzleID)
		// }

		h.logger.Info("使用指定喷嘴进行授权",
			zap.String("device_id", req.DeviceID),
			zap.String("nozzle_id", *req.NozzleID),
			zap.Uint8("nozzle_number", nozzle.Number),
			zap.String("employee_id", req.EmployeeID))

		return devicePoller.AuthorizeDevice(req.EmployeeID, nozzle.Number)
	})
}

// ExecuteReset 执行复位命令 CD1(0x05)
func (h *WayneCommandHandler) ExecuteReset(c echo.Context) error {
	var req dto.ResetRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid reset request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.ResetDevice()
	})
}

// ExecuteStop 执行停止命令 CD1(0x08)
func (h *WayneCommandHandler) ExecuteStop(c echo.Context) error {
	var req dto.StopRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid stop request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.StopDevice()
	})
}

// ExecuteReturnStatus 执行返回状态命令 CD1(0x00)
func (h *WayneCommandHandler) ExecuteReturnStatus(c echo.Context) error {
	var req dto.ReturnStatusRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid return status request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.ReturnStatus()
	})
}

// ExecuteReturnFillingInfo 执行返回填充信息命令 CD1(0x04)
func (h *WayneCommandHandler) ExecuteReturnFillingInfo(c echo.Context) error {
	var req dto.ReturnFillingInfoRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid return filling info request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.ReturnFillingInformation()
	})
}

// ExecuteResumeFuellingPoint 执行恢复加油点命令 CD1(0x0E)
func (h *WayneCommandHandler) ExecuteResumeFuellingPoint(c echo.Context) error {
	var req dto.ResumeFuellingPointRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid resume fuelling point request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.ResumeFuellingPoint()
	})
}

// ExecuteConfigureNozzles 执行配置喷嘴命令 CD2
func (h *WayneCommandHandler) ExecuteConfigureNozzles(c echo.Context) error {
	var req dto.ConfigureNozzlesRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid configure nozzles request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		// 转换为byte数组
		nozzles := make([]byte, len(req.NozzleNumbers))
		for i, nozzleNum := range req.NozzleNumbers {
			nozzles[i] = byte(nozzleNum)
		}
		return devicePoller.ConfigureNozzles(nozzles)
	})
}

// ExecutePresetVolume 执行预设体积命令 CD3
func (h *WayneCommandHandler) ExecutePresetVolume(c echo.Context) error {
	var req dto.PresetVolumeRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid preset volume request: "+err.Error())
	}

	if req.NozzleID == nil || *req.NozzleID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Nozzle ID is required")
	}

	// 检查 Pump 和 Nozzle 状态并锁 Nozzle
	err := h.nozzleService.CheckFillingStatusAndLockNozzle(c.Request().Context(), req.DeviceID, *req.NozzleID)
	if err != nil {
		return echo.NewHTTPError(http.StatusConflict, "PresetVolume rejected: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.PresetVolume(req.Volume, req.EmployeeID)
	})
}

// ExecutePresetAmount 执行预设金额命令 CD4
func (h *WayneCommandHandler) ExecutePresetAmount(c echo.Context) error {
	var req dto.PresetAmountRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid preset amount request: "+err.Error())
	}
	if req.NozzleID == nil || *req.NozzleID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Nozzle ID is required")
	}

	// 检查 Pump 和 Nozzle 状态并锁 Nozzle
	err := h.nozzleService.CheckFillingStatusAndLockNozzle(c.Request().Context(), req.DeviceID, *req.NozzleID)
	if err != nil {
		return echo.NewHTTPError(http.StatusConflict, "PresetAmount rejected: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.PresetAmount(req.Amount, req.EmployeeID)
	})
}

// ExecuteUpdatePrices 执行价格更新命令 CD5
// 改进：Wayne DART协议要求按序传递所有喷嘴价格
// 当只更新单个喷嘴价格时，自动填充其他喷嘴的当前价格
// 新增：同步更新数据库，确保数据一致性
func (h *WayneCommandHandler) ExecuteUpdatePrices(c echo.Context) error {
	h.logger.Info("ExecuteUpdatePrices called")

	var req dto.UpdatePricesRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid update prices request: "+err.Error())
	}

	h.logger.Info("Request bound successfully",
		zap.String("device_id", req.DeviceID),
		zap.Int("price_count", len(req.Prices)))

	if err := req.Validate(); err != nil {
		h.logger.Error("Request validation failed", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	h.logger.Info("Request validated successfully")

	// 新增：预先验证所有价格数据，避免部分成功部分失败的情况
	ctx := c.Request().Context()
	if err := h.validatePriceUpdates(ctx, req.DeviceID, req.Prices); err != nil {
		h.logger.Error("Price validation failed", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Price validation failed: %v", err))
	}

	// 新增：使用原子性批量操作同步更新数据库目标价格
	var updatedNozzles []struct {
		Number      byte
		OldPrice    decimal.Decimal
		TargetPrice decimal.Decimal
	}

	// 准备批量价格更新数据
	var priceUpdates []nozzle.NozzleTargetPriceUpdate
	for _, priceInfo := range req.Prices {
		// 获取当前价格用于回滚
		currentNozzle, err := h.nozzleService.GetNozzle(ctx, req.DeviceID, byte(priceInfo.NozzleNumber))
		if err != nil {
			h.logger.Error("Failed to get nozzle for price update",
				zap.String("device_id", req.DeviceID),
				zap.Int("nozzle_number", priceInfo.NozzleNumber),
				zap.Error(err))
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get nozzle information")
		}

		updatedNozzles = append(updatedNozzles, struct {
			Number      byte
			OldPrice    decimal.Decimal
			TargetPrice decimal.Decimal
		}{
			Number:      byte(priceInfo.NozzleNumber),
			OldPrice:    currentNozzle.CurrentPrice,
			TargetPrice: priceInfo.Price,
		})

		priceUpdates = append(priceUpdates, nozzle.NozzleTargetPriceUpdate{
			NozzleNumber: byte(priceInfo.NozzleNumber),
			TargetPrice:  priceInfo.Price,
		})
	}

	// 原子性批量设置目标价格
	if err := h.nozzleService.BatchSetNozzleTargetPrices(ctx, req.DeviceID, priceUpdates); err != nil {
		h.logger.Error("Failed to batch set target prices in database",
			zap.String("device_id", req.DeviceID),
			zap.Int("price_count", len(priceUpdates)),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to set target prices in database")
	}

	h.logger.Info("Target prices set in database successfully",
		zap.String("device_id", req.DeviceID),
		zap.Int("updated_count", len(updatedNozzles)))

	// 执行Wayne命令发送
	commandErr := h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		var prices []nozzle.NozzlePriceInfo

		// 关键修复：检查是否为单个喷嘴价格更新（常见场景）
		if len(req.Prices) == 1 {
			// 添加空指针检查
			if h.nozzleService == nil {
				h.logger.Error("Nozzle service is nil",
					zap.String("device_id", req.DeviceID))
				return fmt.Errorf("nozzle service not available")
			}

			// 单个喷嘴价格更新 - 需要构建完整价格数组
			singlePrice := req.Prices[0]
			h.logger.Info("Single nozzle price update detected, building complete price array",
				zap.String("device_id", req.DeviceID),
				zap.Int("target_nozzle", singlePrice.NozzleNumber),
				zap.String("target_price", singlePrice.Price.String()))

			// 使用nozzle service构建完整价格数组
			completePrices, err := h.nozzleService.BuildCompleteNozzlePrices(
				c.Request().Context(),
				req.DeviceID,
				byte(singlePrice.NozzleNumber),
				singlePrice.Price,
			)
			if err != nil {
				h.logger.Error("Failed to build complete nozzle prices",
					zap.String("device_id", req.DeviceID),
					zap.Error(err))
				return fmt.Errorf("failed to build complete nozzle prices: %w", err)
			}

			// 添加空指针检查
			if completePrices == nil {
				h.logger.Error("BuildCompleteNozzlePrices returned nil",
					zap.String("device_id", req.DeviceID))
				return fmt.Errorf("failed to build complete nozzle prices: got nil result")
			}

			// 转换为DevicePoller所需的类型
			prices = make([]nozzle.NozzlePriceInfo, len(completePrices))
			for i, price := range completePrices {
				prices[i] = nozzle.NozzlePriceInfo{
					NozzleNumber: price.NozzleNumber,
					Price:        price.Price,
					Decimals:     price.Decimals,
				}
			}

			h.logger.Info("Built complete price array for Wayne DART CD5",
				zap.String("device_id", req.DeviceID),
				zap.Int("total_nozzles", len(prices)),
				zap.Int("target_nozzle", singlePrice.NozzleNumber))

		} else {
			// 多个喷嘴价格更新 - 直接使用提供的价格
			h.logger.Info("Multiple nozzle price update, using provided prices",
				zap.String("device_id", req.DeviceID),
				zap.Int("price_count", len(req.Prices)))

			prices = make([]nozzle.NozzlePriceInfo, len(req.Prices))
			for i, price := range req.Prices {
				prices[i] = nozzle.NozzlePriceInfo{
					NozzleNumber: byte(price.NozzleNumber),
					Price:        price.Price,
					Decimals:     price.Decimals,
				}
			}
		}

		return devicePoller.UpdatePrices(prices)
	})

	// 新增：处理设备命令失败的情况
	if commandErr != nil {
		h.logger.Error("Wayne command failed, rolling back database changes",
			zap.String("device_id", req.DeviceID),
			zap.Error(commandErr))

		// 回滚数据库中的目标价格设置
		h.rollbackTargetPrices(ctx, req.DeviceID, updatedNozzles)

		// 返回原始错误（executeWayneCommand已经处理了HTTP响应）
		return commandErr
	}

	h.logger.Info("Wayne price update command executed successfully with database sync",
		zap.String("device_id", req.DeviceID),
		zap.Int("updated_nozzles", len(updatedNozzles)),
		zap.String("sync_mode", "immediate_database_update"))

	return commandErr
}

// validatePriceUpdates 验证价格更新请求
func (h *WayneCommandHandler) validatePriceUpdates(ctx context.Context, deviceID string, prices []dto.NozzlePriceInfo) error {
	// 验证设备存在
	_, err := h.deviceManager.GetDevice(ctx, deviceID)
	if err != nil {
		return fmt.Errorf("device not found: %w", err)
	}

	// 验证所有喷嘴存在且启用
	for _, priceInfo := range prices {
		nozzle, err := h.nozzleService.GetNozzle(ctx, deviceID, byte(priceInfo.NozzleNumber))
		if err != nil {
			return fmt.Errorf("nozzle %d not found: %w", priceInfo.NozzleNumber, err)
		}

		if !nozzle.IsEnabled {
			return fmt.Errorf("nozzle %d is disabled", priceInfo.NozzleNumber)
		}

		// 验证价格范围
		priceFloat, _ := priceInfo.Price.Float64()
		if priceFloat <= 0 || priceFloat > 999999.999 {
			return fmt.Errorf("invalid price %.3f for nozzle %d: must be between 0.001 and 999999.999",
				priceFloat, priceInfo.NozzleNumber)
		}

		// 检查是否有正在进行的交易
		if nozzle.IsTransactionInProgress() {
			h.logger.Warn("Updating price during active transaction",
				zap.String("device_id", deviceID),
				zap.Int("nozzle_number", priceInfo.NozzleNumber),
				zap.String("nozzle_status", string(nozzle.Status)))
			// 警告但不阻止，允许紧急调价
		}
	}

	return nil
}

// rollbackTargetPrices 回滚数据库中的目标价格设置
func (h *WayneCommandHandler) rollbackTargetPrices(ctx context.Context, deviceID string, updatedNozzles []struct {
	Number      byte
	OldPrice    decimal.Decimal
	TargetPrice decimal.Decimal
}) {
	for _, nozzleInfo := range updatedNozzles {
		// 获取当前喷嘴状态
		nozzle, err := h.nozzleService.GetNozzle(ctx, deviceID, nozzleInfo.Number)
		if err != nil {
			h.logger.Error("Failed to get nozzle for rollback",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleInfo.Number),
				zap.Error(err))
			continue
		}

		// 恢复原始价格状态
		nozzle.CurrentPrice = nozzleInfo.OldPrice
		nozzle.TargetPrice = nil
		nozzle.PriceStatus = models.PriceStatusSynced
		nozzle.PriceUpdatedAt = nil

		if err := h.nozzleService.UpdateNozzle(ctx, nozzle); err != nil {
			h.logger.Error("Failed to rollback nozzle price",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleInfo.Number),
				zap.String("old_price", nozzleInfo.OldPrice.String()),
				zap.Error(err))
		} else {
			h.logger.Info("Rolled back nozzle price",
				zap.String("device_id", deviceID),
				zap.Uint8("nozzle_number", nozzleInfo.Number),
				zap.String("restored_price", nozzleInfo.OldPrice.String()))
		}
	}
}

// ExecuteSuspendNozzle 执行暂停喷嘴命令 CD14
func (h *WayneCommandHandler) ExecuteSuspendNozzle(c echo.Context) error {
	var req dto.SuspendNozzleRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid suspend nozzle request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.SuspendNozzle(byte(req.NozzleNumber))
	})
}

// ExecuteResumeNozzle 执行恢复喷嘴命令 CD15
func (h *WayneCommandHandler) ExecuteResumeNozzle(c echo.Context) error {
	var req dto.ResumeNozzleRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid resume nozzle request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		return devicePoller.ResumeNozzle(byte(req.NozzleNumber))
	})
}

// ExecuteRequestCounters 执行请求计数器命令 CD101
func (h *WayneCommandHandler) ExecuteRequestCounters(c echo.Context) error {
	var req dto.RequestCountersRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request counters request: "+err.Error())
	}

	if err := req.Validate(); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
		counterType := req.CounterType.GetCounterTypeByte()
		return devicePoller.RequestCounters(v2.CounterTypeEnum(counterType))
	})
}

// SetNozzleTargetPrice 设置喷嘴目标价格（异步调价）
// POST /api/v2/devices/{id}/nozzles/{number}/target-price
func (h *WayneCommandHandler) SetNozzleTargetPrice(c echo.Context) error {
	deviceID := c.Param("id")
	nozzleNumberStr := c.Param("number")

	if deviceID == "" || nozzleNumberStr == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID and nozzle number are required")
	}

	// 解析喷嘴编号
	nozzleNumber, err := strconv.ParseUint(nozzleNumberStr, 10, 8)
	if err != nil || nozzleNumber < 1 || nozzleNumber > 15 {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid nozzle number: must be between 1 and 15")
	}

	// 解析请求体
	var req struct {
		TargetPrice float64 `json:"target_price" validate:"required,min=1,max=999999"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Validation failed: %v", err))
	}

	targetPrice := decimal.NewFromFloat(req.TargetPrice)

	h.logger.Info("Setting nozzle target price",
		zap.String("device_id", deviceID),
		zap.Uint8("nozzle_number", byte(nozzleNumber)),
		zap.String("target_price", targetPrice.String()))

	ctx := context.Background()

	// 设置目标价格（写入数据库，触发异步调价）
	err = h.nozzleService.SetNozzleTargetPrice(ctx, deviceID, byte(nozzleNumber), targetPrice)
	if err != nil {
		h.logger.Error("Failed to set nozzle target price",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", byte(nozzleNumber)),
			zap.String("target_price", targetPrice.String()),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to set target price")
	}

	response := map[string]interface{}{
		"success":       true,
		"device_id":     deviceID,
		"nozzle_number": nozzleNumber,
		"target_price":  req.TargetPrice,
		"message":       "Target price set successfully. Price sync will be handled automatically by the device poller.",
		"timestamp":     time.Now(),
	}

	return c.JSON(http.StatusOK, response)
}

// GetPriceSyncStatus 获取设备价格同步状态
// GET /api/v2/devices/{id}/price-sync-status
func (h *WayneCommandHandler) GetPriceSyncStatus(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	ctx := context.Background()

	status, err := h.nozzleService.GetPriceSyncStatus(ctx, deviceID)
	if err != nil {
		h.logger.Error("Failed to get price sync status",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get price sync status")
	}

	return c.JSON(http.StatusOK, status)
}

// executeWayneCommand 统一的Wayne命令执行方法
// 使用函数式编程模式，避免复杂的switch语句
func (h *WayneCommandHandler) executeWayneCommand(
	c echo.Context,
	baseReq *dto.WayneCommandRequest,
	commandFunc func(*v2.DevicePoller) error,
) error {
	startTime := time.Now()

	// 生成命令ID
	if baseReq.CommandID == "" {
		baseReq.CommandID = fmt.Sprintf("wayne_%s_%d", baseReq.Command, time.Now().UnixNano())
	}

	h.logger.Info("Executing Wayne DART command",
		zap.String("command_id", baseReq.CommandID),
		zap.String("device_id", baseReq.DeviceID),
		zap.String("command", string(baseReq.Command)),
		zap.String("employee_id", baseReq.EmployeeID),
		zap.Bool("async", baseReq.Async))

	// 验证设备存在性
	ctx := context.Background()
	_, err := h.deviceManager.GetDevice(ctx, baseReq.DeviceID)
	if err != nil {
		h.logger.Error("Device not found for Wayne command",
			zap.String("device_id", baseReq.DeviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 注释：移除了状态验证逻辑，直接执行命令

	// 获取设备轮询器
	devicePoller, err := h.dispatchTask.GetDevice(baseReq.DeviceID)
	if err != nil {
		h.logger.Error("Failed to get device poller",
			zap.String("device_id", baseReq.DeviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Device poller not available")
	}

	// 添加空指针检查
	if devicePoller == nil {
		h.logger.Error("Device poller is nil",
			zap.String("device_id", baseReq.DeviceID))
		return echo.NewHTTPError(http.StatusInternalServerError, "Device poller is not available")
	}

	// 执行命令
	var commandErr error
	if baseReq.Async {
		// 异步执行
		go func() {
			commandErr = commandFunc(devicePoller)
			if commandErr != nil {
				h.logger.Error("Async Wayne command execution failed",
					zap.String("command_id", baseReq.CommandID),
					zap.String("device_id", baseReq.DeviceID),
					zap.String("command", string(baseReq.Command)),
					zap.Error(commandErr))
			} else {
				h.logger.Info("Async Wayne command executed successfully",
					zap.String("command_id", baseReq.CommandID),
					zap.String("device_id", baseReq.DeviceID),
					zap.String("command", string(baseReq.Command)),
					zap.Int64("execution_time_ms", time.Since(startTime).Milliseconds()))
			}
		}()

		// 立即返回异步响应
		response := dto.WayneCommandResponse{
			CommandID: baseReq.CommandID,
			DeviceID:  baseReq.DeviceID,
			Command:   baseReq.Command,
			Success:   true,
			Data: map[string]interface{}{
				"status":  "submitted",
				"message": "Command submitted for async execution",
			},
			ExecutionTime: time.Since(startTime).Milliseconds(),
			SubmittedAt:   startTime,
			ProtocolInfo: &dto.ProtocolInfo{
				Protocol:        "Wayne DART v1.3",
				TransactionType: h.getTransactionType(baseReq.Command),
				TransactionCode: h.getTransactionCode(baseReq.Command),
			},
		}
		return c.JSON(http.StatusAccepted, response)
	}

	// 同步执行
	commandErr = commandFunc(devicePoller)
	executionTime := time.Since(startTime).Milliseconds()

	if commandErr != nil {
		h.logger.Error("Wayne command execution failed",
			zap.String("command_id", baseReq.CommandID),
			zap.String("device_id", baseReq.DeviceID),
			zap.String("command", string(baseReq.Command)),
			zap.Error(commandErr),
			zap.Int64("execution_time_ms", executionTime))

		response := dto.WayneCommandResponse{
			CommandID:     baseReq.CommandID,
			DeviceID:      baseReq.DeviceID,
			Command:       baseReq.Command,
			Success:       false,
			Error:         commandErr.Error(),
			ExecutionTime: executionTime,
			SubmittedAt:   startTime,
			CompletedAt:   &[]time.Time{time.Now()}[0],
			ProtocolInfo: &dto.ProtocolInfo{
				Protocol:        "Wayne DART v1.3",
				TransactionType: h.getTransactionType(baseReq.Command),
				TransactionCode: h.getTransactionCode(baseReq.Command),
				ResponseTime:    executionTime,
				ProtocolStatus:  "NACK", // 执行失败时返回NACK
			},
		}
		return c.JSON(http.StatusInternalServerError, response)
	}

	// 成功响应
	completedTime := time.Now()
	response := dto.WayneCommandResponse{
		CommandID: baseReq.CommandID,
		DeviceID:  baseReq.DeviceID,
		Command:   baseReq.Command,
		Success:   true,
		Data: map[string]interface{}{
			"message":         "Wayne DART command executed successfully",
			"device_id":       baseReq.DeviceID,
			"command":         string(baseReq.Command),
			"protocol_status": "ACK", // 执行成功时返回ACK
			"timestamp":       completedTime,
		},
		ExecutionTime: executionTime,
		SubmittedAt:   startTime,
		CompletedAt:   &completedTime,
		ProtocolInfo: &dto.ProtocolInfo{
			Protocol:        "Wayne DART v1.3",
			TransactionType: h.getTransactionType(baseReq.Command),
			TransactionCode: h.getTransactionCode(baseReq.Command),
			ResponseTime:    executionTime,
			ProtocolStatus:  "ACK", // 执行成功时返回ACK
		},
	}

	h.logger.Info("Wayne command executed successfully",
		zap.String("command_id", baseReq.CommandID),
		zap.String("device_id", baseReq.DeviceID),
		zap.String("command", string(baseReq.Command)),
		zap.Int64("execution_time_ms", executionTime))

	return c.JSON(http.StatusOK, response)
}

// getTransactionType 获取事务类型
func (h *WayneCommandHandler) getTransactionType(command dto.WayneCommandType) string {
	switch command {
	case dto.WayneCommandAuthorize, dto.WayneCommandReset, dto.WayneCommandStop,
		dto.WayneCommandReturnStatus, dto.WayneCommandReturnFillingInfo:
		return "CD1"
	case dto.WayneCommandConfigureNozzles:
		return "CD2"
	case dto.WayneCommandPresetVolume:
		return "CD3"
	case dto.WayneCommandPresetAmount:
		return "CD4"
	case dto.WayneCommandUpdatePrices:
		return "CD5"
	case dto.WayneCommandSuspendNozzle:
		return "CD14"
	case dto.WayneCommandResumeNozzle:
		return "CD15"
	case dto.WayneCommandRequestCounters:
		return "CD101"
	default:
		return "UNKNOWN"
	}
}

// getTransactionCode 获取事务代码
func (h *WayneCommandHandler) getTransactionCode(command dto.WayneCommandType) string {
	switch command {
	case dto.WayneCommandReturnStatus:
		return "0x00"
	case dto.WayneCommandReturnFillingInfo:
		return "0x04"
	case dto.WayneCommandReset:
		return "0x05"
	case dto.WayneCommandAuthorize:
		return "0x06"
	case dto.WayneCommandStop:
		return "0x08"
	case dto.WayneCommandConfigureNozzles:
		return "0x02"
	case dto.WayneCommandPresetVolume:
		return "0x03"
	case dto.WayneCommandPresetAmount:
		return "0x04"
	case dto.WayneCommandUpdatePrices:
		return "0x05"
	case dto.WayneCommandSuspendNozzle:
		return "0x0E"
	case dto.WayneCommandResumeNozzle:
		return "0x0F"
	case dto.WayneCommandRequestCounters:
		return "0x65"
	default:
		return "0x00"
	}
}
