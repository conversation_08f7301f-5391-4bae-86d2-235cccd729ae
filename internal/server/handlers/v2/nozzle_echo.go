package handlers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"fcc-service/internal/server/dto"
	"fcc-service/internal/services/nozzle"
	v2 "fcc-service/internal/services/polling/v2"
	"fcc-service/pkg/api"
	"fcc-service/pkg/models"
	v2models "fcc-service/pkg/models/v2"
)

// NozzleEchoHandlerV2 V2架构的喷嘴处理器
type NozzleEchoHandlerV2 struct {
	dispatchTask  *v2.DispatchTask            // v2调度任务
	stateManager  v2models.DeviceStateManager // v2状态管理器
	deviceManager api.DeviceManager           // 设备管理器（数据库+缓存）
	nozzleService nozzle.ServiceV2            // 喷嘴服务
	logger        *zap.Logger
}

// NewNozzleEchoHandlerV2 创建v2喷嘴处理器
func NewNozzleEchoHandlerV2(
	dispatchTask *v2.DispatchTask,
	stateManager v2models.DeviceStateManager,
	deviceManager api.DeviceManager,
	nozzleService nozzle.ServiceV2,
	logger *zap.Logger,
) *NozzleEchoHandlerV2 {
	return &NozzleEchoHandlerV2{
		dispatchTask:  dispatchTask,
		stateManager:  stateManager,
		deviceManager: deviceManager,
		nozzleService: nozzleService,
		logger:        logger,
	}
}

// GetDeviceNozzles 获取设备的所有喷嘴状态
// GET /api/v2/devices/{id}/nozzles
func (h *NozzleEchoHandlerV2) GetDeviceNozzles(c echo.Context) error {
	deviceID := c.Param("id")
	if deviceID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID is required")
	}

	ctx := context.Background()

	// 首先验证设备是否存在
	device, err := h.deviceManager.GetDevice(ctx, deviceID)
	if err != nil {
		h.logger.Error("Device not found",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Device not found")
	}

	// 从数据库获取喷嘴列表，不再合并实时状态
	nozzles, err := h.nozzleService.GetDeviceNozzles(ctx, deviceID)
	if err != nil {
		h.logger.Error("Failed to get device nozzles",
			zap.String("device_id", deviceID),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get nozzles")
	}

	// 直接转换为响应格式，不合并实时状态
	response := dto.FromNozzleModels(deviceID, device.Name, nozzles)

	return c.JSON(http.StatusOK, response)
}

// GetNozzle 获取单个喷嘴状态
// GET /api/v2/devices/{id}/nozzles/{number}
func (h *NozzleEchoHandlerV2) GetNozzle(c echo.Context) error {
	deviceID := c.Param("id")
	nozzleNumberStr := c.Param("number")

	if deviceID == "" || nozzleNumberStr == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID and nozzle number are required")
	}

	// 解析喷嘴编号
	nozzleNumber, err := strconv.ParseUint(nozzleNumberStr, 10, 8)
	if err != nil || nozzleNumber < 1 || nozzleNumber > 15 {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid nozzle number: must be between 1 and 15")
	}

	ctx := context.Background()

	// 从数据库获取喷嘴信息，不再合并实时状态
	nozzle, err := h.nozzleService.GetNozzle(ctx, deviceID, byte(nozzleNumber))
	if err != nil {
		h.logger.Error("Nozzle not found",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", byte(nozzleNumber)),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Nozzle not found")
	}

	// 直接转换为响应格式，不合并实时状态
	response := dto.FromNozzleModel(nozzle)

	return c.JSON(http.StatusOK, response)
}

// GetNozzleTransaction 获取喷嘴当前交易信息
// GET /api/v2/devices/{id}/nozzles/{number}/transaction
func (h *NozzleEchoHandlerV2) GetNozzleTransaction(c echo.Context) error {
	deviceID := c.Param("id")
	nozzleNumberStr := c.Param("number")

	if deviceID == "" || nozzleNumberStr == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID and nozzle number are required")
	}

	// 解析喷嘴编号
	nozzleNumber, err := strconv.ParseUint(nozzleNumberStr, 10, 8)
	if err != nil || nozzleNumber < 1 || nozzleNumber > 15 {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid nozzle number: must be between 1 and 15")
	}

	ctx := context.Background()

	// 从数据库获取喷嘴信息，不再合并实时状态
	nozzle, err := h.nozzleService.GetNozzle(ctx, deviceID, byte(nozzleNumber))
	if err != nil {
		h.logger.Error("Nozzle not found for transaction query",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", byte(nozzleNumber)),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Nozzle not found")
	}

	// 获取交易信息
	var transactionID *string
	var startTime *time.Time

	// 尝试从TransactionLifecycleService获取活跃交易信息
	if poller, err := h.dispatchTask.GetDevice(deviceID); err == nil {
		if lifecycleService := poller.GetTransactionLifecycleService(); lifecycleService != nil {
			if activeTransactions, err := lifecycleService.GetActiveTransactions(ctx, deviceID); err == nil {
				// 查找匹配的喷嘴交易
				for _, transaction := range activeTransactions {
					if transaction.NozzleID == nozzle.ID {
						transactionID = &transaction.ID
						if transaction.StartedAt != nil {
							startTime = transaction.StartedAt
						}
						break
					}
				}
			}
		}
	}

	// 转换为响应格式
	response := dto.FromNozzleTransaction(nozzle, transactionID, startTime)

	return c.JSON(http.StatusOK, response)
}

// GetNozzleStats 获取喷嘴统计信息
// GET /api/v2/devices/{id}/nozzles/{number}/stats
func (h *NozzleEchoHandlerV2) GetNozzleStats(c echo.Context) error {
	deviceID := c.Param("id")
	nozzleNumberStr := c.Param("number")

	if deviceID == "" || nozzleNumberStr == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Device ID and nozzle number are required")
	}

	// 解析喷嘴编号
	nozzleNumber, err := strconv.ParseUint(nozzleNumberStr, 10, 8)
	if err != nil || nozzleNumber < 1 || nozzleNumber > 15 {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid nozzle number: must be between 1 and 15")
	}

	ctx := context.Background()

	// 从数据库获取喷嘴信息
	nozzle, err := h.nozzleService.GetNozzle(ctx, deviceID, byte(nozzleNumber))
	if err != nil {
		h.logger.Error("Nozzle not found for stats query",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", byte(nozzleNumber)),
			zap.Error(err))
		return echo.NewHTTPError(http.StatusNotFound, "Nozzle not found")
	}

	// 构建统计响应
	response := dto.NozzleStatsResponse{
		ID:               nozzle.ID,
		NozzleNumber:     nozzle.Number,
		TotalVolume:      nozzle.TotalVolume,
		TotalAmount:      nozzle.TotalAmount,
		TransactionCount: nozzle.TransactionCount,
		LastTransaction:  &nozzle.UpdatedAt,
		Utilization:      h.calculateNozzleUtilization(nozzle),
	}

	// 计算平均值
	if nozzle.TransactionCount > 0 {
		transactionCountDecimal := decimal.NewFromInt(nozzle.TransactionCount)
		response.AverageVolume = nozzle.TotalVolume.Div(transactionCountDecimal)
		response.AverageAmount = nozzle.TotalAmount.Div(transactionCountDecimal)
	} else {
		response.AverageVolume = decimal.NewFromInt(0)
		response.AverageAmount = decimal.NewFromInt(0)
	}

	return c.JSON(http.StatusOK, response)
}

// 辅助方法

// calculateNozzleUtilization 计算喷嘴使用率
func (h *NozzleEchoHandlerV2) calculateNozzleUtilization(nozzle *models.Nozzle) float64 {
	// 简单的使用率计算：基于交易次数和时间
	// 这里可以根据业务需求实现更复杂的计算逻辑

	if nozzle.TransactionCount == 0 {
		return 0.0
	}

	// 计算设备运行天数（从创建时间到现在）
	daysSinceCreation := time.Since(nozzle.CreatedAt).Hours() / 24
	if daysSinceCreation < 1 {
		daysSinceCreation = 1 // 至少按1天计算
	}

	// 每天交易次数作为使用率指标（假设每天最多10次交易为满使用率）
	transactionsPerDay := float64(nozzle.TransactionCount) / daysSinceCreation
	utilization := transactionsPerDay / 10.0 // 假设每天10次交易为100%使用率

	if utilization > 1.0 {
		utilization = 1.0
	}

	return utilization
}
