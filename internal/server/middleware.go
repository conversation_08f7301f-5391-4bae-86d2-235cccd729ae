package server

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"go.uber.org/zap"
)

// SetupMiddleware 设置Echo中间件
func SetupMiddleware(e *echo.Echo, logger *zap.Logger) {
	// Recovery中间件 - 防止panic导致服务崩溃
	e.Use(middleware.Recover())

	// CORS中间件 - 解决跨域问题
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
		AllowHeaders:     []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization, "X-Requested-With"},
		AllowCredentials: false,
		MaxAge:           86400,
	}))

	// 请求日志中间件
	e.Use(RequestLogger(logger))

	// 路由调试中间件（仅在调试模式下启用）
	e.Use(RouteDebugLogger(logger))

	// 统一错误处理中间件
	e.HTTPErrorHandler = CustomErrorHandler(logger)
}

// RequestLogger 自定义请求日志中间件
func RequestLogger(logger *zap.Logger) echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()

			// 执行请求
			err := next(c)

			// 记录请求日志
			req := c.Request()
			res := c.Response()

			logger.Info("HTTP Request",
				zap.String("method", req.Method),
				zap.String("uri", req.RequestURI),
				zap.String("remote_ip", c.RealIP()),
				zap.String("user_agent", req.UserAgent()),
				zap.Int("status", res.Status),
				zap.Int64("bytes_out", res.Size),
				zap.Duration("latency", time.Since(start)),
			)

			return err
		}
	})
}

// CustomErrorHandler 自定义错误处理器
func CustomErrorHandler(logger *zap.Logger) echo.HTTPErrorHandler {
	return func(err error, c echo.Context) {
		// 如果响应已经发送，直接返回
		if c.Response().Committed {
			return
		}

		var (
			code = http.StatusInternalServerError
			msg  interface{}
		)

		// 处理Echo内置错误
		if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
			msg = he.Message
		} else {
			// 处理自定义错误或其他错误
			msg = map[string]interface{}{
				"error":     "Internal server error",
				"message":   err.Error(),
				"timestamp": time.Now().Format(time.RFC3339),
			}
		}

		// 记录错误日志
		logger.Error("HTTP Error",
			zap.String("method", c.Request().Method),
			zap.String("uri", c.Request().RequestURI),
			zap.Int("status", code),
			zap.Error(err),
		)

		// 发送错误响应
		if !c.Response().Committed {
			if c.Request().Method == http.MethodHead {
				c.NoContent(code)
			} else {
				c.JSON(code, msg)
			}
		}
	}
}

// RouteDebugLogger 路由调试日志中间件
// 用于调试路由匹配问题，记录详细的路由信息
func RouteDebugLogger(logger *zap.Logger) echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 记录路由调试信息
			req := c.Request()

			// 获取路由信息
			route := c.Path()
			if route == "" {
				route = "NO_ROUTE_MATCHED"
			}

			// 记录详细的路由调试信息
			logger.Debug("Route Debug",
				zap.String("method", req.Method),
				zap.String("request_uri", req.RequestURI),
				zap.String("request_path", req.URL.Path),
				zap.String("matched_route", route),
				zap.Any("path_params", c.ParamNames()),
				zap.Any("path_values", c.ParamValues()),
				zap.String("remote_ip", c.RealIP()),
			)

			// 特别关注 preauth 相关的请求
			path := req.URL.Path
			if path == "/preauth" ||
				path == "/preauth/" ||
				(len(path) >= 8 && path[:8] == "/preauth") {
				logger.Warn("PREAUTH Route Debug - Potential Route Issue",
					zap.String("method", req.Method),
					zap.String("request_uri", req.RequestURI),
					zap.String("request_path", path),
					zap.String("matched_route", route),
					zap.String("expected_route", "/api/v2/wayne/preauth/:device_id/:nozzle_id"),
					zap.String("suggestion", "Client should use /api/v2/wayne/preauth/ instead of /preauth/"),
				)
			}

			return next(c)
		}
	})
}
