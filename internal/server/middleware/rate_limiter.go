package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"fcc-service/internal/storage"
)

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	WindowDuration time.Duration // 时间窗口
	MaxRequests    int           // 最大请求数
	Enabled        bool          // 是否启用
}

// DefaultRateLimitRules 默认限流规则
var DefaultRateLimitRules = map[string]RateLimitConfig{
	"preset_volume": {
		WindowDuration: 3 * time.Second,
		MaxRequests:    1,
		Enabled:        true,
	},
	"preset_amount": {
		WindowDuration: 3 * time.Second,
		MaxRequests:    1,
		Enabled:        true,
	},
	"authorize": {
		WindowDuration: 3 * time.Second,
		MaxRequests:    2, // 允许重试
		Enabled:        true,
	},
}

// WayneRateLimiterMiddleware Wayne命令限流中间件
// 基于device_id + nozzle_id + operation的请求时间限制
func WayneRateLimiterMiddleware(cache storage.Cache, logger *zap.Logger) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 只对Wayne命令路径进行限流
			if !strings.Contains(c.Path(), "/wayne/") {
				return next(c)
			}

			// 提取操作类型
			operation := extractWayneOperation(c.Path())
			if operation == "" {
				return next(c) // 不是目标操作，直接通过
			}

			// 检查操作是否启用限流
			config, exists := DefaultRateLimitRules[operation]
			if !exists || !config.Enabled {
				return next(c) // 未配置或未启用限流，直接通过
			}

			// 提取请求参数
			deviceID, nozzleID, err := extractRequestParams(c)
			if err != nil {
				logger.Debug("Failed to extract request params for rate limiting",
					zap.Error(err),
					zap.String("path", c.Path()))
				return next(c) // 解析失败，让后续handler处理
			}

			// 构建限流key
			if deviceID != "" && nozzleID != "" {
				key := fmt.Sprintf("throttle:%s:%s:%s", deviceID, nozzleID, operation)

				// 检查限流
				allowed, remaining := checkRateLimit(cache, key, config.WindowDuration, config.MaxRequests)
				if !allowed {
					logger.Warn("Rate limit exceeded",
						zap.String("device_id", deviceID),
						zap.String("nozzle_id", nozzleID),
						zap.String("operation", operation),
						zap.String("rate_limit_key", key),
						zap.Duration("window", config.WindowDuration),
						zap.Int("max_requests", config.MaxRequests))

					return echo.NewHTTPError(429, map[string]interface{}{
						"error":         "Rate limit exceeded",
						"message":       fmt.Sprintf("Nozzle %s can only perform %s %d time(s) per %v", nozzleID, operation, config.MaxRequests, config.WindowDuration),
						"device_id":     deviceID,
						"nozzle_id":     nozzleID,
						"operation":     operation,
						"window_seconds": int(config.WindowDuration.Seconds()),
						"max_requests":  config.MaxRequests,
						"retry_after":   int(config.WindowDuration.Seconds()),
					})
				}

				logger.Debug("Rate limit check passed",
					zap.String("device_id", deviceID),
					zap.String("nozzle_id", nozzleID),
					zap.String("operation", operation),
					zap.Int("remaining", remaining))
			}

			return next(c)
		}
	}
}

// extractWayneOperation 从路径提取Wayne操作类型
func extractWayneOperation(path string) string {
	if strings.Contains(path, "/preset-volume") {
		return "preset_volume"
	}
	if strings.Contains(path, "/preset-amount") {
		return "preset_amount"
	}
	if strings.Contains(path, "/authorize") {
		return "authorize"
	}
	return ""
}

// extractRequestParams 从请求体提取device_id和nozzle_id
func extractRequestParams(c echo.Context) (deviceID, nozzleID string, err error) {
	// 读取请求体
	body, err := io.ReadAll(c.Request().Body)
	if err != nil {
		return "", "", fmt.Errorf("failed to read request body: %w", err)
	}

	// 重新设置请求体供后续handler使用
	c.Request().Body = io.NopCloser(strings.NewReader(string(body)))

	// 解析JSON获取基础字段
	var baseReq struct {
		DeviceID string  `json:"device_id"`
		NozzleID *string `json:"nozzle_id"`
	}

	if err := json.Unmarshal(body, &baseReq); err != nil {
		return "", "", fmt.Errorf("failed to parse request JSON: %w", err)
	}

	deviceID = baseReq.DeviceID
	if baseReq.NozzleID != nil {
		nozzleID = *baseReq.NozzleID
	}

	return deviceID, nozzleID, nil
}

// checkRateLimit 检查限流
// 返回: (是否允许, 剩余请求数)
func checkRateLimit(cache storage.Cache, key string, window time.Duration, limit int) (bool, int) {
	ctx := context.Background()

	// 获取当前计数
	count, err := cache.Incr(ctx, key)
	if err != nil {
		// 缓存失败时允许请求，避免影响业务
		return true, limit - 1
	}

	// 首次请求设置过期时间
	if count == 1 {
		if err := cache.Expire(ctx, key, window); err != nil {
			// 设置过期时间失败，但不影响当前请求
		}
	}

	allowed := count <= int64(limit)
	remaining := limit - int(count)
	if remaining < 0 {
		remaining = 0
	}

	return allowed, remaining
} 