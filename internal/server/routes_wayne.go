package server

import (
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	handlers "fcc-service/internal/server/handlers/v2"
	"fcc-service/internal/server/middleware"
	"fcc-service/internal/services/nozzle"
	v2Services "fcc-service/internal/services/polling/v2"
	"fcc-service/internal/storage"
	"fcc-service/pkg/api"
)

// SetupWayneRoutes 设置Wayne DART协议专用路由
// 使用类型安全的DTO和处理器，避免复杂的参数解析
func SetupWayneRoutes(
	e *echo.Echo,
	dispatchTask *v2Services.DispatchTask,
	deviceManager api.DeviceManager,
	nozzleService nozzle.ServiceV2,
	cache storage.Cache,
	logger *zap.Logger,
) {
	// 创建Wayne命令处理器，正确传入nozzleService
	wayneHandler := handlers.NewWayneCommandHandler(dispatchTask, deviceManager, logger, nozzleService)

	// 创建预授权处理器（使用DispatchTask访问设备级别的OperatorIDCache）
	preAuthHandler := handlers.NewPreAuthHandler(dispatchTask, nozzleService, logger)

	// Wayne DART 协议专用API路由组
	wayne := e.Group("/api/v2/wayne")

	// 添加Wayne命令限流中间件
	wayne.Use(middleware.WayneRateLimiterMiddleware(cache, logger))

	// CD1: 泵控制命令
	// wayne.POST("/authorize", wayneHandler.ExecuteAuthorize)                       // 授权命令 CD1(0x06)
	wayne.POST("/reset", wayneHandler.ExecuteReset)                               // 复位命令 CD1(0x05)
	wayne.POST("/stop", wayneHandler.ExecuteStop)                                 // 停止命令 CD1(0x08)
	wayne.POST("/status", wayneHandler.ExecuteReturnStatus)                       // 返回状态命令 CD1(0x00)
	wayne.POST("/filling-info", wayneHandler.ExecuteReturnFillingInfo)            // 返回填充信息命令 CD1(0x04)
	wayne.POST("/resume-fuelling-point", wayneHandler.ExecuteResumeFuellingPoint) // 恢复加油点命令 CD1(0x0E)

	// CD2: 配置允许的喷嘴
	wayne.POST("/configure-nozzles", wayneHandler.ExecuteConfigureNozzles) // 配置喷嘴命令 CD2

	// CD3: 预设体积
	wayne.POST("/preset-volume", wayneHandler.ExecutePresetVolume) // 预设体积命令 CD3

	// CD4: 预设金额
	wayne.POST("/preset-amount", wayneHandler.ExecutePresetAmount) // 预设金额命令 CD4

	// CD5: 价格更新
	wayne.POST("/update-prices", wayneHandler.ExecuteUpdatePrices) // 价格更新命令 CD5

	// CD14: 暂停喷嘴
	wayne.POST("/suspend-nozzle", wayneHandler.ExecuteSuspendNozzle) // 暂停喷嘴命令 CD14

	// CD15: 恢复喷嘴
	wayne.POST("/resume-nozzle", wayneHandler.ExecuteResumeNozzle) // 恢复喷嘴命令 CD15

	// CD101: 请求计数器
	wayne.POST("/request-counters", wayneHandler.ExecuteRequestCounters) // 请求计数器命令 CD101

	// 🆕 预授权API路由
	wayne.POST("/preauth", preAuthHandler.CreatePreAuth)                         // 创建预授权
	wayne.GET("/preauth/:device_id/:nozzle_id", preAuthHandler.GetPreAuth)       // 获取预授权信息（调试用）
	wayne.DELETE("/preauth/:device_id/:nozzle_id", preAuthHandler.DeletePreAuth) // 删除预授权（调试用）

	logger.Info("Wayne DART protocol routes registered with rate limiting",
		zap.String("base_path", "/api/v2/wayne"),
		zap.Int("route_count", 14), // 更新路由数量
		zap.Bool("nozzle_service_available", nozzleService != nil),
		zap.Bool("rate_limiting_enabled", cache != nil),
		zap.Bool("preauth_handler_available", preAuthHandler != nil))
}
