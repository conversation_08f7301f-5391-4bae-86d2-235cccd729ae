package dto

import (
	"time"

	"fcc-service/pkg/models"

	"github.com/shopspring/decimal"
)

// NozzleStatusResponse 喷嘴状态响应
type NozzleStatusResponse struct {
	ID            string          `json:"id"`             // 喷嘴ID
	NozzleNumber  byte            `json:"nozzle_number"`  // 喷嘴编号
	Name          string          `json:"name"`           // 喷嘴名称
	Status        string          `json:"status"`         // 状态：idle, selected, authorized, out, filling, completed, suspended, error, maintenance
	IsOut         bool            `json:"is_out"`         // 是否拔出
	IsSelected    bool            `json:"is_selected"`    // 是否选中
	IsEnabled     bool            `json:"is_enabled"`     // 是否启用
	CurrentPrice  decimal.Decimal `json:"current_price"`  // 当前单价
	CurrentVolume decimal.Decimal `json:"current_volume"` // 当前体积
	CurrentAmount decimal.Decimal `json:"current_amount"` // 当前金额
	TotalVolume   decimal.Decimal `json:"total_volume"`   // 累计体积
	TotalAmount   decimal.Decimal `json:"total_amount"`   // 累计金额
	LastUpdate    time.Time       `json:"last_update"`    // 最后更新时间

	// 扩展信息
	FuelGradeName    *string          `json:"fuel_grade_name,omitempty"` // 油品名称
	TransactionCount int64            `json:"transaction_count"`         // 交易次数
	PresetVolume     *decimal.Decimal `json:"preset_volume,omitempty"`   // 预设体积
	PresetAmount     *decimal.Decimal `json:"preset_amount,omitempty"`   // 预设金额

	// 🆕 预授权追踪字段
	PreAuthType      *string          `json:"preauth_type"`       // 预授权类型：preset_volume 或 preset_amount
	PreAuthNumber    *decimal.Decimal `json:"preauth_number"`     // 预授权数字（体积或金额）
	PreAuthCreatedAt *time.Time       `json:"preauth_created_at"` // 预授权创建时间
	PreAuthExpiresAt *time.Time       `json:"preauth_expires_at"` // 预授权过期时间

	// 元数据
	Metadata interface{} `json:"metadata,omitempty"` // 元数据
}

// DeviceNozzlesResponse 设备喷嘴列表响应
type DeviceNozzlesResponse struct {
	DeviceID     string                 `json:"device_id"`     // 设备ID
	DeviceName   string                 `json:"device_name"`   // 设备名称
	TotalNozzles int                    `json:"total_nozzles"` // 总喷嘴数
	ActiveCount  int                    `json:"active_count"`  // 活跃喷嘴数
	Nozzles      []NozzleStatusResponse `json:"nozzles"`       // 喷嘴列表
	LastUpdate   time.Time              `json:"last_update"`   // 最后更新时间
}

// NozzleTransactionResponse 喷嘴当前交易信息
type NozzleTransactionResponse struct {
	ID            string           `json:"id"`                      // 喷嘴ID
	NozzleNumber  byte             `json:"nozzle_number"`           // 喷嘴编号
	TransactionID *string          `json:"transaction_id"`          // 交易ID
	Status        string           `json:"status"`                  // 交易状态
	StartTime     *time.Time       `json:"start_time"`              // 开始时间
	CurrentVolume decimal.Decimal  `json:"current_volume"`          // 当前体积
	CurrentAmount decimal.Decimal  `json:"current_amount"`          // 当前金额
	UnitPrice     decimal.Decimal  `json:"unit_price"`              // 单价
	PresetVolume  *decimal.Decimal `json:"preset_volume,omitempty"` // 预设体积
	PresetAmount  *decimal.Decimal `json:"preset_amount,omitempty"` // 预设金额
	Duration      *time.Duration   `json:"duration,omitempty"`      // 交易持续时间
	IsActive      bool             `json:"is_active"`               // 是否活跃交易
}

// NozzleStatsResponse 喷嘴统计信息
type NozzleStatsResponse struct {
	ID               string          `json:"id"`                // 喷嘴ID
	NozzleNumber     byte            `json:"nozzle_number"`     // 喷嘴编号
	TotalVolume      decimal.Decimal `json:"total_volume"`      // 累计体积
	TotalAmount      decimal.Decimal `json:"total_amount"`      // 累计金额
	TransactionCount int64           `json:"transaction_count"` // 交易次数
	AverageVolume    decimal.Decimal `json:"average_volume"`    // 平均体积
	AverageAmount    decimal.Decimal `json:"average_amount"`    // 平均金额
	LastTransaction  *time.Time      `json:"last_transaction"`  // 最后交易时间
	Utilization      float64         `json:"utilization"`       // 使用率（0-1）
}

// 转换函数：从模型转换为DTO

// FromNozzleModel 从Nozzle模型转换为NozzleStatusResponse
func FromNozzleModel(nozzle *models.Nozzle) NozzleStatusResponse {
	response := NozzleStatusResponse{
		ID:               nozzle.ID,
		NozzleNumber:     nozzle.Number,
		Name:             nozzle.Name,
		Status:           string(nozzle.Status),
		IsOut:            nozzle.IsOut,
		IsSelected:       nozzle.IsSelected,
		IsEnabled:        nozzle.IsEnabled,
		CurrentPrice:     nozzle.CurrentPrice, // 修复：直接使用实际价格，不除以1000
		CurrentVolume:    nozzle.CurrentVolume,
		CurrentAmount:    nozzle.CurrentAmount, // 修复：直接使用实际金额，不除以1000
		TotalVolume:      nozzle.TotalVolume,
		TotalAmount:      nozzle.TotalAmount, // 修复：直接使用实际金额，不除以1000
		TransactionCount: nozzle.TransactionCount,
		PresetVolume:     nozzle.PresetVolume,
		PresetAmount:     nozzle.PresetAmount,
		LastUpdate:       nozzle.UpdatedAt,

		// 🆕 预授权追踪字段
		PreAuthType:      nozzle.PreAuthType,
		PreAuthNumber:    nozzle.PreAuthNumber,
		PreAuthCreatedAt: nozzle.PreAuthCreatedAt,
		PreAuthExpiresAt: nozzle.PreAuthExpiresAt,

		// 元数据
		Metadata: nozzle.Metadata,
	}

	// 添加油品信息
	if nozzle.FuelGrade != nil {
		response.FuelGradeName = &nozzle.FuelGrade.Name
	}

	return response
}

// FromNozzleModels 从Nozzle模型列表转换为DeviceNozzlesResponse
func FromNozzleModels(deviceID, deviceName string, nozzles []*models.Nozzle) DeviceNozzlesResponse {
	response := DeviceNozzlesResponse{
		DeviceID:     deviceID,
		DeviceName:   deviceName,
		TotalNozzles: len(nozzles),
		ActiveCount:  0,
		Nozzles:      make([]NozzleStatusResponse, len(nozzles)),
		LastUpdate:   time.Now(),
	}

	for i, nozzle := range nozzles {
		response.Nozzles[i] = FromNozzleModel(nozzle)

		// 统计活跃喷嘴
		if nozzle.IsActive() {
			response.ActiveCount++
		}

		// 更新最后更新时间
		if nozzle.UpdatedAt.After(response.LastUpdate) {
			response.LastUpdate = nozzle.UpdatedAt
		}
	}

	return response
}

// FromNozzleTransaction 从交易信息转换为NozzleTransactionResponse
func FromNozzleTransaction(nozzle *models.Nozzle, transactionID *string, startTime *time.Time) NozzleTransactionResponse {
	response := NozzleTransactionResponse{
		ID:            nozzle.ID,
		NozzleNumber:  nozzle.Number,
		TransactionID: transactionID,
		Status:        string(nozzle.Status),
		StartTime:     startTime,
		CurrentVolume: nozzle.CurrentVolume,
		CurrentAmount: nozzle.CurrentAmount, // 修复：直接使用实际金额，不除以1000
		UnitPrice:     nozzle.CurrentPrice,  // 修复：直接使用实际价格，不除以1000
		PresetVolume:  nozzle.PresetVolume,
		PresetAmount:  nozzle.PresetAmount,
		IsActive:      nozzle.IsTransactionInProgress(),
	}

	// 计算交易持续时间
	if startTime != nil && response.IsActive {
		duration := time.Since(*startTime)
		response.Duration = &duration
	}

	// 计算单价（如果有体积数据）- 使用真实价格值计算
	if !nozzle.CurrentVolume.IsZero() && !nozzle.CurrentAmount.IsZero() {
		response.UnitPrice = nozzle.CurrentAmount.Div(nozzle.CurrentVolume) // 修复：直接使用实际金额计算
	}

	return response
}
