# 二进制文件
*.exe
*.dll
*.so
*.dylib

# 编译产物
/bin/
/build/
/dist/

# Go 编译产物
*.test
*.prof
*.cover
fcc-service-v2
fcc-service
fcc-service-*

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.log
*.pid

# 环境配置
.env
.env.local
.env.production

# 操作系统文件
.DS_Store
Thumbs.db

# 测试覆盖率
coverage.out
coverage.html

# 依赖目录
vendor/

# 配置文件的敏感信息
config.local.yaml
*.key
*.pem
*.crt

# Node.js 和前端项目
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Next.js
.next/
out/

# Production
build/
dist/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Vercel
.vercel

# Environment variables
.env*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Webpack
.webpack/

# Electron-builder output
/dist_electron/

# 项目特定的排除文件
.claude/
.cursor/
bin/
logs/
__pycache__/
*.pyc
*.pyo
*.pyd

# 临时和调试文件
*.gz
*.bak
*.backup
analysis_result.txt 


nohup.out
main_v2
