package main

import (
	"context"
	"runtime/debug"

	// "database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	// "github.com/go-redis/redis/v8"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"fcc-service/internal/bootstrap"
	"fcc-service/internal/config"
	"fcc-service/internal/server"
	"fcc-service/internal/server/handlers"
	handlersv2 "fcc-service/internal/server/handlers/v2"
	"fcc-service/internal/services/device"
	"fcc-service/internal/services/device_runtime_state"
	"fcc-service/internal/services/fuel_sync"
	"fcc-service/internal/services/monitor"
	"fcc-service/internal/services/nozzle"
	"fcc-service/internal/services/nozzle_counters"
	pollingv2 "fcc-service/internal/services/polling/v2"
	"fcc-service/internal/services/transaction"
	"fcc-service/internal/storage"
	"fcc-service/pkg/api"
	v2models "fcc-service/pkg/models/v2"
)

const (
	DefaultPollInterval = 1 * time.Second // 🔧 改回：恢复1秒轮询间隔
	MaxConcurrentTasks  = 10
)

// ApplicationV2 基于 v2 架构的应用程序实例
type ApplicationV2 struct {
	// 配置和日志
	config *config.Config
	logger *zap.Logger

	// 基础设施层
	database storage.Database
	cache    storage.Cache

	// 服务层
	deviceManager           api.DeviceManager
	nozzleService           nozzle.ServiceV2
	transactionService      transaction.TransactionService
	lifecycleServiceFactory bootstrap.TransactionLifecycleServiceFactory   // 🚀 新增：交易生命周期服务工厂
	nozzleCountersService   nozzle_counters.Service                        // 🚀 新增：喷嘴计数器服务
	runtimeStateService     device_runtime_state.DeviceRuntimeStateService // 🆕 新增：设备运行时状态服务
	syncMonitor             *monitor.SyncMonitor                           // 🚀 新增：同步监控服务

	// 🚀 新增：Fuel Sync 相关服务
	fuelSyncService   *fuel_sync.FuelGradeSyncService
	fuelSyncScheduler *fuel_sync.SyncScheduler
	fuelSyncHandler   *fuel_sync.FuelSyncHandler

	// v2 核心组件
	stateManager   v2models.DeviceStateManager
	dispatchTask   *pollingv2.DispatchTask
	resultHandlers []pollingv2.ResultHandler

	// v2 处理器
	deviceHandler *handlersv2.DeviceEchoHandlerV2
	nozzleHandler *handlersv2.NozzleEchoHandlerV2

	// HTTP 服务器
	echoServer *echo.Echo

	// 生命周期管理
	ctx       context.Context
	cancel    context.CancelFunc
	startTime time.Time // 添加启动时间记录

	// 🛡️ 全局goroutine管理
	errGroup    *errgroup.Group
	errGroupCtx context.Context

	// 添加缓存配置标识
	cacheEnabled bool
}

// main 应用程序入口点
func main() {
	// 🛡️ 全局Panic恢复：确保程序不会因为任何未捕获的panic而崩溃
	defer func() {
		if r := recover(); r != nil {
			fmt.Fprintf(os.Stderr, "🚨 CRITICAL: Unhandled panic in main: %v\n", r)
			fmt.Fprintf(os.Stderr, "📍 Stack trace:\n%s\n", getStackTrace())

			// 写入panic日志文件（紧急情况下的备用日志）
			if err := writePanicLog(r); err != nil {
				fmt.Fprintf(os.Stderr, "Failed to write panic log: %v\n", err)
			}

			fmt.Fprintf(os.Stderr, "❌ FCC Service terminated due to unrecoverable error\n")
			os.Exit(1)
		}
	}()

	// 解析命令行参数
	var configPath string
	flag.StringVar(&configPath, "config", "configs/config.yaml", "Configuration file path")
	flag.Parse()

	// 创建应用程序实例
	app, err := NewApplicationV2(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create application: %v\n", err)
		os.Exit(1)
	}

	// 运行应用程序
	if err := app.Run(); err != nil {
		app.logger.Error("Application failed to run", zap.Error(err))
		os.Exit(1)
	}
}

// NewApplicationV2 创建新的 v2 应用程序实例
func NewApplicationV2(configPath string) (*ApplicationV2, error) {
	// 加载配置
	cfg, err := config.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// 创建日志器
	logger, err := cfg.GetLogger()
	if err != nil {
		return nil, fmt.Errorf("failed to create logger: %w", err)
	}

	logger.Info("Starting FCC Service V2",
		zap.String("version", "2.0.0"),
		zap.String("config_path", configPath))

	checkGoTimezone()

	// 创建应用程序上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 🛡️ 创建全局错误组，用于管理所有关键goroutine
	errGroup, errGroupCtx := errgroup.WithContext(ctx)

	app := &ApplicationV2{
		config:      cfg,
		logger:      logger,
		ctx:         ctx,
		cancel:      cancel,
		startTime:   time.Now(), // 记录启动时间
		errGroup:    errGroup,
		errGroupCtx: errGroupCtx,
	}

	// 初始化组件
	if err := app.initialize(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize application: %w", err)
	}

	return app, nil
}

// initialize 初始化应用程序组件
func (app *ApplicationV2) initialize() error {
	var err error

	// 1. 初始化基础设施层
	if err = app.initInfrastructure(); err != nil {
		return fmt.Errorf("failed to initialize infrastructure: %w", err)
	}

	// 2. 初始化服务层
	if err = app.initServices(); err != nil {
		return fmt.Errorf("failed to initialize services: %w", err)
	}

	// 3. 初始化 v2 核心组件
	if err = app.initV2Components(); err != nil {
		return fmt.Errorf("failed to initialize v2 components: %w", err)
	}

	// 4. 初始化 HTTP 服务器
	if err = app.initHTTPServer(); err != nil {
		return fmt.Errorf("failed to initialize HTTP server: %w", err)
	}

	return nil
}

// initInfrastructure 初始化基础设施层
func (app *ApplicationV2) initInfrastructure() error {
	app.logger.Info("Initializing infrastructure layer")

	// 初始化数据库
	app.database = storage.NewPostgreSQLDatabase(&app.config.Database, app.logger)
	if err := app.database.Connect(app.ctx); err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 测试数据库连接
	if err := app.database.Ping(app.ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	// 条件性初始化缓存：支持 Redis 开关和降级方案
	app.cacheEnabled = app.config.Redis.Enabled
	if app.cacheEnabled {
		app.logger.Info("Initializing Redis cache (enabled)")
		app.cache = storage.NewRedisCache(&app.config.Redis, app.logger)
		if err := app.cache.Connect(app.ctx); err != nil {
			app.logger.Warn("Failed to connect to Redis, falling back to memory cache", zap.Error(err))
			app.cacheEnabled = false
			app.cache = storage.NewMemoryCache(app.logger)
		} else {
			// 测试缓存连接
			if err := app.cache.Ping(app.ctx); err != nil {
				app.logger.Warn("Redis ping failed, falling back to memory cache", zap.Error(err))
				app.cacheEnabled = false
				app.cache = storage.NewMemoryCache(app.logger)
			} else {
				app.logger.Info("Redis cache connected successfully")
			}
		}
	} else {
		app.logger.Info("Redis cache disabled, using memory cache")
		app.cache = storage.NewMemoryCache(app.logger)
	}

	app.logger.Info("Infrastructure layer initialized successfully",
		zap.Bool("redis_enabled", app.cacheEnabled),
		zap.String("cache_type", app.getCacheType()))
	return nil
}

// initServices 初始化服务层
func (app *ApplicationV2) initServices() error {
	app.logger.Info("Initializing service layer")

	// 初始化设备管理器（使用现有实现）
	// 注意：这里暂时使用 nil 作为 adapterManager，在 v2 中由 DispatchTask 直接管理通信
	app.deviceManager = device.NewManager(app.database, app.cache, nil, app.logger)

	// 从数据库加载设备数据（只有在真实数据库连接时才执行）
	if deviceMgr, ok := app.deviceManager.(*device.Manager); ok && app.database.IsConnected() {
		if err := deviceMgr.BatchLoadControllersFromDB(app.ctx); err != nil {
			app.logger.Error("Failed to load controllers from database", zap.Error(err))
			// 在测试模式下，不将此视为致命错误
			app.logger.Warn("Continuing without loading controllers from database")
		}

		if err := deviceMgr.BatchLoadDevicesFromDB(app.ctx); err != nil {
			app.logger.Error("Failed to load devices from database", zap.Error(err))
			// 在测试模式下，不将此视为致命错误
			app.logger.Warn("Continuing without loading devices from database")
		}
	} else {
		app.logger.Info("Skipping database operations (no database connection)")
	}

	// 初始化 Nozzle 服务 V2（只有在真实数据库连接时才执行）
	if postgresDB, ok := app.database.(*storage.PostgreSQLDatabase); ok {
		gormDB := postgresDB.GetGormDB()
		app.nozzleService = nozzle.NewServiceV2(gormDB, app.logger)
	} else {
		return fmt.Errorf("failed to get GORM DB instance for nozzle service")
	}

	// 初始化交易服务及其依赖
	if err := app.initTransactionService(); err != nil {
		return fmt.Errorf("failed to initialize transaction service: %w", err)
	}

	// 🚀 初始化喷嘴计数器服务
	if err := app.initNozzleCountersService(); err != nil {
		return fmt.Errorf("failed to initialize nozzle counters service: %w", err)
	}

	// 🆕 初始化设备运行时状态服务
	if err := app.initRuntimeStateService(); err != nil {
		return fmt.Errorf("failed to initialize runtime state service: %w", err)
	}

	// 🚀 初始化交易生命周期服务
	if err := app.initTransactionLifecycleService(); err != nil {
		return fmt.Errorf("failed to initialize transaction lifecycle service: %w", err)
	}

	// 🚀 初始化同步监控服务
	if err := app.initSyncMonitor(); err != nil {
		app.logger.Warn("Failed to initialize sync monitor", zap.Error(err))
		// 监控服务失败不作为致命错误
	}

	// 🚀 初始化 Fuel Sync 服务
	if err := app.initFuelSyncServices(); err != nil {
		app.logger.Warn("Failed to initialize fuel sync services", zap.Error(err))
		// Fuel sync 服务失败不作为致命错误
	}

	app.logger.Info("Service layer initialized successfully")
	return nil
}

// initTransactionService 初始化交易服务
func (app *ApplicationV2) initTransactionService() error {
	app.logger.Info("Initializing transaction service with integrated sync")

	// 获取 GORM DB 实例
	postgresDB, ok := app.database.(*storage.PostgreSQLDatabase)
	if !ok {
		return fmt.Errorf("failed to get GORM DB instance for transaction service")
	}
	gormDB := postgresDB.GetGormDB()

	// 创建交易仓储
	repository := transaction.NewGormRepository(gormDB, app.logger)

	// 🎯 创建交易服务工厂并创建带Redis支持的完整交易服务
	factory := &transaction.TransactionServiceFactory{}

	// 检查是否有Redis连接可用
	if app.cache != nil {
		// 尝试获取Redis客户端
		if redisCache, ok := app.cache.(*storage.RedisCache); ok {
			redisClient := redisCache.GetClient()
			app.transactionService = factory.CreateTransactionServiceWithRedis(
				repository,
				app.deviceManager,
				app.nozzleService,
				redisClient,
				app.logger,
			)
			app.logger.Info("Transaction service created with Redis sync publisher")
		} else {
			// 回退到内存版本
			app.transactionService = factory.CreateTransactionService(
				repository,
				app.deviceManager,
				app.nozzleService,
				app.logger,
			)
			app.logger.Info("Transaction service created with memory sync publisher (Redis not available)")
		}
	} else {
		// 回退到内存版本
		app.transactionService = factory.CreateTransactionService(
			repository,
			app.deviceManager,
			app.nozzleService,
			app.logger,
		)
		app.logger.Info("Transaction service created with memory sync publisher (no cache available)")
	}

	// 🚀 如果启用同步功能，注册HTTP同步策略
	if app.config.Business.TransactionSync.Enabled {
		httpStrategy := transaction.NewHTTPSyncStrategy(
			"external_api_mvp",
			app.config.Business.TransactionSync.Endpoint,
			app.config.Business.TransactionSync.Timeout,
			app.config.Business.TransactionSync.APIKey,
			app.logger,
		)

		app.transactionService.RegisterSyncStrategy("external_api_mvp", httpStrategy)
		app.logger.Info("HTTP sync strategy registered successfully",
			zap.String("system_id", "external_api_mvp"),
			zap.String("endpoint", app.config.Business.TransactionSync.Endpoint))
	}

	// 🚀 初始化同步监控服务
	if err := app.initSyncMonitor(); err != nil {
		app.logger.Warn("Failed to initialize sync monitor", zap.Error(err))
		// 监控服务失败不作为致命错误
	}

	// 添加详细的调试日志：验证 transaction service 创建
	app.logger.Info("Transaction service initialized successfully",
		zap.Bool("service_created", app.transactionService != nil),
		zap.String("service_type", fmt.Sprintf("%T", app.transactionService)),
		zap.Bool("repository_available", repository != nil),
		zap.Bool("device_manager_available", app.deviceManager != nil),
		zap.Bool("nozzle_service_available", app.nozzleService != nil),
		zap.Bool("sync_enabled", app.config.Business.TransactionSync.Enabled))
	return nil
}

// initTransactionLifecycleService 初始化交易生命周期服务
func (app *ApplicationV2) initTransactionLifecycleService() error {
	app.logger.Info("Initializing transaction lifecycle service with event bridge integration")

	// 获取 GORM DB 实例
	postgresDB, ok := app.database.(*storage.PostgreSQLDatabase)
	if !ok {
		return fmt.Errorf("failed to get GORM DB instance for transaction lifecycle service")
	}
	gormDB := postgresDB.GetGormDB()

	// 🚀 提取 TransactionService 的内部依赖用于事件桥接
	var transactionSyncPublisher transaction.TransactionSyncPublisher
	var transactionDataCollector transaction.TransactionDataCollector
	var nozzleResolver nozzle.NozzleResolver

	if app.transactionService != nil {
		transactionSyncPublisher = app.transactionService.GetSyncPublisher()
		transactionDataCollector = app.transactionService.GetDataCollector()
		nozzleResolver = app.transactionService.GetNozzleResolver()

		app.logger.Info("Transaction service dependencies extracted successfully",
			zap.Bool("has_sync_publisher", transactionSyncPublisher != nil),
			zap.Bool("has_data_collector", transactionDataCollector != nil),
			zap.Bool("has_nozzle_resolver", nozzleResolver != nil))
	} else {
		app.logger.Warn("Transaction service not available, event bridge will not be created")
	}

	// 使用 bootstrap 包初始化服务（包含事件桥接依赖）
	lifecycleServiceFactory := bootstrap.CreateTransactionLifecycleServiceFactory(
		gormDB,
		app.logger,
		app.cache,
		app.nozzleService,
		transactionSyncPublisher,
		transactionDataCollector,
		nozzleResolver,
		app.nozzleCountersService,
		app.runtimeStateService, // 🆕 新增：传递设备运行时状态服务
	)

	app.lifecycleServiceFactory = lifecycleServiceFactory

	app.logger.Info("Transaction lifecycle service initialized successfully with event bridge integration")
	return nil
}

// initSyncMonitor 初始化同步监控服务
func (app *ApplicationV2) initSyncMonitor() error {
	// 检查是否启用交易同步功能
	if !app.config.Business.TransactionSync.Enabled {
		app.logger.Info("Transaction sync disabled, skipping sync monitor initialization")
		return nil
	}

	app.logger.Info("Initializing sync monitor service")

	// 创建监控配置
	monitorConfig := monitor.SyncMonitorConfig{
		// 事件驱动配置
		FailureThreshold: 10, // 1分钟内失败10次触发告警

		// 轮询配置
		PollingInterval: 10 * time.Minute, // 每10分钟进行健康检查
		PollingEnabled:  true,

		// 熔断配置
		CooldownPeriod: 30 * time.Minute, // 30分钟冷却期

		// 健康检查阈值
		MaxFailedCount:   100, // 失败交易超过100笔
		MaxRetryingCount: 50,  // 重试交易超过50笔
		MaxPendingCount:  200, // 待处理交易超过200笔
		MaxPendingAge:    24,  // 最老待处理交易超过24小时

		// 功能开关
		AlertingEnabled: true, // 启用告警
	}

	// 创建监控服务
	app.syncMonitor = monitor.NewSyncMonitor(
		app.transactionService, // 交易服务作为数据提供者
		monitorConfig,
		app.logger,
	)

	app.logger.Info("Sync monitor service initialized successfully",
		zap.Int64("failure_threshold", monitorConfig.FailureThreshold),
		zap.Duration("polling_interval", monitorConfig.PollingInterval),
		zap.Duration("cooldown_period", monitorConfig.CooldownPeriod),
		zap.Bool("alerting_enabled", monitorConfig.AlertingEnabled))

	return nil
}

// initNozzleCountersService 初始化喷嘴计数器服务
func (app *ApplicationV2) initNozzleCountersService() error {
	app.logger.Info("Initializing nozzle counters service")

	// 获取 GORM DB 实例
	postgresDB, ok := app.database.(*storage.PostgreSQLDatabase)
	if !ok {
		return fmt.Errorf("failed to get GORM DB instance for nozzle counters service")
	}
	gormDB := postgresDB.GetGormDB()

	// 创建计数器仓储
	countersRepo := nozzle_counters.NewGormRepository(gormDB, app.logger)

	// 创建计数器服务
	concreteCountersService := nozzle_counters.NewService(countersRepo, app.logger, app.cache)

	// 创建适配器来解决类型不匹配
	app.nozzleCountersService = concreteCountersService

	app.logger.Info("Nozzle counters service initialized successfully")
	return nil
}

// initRuntimeStateService 初始化设备运行时状态服务
func (app *ApplicationV2) initRuntimeStateService() error {
	app.logger.Info("Initializing device runtime state service")

	// 🚀 创建无锁的设备运行时状态服务
	app.runtimeStateService = device_runtime_state.NewService(app.logger)

	app.logger.Info("Device runtime state service initialized successfully")
	return nil
}

// initV2Components 初始化 v2 核心组件
func (app *ApplicationV2) initV2Components() error {
	app.logger.Info("Initializing V2 core components")

	// 1. 创建设备状态管理器
	app.stateManager = app.createStateManager()

	// 2. 创建调度任务配置
	dispatchConfig := pollingv2.DispatchTaskConfig{
		Name:                "FCC-Wayne-Dispatcher",
		MaxDevices:          app.config.Business.Pump.MaxConcurrentOperations,
		DefaultPollInterval: DefaultPollInterval,
		DefaultPollTimeout:  app.config.DART.Protocol.ResponseTimeout, // 🚀 修复：正确使用配置文件中的response_timeout
		WatchdogTimeout:     10 * time.Second,
		MaxRetries:          app.config.DART.Protocol.MaxRetries,
		ResultBufferSize:    1000,
		EnableMetrics:       app.config.Monitoring.Enabled,
	}

	// 3. 创建调度任务
	app.dispatchTask = pollingv2.NewDispatchTask(
		dispatchConfig,
		app.stateManager,
		app.nozzleService,
		app.transactionService,
		app.lifecycleServiceFactory, // 传入工厂函数
		app.nozzleCountersService,
		app.deviceManager,
		&app.config.PreAuth,     // 🆕 传递预授权配置
		&app.config.AutoAuth,    // 🆕 传递自动授权配置
		app.runtimeStateService, // 🆕 新增：传递设备运行时状态服务
		app.logger,
	)

	// 🚀 新增：配置串口调度器
	serialConfig := pollingv2.TimeSliceSchedulerConfig{
		Enabled:          app.config.SerialScheduler.Enabled,
		TimeSliceMs:      app.config.SerialScheduler.TimeSliceMs,
		SwitchDelayMs:    app.config.SerialScheduler.SwitchDelayMs,
		MaxWaitTimeoutMs: app.config.SerialScheduler.MaxWaitTimeoutMs,
	}
	app.dispatchTask.SetSerialSchedulerConfig(serialConfig)

	// 4. 创建结果处理器
	app.createResultHandlers()

	// 5. 注册结果处理器到调度任务
	for _, handler := range app.resultHandlers {
		app.dispatchTask.AddResultHandler(handler)
	}

	// 6. 创建 v2 设备处理器
	app.deviceHandler = handlersv2.NewDeviceEchoHandlerV2(
		app.dispatchTask,
		app.stateManager,
		app.deviceManager,
		app.logger,
	)

	// 7. 创建 v2 喷嘴处理器
	app.nozzleHandler = handlersv2.NewNozzleEchoHandlerV2(
		app.dispatchTask,
		app.stateManager,
		app.deviceManager,
		app.nozzleService,
		app.logger,
	)

	app.logger.Info("V2 core components initialized successfully",
		zap.Int("max_devices", pollingv2.DispatchTaskConfig{
			Name:                "FCC-Wayne-Dispatcher",
			MaxDevices:          app.config.Business.Pump.MaxConcurrentOperations,
			DefaultPollInterval: DefaultPollInterval,
			DefaultPollTimeout:  app.config.DART.Protocol.ResponseTimeout, // 🚀 修复：正确使用配置文件中的response_timeout
			WatchdogTimeout:     10 * time.Second,
			MaxRetries:          app.config.DART.Protocol.MaxRetries,
			ResultBufferSize:    1000,
			EnableMetrics:       app.config.Monitoring.Enabled,
		}.MaxDevices),
		zap.Duration("poll_interval", pollingv2.DispatchTaskConfig{
			Name:                "FCC-Wayne-Dispatcher",
			MaxDevices:          app.config.Business.Pump.MaxConcurrentOperations,
			DefaultPollInterval: DefaultPollInterval,
			DefaultPollTimeout:  app.config.DART.Protocol.ResponseTimeout, // 🚀 修复：正确使用配置文件中的response_timeout
			WatchdogTimeout:     10 * time.Second,
			MaxRetries:          app.config.DART.Protocol.MaxRetries,
			ResultBufferSize:    1000,
			EnableMetrics:       app.config.Monitoring.Enabled,
		}.DefaultPollInterval),
		zap.Duration("poll_timeout", pollingv2.DispatchTaskConfig{
			Name:                "FCC-Wayne-Dispatcher",
			MaxDevices:          app.config.Business.Pump.MaxConcurrentOperations,
			DefaultPollInterval: DefaultPollInterval,
			DefaultPollTimeout:  app.config.DART.Protocol.ResponseTimeout, // 🚀 修复：正确使用配置文件中的response_timeout
			WatchdogTimeout:     10 * time.Second,
			MaxRetries:          app.config.DART.Protocol.MaxRetries,
			ResultBufferSize:    1000,
			EnableMetrics:       app.config.Monitoring.Enabled,
		}.DefaultPollTimeout),
		zap.Int("result_handlers", len(app.resultHandlers)))

	return nil
}

// createStateManager 创建设备状态管理器
func (app *ApplicationV2) createStateManager() v2models.DeviceStateManager {
	return &deviceStateManager{
		devices: make(map[string]v2models.DeviceStateMachine),
		logger:  app.logger,
	}
}

// createResultHandlers 创建结果处理器
func (app *ApplicationV2) createResultHandlers() {
	// 数据库持久化处理器
	dbHandler := &DatabaseResultHandler{
		database: app.database,
		logger:   app.logger,
	}

	// 缓存更新处理器
	cacheHandler := &CacheResultHandler{
		cache:  app.cache,
		logger: app.logger,
	}

	// 状态更新处理器
	stateHandler := &StateUpdateHandler{
		stateManager: app.stateManager,
		logger:       app.logger,
	}

	// 业务逻辑处理器
	businessHandler := &BusinessLogicHandler{
		nozzleService: app.nozzleService,
		logger:        app.logger,
	}

	app.resultHandlers = []pollingv2.ResultHandler{
		dbHandler,
		cacheHandler,
		stateHandler,
		businessHandler,
	}
}

// initHTTPServer 初始化 HTTP 服务器
func (app *ApplicationV2) initHTTPServer() error {
	app.logger.Info("Initializing HTTP server")

	// 创建 Echo 实例
	app.echoServer = echo.New()
	app.echoServer.HideBanner = true
	app.echoServer.HidePort = true

	// 设置中间件
	server.SetupMiddleware(app.echoServer, app.logger)

	// 设置路由
	app.setupRoutes()

	app.logger.Info("HTTP server initialized successfully")

	checkGoTimezone()
	return nil
}

// setupRoutes 设置路由
func (app *ApplicationV2) setupRoutes() {
	api := app.echoServer.Group("/api/v2")

	// 系统状态 API
	api.GET("/health", app.healthCheckHandler)
	api.GET("/status", app.systemStatusHandler)
	api.GET("/metrics", app.metricsHandler)
	api.GET("/cache/stats", app.cacheStatsHandler)

	// 创建transaction handler
	transactionHandler := handlers.NewTransactionHandler(app.transactionService, app.logger)

	// 设备管理 API（使用 v2 设备处理器）
	devices := api.Group("/devices")
	devices.GET("", app.deviceHandler.ListDevices)
	devices.POST("", app.deviceHandler.CreateDevice)
	devices.GET("/:id", app.deviceHandler.GetDevice)
	devices.PUT("/:id", app.deviceHandler.UpdateDevice)
	devices.DELETE("/:id", app.deviceHandler.DeleteDevice)
	devices.GET("/:id/status", app.deviceHandler.GetDeviceStatus)
	devices.GET("/:id/pump/status", app.deviceHandler.GetPumpStatus)
	devices.GET("/:id/pump/totals", app.deviceHandler.GetPumpTotals)

	// 设备交易相关路由
	devices.GET("/:id/transactions", transactionHandler.GetDeviceTransactions)
	devices.GET("/:id/transactions/summary", transactionHandler.GetDeviceTransactionSummary)

	// 设备喷嘴相关路由 - 使用专门的 NozzleEchoHandlerV2
	devices.GET("/:id/nozzles", app.nozzleHandler.GetDeviceNozzles)
	devices.GET("/:id/nozzles/:number", app.nozzleHandler.GetNozzle)
	devices.GET("/:id/nozzles/:number/transaction", app.nozzleHandler.GetNozzleTransaction)
	devices.GET("/:id/nozzles/:number/stats", app.nozzleHandler.GetNozzleStats)

	// 交易管理 API
	transactions := api.Group("/transactions")
	transactions.GET("", transactionHandler.ListTransactions)
	transactions.GET("/:id", transactionHandler.GetTransaction)
	transactions.GET("/stats", transactionHandler.GetTransactionStats)

	// 泵码管理 API - Wayne DART 协议支持
	transactions.GET("/:id/pump-readings", transactionHandler.GetTransactionPumpReadings)
	transactions.GET("/pump-issues", transactionHandler.GetTransactionsWithPumpIssues)

	// 调度任务管理 API
	dispatch := api.Group("/dispatch")
	dispatch.GET("/status", app.getDispatchStatusHandler)
	dispatch.POST("/start", app.startDispatchHandler)
	dispatch.POST("/stop", app.stopDispatchHandler)
	dispatch.GET("/devices", app.getDispatchDevicesHandler)

	// 🚀 新增：设置 Fuel Sync 路由
	if app.fuelSyncHandler != nil {
		fuel_sync.SetupFuelSyncRoutes(api, app.fuelSyncHandler, app.logger)
	}

	// 注册 Wayne DART 协议路由（带限流中间件）
	server.SetupWayneRoutes(app.echoServer, app.dispatchTask, app.deviceManager, app.nozzleService, app.cache, app.logger)

	// 🆕 添加错误路由捕获，帮助调试路由问题
	app.setupErrorRoutes()
}

// setupErrorRoutes 设置错误路由捕获，帮助调试路由问题
func (app *ApplicationV2) setupErrorRoutes() {
	// 捕获错误的 preauth 路由请求
	app.echoServer.Any("/preauth/*", func(c echo.Context) error {
		app.logger.Warn("Incorrect preauth route accessed",
			zap.String("method", c.Request().Method),
			zap.String("path", c.Request().URL.Path),
			zap.String("remote_ip", c.RealIP()),
			zap.String("user_agent", c.Request().UserAgent()),
		)

		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"error":   "Route not found",
			"message": "PreAuth API has moved to /api/v2/wayne/preauth/",
			"correct_routes": map[string]string{
				"POST":   "/api/v2/wayne/preauth",
				"GET":    "/api/v2/wayne/preauth/{device_id}/{nozzle_id}",
				"DELETE": "/api/v2/wayne/preauth/{device_id}/{nozzle_id}",
			},
			"documentation": "Please update your client to use the correct API endpoints",
		})
	})

	app.logger.Info("Error route handlers configured for debugging")
}

// Run 运行应用程序
func (app *ApplicationV2) Run() error {
	app.logger.Info("Starting FCC Service V2 with global errgroup management")

	// 启动 v2 核心组件（同步初始化）
	app.logger.Info("Initializing V2 core components...")
	if err := app.startV2Components(); err != nil {
		return fmt.Errorf("failed to start v2 components: %w", err)
	}
	app.logger.Info("V2 core components initialized successfully")

	// 🛡️ 使用errgroup管理所有关键组件的goroutine

	// 1. HTTP服务器goroutine
	app.errGroup.Go(func() error {
		app.logger.Info("Starting HTTP server", zap.String("addr", app.config.HTTP.Addr))

		// 简化的HTTP服务器启动和关闭处理
		serverErr := make(chan error, 1)
		go func() {
			if err := app.echoServer.Start(app.config.HTTP.Addr); err != nil && err != http.ErrServerClosed {
				serverErr <- fmt.Errorf("HTTP server failed to start: %w", err)
			} else {
				serverErr <- nil
			}
		}()

		// 等待关闭信号或启动错误
		select {
		case err := <-serverErr:
			if err != nil {
				return err
			}
			// 服务器正常启动，等待关闭信号
			<-app.errGroupCtx.Done()
			app.logger.Info("Shutting down HTTP server...")

			shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := app.echoServer.Shutdown(shutdownCtx); err != nil {
				return fmt.Errorf("HTTP server shutdown error: %w", err)
			}
			return nil

		case <-app.errGroupCtx.Done():
			// 收到关闭信号但服务器还未启动完成
			app.logger.Info("Shutting down HTTP server before startup complete...")
			return app.errGroupCtx.Err()
		}
	})

	// 2. 设备轮询器管理goroutine
	app.errGroup.Go(func() error {
		app.logger.Info("Starting device pollers with errgroup management...")

		// 启动所有设备轮询器
		if err := app.dispatchTask.Start(); err != nil {
			return fmt.Errorf("failed to start device pollers: %w", err)
		}

		app.logger.Info("All device pollers started successfully")

		// 等待取消信号
		<-app.errGroupCtx.Done()

		app.logger.Info("Stopping device pollers...")
		if err := app.dispatchTask.StopAll(); err != nil {
			return fmt.Errorf("failed to stop device pollers: %w", err)
		}

		return nil
	})

	// 3. 信号处理goroutine
	app.errGroup.Go(func() error {
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

		select {
		case sig := <-quit:
			app.logger.Info("Received shutdown signal", zap.String("signal", sig.String()))
			app.cancel() // 触发优雅关闭
			return nil
		case <-app.errGroupCtx.Done():
			return app.errGroupCtx.Err()
		}
	})

	app.logger.Info("🛡️ FCC Service V2 started with errgroup - all components running safely")

	// 等待所有goroutine完成或出错
	if err := app.errGroup.Wait(); err != nil {
		app.logger.Error("Application error group failed", zap.Error(err))
		return err
	}

	app.logger.Info("FCC Service V2 shutdown completed")
	return nil
}

// startV2Components 启动 v2 组件
func (app *ApplicationV2) startV2Components() error {
	app.logger.Info("Starting V2 components")

	// 注意：dispatchTask.Start() 现在在 errGroup 中启动，这里不再重复启动

	// 🚀 启动同步监控服务（如果已初始化）
	if app.syncMonitor != nil {
		if err := app.syncMonitor.Start(app.errGroupCtx); err != nil {
			app.logger.Error("Failed to start sync monitor", zap.Error(err))
			// 监控服务启动失败不阻断主流程
		} else {
			app.logger.Info("Sync monitor started successfully")
		}
	}

	// 🚀 启动 Fuel Sync 调度器（如果已初始化）
	if app.fuelSyncScheduler != nil {
		if err := app.fuelSyncScheduler.Start(app.errGroupCtx); err != nil {
			app.logger.Error("Failed to start fuel sync scheduler", zap.Error(err))
			// Fuel sync 调度器启动失败不阻断主流程
		} else {
			app.logger.Info("Fuel sync scheduler started successfully")
		}
	}

	app.logger.Info("V2 components started successfully")
	return nil
}

// waitForShutdown 等待关闭信号 (已被errgroup替代，保留作为备用)
func (app *ApplicationV2) waitForShutdown(serverError chan error) error {
	app.logger.Warn("waitForShutdown called - this is now handled by errgroup")
	return nil
}

// shutdown 优雅关闭应用程序
func (app *ApplicationV2) shutdown() error {
	app.logger.Info("Shutting down application gracefully")

	// 🚀 停止同步监控服务
	if app.syncMonitor != nil {
		app.syncMonitor.Stop()
		app.logger.Info("Sync monitor stopped")
	}

	// 🚀 停止 Fuel Sync 调度器
	if app.fuelSyncScheduler != nil {
		app.fuelSyncScheduler.Stop()
		app.logger.Info("Fuel sync scheduler stopped")
	}

	// 停止调度任务
	if app.dispatchTask != nil {
		app.dispatchTask.Stop()
		app.logger.Info("Dispatch task stopped")
	}

	// 关闭数据库连接
	if app.database != nil {
		if err := app.database.Disconnect(context.Background()); err != nil {
			app.logger.Error("Failed to close database", zap.Error(err))
		} else {
			app.logger.Info("Database connection closed")
		}
	}

	app.logger.Info("Application shutdown completed")
	return nil
}

// HTTP 处理器实现

func (app *ApplicationV2) healthCheckHandler(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]interface{}{
		"status":        "healthy",
		"version":       "2.0.0",
		"timestamp":     time.Now(),
		"uptime":        time.Since(app.startTime).String(), // 修复：计算正确的运行时间
		"cache_type":    app.getCacheType(),
		"cache_enabled": app.cacheEnabled,
	})
}

func (app *ApplicationV2) systemStatusHandler(c echo.Context) error {
	status := app.dispatchTask.GetSystemStatus()

	// 添加缓存状态信息
	statusWithCache := map[string]interface{}{
		"dispatch_status": status,
		"cache_info": map[string]interface{}{
			"enabled": app.cacheEnabled,
			"type":    app.getCacheType(),
		},
	}

	return c.JSON(http.StatusOK, statusWithCache)
}

func (app *ApplicationV2) metricsHandler(c echo.Context) error {
	metrics := app.dispatchTask.GetSystemMetrics()
	return c.JSON(http.StatusOK, metrics)
}

func (app *ApplicationV2) cacheStatsHandler(c echo.Context) error {
	stats := map[string]interface{}{
		"enabled": app.cacheEnabled,
		"type":    app.getCacheType(),
	}

	ctx := context.Background()

	// 测试缓存连接状态
	if err := app.cache.Ping(ctx); err == nil {
		stats["status"] = "connected"
	} else {
		stats["status"] = "disconnected"
		stats["error"] = err.Error()
	}

	// 添加基本缓存信息
	stats["is_connected"] = app.cache.IsConnected()

	// 如果是 Redis 缓存，获取客户端信息
	if app.cacheEnabled && app.cache.GetClient() != nil {
		stats["client_type"] = "redis"
	} else {
		stats["client_type"] = "memory"
		// 对于内存缓存，可以在将来添加更多统计信息
		stats["note"] = "Memory cache statistics available via separate endpoint"
	}

	return c.JSON(http.StatusOK, stats)
}

func (app *ApplicationV2) getDispatchStatusHandler(c echo.Context) error {
	status := app.dispatchTask.GetSystemStatus()
	return c.JSON(http.StatusOK, status)
}

func (app *ApplicationV2) startDispatchHandler(c echo.Context) error {
	if err := app.dispatchTask.StartAll(); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}
	return c.JSON(http.StatusOK, map[string]string{"status": "dispatch started"})
}

func (app *ApplicationV2) stopDispatchHandler(c echo.Context) error {
	if err := app.dispatchTask.StopAll(); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}
	return c.JSON(http.StatusOK, map[string]string{"status": "dispatch stopped"})
}

func (app *ApplicationV2) getDispatchDevicesHandler(c echo.Context) error {
	devices := app.dispatchTask.GetAllDevices()
	deviceList := make([]map[string]interface{}, 0, len(devices))

	for deviceID, poller := range devices {
		deviceList = append(deviceList, map[string]interface{}{
			"device_id":     deviceID,
			"is_running":    poller.IsRunning(),
			"last_activity": poller.GetLastActivity(),
			"stats":         poller.GetStats(),
		})
	}

	return c.JSON(http.StatusOK, deviceList)
}

func (app *ApplicationV2) getDeviceNozzlesHandler(c echo.Context) error {
	deviceID := c.Param("deviceId")
	ctx := c.Request().Context()

	nozzles, err := app.nozzleService.GetDeviceNozzles(ctx, deviceID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}
	return c.JSON(http.StatusOK, nozzles)
}

func (app *ApplicationV2) getNozzleHandler(c echo.Context) error {
	nozzleID := c.Param("nozzleId")
	ctx := c.Request().Context()

	nozzle, err := app.nozzleService.GetNozzleByID(ctx, nozzleID)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": err.Error()})
	}
	return c.JSON(http.StatusOK, nozzle)
}

func (app *ApplicationV2) updateNozzleHandler(c echo.Context) error {
	nozzleID := c.Param("nozzleId")
	ctx := c.Request().Context()

	// 先获取现有喷嘴
	nozzle, err := app.nozzleService.GetNozzleByID(ctx, nozzleID)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": err.Error()})
	}

	// 绑定更新数据
	if err := c.Bind(nozzle); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid nozzle data"})
	}

	// 更新喷嘴
	if err := app.nozzleService.UpdateNozzle(ctx, nozzle); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, nozzle)
}

// 🚀 新增：初始化 Fuel Sync 服务
func (app *ApplicationV2) initFuelSyncServices() error {
	app.logger.Info("Initializing fuel sync services")

	// 获取 GORM DB 实例
	postgresDB, ok := app.database.(*storage.PostgreSQLDatabase)
	if !ok {
		return fmt.Errorf("failed to get GORM DB instance for fuel sync service")
	}
	gormDB := postgresDB.GetGormDB()

	// 创建 Fuel Sync 策略
	fuelSyncStrategy := fuel_sync.NewHTTPFuelSyncStrategy(
		"external_fuel_api",                             // 系统ID
		"http://192.168.8.114:8080/api/v1/oil/products", // 外部API地址
		30*time.Second,                                  // 超时时间
		"",                                              // API密钥（可选）
		app.logger,
	)

	// 创建 Fuel Sync 客户端
	fuelSyncClient := fuel_sync.NewFuelSyncClient(fuelSyncStrategy, app.logger)

	// 创建数据转换器
	converter := fuel_sync.NewFuelGradeConverter(app.logger)

	// 创建同步配置
	syncConfig := fuel_sync.DefaultSyncConfig()

	// 创建 Fuel Sync 服务
	app.fuelSyncService = fuel_sync.NewFuelGradeSyncService(
		gormDB,
		fuelSyncClient,
		converter,
		app.logger,
		syncConfig,
	)

	// 创建调度器配置
	schedulerConfig := &fuel_sync.SyncSchedulerConfig{
		SyncInterval:       1 * time.Hour,    // 每小时同步一次
		StartDelay:         30 * time.Second, // 启动延迟
		EnableScheduled:    true,             // 启用定时同步
		EnableManual:       true,             // 启用手动触发
		ManualSyncTimeout:  5 * time.Minute,  // 手动同步超时
		MaxRetries:         3,                // 最大重试次数
		RetryDelay:         5 * time.Minute,  // 重试延迟
		RetryBackoff:       true,             // 启用指数退避
		MaxRetryDelay:      30 * time.Minute, // 最大重试延迟
		StopOnError:        false,            // 遇到错误不停止
		MaxConsecutiveErrs: 10,               // 最大连续错误次数
	}

	// 创建同步调度器
	app.fuelSyncScheduler = fuel_sync.NewSyncScheduler(
		schedulerConfig,
		app.fuelSyncService,
		app.logger,
	)

	// 创建 HTTP 处理器
	app.fuelSyncHandler = fuel_sync.NewFuelSyncHandler(
		app.fuelSyncScheduler,
		app.fuelSyncService,
		app.logger,
	)

	app.logger.Info("Fuel sync services initialized successfully")
	return nil
}

// deviceStateManager 设备状态管理器 - 使用 DeviceStateMachineV2
type deviceStateManager struct {
	devices map[string]v2models.DeviceStateMachine
	logger  *zap.Logger
	mu      sync.RWMutex
}

func (dsm *deviceStateManager) RegisterDevice(info v2models.DeviceInfo) (v2models.DeviceStateMachine, error) {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()

	// 使用完整的 DeviceStateMachineV2 实现
	deviceSM := v2models.NewDeviceStateMachineV2(info, 8) // 默认8个喷嘴

	dsm.devices[info.ID] = deviceSM
	dsm.logger.Info("Device registered in state manager", zap.String("device_id", info.ID))
	return deviceSM, nil
}

func (dsm *deviceStateManager) UnregisterDevice(deviceID string) error {
	dsm.mu.Lock()
	defer dsm.mu.Unlock()
	delete(dsm.devices, deviceID)
	return nil
}

func (dsm *deviceStateManager) GetDevice(deviceID string) (v2models.DeviceStateMachine, error) {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()
	if device, exists := dsm.devices[deviceID]; exists {
		return device, nil
	}
	return nil, fmt.Errorf("device not found: %s", deviceID)
}

func (dsm *deviceStateManager) GetAllDevices() map[string]v2models.DeviceStateMachine {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()
	result := make(map[string]v2models.DeviceStateMachine)
	for k, v := range dsm.devices {
		result[k] = v
	}
	return result
}

func (dsm *deviceStateManager) RegisterNozzle(deviceID string, info v2models.NozzleInfo) (v2models.NozzleStateMachine, error) {
	dsm.mu.RLock()
	defer dsm.mu.RUnlock()

	// 获取设备状态机
	device, exists := dsm.devices[deviceID]
	if !exists {
		return nil, fmt.Errorf("device not found: %s", deviceID)
	}

	// DeviceStateMachineV2 内部管理喷嘴状态，无需额外的 NozzleStateMachine
	if deviceV2, ok := device.(v2models.DeviceStateMachineV2); ok {
		// 创建喷嘴状态数据
		nozzleState := &v2models.NozzleStateData{
			NozzleInfo: info,
			State:      v2models.NozzleStateIdle,
			StateTime:  time.Now(),
			LastUpdate: time.Now(),
		}

		// 更新设备的喷嘴状态
		if err := deviceV2.UpdateNozzleState(info.Number, nozzleState); err != nil {
			return nil, fmt.Errorf("failed to update nozzle state: %w", err)
		}

		dsm.logger.Info("Nozzle registered in state manager",
			zap.String("device_id", deviceID),
			zap.Uint8("nozzle_number", info.Number))

		// 返回 nil，因为 DeviceStateMachineV2 内部管理喷嘴状态
		return nil, nil
	}

	return nil, fmt.Errorf("device does not support nozzle state management")
}

func (dsm *deviceStateManager) UnregisterNozzle(deviceID, nozzleID string) error {
	return nil // DeviceStateMachineV2 内部管理
}

func (dsm *deviceStateManager) GetNozzle(deviceID, nozzleID string) (v2models.NozzleStateMachine, error) {
	return nil, fmt.Errorf("use DeviceStateMachineV2.GetNozzleState instead")
}

func (dsm *deviceStateManager) GetDeviceNozzles(deviceID string) map[string]v2models.NozzleStateMachine {
	return make(map[string]v2models.NozzleStateMachine) // DeviceStateMachineV2 内部管理
}

func (dsm *deviceStateManager) Subscribe(handler v2models.StateEventHandler) {
	// 可以添加全局事件处理逻辑
}

func (dsm *deviceStateManager) Unsubscribe(handler v2models.StateEventHandler) {
	// 可以添加全局事件处理逻辑
}

func (dsm *deviceStateManager) GetDeviceState(deviceID string) (*v2models.DeviceStateData, error) {
	device, err := dsm.GetDevice(deviceID)
	if err != nil {
		return nil, err
	}
	return device.GetStateData(), nil
}

func (dsm *deviceStateManager) GetNozzleState(deviceID, nozzleID string) (*v2models.NozzleStateData, error) {
	device, err := dsm.GetDevice(deviceID)
	if err != nil {
		return nil, err
	}

	if deviceV2, ok := device.(v2models.DeviceStateMachineV2); ok {
		// 简化：假设 nozzleID 就是 nozzle number (1-15)
		nozzleNum := byte(1) // 实际应该解析 nozzleID
		return deviceV2.GetNozzleState(nozzleNum)
	}

	return nil, fmt.Errorf("device does not support nozzle state management")
}

func (dsm *deviceStateManager) GetDeviceStateSummary(deviceID string) (*v2models.DeviceStateSummary, error) {
	device, err := dsm.GetDevice(deviceID)
	if err != nil {
		return nil, err
	}

	summary := &v2models.DeviceStateSummary{
		DeviceID:     deviceID,
		DeviceState:  device.GetState(),
		IsOnline:     true,
		NozzleCount:  0,
		ActiveCount:  0,
		NozzleStates: make(map[string]v2models.NozzleState),
		LastUpdate:   time.Now(),
	}

	// 如果是 DeviceStateMachineV2，获取喷嘴状态
	if deviceV2, ok := device.(v2models.DeviceStateMachineV2); ok {
		nozzleStates := deviceV2.GetNozzleStates()
		summary.NozzleCount = len(nozzleStates)

		for nozzleNum, nozzleState := range nozzleStates {
			nozzleID := fmt.Sprintf("nozzle-%d", nozzleNum)
			summary.NozzleStates[nozzleID] = nozzleState.State

			if nozzleState.State != v2models.NozzleStateIdle {
				summary.ActiveCount++
			}
		}
	}

	return summary, nil
}

// 结果处理器实现

// DatabaseResultHandler 数据库结果处理器
type DatabaseResultHandler struct {
	database storage.Database
	logger   *zap.Logger
}

func (h *DatabaseResultHandler) HandleResult(result pollingv2.PollResult) error {
	// 处理数据库轮询结果

	// 这里可以实现数据库持久化逻辑
	// 例如保存轮询结果、更新设备状态等

	return nil
}

func (h *DatabaseResultHandler) GetName() string {
	return "DatabaseResultHandler"
}

// CacheResultHandler 缓存结果处理器
type CacheResultHandler struct {
	cache  storage.Cache
	logger *zap.Logger
}

func (h *CacheResultHandler) HandleResult(result pollingv2.PollResult) error {
	ctx := context.Background()

	// 处理缓存轮询结果

	// 实际的缓存操作：存储设备最新状态
	if result.Success {
		// 缓存设备状态
		deviceStatusKey := fmt.Sprintf("device:status:%s", result.DeviceID)
		statusData := map[string]interface{}{
			"device_id":     result.DeviceID,
			"last_poll":     result.Timestamp,
			"response_time": result.ResponseTime,
			"business_type": result.BusinessType,
			"state_changed": result.StateChanged,
			"success":       result.Success,
		}

		if statusBytes, err := json.Marshal(statusData); err == nil {
			if err := h.cache.Set(ctx, deviceStatusKey, statusBytes, 5*time.Minute); err != nil {
				// 缓存设备状态失败（非关键错误）
			}
		}

		// 缓存轮询统计信息
		pollStatsKey := fmt.Sprintf("device:poll_stats:%s", result.DeviceID)
		pollStats := map[string]interface{}{
			"last_successful_poll": result.Timestamp,
			"response_time_ms":     result.ResponseTime.Milliseconds(),
			"business_type":        result.BusinessType,
		}

		if statsBytes, err := json.Marshal(pollStats); err == nil {
			if err := h.cache.Set(ctx, pollStatsKey, statsBytes, 30*time.Minute); err != nil {
				// 缓存轮询统计失败（非关键错误）
			}
		}

		// 设备轮询结果已缓存
	} else {
		// 缓存错误信息
		errorKey := fmt.Sprintf("device:error:%s", result.DeviceID)
		errorMsg := "unknown error"
		if result.Error != nil {
			errorMsg = result.Error.Error()
		}
		errorData := map[string]interface{}{
			"device_id": result.DeviceID,
			"error":     errorMsg,
			"timestamp": result.Timestamp,
		}

		if errorBytes, err := json.Marshal(errorData); err == nil {
			if err := h.cache.Set(ctx, errorKey, errorBytes, 10*time.Minute); err != nil {
				// 缓存设备错误失败（非关键错误）
			}
		}

		// 设备错误已缓存
	}

	return nil
}

func (h *CacheResultHandler) GetName() string {
	return "CacheResultHandler"
}

// StateUpdateHandler 状态更新处理器
type StateUpdateHandler struct {
	stateManager v2models.DeviceStateManager
	logger       *zap.Logger
}

func (h *StateUpdateHandler) HandleResult(result pollingv2.PollResult) error {
	// 处理状态更新

	// 更新设备状态
	if device, err := h.stateManager.GetDevice(result.DeviceID); err == nil {
		if result.Success {
			device.OnPollResponse(result.ResponseTime)
		} else {
			device.OnPollError(result.Error)
		}
	}

	return nil
}

func (h *StateUpdateHandler) GetName() string {
	return "StateUpdateHandler"
}

// BusinessLogicHandler 业务逻辑处理器
type BusinessLogicHandler struct {
	nozzleService nozzle.ServiceV2
	logger        *zap.Logger
}

func (h *BusinessLogicHandler) HandleResult(result pollingv2.PollResult) error {
	// 处理业务逻辑

	// 这里可以实现业务逻辑处理
	// 例如根据轮询结果触发业务操作、更新喷嘴状态等
	// 注意：PUMP_NOT_PROGRAMMED 的自动价格配置已经在 DevicePoller 中直接处理

	return nil
}

func (h *BusinessLogicHandler) GetName() string {
	return "BusinessLogicHandler"
}

// getCacheType 获取缓存类型用于日志
func (app *ApplicationV2) getCacheType() string {
	if app.cacheEnabled {
		return "redis"
	}
	return "memory"
}

// 🛡️ Panic处理辅助函数

// getStackTrace 获取当前的堆栈跟踪信息
func getStackTrace() string {
	return string(debug.Stack())
}

// writePanicLog 写入panic日志到文件（紧急情况备用）
func writePanicLog(panicValue interface{}) error {
	// 创建日志目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		return fmt.Errorf("failed to create logs directory: %w", err)
	}

	// 生成panic日志文件名
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	filename := fmt.Sprintf("logs/panic_%s.log", timestamp)

	// 写入panic信息
	content := fmt.Sprintf("Panic occurred at: %s\n", time.Now().Format(time.RFC3339))
	content += fmt.Sprintf("Panic value: %v\n", panicValue)
	content += fmt.Sprintf("Stack trace:\n%s\n", getStackTrace())

	return os.WriteFile(filename, []byte(content), 0644)
}

func checkGoTimezone() {
	fmt.Println("=== Go 程序时区检查 ===")
	fmt.Println("time.Local:", time.Local)
	fmt.Println("当前时间:", time.Now())
	fmt.Println("当前时间(UTC):", time.Now().UTC())
	fmt.Println("当前时间(Local):", time.Now().Local())

	// 检查环境变量
	fmt.Println("TZ 环境变量:", os.Getenv("TZ"))
}
