# FCC Service V2 API 规范文档

## 概述

FCC Service V2 基于新的架构设计，提供统一的设备管理和 Wayne DART 协议支持。

**Base URL**: `/api/v2`

## 数据类型说明

### 基础类型
- `string`: 字符串
- `int`: 整数
- `byte`: 字节 (0-255)
- `bool`: 布尔值
- `decimal`: 高精度小数 (shopspring/decimal)
- `time`: RFC3339 格式时间字符串

### 枚举类型

#### DeviceType
```
controller, fuel_controller, payment_controller, monitor_controller,
dart_line_controller, tcp_device_controller, serial_port_controller,
fuel_pump, fuel_nozzle, atg, display, pos, edc, pump,
dart_pump, dart_tank, dart_display
```

#### DeviceStatus/DeviceState
```
online, offline, error, maintenance, unknown,
initializing, ready, polling, configuring
```

#### ProtocolType
```
tcp, udp, serial, modbus, dart, wayne_dart
```

## API 端点详细说明

---

## 1. 基础系统 API

### 1.1 健康检查
```
GET /health
```

**返回**: `HealthResponse`
```json
{
  "status": "healthy",
  "version": "2.0.0", 
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime": "72h30m0s"
}
```

### 1.2 系统状态
```
GET /status
```

**返回**: `SystemStatusV2`
```json
{
  "is_running": true,
  "total_devices": 10,
  "active_devices": 8,
  "start_time": "2024-01-01T10:00:00Z",
  "uptime": 9000000000000,
  "device_statuses": {
    "device_001": {
      "device_id": "device_001",
      "is_running": true,
      "last_activity": "2024-01-01T12:00:00Z",
      "state": "online"
    }
  }
}
```

### 1.3 系统指标
```
GET /metrics
```

**返回**: `SystemMetricsV2`
```json
{
  "total_devices": 10,
  "active_devices": 8,
  "total_polls": 50000,
  "successful_polls": 49500,
  "failed_polls": 500,
  "average_latency": 15000000,
  "start_time": "2024-01-01T10:00:00Z",
  "last_update_time": "2024-01-01T12:00:00Z"
}
```

---

## 2. 设备管理 API

### 2.1 列出设备
```
GET /devices?controller_id=&station_id=&status=&type=&page=&page_size=
```

**查询参数**:
- `controller_id` (可选): 控制器ID过滤
- `station_id` (可选): 加油站ID过滤  
- `status` (可选): 状态过滤
- `type` (可选): 设备类型过滤
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认50

**返回**: `DeviceListResponse`
```json
{
  "devices": [
    {
      "id": "device_001",
      "name": "Pump 1",
      "type": "dart_pump",
      "controller_id": "ctrl_001", 
      "device_address": 80,
      "station_id": "station_001",
      "island_id": "island_A",
      "position": "A1",
      "config": {
        "timeout": 5000,
        "retry_count": 3,
        "monitor_interval": 30000,
        "protocol_config": {},
        "device_params": {},
        "extensions": {}
      },
      "status": "online",
      "health": "healthy",
      "capabilities": {
        "supported_commands": ["authorize", "reset", "stop"],
        "supported_data_types": ["fuel_data"],
        "protocol_version": "1.3",
        "features": {
          "transaction_support": true
        }
      },
      "last_seen": "2024-01-01T12:00:00Z",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T11:00:00Z"
    }
  ],
  "total": 10,
  "page": 1,
  "page_size": 50
}
```

### 2.2 创建设备
```
POST /devices
```

**入参**: `DeviceRequest`
```json
{
  "id": "device_002",
  "name": "Pump 2", 
  "type": "dart_pump",
  "controller_id": "ctrl_001",
  "device_address": 81,
  "station_id": "station_001",
  "island_id": "island_A",
  "position": "A2",
  "config": {
    "timeout": 5000,
    "retry_count": 3,
    "monitor_interval": 30000
  }
}
```

**字段验证**:
- `id`: 必填，唯一标识
- `name`: 必填，设备名称
- `type`: 必填，设备类型枚举值
- `controller_id`: 必填，关联控制器ID
- `device_address`: 必填，DART协议地址 (80-111)
- `station_id`: 必填，加油站ID

**返回**: `DeviceResponse` (同列表中的设备对象)

### 2.3 获取单个设备
```
GET /devices/{id}
```

**返回**: `DeviceResponse` (同上)

### 2.4 更新设备
```
PUT /devices/{id}
```

**入参**: `DeviceRequest` (同创建设备)

**返回**: `DeviceResponse`

### 2.5 删除设备
```
DELETE /devices/{id}
```

**返回**: `204 No Content`

### 2.6 获取设备状态
```
GET /devices/{id}/status
```

**返回**: `DeviceStatusResponse`
```json
{
  "device_id": "device_001",
  "status": "online",
  "last_seen": "2024-01-01T12:00:00Z"
}
```

### 2.7 发送设备命令
```
POST /devices/{id}/commands
```

**入参**: `CommandRequest`
```json
{
  "command_id": "cmd_123",
  "device_id": "device_001",
  "command_type": "status",
  "parameters": {
    "timeout": 30
  },
  "priority": "normal",
  "timeout": 30,
  "async": false
}
```

**返回**: `CommandResponse`
```json
{
  "command_id": "cmd_123",
  "device_id": "device_001", 
  "command_type": "status",
  "success": true,
  "data": {
    "pump_status": 2,
    "nozzle_states": [1, 0, 0, 0]
  },
  "execution_time_ms": 25,
  "submitted_at": "2024-01-01T12:00:00Z",
  "completed_at": "2024-01-01T12:00:00.025Z"
}
```

### 2.8 获取泵状态
```
GET /devices/{id}/pump/status
```

**返回**: `PumpStatusV2`
```json
{
  "device_id": "device_001",
  "pump_status": 2,
  "device_state": "online",
  "is_online": true,
  "tx_sequence": 15,
  "last_poll": "2024-01-01T12:00:00Z",
  "last_response": "2024-01-01T12:00:00.025Z",
  "poll_count": 1000,
  "error_count": 5,
  "response_time": 25000000,
  "nozzles": {
    "1": {
      "state": "idle",
      "is_out": false,
      "is_selected": false,
      "current_volume": 0,
      "current_amount": 0,
      "current_price": 6.58
    }
  }
}
```

### 2.9 获取泵累计数据
```
GET /devices/{id}/pump/totals
```

**返回**: `PumpTotalsV2`
```json
{
  "device_id": "device_001",
  "totals": {
    "1": {
      "nozzle_id": "1",
      "total_volume": 15000.5,
      "total_amount": 98703.29,
      "transaction_count": 145,
      "last_transaction_time": "2024-01-01T11:45:00Z"
    }
  }
}
```

---

## 3. 调度任务管理 API

### 3.1 获取调度状态
```
GET /dispatch/status
```

**返回**: `DispatchStatusV2`
```json
{
  "is_running": true,
  "total_devices": 10,
  "active_devices": 8,
  "start_time": "2024-01-01T10:00:00Z", 
  "uptime": 7200000000000
}
```

### 3.2 启动调度
```
POST /dispatch/start
```

**返回**:
```json
{
  "status": "dispatch started"
}
```

### 3.3 停止调度
```
POST /dispatch/stop
```

**返回**:
```json
{
  "status": "dispatch stopped"
}
```

### 3.4 获取调度设备列表
```
GET /dispatch/devices
```

**返回**: `DispatchDeviceV2[]`
```json
[
  {
    "device_id": "device_001",
    "is_running": true,
    "last_activity": "2024-01-01T12:00:00Z",
    "stats": {
      "total_polls": 1000,
      "successful_polls": 995,
      "failed_polls": 5,
      "average_response_time": 25000000,
      "last_error": ""
    }
  }
]
```

---

## 4. 喷嘴管理 API

### 4.1 获取设备所有喷嘴状态
```
GET /devices/{id}/nozzles
```

**返回**: `DeviceNozzlesResponse`
```json
{
  "device_id": "device_001",
  "device_name": "Pump Station 1",
  "total_nozzles": 4,
  "active_count": 2,
  "nozzles": [
    {
      "id": "nozzle_001",
      "nozzle_number": 1,
      "status": "filling",
      "is_out": true,
      "is_selected": true,
      "is_enabled": true,
      "current_price": 7.50,
      "current_volume": 25.5,
      "current_amount": 191.25,
      "total_volume": 1000.0,
      "total_amount": 7500.0,
      "transaction_count": 150,
      "fuel_grade_name": "95#汽油",
      "preset_volume": 30.0,
      "preset_amount": 225.0,
      "last_update": "2024-01-01T12:00:00Z"
    }
  ],
  "last_update": "2024-01-01T12:00:00Z"
}
```

### 4.2 获取单个喷嘴状态
```
GET /devices/{id}/nozzles/{number}
```

**返回**: `NozzleStatusResponse`
```json
{
  "id": "nozzle_001",
  "nozzle_number": 1,
  "status": "filling",
  "is_out": true,
  "is_selected": true,
  "is_enabled": true,
  "current_price": 7.50,
  "current_volume": 25.5,
  "current_amount": 191.25,
  "total_volume": 1000.0,
  "total_amount": 7500.0,
  "transaction_count": 150,
  "fuel_grade_name": "95#汽油",
  "preset_volume": 30.0,
  "preset_amount": 225.0,
  "last_update": "2024-01-01T12:00:00Z"
}
```

### 4.3 获取喷嘴当前交易信息
```
GET /devices/{id}/nozzles/{number}/transaction
```

**返回**: `NozzleTransactionResponse`
```json
{
  "id": "nozzle_001",
  "nozzle_number": 1,
  "transaction_id": "txn_001",
  "status": "filling",
  "start_time": "2024-01-01T11:45:00Z",
  "current_volume": 25.5,
  "current_amount": 191.25,
  "unit_price": 7.50,
  "preset_volume": 30.0,
  "preset_amount": 225.0,
  "duration": "15m0s",
  "is_active": true
}
```

### 4.4 获取喷嘴统计信息
```
GET /devices/{id}/nozzles/{number}/stats
```

**返回**: `NozzleStatsResponse`
```json
{
  "id": "nozzle_001",
  "nozzle_number": 1,
  "total_volume": 15000.5,
  "total_amount": 112503.75,
  "transaction_count": 150,
  "average_volume": 100.0,
  "average_amount": 750.03,
  "last_transaction": "2024-01-01T11:45:00Z",
  "utilization": 0.75
}
```

**字段说明**:
- `id`: 喷嘴唯一标识符
- `nozzle_number`: 喷嘴编号 (1-15，Wayne协议限制)
- `status`: 状态枚举 (idle, selected, authorized, out, filling, completed, suspended, error, maintenance)
- `is_out`: 油枪是否拔出
- `is_selected`: 喷嘴是否被选中
- `current_price`: 当前单价 (decimal)
- `current_volume`: 当前体积 (decimal)
- `current_amount`: 当前金额 (decimal)
- `total_volume`: 累计体积 (decimal)
- `total_amount`: 累计金额 (decimal)
- `transaction_count`: 交易次数
- `utilization`: 使用率 (0-1)

---

## 5. Wayne DART 协议 API

所有 Wayne 命令都返回 `WayneCommandResponse`:

```json
{
  "command_id": "wayne_authorize_1640995200000",
  "device_id": "device_001",
  "command": "authorize",
  "success": true,
  "data": {
    "message": "Wayne DART command executed successfully",
    "device_id": "device_001",
    "command": "authorize",
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "execution_time_ms": 25,
  "submitted_at": "2024-01-01T12:00:00Z",
  "completed_at": "2024-01-01T12:00:00.025Z",
  "protocol_info": {
    "protocol": "Wayne DART v1.3",
    "transaction_type": "CD1", 
    "transaction_code": "0x06",
    "response_time_ms": 25
  }
}
```

### Wayne 命令基础结构
所有 Wayne 命令请求都包含以下基础字段:
```json
{
  "command_id": "cmd_123",
  "device_id": "device_001",
  "command": "authorize",
  "priority": "normal",
  "timeout": 30,
  "async": false
}
```

### 5.1 授权命令 (CD1: 0x06)
```
POST /wayne/authorize
```

**入参**: `AuthorizeRequest`
```json
{
  "device_id": "device_001",
  "command": "authorize"
}
```

### 5.2 复位命令 (CD1: 0x05)
```
POST /wayne/reset
```

**入参**: `ResetRequest`
```json
{
  "device_id": "device_001",
  "command": "reset"
}
```

### 5.3 停止命令 (CD1: 0x08)
```
POST /wayne/stop
```

**入参**: `StopRequest`
```json
{
  "device_id": "device_001",
  "command": "stop"
}
```

### 5.4 返回状态命令 (CD1: 0x00)
```
POST /wayne/status
```

**入参**: `ReturnStatusRequest`
```json
{
  "device_id": "device_001",
  "command": "return_status"
}
```

### 5.5 返回填充信息命令 (CD1: 0x04)
```
POST /wayne/filling-info
```

**入参**: `ReturnFillingInfoRequest`
```json
{
  "device_id": "device_001",
  "command": "return_filling_info"
}
```

### 5.6 配置喷嘴命令 (CD2)
```
POST /wayne/configure-nozzles
```

**入参**: `ConfigureNozzlesRequest`
```json
{
  "device_id": "device_001",
  "command": "configure_nozzles",
  "nozzle_numbers": [1, 2, 3, 4]
}
```

**验证**: `nozzle_numbers` 数组，每个元素 1-8

### 5.7 预设体积命令 (CD3)
```
POST /wayne/preset-volume
```

**入参**: `PresetVolumeRequest`
```json
{
  "device_id": "device_001",
  "command": "preset_volume",
  "volume": "50.00",
  "decimals": 2
}
```

**验证**: 
- `volume`: 必须 > 0，decimal 类型
- `decimals`: 0-3

### 5.8 预设金额命令 (CD4)
```
POST /wayne/preset-amount
```

**入参**: `PresetAmountRequest`
```json
{
  "device_id": "device_001",
  "command": "preset_amount",
  "amount": "100.00",
  "decimals": 2
}
```

**验证**:
- `amount`: 必须 > 0，decimal 类型
- `decimals`: 0-3

### 5.9 价格更新命令 (CD5)
```
POST /wayne/update-prices
```

**入参**: `UpdatePricesRequest`
```json
{
  "device_id": "device_001",
  "command": "update_prices",
  "prices": [
    {
      "nozzle_number": 1,
      "price": "6.58",
      "decimals": 2
    },
    {
      "nozzle_number": 2, 
      "price": "6.68",
      "decimals": 2
    }
  ]
}
```

**验证**:
- `nozzle_number`: 1-8
- `price`: 必须 > 0，decimal 类型
- `decimals`: 0-4

### 5.10 暂停喷嘴命令 (CD14)
```
POST /wayne/suspend-nozzle
```

**入参**: `SuspendNozzleRequest`
```json
{
  "device_id": "device_001",
  "command": "suspend_nozzle",
  "nozzle_number": 1
}
```

**验证**: `nozzle_number`: 1-8

### 5.11 恢复喷嘴命令 (CD15)
```
POST /wayne/resume-nozzle
```

**入参**: `ResumeNozzleRequest`
```json
{
  "device_id": "device_001",
  "command": "resume_nozzle", 
  "nozzle_number": 1
}
```

**验证**: `nozzle_number`: 1-8

### 5.12 请求计数器命令 (CD101)
```
POST /wayne/request-counters
```

**入参**: `RequestCountersRequest`
```json
{
  "device_id": "device_001",
  "command": "request_counters",
  "counter_type": "amount_total"
}
```

**counter_type 枚举值**:
- 体积计数器: `volume_nozzle_1` 到 `volume_nozzle_8`, `volume_total`
- 金额计数器: `amount_nozzle_1` 到 `amount_nozzle_8`, `amount_total`

---

## 错误处理

### 标准错误响应
```json
{
  "error": "Device not found",
  "code": "DEVICE_NOT_FOUND",
  "details": {
    "device_id": "device_001"
  }
}
```

### HTTP 状态码
- `200`: 成功
- `201`: 创建成功
- `204`: 删除成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### DART 协议约束
- 设备地址范围: 0x50-0x6F (80-111)
- 响应超时: ≤ 25ms
- 支持波特率: 9600, 19200
- 喷嘴编号: 1-8 (Wayne 协议限制)
- 小数位数限制: 体积/金额 0-3 位，价格 0-4 位

---

## 版本信息
- API 版本: v2.0.0
- 协议支持: Wayne DART v1.3, 泵接口协议 v2.1
- 文档更新: 2024-01-01 