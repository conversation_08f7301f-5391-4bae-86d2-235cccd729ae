# Wayne DART Protocol Commands API Guide

## 🚀 概述

这是新设计的Wayne DART协议命令API，使用**类型安全的DTO结构**替代了复杂的通用参数解析，大大简化了对接难度。

## ✨ 新设计优势

### 1. 类型安全
- 每个命令都有专门的DTO结构体
- 编译时类型检查，减少运行时错误
- IDE智能提示，提高开发效率

### 2. 接口清晰
- 每个命令有独立的端点
- 参数结构明确，带有详细注释
- 请求/响应格式统一

### 3. 易于维护
- 避免复杂的switch语句
- 参数验证集中在DTO层
- 错误处理统一

## 📋 支持的命令

| 命令类型 | 协议 | 端点 | 用途 |
|---------|------|------|------|
| 授权 | CD1(0x06) | `POST /api/v2/wayne/authorize` | 授权设备开始加油 |
| 复位 | CD1(0x05) | `POST /api/v2/wayne/reset` | 复位设备到初始状态 |
| 停止 | CD1(0x08) | `POST /api/v2/wayne/stop` | 停止当前加油操作 |
| 状态查询 | CD1(0x00) | `POST /api/v2/wayne/status` | 查询设备当前状态 |
| 填充信息 | CD1(0x04) | `POST /api/v2/wayne/filling-info` | 查询当前加油信息 |
| 配置喷嘴 | CD2 | `POST /api/v2/wayne/configure-nozzles` | 配置允许的喷嘴号 |
| 预设体积 | CD3 | `POST /api/v2/wayne/preset-volume` | 设置体积预设值 |
| 预设金额 | CD4 | `POST /api/v2/wayne/preset-amount` | 设置金额预设值 |
| 价格更新 | CD5 | `POST /api/v2/wayne/update-prices` | 更新喷嘴价格 |
| 暂停喷嘴 | CD14 | `POST /api/v2/wayne/suspend-nozzle` | 暂停指定喷嘴 |
| 恢复喷嘴 | CD15 | `POST /api/v2/wayne/resume-nozzle` | 恢复指定喷嘴 |
| 请求计数器 | CD101 | `POST /api/v2/wayne/request-counters` | 查询计数器数据 |

## 🔧 API使用示例

### 1. 授权命令 (CD1: 0x06)

```bash
curl -X POST http://localhost:8080/api/v2/wayne/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "authorize",
    "priority": "high",
    "timeout": 30,
    "async": false
  }'
```

**响应示例：**
```json
{
  "command_id": "wayne_authorize_1699123456789",
  "device_id": "pump_001",
  "command": "authorize",
  "success": true,
  "data": {
    "message": "Wayne DART command executed successfully",
    "device_id": "pump_001",
    "command": "authorize",
    "timestamp": "2023-11-04T10:30:56Z"
  },
  "execution_time_ms": 25,
  "submitted_at": "2023-11-04T10:30:56Z",
  "completed_at": "2023-11-04T10:30:56Z",
  "protocol_info": {
    "protocol": "Wayne DART v1.3",
    "transaction_type": "CD1",
    "transaction_code": "0x06",
    "response_time_ms": 25
  }
}
```

### 2. 配置喷嘴 (CD2)

```bash
curl -X POST http://localhost:8080/api/v2/wayne/configure-nozzles \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "configure_nozzles",
    "nozzle_numbers": [1, 2, 3],
    "priority": "normal",
    "async": false
  }'
```

### 3. 预设体积 (CD3)

```bash
curl -X POST http://localhost:8080/api/v2/wayne/preset-volume \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "preset_volume",
    "volume": "50.00",
    "decimals": 2,
    "priority": "normal",
    "async": false
  }'
```

### 4. 预设金额 (CD4)

```bash
curl -X POST http://localhost:8080/api/v2/wayne/preset-amount \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "preset_amount",
    "amount": "100.00",
    "decimals": 2,
    "priority": "normal",
    "async": false
  }'
```

### 5. 价格更新 (CD5)

```bash
curl -X POST http://localhost:8080/api/v2/wayne/update-prices \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "update_prices",
    "prices": [
      {
        "nozzle_number": 1,
        "price": "6.58",
        "decimals": 2
      },
      {
        "nozzle_number": 2,
        "price": "6.65",
        "decimals": 2
      }
    ],
    "priority": "normal",
    "async": false
  }'
```

### 6. 暂停/恢复喷嘴 (CD14/CD15)

```bash
# 暂停喷嘴
curl -X POST http://localhost:8080/api/v2/wayne/suspend-nozzle \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "suspend_nozzle",
    "nozzle_number": 1,
    "priority": "high",
    "async": false
  }'

# 恢复喷嘴
curl -X POST http://localhost:8080/api/v2/wayne/resume-nozzle \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "resume_nozzle",
    "nozzle_number": 1,
    "priority": "high",
    "async": false
  }'
```

### 7. 请求计数器 (CD101)

```bash
curl -X POST http://localhost:8080/api/v2/wayne/request-counters \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "pump_001",
    "command": "request_counters",
    "counter_type": "amount_total",
    "priority": "normal",
    "async": false
  }'
```

**可用的计数器类型：**
- `volume_nozzle_1` - `volume_nozzle_8`: 喷嘴1-8体积计数器
- `volume_total`: 总体积计数器
- `amount_nozzle_1` - `amount_nozzle_8`: 喷嘴1-8金额计数器
- `amount_total`: 总金额计数器

## 📝 DTO结构说明

### 基础请求结构

```go
type WayneCommandRequest struct {
    CommandID string           `json:"command_id,omitempty"`           // 命令ID（可选，系统自动生成）
    DeviceID  string           `json:"device_id" validate:"required"`  // 设备ID（必需）
    Command   WayneCommandType `json:"command" validate:"required"`    // 命令类型（必需）
    Priority  string           `json:"priority,omitempty"`             // 优先级：high/normal/low
    Timeout   int              `json:"timeout,omitempty"`              // 超时时间（秒）
    Async     bool             `json:"async,omitempty"`                // 是否异步执行
}
```

### 专用命令结构

#### 配置喷嘴请求
```go
type ConfigureNozzlesRequest struct {
    WayneCommandRequest
    NozzleNumbers []int `json:"nozzle_numbers" validate:"required,min=1,max=8"` // 喷嘴号列表 (1-8)
}
```

#### 预设体积请求
```go
type PresetVolumeRequest struct {
    WayneCommandRequest
    Volume   decimal.Decimal `json:"volume" validate:"required"`   // 预设体积 (升)
    Decimals int             `json:"decimals,omitempty"`           // 小数位数 (0-3)
}
```

#### 预设金额请求
```go
type PresetAmountRequest struct {
    WayneCommandRequest
    Amount   decimal.Decimal `json:"amount" validate:"required"`   // 预设金额
    Decimals int             `json:"decimals,omitempty"`           // 小数位数 (0-3)
}
```

#### 价格更新请求
```go
type UpdatePricesRequest struct {
    WayneCommandRequest
    Prices []NozzlePriceInfo `json:"prices" validate:"required,min=1,max=8"` // 价格列表
}

type NozzlePriceInfo struct {
    NozzleNumber int             `json:"nozzle_number" validate:"required,min=1,max=8"` // 喷嘴号 (1-8)
    Price        decimal.Decimal `json:"price" validate:"required"`                     // 单价
    Decimals     int             `json:"decimals,omitempty"`                            // 小数位数 (0-4)
}
```

## 🔄 新旧对比

### 旧设计（复杂）
```go
// 复杂的通用参数解析
func (h *CommandEchoHandlerV2) executeWayneCommandDirect(devicePoller *v2.DevicePoller, req *dto.CommandRequest) error {
    switch req.CommandType {
    case "preset_volume":
        if volume, ok := req.Parameters["volume"].(float64); ok {
            volumeDecimal := decimal.NewFromFloat(volume)
            return devicePoller.PresetVolume(volumeDecimal)
        }
        return fmt.Errorf("invalid volume parameter")
    // ... 更多复杂的类型转换
    }
}
```

### 新设计（简洁）
```go
// 类型安全的专用处理器
func (h *WayneCommandHandler) ExecutePresetVolume(c echo.Context) error {
    var req dto.PresetVolumeRequest
    if err := c.Bind(&req); err != nil {
        return echo.NewHTTPError(http.StatusBadRequest, "Invalid preset volume request: "+err.Error())
    }
    
    if err := req.Validate(); err != nil {
        return echo.NewHTTPError(http.StatusBadRequest, err.Error())
    }
    
    return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
        return devicePoller.PresetVolume(req.Volume) // 直接使用类型安全的字段
    })
}
```

## 🎯 最佳实践

### 1. 参数验证
- 所有DTO都有内置的验证方法
- 参数类型和范围在编译时检查
- 错误信息清晰明确

### 2. 错误处理
- 统一的错误响应格式
- 详细的错误信息和错误码
- 协议级别的错误追踪

### 3. 性能优化
- 避免反射和类型断言
- 预编译的参数验证
- 25ms内的响应时间保证

### 4. 可维护性
- 每个命令独立的处理逻辑
- 清晰的代码结构
- 统一的测试模式

## 🔧 集成指南

### 1. 添加路由
```go
import "fcc-service/internal/server"

func main() {
    e := echo.New()
    
    // 设置Wayne专用路由
    server.SetupWayneRoutes(e, dispatchTask, deviceManager, logger)
    
    e.Start(":8080")
}
```

### 2. 扩展新命令
```go
// 1. 添加新的DTO结构
type NewCommandRequest struct {
    WayneCommandRequest
    CustomParam string `json:"custom_param" validate:"required"`
}

// 2. 添加验证方法
func (req *NewCommandRequest) Validate() error {
    if err := req.WayneCommandRequest.Validate(); err != nil {
        return err
    }
    // 自定义验证逻辑
    return nil
}

// 3. 添加处理器方法
func (h *WayneCommandHandler) ExecuteNewCommand(c echo.Context) error {
    var req dto.NewCommandRequest
    if err := c.Bind(&req); err != nil {
        return echo.NewHTTPError(http.StatusBadRequest, "Invalid request: "+err.Error())
    }
    
    if err := req.Validate(); err != nil {
        return echo.NewHTTPError(http.StatusBadRequest, err.Error())
    }
    
    return h.executeWayneCommand(c, &req.WayneCommandRequest, func(devicePoller *v2.DevicePoller) error {
        return devicePoller.NewCommand(req.CustomParam)
    })
}
```

## 🚀 总结

新的Wayne命令API设计提供了：

1. **类型安全**：编译时类型检查，减少运行时错误
2. **接口清晰**：每个命令有明确的参数结构和文档
3. **易于维护**：避免复杂的参数解析逻辑
4. **性能优化**：满足DART协议25ms响应时间要求
5. **可扩展性**：新命令可以轻松添加

这大大简化了对接难度，提高了开发效率和代码质量。 