# FCC Wayne DART 泵码数据 API 文档 v1.0

## 概述

本文档描述了FCC系统中用于访问和查询Wayne DART协议交易泵码数据的API端点。泵码数据包括交易开始和结束时的累计计数器读数，用于验证交易数据的准确性和完整性。

## 基础URL
```
/api/v1/transactions
```

## 认证
所有API端点都需要适当的认证和授权。

---

## API 端点

### 1. 获取交易列表（包含泵码过滤器）

**端点**: `GET /api/v1/transactions`

**描述**: 获取交易列表，支持泵码数据相关的过滤条件

**查询参数**:

#### 基础过滤参数
- `device_id` (string, optional): 设备ID
- `nozzle_id` (string, optional): 喷嘴ID  
- `status` (string, optional): 交易状态
- `start_time` (string, optional): 开始时间 (RFC3339格式)
- `end_time` (string, optional): 结束时间 (RFC3339格式)
- `page` (int, optional): 页码 (默认: 1)
- `limit` (int, optional): 每页数量 (默认: 20, 最大: 100)

#### 泵码过滤参数 (任务7新增)
- `pump_reading_source` (string, optional): 泵码数据来源
  - 可选值: `DC101`, `MANUAL`, `CALCULATED`, `ESTIMATED`
- `pump_reading_quality` (string, optional): 泵码数据质量
  - 可选值: `good`, `poor`, `bad`, `missing`
- `pump_reading_validated` (boolean, optional): 泵码是否已验证
  - 可选值: `true`, `false`
- `max_pump_discrepancy` (decimal, optional): 最大泵码差异 (升)
- `has_pump_readings` (boolean, optional): 是否包含泵码数据
  - 可选值: `true`, `false`

**响应示例**:
```json
{
  "transactions": [
    {
      "id": "tx-001",
      "device_id": "device-com7-pump01",
      "nozzle_id": "nozzle-001",
      "status": "completed",
      "actual_volume": "50.500",
      "actual_amount": "378.75",
      "unit_price": "7.50",
      "start_pump_volume_reading": "1000.000",
      "end_pump_volume_reading": "1050.500",
      "start_pump_amount_reading": "5000.00",
      "end_pump_amount_reading": "5378.75",
      "pump_reading_source": "DC101",
      "pump_reading_quality": "good",
      "pump_reading_validated": true,
      "pump_reading_discrepancy": "0.000",
      "started_at": "2024-06-25T10:00:00Z",
      "completed_at": "2024-06-25T10:05:30Z"
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "has_more": true
  }
}
```

### 2. 获取交易泵码详情

**端点**: `GET /api/v1/transactions/{id}/pump-readings`

**描述**: 获取指定交易的详细泵码数据和分析

**路径参数**:
- `id` (string, required): 交易ID

**响应示例**:
```json
{
  "transaction_id": "tx-001",
  "pump_readings": {
    "source": "DC101",
    "quality": "good",
    "validated": true,
    "has_complete_data": true,
    "start_readings": {
      "volume": "1000.000",
      "amount": "5000.00"
    },
    "end_readings": {
      "volume": "1050.500",
      "amount": "5378.75"
    },
    "calculated": {
      "volume": "50.500",
      "amount": "378.75"
    },
    "discrepancy": {
      "volume": "0.000",
      "within_tolerance": true
    },
    "validation_status": "passed"
  }
}
```

### 3. 获取有泵码问题的交易

**端点**: `GET /api/v1/transactions/pump-issues`

**描述**: 获取存在泵码数据问题的交易列表及分析

**查询参数**:
- 支持所有标准交易过滤参数
- 如果未指定`pump_reading_quality`，默认筛选质量为`poor`的交易

**响应示例**:
```json
{
  "transactions": [
    {
      "transaction": {
        "id": "tx-002",
        "device_id": "device-com7-pump01",
        "status": "completed",
        "pump_reading_quality": "poor",
        "pump_reading_validated": false
      },
      "pump_analysis": {
        "has_readings": true,
        "quality": "poor",
        "source": "DC101",
        "validated": false,
        "completeness": {
          "has_start_volume": true,
          "has_end_volume": true,
          "has_start_amount": true,
          "has_end_amount": false
        },
        "consistency": {
          "calculated_volume": "45.250",
          "actual_volume": "45.500",
          "volume_discrepancy": "0.250",
          "discrepancy_percentage": "0.55%"
        },
        "issues": [
          "质量问题: poor",
          "未验证",
          "差异超出容差"
        ],
        "has_issues": true
      }
    }
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 20,
    "has_more": true
  },
  "summary": {
    "total_transactions": 25,
    "issues_breakdown": {
      "quality_issues": 15,
      "unvalidated": 8,
      "missing_data": 3,
      "high_discrepancy": 12
    },
    "quality_breakdown": {
      "poor": 15,
      "bad": 5,
      "good": 3,
      "unknown": 2
    },
    "source_breakdown": {
      "DC101": 20,
      "MANUAL": 3,
      "ESTIMATED": 2
    },
    "average_discrepancy": "0.125"
  }
}
```

---

## 数据模型

### Transaction (扩展)

交易模型现在包含以下泵码相关字段：

```json
{
  "start_pump_volume_reading": "1000.000",    // 起始体积泵码 (decimal, nullable)
  "end_pump_volume_reading": "1050.500",      // 结束体积泵码 (decimal, nullable)  
  "start_pump_amount_reading": "5000.00",     // 起始金额泵码 (decimal, nullable)
  "end_pump_amount_reading": "5378.75",       // 结束金额泵码 (decimal, nullable)
  "pump_reading_source": "DC101",             // 泵码数据来源 (string)
  "pump_reading_quality": "good",             // 泵码数据质量 (string)
  "pump_reading_validated": true,             // 泵码是否已验证 (boolean)
  "pump_reading_discrepancy": "0.000"         // 体积差异值 (decimal, nullable)
}
```

### 泵码数据来源 (Pump Reading Source)

- `DC101`: Wayne DART协议DC101累计计数器
- `MANUAL`: 手动输入
- `CALCULATED`: 系统计算
- `ESTIMATED`: 估算值

### 泵码数据质量 (Pump Reading Quality)

- `good`: 数据完整且一致性良好
- `poor`: 数据完整但一致性较差  
- `bad`: 数据不完整或一致性很差
- `missing`: 缺少泵码数据

---

## 错误响应

### 标准错误格式
```json
{
  "error": "error_code",
  "message": "Human readable error message"
}
```

### 常见错误代码

- `invalid_parameters`: 请求参数无效
- `transaction_not_found`: 交易不存在
- `internal_error`: 服务器内部错误

---

## 使用示例

### 查询特定设备的高质量泵码交易
```bash
GET /api/v1/transactions?device_id=device-com7-pump01&pump_reading_quality=good&pump_reading_validated=true
```

### 查询有泵码差异问题的交易
```bash
GET /api/v1/transactions?max_pump_discrepancy=0.05&has_pump_readings=true
```

### 获取泵码问题汇总
```bash
GET /api/v1/transactions/pump-issues?start_time=2024-06-01T00:00:00Z&end_time=2024-06-25T23:59:59Z
```

---

## 版本历史

- **v1.0** (2024-06-25): 初始版本，包含泵码数据查询功能
  - 新增泵码过滤参数
  - 新增泵码详情API
  - 新增泵码问题分析API

---

## 注意事项

1. **精度**: 所有decimal字段使用字符串格式返回，保证精度
2. **空值**: 泵码字段可能为null，表示数据不可用
3. **容差**: 默认体积差异容差为10毫升(0.01升)
4. **性能**: 大量数据查询时建议使用分页和时间范围限制
5. **缓存**: 泵码数据分析结果可能被缓存，更新可能有延迟 