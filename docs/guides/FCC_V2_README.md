# FCC Service V2 版本

## 概述

FCC Service V2 是基于全新 v2 架构设计的前庭控制系统，专门针对 Wayne DART 协议进行了优化。V2 版本采用了更现代的轮询调度器设计，提供了更好的性能和可扩展性。

## 主要特性

### v2 架构核心组件

1. **DispatchTask** - 调度任务管理器
   - 统一管理所有设备的轮询任务
   - 支持优先级命令队列
   - 集成 Wayne DART 协议通信

2. **DevicePoller** - 设备轮询器  
   - 独立的设备轮询逻辑
   - 业务状态跟踪
   - 集成喷嘴状态管理

3. **Wayne Communication Manager** - 通信管理器
   - 串口/TCP 通信抽象
   - 连接池管理
   - 重试机制

4. **Nozzle Service V2** - 喷嘴服务
   - 专为 Wayne DART 协议设计
   - DC2/DC3 事务处理
   - 实时状态同步

5. **State Manager** - 状态管理器
   - 设备状态机
   - 喷嘴状态机
   - 事件发布/订阅

## 快速开始

### 1. 编译和运行

```bash
# 编译 V2 版本
go build -o fcc-v2.exe main_v2.go

# 运行服务
./fcc-v2.exe -config configs/config.yaml
```

### 2. 配置文件

使用标准的 `configs/config.yaml` 配置文件，V2 版本会使用以下配置项：

```yaml
# V2 版本特别关注的配置项
business:
  pump:
    max_concurrent_operations: 50  # DispatchTask 最大设备数

dart:
  protocol:
    response_timeout: 25ms  # DART 协议响应超时
    max_retries: 3         # 最大重试次数

monitoring:
  enabled: true  # 启用指标收集
```

### 3. API 接口

V2 版本提供专门的 API 端点：

#### 系统状态
```bash
GET /api/v2/health         # 健康检查
GET /api/v2/status         # 系统状态
GET /api/v2/metrics        # 监控指标
```

#### 设备管理
```bash
GET /api/v2/devices                      # 设备列表
GET /api/v2/devices/{deviceId}           # 设备详情
GET /api/v2/devices/{deviceId}/status    # 设备状态
POST /api/v2/devices/{deviceId}/commands # 发送命令
```

#### 调度任务管理
```bash
GET /api/v2/dispatch/status    # 调度状态
POST /api/v2/dispatch/start    # 启动调度
POST /api/v2/dispatch/stop     # 停止调度
GET /api/v2/dispatch/devices   # 调度设备列表
```

#### 喷嘴管理
```bash
GET /api/v2/nozzles/device/{deviceId}  # 设备喷嘴列表
GET /api/v2/nozzles/{nozzleId}         # 喷嘴详情
PUT /api/v2/nozzles/{nozzleId}         # 更新喷嘴
```

## V2 架构优势

### 1. 性能提升
- **统一轮询调度**：DispatchTask 统一管理所有设备轮询，避免资源竞争
- **优先级队列**：支持高优先级命令优先处理
- **连接池管理**：复用连接，减少连接开销

### 2. Wayne DART 协议优化
- **专业化设计**：针对 Wayne DART 协议专门优化
- **TX 序列号管理**：自动管理 DART 协议 TX# 序列
- **DC 事务处理**：完整支持 DC1-DC101 事务解析

### 3. 状态管理
- **设备状态机**：完整的设备生命周期管理
- **喷嘴状态机**：精确的喷嘴状态跟踪
- **事件驱动**：基于事件的状态更新机制

### 4. 可扩展性
- **模块化设计**：各组件独立，易于扩展
- **结果处理器**：支持多种结果处理策略
- **通信抽象**：支持多种传输方式（串口/TCP）

## 示例使用场景

### 1. 设备初始化和启动

```bash
# 1. 启动 FCC V2 服务
./fcc-v2.exe -config configs/config.yaml

# 2. 检查系统状态
curl http://localhost:8080/api/v2/health

# 3. 查看已注册的设备
curl http://localhost:8080/api/v2/devices

# 4. 启动设备轮询
curl -X POST http://localhost:8080/api/v2/dispatch/start
```

### 2. 设备命令发送

```bash
# 发送授权命令
curl -X POST http://localhost:8080/api/v2/devices/device-001/commands \
  -H "Content-Type: application/json" \
  -d '{
    "type": "configure",
    "business_type": "authorize",
    "priority": 1,
    "data": [1, 1, 3],
    "timeout": "25ms"
  }'

# 发送预设体积命令
curl -X POST http://localhost:8080/api/v2/devices/device-001/commands \
  -H "Content-Type: application/json" \
  -d '{
    "type": "configure", 
    "business_type": "preset",
    "priority": 2,
    "data": [3, 4, 20, 0, 0, 0],
    "timeout": "25ms"
  }'
```

### 3. 监控和状态查询

```bash
# 查看调度任务状态
curl http://localhost:8080/api/v2/dispatch/status

# 查看设备轮询统计
curl http://localhost:8080/api/v2/devices/device-001/status

# 查看系统指标
curl http://localhost:8080/api/v2/metrics
```

## 与 V1 版本的区别

| 特性 | V1 版本 | V2 版本 |
|------|---------|---------|
| 轮询架构 | 分散式轮询 | 统一调度器 |
| 协议支持 | 通用适配器 | Wayne DART 专用 |
| 状态管理 | 简单状态存储 | 完整状态机 |
| 命令处理 | 同步处理 | 优先级队列 |
| 喷嘴管理 | 基础 CRUD | 实时状态同步 |
| 性能优化 | 一般 | 高度优化 |

## 开发和调试

### 日志级别
V2 版本支持详细的日志输出：

```yaml
logging:
  level: debug  # 开发时使用 debug 级别
  format: json  # 生产环境使用 json 格式
```

### 调试端点
```bash
# 查看所有轮询器状态
curl http://localhost:8080/api/v2/dispatch/devices

# 查看具体设备的轮询统计
curl http://localhost:8080/api/v2/devices/{deviceId}/status
```

## 部署注意事项

### 1. 数据库要求
- PostgreSQL 12+ 
- 支持 GORM 自动迁移
- 建议配置连接池

### 2. 串口配置
- 确保串口设备可访问
- 配置正确的波特率（9600/19200）
- Wayne DART 地址范围：0x50-0x6F

### 3. 性能调优
- 根据设备数量调整 `max_devices`
- 调整轮询间隔和超时时间
- 启用指标收集监控性能

## 故障排除

### 常见问题

1. **设备连接失败**
   - 检查串口配置
   - 验证设备地址范围
   - 查看通信日志

2. **轮询超时**
   - 调整 DART 协议超时时间
   - 检查设备响应能力
   - 验证通信质量

3. **状态同步问题**
   - 检查状态管理器日志
   - 验证事件处理器
   - 确认数据库连接

## 贡献指南

V2 版本采用模块化设计，便于扩展：

1. **添加新的结果处理器**：实现 `ResultHandler` 接口
2. **扩展通信方式**：实现 `TransportInterface` 接口  
3. **自定义状态机**：实现 `DeviceStateMachine` 接口
4. **添加业务逻辑**：在相应的处理器中添加逻辑

## 许可证

与主项目相同的许可证。 