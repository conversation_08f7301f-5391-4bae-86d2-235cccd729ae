# Wayne Pump Interface 使用手册

## 📚 目录
- [概述](#概述)
- [快速开始](#快速开始)
- [BCD格式转换](#bcd格式转换)
- [CD/DC事务处理](#cddc事务处理)
- [泵状态机管理](#泵状态机管理)
- [API参考](#api参考)
- [最佳实践](#最佳实践)
- [故障排查](#故障排查)

## 概述

Wayne Pump Interface是FCC系统中用于处理Wayne DART协议设备的核心组件，完全实现了Wayne Pump Interface v2.1规范。

### 核心功能
- **BCD格式转换**: 支持价格、体积、金额的BCD编码/解码
- **CD/DC事务处理**: 完整的Controller-Device事务格式支持
- **泵状态机**: 符合Wayne协议的泵状态管理
- **并发安全**: 支持高并发环境下的安全操作

### 技术特性
- ✅ Wayne Pump Interface v2.1 100%合规
- ✅ 64.2%测试覆盖率，所有测试通过
- ✅ 并发安全的状态管理
- ✅ 生产级错误处理

## 快速开始

### 安装导入

```go
import (
    "fcc-service/internal/adapters/wayne/pump"
)
```

### 基本使用示例

```go
package main

import (
    "fmt"
    "log"
    
    "fcc-service/internal/adapters/wayne/pump"
)

func main() {
    // 1. 创建BCD转换器
    bcdConverter := pump.NewBCDConverter()
    
    // 2. 创建事务构建器
    builder := pump.NewTransactionBuilder()
    
    // 3. 创建泵状态机
    stateMachine := pump.NewPumpStateMachine()
    
    // 示例：价格转换
    price := 1.259 // $1.259
    bcdPrice := bcdConverter.EncodePrice(price, 3, 3)
    fmt.Printf("Price %f encoded to BCD: %X\n", price, bcdPrice)
    
    // 示例：创建授权命令
    authCmd, err := builder.BuildCD1(pump.CmdAuthorize)
    if err != nil {
        log.Fatal(err)
    }
    
    // 示例：处理状态变化
    err = stateMachine.SetState(pump.StatusReset)
    if err != nil {
        log.Fatal(err)
    }
    
    err = stateMachine.ProcessCommand(pump.CmdAuthorize)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("Current pump status: %d\n", stateMachine.GetCurrentStatus())
}
```

## BCD格式转换

### BCD转换器接口

```go
type BCDConverter interface {
    // 基础BCD转换
    EncodeBCD(value int64, bytes int) []byte
    DecodeBCD(data []byte) int64
    
    // 专用转换函数
    EncodePrice(price float64, decimals int, bytes int) []byte
    DecodePrice(data []byte, decimals int) float64
    
    EncodeVolume(volume float64, decimals int, bytes int) []byte
    DecodeVolume(data []byte, decimals int) float64
    
    EncodeAmount(amount float64, decimals int, bytes int) []byte
    DecodeAmount(data []byte, decimals int) float64
}
```

### 使用示例

#### 1. 价格转换 (3字节BCD格式)

```go
converter := pump.NewBCDConverter()

// 编码价格: $1.259 -> BCD
price := 1.259
bcdData := converter.EncodePrice(price, 3, 3) // 3位小数，3字节
// 结果: [0x01, 0x25, 0x90]

// 解码价格: BCD -> $1.259
decodedPrice := converter.DecodePrice(bcdData, 3)
fmt.Printf("Price: $%.3f\n", decodedPrice)
```

#### 2. 体积转换 (4字节BCD格式)

```go
// 编码体积: 123.456升 -> BCD
volume := 123.456
bcdData := converter.EncodeVolume(volume, 3, 4) // 3位小数，4字节
// 结果: [0x01, 0x23, 0x45, 0x60]

// 解码体积
decodedVolume := converter.DecodeVolume(bcdData, 3)
fmt.Printf("Volume: %.3f L\n", decodedVolume)
```

#### 3. 金额转换 (4字节BCD格式)

```go
// 编码金额: $156.78 -> BCD
amount := 156.78
bcdData := converter.EncodeAmount(amount, 2, 4) // 2位小数，4字节
// 结果: [0x00, 0x01, 0x56, 0x78]

// 解码金额
decodedAmount := converter.DecodeAmount(bcdData, 2)
fmt.Printf("Amount: $%.2f\n", decodedAmount)
```

#### 4. 便捷转换函数

```go
// Wayne协议标准格式的快速转换
price := 1.259
bcdPrice := pump.EncodeBCDPrice3(price)        // 3字节价格
volume := 123.456
bcdVolume := pump.EncodeBCDVolume4(volume)     // 4字节体积
amount := 156.78
bcdAmount := pump.EncodeBCDAmount4(amount)     // 4字节金额

// 解码
decodedPrice := pump.DecodeBCDPrice3(bcdPrice)
decodedVolume := pump.DecodeBCDVolume4(bcdVolume)
decodedAmount := pump.DecodeBCDAmount4(bcdAmount)
```

### BCD验证

```go
// 验证BCD数据是否有效
bcdData := []byte{0x12, 0x34, 0x56}
err := pump.ValidateBCD(bcdData)
if err != nil {
    log.Printf("Invalid BCD data: %v", err)
}
```

## CD/DC事务处理

### 事务类型概述

#### CD事务 (Controller to Device)
- **CD1**: 泵控制命令
- **CD2**: 允许使用的油枪编号
- **CD3**: 预设体积
- **CD4**: 预设金额
- **CD5**: 价格更新
- **CD14/CD15**: 暂停/恢复请求
- **CD101**: 请求累计计数器

#### DC事务 (Device to Controller)
- **DC1**: 泵状态
- **DC2**: 加油体积和金额
- **DC3**: 油枪状态和单价
- **DC5**: 告警代码
- **DC7**: 泵参数
- **DC14/DC15**: 暂停/恢复响应
- **DC101**: 累计计数器

### CD事务构建示例

#### 1. CD1 - 泵控制命令

```go
builder := pump.NewTransactionBuilder()

// 创建授权命令
authTransaction, err := builder.BuildCD1(pump.CmdAuthorize)
if err != nil {
    log.Fatal(err)
}

// 编码为字节流
data := authTransaction.Encode()
fmt.Printf("CD1 Authorize: %X\n", data) // [0x01, 0x01, 0x06]

// 其他命令示例
resetTransaction, _ := builder.BuildCD1(pump.CmdReset)
stopTransaction, _ := builder.BuildCD1(pump.CmdStop)
statusTransaction, _ := builder.BuildCD1(pump.CmdReturnStatus)
```

#### 2. CD2 - 允许的油枪编号

```go
// 设置允许使用的油枪(1, 2, 3号油枪)
nozzles := []byte{1, 2, 3}
nozzleTransaction, err := builder.BuildCD2(nozzles)
if err != nil {
    log.Fatal(err)
}

data := nozzleTransaction.Encode()
fmt.Printf("CD2 Nozzles: %X\n", data)
```

#### 3. CD3 - 预设体积

```go
converter := pump.NewBCDConverter()

// 预设50升
volume := 50.0
volumeBCD := converter.EncodeVolume(volume, 3, 4) // 3位小数，4字节

volumeTransaction, err := builder.BuildCD3(volumeBCD)
if err != nil {
    log.Fatal(err)
}
```

#### 4. CD5 - 价格更新

```go
// 设置多种油品价格
prices := [][]byte{
    pump.EncodeBCDPrice3(1.259), // 汽油92# $1.259
    pump.EncodeBCDPrice3(1.329), // 汽油95# $1.329
    pump.EncodeBCDPrice3(1.159), // 柴油    $1.159
}

priceTransaction, err := builder.BuildCD5(prices)
if err != nil {
    log.Fatal(err)
}
```

### DC事务解析示例

#### 1. DC1 - 泵状态解析

```go
// 接收到的DC1响应数据
responseData := []byte{0x01, 0x01, 0x02} // TRANS=0x01, LNG=0x01, STATUS=0x02

dcTransaction, err := builder.ParseDC1(responseData)
if err != nil {
    log.Fatal(err)
}

status := pump.PumpStatus(dcTransaction.GetStatusCode())
switch status {
case pump.StatusAuthorized:
    fmt.Println("Pump is authorized")
case pump.StatusFilling:
    fmt.Println("Pump is filling")
case pump.StatusFillingCompleted:
    fmt.Println("Filling completed")
}
```

#### 2. DC2 - 体积和金额解析

```go
// DC2响应包含8字节数据：4字节体积 + 4字节金额
responseData := []byte{
    0x02, 0x08,           // TRANS=0x02, LNG=0x08
    0x01, 0x23, 0x45, 0x60, // 体积 BCD: 123.456L
    0x00, 0x01, 0x56, 0x78, // 金额 BCD: $156.78
}

dcTransaction, err := builder.ParseDC2(responseData)
if err != nil {
    log.Fatal(err)
}

data := dcTransaction.GetData()
volumeBCD := data[0:4]
amountBCD := data[4:8]

converter := pump.NewBCDConverter()
volume := converter.DecodeVolume(volumeBCD, 3)
amount := converter.DecodeAmount(amountBCD, 2)

fmt.Printf("Filled: %.3f L, Amount: $%.2f\n", volume, amount)
```

#### 3. DC5 - 告警代码解析

```go
responseData := []byte{0x05, 0x01, 0x09} // 紧急停止告警

dcTransaction, err := builder.ParseDC5(responseData)
if err != nil {
    log.Fatal(err)
}

alarmCode := dcTransaction.GetData()[0]
switch alarmCode {
case 0x09:
    fmt.Println("Emergency stop alarm")
case 0x0A:
    fmt.Println("Power failure alarm")
case 0x14:
    fmt.Println("Low tank level warning")
}
```

## 泵状态机管理

### 状态机接口

```go
type PumpStateMachineInterface interface {
    // 状态管理
    GetCurrentStatus() PumpStatus
    SetState(status PumpStatus) error
    IsSuspended() bool
    GetStateHistory() []StateTransition
    
    // 命令处理
    ProcessCommand(command PumpCommand) error
    ProcessCDTransaction(transaction CDTransaction) (DCTransaction, error)
    
    // 事件处理
    ProcessNozzleEvent(nozzleOut bool, nozzleNumber byte) error
    ProcessVolumeUpdate(volume int64) error
    ProcessAmountUpdate(amount int64) error
    ProcessAlarm(alarmCode byte) error
    
    // 预设管理
    SetPresetVolume(volume int64) error
    SetPresetAmount(amount int64) error
    
    // 价格管理
    SetPricesReceived(received bool)
}
```

### 泵状态定义

```go
const (
    StatusPumpNotProgrammed PumpStatus = 0x00 // 泵未编程
    StatusReset             PumpStatus = 0x01 // 复位状态
    StatusAuthorized        PumpStatus = 0x02 // 已授权
    StatusFilling           PumpStatus = 0x04 // 加油中
    StatusFillingCompleted  PumpStatus = 0x05 // 加油完成
    StatusMaxAmountReached  PumpStatus = 0x06 // 达到最大金额/体积
    StatusSwitchedOff       PumpStatus = 0x07 // 关闭
    StatusSuspended         PumpStatus = 0x08 // 暂停
)
```

### 使用示例

#### 1. 基本状态机操作

```go
// 创建状态机
sm := pump.NewPumpStateMachine()

// 检查初始状态
fmt.Printf("Initial status: %d\n", sm.GetCurrentStatus()) // StatusPumpNotProgrammed

// 设置价格已接收(启用编程)
sm.SetPricesReceived(true)

// 执行复位命令
err := sm.ProcessCommand(pump.CmdReset)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("After reset: %d\n", sm.GetCurrentStatus()) // StatusReset

// 授权加油
err = sm.ProcessCommand(pump.CmdAuthorize)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("After authorize: %d\n", sm.GetCurrentStatus()) // StatusAuthorized
```

#### 2. 油枪事件处理

```go
// 授权状态下，油枪拔出事件
err := sm.ProcessNozzleEvent(true, 1) // 油枪拔出，油枪号1
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Nozzle out: %d\n", sm.GetCurrentStatus()) // StatusFilling

// 加油过程中更新体积
err = sm.ProcessVolumeUpdate(25000) // 25.000升
if err != nil {
    log.Fatal(err)
}

// 油枪插回事件
err = sm.ProcessNozzleEvent(false, 1) // 油枪插回
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Nozzle in: %d\n", sm.GetCurrentStatus()) // StatusFillingCompleted
```

#### 3. 预设限值处理

```go
// 设置预设体积限值 (50升)
err := sm.SetPresetVolume(50000) // 50.000升 (毫升为单位)
if err != nil {
    log.Fatal(err)
}

// 设置预设金额限值 ($100)
err = sm.SetPresetAmount(10000) // $100.00 (分为单位)
if err != nil {
    log.Fatal(err)
}

// 模拟加油过程中达到预设限值
sm.SetState(pump.StatusFilling)
err = sm.ProcessVolumeUpdate(50000) // 达到预设体积
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Preset reached: %d\n", sm.GetCurrentStatus()) // StatusMaxAmountReached
```

#### 4. 暂停/恢复操作

```go
// 在授权状态下暂停
sm.SetState(pump.StatusAuthorized)
err := sm.ProcessCommand(pump.CmdSuspendFuellingPoint)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Is suspended: %t\n", sm.IsSuspended()) // true

// 恢复操作
err = sm.ProcessCommand(pump.CmdResumeFuellingPoint)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Is suspended: %t\n", sm.IsSuspended()) // false
```

#### 5. 告警处理

```go
// 处理紧急停止告警
err := sm.ProcessAlarm(0x09) // 紧急停止
if err != nil {
    log.Fatal(err)
}
fmt.Printf("After alarm: %d\n", sm.GetCurrentStatus()) // StatusSwitchedOff

// 处理非关键告警(不改变状态)
sm.SetState(pump.StatusFilling)
err = sm.ProcessAlarm(0x14) // 低油罐液位警告
if err != nil {
    log.Fatal(err)
}
fmt.Printf("After warning: %d\n", sm.GetCurrentStatus()) // StatusFilling (不变)
```

#### 6. 状态历史查看

```go
// 获取状态变化历史
history := sm.GetStateHistory()
for _, transition := range history {
    fmt.Printf("State change: %d -> %d at %s (trigger: %s)\n",
        transition.FromStatus,
        transition.ToStatus,
        transition.Timestamp.Format("15:04:05"),
        transition.Trigger)
}
```

#### 7. 事务处理集成

```go
builder := pump.NewTransactionBuilder()

// 创建CD1授权命令
cdTransaction, err := builder.BuildCD1(pump.CmdAuthorize)
if err != nil {
    log.Fatal(err)
}

// 通过状态机处理CD事务，获得DC响应
dcResponse, err := sm.ProcessCDTransaction(cdTransaction)
if err != nil {
    log.Fatal(err)
}

// 检查响应
fmt.Printf("Response type: %d\n", dcResponse.GetType())
fmt.Printf("Response status: %d\n", dcResponse.GetStatusCode())
```

## API参考

### 错误类型

```go
// 验证错误
type ValidationError struct {
    Field   string      // 出错字段
    Message string      // 错误信息
    Value   interface{} // 错误值
}

// 事务错误
type TransactionError struct {
    Type    TransactionType // 事务类型
    Message string          // 错误信息
    Cause   error          // 根本原因
}

// 状态转换错误
type StateTransitionError struct {
    FromStatus PumpStatus  // 源状态
    ToStatus   PumpStatus  // 目标状态
    Command    PumpCommand // 命令
    Message    string      // 错误信息
}
```

### 常用常量

```go
// 泵命令
const (
    CmdReturnStatus             PumpCommand = 0x00
    CmdReturnPumpParameters     PumpCommand = 0x02
    CmdReturnPumpIdentity       PumpCommand = 0x03
    CmdReturnFillingInformation PumpCommand = 0x04
    CmdReset                    PumpCommand = 0x05
    CmdAuthorize                PumpCommand = 0x06
    CmdStop                     PumpCommand = 0x08
    CmdSwitchOff                PumpCommand = 0x0A
    CmdSuspendFuellingPoint     PumpCommand = 0x0D
    CmdResumeFuellingPoint      PumpCommand = 0x0E
    CmdReturnPricesOfAllGrades  PumpCommand = 0x0F
)

// 告警代码
const (
    AlarmCPUReset       = 0x01 // CPU复位
    AlarmRAMError       = 0x03 // RAM错误
    AlarmPROMChecksum   = 0x04 // PROM校验错误
    AlarmPulserError    = 0x06 // 脉冲器错误
    AlarmEmergencyStop  = 0x09 // 紧急停止
    AlarmPowerFailure   = 0x0A // 停电
    AlarmPressureLost   = 0x0B // 压力丢失
    AlarmBlendRatio     = 0x0C // 混合比例错误
    AlarmLowTankLevel   = 0x14 // 低油罐液位(警告)
    AlarmHighTankLevel  = 0x15 // 高油罐液位(警告)
)
```

## 最佳实践

### 1. 错误处理

```go
// 总是检查错误
converter := pump.NewBCDConverter()
bcdData := converter.EncodeBCD(12345, 3)

// 验证BCD数据
if err := pump.ValidateBCD(bcdData); err != nil {
    log.Printf("BCD validation failed: %v", err)
    return err
}

// 捕获特定错误类型
err := sm.ProcessCommand(pump.CmdAuthorize)
if err != nil {
    switch e := err.(type) {
    case *pump.StateTransitionError:
        log.Printf("Invalid state transition: %v", e)
    case *pump.ValidationError:
        log.Printf("Validation error: %v", e)
    default:
        log.Printf("Unknown error: %v", e)
    }
}
```

### 2. 并发安全

```go
// 状态机自动处理并发安全
var wg sync.WaitGroup
sm := pump.NewPumpStateMachine()

// 多个goroutine同时操作状态机
for i := 0; i < 10; i++ {
    wg.Add(1)
    go func() {
        defer wg.Done()
        status := sm.GetCurrentStatus()
        sm.ProcessCommand(pump.CmdReturnStatus)
        history := sm.GetStateHistory()
        _ = status
        _ = history
    }()
}
wg.Wait()
```

### 3. 性能优化

```go
// 重用转换器和构建器实例
var (
    converter = pump.NewBCDConverter()
    builder   = pump.NewTransactionBuilder()
)

// 批量处理价格数据
prices := []float64{1.259, 1.329, 1.159}
bcdPrices := make([][]byte, len(prices))
for i, price := range prices {
    bcdPrices[i] = converter.EncodePrice(price, 3, 3)
}

// 使用预分配的切片
data := make([]byte, 0, 100) // 预分配足够容量
```

### 4. 配置管理

```go
// 定义配置结构
type PumpConfig struct {
    PriceDecimals  int    // 价格小数位数
    VolumeDecimals int    // 体积小数位数
    AmountDecimals int    // 金额小数位数
    MaxNozzles     int    // 最大油枪数量
    AlarmCritical  []byte // 关键告警代码
}

// 使用配置驱动的转换
func encodePriceWithConfig(price float64, config PumpConfig) []byte {
    converter := pump.NewBCDConverter()
    return converter.EncodePrice(price, config.PriceDecimals, 3)
}
```

### 5. 日志记录

```go
import "log/slog"

// 结构化日志记录
func processTransaction(transaction pump.CDTransaction) error {
    slog.Info("Processing CD transaction",
        "type", transaction.GetType(),
        "length", transaction.GetLength(),
        "command", transaction.GetCommandCode())
    
    // 处理逻辑...
    
    slog.Info("Transaction processed successfully",
        "type", transaction.GetType())
    
    return nil
}
```

## 故障排查

### 常见问题

#### 1. BCD转换错误

**问题**: BCD数据验证失败
```go
err := pump.ValidateBCD([]byte{0x1A, 0x23})
// Error: invalid BCD digit A in high nibble at byte 0
```

**解决方案**:
```go
// 检查数据源，确保输入为有效BCD格式
// BCD只能包含0-9的十进制数字
validBCD := []byte{0x12, 0x34, 0x56} // 正确
invalidBCD := []byte{0x1A, 0x23}     // 错误：包含0xA
```

#### 2. 状态转换失败

**问题**: 无效的状态转换
```go
sm := pump.NewPumpStateMachine()
err := sm.ProcessCommand(pump.CmdAuthorize) // 从NOT_PROGRAMMED直接授权
// Error: invalid state transition from 0 to 2 via command 6
```

**解决方案**:
```go
// 遵循正确的状态转换序列
sm.SetPricesReceived(true)           // 1. 先接收价格
sm.ProcessCommand(pump.CmdReset)     // 2. 复位
sm.ProcessCommand(pump.CmdAuthorize) // 3. 然后授权
```

#### 3. 事务长度不匹配

**问题**: 事务数据长度与长度字段不符
```go
data := []byte{0x01, 0x02, 0x06} // LNG=0x02但只有1字节数据
_, err := builder.ParseDC1(data)
// Error: length field (2) doesn't match data length (1)
```

**解决方案**:
```go
// 确保数据长度与LNG字段一致
correctData := []byte{0x01, 0x01, 0x06} // LNG=0x01，1字节数据
```

#### 4. 并发访问问题

**问题**: 并发环境下状态不一致

**解决方案**:
```go
// 状态机内置并发保护，但建议在应用层也加保护
type SafePumpManager struct {
    sm    pump.PumpStateMachineInterface
    mutex sync.RWMutex
}

func (pm *SafePumpManager) ProcessCommand(cmd pump.PumpCommand) error {
    pm.mutex.Lock()
    defer pm.mutex.Unlock()
    return pm.sm.ProcessCommand(cmd)
}

func (pm *SafePumpManager) GetStatus() pump.PumpStatus {
    pm.mutex.RLock()
    defer pm.mutex.RUnlock()
    return pm.sm.GetCurrentStatus()
}
```

### 调试技巧

#### 1. 启用详细日志

```go
import "log/slog"

// 设置调试级别日志
slog.SetDefault(slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelDebug,
})))

// 在关键点添加日志
sm := pump.NewPumpStateMachine()
slog.Debug("State machine created", "initial_status", sm.GetCurrentStatus())

err := sm.ProcessCommand(pump.CmdReset)
slog.Debug("Command processed", "command", pump.CmdReset, "error", err)
```

#### 2. 状态历史分析

```go
// 查看完整状态变化历史
history := sm.GetStateHistory()
for i, transition := range history {
    fmt.Printf("[%d] %s: %d -> %d (trigger: %s)\n",
        i,
        transition.Timestamp.Format("15:04:05.000"),
        transition.FromStatus,
        transition.ToStatus,
        transition.Trigger)
}
```

#### 3. 事务数据分析

```go
// 分析事务原始数据
transaction, _ := builder.BuildCD1(pump.CmdAuthorize)
rawData := transaction.Encode()

fmt.Printf("Transaction breakdown:\n")
fmt.Printf("  TRANS: 0x%02X (%d)\n", rawData[0], rawData[0])
fmt.Printf("  LNG:   0x%02X (%d)\n", rawData[1], rawData[1])
for i, b := range rawData[2:] {
    fmt.Printf("  DATA[%d]: 0x%02X (%d)\n", i, b, b)
}
```

### 性能监控

```go
// 测量BCD转换性能
func benchmarkBCDConversion() {
    converter := pump.NewBCDConverter()
    start := time.Now()
    
    for i := 0; i < 10000; i++ {
        bcd := converter.EncodeBCD(int64(i), 3)
        _ = converter.DecodeBCD(bcd)
    }
    
    duration := time.Since(start)
    fmt.Printf("10000 BCD conversions took: %v\n", duration)
}

// 测量状态机性能
func benchmarkStateMachine() {
    sm := pump.NewPumpStateMachine()
    start := time.Now()
    
    for i := 0; i < 1000; i++ {
        sm.ProcessCommand(pump.CmdReturnStatus)
        _ = sm.GetCurrentStatus()
    }
    
    duration := time.Since(start)
    fmt.Printf("1000 state operations took: %v\n", duration)
}
```

---

## 📞 技术支持

如有问题或建议，请联系FCC开发团队。

- **技术文档**: `/docs/FCC_Wayne_DART协议支持开发任务.md`
- **架构设计**: `/docs/FCC_Architecture_Design.md`
- **代码仓库**: `internal/adapters/wayne/pump/`
- **测试覆盖**: 运行 `go test ./internal/adapters/wayne/pump -cover`

---

*本文档基于Wayne Pump Interface v2.1实现，最后更新: 2024年12月* 