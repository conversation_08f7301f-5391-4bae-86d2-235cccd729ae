# FCC Service 日志配置指南

## 功能特性

FCC Service 现在支持同时输出日志到控制台和文件，具备以下特性：

- ✅ **双重输出**：同时输出到控制台和文件
- ✅ **日志轮转**：自动轮转日志文件，避免单个文件过大
- ✅ **压缩备份**：自动压缩旧的日志文件
- ✅ **灵活配置**：可单独控制控制台和文件输出
- ✅ **结构化日志**：支持 JSON 和文本格式

## 配置说明

### 配置文件位置
配置位于 `configs/config.yaml` 的 `logging` 部分：

```yaml
logging:
  level: "debug"             # 日志级别: debug, info, warn, error
  format: "json"             # 日志格式: json, text
  console:
    enabled: true            # 启用控制台输出
  file:
    enabled: true            # 启用文件输出
    path: "logs/fcc-service.log"  # 日志文件路径
    max_size: 100            # 单个日志文件最大大小(MB)
    max_backups: 10          # 保留的备份文件数量
    max_age: 30              # 保留日志文件天数
    compress: true           # 是否压缩旧的日志文件
```

### 配置选项详解

#### 基本配置
- **level**: 日志级别，控制输出的日志详细程度
  - `debug`: 输出所有日志（开发模式）
  - `info`: 输出信息、警告和错误日志
  - `warn`: 仅输出警告和错误日志
  - `error`: 仅输出错误日志

- **format**: 日志输出格式
  - `json`: JSON 格式，便于日志收集和分析
  - `text`: 文本格式，便于人工阅读

#### 控制台输出
- **console.enabled**: 是否启用控制台输出
  - `true`: 日志输出到标准输出
  - `false`: 禁用控制台输出

#### 文件输出
- **file.enabled**: 是否启用文件输出
- **file.path**: 日志文件保存路径（会自动创建目录）
- **file.max_size**: 单个日志文件最大大小（MB）
- **file.max_backups**: 保留的备份文件数量
- **file.max_age**: 保留日志文件的天数
- **file.compress**: 是否压缩旧的日志文件

## 使用示例

### 仅控制台输出（开发环境）
```yaml
logging:
  level: "debug"
  format: "text"
  console:
    enabled: true
  file:
    enabled: false
```

### 仅文件输出（后台服务）
```yaml
logging:
  level: "info"
  format: "json"
  console:
    enabled: false
  file:
    enabled: true
    path: "logs/fcc-service.log"
    max_size: 50
    max_backups: 5
    max_age: 15
    compress: true
```

### 双重输出（生产环境推荐）
```yaml
logging:
  level: "info"
  format: "json"
  console:
    enabled: true
  file:
    enabled: true
    path: "logs/fcc-service.log"
    max_size: 100
    max_backups: 10
    max_age: 30
    compress: true
```

## 日志文件管理

### 文件结构
```
logs/
├── fcc-service.log           # 当前日志文件
├── fcc-service.log.1         # 备份文件1
├── fcc-service.log.2.gz      # 压缩的备份文件2
└── ...
```

### 轮转机制
- 当日志文件大小达到 `max_size` 时，自动轮转
- 保留最近 `max_backups` 个备份文件
- 超过 `max_age` 天的文件自动删除
- 旧文件根据 `compress` 设置决定是否压缩

## 查看日志

### 实时查看控制台日志
```bash
go run main_v2.go --config configs/config.yaml
```

### 查看日志文件
```bash
# 查看最新日志
tail -f logs/fcc-service.log

# 查看最近50行
tail -n 50 logs/fcc-service.log

# 搜索错误日志
grep "ERROR" logs/fcc-service.log
```

### Windows PowerShell 查看
```powershell
# 查看最新日志
Get-Content logs/fcc-service.log -Tail 20 -Wait

# 查看最近50行
Get-Content logs/fcc-service.log | Select-Object -Last 50

# 搜索错误日志
Select-String "ERROR" logs/fcc-service.log
```

## 性能考虑

- 文件日志使用异步写入，不影响应用性能
- JSON 格式便于日志分析工具处理
- 自动压缩和清理避免磁盘空间问题
- 轮转机制确保单个文件不会过大

## 故障排除

### 日志文件无法创建
1. 检查目录权限
2. 确保磁盘空间充足
3. 检查配置文件中的路径是否正确

### 日志输出为空
1. 检查日志级别设置
2. 确认 console.enabled 或 file.enabled 为 true
3. 检查应用程序是否有足够的权限写入文件

### 性能问题
1. 考虑降低日志级别（从 debug 改为 info）
2. 适当减少 max_backups 数量
3. 增大 max_size 减少轮转频率 