# 串口原始数据调试功能使用说明

## 功能简介

为了方便调试串口通信问题，我们在传输层添加了原始数据日志记录功能。该功能可以记录串口发送和接收的最原始数据，包括：

- **发送数据**：HEX格式、ASCII格式、二进制格式、精确时间戳
- **接收数据**：HEX格式、ASCII格式、二进制格式、精确时间戳
- **通信统计**：发送耗时、字节数统计、响应通道状态等
- **传输层详情**：设备ID、端口、地址、通道缓冲状态

## 当前实现层级

系统中有多个层级的串口通信实现：

1. **传输层** (`transport_implementations.go`) - **新增调试功能**
2. **连接管理层** (`serial_manager.go`) - 已有原始数据日志
3. **Wayne适配器层** (`wayne/connection/serial_manager.go`) - 已有原始数据日志

## 如何启用

### 1. 配置文件方式

编辑 `configs/config.yaml` 文件：

```yaml
# 日志配置 - 必须设置为debug级别
logging:
  level: "debug"              # 设置为debug级别
  format: "json"             # 建议使用json格式便于解析

# DART协议配置
dart:
  serial:
    enable_raw_data_logging: true  # 启用原始数据调试日志
    # ... 其他配置
```

### 2. 代码层面启用

```go
// 在创建SerialTransport时启用调试
transport, err := NewSerialTransportWithConfig(deviceID, address, port, logger, true)

// 或者在运行时设置
transport.SetRawDataLogging(true)
```

### 3. 临时调试方式

如果只是临时需要调试，可以修改配置文件：

```yaml
logging:
  level: "debug"
dart:
  serial:
    enable_raw_data_logging: true
```

## 修复的问题

### 1. 响应通道满问题

**问题**：原来响应通道缓冲区只有10，容易满
```
{"level":"warn","ts":1750538394.9934685,"caller":"v2/transport_implementations.go:167","msg":"Response channel full, dropping data","device_id":"device_com7_pump01"}
```

**解决**：
- 增加缓冲区大小从10到100
- 增强错误信息，显示通道状态
- 添加建议信息

### 2. 原始数据调试功能

**新增功能**：
- 发送数据详细日志记录
- 接收数据详细日志记录
- 通道状态监控
- 响应时间统计

## 日志输出示例

### 发送数据日志 (传输层)

```json
{
  "level": "debug",
  "ts": 1750538012.123456,
  "caller": "v2/transport_implementations.go:110",
  "msg": "串口传输层发送数据",
  "device_id": "device_com7_pump01",
  "port": "COM7",
  "address": 81,
  "字节数": 8,
  "发送数据(HEX)": "10025103DC110315",
  "发送数据(ASCII)": "..Q.....\\.",
  "发送数据(二进制)": "0001000000000010010100010000001111011100000100010000001100010101",
  "发送时间": "14:26:52.123",
  "超时时间": "25ms"
}
```

### 接收响应日志 (传输层)

```json
{
  "level": "debug",
  "ts": 1750538012.125789,
  "caller": "v2/transport_implementations.go:140",
  "msg": "串口传输层接收响应",
  "device_id": "device_com7_pump01", 
  "port": "COM7",
  "address": 81,
  "字节数": 6,
  "响应数据(HEX)": "100651D11003A5",
  "响应数据(ASCII)": "..Q....",
  "响应数据(二进制)": "000100000000011001010001110100010001000000000011",
  "接收时间": "14:26:52.125",
  "响应耗时": "2.456ms"
}
```

### 原始数据回调日志

```json
{
  "level": "debug",
  "ts": 1750538012.125234,
  "caller": "v2/transport_implementations.go:180",
  "msg": "串口传输层原始数据回调",
  "device_id": "device_com7_pump01",
  "port": "COM7", 
  "address": 81,
  "字节数": 6,
  "原始数据(HEX)": "100651D11003A5",
  "原始数据(ASCII)": "..Q....",
  "原始数据(二进制)": "000100000000011001010001110100010001000000000011",
  "接收时间": "14:26:52.125",
  "响应通道缓冲": 3
}
```

### 响应通道满错误 (改进后)

```json
{
  "level": "error",
  "ts": 1750538012.126789,
  "caller": "v2/transport_implementations.go:195",
  "msg": "响应通道已满，丢弃数据！",
  "device_id": "device_com7_pump01",
  "port": "COM7",
  "丢弃字节数": 6,
  "丢弃数据(HEX)": "100651D11003A5",
  "通道容量": 100,
  "通道当前长度": 100,
  "建议": "考虑增加响应通道缓冲区大小或检查数据处理速度"
}
```

## 性能影响

### 启用原始数据日志时：
- 每次串口读写都会记录详细信息（3层日志：传输层+连接层+管理层）
- 包含多种数据格式转换
- 日志量会显著增加（约3倍）
- **建议仅在调试时启用**

### 禁用原始数据日志时：
- 只记录基本的发送/接收信息
- 日志量很少
- 性能影响微乎其微
- **生产环境推荐设置**

## 使用场景

### 1. 协议调试
当需要分析DART协议的具体数据格式时：
```yaml
logging:
  level: "debug"
dart:
  serial:
    enable_raw_data_logging: true
```

### 2. 通信问题排查
当设备通信异常时，可以看到具体的收发数据：
- 检查数据格式是否正确
- 确认CRC校验值
- 分析通信时序
- 监控响应通道状态

### 3. 响应通道问题诊断
当出现数据丢失时：
- 监控通道缓冲状态
- 分析数据处理速度
- 优化通道大小

## 注意事项

1. **性能影响**：原始数据日志会增加CPU和内存使用，不建议在生产环境长期开启

2. **日志大小**：高频通信时日志文件会快速增长，注意磁盘空间

3. **敏感信息**：原始数据可能包含敏感信息，注意日志文件的安全性

4. **日志级别**：必须将日志级别设置为`debug`，否则原始数据日志不会输出

5. **通道监控**：关注响应通道缓冲状态，避免数据丢失

## 配置建议

### 开发环境
```yaml
logging:
  level: "debug"
dart:
  serial:
    enable_raw_data_logging: true
```

### 测试环境  
```yaml
logging:
  level: "info"
dart:
  serial:
    enable_raw_data_logging: false
```

### 生产环境
```yaml
logging:
  level: "warn"
dart:
  serial:
    enable_raw_data_logging: false
```

## 故障排查

如果原始数据日志没有输出，请检查：

1. 日志级别是否设置为`debug`
2. `enable_raw_data_logging`是否设置为`true`
3. 串口连接是否正常建立
4. 是否有实际的数据收发
5. 响应通道是否正常工作

如果出现响应通道满的错误：

1. 检查数据处理速度是否过慢
2. 考虑增加通道缓冲区大小
3. 优化数据处理逻辑
4. 检查是否有协程泄漏

## 相关文件

### 主要实现文件
- `internal/services/polling/v2/transport_implementations.go` - **传输层(新增)**
- `internal/connection/serial_manager.go` - 连接管理层
- `internal/adapters/wayne/connection/serial_manager.go` - Wayne适配器层

### 配置文件
- `configs/config.yaml` - 主配置文件

### 调用位置
- `internal/services/polling/v2/wayne_communication.go` - Wayne通信模块
- `internal/services/polling/v2/generic_communication.go` - 通用通信模块 