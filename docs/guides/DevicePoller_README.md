# DevicePoller - Wayne DART协议设备轮询器

## 概述

DevicePoller是一个专为Wayne DART协议设计的高性能设备轮询器，支持与Wayne加油机设备进行实时通信。它采用分层架构设计，提供完整的串口通信、协议解析、业务处理和状态管理功能。

## 核心特性

### 🚀 高性能轮询引擎
- **优化的轮询策略**: 优先处理命令队列，支持业务持续性处理
- **多路复用**: 单个轮询器管理一个设备的完整生命周期
- **看门狗机制**: 自动检测和恢复设备连接异常

### 🔌 灵活的连接支持
- **串口通信**: 通过RS485串口与实际设备通信
- **TCP预留**: 预留TCP连接接口，支持网络设备
- **模拟模式**: 提供完整的模拟环境用于开发和测试

### 📋 完整的协议支持
- **Wayne DART v1.3**: 严格遵循Wayne DART协议规范
- **25ms响应**: 满足DART协议响应时间要求
- **多事务处理**: 支持DC1/DC2/DC3/DC5/DC101等所有主要事务类型

### 🎯 业务驱动设计
- **业务分类**: 根据业务类型进行差异化处理
- **状态跟踪**: 完整的业务状态生命周期管理
- **统计监控**: 实时性能监控和统计分析

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────┐
│                    外部接口层                            │
│  CommandChannel | ResultChannel | StopChannel           │
├─────────────────────────────────────────────────────────┤
│                    核心控制层                            │
│  DevicePoller | PollerContext | BusinessState           │
├─────────────────────────────────────────────────────────┤
│                    业务处理层                            │
│  AuthorizeBusiness | PresetBusiness | FillingBusiness   │
├─────────────────────────────────────────────────────────┤
│                    协议处理层                            │
│  FrameBuilder | TransactionBuilder | DartlineFrame      │
├─────────────────────────────────────────────────────────┤
│                    连接管理层                            │
│  ConnectionManager | SerialConnection | SerialManager   │
├─────────────────────────────────────────────────────────┤
│                    状态管理层                            │
│  DeviceStateMachine | DeviceStateData | StatsCollector  │
└─────────────────────────────────────────────────────────┘
```

```
graph TB
    subgraph "DevicePoller 核心组件"
        DP["DevicePoller"]
        PC["PollerContext<br/>轮询器上下文"]
        BS["BusinessState<br/>业务状态"]
        SC["StatsCollector<br/>统计收集器"]
        WD["Watchdog<br/>看门狗"]
    end

    subgraph "连接管理层"
        CM["ConnectionManager<br/>连接管理器"]
        SerialConn["SerialConnection<br/>串口连接"]
        SerialMgr["SerialManager<br/>串口管理器"]
    end

    subgraph "协议处理层"
        FB["FrameBuilder<br/>帧构建器"]
        TB["TransactionBuilder<br/>事务构建器"]
        DF["DartlineFrame<br/>DART协议帧"]
        DC["DCTransaction<br/>DC事务"]
    end

    subgraph "状态管理层"
        DSM["DeviceStateMachine<br/>设备状态机"]
        DeviceInfo["DeviceInfo<br/>设备信息"]
        DeviceState["DeviceStateData<br/>设备状态数据"]
    end

    subgraph "外部接口"
        CmdChan["CommandChannel<br/>命令通道"]
        ResultChan["ResultChannel<br/>结果通道"]
        StopChan["StopChannel<br/>停止通道"]
    end

    %% 组件关系
    DP --> PC
    DP --> BS
    DP --> SC
    DP --> WD
    DP --> CM
    DP --> FB
    DP --> TB
    DP --> DSM

    CM --> SerialConn
    SerialConn --> SerialMgr
    FB --> DF
    TB --> DC
    DSM --> DeviceState

    %% 数据流
    CmdChan --> DP
    DP --> ResultChan
    StopChan --> DP

    %% 样式
    classDef coreComponent fill:#e1f5fe
    classDef connectionLayer fill:#f3e5f5
    classDef protocolLayer fill:#e8f5e8
    classDef stateLayer fill:#fff3e0
    classDef externalInterface fill:#fce4ec

    class DP,PC,BS,SC,WD coreComponent
    class CM,SerialConn,SerialMgr connectionLayer
    class FB,TB,DF,DC protocolLayer
    class DSM,DeviceInfo,DeviceState stateLayer
    class CmdChan,ResultChan,StopChan externalInterface
```
### 核心组件

#### DevicePoller (核心轮询器)
- **职责**: 管理单个设备的完整轮询生命周期
- **功能**: 命令调度、业务持续处理、定时轮询
- **特性**: 线程安全、高性能、可监控

#### PollerContext (轮询上下文)
- **职责**: 管理命令队列和业务状态
- **功能**: 优先级调度、业务状态跟踪
- **特性**: 并发安全、状态持久化

#### ConnectionManager (连接管理器)
- **职责**: 管理设备连接池和健康检查
- **功能**: 连接复用、故障恢复、资源管理
- **特性**: 自动重连、连接池、监控统计

## 核心功能


### 主要处理流程图
```
graph TD
    Start["Start()"] --> InitConn["initializeConnection()"]
    InitConn --> StartWD["watchdog.Start()"]
    StartWD --> StartLoop["unifiedPollLoop()"]
    
    StartLoop --> PollCycle["executeOptimizedPoll()"]
    
    subgraph "轮询决策逻辑"
        PollCycle --> CheckCmd{"检查待处理命令<br/>GetNextCommand()"}
        CheckCmd -->|有命令| ExecCmd["executeBusinessCommand()"]
        CheckCmd -->|无命令| CheckBusiness{"检查业务持续<br/>ShouldContinueBusiness()"}
        CheckBusiness -->|需要持续| ContBusiness["continuePreviousBusiness()"]
        CheckBusiness -->|无业务| RoutinePoll["executeRoutinePoll()"]
    end

    subgraph "命令执行流程"
        ExecCmd --> BuildFrame["frameBuilder.BuildDataFrame()"]
        BuildFrame --> SendFrame["sendFrameAndWait()"]
        SendFrame --> ParseResp["frameBuilder.ParseResponse()"]
        ParseResp --> ProcCmdResp["processCommandResponse()"]
        ProcCmdResp --> HandleCmdResult["handleCommandResult()"]
    end

    subgraph "例行轮询流程"
        RoutinePoll --> BuildPoll["frameBuilder.BuildPollFrame()"]
        BuildPoll --> SendPoll["sendFrameAndWait()"]
        SendPoll --> ParsePollResp["frameBuilder.ParseResponse()"]
        ParsePollResp --> ProcPollResp["processPollResponse()"]
        ProcPollResp --> HandlePollResult["handlePollResult()"]
    end

    subgraph "业务持续流程"
        ContBusiness --> AuthorizeFlow["continueAuthorizeProcess()"]
        ContBusiness --> PresetFlow["continuePresetProcess()"]
        ContBusiness --> FillingFlow["continueFillingProcess()"]
        AuthorizeFlow --> HandleBizResult["handleBusinessContinuation()"]
        PresetFlow --> HandleBizResult
        FillingFlow --> HandleBizResult
    end

    %% 数据处理流程
    subgraph "数据处理流程"
        ProcCmdResp --> ParseDC["parseDCTransactions()"]
        ProcPollResp --> ParseDC
        ParseDC --> ProcDCTrans["processDCTransaction()"]
        ProcDCTrans --> ProcDC1["processDC1Status()"]
        ProcDCTrans --> ProcDC2["processDC2VolumeAmount()"]
        ProcDCTrans --> ProcDC3["processDC3PriceNozzle()"]
        ProcDCTrans --> ProcDC5["processDC5Alarm()"]
        ProcDCTrans --> ProcDC101["processDC101Counters()"]
    end

    %% 串口通信流程
    subgraph "串口通信"
        SendFrame --> CheckSerial{"有串口连接?"}
        SendPoll --> CheckSerial
        CheckSerial -->|是| RealSerial["serialConnection.SendFrame()"]
        CheckSerial -->|否| SimMode["模拟模式"]
        RealSerial --> WaitResp["等待响应"]
        SimMode --> MockResp["模拟响应"]
    end

    %% 结果处理
    HandleCmdResult --> UpdateStats["statsCollector.UpdateStats()"]
    HandlePollResult --> UpdateStats
    HandleBizResult --> UpdateStats
    UpdateStats --> NotifySM["notifyStateMachine()"]
    NotifySM --> SendResult["sendResult()"]
    SendResult --> UpdateActivity["updateActivity()"]
    UpdateActivity --> PollCycle

    %% 样式
    classDef startEnd fill:#ffcdd2
    classDef decision fill:#fff3e0
    classDef process fill:#e8f5e8
    classDef dataFlow fill:#e1f5fe
    classDef communication fill:#f3e5f5

    class Start,Stop startEnd
    class CheckCmd,CheckBusiness,CheckSerial decision
    class ExecCmd,RoutinePoll,ContBusiness,BuildFrame,SendFrame process
    class ParseDC,ProcDCTrans,ProcDC1,ProcDC2,ProcDC3,ProcDC5,ProcDC101 dataFlow
    class RealSerial,SimMode,WaitResp,MockResp communication
```

### 1. 统一轮询循环


```go
func (p *DevicePoller) executeOptimizedPoll(ctx context.Context) {
    // 1️⃣ 优先检查命令队列
    if cmd := p.pollerContext.GetNextCommand(); cmd != nil {
        result := p.executeBusinessCommand(ctx, *cmd, startTime)
        p.handleCommandResult(result, cmd)
        return
    }
    
    // 2️⃣ 检查业务持续性处理
    if p.pollerContext.ShouldContinueBusiness() {
        result := p.continuePreviousBusiness(ctx, startTime)
        p.handleBusinessContinuation(result)
        return
    }
    
    // 3️⃣ 执行例行状态轮询
    result := p.executeRoutinePoll(ctx, startTime)
    p.handlePollResult(result)
}
```

### 2. 业务命令处理

支持以下业务类型：
- **授权业务** (`BusinessTypeAuthorize`): 油枪授权和准备
- **预设业务** (`BusinessTypePreset`): 预设加油量或金额
- **加油业务** (`BusinessTypeFilling`): 加油过程监控
- **状态查询** (`BusinessTypeStatus`): 设备状态获取
- **价格更新** (`BusinessTypePriceUpdate`): 油品价格更新

### 3. DC事务解析

```go
// 支持的DC事务类型
type DCTransactionType byte

const (
    TransactionTypeDC1   = 0x01 // 泵状态
    TransactionTypeDC2   = 0x02 // 体积和金额
    TransactionTypeDC3   = 0x03 // 价格和喷嘴状态
    TransactionTypeDC5   = 0x05 // 报警代码
    TransactionTypeDC101 = 0x65 // 总计数器
)
```

### 4. 串口通信

```go
// 串口配置示例
serialConfig := connection.SerialConfig{
    Port:         "/dev/ttyUSB0",     // 串口端口
    BaudRate:     9600,               // DART协议要求
    DataBits:     8,                  // DART协议要求
    StopBits:     1,                  // DART协议要求
    Parity:       connection.ParityOdd, // DART协议要求
    Timeout:      25 * time.Millisecond, // DART协议限制
}
```

## 使用指南

### 基本使用

```go
package main

import (
    "context"
    "time"
    
    "fcc-service/internal/services/polling/v2"
    v2models "fcc-service/pkg/models/v2"
    "go.uber.org/zap"
)

func main() {
    // 1. 配置设备信息
    deviceInfo := v2models.DeviceInfo{
        ID:      "pump_001",
        Name:    "加油机1号",
        Type:    "wayne_pump",
        Address: 0x50, // DART协议地址
    }
    
    // 2. 创建配置
    config := v2.DevicePollerConfig{
        DeviceInfo:      deviceInfo,
        PollInterval:    100 * time.Millisecond,
        PollTimeout:     25 * time.Millisecond,
        WatchdogTimeout: 5 * time.Second,
        BufferSize:      10,
    }
    
    // 3. 创建状态机（需要实现DeviceStateMachine接口）
    deviceSM := NewDeviceStateMachine(deviceInfo)
    
    // 4. 创建轮询器
    logger := zap.NewDevelopment()
    poller := v2.NewDevicePoller(config, deviceSM, nil, logger)
    
    // 5. 启动轮询器
    ctx := context.Background()
    if err := poller.Start(ctx); err != nil {
        panic(err)
    }
    defer poller.Stop()
    
    // 6. 发送命令
    cmd := v2.PollCommand{
        Type:         "data",
        BusinessType: "authorize",
        Priority:     1,
        Data:         []byte{0x06}, // 授权命令
        Timeout:      25 * time.Millisecond,
    }
    
    if err := poller.SendCommand(cmd); err != nil {
        logger.Error("Failed to send command", zap.Error(err))
    }
    
    // 7. 处理结果
    go func() {
        for result := range poller.GetResultChannel() {
            logger.Info("Received result",
                zap.String("device_id", result.DeviceID),
                zap.Bool("success", result.Success),
                zap.Duration("response_time", result.ResponseTime))
        }
    }()
}
```

### 高级配置

```go
// 创建自定义帧构建器
frameBuilder := v2.NewWayneFrameBuilder("v1.3", "wayne_pump")

// 创建连接管理器配置
connConfig := connection.NewDefaultConnectionManagerConfig()
connConfig.MaxConnections = 1
connConfig.DARTConfig.SerialConfig.Port = "/dev/ttyUSB0"

// 创建轮询器
poller := v2.NewDevicePoller(config, deviceSM, frameBuilder, logger)
```

## 配置说明

### DevicePollerConfig

| 字段 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| DeviceInfo | v2models.DeviceInfo | 设备基础信息 | 必填 |
| PollInterval | time.Duration | 轮询间隔 | 100ms |
| PollTimeout | time.Duration | 轮询超时 | 25ms |
| MaxRetries | int | 最大重试次数 | 3 |
| RetryDelay | time.Duration | 重试延迟 | 10ms |
| WatchdogTimeout | time.Duration | 看门狗超时 | 5s |
| BufferSize | int | 通道缓冲区大小 | 10 |

### 串口配置

| 字段 | 类型 | 说明 | DART要求 |
|------|------|------|----------|
| Port | string | 串口端口 | 根据系统配置 |
| BaudRate | int | 波特率 | 9600 或 19200 |
| DataBits | int | 数据位 | 8 |
| StopBits | int | 停止位 | 1 |
| Parity | ParityType | 校验位 | 奇校验 |
| Timeout | time.Duration | 响应超时 | ≤25ms |

## API接口

### DevicePollerInterface

```go
type DevicePollerInterface interface {
    // 生命周期管理
    Start(ctx context.Context) error
    Stop() error
    IsRunning() bool
    
    // 命令发送
    SendCommand(cmd PollCommand) error
    
    // 结果获取
    GetResultChannel() <-chan PollResult
    
    // 状态查询
    GetStats() PollerStats
    GetCurrentBusiness() *BusinessState
    GetPollerContext() *PollerContext
    
    // 设备信息
    GetDeviceID() string
    GetLastActivity() time.Time
}
```

### PollCommand

```go
type PollCommand struct {
    Type         string        `json:"type"`          // 命令类型
    BusinessType string        `json:"business_type"` // 业务类型
    Priority     int           `json:"priority"`      // 优先级 1-3
    Data         interface{}   `json:"data"`          // 命令数据
    Timeout      time.Duration `json:"timeout"`       // 超时时间
    Retryable    bool          `json:"retryable"`     // 是否可重试
    MaxRetries   int           `json:"max_retries"`   // 最大重试次数
}
```

### PollResult

```go
type PollResult struct {
    DeviceID     string        `json:"device_id"`     // 设备ID
    Success      bool          `json:"success"`       // 是否成功
    ResponseTime time.Duration `json:"response_time"` // 响应时间
    Data         interface{}   `json:"data"`          // 响应数据
    Error        error         `json:"error"`         // 错误信息
    Timestamp    time.Time     `json:"timestamp"`     // 时间戳
    BusinessType string        `json:"business_type"` // 业务类型
    CommandType  string        `json:"command_type"`  // 命令类型
    StateChanged bool          `json:"state_changed"` // 状态是否变更
}
```

## 流程图

### 统一轮询流程

```mermaid
graph TD
    Start[executeOptimizedPoll] --> CheckCmd{检查命令队列}
    CheckCmd -->|有命令| ExecCmd[executeBusinessCommand]
    CheckCmd -->|无命令| CheckBiz{检查业务持续}
    CheckBiz -->|需要持续| ContBiz[continuePreviousBusiness]
    CheckBiz -->|无业务| RoutinePoll[executeRoutinePoll]
    
    ExecCmd --> HandleCmd[handleCommandResult]
    ContBiz --> HandleBiz[handleBusinessContinuation]
    RoutinePoll --> HandlePoll[handlePollResult]
    
    HandleCmd --> UpdateStats[updateActivity]
    HandleBiz --> UpdateStats
    HandlePoll --> UpdateStats
    UpdateStats --> End[返回下次轮询]
```

### 数据处理流程

```mermaid
graph LR
    RawData[原始数据] --> ParseFrame[解析DART帧]
    ParseFrame --> ParseTrans[解析DC事务]
    ParseTrans --> ProcDC1[处理DC1状态]
    ParseTrans --> ProcDC2[处理DC2体积金额]
    ParseTrans --> ProcDC3[处理DC3价格喷嘴]
    ParseTrans --> ProcDC5[处理DC5报警]
    ParseTrans --> ProcDC101[处理DC101计数器]
    
    ProcDC1 --> UpdateSM[更新状态机]
    ProcDC2 --> UpdateSM
    ProcDC3 --> UpdateSM
    ProcDC5 --> UpdateSM
    ProcDC101 --> UpdateSM
```
### 函数调用关系图

```mermaid
graph LR
    subgraph "生命周期管理"
        NewPoller["NewDevicePoller()"]
        Start["Start()"]
        Stop["Stop()"]
        InitConn["initializeConnection()"]
        CloseConn["closeConnection()"]
    end

    subgraph "核心轮询逻辑"
        UnifiedLoop["unifiedPollLoop()"]
        OptimizedPoll["executeOptimizedPoll()"]
        BizCommand["executeBusinessCommand()"]
        DataCommand["executeDataCommand()"]
        ConfigCommand["executeConfigureCommand()"]
        RoutinePoll["executeRoutinePoll()"]
        ContBiz["continuePreviousBusiness()"]
    end

    subgraph "协议通信"
        SendWait["sendFrameAndWait()"]
        BuildData["frameBuilder.BuildDataFrame()"]
        BuildPoll["frameBuilder.BuildPollFrame()"]
        ParseResp["frameBuilder.ParseResponse()"]
    end

    subgraph "数据解析处理"
        ParseDCTrans["parseDCTransactions()"]
        ProcDCTrans["processDCTransaction()"]
        ProcDeviceData["processDeviceData()"]
        ProcBizData["processBusinessData()"]
    end

    subgraph "DC事务处理"
        ProcDC1["processDC1Status()"]
        ProcDC2["processDC2VolumeAmount()"]
        ProcDC3["processDC3PriceNozzle()"]
        ProcDC5["processDC5Alarm()"]
        ProcDC101["processDC101Counters()"]
    end

    subgraph "业务数据处理"
        ProcAuthBiz["processAuthorizeBusinessData()"]
        ProcPresetBiz["processPresetBusinessData()"]
        ProcFillBiz["processFillingBusinessData()"]
        ProcStatusBiz["processStatusBusinessData()"]
        ProcPriceBiz["processPriceUpdateBusinessData()"]
        ProcGenBiz["processGeneralBusinessData()"]
    end

    subgraph "响应处理"
        ProcCmdResp["processCommandResponse()"]
        ProcPollResp["processPollResponse()"]
        HandleCmdResult["handleCommandResult()"]
        HandlePollResult["handlePollResult()"]
        HandleBizCont["handleBusinessContinuation()"]
    end

    subgraph "业务持续处理"
        ContAuth["continueAuthorizeProcess()"]
        ContPreset["continuePresetProcess()"]
        ContFill["continueFillingProcess()"]
    end

    subgraph "辅助功能"
        SendResult["sendResult()"]
        NotifySM["notifyStateMachine()"]
        UpdateActivity["updateActivity()"]
        UpdateBizStatus["updateBusinessStatus()"]
        GetFrameType["getFrameTypeName()"]
        GetPumpStatus["getPumpStatusName()"]
        GetAlarmCode["getAlarmCodeName()"]
    end

    %% 调用关系
    NewPoller --> InitConn
    Start --> InitConn
    Start --> UnifiedLoop
    Stop --> CloseConn

    UnifiedLoop --> OptimizedPoll
    OptimizedPoll --> BizCommand
    OptimizedPoll --> RoutinePoll
    OptimizedPoll --> ContBiz

    BizCommand --> DataCommand
    BizCommand --> ConfigCommand
    DataCommand --> BuildData
    DataCommand --> SendWait
    RoutinePoll --> BuildPoll
    RoutinePoll --> SendWait

    SendWait --> ParseResp
    BizCommand --> ProcCmdResp
    RoutinePoll --> ProcPollResp

    ProcCmdResp --> HandleCmdResult
    ProcPollResp --> HandlePollResult
    ContBiz --> HandleBizCont

    ContBiz --> ContAuth
    ContBiz --> ContPreset
    ContBiz --> ContFill

    ProcDeviceData --> ParseDCTrans
    ProcBizData --> ParseDCTrans
    ParseDCTrans --> ProcDCTrans

    ProcDCTrans --> ProcDC1
    ProcDCTrans --> ProcDC2
    ProcDCTrans --> ProcDC3
    ProcDCTrans --> ProcDC5
    ProcDCTrans --> ProcDC101

    ProcBizData --> ProcAuthBiz
    ProcBizData --> ProcPresetBiz
    ProcBizData --> ProcFillBiz
    ProcBizData --> ProcStatusBiz
    ProcBizData --> ProcPriceBiz
    ProcBizData --> ProcGenBiz

    HandleCmdResult --> NotifySM
    HandlePollResult --> NotifySM
    HandleBizCont --> NotifySM
    NotifySM --> SendResult
    SendResult --> UpdateActivity

    %% 样式
    classDef lifecycle fill:#ffcdd2
    classDef coreLogic fill:#e8f5e8
    classDef protocol fill:#e1f5fe
    classDef dataProcessing fill:#fff3e0
    classDef businessLogic fill:#f3e5f5
    classDef auxiliary fill:#e0e0e0

    class NewPoller,Start,Stop,InitConn,CloseConn lifecycle
    class UnifiedLoop,OptimizedPoll,BizCommand,DataCommand,RoutinePoll coreLogic
    class SendWait,BuildData,BuildPoll,ParseResp protocol
    class ParseDCTrans,ProcDCTrans,ProcDeviceData,ProcBizData,ProcDC1,ProcDC2,ProcDC3,ProcDC5,ProcDC101 dataProcessing
    class ProcAuthBiz,ProcPresetBiz,ProcFillBiz,ProcStatusBiz,ProcPriceBiz,ContAuth,ContPreset,ContFill businessLogic
    class SendResult,NotifySM,UpdateActivity,GetFrameType,GetPumpStatus auxiliary
```

### 典型操作时序图

```mermaid
sequenceDiagram
    participant Client
    participant DevicePoller as DevicePoller
    participant PollerContext as PollerContext
    participant ConnectionMgr as ConnectionManager
    participant SerialConn as SerialConnection
    participant FrameBuilder as FrameBuilder
    participant TransBuilder as TransactionBuilder
    participant DeviceSM as DeviceStateMachine
    participant StatsCollector as StatsCollector

    Note over DevicePoller: 轮询器启动流程
    Client->>DevicePoller: Start()
    DevicePoller->>ConnectionMgr: GetConnection()
    ConnectionMgr->>SerialConn: createConnection()
    SerialConn-->>DevicePoller: connection established
    DevicePoller->>DevicePoller: unifiedPollLoop()

    Note over DevicePoller: 例行轮询流程
    loop 轮询循环
        DevicePoller->>PollerContext: GetNextCommand()
        PollerContext-->>DevicePoller: null (无命令)
        DevicePoller->>PollerContext: ShouldContinueBusiness()
        PollerContext-->>DevicePoller: false (无业务)
        DevicePoller->>DevicePoller: executeRoutinePoll()
        DevicePoller->>FrameBuilder: BuildPollFrame()
        FrameBuilder-->>DevicePoller: pollFrame
        DevicePoller->>SerialConn: SendFrame(pollFrame)
        SerialConn-->>DevicePoller: responseData
        DevicePoller->>FrameBuilder: ParseResponse(responseData)
        FrameBuilder-->>DevicePoller: responseFrame
        DevicePoller->>DevicePoller: processPollResponse()
        alt DATA响应
            DevicePoller->>DevicePoller: parseDCTransactions()
            DevicePoller->>TransBuilder: ParseDC1/DC2/DC3()
            TransBuilder-->>DevicePoller: dcTransactions
            DevicePoller->>DevicePoller: processDCTransaction()
            DevicePoller->>DeviceSM: UpdatePumpStatus()
        end
        DevicePoller->>StatsCollector: UpdateStats()
        DevicePoller->>DevicePoller: updateActivity()
    end

    Note over DevicePoller: 命令处理流程
    Client->>DevicePoller: SendCommand(authCmd)
    DevicePoller->>PollerContext: AddCommand(authCmd)
    
    Note over DevicePoller: 下一轮询周期
    DevicePoller->>PollerContext: GetNextCommand()
    PollerContext-->>DevicePoller: authCmd
    DevicePoller->>DevicePoller: executeBusinessCommand()
    DevicePoller->>FrameBuilder: BuildDataFrame()
    FrameBuilder-->>DevicePoller: dataFrame
    DevicePoller->>SerialConn: SendFrame(dataFrame)
    SerialConn-->>DevicePoller: responseData
    DevicePoller->>FrameBuilder: ParseResponse(responseData)
    FrameBuilder-->>DevicePoller: responseFrame
    alt ACK响应
        DevicePoller->>DevicePoller: processCommandResponse()
        DevicePoller->>DevicePoller: updateBusinessStatus("accepted")
    else DATA响应
        DevicePoller->>DevicePoller: processBusinessData()
        DevicePoller->>DevicePoller: processAuthorizeBusinessData()
    end
    DevicePoller->>StatsCollector: UpdateStats()
    DevicePoller->>Client: PollResult (via resultChan)

    Note over DevicePoller: 业务持续处理
    loop 业务持续
        DevicePoller->>PollerContext: ShouldContinueBusiness()
        PollerContext-->>DevicePoller: true
        DevicePoller->>DevicePoller: continuePreviousBusiness()
        DevicePoller->>DevicePoller: continueAuthorizeProcess()
    end

    Note over DevicePoller: 关闭流程
    Client->>DevicePoller: Stop()
    DevicePoller->>ConnectionMgr: ReleaseConnection()
    DevicePoller->>ConnectionMgr: Close()
```

### 数据流图
```mermaid
graph TD
    subgraph "输入数据源"
        ExtCmd["外部命令<br/>PollCommand"]
        SerialData["串口原始数据<br/>[]byte"]
        TimerEvent["定时器事件<br/>Ticker"]
    end

    subgraph "数据转换层"
        CmdQueue["命令队列<br/>PollerContext.PendingCommands"]
        FrameData["DART协议帧<br/>dartline.Frame"]
        DCTrans["DC事务<br/>pump.DCTransaction"]
    end

    subgraph "业务处理层"
        BizState["业务状态<br/>BusinessState"]
        DeviceStatus["设备状态<br/>DeviceStateData"]
        PollStats["轮询统计<br/>PollerStats"]
    end

    subgraph "输出数据"
        PollResult["轮询结果<br/>PollResult"]
        StateUpdate["状态更新<br/>StateEvent"]
        LogData["日志数据<br/>zap.Logger"]
    end

    %% 数据流向
    ExtCmd --> CmdQueue
    TimerEvent --> CmdQueue
    
    CmdQueue --> FrameData
    SerialData --> FrameData
    
    FrameData --> DCTrans
    DCTrans --> BizState
    DCTrans --> DeviceStatus
    
    BizState --> PollResult
    DeviceStatus --> PollResult
    DCTrans --> PollStats
    
    PollResult --> StateUpdate
    PollResult --> LogData

    subgraph "具体数据类型"
        DC1["DC1: 泵状态<br/>status_code: byte"]
        DC2["DC2: 体积金额<br/>volume_bcd: [4]byte<br/>amount_bcd: [4]byte"]
        DC3["DC3: 价格喷嘴<br/>price_bcd: [3]byte<br/>nozzle_io: byte"]
        DC5["DC5: 报警代码<br/>alarm_code: byte"]
        DC101["DC101: 计数器<br/>counter_type: byte<br/>counter_value: [5]byte"]
    end

    DCTrans --> DC1
    DCTrans --> DC2
    DCTrans --> DC3
    DCTrans --> DC5
    DCTrans --> DC101

    subgraph "业务数据流"
        AuthFlow["授权业务流<br/>BusinessTypeAuthorize"]
        PresetFlow["预设业务流<br/>BusinessTypePreset"]
        FillFlow["加油业务流<br/>BusinessTypeFilling"]
        StatusFlow["状态查询流<br/>BusinessTypeStatus"]
        PriceFlow["价格更新流<br/>BusinessTypePriceUpdate"]
    end

    BizState --> AuthFlow
    BizState --> PresetFlow
    BizState --> FillFlow
    BizState --> StatusFlow
    BizState --> PriceFlow

    subgraph "数据处理函数映射"
        ProcessAuth["processAuthorizeBusinessData()"]
        ProcessPreset["processPresetBusinessData()"]
        ProcessFill["processFillingBusinessData()"]
        ProcessStatus["processStatusBusinessData()"]
        ProcessPrice["processPriceUpdateBusinessData()"]
    end

    AuthFlow --> ProcessAuth
    PresetFlow --> ProcessPreset
    FillFlow --> ProcessFill
    StatusFlow --> ProcessStatus
    PriceFlow --> ProcessPrice

    %% 样式
    classDef inputData fill:#ffcdd2
    classDef transformData fill:#e1f5fe
    classDef businessData fill:#e8f5e8
    classDef outputData fill:#fff3e0
    classDef dcTypes fill:#f3e5f5
    classDef businessFlow fill:#e0f2f1
    classDef processFunc fill:#fce4ec

    class ExtCmd,SerialData,TimerEvent inputData
    class CmdQueue,FrameData,DCTrans transformData
    class BizState,DeviceStatus,PollStats businessData
    class PollResult,StateUpdate,LogData outputData
    class DC1,DC2,DC3,DC5,DC101 dcTypes
    class AuthFlow,PresetFlow,FillFlow,StatusFlow,PriceFlow businessFlow
    class ProcessAuth,ProcessPreset,ProcessFill,ProcessStatus,ProcessPrice processFunc
```
## 监控和调试

### 统计信息

```go
// 获取轮询统计
stats := poller.GetStats()
fmt.Printf("总轮询次数: %d\n", stats.TotalPolls)
fmt.Printf("成功率: %.2f%%\n", stats.SuccessRate)
fmt.Printf("平均响应时间: %v\n", stats.AverageLatency)
fmt.Printf("超时率: %.2f%%\n", stats.TimeoutRate)
```

### 日志配置

```go
// 开发环境
logger := zap.NewDevelopment()

// 生产环境
config := zap.NewProductionConfig()
config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
logger, _ := config.Build()
```

### 性能监控

- **响应时间**: 监控每次轮询的响应时间
- **成功率**: 统计轮询成功率和失败率
- **连接状态**: 监控串口连接健康状态
- **业务状态**: 跟踪当前业务处理状态

## 故障处理

### 常见问题

1. **串口连接失败**
   - 检查串口设备是否存在
   - 验证波特率和协议参数
   - 确认设备地址配置

2. **响应超时**
   - 检查设备电源和连接
   - 验证DART协议参数
   - 调整超时配置

3. **协议解析错误**
   - 检查设备地址范围(0x50-0x6F)
   - 验证CRC校验
   - 检查帧格式是否正确

### 调试技巧

```go
// 启用详细日志
logger := zap.NewDevelopment()
logger = logger.With(zap.String("component", "device_poller"))

// 监控命令执行
go func() {
    for result := range poller.GetResultChannel() {
        if !result.Success {
            logger.Error("Command failed",
                zap.String("business_type", result.BusinessType),
                zap.Error(result.Error),
                zap.Duration("response_time", result.ResponseTime))
        }
    }
}()

// 检查连接状态
if !poller.IsRunning() {
    logger.Warn("Poller is not running")
}

// 获取当前业务状态
if business := poller.GetCurrentBusiness(); business != nil {
    logger.Info("Current business",
        zap.String("type", string(business.Type)),
        zap.String("status", business.GetStatus()))
}
```

## 开发指南

### 扩展业务类型

1. 在`polling_types.go`中添加新的`BusinessType`常量
2. 实现对应的`process*BusinessData()`方法
3. 在`processBusinessData()`中添加新的case分支

### 扩展DC事务类型

1. 在pump包中添加新的`TransactionType`常量
2. 实现对应的`ParseDC*`和`process*`方法
3. 在`parseDCTransactions()`中添加处理逻辑

### 自定义连接类型

1. 实现`SerialManagerInterface`接口
2. 扩展`ConnectionManager`支持新的连接类型
3. 在`initializeConnection()`中添加初始化逻辑

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进DevicePoller。

---

**DevicePoller v2.0** - 为Wayne DART协议设备提供高性能、高可靠性的轮询解决方案。 