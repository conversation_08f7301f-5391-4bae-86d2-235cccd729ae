# 产品需求文档：前庭控制系统 (FCC - Forecourt Controller System)

## 1. 概述

### 1.1 问题陈述

**业务背景**：
随着加油站业务规模的扩大和设备数量的增加，传统的分散式设备管理方式面临诸多挑战：
- 设备控制分散，缺乏统一管理和协调
- 交易流程复杂，涉及多个独立系统，容易出现数据不一致
- 设备故障难以及时发现和处理，影响客户体验
- 离线场景下无法保证业务连续性
- 设备间数据同步复杂，运维成本高

**核心问题**：
- 缺乏统一的前庭设备控制能力提供者，BOS 系统难以标准化设备操作
- 设备控制接口不统一，业务系统需要适配多种设备协议
- 设备通信协议多样化，兼容性和稳定性问题突出
- 网络中断时设备控制能力中断，无法支持离线模式运行

**问题影响**：
- 设备故障平均处理时间长，影响 15-20% 的客户体验
- 交易数据不一致导致的对账问题，每月影响约 2-3% 的交易准确性
- 设备运维成本高，需要专业技术人员现场处理问题
- 网络故障导致的业务中断，每次影响 30-60 分钟的营业收入

### 1.2 解决方案范围

**功能范围**：
- 包含：设备通信管理、设备控制能力提供、实时数据采集与上报、离线模式支持
- 包含：油枪控制能力、ATG 数据采集、POS/EDC 通信接口、Totem 显示控制
- 包含：设备状态监控、异常检测与上报、数据完整性验证
- 不包含：业务流程逻辑（由 BOS 中台实现）、财务结算系统、库存管理业务流程

**用户范围**：
- 主要用户：BOS 中台系统（通过 API 调用 FCC 能力）
- 直接用户：站点管理员（设备监控和维护）、系统管理员
- 最终受益用户：加油站操作员工、加油客户

**场景范围**：
- 适用场景：标准加油站前庭设备控制、多品牌设备兼容、7×24 小时连续运行
- 支持场景：网络中断离线模式、设备故障自动切换、紧急情况处理
- 不适用：非标准协议设备、特殊定制化设备

### 1.3 预期价值

**业务价值**：
- 设备故障处理时间减少 60%，从平均 30 分钟降至 12 分钟
- 交易数据准确性提升至 99.5% 以上
- 运维成本降低 40%，减少现场维护次数
- 网络故障业务中断时间减少 80%，从 30-60 分钟降至 5-10 分钟

**用户价值**：
- 客户体验改善：加油等待时间减少 25%
- 操作员效率提升：设备操作复杂度降低 50%
- 管理员监控效率：实时监控覆盖率 100%，异常响应时间<1 分钟

**成功指标**：
- 系统可用性 ≥ 99.9%
- 交易处理成功率 ≥ 99.8%
- 设备控制响应时间 ≤ 100ms
- 离线模式支持时长 ≥ 24 小时

## 2. 核心功能

### 2.1 功能清单

按优先级排序的核心功能：

1. **P0 级功能（必须有）**：
   - 设备通信管理
   - 油枪控制功能
   - 交易数据实时获取
   - 交易状态机管理

2. **P1 级功能（重要）**：
   - ATG 数据采集与监控
   - POS/EDC 设备集成
   - 员工授权管理
   - 异常处理与恢复

3. **P2 级功能（有用）**：
   - 离线模式支持
   - Totem 显示控制
   - 硬件兼容性保护
   - 高级数据分析

### 2.2 功能详述

#### 设备通信管理

**功能描述**：建立和维护与各类前庭设备的稳定通信连接，支持 Wayne、Gilbarco 等主流协议，实现设备统一接入和管理。

**业务重要性**：作为整个系统的通信基础，确保所有设备能够正常接入和控制。设备通信的稳定性直接影响整个加油站的正常运营，是系统的核心基础功能。

**实现原理**：
1. 通过 Vhub/Pts-2 产品建立与设备的物理连接
2. 自动识别设备类型和协议版本
3. 维护设备映射关系和通信状态
4. 实现设备故障自动检测和切换
5. 提供统一的设备控制接口

**验收标准**：
- 支持同时连接不少于 20 个前庭设备
- 设备连接建立时间 ≤ 3 秒
- 通信中断后自动重连成功率 ≥ 95%
- 设备状态检测周期 ≤ 5 秒
- 支持 Wayne 和 Gilbarco 协议的主要版本

**依赖关系**：依赖 Vhub/Pts-2 硬件产品、网络基础设施

#### 油枪控制功能

**功能描述**：实现对加油枪泵的精确控制，包括启动、停止、暂停操作，支持预设金额、升数控制和安全联锁功能。

**业务重要性**：油枪控制能力是 FCC 的核心功能，为 BOS 系统提供稳定可靠的设备控制接口。精确的控制和快速的响应是整个加油业务流程的基础保障。

**实现原理**：
1. 接收 BOS 系统的设备控制 API 调用
2. 验证 API 参数和调用权限
3. 执行设备安全检查和状态验证
4. 向油枪发送底层控制信号
5. 实时监控设备执行状态并回调通知
6. 提供紧急停止和设备保护功能

**验收标准**：
- 控制指令响应时间 ≤ 100ms
- 预设条件精度误差 ≤ 0.1%
- 安全联锁响应时间 ≤ 50ms
- 支持同时控制 8 个以上油枪
- 紧急停止成功率 100%

**依赖关系**：依赖设备通信管理、员工授权管理、安全系统

#### 交易数据实时获取

**功能描述**：实时采集和处理加油过程中的流量、累计器、价格等关键数据，确保交易数据的准确性和完整性。

**业务重要性**：交易数据是业务结算的基础，数据的准确性和实时性直接影响客户权益和商业利润。同时也是监管合规的重要依据。

**实现原理**：
1. 从油枪传感器实时读取流量数据
2. 获取累计器读数和变化量
3. 结合当前价格计算交易金额
4. 实时验证数据的合理性
5. 将数据同步给相关系统
6. 处理数据异常和累计器跳变

**验收标准**：
- 数据采集频率 ≥ 1 次/秒
- 数据显示延迟 ≤ 200ms
- 累计器数据精度满足国家计量标准
- 数据完整性验证通过率 ≥ 99.9%
- 异常数据检测和处理成功率 ≥ 95%

**依赖关系**：依赖油枪控制功能、价格管理系统、数据验证规则

#### ATG 数据采集与监控

**功能描述**：持续采集 ATG 设备的油罐液位、温度、密度、水位等数据，提供实时状态监控和异常预警功能。

**业务重要性**：ATG 数据是库存管理和安全监控的重要依据，及时发现异常情况可以避免安全事故和经济损失，确保加油站的安全运营。

**实现原理**：
1. 定期从 ATG 设备采集传感器数据
2. 对数据进行有效性验证和过滤
3. 分析数据趋势和异常模式
4. 根据预设阈值生成告警
5. 将数据同步给库存管理系统
6. 提供历史数据查询和分析

**验收标准**：
- 数据采集周期 ≤ 5 秒
- 异常检测响应时间 ≤ 1 秒
- 液位测量精度 ≤ ±2mm
- 温度测量精度 ≤ ±0.5°C
- 预警通知发送成功率 ≥ 99%

**依赖关系**：依赖设备通信管理、告警系统、库存管理系统

#### 交易状态机管理

**功能描述**：管理完整的交易生命周期，控制交易状态的合理转换，确保交易流程的正确执行和异常恢复。

**业务重要性**：交易状态机是保证交易流程正确性的核心机制，防止交易状态混乱和数据不一致，是交易安全和数据完整性的重要保障。

**实现原理**：
1. 定义完整的交易状态和转换规则
2. 监听各种业务事件和用户操作
3. 验证状态转换的合法性
4. 执行状态转换和相关业务逻辑
5. 处理异常情况和状态恢复
6. 记录完整的状态变化历史

**验收标准**：
- 状态转换响应时间 ≤ 100ms
- 非法状态转换拦截成功率 100%
- 异常状态恢复成功率 ≥ 95%
- 状态变化记录完整性 100%
- 并发状态冲突处理成功率 ≥ 99%

**依赖关系**：依赖油枪控制、支付系统、订单管理系统

## 3. 用户体验设计

### 3.1 用户画像

#### 主要用户角色

**BOS 中台系统**：
- 基本信息：业务处理中台，负责加油站业务流程编排和逻辑处理
- 需求痛点：需要稳定可靠的设备控制能力、实时准确的设备数据、快速的响应能力
- 使用场景：调用 FCC API 执行设备控制、获取设备状态、处理设备异常
- 成功指标：API 调用成功率 ≥ 99.9%、响应时间 ≤ 100ms、数据准确率 ≥ 99.9%

**站点管理员**：
- 基本信息：具备一定技术背景，负责站点设备管理和运营监控
- 需求痛点：设备状态难以掌握、异常发现不及时、设备维护困难
- 使用场景：设备状态监控、异常处理、设备维护、系统配置
- 成功指标：监控效率提升 70%、异常响应时间减少 80%、设备可用率 ≥ 99%

**系统管理员**：
- 基本信息：技术运维人员，负责 FCC 系统的部署、配置和维护
- 需求痛点：系统部署复杂、配置管理困难、故障排查效率低
- 使用场景：系统部署、参数配置、性能监控、故障诊断
- 成功指标：部署时间减少 50%、配置效率提升 60%、故障定位时间减少 70%

### 3.2 关键API调用流程

#### BOS 调用 FCC 设备控制能力

**流程名称**：BOS 调用设备控制 API
**目标**：BOS 通过 FCC 提供的 API 实现设备控制
**参与角色**：BOS 中台、FCC 系统
**前置条件**：FCC 系统正常运行、设备连接正常

**主要步骤**：
1. [BOS 请求]：调用设备控制 API - [FCC 响应]：验证请求参数和权限
2. [FCC 处理]：执行安全检查和设备状态验证 - [FCC 响应]：返回设备准备状态
3. [BOS 确认]：发送控制指令 - [FCC 执行]：向设备发送控制命令
4. [FCC 监控]：实时监控设备执行状态 - [FCC 回调]：通过回调接口上报状态变化
5. [设备响应]：设备执行完成或异常 - [FCC 通知]：实时推送执行结果给 BOS
6. [FCC 记录]：记录操作日志和设备状态 - [BOS 接收]：获取完整的操作记录

**后置条件**：设备状态已更新，操作日志已记录，BOS 获得执行结果
**异常处理**：
- 设备无响应：FCC 返回设备故障状态，BOS 决定处理策略
- 参数错误：FCC 返回参数验证错误，BOS 重新组织请求
- 网络中断：FCC 启动离线模式，BOS 后续同步离线数据

#### FCC 数据采集与上报流程

**流程名称**：实时数据采集与主动上报
**目标**：FCC 持续采集设备数据并主动上报给 BOS
**参与角色**：FCC 系统、设备、BOS 中台
**前置条件**：设备通信正常、数据上报通道畅通

**主要步骤**：
1. [FCC 采集]：定时从设备采集状态和交易数据 - [设备响应]：返回实时数据
2. [FCC 验证]：数据有效性验证和异常检测 - [FCC 处理]：过滤无效数据，标记异常
3. [FCC 格式化]：数据标准化处理和格式转换 - [FCC 缓存]：临时存储待上报数据
4. [FCC 上报]：通过 API 推送数据给 BOS - [BOS 接收]：接收并确认数据
5. [异常检测]：发现异常立即触发告警 - [FCC 通知]：实时推送异常事件
6. [数据持久化]：本地保存数据副本 - [同步确认]：确认 BOS 已成功接收

**后置条件**：数据已成功上报，异常已及时通知，本地数据已备份
**异常处理**：
- 数据采集失败：使用上次有效数据，记录异常日志
- 上报失败：数据缓存到本地，网络恢复后重传
- 异常检测：立即告警，同时提供设备状态详情

### 3.3 界面交互要求

**界面布局**：
- 采用直观的图形化界面，支持触摸屏操作
- 关键信息大字体显示，适合户外环境查看
- 设备状态采用颜色编码：绿色（正常）、黄色（警告）、红色（故障）
- 操作按钮大小适中，防止误操作

**交互方式**：
- 支持触摸、按键、刷卡等多种交互方式
- 提供语音提示和文字提示双重反馈
- 关键操作需要二次确认，防止误操作
- 支持快捷键和批量操作，提高操作效率

**信息反馈**：
- 操作响应时间 ≤ 200ms，提供即时反馈
- 异常情况提供明确的错误提示和处理建议
- 重要状态变化提供声光提示
- 定期显示系统状态和健康信息

**可访问性**：
- 支持多语言界面切换
- 提供高对比度显示选项
- 支持屏幕阅读器和辅助功能
- 界面元素符合无障碍设计标准

## 4. 业务规则与约束

### 4.1 核心业务规则

**设备控制规则**：
- API 调用频率限制：单设备最大 10 次/秒
- 设备控制指令超时：5 秒无响应视为失败
- 并发控制限制：单设备同时只能执行一个控制指令
- 预设参数精度：金额精度 0.01 元，升数精度 0.01 升

**设备安全规则**：
- 紧急停止响应时间：≤ 50ms
- 累计器异常跳变阈值：>正常值的 10 倍
- ATG 异常告警阈值：液位变化 >10cm/小时
- 设备故障自动切换时间：≤ 3 秒

**API 访问规则**：
- API 调用需要有效的认证令牌
- 不同操作需要相应的权限级别
- 敏感操作需要双重验证
- API 调用日志必须完整记录

**数据采集规则**：
- 设备数据采集频率：1-60 秒可配置
- 数据异常检测阈值可配置
- 数据本地缓存时长：72 小时
- 数据上报重试次数：最多 3 次

### 4.2 业务约束

**API 权限控制**：
- 系统级 API：设备配置、系统管理功能
- 管理级 API：设备监控、状态查询功能  
- 操作级 API：基础设备控制功能
- 只读级 API：数据查询、状态获取功能

**数据完整性**：
- 设备数据必须实时备份到本地
- 操作日志必须完整记录，不可删除或修改
- 累计器数据必须连续，异常跳变需要标记
- 数据上报必须确保不丢失、不重复

**设备管控约束**：
- 设备控制指令必须经过安全检查
- 异常设备必须自动隔离和告警
- 设备维护模式下禁止业务操作
- 关键设备故障必须立即上报

**合规要求**：
- 符合国家计量法规和标准
- 满足消防安全规范要求
- 遵循数据保护和隐私法规
- 符合行业监管和审计要求

## 5. 非功能性需求

### 5.1 性能需求

**响应时间**：
- 设备控制指令响应：≤ 100ms
- 数据查询响应：≤ 500ms
- 界面操作响应：≤ 200ms
- 告警通知响应：≤ 1 秒

**吞吐量**：
- 并发交易处理：≥ 8 笔/同时
- 数据采集频率：≥ 1 次/秒/设备
- 消息处理能力：≥ 1000 条/秒
- 日志记录能力：≥ 10000 条/分钟

**并发连接**：
- BOS 中台系统：10 个并发 API 连接
- 管理员监控：5 个并发 Web 连接
- 第三方系统：3 个并发 API 连接
- 设备数据上报：无限制

**数据量**：
- 日交易量：≥ 500 笔
- 日志数据：≥ 10MB/天
- 历史数据保存：5 年
- 实时数据缓存：24 小时

### 5.2 可靠性需求

**可用性**：
- 系统可用性：≥ 99.9%（年停机时间 ≤ 8.76 小时）
- 核心功能可用性：≥ 99.95%
- 计划维护时间：≤ 4 小时/月
- 故障恢复时间：≤ 5 分钟

**容错性**：
- 单点设备故障不影响整体系统运行
- 网络中断后自动恢复，数据不丢失
- 关键数据自动备份，支持快速恢复
- 异常情况下系统自动降级运行

**数据安全**：
- 数据加密存储和传输
- 定期数据备份和恢复测试
- 访问日志完整记录和审计
- 敏感数据脱敏处理

### 5.3 可用性需求

**易用性**：
- 新用户培训时间：≤ 2 小时
- 常用操作步骤：≤ 3 步
- 错误操作恢复：≤ 1 分钟
- 界面直观度：用户满意度 ≥ 85%

**学习成本**：
- 操作员上手时间：≤ 1 天
- 管理员掌握时间：≤ 3 天
- 在线帮助覆盖率：100%
- 操作手册完整性：100%

**操作效率**：
- 日常操作效率提升：≥ 50%
- 异常处理时间减少：≥ 60%
- 数据查询速度提升：≥ 80%
- 报表生成时间减少：≥ 70%

### 5.4 兼容性需求

**设备兼容**：
- 支持 Wayne 和 Gilbarco 主流协议
- 兼容不同型号的 ATG 设备
- 支持多种品牌的 POS/EDC 设备
- 适配各种规格的 Totem 显示设备

**系统集成**：
- 与现有 BOS 系统无缝集成
- 支持 HOS 总部系统连接
- 兼容边缘计算网关
- 支持第三方监控系统

**环境兼容**：
- 工作温度：-20°C 到 +60°C
- 防护等级：IP54 以上
- 电磁兼容：符合国家 EMC 标准
- 防爆要求：符合加油站防爆规范

## 6. 验收标准

### 6.1 功能验收

**基础功能测试**：
- 设备连接成功率：≥ 95%
- 设备控制成功率：≥ 99%
- 数据采集准确率：≥ 99.9%
- 交易处理成功率：≥ 99.8%

**异常处理测试**：
- 网络中断恢复测试：100% 通过
- 设备故障切换测试：100% 通过
- 数据异常处理测试：≥ 95% 通过
- 紧急停止响应测试：100% 通过

**集成测试**：
- BOS 系统集成测试：100% 通过
- 支付系统集成测试：100% 通过
- 第三方设备兼容测试：≥ 90% 通过
- 多系统协同测试：100% 通过

### 6.2 用户体验验收

**可用性测试**：
- 用户任务完成率：≥ 95%
- 用户满意度评分：≥ 4.0/5.0
- 操作错误率：≤ 5%
- 学习曲线：符合预期目标

**界面测试**：
- 界面响应速度：符合性能要求
- 显示效果：在各种环境下清晰可见
- 交互体验：操作流畅自然
- 多语言支持：100% 功能覆盖

### 6.3 性能验收

**性能基准测试**：
- 响应时间：100% 满足需求指标
- 吞吐量：100% 满足需求指标
- 并发能力：100% 满足需求指标
- 资源使用：CPU ≤ 70%，内存 ≤ 80%

**稳定性测试**：
- 7×24 小时稳定运行测试
- 大负载压力测试
- 长期运行稳定性测试
- 故障恢复能力测试

**安全测试**：
- 权限控制测试：100% 通过
- 数据安全测试：100% 通过
- 网络安全测试：100% 通过
- 审计功能测试：100% 通过

## 附录

### A. 术语表

| 术语 | 定义 |
|------|------|
| FCC | 前庭控制系统 (Forecourt Controller System) |
| ATG | 自动罐量计 (Automatic Tank Gauge) |
| POS | 销售点终端 (Point of Sale) |
| EDC | 电子数据采集器 (Electronic Data Capture) |
| BOS | 后台运营系统 (Back Office System) |
| HOS | 总部运营系统 (Head Office System) |
| Totem | 立式显示设备 |
| Vhub/Pts-2 | 设备通信网关产品 |
| 累计器 | 记录加油总量的计量设备 |
| RFID/NFC | 射频识别/近场通信技术 |

### B. 参考资料

- 《加油站技术规范》GB 50156-2012
- 《燃油加油机检定规程》JJG 443-2015
- 《石油产品储运技术规范》
- Wayne 设备通信协议规范
- Gilbarco 设备通信协议规范

### C. 变更记录

| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| v1.0 | 2024-12-19 | 初始版本创建 | 系统分析师 | 