# FCC后台管理系统 - React + Next.js 设计方案

## 🎯 项目概述

基于 React + Next.js 构建的FCC后台管理系统，采用模块化设计理念，支持组件和功能模块的快速迁移。

### 核心特性
- ⚡ **Next.js 13+ App Router** - 现代化路由和布局系统
- 🧩 **高度模块化** - 独立的组件库和业务模块
- 🔄 **可迁移性** - 零耦合的功能模块设计
- 🎨 **TypeScript** - 类型安全的开发体验
- 📱 **响应式设计** - 支持桌面和平板设备

## 🏗️ 技术架构

### 技术栈选择
```json
{
  "framework": "Next.js 14+",
  "language": "TypeScript 5+",
  "styling": "Tailwind CSS 3+",
  "ui": "Headless UI + Radix UI",
  "state": "Zustand + TanStack Query",
  "forms": "React Hook Form + Zod",
  "charts": "Recharts + D3.js",
  "websocket": "Socket.io Client",
  "testing": "Jest + React Testing Library"
}
```

### 模块化设计原则
1. **单一职责** - 每个模块只负责一个业务领域
2. **零耦合** - 模块间通过标准接口通信
3. **可配置** - 通过配置文件适配不同项目
4. **可插拔** - 支持模块的动态加载和卸载

## 📁 项目结构

```
/fcc-admin-next
├── 📁 apps/
│   └── 📁 fcc-admin/                    # FCC管理后台应用
│       ├── 📁 app/                      # Next.js 13+ App Router
│       │   ├── 📁 dashboard/            # 仪表板页面
│       │   ├── 📁 controllers/          # 控制器管理页面
│       │   ├── 📁 devices/              # 设备管理页面
│       │   ├── 📁 monitor/              # 系统监控页面
│       │   ├── 📁 api/                  # API 路由
│       │   ├── layout.tsx               # 根布局
│       │   ├── page.tsx                 # 首页
│       │   └── loading.tsx              # 加载页面
│       ├── 📁 components/               # 应用级组件
│       │   ├── 📁 layout/               # 布局组件
│       │   ├── 📁 pages/                # 页面组件
│       │   └── 📁 features/             # 功能组件
│       ├── 📁 lib/                      # 应用工具函数
│       ├── 📁 hooks/                    # 应用级Hooks
│       ├── 📁 stores/                   # 状态管理
│       ├── 📁 config/                   # 配置文件
│       ├── 📁 types/                    # TypeScript类型
│       ├── next.config.js               # Next.js配置
│       └── package.json
├── 📁 packages/                         # 共享包(模块化核心)
│   ├── 📁 fcc-ui/                       # FCC UI组件库
│   │   ├── 📁 src/
│   │   │   ├── 📁 components/           # 通用UI组件
│   │   │   │   ├── 📁 data-display/     # 数据展示组件
│   │   │   │   ├── 📁 feedback/         # 反馈组件
│   │   │   │   ├── 📁 forms/            # 表单组件
│   │   │   │   ├── 📁 layout/           # 布局组件
│   │   │   │   └── 📁 navigation/       # 导航组件
│   │   │   ├── 📁 hooks/                # UI相关Hooks
│   │   │   ├── 📁 styles/               # 样式文件
│   │   │   ├── 📁 utils/                # 工具函数
│   │   │   └── index.ts                 # 导出入口
│   │   └── package.json
│   ├── 📁 fcc-core/                     # FCC核心业务模块
│   │   ├── 📁 src/
│   │   │   ├── 📁 modules/              # 业务模块
│   │   │   │   ├── 📁 controllers/      # 控制器管理模块
│   │   │   │   ├── 📁 devices/          # 设备管理模块
│   │   │   │   ├── 📁 commands/         # 命令执行模块
│   │   │   │   └── 📁 monitoring/       # 监控模块
│   │   │   ├── 📁 services/             # 服务层
│   │   │   ├── 📁 types/                # 业务类型定义
│   │   │   ├── 📁 utils/                # 业务工具函数
│   │   │   └── index.ts
│   │   └── package.json
│   ├── 📁 wayne-protocol/               # Wayne协议专用模块
│   │   ├── 📁 src/
│   │   │   ├── 📁 components/           # Wayne专用组件
│   │   │   ├── 📁 hooks/                # Wayne专用Hooks
│   │   │   ├── 📁 types/                # Wayne类型定义
│   │   │   ├── 📁 utils/                # Wayne工具函数
│   │   │   └── index.ts
│   │   └── package.json
│   └── 📁 shared/                       # 共享工具包
│       ├── 📁 src/
│       │   ├── 📁 api/                  # API客户端
│       │   ├── 📁 constants/            # 常量定义
│       │   ├── 📁 types/                # 通用类型
│       │   ├── 📁 utils/                # 通用工具
│       │   └── index.ts
│       └── package.json
├── 📁 docs/                             # 文档
├── 📁 scripts/                          # 构建脚本
├── package.json                         # 根包配置
├── turbo.json                           # Turbo构建配置
└── tsconfig.json                        # TypeScript配置
```

## 🧩 核心模块设计

### 1. FCC UI组件库 (`packages/fcc-ui`)

**可复用的UI组件集合，支持主题定制**

#### 1.1 组件分类

```typescript
// packages/fcc-ui/src/components/data-display/StatusCard.tsx
import React from 'react';
import { cn } from '../../utils/cn';

interface StatusCardProps {
  title: string;
  value: string | number;
  status: 'online' | 'offline' | 'warning' | 'error';
  icon?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

const statusColors = {
  online: 'bg-green-100 text-green-800 border-green-200',
  offline: 'bg-gray-100 text-gray-800 border-gray-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  error: 'bg-red-100 text-red-800 border-red-200',
};

export const StatusCard: React.FC<StatusCardProps> = ({
  title,
  value,
  status,
  icon,
  className,
  onClick,
}) => {
  return (
    <div
      className={cn(
        'rounded-lg border p-4 cursor-pointer transition-all hover:shadow-md',
        statusColors[status],
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium opacity-80">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
        {icon && <div className="text-2xl opacity-60">{icon}</div>}
      </div>
    </div>
  );
};
```

#### 1.2 Wayne专用组件

```typescript
// packages/fcc-ui/src/components/wayne/PumpControlPanel.tsx
import React from 'react';
import { Button } from '../forms/Button';
import { Input } from '../forms/Input';
import { StatusCard } from '../data-display/StatusCard';

interface PumpControlPanelProps {
  deviceId: string;
  deviceName: string;
  status: 'idle' | 'fueling' | 'stopped' | 'error';
  currentPrice?: number;
  currentVolume?: number;
  onSetPrice: (price: number) => void;
  onSetVolume: (volume: number) => void;
  onAuthorize: () => void;
  onStop: () => void;
  disabled?: boolean;
}

export const PumpControlPanel: React.FC<PumpControlPanelProps> = ({
  deviceId,
  deviceName,
  status,
  currentPrice = 0,
  currentVolume = 0,
  onSetPrice,
  onSetVolume,
  onAuthorize,
  onStop,
  disabled = false,
}) => {
  const [price, setPrice] = React.useState(currentPrice);
  const [volume, setVolume] = React.useState(currentVolume);

  const statusMap = {
    idle: { label: '空闲', color: 'online' as const },
    fueling: { label: '加油中', color: 'warning' as const },
    stopped: { label: '已停止', color: 'offline' as const },
    error: { label: '错误', color: 'error' as const },
  };

  return (
    <div className="bg-white rounded-lg border p-6 space-y-4">
      {/* 设备信息 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{deviceName}</h3>
          <p className="text-sm text-gray-500">地址: {deviceId}</p>
        </div>
        <StatusCard
          title="状态"
          value={statusMap[status].label}
          status={statusMap[status].color}
        />
      </div>

      {/* 控制面板 */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">价格设置</label>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={price}
              onChange={(e) => setPrice(Number(e.target.value))}
              step="0.001"
              min="0"
              max="99.999"
              placeholder="0.000"
              disabled={disabled}
            />
            <Button
              onClick={() => onSetPrice(price)}
              disabled={disabled}
              variant="primary"
            >
              设置
            </Button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">预设体积</label>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={volume}
              onChange={(e) => setVolume(Number(e.target.value))}
              step="0.01"
              min="0"
              max="999.99"
              placeholder="0.00"
              disabled={disabled}
            />
            <Button
              onClick={() => onSetVolume(volume)}
              disabled={disabled}
              variant="primary"
            >
              设置
            </Button>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex space-x-3">
        <Button
          onClick={onAuthorize}
          disabled={disabled || status === 'fueling'}
          variant="success"
          className="flex-1"
        >
          💰 授权加油
        </Button>
        <Button
          onClick={onStop}
          disabled={disabled || status === 'idle'}
          variant="danger"
          className="flex-1"
        >
          ⛽ 停止加油
        </Button>
      </div>
    </div>
  );
};
```

### 2. FCC核心业务模块 (`packages/fcc-core`)

**业务逻辑封装，支持跨项目复用**

#### 2.1 控制器管理模块

```typescript
// packages/fcc-core/src/modules/controllers/hooks/useControllers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { controllerService } from '../services/controllerService';
import { Controller, CreateControllerRequest } from '../types';

export const useControllers = (filters?: Record<string, any>) => {
  return useQuery({
    queryKey: ['controllers', filters],
    queryFn: () => controllerService.getControllers(filters),
    staleTime: 30 * 1000, // 30秒
  });
};

export const useController = (controllerId: string) => {
  return useQuery({
    queryKey: ['controller', controllerId],
    queryFn: () => controllerService.getController(controllerId),
    enabled: !!controllerId,
  });
};

export const useCreateController = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateControllerRequest) => 
      controllerService.createController(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['controllers'] });
    },
  });
};

export const useDiscoverDevices = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (controllerId: string) => 
      controllerService.discoverDevices(controllerId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['devices'] });
    },
  });
};
```

#### 2.2 设备管理模块

```typescript
// packages/fcc-core/src/modules/devices/hooks/useDevices.ts
import { useQuery, useMutation } from '@tanstack/react-query';
import { deviceService } from '../services/deviceService';
import { Device, ExecuteCommandRequest } from '../types';

export const useDevices = (controllerId?: string) => {
  return useQuery({
    queryKey: ['devices', controllerId],
    queryFn: () => deviceService.getDevices(controllerId),
  });
};

export const useDevice = (deviceId: string) => {
  return useQuery({
    queryKey: ['device', deviceId],
    queryFn: () => deviceService.getDevice(deviceId),
    enabled: !!deviceId,
  });
};

export const useDeviceStatus = (deviceId: string) => {
  return useQuery({
    queryKey: ['device-status', deviceId],
    queryFn: () => deviceService.getDeviceStatus(deviceId),
    enabled: !!deviceId,
    refetchInterval: 5000, // 5秒自动刷新
  });
};

export const useExecuteCommand = () => {
  return useMutation({
    mutationFn: ({ deviceId, command }: ExecuteCommandRequest) =>
      deviceService.executeCommand(deviceId, command),
  });
};
```

#### 2.3 实时状态管理

```typescript
// packages/fcc-core/src/modules/monitoring/hooks/useRealTimeStatus.ts
import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { webSocketService } from '../services/webSocketService';

interface StatusEvent {
  type: 'device_status' | 'controller_status' | 'command_result';
  deviceId?: string;
  controllerId?: string;
  data: any;
  timestamp: string;
}

export const useRealTimeStatus = (deviceIds?: string[]) => {
  const [isConnected, setIsConnected] = useState(false);
  const [events, setEvents] = useState<StatusEvent[]>([]);
  const queryClient = useQueryClient();

  useEffect(() => {
    const ws = webSocketService.connect();
    
    ws.onopen = () => {
      setIsConnected(true);
      // 订阅设备状态
      if (deviceIds) {
        deviceIds.forEach(deviceId => {
          ws.send(JSON.stringify({
            action: 'subscribe',
            topic: `device.${deviceId}.status`
          }));
        });
      }
    };

    ws.onmessage = (event) => {
      const statusEvent: StatusEvent = JSON.parse(event.data);
      setEvents(prev => [statusEvent, ...prev.slice(0, 99)]); // 保留最新100条
      
      // 更新查询缓存
      if (statusEvent.type === 'device_status' && statusEvent.deviceId) {
        queryClient.setQueryData(
          ['device-status', statusEvent.deviceId],
          statusEvent.data
        );
      }
    };

    ws.onclose = () => {
      setIsConnected(false);
    };

    return () => {
      ws.close();
    };
  }, [deviceIds, queryClient]);

  return { isConnected, events };
};
```

### 3. Wayne协议专用模块 (`packages/wayne-protocol`)

**Wayne DART协议的专用组件和工具**

```typescript
// packages/wayne-protocol/src/components/DARTCommandBuilder.tsx
import React from 'react';
import { Select } from '@fcc/ui';
import { CommandType, DARTCommand } from '../types';

interface DARTCommandBuilderProps {
  deviceAddress: number;
  onCommandChange: (command: DARTCommand) => void;
}

export const DARTCommandBuilder: React.FC<DARTCommandBuilderProps> = ({
  deviceAddress,
  onCommandChange,
}) => {
  const [commandType, setCommandType] = React.useState<CommandType>('CD1');
  const [parameters, setParameters] = React.useState<Record<string, any>>({});

  const commandTypes = [
    { value: 'CD1', label: 'CD1 - 状态查询' },
    { value: 'CD2', label: 'CD2 - 喷嘴选择' },
    { value: 'CD3', label: 'CD3 - 预设体积' },
    { value: 'CD4', label: 'CD4 - 预设金额' },
    { value: 'CD5', label: 'CD5 - 价格设置' },
  ];

  React.useEffect(() => {
    const command: DARTCommand = {
      type: commandType,
      deviceAddress,
      parameters,
      timestamp: new Date().toISOString(),
    };
    onCommandChange(command);
  }, [commandType, deviceAddress, parameters, onCommandChange]);

  const renderParameterInputs = () => {
    switch (commandType) {
      case 'CD3':
        return (
          <div>
            <label className="block text-sm font-medium mb-2">预设体积 (升)</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="999.99"
              value={parameters.volume || ''}
              onChange={(e) => setParameters(prev => ({
                ...prev,
                volume: Number(e.target.value)
              }))}
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
        );
      case 'CD4':
        return (
          <div>
            <label className="block text-sm font-medium mb-2">预设金额 (元)</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="9999.99"
              value={parameters.amount || ''}
              onChange={(e) => setParameters(prev => ({
                ...prev,
                amount: Number(e.target.value)
              }))}
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
        );
      case 'CD5':
        return (
          <div>
            <label className="block text-sm font-medium mb-2">价格 (元/升)</label>
            <input
              type="number"
              step="0.001"
              min="0"
              max="99.999"
              value={parameters.price || ''}
              onChange={(e) => setParameters(prev => ({
                ...prev,
                price: Number(e.target.value)
              }))}
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">命令类型</label>
        <Select
          value={commandType}
          onChange={setCommandType}
          options={commandTypes}
        />
      </div>
      
      {renderParameterInputs()}
      
      <div className="bg-gray-50 p-3 rounded-md">
        <p className="text-sm text-gray-600">
          设备地址: 0x{deviceAddress.toString(16).toUpperCase().padStart(2, '0')}
        </p>
        <p className="text-sm text-gray-600">
          命令: {commandType}
        </p>
      </div>
    </div>
  );
};
```

## 📱 页面组件设计

### 1. 仪表板页面

```typescript
// apps/fcc-admin/app/dashboard/page.tsx
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { SystemOverview } from '@/components/pages/dashboard/SystemOverview';
import { DeviceGrid } from '@/components/pages/dashboard/DeviceGrid';
import { RecentActivity } from '@/components/pages/dashboard/RecentActivity';

export default function DashboardPage() {
  return (
    <DashboardLayout title="仪表板">
      <div className="space-y-6">
        {/* 系统概览 */}
        <SystemOverview />
        
        {/* 设备网格 */}
        <DeviceGrid />
        
        {/* 最近活动 */}
        <RecentActivity />
      </div>
    </DashboardLayout>
  );
}
```

### 2. 控制器管理页面

```typescript
// apps/fcc-admin/app/controllers/page.tsx
'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ControllerTable } from '@/components/pages/controllers/ControllerTable';
import { CreateControllerModal } from '@/components/pages/controllers/CreateControllerModal';
import { Button } from '@fcc/ui';
import { useControllers } from '@fcc/core';

export default function ControllersPage() {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [filters, setFilters] = useState({});
  
  const { data: controllers, isLoading } = useControllers(filters);

  return (
    <DashboardLayout title="控制器管理">
      <div className="space-y-6">
        {/* 操作栏 */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-3">
            {/* 搜索和筛选 */}
          </div>
          <Button
            onClick={() => setShowCreateModal(true)}
            variant="primary"
          >
            + 添加控制器
          </Button>
        </div>
        
        {/* 控制器表格 */}
        <ControllerTable
          controllers={controllers}
          loading={isLoading}
          onRefresh={() => {}}
        />
        
        {/* 创建控制器模态框 */}
        <CreateControllerModal
          open={showCreateModal}
          onClose={() => setShowCreateModal(false)}
        />
      </div>
    </DashboardLayout>
  );
}
```

## 🔧 配置和部署

### 1. Monorepo配置

```json
// package.json (根目录)
{
  "name": "fcc-admin-monorepo",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "lint": "turbo run lint",
    "test": "turbo run test",
    "clean": "turbo run clean"
  },
  "devDependencies": {
    "turbo": "^1.10.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0"
  }
}
```

### 2. Turbo配置

```json
// turbo.json
{
  "schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "dependsOn": ["^build"]
    },
    "test": {
      "dependsOn": ["^build"]
    }
  }
}
```

### 3. Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine AS base
WORKDIR /app

# 安装依赖
FROM base AS deps
COPY package.json package-lock.json ./
COPY apps/fcc-admin/package.json ./apps/fcc-admin/
COPY packages/*/package.json ./packages/*/
RUN npm ci

# 构建应用
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build --filter=fcc-admin

# 生产镜像
FROM node:18-alpine AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/apps/fcc-admin/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/apps/fcc-admin/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/fcc-admin/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

## 🚀 开发和部署流程

### 1. 开发环境启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建所有包
npm run build

# 运行测试
npm run test
```

### 2. 模块发布

```bash
# 发布UI组件库
cd packages/fcc-ui
npm publish

# 发布核心业务模块
cd packages/fcc-core
npm publish

# 发布Wayne协议模块
cd packages/wayne-protocol
npm publish
```

### 3. 迁移到其他项目

```bash
# 安装FCC模块
npm install @fcc/ui @fcc/core @wayne/protocol

# 在其他React项目中使用
import { StatusCard, PumpControlPanel } from '@fcc/ui';
import { useControllers, useDevices } from '@fcc/core';
import { DARTCommandBuilder } from '@wayne/protocol';
```

## 📋 开发计划

### Phase 1: 基础架构 (2-3天)
- ✅ 设置Monorepo结构
- ✅ 创建基础UI组件库
- ✅ 实现核心业务模块
- ✅ 配置TypeScript和构建工具

### Phase 2: 核心功能 (3-4天)
- ✅ 仪表板页面
- ✅ 控制器管理功能
- ✅ 设备监控界面
- ✅ 实时状态更新

### Phase 3: Wayne专业功能 (2-3天)
- ✅ Wayne协议组件
- ✅ DART命令构建器
- ✅ 泵控制面板
- ✅ 协议调试工具

### Phase 4: 优化和部署 (1-2天)
- ✅ 性能优化
- ✅ Docker部署配置
- ✅ 文档完善
- ✅ 测试覆盖

## 🎯 模块化优势

1. **独立开发** - 每个包可以独立开发和测试
2. **版本控制** - 每个模块有独立的版本管理
3. **按需引入** - 其他项目可以选择性引入需要的模块
4. **类型安全** - 完整的TypeScript支持
5. **测试隔离** - 每个模块有独立的测试环境
6. **文档完善** - 每个模块有详细的使用文档

这个方案既满足了FCC后台管理的需求，又提供了高度的模块化和可迁移性，为后续的项目复用奠定了良好的基础。 