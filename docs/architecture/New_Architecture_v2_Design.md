# FCC 新架构 v2 设计文档

## 概述

本文档描述了一套全新的FCC设备轮询架构（v2版本），专为Wayne DART协议设计，不修改原有系统，提供独立的高性能设备管理解决方案。

## 设计原则

### 1. 设备独立性
- **每个设备独立 channel**: 每个设备拥有独立的命令通道和结果通道
- **独立轮询器**: 每个设备运行独立的轮询 goroutine
- **独立看门狗**: 每个设备配备专用的看门狗监控

### 2. 状态驱动业务
- **设备状态**: 设备级别的状态管理（在线/离线/错误等）
- **喷嘴状态**: 喷嘴级别的状态管理（空闲/加油/完成等）
- **状态机模式**: 基于状态转换的业务逻辑处理

### 3. 协议复用
- **复用 dartline**: 使用现有的 `adapter/wayne/dartline` 构建命令
- **DART协议兼容**: 完全兼容Wayne DART协议v1.3规范
- **25ms响应要求**: 满足DART协议的严格时间要求

### 4. 看门狗机制
- **设备健康监控**: 实时监控设备通信健康状态
- **超时检测**: 自动检测设备无响应情况
- **故障恢复**: 支持自动重连和错误恢复

## 架构组件

### 核心组件结构

```
┌─────────────────────────────────────────────────────────────┐
│                    DispatchTask (调度器)                     │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              设备轮询器管理                              ││
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐  ││
│  │  │ DevicePoller │  │ DevicePoller │  │ DevicePoller │  ││
│  │  │   设备 A     │  │   设备 B     │  │   设备 C     │  ││
│  │  └──────────────┘  └──────────────┘  └──────────────┘  ││
│  └─────────────────────────────────────────────────────────┘│
│  ┌─────────────────────────────────────────────────────────┐│
│  │                  看门狗管理器                            ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    ││
│  │  │ Watchdog A  │  │ Watchdog B  │  │ Watchdog C  │    ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘    ││
│  └─────────────────────────────────────────────────────────┘│
│  ┌─────────────────────────────────────────────────────────┐│
│  │                 状态管理器                               ││
│  │  ┌────────────────┐           ┌────────────────┐       ││
│  │  │ DeviceStateSM  │           │ NozzleStateSM  │       ││
│  │  └────────────────┘           └────────────────┘       ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 1. DispatchTask (调度任务)
**位置**: `internal/services/polling/v2/dispatch_task.go`

**功能**:
- 管理多个设备轮询器
- 统一结果处理和分发
- 系统级别的指标收集
- 生命周期管理

**关键特性**:
```go
type DispatchTask struct {
    pollers      map[string]*DevicePoller    // 设备轮询器映射
    watchdogMgr  *watchdog.WatchdogManager   // 看门狗管理器
    resultChan   chan PollResult             // 统一结果通道
    stateManager v2models.DeviceStateManager // 状态管理器
}
```

### 2. DevicePoller (设备轮询器)
**位置**: `internal/services/polling/v2/device_poller.go`

**功能**:
- 单设备的独立轮询逻辑
- 基于 channel 的命令处理
- 集成看门狗机制
- 复用 dartline 协议构建

**关键特性**:
```go
type DevicePoller struct {
    commandChan  chan PollCommand   // 命令通道
    resultChan   chan PollResult    // 结果通道
    watchdog     *watchdog.Watchdog // 设备看门狗
    frameBuilder FrameBuilder       // DART协议帧构建器
}
```

### 3. Watchdog (看门狗)
**位置**: `internal/services/watchdog/watchdog.go`

**功能**:
- 设备健康状态监控
- 超时检测和处理
- 故障恢复机制

**关键特性**:
```go
type Watchdog struct {
    lastPing     time.Time    // 最后心跳时间
    timeout      time.Duration // 超时时间
    onTimeout    func()       // 超时回调
}
```

### 4. DeviceStateManager (状态管理器)
**位置**: `pkg/models/v2/device_state.go`

**功能**:
- 设备和喷嘴状态管理
- 状态机模式实现
- 状态事件发布

**关键特性**:
```go
type DeviceStateManager interface {
    RegisterDevice(info DeviceInfo) (DeviceStateMachine, error)
    RegisterNozzle(deviceID string, info NozzleInfo) (NozzleStateMachine, error)
    GetDeviceState(deviceID string) (*DeviceStateData, error)
    GetNozzleState(deviceID, nozzleID string) (*NozzleStateData, error)
}
```

## 状态设计

### 设备状态
```go
type DeviceState string

const (
    DeviceStateOffline      = "offline"      // 设备离线
    DeviceStateOnline       = "online"       // 设备在线
    DeviceStatePolling      = "polling"      // 轮询中
    DeviceStateError        = "error"        // 错误状态
    DeviceStateConfiguring  = "configuring"  // 配置中
)
```

### 喷嘴状态
```go
type NozzleState string

const (
    NozzleStateIdle       = "idle"       // 空闲
    NozzleStateSelected   = "selected"   // 已选择
    NozzleStateAuthorized = "authorized" // 已授权
    NozzleStateOut        = "out"        // 油枪拔出
    NozzleStateFilling    = "filling"    // 加油中
    NozzleStateCompleted  = "completed"  // 加油完成
)
```

## 使用方法

### 1. 基础配置
```go
config := DispatchTaskConfig{
    Name:                "wayne_dispatch_task",
    MaxDevices:          32,                    // 支持最多32个设备
    DefaultPollInterval: 1 * time.Second,      // 默认轮询间隔
    DefaultPollTimeout:  25 * time.Millisecond, // DART协议要求
    WatchdogTimeout:     10 * time.Second,     // 看门狗超时
    ResultBufferSize:    1000,                 // 结果缓冲区大小
    EnableMetrics:       true,                 // 启用指标收集
}
```

### 2. 创建和启动调度器
```go
// 创建状态管理器
stateManager := NewStateManager()

// 创建调度器
dispatcher := NewDispatchTask(config, stateManager, logger)

// 启动调度器
if err := dispatcher.Start(); err != nil {
    log.Fatal("Failed to start dispatcher:", err)
}
```

### 3. 注册设备
```go
deviceInfo := v2models.DeviceInfo{
    ID:           "wayne_pump_01",
    Name:         "Wayne 1号泵",
    Address:      0x50, // DART地址
    ControllerID: "controller_01",
    StationID:    "station_001",
}

pollerConfig := DevicePollerConfig{
    PollInterval:    500 * time.Millisecond,
    PollTimeout:     25 * time.Millisecond,
    WatchdogTimeout: 5 * time.Second,
    BufferSize:      50,
}

err := dispatcher.RegisterDevice(deviceInfo, pollerConfig)
```

### 4. 启动设备轮询
```go
// 启动单个设备
err := dispatcher.StartDevice("wayne_pump_01")

// 启动所有设备
err := dispatcher.StartAll()
```

### 5. 发送命令
```go
// 发送到特定设备
cmd := PollCommand{
    Type:      "data",
    Data:      []byte{0x01, 0x01, 0x00}, // CD1命令
    Timeout:   50 * time.Millisecond,
    Timestamp: time.Now(),
}
err := dispatcher.SendCommand("wayne_pump_01", cmd)

// 广播到所有设备
err := dispatcher.BroadcastCommand(cmd)
```

### 6. 监控状态
```go
// 获取系统状态
status := dispatcher.GetSystemStatus()
fmt.Printf("Active devices: %d/%d\n", status.ActiveDevices, status.TotalDevices)

// 获取系统指标
metrics := dispatcher.GetSystemMetrics()
fmt.Printf("Success rate: %.2f%%\n", 
    float64(metrics.SuccessfulPolls)/float64(metrics.TotalPolls)*100)
```

## 业务流程

### 设备轮询流程
```
1. 设备注册 → 创建轮询器和看门狗
2. 启动轮询 → 开始定时轮询 (如1秒间隔)
3. 构建DART帧 → 使用现有dartline包构建轮询帧
4. 发送命令 → 通过串口发送到设备
5. 等待响应 → 25ms内等待设备响应
6. 解析响应 → 解析DART协议响应帧
7. 更新状态 → 更新设备和喷嘴状态
8. 处理业务 → 根据状态执行业务逻辑
```

### 状态驱动业务
```
设备状态变化:
Offline → Online → Polling → Ready

喷嘴状态变化:
Idle → Selected → Authorized → Out → Filling → Completed
```

### 看门狗监控
```
正常流程:
轮询发送 → 看门狗Ping → 重置计时器

超时流程:
无响应 → 看门狗超时 → 触发回调 → 状态置为Error → 尝试恢复
```

## 性能特性

### 1. 并发性能
- **独立轮询**: 每个设备独立goroutine，互不影响
- **并行处理**: 多设备并行轮询，充分利用多核性能
- **无锁设计**: 使用channel通信，减少锁竞争

### 2. 实时性保证
- **25ms响应**: 满足DART协议严格的时间要求
- **优先级处理**: 紧急命令可插队处理
- **缓冲管理**: 合理的缓冲区避免阻塞

### 3. 可扩展性
- **水平扩展**: 支持动态增加设备
- **垂直扩展**: 单设备可配置多个喷嘴
- **模块化**: 组件独立，易于扩展和维护

### 4. 可靠性
- **故障隔离**: 单设备故障不影响其他设备
- **自动恢复**: 看门狗机制自动检测和恢复
- **状态一致性**: 状态机保证业务状态一致性

## 与现有系统对比

| 特性 | 原架构 | 新架构v2 |
|------|--------|----------|
| 轮询方式 | 统一轮询循环 | 设备独立轮询 |
| 并发模型 | 顺序处理 | 并行处理 |
| 故障隔离 | 单点故障影响全局 | 设备级故障隔离 |
| 看门狗 | 无 | 每设备独立看门狗 |
| 状态管理 | 简单状态 | 设备+喷嘴状态机 |
| 性能 | 受最慢设备影响 | 设备间独立性能 |
| 扩展性 | 有限 | 高度可扩展 |

## 部署和运维

### 配置文件示例
```yaml
dispatch:
  name: "wayne_dispatch_v2"
  max_devices: 32
  default_poll_interval: "1s"
  default_poll_timeout: "25ms"
  watchdog_timeout: "10s"
  result_buffer_size: 1000
  enable_metrics: true

devices:
  - id: "wayne_pump_01"
    name: "Wayne 1号泵"
    address: 0x50
    poll_interval: "500ms"
    watchdog_timeout: "5s"
  - id: "wayne_pump_02"
    name: "Wayne 2号泵"
    address: 0x51
    poll_interval: "500ms"
    watchdog_timeout: "5s"
```

### 监控指标
- **设备在线率**: 在线设备数/总设备数
- **轮询成功率**: 成功轮询次数/总轮询次数
- **平均响应时间**: 设备响应延迟统计
- **看门狗触发**: 看门狗超时次数统计

### 故障排查
1. **设备离线**: 检查看门狗日志和网络连接
2. **轮询失败**: 检查DART协议格式和设备状态
3. **响应超时**: 检查串口配置和设备负载
4. **状态异常**: 检查状态机转换逻辑

## 扩展点

### 1. 自定义结果处理器
```go
type CustomResultHandler struct {
    // 自定义业务逻辑
}

func (h *CustomResultHandler) HandleResult(result PollResult) error {
    // 处理轮询结果
    return nil
}

// 添加到调度器
dispatcher.AddResultHandler(customHandler)
```

### 2. 自定义状态机
```go
type CustomDeviceStateMachine struct {
    // 自定义状态转换逻辑
}

func (sm *CustomDeviceStateMachine) TransitionTo(newState DeviceState) error {
    // 自定义状态转换
    return nil
}
```

### 3. 协议扩展
```go
type CustomFrameBuilder struct {
    // 扩展DART协议支持
}

func (fb *CustomFrameBuilder) BuildCustomFrame(data []byte) (*dartline.Frame, error) {
    // 构建自定义协议帧
    return nil, nil
}
```

## 总结

新架构中 DispatchTask 与 DevicePoller 的关系
组件职责定义
DispatchTask（主调度控制器）：
🎯 管理层：负责多设备的统一管理和协调
📋 设备注册/注销的入口点
🐕 看门狗管理器的统一管理
📊 全局指标收集和结果分发
🔀 命令分发和广播
DevicePoller（设备轮询器）：
⚙️ 执行层：专注单个设备的轮询执行
🔄 独立的轮询循环和命令处理
📡 Wayne DART协议的具体实现
📈 设备级别的统计收集
🤝 与设备状态机的直接交互

graph TD
    subgraph "1️⃣ 启动链路"
        A1["App.main()"] --> A2["NewDispatchTask()"]
        A2 --> A3["dispatchTask.Start()"]
        A3 --> A4["watchdogMgr.StartAll()"]
        A3 --> A5["启动resultProcessingLoop()"]
        A3 --> A6["启动metricsCollectionLoop()"]
    end
    
    subgraph "2️⃣ 设备注册链路"
        B1["dispatchTask.RegisterDevice()"] --> B2["stateManager.RegisterDevice()"]
        B2 --> B3["NewDevicePoller(config, SM, FB)"]
        B3 --> B4["watchdog.Register()"]
        B4 --> B5["存储到pollers[deviceID]"]
        B5 --> B6["启动deviceResultListener()"]
    end
    
    subgraph "3️⃣ 轮询启动链路"
        C1["dispatchTask.StartDevice()"] --> C2["devicePoller.Start()"]
        C2 --> C3["启动pollLoop()"]
        C2 --> C4["启动scheduledPollLoop()"]
        C2 --> C5["watchdog.Start()"]
        C4 --> C6["定时生成PollCommand"]
    end
    
    subgraph "4️⃣ 轮询执行链路"
        D1["PollCommand"] --> D2["executeCommand()"]
        D2 --> D3["stateMachine.OnPollStart()"]
        D3 --> D4["frameBuilder.BuildPollFrame()"]
        D4 --> D5["sendFrameAndWait()<br/>(Wayne DART)"]
        D5 --> D6["frameBuilder.ParseResponse()"]
        D6 --> D7["stateMachine.OnPollResponse()"]
        D7 --> D8["watchdog.Ping()"]
        D8 --> D9["发送PollResult到DispatchTask"]
    end
    
    style A1 fill:#e3f2fd
    style B1 fill:#f3e5f5
    style C1 fill:#fff3e0
    style D1 fill:#e8f5e8

    graph TD
    subgraph "5️⃣ 授权业务链路"
        E1["FCC.ExecuteCommand('authorize')"] --> E2["dispatchTask.SendCommand()"]
        E2 --> E3["devicePoller.SendCommand()"]
        E3 --> E4["executeDataCommand()"]
        E4 --> E5["frameBuilder.BuildDataFrame()<br/>(CD1 AUTHORIZE)"]
        E5 --> E6["sendFrameAndWait()<br/>(Wayne DART TX#管理)"]
        E6 --> E7["收到ACK响应"]
        E7 --> E8["stateMachine.UpdateTxSequence()"]
        E8 --> E9["resultHandler.HandleResult()<br/>(授权成功)"]
    end
    
    subgraph "6️⃣ 预设业务链路"
        F1["FCC.ExecuteCommand('preset')"] --> F2["dispatchTask.SendCommand()"]
        F2 --> F3["devicePoller.SendCommand()"]
        F3 --> F4["executeDataCommand()"]
        F4 --> F5["frameBuilder.BuildDataFrame()<br/>(CD2/CD3/CD4)"]
        F5 --> F6["sendFrameAndWait()<br/>(25ms超时)"]
        F6 --> F7["解析响应"]
        F7 --> F8["stateMachine状态更新"]
        F8 --> F9["resultHandler处理结果"]
    end
    
    subgraph "7️⃣ 价格变更链路"
        G1["FCC.ExecuteCommand('price_change')"] --> G2["dispatchTask.BroadcastCommand()"]
        G2 --> G3["所有devicePoller.SendCommand()"]
        G3 --> G4["executeDataCommand()"]
        G4 --> G5["frameBuilder.BuildDataFrame()<br/>(CD5 PRICE UPDATE)"]
        G5 --> G6["并发发送到所有设备"]
        G6 --> G7["收集所有设备响应"]
        G7 --> G8["统一结果处理"]
    end
    
    subgraph "8️⃣ 状态监控链路"
        H1["定时轮询"] --> H2["devicePoller.executePoll()"]
        H2 --> H3["frameBuilder.BuildPollFrame()<br/>(20H格式)"]
        H3 --> H4["收到DATA响应<br/>(设备状态数据)"]
        H4 --> H5["frameBuilder.ParseResponse()"]
        H5 --> H6["解析DC1/DC2/DC3事务"]
        H6 --> H7["stateMachine状态转换"]
        H7 --> H8["业务状态推送"]
    end
    
    style E1 fill:#e8f5e8
    style F1 fill:#f3e5f5
    style G1 fill:#fff3e0
    style H1 fill:#e1f5fe

    完整调用链路代码示例
    // 1️⃣ 系统启动
func main() {
    // 创建主调度器
    config := DispatchTaskConfig{
        Name:                "Wayne_FCC_Dispatcher",
        MaxDevices:          32,
        DefaultPollInterval: 1 * time.Second,
        DefaultPollTimeout:  25 * time.Millisecond, // Wayne DART要求
        WatchdogTimeout:     10 * time.Second,
        EnableMetrics:       true,
    }
    
    stateManager := NewV2StateManager()
    dispatcher := NewDispatchTask(config, stateManager, logger)
    
    // 启动调度系统
    dispatcher.Start()
    
    // 2️⃣ 设备注册
    deviceInfo := v2models.DeviceInfo{
        ID:      "pump_001",
        Address: 0x51, // Wayne DART地址
        Type:    "Wayne DART Pump",
    }
    
    pollerConfig := DevicePollerConfig{
        PollInterval:    1 * time.Second,
        PollTimeout:     25 * time.Millisecond,
        WatchdogTimeout: 10 * time.Second,
        BufferSize:      100,
    }
    
    dispatcher.RegisterDevice(deviceInfo, pollerConfig)
    
    // 3️⃣ 启动设备轮询
    dispatcher.StartDevice("pump_001")
    
    // 4️⃣ 添加业务处理器
    dispatcher.AddResultHandler(&AuthorizeHandler{})
    dispatcher.AddResultHandler(&PresetHandler{})
    dispatcher.AddResultHandler(&StateUpdateHandler{})
}

// 5️⃣ 授权业务
func (fcc *FCCService) ExecuteAuthorize(deviceID string, cardInfo CardInfo) error {
    cmd := PollCommand{
        Type: "data",
        Data: BuildCD1AuthorizeData(cardInfo),
        Timeout: 100 * time.Millisecond,
    }
    
    return fcc.dispatcher.SendCommand(deviceID, cmd)
}

// 6️⃣ 预设业务
func (fcc *FCCService) ExecutePreset(deviceID string, amount decimal.Decimal) error {
    cmd := PollCommand{
        Type: "data", 
        Data: BuildCD4PresetAmountData(amount),
        Timeout: 100 * time.Millisecond,
    }
    
    return fcc.dispatcher.SendCommand(deviceID, cmd)
}

// 7️⃣ 价格变更业务
func (fcc *FCCService) UpdatePrices(prices map[string]decimal.Decimal) error {
    cmd := PollCommand{
        Type: "data",
        Data: BuildCD5PriceUpdateData(prices),
        Timeout: 100 * time.Millisecond,
    }
    
    return fcc.dispatcher.BroadcastCommand(cmd)
}