# Wayne DART协议适配器Nozzle层级集成架构文档

## 概述

本文档详细说明了Wayne DART协议适配器在集成Nozzle层级支持后的完整业务流程、代码调用链路、数据流向和关键接口。

## 目录

1. [架构概览](#架构概览)
2. [业务链路流程](#业务链路流程)
3. [代码调用链路](#代码调用链路)
4. [数据流向分析](#数据流向分析)
5. [关键接口和方法](#关键接口和方法)
6. [错误处理机制](#错误处理机制)
7. [性能考量](#性能考量)

## 架构概览

### 系统层级结构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Layer     │    │  Admin Frontend │    │   External APIs │
│                 │    │                 │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                          HTTP/Echo Server                         │
└─────────┬───────────────────────┼─────────────────────┬───────────┘
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│ Command Service │    │ Device Manager  │    │  Polling System │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
┌─────────────────────────────────▼─────────────────────────────────┐
│                      Adapter Manager                              │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │                Wayne DART Adapter                          │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │  │
│  │  │    State    │  │   Serial    │  │     Protocol        │  │  │
│  │  │   Machine   │  │  Manager    │  │     Handler         │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────┬───────────────────────────────────────────┬───────────┘
          │                                           │
┌─────────▼───────┐                         ┌─────────▼───────┐
│ Nozzle Service  │                         │ Business Engine │
│                 │                         │                 │
└─────────┬───────┘                         └─────────────────┘
          │
┌─────────▼───────────────────────────────────────────────────────┐
│                        Database Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │  GORM/ORM   │  │ PostgreSQL  │  │       Redis Cache       │  │
│  │   (Nozzle)  │  │ (Main Data) │  │       (Session)         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
          │
┌─────────▼───────────────────────────────────────────────────────┐
│                     Physical Device Layer                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  Wayne Fuel Dispenser                      │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │   Pump      │  │   Nozzle    │  │      DART           │ │ │
│  │  │ Controller  │  │ 1,2,3...15  │  │   Communication     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 核心组件交互

```mermaid
graph TB
    A[API Request] --> B[Echo Router]
    B --> C[Command Handler]
    C --> D[Device Manager]
    D --> E[Adapter Manager]
    E --> F[Wayne Adapter]
    F --> G[State Machine]
    F --> H[Nozzle Service]
    F --> I[Serial Manager]
    I --> J[Wayne Device]
    G --> H
    H --> K[Database]
    J --> L[Device Response]
    L --> I
    I --> F
    F --> E
    E --> D
    D --> C
    C --> B
    B --> M[API Response]
```

## 业务链路流程

### 1. 价格设置业务流程

#### 1.1 API请求处理流程

```
API Request (POST /device/{id}/command)
├── Request Body: {"command_type": "set_price", "nozzle_number": 1, "price": 7.50}
├── Echo Router → Command Handler
├── Validation & Authentication
├── Device Manager → Get Device Info
├── Adapter Manager → Get Wayne Adapter
└── Command Execution Flow
```

#### 1.2 价格设置详细流程

```
1. executeSetPrices() 开始执行
   ├── 解析输入参数（支持3种格式）
   │   ├── Format 1: prices数组 [7.50, 8.00, 8.50]
   │   ├── Format 2: prices对象 {"1": 7.50, "2": 8.00}
   │   └── Format 3: 单喷嘴 {nozzle_number: 1, price: 7.50}
   │
2. 数据库价格获取
   ├── nozzleService.GetDeviceNozzlePrices(deviceID)
   ├── 获取所有喷嘴当前价格
   └── 构建完整价格映射
   │
3. 价格合并与验证
   ├── 合并目标价格与现有价格
   ├── 验证喷嘴编号(1-15)
   └── 构建最终价格数组
   │
4. Wayne协议命令构建
   ├── 按喷嘴编号排序(1-15)
   ├── 转换为BCD格式(3位小数，3字节)
   ├── 构建CD5事务
   └── 包含所有喷嘴价格(协议要求)
   │
5. 设备通信
   ├── sendCommandAndWait() 发送到设备
   ├── 等待ACK响应
   └── 记录响应时间
   │
6. 数据库更新
   ├── nozzleService.UpdateMultipleNozzlePrices()
   ├── 只更新变化的喷嘴
   └── 事务处理确保一致性
   │
7. 状态机更新
   ├── device.StateMachine.SetPricesReceived(true)
   └── 记录价格设置操作
```

### 2. 交易处理业务流程

#### 2.1 授权到加油完整流程

```
1. 泵授权 (authorize)
   ├── API: POST /device/{id}/command {"command_type": "authorize"}
   ├── Wayne: CD1 0x01 (Authorize)
   ├── State: Reset → Authorized
   └── Response: ACK + DC1 Status

2. 喷嘴选择事件 (DC3 事务)
   ├── 设备事件: 用户选择喷嘴
   ├── Wayne: DC3 [价格][NOZIO] 
   ├── State Machine: ProcessNozzleEvent()
   ├── Database: UpdateNozzleSelectedStatus()
   └── Status: Selected

3. 喷嘴拔出事件 (DC3 事务)
   ├── 设备事件: 用户拔出油枪
   ├── Wayne: DC3 [价格][NOZIO|0x10]
   ├── State: Authorized → Filling
   ├── Database: UpdateNozzleOutStatus()
   └── 开始计量

4. 实时交易数据 (DC2 事务)
   ├── 设备上报: 体积和金额数据
   ├── Wayne: DC2 [VOLUME(4)][AMOUNT(4)]
   ├── State Machine: ProcessVolumeUpdate(), ProcessAmountUpdate()
   ├── Database: UpdateNozzleTransactionData()
   └── 实时显示

5. 交易完成 (喷嘴插回)
   ├── 设备事件: 用户插回油枪
   ├── Wayne: DC3 [价格][NOZIO&0xEF]
   ├── State: Filling → FillingCompleted
   ├── Database: 完成交易记录
   └── 重置状态
```

### 3. 设备轮询业务流程

#### 3.1 DART轮询机制

```
1. 轮询调度器启动
   ├── PollScheduler.Start()
   ├── 加载数据库中的设备列表
   ├── 为每个设备创建轮询任务
   └── 按设备配置的轮询间隔执行

2. 单设备轮询执行
   ├── PollDevice(deviceID) 调用
   ├── 获取Wayne适配器
   ├── 构建DART轮询帧 [ADR][CTRL][SF]
   ├── 发送到串口
   └── 等待25ms响应

3. 响应处理
   ├── 接收DART响应帧
   ├── 解析协议数据(DC1/DC2/DC3)
   ├── 更新状态机
   ├── 同步数据库
   └── 触发业务事件
```

## 代码调用链路

### 1. 价格设置调用链

```go
// API层 → 服务层 → 适配器层 → 协议层 → 设备层

// 1. API入口
POST /api/v1/devices/{deviceID}/commands
↓
// 2. Echo路由处理
server.Router.SetupRoutes() → device_echo.go
↓
// 3. 命令处理器
handlers.ExecuteDeviceCommand()
↓
// 4. 设备管理器
device.Manager.ExecuteCommand()
↓
// 5. 适配器管理器
adapter.Manager.ExecuteCommand()
↓
// 6. Wayne适配器
wayne.WayneAdapter.ExecuteCommand()
  ├── 命令分发: switch command.Type
  └── case "set_price": executeSetPrices()
↓
// 7. 价格设置执行
executeSetPrices() {
    // 7.1 参数解析
    targetPrices := parseInputParameters()
    
    // 7.2 数据库查询
    currentPrices := nozzleService.GetDeviceNozzlePrices()
    
    // 7.3 价格合并
    finalPrices := mergePrices(targetPrices, currentPrices)
    
    // 7.4 协议命令构建
    cdTransaction := transactionBuilder.BuildCD5(pricesBCD)
    
    // 7.5 设备通信
    response := sendCommandAndWait(cdTransaction)
    
    // 7.6 数据库更新
    nozzleService.UpdateMultipleNozzlePrices(updatedPrices)
    
    // 7.7 状态机更新
    device.StateMachine.SetPricesReceived(true)
}
↓
// 8. 协议层处理
wayne/pump.TransactionBuilder.BuildCD5()
↓
// 9. 串口通信
connection.SerialManager.SendFrame()
↓
// 10. Wayne设备响应
Device → SerialManager → WayneAdapter → ResponseHandler
```

### 2. 交易数据处理调用链

```go
// 设备事件 → 协议解析 → 状态更新 → 数据库同步

// 1. 设备事件触发
Wayne Device → DC2/DC3 Transaction
↓
// 2. 串口数据接收
SerialManager.onDataReceived()
↓
// 3. 帧缓冲处理
WayneAdapter.onDataReceived() {
    extractCompleteFrame() → processCompleteFrame()
}
↓
// 4. 协议帧解析
dartline.DecodeFrame() → processFrame()
↓
// 5. 事务数据处理
WayneAdapter.handleDataFrame() {
    // 支持多事务解析
    for each transaction in frame.Data {
        switch transactionType {
            case DC2: handleDC2Transaction()
            case DC3: handleDC3Transaction()
        }
    }
}
↓
// 6. DC2交易数据处理 (体积/金额)
handleDC2Transaction() {
    // 6.1 BCD解码
    volume := bcdConverter.DecodeVolume()
    amount := bcdConverter.DecodeAmount()
    
    // 6.2 状态机更新
    device.StateMachine.ProcessVolumeUpdate(volume)
    device.StateMachine.ProcessAmountUpdate(amount)
    
    // 6.3 数据库同步
    selectedNozzle := device.StateMachine.GetSelectedNozzle()
    nozzleService.UpdateNozzleTransactionData(selectedNozzle, volume, amount)
    nozzleService.UpdateNozzleStatus(selectedNozzle, NozzleStatusFilling)
}
↓
// 7. DC3喷嘴事件处理 (选择/拔出/插入)
handleDC3Transaction() {
    // 7.1 解析喷嘴信息
    price := bcdConverter.DecodePrice()
    nozzleNum := nozio & 0x0F
    nozzleOut := (nozio & 0x10) != 0
    
    // 7.2 状态机处理
    device.StateMachine.ProcessNozzleEvent(nozzleOut, nozzleNum)
    
    // 7.3 数据库状态同步
    nozzleService.UpdateNozzleOutStatus(nozzleNum, nozzleOut)
    nozzleService.UpdateNozzleSelectedStatus(nozzleNum, isSelected)
    nozzleService.UpdateNozzleStatus(nozzleNum, computedStatus)
    nozzleService.UpdateNozzlePrice(nozzleNum, price)
}
```

### 3. 状态机与数据库双向同步

```go
// 3.1 协议事件 → 数据库同步
Wayne Protocol Event → State Machine → Database

pump.PumpStateMachine.ProcessNozzleEvent() {
    // 更新内部状态
    updateInternalState()
    
    // 更新多喷嘴状态追踪
    updateNozzleState(nozzleNumber, func(state *NozzleStateInfo) {
        state.IsOut = nozzleOut
        state.IsFlowing = isFlowing
        state.Status = computeNozzleStatus()
    })
}

// 3.2 数据库更新 → 状态机同步  
Database Update → State Machine Sync

pump.PumpStateMachine.UpdateNozzleFromDatabase() {
    updateNozzleState(nozzleNumber, func(state *NozzleStateInfo) {
        state.Status = dbStatus
        state.Price = dbPrice
        state.Grade = dbGrade
        
        // 根据数据库状态更新内部状态
        switch dbStatus {
            case NozzleStatusSelected:
                state.IsSelected = true
            case NozzleStatusOut:
                state.IsOut = true
            case NozzleStatusFilling:
                state.IsFlowing = true
        }
    })
}
```

## 数据流向分析

### 1. 价格数据流向

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ API Request │───▶│   Adapter   │───▶│  Database   │
│  {price}    │    │  Validation │    │  Current    │
└─────────────┘    └─────────────┘    │  Prices     │
                                      └──────┬──────┘
                                             │
┌─────────────┐    ┌─────────────┐    ┌─────▼──────┐
│    Wayne    │◀───│ CD5 Command │◀───│   Price    │
│   Device    │    │   Builder   │    │  Merger    │
└──────┬──────┘    └─────────────┘    └────────────┘
       │
       ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    ACK      │───▶│  Response   │───▶│  Database   │
│  Response   │    │  Handler    │    │   Update    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 2. 交易数据流向

```
Wayne Device (DC2/DC3 Transactions)
           │
           ▼
    Serial Manager
           │
           ▼
    DART Frame Parser
           │
           ▼
    Transaction Decoder
           │
           ├──DC2──▶ Volume/Amount Data
           │              │
           │              ▼
           │         State Machine
           │              │
           │              ▼
           │         Database Update
           │
           └──DC3──▶ Nozzle Event Data
                          │
                          ▼
                     State Machine
                          │
                          ▼
                     Nozzle Status Update
                          │
                          ▼
                     Database Sync
```

### 3. 状态同步数据流

```
┌─────────────────────────────────────────────────────────┐
│                   State Synchronization                 │
│                                                         │
│  Wayne Protocol          State Machine         Database │
│       Events      ────▶     Internal     ────▶  Tables  │
│                           State Map              │      │
│                              │                   │      │
│                              ▼                   │      │
│                         NozzleStateInfo          │      │
│                              │                   │      │
│                              ▼                   ▼      │
│  Device Commands  ◀────  Status Queries  ◀──── Sync    │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 关键接口和方法

### 1. Nozzle服务接口

```go
// 核心接口定义
type Service interface {
    // 基础CRUD操作
    CreateNozzle(ctx context.Context, nozzle *models.Nozzle) error
    GetNozzleByDeviceAndNumber(ctx context.Context, deviceID string, number byte) (*models.Nozzle, error)
    GetNozzlesByDevice(ctx context.Context, deviceID string) ([]*models.Nozzle, error)
    UpdateNozzle(ctx context.Context, nozzle *models.Nozzle) error
    
    // 价格管理 - Wayne协议专用
    GetDeviceNozzlePrices(ctx context.Context, deviceID string) (map[byte]decimal.Decimal, error)
    UpdateNozzlePrice(ctx context.Context, deviceID string, nozzleNumber byte, price decimal.Decimal) error
    UpdateMultipleNozzlePrices(ctx context.Context, deviceID string, prices map[byte]decimal.Decimal) error
    
    // 状态管理
    UpdateNozzleStatus(ctx context.Context, deviceID string, nozzleNumber byte, status models.NozzleStatus) error
    UpdateNozzleOutStatus(ctx context.Context, deviceID string, nozzleNumber byte, isOut bool) error
    UpdateNozzleSelectedStatus(ctx context.Context, deviceID string, nozzleNumber byte, isSelected bool) error
    
    // 交易数据更新
    UpdateNozzleTransactionData(ctx context.Context, deviceID string, nozzleNumber byte, volume, amount decimal.Decimal) error
    ResetNozzleTransactionData(ctx context.Context, deviceID string, nozzleNumber byte) error
}
```

### 2. 状态机增强接口

```go
// 新增的多喷嘴管理方法
type PumpStateMachineInterface interface {
    // 原有接口保持不变
    GetCurrentStatus() PumpStatus
    GetSelectedNozzle() byte
    ProcessNozzleEvent(nozzleOut bool, nozzleNumber byte) error
    
    // 新增的多喷嘴状态管理
    GetNozzleState(nozzleNumber byte) *NozzleStateInfo
    GetAllNozzleStates() map[byte]*NozzleStateInfo
    GetNozzleStatusForDatabase(nozzleNumber byte) models.NozzleStatus
    UpdateNozzleFromDatabase(nozzleNumber byte, status models.NozzleStatus, price float64, grade byte)
}

// 喷嘴状态信息结构
type NozzleStateInfo struct {
    Number     byte                 `json:"number"`
    IsSelected bool                 `json:"is_selected"`
    IsOut      bool                 `json:"is_out"`
    IsFlowing  bool                 `json:"is_flowing"`
    Grade      byte                 `json:"grade"`
    Price      float64              `json:"price"`
    Status     models.NozzleStatus  `json:"status"`
}
```

### 3. Wayne适配器增强接口

```go
// executeSetPrices方法签名
func (wa *WayneAdapter) executeSetPrices(ctx context.Context, device *WayneDevice, command api.Command) (*api.CommandResult, error)

// 支持的输入格式
// Format 1: prices数组
{"command_type": "set_price", "prices": [7.50, 8.00, 8.50]}

// Format 2: prices对象 (推荐)
{"command_type": "set_price", "prices": {"1": 7.50, "2": 8.00, "3": 8.50}}

// Format 3: 单喷嘴更新
{"command_type": "set_price", "nozzle_number": 1, "price": 7.50}
```

### 4. 数据库模型

```go
// Nozzle实体模型
type Nozzle struct {
    ID           string          `json:"id" gorm:"primaryKey"`
    Number       byte            `json:"number" gorm:"not null;index"`
    Name         string          `json:"name"`
    DeviceID     string          `json:"device_id" gorm:"not null;index"`
    
    // 状态信息
    Status       NozzleStatus    `json:"status" gorm:"default:idle"`
    
    // 燃料信息
    FuelGradeID  *string         `json:"fuel_grade_id" gorm:"index"`
    FuelGrade    *FuelGrade      `json:"fuel_grade,omitempty" gorm:"foreignKey:FuelGradeID"`
    
    // 价格信息
    CurrentPrice decimal.Decimal `json:"current_price" gorm:"type:decimal(10,3);default:0"`
    
    // 交易数据
    CurrentVolume decimal.Decimal `json:"current_volume" gorm:"type:decimal(10,3);default:0"`
    CurrentAmount decimal.Decimal `json:"current_amount" gorm:"type:decimal(10,2);default:0"`
    
    // 状态标志
    IsOut        bool            `json:"is_out" gorm:"default:false"`
    IsSelected   bool            `json:"is_selected" gorm:"default:false"`
    IsEnabled    bool            `json:"is_enabled" gorm:"default:true"`
    
    // 审计字段
    CreatedAt    time.Time       `json:"created_at"`
    UpdatedAt    time.Time       `json:"updated_at"`
}

// 喷嘴状态枚举
type NozzleStatus string

const (
    NozzleStatusIdle       NozzleStatus = "idle"       // 空闲
    NozzleStatusSelected   NozzleStatus = "selected"   // 已选择
    NozzleStatusOut        NozzleStatus = "out"        // 油枪拔出
    NozzleStatusFilling    NozzleStatus = "filling"    // 加油中
    NozzleStatusSuspended  NozzleStatus = "suspended"  // 暂停
    NozzleStatusError      NozzleStatus = "error"      // 错误
    NozzleStatusMaintenance NozzleStatus = "maintenance" // 维护
)
```

## 错误处理机制

### 1. 分层错误处理

```go
// 1. API层错误处理
func (h *Handler) ExecuteDeviceCommand(c echo.Context) error {
    result, err := h.deviceManager.ExecuteCommand(ctx, deviceID, command)
    if err != nil {
        // 错误分类和HTTP状态码映射
        return handleAPIError(c, err)
    }
    return c.JSON(http.StatusOK, result)
}

// 2. 业务层错误处理
func (wa *WayneAdapter) executeSetPrices(ctx context.Context, device *WayneDevice, command api.Command) (*api.CommandResult, error) {
    // 参数验证错误
    if len(targetPrices) == 0 {
        return nil, errors.NewValidationError("缺少价格参数")
    }
    
    // 数据库访问错误
    currentPrices, err := wa.nozzleService.GetDeviceNozzlePrices(ctx, device.ID)
    if err != nil {
        wa.logger.Error("获取设备当前喷嘴价格失败", zap.Error(err))
        return nil, fmt.Errorf("获取设备喷嘴价格失败: %w", err)
    }
    
    // 协议通信错误
    responseTime, err := wa.sendCommandAndWait(ctx, device, cdTransaction.Encode())
    if err != nil {
        return nil, fmt.Errorf("发送价格更新命令失败: %w", err)
    }
    
    // 数据库更新错误（非致命，记录但不返回失败）
    if err := wa.nozzleService.UpdateMultipleNozzlePrices(ctx, device.ID, updatePrices); err != nil {
        wa.logger.Error("更新数据库喷嘴价格失败", zap.Error(err))
        // 不返回错误，因为设备命令已经成功
    }
}

// 3. 协议层错误处理
func (wa *WayneAdapter) sendCommandAndWait(ctx context.Context, device *WayneDevice, data []byte) (time.Duration, error) {
    // 超时错误
    responseCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    select {
    case <-responseCh:
        return time.Since(startTime), nil
    case <-responseCtx.Done():
        return 0, errors.NewTimeoutError("命令执行超时")
    }
}

// 4. 数据库层错误处理
func (s *service) UpdateNozzlePrice(ctx context.Context, deviceID string, nozzleNumber byte, price decimal.Decimal) error {
    result := s.db.WithContext(ctx).
        Model(&models.Nozzle{}).
        Where("device_id = ? AND number = ?", deviceID, nozzleNumber).
        Update("current_price", price)

    if result.Error != nil {
        return errors.NewDatabaseError("更新喷嘴价格失败", result.Error.Error())
    }

    if result.RowsAffected == 0 {
        return errors.NewNotFoundError(fmt.Sprintf("喷嘴未找到: 设备%s 喷嘴%d", deviceID, nozzleNumber))
    }

    return nil
}
```

### 2. 错误类型和处理策略

| 错误类型 | 处理策略 | 示例 |
|---------|---------|------|
| 验证错误 | 立即返回400错误 | 喷嘴编号超出范围(1-15) |
| 设备不存在 | 返回404错误 | 设备ID不存在 |
| 通信超时 | 重试3次后返回504错误 | DART协议25ms超时 |
| 数据库错误 | 记录日志，返回500错误 | 数据库连接失败 |
| 协议错误 | 记录详细日志，返回设备错误 | CRC校验失败 |
| 状态冲突 | 返回409错误 | 设备不在可操作状态 |

### 3. 重试机制

```go
// DART协议重试机制
type DARTRetryConfig struct {
    MaxRetries    int           // 最大重试次数: 3
    Timeout       time.Duration // 单次超时: 25ms
    RetryInterval time.Duration // 重试间隔: 10ms
}

// 数据库操作重试机制
type DatabaseRetryConfig struct {
    MaxRetries    int           // 最大重试次数: 3
    BaseDelay     time.Duration // 基础延迟: 100ms
    MaxDelay      time.Duration // 最大延迟: 1s
    Multiplier    float64       // 延迟倍数: 2.0
}
```

## 性能考量

### 1. 并发处理

```go
// 1. 状态机线程安全
type PumpStateMachine struct {
    mutex sync.RWMutex  // 读写锁优化并发读取
    // ... 其他字段
}

// 2. 连接池管理
func (app *Application) initServices() error {
    // GORM连接池配置
    sqlDB.SetMaxOpenConns(app.config.Database.MaxOpenConns)  // 最大连接数
    sqlDB.SetMaxIdleConns(app.config.Database.MaxIdleConns)  // 最大空闲连接
    sqlDB.SetConnMaxLifetime(30 * time.Minute)               // 连接最大生命周期
}

// 3. 批量操作优化
func (s *service) UpdateMultipleNozzlePrices(ctx context.Context, deviceID string, prices map[byte]decimal.Decimal) error {
    // 使用事务进行批量更新
    tx := s.db.WithContext(ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    for nozzleNumber, price := range prices {
        // 批量执行更新
        result := tx.Model(&models.Nozzle{}).
            Where("device_id = ? AND number = ?", deviceID, nozzleNumber).
            Update("current_price", price)
        // 错误处理...
    }
    
    return tx.Commit().Error
}
```

### 2. 缓存策略

```go
// 1. 设备状态缓存
type DeviceStatusCache struct {
    cache map[string]*CachedStatus
    mutex sync.RWMutex
    ttl   time.Duration  // 5分钟TTL
}

// 2. 喷嘴价格缓存
func (s *service) GetDeviceNozzlePrices(ctx context.Context, deviceID string) (map[byte]decimal.Decimal, error) {
    // 先查缓存
    if cached := s.getCachedPrices(deviceID); cached != nil {
        return cached, nil
    }
    
    // 缓存未命中，查数据库
    prices, err := s.queryDatabasePrices(ctx, deviceID)
    if err != nil {
        return nil, err
    }
    
    // 更新缓存
    s.setCachedPrices(deviceID, prices, 1*time.Minute)
    return prices, nil
}
```

### 3. 性能监控指标

| 指标类型 | 监控项 | 目标值 |
|---------|--------|--------|
| API响应时间 | 设备命令响应 | <200ms |
| 协议响应时间 | DART命令响应 | <25ms |
| 数据库查询 | 喷嘴价格查询 | <50ms |
| 吞吐量 | 并发设备数 | 100+ |
| 错误率 | 命令执行失败率 | <1% |

## 最新架构改进 (2024年12月更新)

### 1. 服务依赖注入统一化

基于最新的代码修改，我们完成了以下关键改进：

#### 1.1 main.go中的服务初始化链路

```go
func (app *Application) initServices() error {
    // 1. 数据库连接和GORM初始化
    db, err := initDatabase(app.config.Database)
    if err != nil {
        return fmt.Errorf("数据库初始化失败: %w", err)
    }
    app.db = db

    // 2. 核心服务层初始化
    app.deviceManager = device.NewManager(db, app.logger)
    app.commandExecutor = command.NewExecutor(app.logger)
    app.businessEngine = statemachine.NewBusinessEngine(app.logger)
    
    // 3. Nozzle服务初始化 (新增)
    app.nozzleService = nozzle.NewService(db, app.logger)
    
    // 4. 适配器管理器初始化 (依赖注入增强)
    app.adapterManager = adapter.NewManager(app.logger)
    
    // 5. Wayne适配器创建 (现在支持Nozzle服务注入)
    wayneAdapter, err := wayne.NewWayneAdapter(wayneConfig, app.nozzleService)
    if err != nil {
        return fmt.Errorf("创建Wayne适配器失败: %w", err)
    }
    
    // 6. 适配器注册和设备注册
    app.adapterManager.RegisterAdapter("wayne", wayneAdapter)
    
    // 7. 轮询调度器初始化 (支持DART轮询)
    app.pollScheduler = polling.NewScheduler(
        app.deviceManager, 
        app.adapterManager, 
        app.logger,
    )
    
    return nil
}
```

#### 1.2 依赖注入流程图

```
Database (GORM)
    ↓
Nozzle Service ──┐
    ↓           │
Device Manager   │
    ↓           │
Adapter Manager  │
    ↓           │
Wayne Adapter ←──┘ (依赖注入)
    ↓
Command Executor
    ↓
Poll Scheduler
    ↓
Business Engine
```

### 2. 命令类型标准化

#### 2.1 GetSupportedCommands()的统一

Wayne适配器现在完全使用标准化的命令类型：

```go
func (wa *WayneAdapter) GetSupportedCommands() []string {
    return []string{
        // 基础设备控制命令 (统一标准)
        string(models.CommandTypeStatus),        // "status"
        string(models.CommandTypeStart),         // "start"  
        string(models.CommandTypeStop),          // "stop"
        string(models.CommandTypePause),         // "pause"
        string(models.CommandTypeResume),        // "resume"
        string(models.CommandTypeReset),         // "reset"
        string(models.CommandTypeShutdown),      // "shutdown"

        // 泵设备专用命令 (标准化)
        string(models.CommandTypeAuthorize),     // "authorize" (不再是"pump_authorize")
        string(models.CommandTypePreset),        // "preset"
        string(models.CommandTypePumpStart),     // "pump_start"
        string(models.CommandTypePumpStop),      // "pump_stop"
        string(models.CommandTypeSetPrice),      // "set_price" (不再是"set_prices")

        // DART协议专用命令 (新增标准化)
        string(models.CommandTypeRequestStatus),        // "request_status"
        string(models.CommandTypeRequestTotalCounters), // "request_total_counters"
        string(models.CommandTypeRequestFillingInfo),   // "request_filling_info"
        string(models.CommandTypeRequestPumpIdentity),  // "request_pump_identity"
        string(models.CommandTypeRequestPumpParams),    // "request_pump_params"
        string(models.CommandTypeAllowedNozzles),       // "allowed_nozzles"
        string(models.CommandTypePresetVolume),         // "preset_volume"
        string(models.CommandTypePresetAmount),         // "preset_amount"
        string(models.CommandTypePriceUpdate),          // "price_update"
    }
}
```

#### 2.2 命令执行分发的标准化

```go
func (wa *WayneAdapter) ExecuteCommand(ctx context.Context, deviceID string, command api.Command) (*api.CommandResult, error) {
    // 统一的命令分发机制 - 完全基于标准CommandType
    switch command.Type {
    case string(models.CommandTypeAuthorize):      // 统一支持"authorize"
        return wa.executePumpAuthorize(ctx, device, command)
    case string(models.CommandTypeSetPrice):       // 统一支持"set_price"
        return wa.executeSetPrices(ctx, device, command)
    case string(models.CommandTypePriceUpdate):    // 同时支持"price_update"
        return wa.executeSetPrices(ctx, device, command)
    // ... 其他命令映射
    }
}
```

### 3. 状态机多喷嘴增强

#### 3.1 NozzleStateInfo结构优化

```go
type NozzleStateInfo struct {
    Number     byte                 `json:"number"`      // 喷嘴编号(1-15)
    IsSelected bool                 `json:"is_selected"` // 是否被选中
    IsOut      bool                 `json:"is_out"`      // 油枪是否拔出
    IsFlowing  bool                 `json:"is_flowing"`  // 是否正在加油
    Grade      byte                 `json:"grade"`       // 油品等级
    Price      float64              `json:"price"`       // 当前价格
    Status     models.NozzleStatus  `json:"status"`      // 数据库状态
    
    // 新增字段 - 与数据库模型对应
    CurrentVolume decimal.Decimal   `json:"current_volume"` // 当前交易体积
    CurrentAmount decimal.Decimal   `json:"current_amount"` // 当前交易金额
    IsEnabled     bool              `json:"is_enabled"`     // 是否启用
}
```

#### 3.2 状态机与数据库双向同步机制

```go
// 协议事件 → 状态机 → 数据库 (实时同步)
func (wa *WayneAdapter) handleDC3Transaction(transactionData []byte, device *WayneDevice) {
    // ... DC3解析 ...
    
    // 1. 更新状态机内部状态
    device.StateMachine.ProcessNozzleEvent(nozzleOut, nozzleNum)
    
    // 2. 实时同步到数据库 (新增逻辑)
    if wa.nozzleService != nil {
        ctx := context.Background()
        
        // 更新喷嘴拔出状态
        wa.nozzleService.UpdateNozzleOutStatus(ctx, device.ID, nozzleNum, nozzleOut)
        
        // 更新喷嘴选中状态
        isSelected := nozzleNum > 0
        wa.nozzleService.UpdateNozzleSelectedStatus(ctx, device.ID, nozzleNum, isSelected)
        
        // 更新综合状态
        var status models.NozzleStatus
        if nozzleOut {
            status = models.NozzleStatusOut
        } else if isSelected {
            status = models.NozzleStatusSelected
        } else {
            status = models.NozzleStatusIdle
        }
        wa.nozzleService.UpdateNozzleStatus(ctx, device.ID, nozzleNum, status)
        
        // 更新价格信息
        priceDecimal := decimal.NewFromFloat(price)
        wa.nozzleService.UpdateNozzlePrice(ctx, device.ID, nozzleNum, priceDecimal)
    }
}
```

### 4. 错误处理统一化

#### 4.1 分层错误处理标准

```go
// 业务层错误处理 - executeSetPrices示例
func (wa *WayneAdapter) executeSetPrices(ctx context.Context, device *WayneDevice, command api.Command) (*api.CommandResult, error) {
    // 1. 参数验证错误 - 立即返回
    if len(targetPrices) == 0 {
        return nil, errors.NewValidationError("缺少价格参数：支持prices数组、prices对象或nozzle_number+price格式")
    }
    
    // 2. 数据库访问错误 - 记录详细日志并返回
    currentPrices, err := wa.nozzleService.GetDeviceNozzlePrices(ctx, device.ID)
    if err != nil {
        wa.logger.Error("获取设备当前喷嘴价格失败",
            zap.String("device_id", device.ID),
            zap.Error(err))
        return nil, fmt.Errorf("获取设备喷嘴价格失败: %w", err)
    }
    
    // 3. 协议通信错误 - 致命错误，必须返回
    responseTime, err := wa.sendCommandAndWait(ctx, device, cdTransaction.Encode())
    if err != nil {
        return nil, fmt.Errorf("发送价格更新命令失败: %w", err)
    }
    
    // 4. 数据库更新错误 - 非致命，记录但不中断流程
    if len(updatedNozzles) > 0 {
        if err := wa.nozzleService.UpdateMultipleNozzlePrices(ctx, device.ID, updatePrices); err != nil {
            wa.logger.Error("更新数据库喷嘴价格失败",
                zap.String("device_id", device.ID),
                zap.Error(err))
            // 不返回错误，因为设备命令已经成功
        }
    }
}
```

#### 4.2 数据库错误处理统一

```go
// 修正所有数据库错误处理 - 使用proper string conversion
if result.Error != nil {
    return errors.NewDatabaseError("更新喷嘴价格失败", result.Error.Error()) // .Error()方法
}

// 而不是
if result.Error != nil {
    return errors.NewDatabaseError("更新喷嘴价格失败", string(result.Error)) // 错误的类型转换
}
```

## 最新业务链路完整流程

### 价格设置完整调用链 (Updated 2024.12)

```
1. API请求 (支持3种格式)
   POST /api/v1/devices/{deviceID}/commands
   Body: {"command_type": "set_price", "nozzle_number": 1, "price": 7.50}
   ↓
2. Echo路由 → handlers.ExecuteDeviceCommand()
   ↓
3. device.Manager.ExecuteCommand() (注入Nozzle服务)
   ↓
4. adapter.Manager.ExecuteCommand()
   ↓
5. wayne.WayneAdapter.ExecuteCommand()
   ├── 命令类型标准化: "set_price" → models.CommandTypeSetPrice
   └── 分发到: executeSetPrices()
   ↓
6. executeSetPrices() 增强版本:
   ├── 6.1 解析3种输入格式 (数组/对象/单喷嘴)
   ├── 6.2 nozzleService.GetDeviceNozzlePrices() [数据库查询]
   ├── 6.3 价格合并 (target + current → final)
   ├── 6.4 构建CD5事务 (BCD编码，所有喷嘴)
   ├── 6.5 sendCommandAndWait() [Wayne设备通信]
   ├── 6.6 nozzleService.UpdateMultipleNozzlePrices() [数据库更新]
   └── 6.7 device.StateMachine.SetPricesReceived(true) [状态机]
   ↓
7. Wayne设备响应 → ACK确认
   ↓
8. API返回:
   {
     "success": true,
     "data": {
       "total_nozzles": 3,
       "updated_nozzles": 1,
       "response_time": "15ms",
       "final_prices": {"1": 7.50, "2": 8.00, "3": 8.50}
     }
   }
```

### 实时交易数据处理流程 (Updated 2024.12)

```
1. Wayne设备事件 (用户操作)
   ├── DC2: 体积/金额数据 [VOLUME(4)][AMOUNT(4)]
   └── DC3: 喷嘴事件数据 [PRICE(3)][NOZIO(1)]
   ↓
2. 串口数据接收 → SerialManager.onDataReceived()
   ↓
3. DART帧缓冲 → WayneAdapter.extractCompleteFrame()
   ↓
4. 协议解析 → dartline.DecodeFrame()
   ↓
5. 多事务处理 → handleDataFrame()
   ├── DC2 → handleDC2Transaction()
   │   ├── BCD解码 (volume, amount)
   │   ├── StateMachine.ProcessVolumeUpdate()
   │   ├── StateMachine.ProcessAmountUpdate()
   │   └── nozzleService.UpdateNozzleTransactionData() [实时数据库同步]
   │
   └── DC3 → handleDC3Transaction()
       ├── 解析喷嘴信息 (price, nozzleNum, nozzleOut)
       ├── StateMachine.ProcessNozzleEvent()
       └── 数据库状态同步 (4个更新操作):
           ├── nozzleService.UpdateNozzleOutStatus()
           ├── nozzleService.UpdateNozzleSelectedStatus()
           ├── nozzleService.UpdateNozzleStatus()
           └── nozzleService.UpdateNozzlePrice()
   ↓
6. 业务引擎处理 → statemachine.BusinessEngine
   ├── 状态转换 (idle → selected → out → filling)
   ├── 交易记录创建
   └── 实时监控更新
```

## 总结

Wayne DART协议适配器的Nozzle层级集成在2024年12月的最新版本中实现了：

1. **完整的业务流程覆盖**：从API请求到设备响应的端到端处理
2. **健壮的错误处理机制**：分层错误处理和重试机制
3. **高性能的数据处理**：批量操作、缓存策略和并发优化
4. **灵活的接口设计**：支持多种输入格式和向后兼容
5. **实时的状态同步**：协议事件与数据库的双向同步
6. **统一的依赖注入**：服务初始化和依赖管理标准化
7. **标准化的命令类型**：消除命名不一致问题
8. **增强的多喷嘴支持**：完整的状态机与数据库同步机制

该架构为Wayne燃油分配器的数字化管理提供了坚实的技术基础，支持复杂的多喷嘴操作场景和实时业务监控需求。通过本次架构升级，系统的可维护性、可扩展性和稳定性都得到了显著提升。 