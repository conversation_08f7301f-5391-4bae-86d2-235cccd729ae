 # FCC管理后台 - 简化MVP版本

## 🎯 项目概述

基于 Next.js 的FCC管理后台，专注MVP功能，简单实用，避免过度设计。

### 核心功能
- 📊 **系统概览** - 控制器和设备状态总览
- 🔧 **控制器管理** - 查看和管理Wayne控制器
- 📱 **设备管理** - 设备状态监控和基本控制
- ⚡ **命令执行** - 简单的设备命令界面

## 🏗️ 技术栈 (最小化)

```json
{
  "framework": "Next.js 14",
  "language": "TypeScript", 
  "styling": "Tailwind CSS",
  "ui": "简单HTML组件",
  "state": "React useState/useEffect",
  "http": "fetch API",
  "realtime": "简单的轮询"
}
```

## 📁 项目结构 (单应用)

```
fcc-admin/
├── 📁 app/                     # Next.js App Router
│   ├── 📁 api/                 # API路由 (代理FCC服务)
│   │   ├── controllers/
│   │   ├── devices/
│   │   └── commands/
│   ├── 📁 dashboard/           # 仪表板页面
│   ├── 📁 controllers/         # 控制器页面
│   ├── 📁 devices/             # 设备页面
│   ├── layout.tsx              # 根布局
│   └── page.tsx                # 首页重定向到dashboard
├── 📁 components/              # 简单组件
│   ├── 📁 ui/                  # 基础UI组件
│   ├── 📁 features/            # 功能组件
│   └── Layout.tsx              # 主布局
├── 📁 lib/                     # 工具函数
│   ├── api.ts                  # API调用
│   ├── types.ts                # 类型定义
│   └── utils.ts                # 工具函数
├── 📁 styles/                  # 样式文件
├── next.config.js
├── tailwind.config.js
└── package.json
```

## 🧩 核心组件

### 1. 基础UI组件

```typescript
// components/ui/Card.tsx
interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export function Card({ title, children, className = '' }: CardProps) {
  return (
    <div className={`bg-white border rounded-lg p-4 ${className}`}>
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      {children}
    </div>
  );
}

// components/ui/StatusBadge.tsx
interface StatusBadgeProps {
  status: 'online' | 'offline' | 'error' | 'warning';
  children: React.ReactNode;
}

export function StatusBadge({ status, children }: StatusBadgeProps) {
  const colors = {
    online: 'bg-green-100 text-green-800',
    offline: 'bg-gray-100 text-gray-800', 
    error: 'bg-red-100 text-red-800',
    warning: 'bg-yellow-100 text-yellow-800'
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[status]}`}>
      {children}
    </span>
  );
}

// components/ui/Button.tsx
interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  className?: string;
}

export function Button({ 
  children, 
  onClick, 
  variant = 'primary', 
  disabled = false,
  className = '' 
}: ButtonProps) {
  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800',
    danger: 'bg-red-600 hover:bg-red-700 text-white'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 rounded-md font-medium ${variants[variant]} disabled:opacity-50 ${className}`}
    >
      {children}
    </button>
  );
}
```

### 2. 数据获取

```typescript
// lib/api.ts
const API_BASE = process.env.NEXT_PUBLIC_FCC_API || 'http://localhost:8080/api/v1';

export async function fetchControllers() {
  const response = await fetch(`${API_BASE}/controllers`);
  if (!response.ok) throw new Error('Failed to fetch controllers');
  return response.json();
}

export async function fetchDevices(controllerId?: string) {
  const url = controllerId 
    ? `${API_BASE}/devices?controller_id=${controllerId}`
    : `${API_BASE}/devices`;
  const response = await fetch(url);
  if (!response.ok) throw new Error('Failed to fetch devices');
  return response.json();
}

export async function fetchDeviceStatus(deviceId: string) {
  const response = await fetch(`${API_BASE}/devices/${deviceId}/status`);
  if (!response.ok) throw new Error('Failed to fetch device status');
  return response.json();
}

export async function executeCommand(deviceId: string, command: any) {
  const response = await fetch(`${API_BASE}/devices/${deviceId}/commands`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(command)
  });
  if (!response.ok) throw new Error('Failed to execute command');
  return response.json();
}

// lib/hooks.ts
import { useState, useEffect } from 'react';

export function useControllers() {
  const [controllers, setControllers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchControllers()
      .then(setControllers)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  return { controllers, loading, error, refetch: () => {
    setLoading(true);
    fetchControllers()
      .then(setControllers)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }};
}

export function useDevices(controllerId?: string) {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDevices(controllerId)
      .then(setDevices)
      .catch(console.error)
      .finally(() => setLoading(false));
  }, [controllerId]);

  return { devices, loading };
}
```

### 3. 页面组件

```typescript
// app/dashboard/page.tsx
import { Card } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/StatusBadge';

async function getSystemStats() {
  // 服务端数据获取
  const [controllers, devices] = await Promise.all([
    fetch(`${process.env.FCC_API}/controllers`).then(r => r.json()),
    fetch(`${process.env.FCC_API}/devices`).then(r => r.json())
  ]);

  return {
    totalControllers: controllers.length,
    onlineControllers: controllers.filter(c => c.status === 'online').length,
    totalDevices: devices.length,
    onlineDevices: devices.filter(d => d.status === 'online').length
  };
}

export default async function DashboardPage() {
  const stats = await getSystemStats();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">系统概览</h1>
      
      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.totalControllers}</div>
            <div className="text-sm text-gray-600">控制器总数</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.onlineControllers}</div>
            <div className="text-sm text-gray-600">在线控制器</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.totalDevices}</div>
            <div className="text-sm text-gray-600">设备总数</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.onlineDevices}</div>
            <div className="text-sm text-gray-600">在线设备</div>
          </div>
        </Card>
      </div>
    </div>
  );
}

// app/controllers/page.tsx
'use client';

import { useControllers } from '@/lib/hooks';
import { Card } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { Button } from '@/components/ui/Button';

export default function ControllersPage() {
  const { controllers, loading, error, refetch } = useControllers();

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">控制器管理</h1>
        <Button onClick={refetch}>刷新</Button>
      </div>

      <div className="grid gap-4">
        {controllers.map((controller: any) => (
          <Card key={controller.id}>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{controller.name}</h3>
                <p className="text-sm text-gray-600">
                  {controller.protocol} - {controller.address}
                </p>
                <p className="text-sm text-gray-600">
                  站点: {controller.station_id}
                </p>
              </div>
              
              <div className="text-right">
                <StatusBadge status={controller.status}>
                  {controller.status}
                </StatusBadge>
                <div className="text-sm text-gray-600 mt-1">
                  {controller.last_seen ? 
                    `最后在线: ${new Date(controller.last_seen).toLocaleString()}` :
                    '从未在线'
                  }
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}

// app/devices/page.tsx
'use client';

import { useState } from 'react';
import { useDevices } from '@/lib/hooks';
import { Card } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { Button } from '@/components/ui/Button';
import { executeCommand } from '@/lib/api';

export default function DevicesPage() {
  const { devices, loading } = useDevices();
  const [executing, setExecuting] = useState<string | null>(null);

  const handleCommand = async (deviceId: string, command: string) => {
    setExecuting(deviceId);
    try {
      await executeCommand(deviceId, { type: command, action: command.toLowerCase() });
      alert('命令执行成功');
    } catch (error) {
      alert('命令执行失败: ' + error.message);
    } finally {
      setExecuting(null);
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">设备管理</h1>

      <div className="grid gap-4">
        {devices.map((device: any) => (
          <Card key={device.id}>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{device.name}</h3>
                <p className="text-sm text-gray-600">
                  类型: {device.type} | 地址: 0x{device.device_address?.toString(16)?.toUpperCase()}
                </p>
                <p className="text-sm text-gray-600">
                  控制器: {device.controller_id}
                </p>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <StatusBadge status={device.status}>
                    {device.status}
                  </StatusBadge>
                  <div className="text-sm text-gray-600 mt-1">
                    健康: {device.health}
                  </div>
                </div>
                
                {device.type === 'pump' && device.status === 'online' && (
                  <div className="space-x-2">
                    <Button
                      variant="primary"
                      disabled={executing === device.id}
                      onClick={() => handleCommand(device.id, 'authorize')}
                    >
                      {executing === device.id ? '执行中...' : '授权'}
                    </Button>
                    <Button
                      variant="danger"
                      disabled={executing === device.id}
                      onClick={() => handleCommand(device.id, 'stop')}
                    >
                      停止
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
```

### 4. 布局组件

```typescript
// components/Layout.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

const navigation = [
  { name: '概览', href: '/dashboard', icon: '📊' },
  { name: '控制器', href: '/controllers', icon: '🔧' },
  { name: '设备', href: '/devices', icon: '📱' },
];

export function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 侧边栏 */}
      <div className="w-64 bg-white shadow-sm">
        <div className="p-6">
          <h1 className="text-xl font-bold text-gray-900">FCC管理后台</h1>
        </div>
        
        <nav className="mt-6">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-6 py-3 text-sm font-medium ${
                pathname.startsWith(item.href)
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <span className="mr-3">{item.icon}</span>
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 overflow-auto">
        <main className="p-8">
          {children}
        </main>
      </div>
    </div>
  );
}

// app/layout.tsx
import { Layout } from '@/components/Layout';
import './globals.css';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh">
      <body>
        <Layout>{children}</Layout>
      </body>
    </html>
  );
}

// app/page.tsx
import { redirect } from 'next/navigation';

export default function HomePage() {
  redirect('/dashboard');
}
```

## 📦 package.json

```json
{
  "name": "fcc-admin",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "^18",
    "react-dom": "^18",
    "typescript": "^5"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "autoprefixer": "^10",
    "eslint": "^8",
    "eslint-config-next": "14.0.0",
    "postcss": "^8",
    "tailwindcss": "^3"
  }
}
```

## 🚀 快速开始

```bash
# 创建项目
npx create-next-app@latest fcc-admin --typescript --tailwind --app

# 安装依赖
cd fcc-admin
npm install

# 配置环境变量
echo "NEXT_PUBLIC_FCC_API=http://localhost:8080/api/v1" > .env.local

# 启动开发服务器
npm run dev
```

## ✨ MVP特点

1. **极简结构** - 单一Next.js应用，没有复杂的monorepo
2. **最少依赖** - 只使用必要的依赖包
3. **核心功能** - 专注于设备管理的基本需求
4. **简单UI** - 使用Tailwind CSS创建简洁界面
5. **易于维护** - 代码结构清晰，便于快速迭代

这个MVP版本去除了复杂的架构设计，专注于实现FCC管理后台的核心功能，可以快速部署和使用。