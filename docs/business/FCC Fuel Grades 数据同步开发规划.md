# FCC Fuel Grades 数据同步开发规划

## 📋 项目概述

基于外部API `http://*************:8080/api/v1/oil/products` 实现fuel grades数据同步功能，支持：

1. **全量数据同步** - 从外部API获取所有油品数据
2. **新增和更新支持** - 对比本地数据，实现增量同步 
3. **软删除机制** - 对于外部API中不存在的本地油品，实现软删除

## 🔄 重要修正说明

1. **价格单位**：印尼盾（IDR），无小数点，直接存储整数值
2. **HTTP客户端**：项目已有`HTTPSyncStrategy`模式，复用现有架构
3. **现有基础**：项目已有完整的同步框架和配置机制

## 🔍 外部API数据结构分析

### API端点
```
GET http://*************:8080/api/v1/oil/products
```

### 响应数据格式
```json
{
  "id": 101,
  "code": "BP-92", 
  "name": "BP 92",
  "category": "Gasoline",
  "grade": "BP 92",
  "description": "Standard 92 Octane Gasoline",
  "unit": "Litre",
  "default_price": 12360,  // 印尼盾，无小数点
  "current_price": 12360,  // 印尼盾，无小数点
  "is_active": true,
  "specifications": "",
  "image_url": "",
  "created_by": "system",
  "created_at": "2025-06-23T23:15:45.72494-04:00",
  "updated_at": "2025-06-23T23:15:45.72494-04:00"
}
```

## 🏗️ 技术架构设计

### 1. 复用现有架构模式

基于项目中 `internal/services/transaction/sync_publisher.go` 的成熟实现：
- 完整的HTTP客户端 (`HTTPSyncStrategy`)
- 重试机制和错误处理
- 配置管理和日志记录
- 异步同步支持

### 2. 文件结构设计

```
internal/services/fuel_sync/
├── models.go          # 外部API数据模型定义
├── strategy.go        # FuelSyncStrategy（基于HTTPSyncStrategy模式）
├── converter.go       # 数据转换器（OilProduct → FuelGrade）
├── service.go         # 核心同步服务逻辑
├── scheduler.go       # 定时同步调度器
└── handlers.go        # HTTP API处理器
```

### 3. 数据模型映射

| 外部API字段 | FuelGrade字段 | 转换说明 |
|------------|---------------|----------|
| id | id | 字符串转换：fmt.Sprintf("oil_%d", id) |
| name | name | 直接映射 |
| category | type | 转换：Gasoline→gasoline, Diesel→diesel |
| current_price | price | **印尼盾直接存储**：decimal.NewFromInt64(current_price) |
| description | description | 直接映射 |
| grade | metadata.grade | 存储在metadata中 |
| code | metadata.code | 存储在metadata中 |
| is_active | deleted_at | is_active=false时设置deleted_at |

### 4. 软删除字段扩展

为`fuel_grades`表添加软删除支持：
```sql
ALTER TABLE fuel_grades ADD COLUMN deleted_at TIMESTAMP DEFAULT NULL;
CREATE INDEX idx_fuel_grades_deleted_at ON fuel_grades(deleted_at);
```

## 📋 详细开发计划

### 🎯 第一阶段：模型和策略（1天）

#### 任务1：创建外部API数据模型
- **文件**: `internal/services/fuel_sync/models.go`
- **内容**: 定义OilProduct结构体，正确处理印尼盾价格格式
- **关键点**: 价格字段为int64类型，无小数点

#### 任务2：创建FuelSyncStrategy
- **文件**: `internal/services/fuel_sync/strategy.go`
- **内容**: 基于现有HTTPSyncStrategy模式创建油品同步策略
- **复用**: 借鉴transaction同步的成熟设计

#### 任务3：实现数据转换器
- **文件**: `internal/services/fuel_sync/converter.go`
- **内容**: 将外部OilProduct转换为FuelGrade模型
- **关键点**: 正确处理印尼盾价格转换

### 🎯 第二阶段：服务和存储（1-2天）

#### 任务4：扩展FuelGrade模型支持软删除
- **文件**: 修改`pkg/models/nozzle.go`和数据库迁移脚本
- **内容**: 添加deleted_at字段和相关索引
- **注意**: 保持向后兼容性

#### 任务5：实现油品同步服务
- **文件**: `internal/services/fuel_sync/service.go`
- **内容**: 创建FuelGradeSyncService处理核心业务逻辑
- **功能**: 全量拉取、增量更新、软删除、冲突解决

#### 任务6：创建同步任务调度器
- **文件**: `internal/services/fuel_sync/scheduler.go`
- **内容**: 实现定时同步和手动触发同步
- **集成**: 与现有配置系统集成

### 🎯 第三阶段：集成和测试（1天）

#### 任务7：添加API接口
- **文件**: `internal/services/fuel_sync/handlers.go`
- **内容**: 提供HTTP API用于手动触发和状态查询
- **路径**: `/api/v2/fuel-sync/*`

#### 任务8：编写单元测试
- **文件**: `*_test.go`文件
- **覆盖**: 数据转换、同步逻辑、软删除等核心功能
- **目标**: ≥80%测试覆盖率

#### 任务9：集成测试验证
- **内容**: 端到端测试同步功能和错误处理
- **验证**: 与真实外部API的集成测试

## 🔧 关键实现细节

### 1. 价格处理（印尼盾）
```go
// 外部API返回
type OilProduct struct {
    CurrentPrice int64 `json:"current_price"` // 12360 印尼盾
}

// 转换为内部模型
price := decimal.NewFromInt64(oilProduct.CurrentPrice)
fuelGrade.Price = price // 存储为 12360.0000
```

### 2. HTTP客户端复用
```go
type FuelSyncStrategy struct {
    systemID        string
    endpoint        string
    timeout         time.Duration
    maxRetries      int
    retryDelay      time.Duration
    headers         map[string]string
    logger          *zap.Logger
    // 复用HTTPSyncStrategy的成熟设计
}
```

### 3. 软删除逻辑
```go
func (c *Converter) handleSoftDelete(oilProduct *OilProduct, fuelGrade *models.FuelGrade) {
    now := time.Now()
    
    if !oilProduct.IsActive {
        // 软删除
        fuelGrade.DeletedAt = &now
    } else if fuelGrade.DeletedAt != nil {
        // 重新激活（恢复软删除）
        fuelGrade.DeletedAt = nil
    }
}
```

### 4. 同步策略
```go
type SyncStrategy struct {
    FullSync      bool // 是否全量同步
    IncrementalSync bool // 是否增量同步
    SoftDelete    bool // 是否启用软删除
    ConflictResolution string // 冲突解决策略
}
```

## ⚙️ 配置集成

### config.yaml配置
```yaml
business:
  fuel_sync:
    enabled: true
    endpoint: "http://*************:8080/api/v1/oil/products"
    sync_interval: "1h"          # 每小时同步一次
    timeout: "30s"               # HTTP请求超时
    retry_count: 3               # 重试次数
    strategy:
      full_sync: true            # 启用全量同步
      incremental_sync: true     # 启用增量同步
      soft_delete: true          # 启用软删除
      conflict_resolution: "external_wins" # 外部数据优先
```

## 🚨 错误处理机制

### 1. HTTP请求错误
- 连接超时：重试机制
- 服务不可用：指数退避重试
- 数据格式错误：记录日志并跳过

### 2. 数据转换错误
- 价格格式错误：使用默认值并记录警告
- 必填字段缺失：跳过该记录并记录错误
- 字符编码问题：自动转换或使用默认值

### 3. 数据库操作错误
- 主键冲突：根据配置决定覆盖或跳过
- 外键约束：检查依赖关系后重试
- 事务失败：回滚并重试

## 📊 监控和指标

### 同步状态指标
- 同步成功/失败次数
- 新增/更新/软删除记录数
- 同步耗时统计
- 错误率监控

### API接口
- `GET /api/v2/fuel-sync/status` - 获取同步状态
- `POST /api/v2/fuel-sync/trigger` - 手动触发同步
- `GET /api/v2/fuel-sync/history` - 获取同步历史

## 🔍 测试策略

### 单元测试
- 数据模型转换测试
- 价格格式处理测试
- 软删除逻辑测试
- 错误处理测试

### 集成测试
- HTTP客户端集成测试
- 数据库操作集成测试
- 端到端同步流程测试

### 性能测试
- 大量数据同步性能测试
- 并发同步压力测试
- 内存使用监控

## 🚀 部署和运维

### MVP版本特性
- 内存状态存储（简化实现）
- 基本错误处理和重试
- 手动触发同步
- 基础监控指标

### 生产级扩展计划
- 数据库持久化同步状态
- 高级错误恢复机制
- 自动同步调度
- 完整的监控和告警

## 📅 开发时间线

| 阶段 | 任务 | 时间 | 依赖 |
|------|------|------|------|
| 第一阶段 | 任务1-3 | 1天 | 无 |
| 第二阶段 | 任务4-6 | 1-2天 | 第一阶段完成 |
| 第三阶段 | 任务7-9 | 1天 | 第二阶段完成 |
| **总计** | | **3-4天** | |

## 🎯 MVP交付目标

1. ✅ **基础同步功能** - 能够从外部API拉取并同步油品数据
2. ✅ **价格正确处理** - 正确处理印尼盾价格格式
3. ✅ **软删除支持** - 实现基于is_active字段的软删除
4. ✅ **错误处理** - 基本的重试和错误记录机制
5. ✅ **手动触发** - 提供API接口手动触发同步
6. ✅ **状态查询** - 能够查询同步状态和历史

这个MVP版本将为后续的生产级功能奠定坚实的基础。 