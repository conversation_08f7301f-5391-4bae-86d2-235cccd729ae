# FCC详细业务流程图

## 1. DART数据处理完整流程

### 数据接收到业务响应的完整链路

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ DART设备    │───▶│ WayneAdapter │───▶│ 协议解析层  │
│ 串口数据    │    │ 接收处理     │    │ Frame解码   │
└─────────────┘    └──────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ 业务响应    │◀───│ 业务处理中枢 │◀───│ 事务解析层  │
│ 命令执行    │    │ 规则引擎     │    │ BCD解码     │
└─────────────┘    └──────────────┘    └─────────────┘
```

## 2. 设备重启业务流程详解

### 检测到TX#=0后的处理流程

```
DART数据(TX#=0) → 重启检测 → 业务规则触发
        │              │           │
        ▼              ▼           ▼
   协议层解析    DeviceManager   StatusMonitor
        │         设备重启标记    发布重启事件
        ▼              │           │
   pump状态分析         ▼           ▼
        │         清除设备缓存    订阅者通知
        ▼              │           │
   BCD数据解码          ▼           ▼
                  CommandExecutor  数据库记录
                  创建恢复序列      重启日志
                       │
                       ▼
                  1. CD1状态查询
                  2. CD5价格同步  
                  3. CD9参数设置
```

## 3. 价格同步业务流程

### 价格差异检测与同步

```
设备价格: 3.302元/升
     │
     ▼
┌─────────────┐
│数据库查询    │ → 系统标准价格: 3.500元/升
│标准价格     │
└─────────────┘
     │
     ▼
┌─────────────┐
│价格对比引擎 │ → 检测差异: 0.198元/升
└─────────────┘
     │
     ▼
┌─────────────┐
│业务规则判断 │ → 差异超过容差(0.001元)
└─────────────┘
     │
     ▼
┌─────────────┐
│CommandExecutor│ → 创建CD5价格同步命令
│命令生成     │
└─────────────┘
     │
     ▼
┌─────────────┐
│WayneAdapter │ → 发送BCD格式价格数据
│协议转换     │
└─────────────┘
     │
     ▼
┌─────────────┐
│设备确认     │ → DC1响应状态
└─────────────┘
     │
     ▼
┌─────────────┐
│数据库更新   │ → 记录同步成功
└─────────────┘
```

## 4. 命令执行优先级调度

### 多优先级命令队列处理

```
                     命令接收入口
                          │
                          ▼
                    ┌─────────────┐
                    │ 命令验证器   │
                    └─────────────┘
                          │
                          ▼
                    ┌─────────────┐
                    │ 优先级分配器 │
                    └─────────────┘
                          │
              ┌───────────┼───────────┐
              ▼           ▼           ▼
        ┌──────────┐ ┌─────────┐ ┌─────────┐
        │紧急队列  │ │普通队列 │ │后台队列 │
        │优先级1-2 │ │优先级3-7│ │优先级8-10│
        └──────────┘ └─────────┘ └─────────┘
              │           │           │
              └───────────┼───────────┘
                          ▼
                    ┌─────────────┐
                    │ 调度器执行   │
                    └─────────────┘
                          │
                          ▼
                    ┌─────────────┐
                    │ AdapterManager│
                    │ 协议转换执行  │
                    └─────────────┘
```

## 5. 状态监控事件处理

### 事件驱动的状态变更处理

```
设备状态变更
     │
     ▼
┌─────────────┐
│StatusMonitor│ → 事件检测器
│状态检测     │
└─────────────┘
     │
     ▼
┌─────────────┐
│事件分类器   │ → 确定事件类型
└─────────────┘
     │
     ├─ 设备上线 → 上线处理流程
     ├─ 设备离线 → 离线处理流程
     ├─ 状态变更 → 状态更新流程
     └─ 错误事件 → 错误处理流程
```

## 6. 异常处理决策树

### 通信异常的分级处理

```
通信异常检测
     │
     ▼
┌─────────────┐
│异常类型分析 │
└─────────────┘
     │
     ├─ CRC校验错误
     │      │
     │      ▼
     │  重新发送命令 (最大3次)
     │
     ├─ 串口断开
     │      │
     │      ▼
     │  重新建立连接 → 连接池管理
     │
     ├─ 设备超时
     │      │
     │      ▼
     │  设备状态检查 → 可能离线标记
     │
     └─ 设备无响应
            │
            ▼
        设备离线流程 → 停止命令发送
```

## 7. 数据一致性保证

### 三层数据同步机制

```
业务数据变更
     │
     ▼
┌─────────────┐
│内存注册表   │ → 实时状态更新
│立即更新     │
└─────────────┘
     │
     ▼
┌─────────────┐
│Redis缓存    │ → 缓存层同步
│异步更新     │
└─────────────┘
     │
     ▼
┌─────────────┐
│PostgreSQL   │ → 持久化存储
│事务更新     │
└─────────────┘
     │
     ▼
┌─────────────┐
│一致性检查   │ → 数据校验
└─────────────┘
```

## 8. 性能优化策略

### 连接池和批量处理

```
并发请求处理
     │
     ▼
┌─────────────┐
│请求分组器   │ → 按设备/控制器分组
└─────────────┘
     │
     ▼
┌─────────────┐
│连接池管理   │ → 复用现有连接
└─────────────┘
     │
     ▼
┌─────────────┐
│并发执行器   │ → 批量并发处理
└─────────────┘
     │
     ▼
┌─────────────┐
│结果聚合器   │ → 收集处理结果
└─────────────┘
```

## 9. 业务规则配置

### 动态配置管理

```yaml
business_rules:
  device_management:
    restart_detection: true
    auto_recovery: true
    recovery_timeout: 5s
    
  price_management:
    tolerance: 0.001
    auto_sync: true
    sync_strategy: "system_priority"
    
  command_execution:
    max_retries: 3
    timeout: 25ms
    batch_size: 50
    
  monitoring:
    health_check_interval: 30s
    event_retention: 24h
    alert_threshold: 0.99
```

## 10. 业务监控大屏

### 实时业务指标展示

```
┌─────────────────────────────────────┐
│            FCC业务监控面板           │
├─────────────────────────────────────┤
│ 设备状态    │ 命令执行    │ 系统性能  │
│ 在线: 98.5% │ 成功: 99.9% │ CPU: 45%  │
│ 离线: 1.5%  │ 失败: 0.1%  │ 内存: 60% │
├─────────────────────────────────────┤
│ 价格同步    │ 事务处理    │ 异常统计  │
│ 准确: 100%  │ TPS: 1250   │ 告警: 2个 │
│ 差异: 0个   │ 平均: 15ms  │ 错误: 5个 │
└─────────────────────────────────────┘
```

这个详细的业务流程文档展示了FCC系统作为一个**完整业务处理中枢**的运作机制，从数据接收到业务响应的全链路处理能力。 



sequenceDiagram
    participant DART as DART设备
    participant WA as WayneAdapter
    participant DM as DeviceManager  
    participant CE as CommandExecutor
    participant SM as StatusMonitor
    participant DB as Database
    
    Note over DART,DB: DART数据接收完整业务流程
    
    DART->>WA: 发送DART数据(50 30 02 08...)
    WA->>WA: 解析协议帧<br/>检测TX#=0
    WA->>DM: 通知设备重启事件
    
    DM->>DB: 记录设备重启日志
    DM->>SM: 发布设备重启事件
    
    SM->>SM: 检测状态变更<br/>触发告警规则
    SM->>DM: 请求设备状态同步
    
    DM->>CE: 创建设备恢复命令序列
    CE->>CE: 1.CD1状态查询<br/>2.CD5价格同步<br/>3.CD9参数设置
    
    CE->>WA: 发送恢复命令
    WA->>DART: 执行DART命令
    DART->>WA: 返回执行结果
    
    WA->>CE: 命令执行完成
    CE->>DM: 设备恢复完成通知
    DM->>DB: 更新设备状态为正常
    DM->>SM: 发布恢复完成事件


sequenceDiagram
    participant API as 外部API
    participant DM as DeviceManager
    participant DB as Database
    participant CE as CommandExecutor
    participant WA as WayneAdapter
    participant DART as DART设备
    
    Note over API,DART: 价格同步业务流程
    
    API->>DM: 检测设备价格(3.302元/升)
    DM->>DB: 查询系统标准价格
    DB->>DM: 返回标准价格(3.500元/升)
    
    DM->>DM: 对比价格差异<br/>检测超过容差
    
    DM->>CE: 创建价格同步命令
    CE->>CE: 构建CD5事务<br/>转换BCD格式
    
    CE->>WA: 发送价格同步命令
    WA->>DART: CD5事务(新价格)
    DART->>WA: DC1响应确认
    
    WA->>CE: 价格设置完成
    CE->>DM: 同步成功通知
    DM->>DB: 更新设备价格记录
    
    DM->>API: 返回同步结果