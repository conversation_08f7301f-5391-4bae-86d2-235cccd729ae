# FCC交易同步功能指南

## 📖 功能概述

FCC交易同步功能自动将完成的交易数据同步到外部API系统。

**核心特点：**
- 🚀 **异步处理**：不影响主业务性能（响应时间 < 100ms）
- 🔄 **自动重试**：失败后自动重试3次，每次间隔5秒
- 📊 **状态跟踪**：完整记录每笔交易的同步状态
- 🔧 **配置驱动**：通过配置文件灵活控制

## 🏗️ 代码架构与文件位置

### 核心模块位置

**同步发布器 (Transaction Sync Publisher)**
- **文件**: `internal/services/transaction/sync_publisher.go`
- **接口**: `TransactionSyncPublisher`
- **实现**: `syncPublisher`
- **关键函数**:
  - `PublishTransactionEvent(ctx, event)` - 发布交易事件
  - `RegisterSyncStrategy(systemID, strategy)` - 注册同步策略
  - `GetSyncStatus(transactionID)` - 获取同步状态

**同步状态存储 (Sync Status Store)**
- **文件**: `internal/services/transaction/sync_status_model.go`
- **接口**: `SyncStatusStore`
- **实现**: `memorySyncStatusStore` (MVP版本)
- **数据模型**: `SyncStatus`
- **表名**: `transaction_sync_statuses` (生产级版本)

**事务服务接口 (Transaction Service)**
- **文件**: `internal/services/transaction/service.go`
- **接口**: `Service`
- **关键函数**:
  - `GetUnuploadedTransactions(ctx, systemID, filter)` - 获取未上传交易
  - `GetUnuploadedTransactionsSummary(ctx)` - 获取同步统计

**同步监控服务 (Sync Monitor)**
- **文件**: `internal/services/monitor/sync_monitor.go`
- **实现**: `syncMonitor`
- **关键函数**:
  - `Start()` - 启动监控
  - `checkSyncHealth()` - 检查同步健康状态

**配置管理 (Configuration)**
- **文件**: `internal/config/config.go`
- **结构**: `TransactionSyncConfig`
- **配置项**: `business.transaction_sync`

### 数据流程

```
Transaction Complete → sync_publisher.PublishTransactionEvent()
                    → HTTPSyncStrategy.Sync()
                    → ExternalTransactionPayload.TransformData()
                    → HTTP POST to External API
                    → sync_status_store.Update()
```

## ⚠️ 架构状态：从MVP到生产级

**当前版本将同步状态存储在内存中**。这是一个MVP（最小可行产品）实现，旨在快速验证核心流程。

- **现状 (MVP)**:
  - **优点**: 实现快，无须修改数据库。
  - **缺点**: **服务重启后，所有进行中或失败的同步状态将会丢失**。

- **下一步规划 (生产级方案)**:
  - **目标**: 将同步状态持久化到数据库，确保服务重启后数据不丢失。
  - **方案**: 推荐采用**独立状态表**方案，创建一个`transaction_sync_statuses`表来专门存储同步状态，以保证主交易表的性能和扩展性。

**本文档描述的是包含了持久化方案的最终产品形态。**

## ⚙️ 快速配置

### 1. 启用功能

在 `configs/config.yaml` 中添加：

```yaml
business:
  transaction_sync:
    enabled: true
    endpoint: "http://192.168.8.121:8080/api/v1/fuel-transactions"
    timeout: 10s
    api_key: "your-secret-api-key"
```

### 2. 重启服务

配置完成后重启FCC服务即可自动生效。

## 🔧 数据映射与转换

### 外部API数据格式

**文件**: `internal/services/transaction/sync_publisher.go`
**结构**: `ExternalTransactionPayload`
**转换函数**: `HTTPSyncStrategy.TransformData()`

### 字段映射关系

#### ✅ 直接可用字段（Transaction表已有）
```go
// 基础交易数据
TransactionNumber → Transaction.ID
PumpID           → Transaction.DeviceID
NozzleID         → Transaction.NozzleID
FuelType         → Transaction.FuelType
UnitPrice        → Transaction.UnitPrice
Volume           → Transaction.ActualVolume
Amount           → Transaction.ActualAmount
TotalVolume      → Transaction.ActualVolume
TotalAmount      → Transaction.TotalAmount

// 时间戳数据
NozzleStartTime  → Transaction.StartedAt
NozzleEndTime    → Transaction.CompletedAt

// 泵码数据 (Wayne DART DC101)
StartTotalizer   → Transaction.StartPumpVolumeReading
EndTotalizer     → Transaction.EndPumpVolumeReading

// 客户和操作员数据
MemberCardID     → Transaction.CustomerCard
EmployeeID       → Transaction.OperatorID (需要类型转换)
FCCTransactionID → Transaction.ID
```

#### 🔗 需要联表查询的字段
```go
// 通过 devices 表获取站点信息
StationID        → devices.station_id (WHERE devices.id = Transaction.DeviceID)

// 通过 nozzles → fuel_grades 表获取油品信息
FuelGrade        → fuel_grades.name (WHERE fuel_grades.id = nozzles.fuel_grade_id 
                                    AND nozzles.id = Transaction.NozzleID)

// 通过 nozzles 表获取油罐信息
Tank             → nozzles.metadata->>'tank_id' (需要在nozzles表中配置)
```

#### 🔧 需要配置或推导的字段
```go
// 从配置文件获取
StationID        → config.yaml 中的 station_id (如果devices表中没有)

// 从元数据推导
Metadata         → Transaction.ExtraData + 扩展信息
```

#### ❌ 当前无法提供的字段
```go
// 需要新增会员系统集成
MemberID         → 需要建立会员卡号到会员ID的映射表

// 需要新增POS终端管理
POSTerminalID    → 需要在设备配置中添加POS终端标识
```

### 查询逻辑示例

**文件**: `internal/services/transaction/converter.go`
**函数**: `TransactionToExternalPayload()`

```sql
-- 获取完整交易数据的SQL查询
SELECT 
    t.*,
    d.station_id,
    n.fuel_grade_id,
    n.metadata->>'tank_id' as tank_id,
    fg.name as fuel_grade_name,
    fg.type as fuel_grade_type
FROM transactions t
    LEFT JOIN devices d ON t.device_id = d.id
    LEFT JOIN nozzles n ON t.nozzle_id = n.id
    LEFT JOIN fuel_grades fg ON n.fuel_grade_id = fg.id
WHERE t.id = ?
```

## 🔄 自动重试机制

系统内置的智能重试机制，其状态**将被持久化到数据库**，确保服务重启后依然有效。

**重试策略配置**:
- **文件**: `internal/services/transaction/sync_publisher.go`
- **结构**: `HTTPSyncStrategy`
- **配置项**:
  - `maxRetries: 3` - 最大重试次数
  - `retryDelay: 5s` - 重试间隔
  - `timeout: 10s` - 请求超时

**状态流转**:
```
pending → retrying → success
        ↓
        failed (超过最大重试次数)
```

**重试逻辑**:
```go
// 文件: internal/services/transaction/sync_publisher.go
// 函数: executeSync()
if err != nil && status.RetryCount < h.maxRetries {
    status.Status = "retrying"
    status.RetryCount++
    status.NextRetryAt = time.Now().Add(h.retryDelay)
} else if err == nil {
    status.Status = "success"
} else {
    status.Status = "failed"
}
```

## 📊 监控接口

### 1. 查询未上传交易

**文件**: `internal/services/transaction/service.go`
**函数**: `GetUnuploadedTransactions()`

```go
// 查询失败的交易
filter := transaction.UnuploadedTransactionFilter{
    Page:       1,
    Limit:      50,
    SyncStatus: stringPtr("failed"), // 可选: failed/retrying/pending
}

result, err := transactionService.GetUnuploadedTransactions(ctx, "external_api_mvp", filter)
```

### 2. 获取同步统计

**文件**: `internal/services/transaction/service.go`
**函数**: `GetUnuploadedTransactionsSummary()`

```go
// 获取整体统计
summary, err := transactionService.GetUnuploadedTransactionsSummary(ctx)

fmt.Printf("统计 - 失败: %d, 重试中: %d, 待处理: %d\n",
    summary.TotalFailed, summary.TotalRetrying, summary.TotalPending)
```

## 🎯 数据转换实现

**文件**: `internal/services/transaction/sync_publisher.go`
**函数**: `HTTPSyncStrategy.TransformData()`

系统自动将FCC交易格式转换为外部API格式：

```go
// FCC格式 (Transaction struct)
Transaction {
    ID:           "tx-12345"
    ActualAmount: 150.00
    ActualVolume: 20.00
    DeviceID:     "pump-001"
    NozzleID:     "nozzle-001"
}

// 转换为外部API格式 (ExternalTransactionPayload)
{
    "transaction_number": "tx-12345",
    "amount": 150.00,
    "volume": 20.00,
    "pump_id": "pump-001",
    "nozzle_id": "nozzle-001",
    // ... 其他必需字段
}
```

## 🔍 故障排除

### 常见问题

**Q: 配置后没有同步？**
- 检查 `configs/config.yaml` 中 `enabled: true`
- 验证网络连接到 `endpoint`
- 查看日志: `tail -f logs/fcc-service.log | grep -i sync`

**Q: 总是同步失败？**
- 检查API端点地址和端口
- 验证 `api_key` 是否正确
- 确认数据格式匹配外部API要求

**Q: 找不到具体的错误信息？**
- 检查 `internal/services/transaction/sync_publisher.go` 中的日志输出
- 查看 `HTTPSyncStrategy.Sync()` 方法的错误处理
- 确认 `sync_status_store` 中的状态记录

### 查看日志

```bash
# 查看同步相关日志
tail -f logs/fcc-service.log | grep -i sync

# 日志示例
INFO  Sync successful      {"system_id": "external_api_mvp", "transaction_id": "tx-12345"}
WARN  Sync attempt failed  {"attempt": 2, "error": "connection timeout"}
ERROR Sync failed after all retries {"max_retries": 3}
```

### 调试工具

**查看同步状态**:
```go
// 获取特定交易的同步状态
statuses, err := syncPublisher.GetSyncStatus(ctx, "tx-12345")

// 查看未上传交易
result, err := transactionService.GetUnuploadedTransactions(ctx, "external_api_mvp", filter)
```

## 📈 监控与告警

我们采用**智能熔断混合方案**进行监控，以确保在快速响应和系统稳定之间取得平衡。

### 1. 监控机制

**文件**: `internal/services/monitor/sync_monitor.go`
**实现**: `syncMonitor`

该机制结合了两种模式的优点：

-   **事件驱动 (快速响应)**:
    -   **触发**: 当短时间内（如1分钟）同步失败次数达到阈值（如10次）时，会立即触发。
    -   **作用**: 确保在发生突发性、大规模同步失败时，运维团队能第一时间收到告警。

-   **周期轮询 (可靠性保底)**:
    -   **触发**: 每隔10分钟，系统会自动进行一次全面的同步健康检查。
    -   **作用**: 保证即使事件信号因某些原因未能触发告警，问题也终将被发现。它也用于监控缓慢增长的失败率。

### 2. 智能熔断与冷却 (避免告警风暴)

为了解决"上游服务长期掉线"等场景导致的告警轰炸问题，我们引入了**熔断器 (Circuit Breaker)** 机制：

-   **触发告警**: 无论是事件驱动还是周期轮询发现了问题，都会先检查熔断器的状态。
-   **进入冷却**: 如果熔断器是关闭的 (`OK`状态)，系统会：
    1.  发送**唯一一次**告警。
    2.  立即"打开"熔断器，进入`ALERTING`状态。
    3.  开始一个为期30分钟的**冷却期**。
-   **抑制告警**: 在30分钟的冷却期内，所有新的告警触发都将被忽略，从而有效避免告警风暴。
-   **自动恢复**: 冷却期结束后，熔断器自动"关闭"，系统恢复到`OK`状态，准备好响应新的问题。

### 3. 监控指标与告警阈值

-   **快速失败阈值**: 1分钟内失败10次。
-   **轮询检查周期**: 10分钟。
-   **告警冷却时间**: 30分钟。
-   **轮询检查关键指标**:
    -   `TotalFailed` (总失败数)
    -   `TotalRetrying` (总重试数)
    -   `OldestPending` (最长未处理交易的时间)

### 4. 查看监控日志

监控服务会输出结构化的日志，清晰地反映其状态：

```bash
# 触发告警
WARN  Sync failures exceeded threshold, circuit breaker opened. {"reason": "failure_rate", "failed_count": 10, "cooldown_period": "30m"}

# 冷却期内抑制告警
INFO  Alerting is currently snoozed, new trigger ignored. {"reason": "polling_check", "cooldown_remaining": "15m"}

# 冷却期结束
INFO  Circuit breaker closed, monitoring resumed.
```

## 🚀 最佳实践

### 1. 生产部署配置

```yaml
# configs/config.yaml
business:
  transaction_sync:
    enabled: true
    endpoint: "${FCC_SYNC_ENDPOINT}"
    timeout: 10s
    api_key: "${FCC_SYNC_API_KEY}"
    max_retries: 3
    retry_delay: 5s
```

### 2. 环境变量配置

```bash
# 生产环境
export FCC_SYNC_ENDPOINT="https://api.example.com/v1/fuel-transactions"
export FCC_SYNC_API_KEY="your-production-api-key"

# 测试环境
export FCC_SYNC_ENDPOINT="https://test-api.example.com/v1/fuel-transactions"
export FCC_SYNC_API_KEY="your-test-api-key"
```

### 3. 监控脚本

```go
// 健康检查脚本
func checkSyncHealth() {
    summary, _ := service.GetUnuploadedTransactionsSummary(ctx)
    if summary.TotalFailed > 100 {
        sendAlert("过多失败交易")
    }
}
```

### 4. 分阶段上线

1. **测试环境验证功能**
   - 配置测试endpoint
   - 验证数据格式匹配
   - 测试重试机制

2. **灰度发布观察性能**
   - 选择部分交易进行同步
   - 观察响应时间和成功率
   - 监控系统资源使用

3. **全量上线监控指标**
   - 启用完整同步功能
   - 监控所有关键指标
   - 建立告警和响应机制

## 🎉 总结

FCC交易同步功能在完成持久化升级后，将**准备好投入生产使用**：

✅ 自动化程度高，无需人工干预  
✅ 内置持久化重试机制，提高成功率  
✅ 性能优异，不影响主业务  
✅ 监控完善，便于运维管理  
✅ 代码结构清晰，易于维护和扩展

### 核心组件总结

| 组件 | 文件位置 | 主要功能 |
|------|----------|----------|
| 同步发布器 | `internal/services/transaction/sync_publisher.go` | 核心同步逻辑 |
| 状态存储 | `internal/services/transaction/sync_status_model.go` | 同步状态管理 |
| 事务服务 | `internal/services/transaction/service.go` | 交易查询接口 |
| 监控服务 | `internal/services/monitor/sync_monitor.go` | 健康监控 |
| 配置管理 | `internal/config/config.go` | 系统配置 |

---

*技术支持：如遇问题请查看对应文件的实现细节，或联系开发团队* 