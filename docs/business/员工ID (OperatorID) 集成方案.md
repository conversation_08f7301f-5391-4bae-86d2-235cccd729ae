# 员工ID (OperatorID) 集成方案

## 1. 需求背景

当前需要支持在 `Authorize` (授权) 和 `Preset` (预设) 接口中传入员工ID，并将此ID与最终生成的交易记录进行关联。经过分析，我们决定利用数据库中 `transactions` 表已有的 `operator_id` 字段来存储这个员工ID。

本方案旨在规划从 API 接口层接收员工ID，到服务层进行状态管理，再到最终持久化到交易记录的完整技术实现路径。

## 2. 核心设计：引入 `OperatorIDCache`

为了在 `DevicePoller` 和 `TransactionAssembler` 之间安全、解耦地传递员工ID，我们引入一个新的共享缓存组件：`OperatorIDCache`。此方案遵循了项目中类似 `PumpReadingCache` 的既有成功模式。

-   **数据流**:
    1.  **生产者 (`DevicePoller`)**: 在执行 `Authorize` 或 `Preset` 操作时，将 `employeeID` 写入共享的 `OperatorIDCache` 实例中。
    2.  **消费者 (`TransactionAssembler`)**: 在创建新交易时，从同一个 `OperatorIDCache` 实例中"消费"（一次性读取）这个ID。
-   **优势**:
    -   **松耦合**: 两个核心组件都只依赖于 `OperatorIDCache`，彼此无直接引用。
    -   **职责单一**: `OperatorIDCache` 专门负责管理 `pendingOperatorID` 的生命周期，包括超时逻辑。
    -   **线程安全**: 并发控制逻辑（互斥锁）被完全封装在缓存组件内部。
    -   **模式一致**: 与项目现有架构风格保持一致。

## 3. 实施步骤

### 阶段一：API 层 - 接收员工ID

**任务 1.1: 修改命令请求 DTO**

-   **文件**: `internal/server/dto/wayne_commands.go`
-   **操作**: 在 `WayneCommandRequest` 结构体中添加 `EmployeeID` 字段。这将自动应用于所有内嵌此结构的请求，如 `AuthorizeRequest`, `PresetVolumeRequest` 等。

```go
// in internal/server/dto/wayne_commands.go

type WayneCommandRequest struct {
	CommandID  string           `json:"command_id,omitempty"`
	DeviceID   string           `json:"device_id" validate:"required" example:"device_001"`
	Command    WayneCommandType `json:"command" validate:"required" example:"authorize"`
	EmployeeID string           `json:"employee_id,omitempty" example:"emp_12345"` // 新增字段
	Priority   string           `json:"priority,omitempty" example:"normal"`
	Timeout    int              `json:"timeout,omitempty" example:"30"`
	Async      bool             `json:"async,omitempty" example:"false"`
}
```

### 阶段二：创建 `OperatorIDCache` 组件

**任务 2.1: 定义并实现 `OperatorIDCache`**

-   **新文件**: `internal/services/polling/v2/operator_id_cache.go`
-   **操作**: 创建一个新的Go文件，并实现 `OperatorIDCache` 的完整逻辑。

    ```go
    // in internal/services/polling/v2/operator_id_cache.go
    package v2
    
    import (
    	"sync"
    	"time"
    	"go.uber.org/zap"
    )
    
    const authorizationTimeout = 30 * time.Second
    
    type OperatorIDCache struct {
    	pendingOperatorID  string
    	authorizationTimer *time.Timer
    	logger             *zap.Logger
    	mu                 sync.Mutex
    }
    
    func NewOperatorIDCache(logger *zap.Logger) *OperatorIDCache {
    	return &OperatorIDCache{logger: logger}
    }
    
    func (c *OperatorIDCache) Set(operatorID string) {
    	c.mu.Lock()
    	defer c.mu.Unlock()
    
    	if c.authorizationTimer != nil {
    		c.authorizationTimer.Stop()
    	}
    	c.pendingOperatorID = operatorID
    	c.logger.Info("Pending operator ID set in cache", zap.String("operatorID", operatorID))
    	c.authorizationTimer = time.AfterFunc(authorizationTimeout, func() { c.clear("timeout") })
    }
    
    func (c *OperatorIDCache) Consume() string {
    	c.mu.Lock()
    	defer c.mu.Unlock()
    
    	operatorID := c.pendingOperatorID
    	if operatorID != "" {
    		c.logger.Info("Consuming pending operator ID from cache", zap.String("operatorID", operatorID))
    		c.pendingOperatorID = ""
    		if c.authorizationTimer != nil {
    			c.authorizationTimer.Stop()
    			c.authorizationTimer = nil
    		}
    	}
    	return operatorID
    }
    
    func (c *OperatorIDCache) clear(reason string) {
    	c.mu.Lock()
    	defer c.mu.Unlock()
    
    	if c.pendingOperatorID != "" {
    		c.logger.Info("Clearing pending operator ID from cache",
    			zap.String("reason", reason),
    			zap.String("pendingOperatorID", c.pendingOperatorID))
    		c.pendingOperatorID = ""
    	}
    	if c.authorizationTimer != nil {
    		c.authorizationTimer.Stop()
    		c.authorizationTimer = nil
    	}
    }
    ```

### 阶段三：集成 `OperatorIDCache`

**任务 3.1: 在 `DevicePoller` 中集成**

-   **文件**: `internal/services/polling/v2/device_poller.go`
-   **操作**:
    1.  在 `DevicePoller` 结构体中添加 `operatorIDCache *OperatorIDCache` 字段。
    2.  在 `NewDevicePoller` 中初始化 `operatorIDCache`。
    3.  将 `operatorIDCache` 实例传递给 `NewTransactionAssembler`。
    4.  修改 `AuthorizeDevice`, `PresetVolume` 等方法，使其调用 `p.operatorIDCache.Set(employeeID)` 来写入ID。

**任务 3.2: 在 `TransactionAssembler` 中集成**

-   **文件**: `internal/services/polling/v2/transaction_assembler.go`
-   **操作**:
    1.  在 `TransactionAssembler` 结构体中添加 `operatorIDCache *OperatorIDCache` 字段。
    2.  修改 `NewTransactionAssembler` 的构造函数，使其能够接收 `*OperatorIDCache` 实例。
    3.  在文件顶部的本地 `Transaction` 结构体中添加 `OperatorID string` 字段。
    4.  在 `getOrCreateActiveTransaction` 方法中，调用 `ta.operatorIDCache.Consume()` 来获取员工ID，并赋值给新创建的交易。

### 阶段四：测试

**任务 4.1: 单元测试**
-   为 `OperatorIDCache` 编写单元测试，覆盖 `Set`, `Consume` 和超时 `clear` 的逻辑。

**任务 4.2: 集成测试**
-   编写一个完整的集成测试，模拟以下流程：
    1.  调用 `Authorize` API 并传递 `employee_id`。
    2.  `DevicePoller` 接收请求并将ID存入 `OperatorIDCache`。
    3.  模拟设备提枪，触发 `TransactionAssembler`。
    4.  `TransactionAssembler` 从缓存中消费ID并创建交易。
    5.  验证最终保存到数据库的交易记录包含了正确的 `operator_id`。
    6.  编写另一个测试用例，验证授权超时后 `operator_id` 会被正确清理，不会被后续交易错误使用。 