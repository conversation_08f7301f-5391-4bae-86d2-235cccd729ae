# FCC Fuel Sync 业务链路与使用指南

## 📋 系统概述

FCC Fuel Sync 是基于外部API `http://*************:8080/api/v1/oil/products` 的油品数据同步系统，支持：

- **定时同步**：每小时自动同步油品数据
- **手动触发**：通过API手动触发同步
- **软删除机制**：基于 `is_active` 字段的软删除
- **增量更新**：智能对比本地数据，只更新变化的记录

## 🏗️ 核心组件架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  FuelSyncHandler │    │  SyncScheduler  │    │ FuelGradeSyncService │
│   (API层)       │────│   (调度层)      │────│    (业务层)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Echo Routes   │    │   Timer/Channel │    │  FuelSyncClient │
│    (路由)       │    │    (定时器)     │    │   (HTTP客户端)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                             ┌─────────────────┐
                                             │  External API   │
                                             │ (外部油品API)   │
                                             └─────────────────┘
```

## 🔄 业务流程时序图

### 1. 定时同步流程

```mermaid
sequenceDiagram
    participant S as SyncScheduler
    participant SVC as FuelGradeSyncService
    participant C as FuelSyncClient
    participant API as External API
    participant DB as Database

    Note over S: 每小时触发
    S->>SVC: SyncAll(ctx)
    SVC->>C: FetchProductsWithRetry(ctx)
    C->>API: GET /api/v1/oil/products
    API-->>C: []OilProduct
    C-->>SVC: []OilProduct
    
    SVC->>SVC: ConvertBatchOilProductsToFuelGrades()
    SVC->>DB: 查询本地FuelGrade数据
    DB-->>SVC: map[string]*FuelGrade
    
    SVC->>SVC: performFullSync() or performIncrementalSync()
    
    loop 处理每个油品
        SVC->>DB: 创建/更新/软删除 FuelGrade
        DB-->>SVC: 操作结果
    end
    
    SVC-->>S: SyncStatistics
    S->>S: 更新状态和历史记录
```

### 2. 手动触发流程

```mermaid
sequenceDiagram
    participant U as User
    participant H as FuelSyncHandler
    participant S as SyncScheduler
    participant SVC as FuelGradeSyncService

    U->>H: POST /api/v2/fuel-sync/trigger
    H->>H: 验证请求参数
    H->>S: TriggerManualSync(userID, requestID)
    S->>S: 发送到manualTriggerChan
    H-->>U: 202 {"success": true, "request_id": "..."}
    
    Note over S: 异步处理
    S->>SVC: SyncAll(ctx)
    SVC->>SVC: 执行同步逻辑
    SVC-->>S: SyncStatistics
    S->>S: 更新状态
```

## 🚀 API 接口清单

### 基础路径：`/api/v2/fuel-sync`

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/trigger` | 手动触发同步 | 需要 `user_id` 参数 |
| GET | `/status` | 获取同步状态 | 包含配置和运行状态 |
| GET | `/history` | 获取同步历史 | 支持分页参数 |
| GET | `/statistics` | 获取统计信息 | 成功率、耗时等 |
| GET | `/health` | 健康检查 | 检查服务可用性 |
| GET | `/config` | 获取当前配置 | 返回调度器配置 |
| PUT | `/config` | 更新配置 | 动态调整同步参数 |
| POST | `/start` | 启动调度器 | 手动启动定时同步 |
| POST | `/stop` | 停止调度器 | 手动停止定时同步 |
| GET | `/fuel-grades` | 获取油品列表 | 查询活跃油品 |
| GET | `/fuel-grades/:id` | 获取单个油品 | 查询油品详情 |

## 📊 数据模型映射

### 外部API → 内部模型

| 外部字段 | 内部字段 | 转换规则 |
|----------|----------|----------|
| `id` | `id` | `fmt.Sprintf("oil_%d", id)` |
| `name` | `name` | 直接映射 |
| `category` | `type` | Gasoline→gasoline, Diesel→diesel |
| `current_price` | `price` | `decimal.NewFromInt64(current_price)` |
| `description` | `description` | 直接映射 |
| `grade` | `metadata.grade` | JSON存储 |
| `code` | `metadata.code` | JSON存储 |
| `is_active` | `deleted_at` | `false` 时设置软删除时间 |

### 价格处理（印尼盾）

```go
// 外部API：12360 (印尼盾整数)
// 内部存储：12360.0000 (decimal类型)
price := decimal.NewFromInt64(oilProduct.CurrentPrice)
```

## 🔧 配置说明

### config.yaml 配置示例

```yaml
# 暂未实现，预留配置位置
business:
  fuel_sync:
    enabled: true
    endpoint: "http://*************:8080/api/v1/oil/products"
    sync_interval: "1h"
    timeout: "30s"
    retry_count: 3
    strategy:
      full_sync: true
      incremental_sync: true
      soft_delete: true
```

### 运行时配置

调度器配置通过 `main_v2.go` 中的 `initFuelSyncServices()` 方法初始化：

```go
schedulerConfig := &fuel_sync.SyncSchedulerConfig{
    SyncInterval:       1 * time.Hour,
    StartDelay:         30 * time.Second,
    EnableScheduled:    true,
    EnableManual:       true,
    MaxRetries:         3,
    RetryDelay:         30 * time.Second,
    MaxConsecutiveErrs: 10,
}
```

## 📈 使用示例

### 1. 手动触发同步

```bash
curl -X POST http://localhost:8081/api/v2/fuel-sync/trigger \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "admin",
    "request_id": "sync_20240101_001",
    "force": false
  }'
```

**响应：**
```json
{
  "success": true,
  "message": "Sync triggered successfully",
  "request_id": "sync_20240101_001",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. 查看同步状态

```bash
curl http://localhost:8080/api/v2/fuel-sync/status
```

**响应：**
```json
{
  "is_running": true,
  "last_sync_time": "2024-01-01T11:00:00Z",
  "next_sync_time": "2024-01-01T12:00:00Z",
  "total_syncs": 24,
  "successful_syncs": 23,
  "failed_syncs": 1,
  "consecutive_errors": 0,
  "current_sync_status": "idle",
  "uptime": "24h30m15s",
  "config": {
    "sync_interval": "1h0m0s",
    "enable_scheduled": true,
    "enable_manual": true,
    "max_retries": 3
  }
}
```

### 3. 查看同步历史

```bash
curl "http://localhost:8080/api/v2/fuel-sync/history?page=1&limit=10"
```

### 4. 获取统计信息

```bash
curl http://localhost:8080/api/v2/fuel-sync/statistics
```

## 🔍 监控与故障排除

### 关键指标

- **成功率**：`successful_syncs / total_syncs`
- **平均耗时**：`average_sync_duration`
- **连续错误**：`consecutive_errors`（超过10次会自动停止）
- **吞吐量**：`throughput_per_sec`

### 常见问题

1. **同步失败**：检查外部API可用性和网络连接
2. **数据不一致**：查看 `error_details` 获取具体错误信息
3. **性能问题**：监控 `avg_processing_time` 和 `throughput_per_sec`

### 日志位置

同步相关日志会输出到应用程序日志中，包含以下关键信息：
- 同步开始/结束时间
- 处理的记录数量
- 错误详情
- 性能指标

## 🎯 MVP 特性

当前版本为 MVP（最小可行产品），具备：

✅ **基础功能**
- 定时同步（1小时间隔）
- 手动触发同步
- 软删除支持
- 增量更新

✅ **监控功能**
- 实时状态查询
- 同步历史记录
- 统计信息展示

✅ **容错机制**
- 自动重试（3次）
- 错误记录和报告
- 连续错误阈值保护

## 📅 未来扩展

- 配置文件支持
- 更灵活的同步策略
- 数据库状态持久化
- 更详细的监控指标
- 告警机制集成 