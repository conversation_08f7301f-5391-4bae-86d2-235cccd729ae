# FCC业务流程文档

## 1. 业务处理中枢架构

### 核心组件
```
应用程序协调中枢 (Application)
├── 设备管理中枢 (DeviceManager)    - 设备生命周期管理
├── 命令执行中枢 (CommandExecutor)  - 命令调度执行
├── 状态监控中枢 (StatusMonitor)    - 实时状态监控
└── 适配器管理中枢 (AdapterManager) - 协议适配管理
```

### 代码架构映射
```go
// main.go - 应用程序协调中枢
type Application struct {
    deviceManager   api.DeviceManager      // internal/services/device
    commandExecutor api.CommandExecutor    // internal/services/command  
    statusMonitor   api.StatusMonitor      // internal/services/monitor
    adapterManager  *adapter.Manager       // internal/services/adapter
}
```

### 代码架构映射
```go
// main.go - 应用程序协调中枢
type Application struct {
    deviceManager   api.DeviceManager      // internal/services/device
    commandExecutor api.CommandExecutor    // internal/services/command  
    statusMonitor   api.StatusMonitor      // internal/services/monitor
    adapterManager  *adapter.Manager       // internal/services/adapter
}
```

## 2. DART数据处理业务流程

基于真实数据: `50 30 02 08 00 00 00 00 00 00 00 00 03 04 00 33 33 02 01 01 00 A7 57 03 FA`

### 阶段1: 数据接收解析

#### 代码调用链路 1 - 协议解析
```go
// 📍 internal/adapters/wayne/connection/serial_manager.go
SerialManager.Read() 
    ↓
// 📍 internal/adapters/wayne/dartline/frame.go  
dartline.DecodeFrame(rawData []byte) (*Frame, error)
    ↓ 解析DART帧结构
frame.Address    // 0x50 (设备地址80)
frame.Command    // 0x30 (DATA命令, TX#=0)
frame.Data       // 业务数据部分
frame.CRC        // 0xA757 (校验通过)
    ↓
// 📍 internal/adapters/wayne/pump/transaction.go
TransactionBuilder.ParseDC2(data[0:10])  // DC2: 体积和金额
TransactionBuilder.ParseDC3(data[10:19]) // DC3: 价格和喷枪状态
```

**具体解析逻辑:**
1. **WayneAdapter.receiveData()** - 串口数据接收
2. **dartline.DecodeFrame()** - DART协议帧解析  
3. **pump.TransactionBuilder.ParseXX()** - 业务事务解析
4. **pump.BCDConverter.DecodePrice()** - BCD价格解码: 3.302元/升

### 阶段2: 业务规则引擎

#### 代码调用链路 2 - 业务分析
```go
// 📍 internal/adapters/wayne/adapter.go
WayneAdapter.processIncomingFrame(frame *dartline.Frame)
    ↓ 检测TX序列号
if frame.GetTxNumber() == 0 {
    // 设备重启检测
    ↓
// 📍 internal/services/device/manager.go  
DeviceManager.handleDeviceRestart(deviceID string)
    ↓
DeviceManager.UpdateDeviceStatus(ctx, deviceID, models.DeviceStatusOnline)
    ↓
DeviceManager.clearDeviceCache(deviceID)
    ↓
// 📍 internal/services/monitor/monitor.go
StatusMonitor.PublishStatusEvent(ctx, &api.StatusEvent{
    EventType: api.StatusEventTypeConnected,
    DeviceID:  deviceID,
    Data: map[string]interface{}{
        "restart_detected": true,
        "tx_sequence": 0,
    },
})
```

#### 代码调用链路 3 - 价格验证
```go
// 📍 internal/services/device/manager.go
DeviceManager.validateDevicePrice(deviceID string, devicePrice float64)
    ↓
// 查询系统标准价格
systemPrice := DeviceManager.getSystemPrice(ctx, deviceID)
    ↓  
// 价格差异检测
if math.Abs(devicePrice - systemPrice) > tolerance {
    ↓
// 📍 internal/services/command/executor.go
CommandExecutor.ExecuteCommand(ctx, &api.CommandRequest{
    DeviceID: deviceID,
    Type: "price_sync",
    Parameters: map[string]interface{}{
        "new_price": systemPrice,
    },
})
}
```

### 阶段3: 业务响应处理

#### 代码调用链路 4 - 设备恢复流程
```go
// 📍 internal/services/command/executor.go
CommandExecutor.createDeviceRecoverySequence(deviceID string)
    ↓
// 创建命令序列
commands := []*api.Command{
    // 1. CD1状态查询
    {Type: "status_query", Parameters: map[string]interface{}{"command": 0x01}},
    // 2. CD5价格同步  
    {Type: "price_sync", Parameters: map[string]interface{}{"prices": priceArray}},
    // 3. CD9参数设置
    {Type: "param_set", Parameters: map[string]interface{}{"config": deviceConfig}},
}
    ↓
for _, cmd := range commands {
    CommandExecutor.executeCommandInternal(ctx, execution)
        ↓
    // 📍 internal/services/adapter/manager.go
    AdapterManager.ExecuteCommand(ctx, controller, deviceID, cmd)
        ↓
    // 📍 internal/adapters/wayne/adapter.go
    WayneAdapter.ExecuteCommand(ctx, deviceID, cmd)
        ↓
    // 📍 internal/adapters/wayne/pump/transaction.go
    TransactionBuilder.BuildCD1(command) // 构建DART命令
        ↓
    // 📍 internal/adapters/wayne/dartline/frame.go
    CreateFrame(address, data, txNumber) // 创建DART帧
        ↓
    // 📍 internal/adapters/wayne/connection/serial_manager.go
    SerialManager.Write(frameData) // 发送到设备
}
```

## 3. 核心业务场景

### 设备重启恢复流程

#### 完整代码调用链
```go
// 📊 业务流程: 检测TX#=0 → 标记设备重启 → 清除缓存状态 → 重建通信会话 → 同步设备配置 → 验证恢复状态

// Step 1: 重启检测
// 📍 internal/adapters/wayne/adapter.go
func (w *WayneAdapter) processIncomingFrame(frame *dartline.Frame) {
    if frame.GetTxNumber() == 0 {
        w.handleDeviceRestart(frame.Address)
    }
}

// Step 2: 设备状态管理
// 📍 internal/services/device/manager.go  
func (m *Manager) handleDeviceRestart(deviceID string) error {
    // 2.1 标记设备重启
    device, _ := m.GetDevice(ctx, deviceID)
    device.Status = models.DeviceStatusOnline
    device.LastSeen = &time.Now()
    
    // 2.2 清除缓存状态
    m.clearDeviceCache(deviceID)
    
    // 2.3 数据库更新
    m.saveDeviceToDB(ctx, device)
    
    // 2.4 发布重启事件
    m.publishDeviceEvent(deviceID, "device_restart")
}

// Step 3: 命令执行调度
// 📍 internal/services/command/executor.go
func (e *Executor) handleDeviceRestart(deviceID string) {
    // 3.1 创建恢复命令序列
    recoveryCommands := e.buildRecoverySequence(deviceID)
    
    // 3.2 高优先级执行
    for _, cmd := range recoveryCommands {
        cmd.Priority = api.PriorityHigh
        e.executeCommandInternal(ctx, cmd)
    }
}

// Step 4: 适配器协议转换
// 📍 internal/adapters/wayne/adapter.go
func (w *WayneAdapter) ExecuteCommand(ctx context.Context, deviceID string, command api.Command) (*api.CommandResult, error) {
    // 4.1 构建DART事务
    transaction, _ := w.buildTransaction(command)
    
    // 4.2 发送DART帧
    response, _ := w.sendTransaction(ctx, transaction)
    
    // 4.3 解析响应
    result := w.parseResponse(response)
    return result, nil
}
```

### 价格同步流程

#### 完整代码调用链
```go
// 📊 业务流程: 检测价格差异 → 构建CD5价格命令 → 发送到设备 → 验证设置结果 → 更新数据库记录

// Step 1: 价格差异检测
// 📍 internal/services/device/manager.go
func (m *Manager) validateDevicePrice(deviceID string, devicePrice float64) error {
    // 1.1 查询系统价格
    systemPrice, _ := m.database.GetDevicePrice(ctx, deviceID)
    
    // 1.2 计算差异
    priceDiff := math.Abs(devicePrice - systemPrice)
    
    // 1.3 检查容差
    if priceDiff > m.config.PriceTolerance {
        return m.triggerPriceSync(deviceID, systemPrice)
    }
    return nil
}

// Step 2: 价格同步命令生成
// 📍 internal/services/command/executor.go  
func (e *Executor) createPriceSyncCommand(deviceID string, newPrice float64) *api.Command {
    // 2.1 转换为BCD格式
    bcdPrice := e.bcdConverter.EncodePrice(newPrice, 3, 3)
    
    // 2.2 构建CD5命令
    return &api.Command{
        Type: "price_sync",
        Parameters: map[string]interface{}{
            "transaction_type": "CD5",
            "price_data": bcdPrice,
        },
        Priority: api.PriorityHigh,
    }
}

// Step 3: DART协议转换
// 📍 internal/adapters/wayne/pump/transaction.go
func (b *transactionBuilder) BuildCD5(prices [][]byte) (CDTransaction, error) {
    // 3.1 验证BCD格式
    for _, price := range prices {
        ValidateBCD(price)
    }
    
    // 3.2 构建CD5事务
    transaction := &cdTransaction{
        baseTransaction: baseTransaction{
            transactionType: TransactionTypeCD5,
            length: byte(len(prices) * 3),
            data: concatenatePrices(prices),
        },
    }
    return transaction, nil
}

// Step 4: 串口通信
// 📍 internal/adapters/wayne/connection/serial_manager.go
func (s *SerialManager) SendTransaction(transaction Transaction) (*Response, error) {
    // 4.1 创建DART帧
    frame := dartline.CreateFrame(address, transaction.GetData(), txNumber)
    
    // 4.2 串口发送
    s.port.Write(frame.Encode())
    
    // 4.3 等待响应
    response := s.waitForResponse(25 * time.Millisecond)
    return response, nil
}
```

### 状态监控流程

#### 完整代码调用链
```go
// 📊 业务流程: 实时状态收集 → 状态变更检测 → 事件发布 → 订阅者通知 → 历史数据存储

// Step 1: 状态收集
// 📍 internal/adapters/wayne/adapter.go
func (w *WayneAdapter) monitorDeviceStatus() {
    for _, device := range w.devices {
        status, _ := w.GetDeviceStatus(ctx, device.ID)
        w.processStatusUpdate(device.ID, status)
    }
}

// Step 2: 状态变更检测
// 📍 internal/services/monitor/monitor.go
func (m *Monitor) handleStatusEvent(event *api.StatusEvent) {
    // 2.1 更新状态缓存
    m.updateStatusCache(event)
    
    // 2.2 检测变更类型
    if m.isStatusChanged(event) {
        m.processStatusChange(event)
    }
}

// Step 3: 事件发布机制  
// 📍 internal/services/monitor/monitor.go
func (m *Monitor) PublishStatusEvent(ctx context.Context, event *api.StatusEvent) error {
    // 3.1 事件验证
    if err := m.validateEvent(event); err != nil {
        return err
    }
    
    // 3.2 发送到事件广播通道
    select {
    case m.eventBroadcast <- event:
        return nil
    case <-ctx.Done():
        return ctx.Err()
    }
}

// Step 4: 订阅者通知
// 📍 internal/services/monitor/monitor.go  
func (m *Monitor) broadcastToSubscribers(event *api.StatusEvent) {
    subscribers := m.subscriptions[event.DeviceID]
    
    for subscriberID, eventChan := range subscribers {
        go func(id string, ch chan *api.StatusEvent) {
            select {
            case ch <- event:
                // 事件发送成功
            default:
                // 订阅者通道满，丢弃事件
            }
        }(subscriberID, eventChan)
    }
}

// Step 5: 历史数据存储
// 📍 internal/services/monitor/monitor.go
func (m *Monitor) persistEvent(event *api.StatusEvent) {
    if m.database != nil {
        m.database.SaveStatusEvent(ctx, event)
    }
}
```

### 命令执行流程

#### 完整代码调用链
```go
// 📊 业务流程: 命令接收 → 优先级分配 → 队列调度 → 适配器执行 → 结果收集 → 状态更新 → 响应返回

// Step 1: 命令接收和验证
// 📍 internal/server/handlers/command.go
func (h *Handler) handleExecuteCommand(c echo.Context) error {
    var req api.CommandRequest
    c.Bind(&req)
    
    // 调用命令执行器
    result, err := h.commandExecutor.ExecuteCommand(c.Request().Context(), &req)
    return c.JSON(200, result)
}

// Step 2: 优先级分配和队列管理
// 📍 internal/services/command/executor.go
func (e *Executor) ExecuteCommand(ctx context.Context, req *api.CommandRequest) (*api.CommandResponse, error) {
    // 2.1 命令验证
    if err := e.validateCommand(req); err != nil {
        return nil, err
    }
    
    // 2.2 优先级分配
    priority := e.assignPriority(req)
    
    // 2.3 创建命令执行对象
    execution := &CommandExecution{
        CommandID:   generateID(),
        DeviceID:    req.DeviceID,
        CommandType: req.Type,
        Parameters:  req.Parameters,
        Priority:    priority,
        Status:      StatusPending,
        CreatedAt:   time.Now(),
    }
    
    // 2.4 加入队列
    return e.enqueueCommand(ctx, execution)
}

// Step 3: 队列调度执行
// 📍 internal/services/command/executor.go
func (e *Executor) processCommandQueue() {
    for {
        // 3.1 从优先级队列获取命令
        execution := e.dequeueHighestPriorityCommand()
        
        // 3.2 执行命令
        go e.executeCommandInternal(ctx, execution)
    }
}

// Step 4: 适配器执行
// 📍 internal/services/command/executor.go
func (e *Executor) executeCommandInternal(ctx context.Context, execution *CommandExecution) (*CommandResult, error) {
    // 4.1 获取设备信息
    device, _ := e.deviceMgr.GetDevice(ctx, execution.DeviceID)
    controller, _ := e.deviceMgr.GetController(ctx, device.ControllerID)
    
    // 4.2 构造命令对象
    command := api.Command{
        Type:       execution.CommandType,
        Parameters: execution.Parameters,
        Timeout:    execution.Timeout,
    }
    
    // 4.3 通过适配器管理器执行
    result, err := e.adapterMgr.ExecuteCommand(ctx, controller, execution.DeviceID, command)
    
    // 4.4 更新命令状态
    e.updateCommandStatus(execution.CommandID, result, err)
    
    return result, err
}

// Step 5: 结果收集和状态更新
// 📍 internal/services/command/executor.go
func (e *Executor) updateCommandStatus(commandID string, result *api.CommandResult, err error) {
    e.mu.Lock()
    defer e.mu.Unlock()
    
    execution := e.commands[commandID]
    if execution != nil {
        if err != nil {
            execution.Status = StatusFailed
            execution.ErrorMsg = err.Error()
        } else {
            execution.Status = StatusCompleted
            execution.Result = result
        }
        execution.CompletedAt = time.Now()
    }
}
```

## 4. 企业级特性

### 优先级管理

#### 代码实现
```go
// 📍 internal/services/command/executor.go
type CommandPriority int

const (
    PriorityEmergency   CommandPriority = 1  // 紧急命令: 立即执行
    PriorityHigh        CommandPriority = 3  // 高优先级: 队列头部
    PriorityNormal      CommandPriority = 5  // 普通命令: FIFO执行
    PriorityLow         CommandPriority = 7  // 低优先级: 空闲执行
    PriorityBackground  CommandPriority = 10 // 后台任务: 系统空闲时执行
)

func (e *Executor) dequeueHighestPriorityCommand() *CommandExecution {
    e.mu.Lock()
    defer e.mu.Unlock()
    
    // 按优先级排序获取最高优先级命令
    var sortedCommands []*CommandExecution
    for _, cmd := range e.commands {
        if cmd.Status == StatusPending {
            sortedCommands = append(sortedCommands, cmd)
        }
    }
    
    sort.Slice(sortedCommands, func(i, j int) bool {
        return sortedCommands[i].Priority < sortedCommands[j].Priority
    })
    
    if len(sortedCommands) > 0 {
        return sortedCommands[0]
    }
    return nil
}
```

### 重试策略

#### 代码实现
```go
// 📍 internal/services/command/executor.go
type RetryStrategy struct {
    MaxRetries      int           `yaml:"max_retries"`
    BackoffStrategy string        `yaml:"backoff_strategy"` // exponential, linear, fixed
    BaseDelay       time.Duration `yaml:"base_delay"`
    MaxDelay        time.Duration `yaml:"max_delay"`
}

func (e *Executor) executeWithRetry(ctx context.Context, execution *CommandExecution) error {
    strategy := e.getRetryStrategy(execution.CommandType)
    
    for attempt := 0; attempt <= strategy.MaxRetries; attempt++ {
        err := e.doExecuteCommand(ctx, execution)
        if err == nil {
            return nil // 成功执行
        }
        
        // 检查是否需要重试
        if !e.shouldRetry(err, attempt, strategy.MaxRetries) {
            return err
        }
        
        // 计算退避延迟
        delay := e.calculateBackoffDelay(strategy, attempt)
        select {
        case <-time.After(delay):
            continue
        case <-ctx.Done():
            return ctx.Err()
        }
    }
    
    return fmt.Errorf("command failed after %d retries", strategy.MaxRetries)
}

func (e *Executor) calculateBackoffDelay(strategy RetryStrategy, attempt int) time.Duration {
    switch strategy.BackoffStrategy {
    case "exponential":
        delay := strategy.BaseDelay * time.Duration(math.Pow(2, float64(attempt)))
        if delay > strategy.MaxDelay {
            return strategy.MaxDelay
        }
        return delay
    case "linear":
        return strategy.BaseDelay * time.Duration(attempt+1)
    case "fixed":
        return strategy.BaseDelay
    default:
        return strategy.BaseDelay
    }
}
```

### 事务支持

#### 代码实现
```go
// 📍 internal/services/command/executor.go
type TransactionContext struct {
    TransactionID string
    Commands      []*CommandExecution
    Status        TransactionStatus
    CreatedAt     time.Time
    CompletedAt   *time.Time
}

func (e *Executor) ExecuteTransaction(ctx context.Context, commands []*api.CommandRequest) (*TransactionResult, error) {
    // 创建事务上下文
    txCtx := &TransactionContext{
        TransactionID: generateTransactionID(),
        Commands:      make([]*CommandExecution, len(commands)),
        Status:        TransactionStatusPending,
        CreatedAt:     time.Now(),
    }
    
    // 转换为命令执行对象
    for i, cmd := range commands {
        txCtx.Commands[i] = &CommandExecution{
            CommandID:     generateID(),
            TransactionID: txCtx.TransactionID,
            DeviceID:      cmd.DeviceID,
            CommandType:   cmd.Type,
            Parameters:    cmd.Parameters,
            Status:        StatusPending,
        }
    }
    
    // 执行事务
    return e.executeTransaction(ctx, txCtx)
}

func (e *Executor) executeTransaction(ctx context.Context, txCtx *TransactionContext) (*TransactionResult, error) {
    txCtx.Status = TransactionStatusExecuting
    
    // 顺序执行所有命令
    for _, cmd := range txCtx.Commands {
        err := e.executeCommandInternal(ctx, cmd)
        if err != nil {
            // 命令失败，回滚事务
            e.rollbackTransaction(ctx, txCtx)
            return nil, fmt.Errorf("transaction failed at command %s: %w", cmd.CommandID, err)
        }
    }
    
    // 事务成功
    txCtx.Status = TransactionStatusCompleted
    now := time.Now()
    txCtx.CompletedAt = &now
    
    return &TransactionResult{
        TransactionID: txCtx.TransactionID,
        Status:        txCtx.Status,
        Commands:      txCtx.Commands,
    }, nil
}

func (e *Executor) rollbackTransaction(ctx context.Context, txCtx *TransactionContext) error {
    // 反向执行回滚命令
    for i := len(txCtx.Commands) - 1; i >= 0; i-- {
        cmd := txCtx.Commands[i]
        if cmd.Status == StatusCompleted {
            rollbackCmd := e.createRollbackCommand(cmd)
            e.executeCommandInternal(ctx, rollbackCmd)
        }
    }
    
    txCtx.Status = TransactionStatusRolledBack
    return nil
}
```

## 5. 异常处理

### 设备离线处理

#### 代码实现
```go
// 📍 internal/services/device/manager.go
func (m *Manager) handleDeviceOffline(deviceID string) error {
    // 1. 标记设备离线
    device, _ := m.GetDevice(ctx, deviceID)
    oldStatus := device.Status
    device.Status = models.DeviceStatusOffline
    device.UpdatedAt = time.Now()
    
    // 2. 停止向设备发送命令
    m.commandExecutor.SuspendCommandsForDevice(deviceID)
    
    // 3. 发布离线事件
    m.statusMonitor.PublishStatusEvent(ctx, &api.StatusEvent{
        EventType: api.StatusEventTypeDisconnected,
        DeviceID:  deviceID,
        Data: map[string]interface{}{
            "old_status": oldStatus,
            "new_status": device.Status,
            "offline_time": time.Now(),
        },
    })
    
    // 4. 启动重连机制
    go m.startReconnectionProcess(deviceID)
    
    return nil
}

func (m *Manager) startReconnectionProcess(deviceID string) {
    maxRetries := 10
    retryInterval := 30 * time.Second
    
    for attempt := 1; attempt <= maxRetries; attempt++ {
        time.Sleep(retryInterval)
        
        // 尝试重新连接
        if err := m.attemptReconnection(deviceID); err == nil {
            // 重连成功
            m.handleDeviceOnline(deviceID)
            return
        }
        
        // 增加重连间隔
        retryInterval = time.Duration(float64(retryInterval) * 1.5)
        if retryInterval > 5*time.Minute {
            retryInterval = 5 * time.Minute
        }
    }
    
    // 达到最大重试次数，标记设备故障
    m.markDeviceAsFaulty(deviceID)
}
```

### 通信异常处理

#### 代码实现
```go
// 📍 internal/adapters/wayne/connection/serial_manager.go
func (s *SerialManager) handleCommunicationError(err error) error {
    switch {
    case errors.Is(err, ErrCRCMismatch):
        // CRC校验错误 - 重新发送
        return s.retryLastCommand()
        
    case errors.Is(err, ErrSerialDisconnected):
        // 串口断开 - 重新连接
        return s.reconnectSerial()
        
    case errors.Is(err, ErrDeviceTimeout):
        // 设备超时 - 检查设备状态
        return s.checkDeviceStatus()
        
    case errors.Is(err, ErrDeviceNoResponse):
        // 设备无响应 - 标记离线
        return s.markDeviceOffline()
        
    default:
        return err
    }
}

func (s *SerialManager) retryLastCommand() error {
    if s.lastCommand != nil {
        return s.sendCommand(s.lastCommand)
    }
    return nil
}

func (s *SerialManager) reconnectSerial() error {
    s.disconnect()
    time.Sleep(1 * time.Second)
    return s.connect()
}
```

## 6. 性能优化

### 连接池管理

#### 代码实现
```go
// 📍 internal/services/adapter/manager.go
func (m *Manager) GetOrCreateAdapter(ctx context.Context, controller *models.Controller) (api.DeviceAdapter, error) {
    m.mu.RLock()
    adapter, exists := m.adapters[controller.ID]
    m.mu.RUnlock()
    
    // 检查现有适配器
    if exists && adapter.IsConnected() {
        return adapter, nil
    }
    
    m.mu.Lock()
    defer m.mu.Unlock()
    
    // 双重检查
    adapter, exists = m.adapters[controller.ID]
    if exists && adapter.IsConnected() {
        return adapter, nil
    }
    
    // 创建新适配器
    config := m.createAdapterConfig(controller)
    newAdapter, err := m.factory.CreateAdapter(controller.Protocol, config)
    if err != nil {
        return nil, fmt.Errorf("failed to create adapter: %w", err)
    }
    
    // 连接适配器
    if err := newAdapter.Connect(ctx, config); err != nil {
        return nil, fmt.Errorf("failed to connect adapter: %w", err)
    }
    
    // 缓存适配器
    m.adapters[controller.ID] = newAdapter
    m.configs[controller.ID] = config
    
    return newAdapter, nil
}

func (m *Manager) HealthCheck(ctx context.Context) map[string]bool {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    health := make(map[string]bool)
    for controllerID, adapter := range m.adapters {
        health[controllerID] = adapter.IsConnected()
        
        // 如果连接断开，尝试重连
        if !adapter.IsConnected() {
            go m.ReconnectAdapter(ctx, controllerID)
        }
    }
    
    return health
}
```

### 批量处理

#### 代码实现
```go
// 📍 internal/services/command/executor.go
func (e *Executor) ExecuteBatchCommands(ctx context.Context, requests []*api.CommandRequest) ([]*api.CommandResponse, error) {
    // 按控制器分组
    groupedCommands := e.groupCommandsByController(requests)
    
    responses := make([]*api.CommandResponse, len(requests))
    var wg sync.WaitGroup
    var mu sync.Mutex
    
    for controllerID, commands := range groupedCommands {
        wg.Add(1)
        go func(cID string, cmds []*api.CommandRequest) {
            defer wg.Done()
            
            // 并发执行同一控制器的命令
            batchResults := e.executeBatchForController(ctx, cID, cmds)
            
            // 合并结果
            mu.Lock()
            for i, result := range batchResults {
                responses[getOriginalIndex(cmds[i])] = result
            }
            mu.Unlock()
        }(controllerID, commands)
    }
    
    wg.Wait()
    return responses, nil
}

func (e *Executor) groupCommandsByController(requests []*api.CommandRequest) map[string][]*api.CommandRequest {
    groups := make(map[string][]*api.CommandRequest)
    
    for _, req := range requests {
        device, _ := e.deviceMgr.GetDevice(ctx, req.DeviceID)
        controllerID := device.ControllerID
        groups[controllerID] = append(groups[controllerID], req)
    }
    
    return groups
}
```

## 7. 监控指标

### 业务指标收集

#### 代码实现
```go
// 📍 internal/services/monitor/monitor.go
type BusinessMetrics struct {
    CommandExecutionTime  time.Duration `json:"command_execution_time"`
    DeviceOnlineRate     float64       `json:"device_online_rate"`
    CommandSuccessRate   float64       `json:"command_success_rate"`
    PriceSyncAccuracy    float64       `json:"price_sync_accuracy"`
    TransactionTPS       float64       `json:"transaction_tps"`
    ResponseTime         time.Duration `json:"response_time"`
}

func (m *Monitor) CollectBusinessMetrics() *BusinessMetrics {
    return &BusinessMetrics{
        CommandExecutionTime: m.calculateAvgCommandTime(),
        DeviceOnlineRate:     m.calculateDeviceOnlineRate(),
        CommandSuccessRate:   m.calculateCommandSuccessRate(),
        PriceSyncAccuracy:    m.calculatePriceSyncAccuracy(),
        TransactionTPS:       m.calculateTransactionTPS(),
        ResponseTime:         m.calculateAvgResponseTime(),
    }
}

func (m *Monitor) calculateDeviceOnlineRate() float64 {
    totalDevices := len(m.statusCache)
    onlineDevices := 0
    
    for _, status := range m.statusCache {
        if status.Status == models.DeviceStatusOnline {
            onlineDevices++
        }
    }
    
    return float64(onlineDevices) / float64(totalDevices) * 100
}
```

## 8. 总结

通过详细的代码调用链路分析，我们可以看到FCC系统构建了一个**完整的企业级业务处理中枢**，具备：

### 核心能力
- ✅ **多层次业务协调** - 从Application到各Service层的完整调用链
- ✅ **事件驱动架构** - StatusMonitor的实时事件处理机制
- ✅ **企业级特性** - CommandExecutor的优先级、重试、事务支持
- ✅ **异常处理机制** - 全方位的错误检测和恢复流程
- ✅ **性能优化** - AdapterManager的连接池和批量处理

### 代码架构优势
- 🚀 **清晰的分层** - 每层职责明确，调用关系清晰
- 🛡️ **完整的错误处理** - 每个调用链都有对应的异常处理
- 📊 **实时监控** - 完整的业务指标收集和状态监控
- 🔧 **高度可配置** - 支持动态配置和策略调整
- 🏢 **企业级** - 支持复杂业务场景和大规模部署

这个业务处理中枢不仅处理简单的设备通信，更是一个**完整的企业级设备管理和控制平台**，通过清晰的代码调用链路实现了复杂业务逻辑的可靠执行。 