# 交易生命周期服务重构方案 (V2.1 - 数据驱动模型)

**文档版本**: 1.0  
**创建日期**: 2024-12-20  
**状态**: 规划中  
**负责人**: FCC开发团队  
**关联组件**: TransactionLifecycleService, NozzleCountersService

---

## 1. 概述

本文档旨在规划对 `TransactionLifecycleService` 的一次重大架构重构。当前服务主要依赖设备的状态信号（`DC1 FILLING_COMPLETED`）来完成交易，这种模式在复杂或异常场景下容易导致数据不一致。

本次重构的核心目标是：**将交易完成的触发机制从"状态驱动"转变为"数据驱动"**。我们将以最可靠的 `DC101` 累计泵码事件作为交易完成的最终"铁证"，从而确保交易数据与物理世界的加油量/金额一一对应，实现系统的强一致性和可审计性。

## 2. 核心问题与痛点 (Problem Statement)

### 2.1 当前架构存在的问题

1. **数据一致性风险**: 当前`DC1`状态与最终的`DC101`泵码数据是解耦的。可能出现交易已完成，但泵码数据丢失或不匹配的情况，导致账目不平。

2. **交易完成的不可靠性**: `DC1` 状态信号可能因网络波动而丢失，或因设备固件问题而不发送，导致交易无法完成。

3. **异常交易难以追溯**: 对于因泵码不匹配而产生的"坏账"交易，当前架构缺乏有效的识别和标记机制。

4. **竞态条件**: `DC1` 和 `DC101` 事件的到达顺序不确定，当前逻辑难以优雅地处理这种竞态问题。

### 2.2 业务影响

- **财务风险**: 交易数据与实际加油量不一致，可能导致财务损失
- **审计困难**: 无法准确追溯异常交易的产生原因
- **系统可靠性**: 在网络不稳定环境下，交易完成率受到影响

## 3. 新架构设计 (Proposed Architecture)

### 3.1 核心设计原则

- **数据是唯一真相**: 只有收到了新的、有效的`DC101`累计泵码增量，才能确认一笔交易的最终完成。
- **状态是过程记录**: `DC1 FILLING_COMPLETED`不再是完成信号，而是将交易推入一个"待验证"的中间状态。
- **强验证机制**: 每笔交易的`ActualVolume`必须与其对应的泵码增量进行验证。

### 3.2 新的交易状态机

我们将在交易模型中引入一个新的状态：`pending_counters`。

```mermaid
stateDiagram-v2
    [*] --> Initiated: DC3 拔枪

    Initiated --> Filling: DC2 数据更新
    Filling --> Filling: DC2 数据持续更新

    Filling --> PendingCounters: DC1 FILLING_COMPLETED
    PendingCounters --> Completed: DC101 泵码验证通过
    PendingCounters --> Cancelled: 超时未收到泵码

    state any_active_state {
        Initiated --> Cancelled
        Filling --> Cancelled
    }
```

### 3.3 核心处理流程

#### 3.3.1 DC1 `FILLING_COMPLETED` 处理器

**触发**: 收到`DC1`状态`0x05`

**动作**:
1. 查找该喷嘴 `filling` 状态的交易
2. 将其状态更新为 `pending_counters`
3. 记录状态变更历史，并设置一个**等待泵码的超时时间**。
   - **实现说明**: 为简化初期实现，该超时时间（例如5分钟）将作为服务内的一个静态变量，无需修改配置。
4. **不**触发交易完成事件
5. **反向匹配**: 立即查询 `NozzleCounterService`，寻找该喷嘴近期内（例如过去60秒内）有无尚未被匹配的`DC101`泵码记录。如果找到，则直接进入完成逻辑。

#### 3.3.2 DC101 `Counter Update` 处理器 (新的完成核心)

**触发**: 收到新的`DC101`泵码事件

**动作**:
1. **验证**: 调用`NozzleCounterService`，确认这是一个新的、有效的累计量增长。此步骤会将泵码记录持久化。
2. **查找**: 寻找该喷嘴 `pending_counters` 状态的交易。
3. **匹配与完成**:
   - **找到交易**:
     - 对比交易的`ActualVolume`与泵码增量。
     - **一致**: 更新最终泵码，状态切换至 `completed`，质量标记为 `good`，发布完成事件。
     - **不一致**: 更新最终泵码，状态切换至 `completed`，质量标记为 `anomaly`，记录差异日志，发布完成事件。
   - **未找到交易**: 说明`DC1`事件延迟或丢失。
     - **依赖持久化**: 无需在交易服务中进行内存暂存。`NozzleCounterService`已将此`DC101`事件作为一条新的泵码流水记录持久化到数据库。
     - **等待反向匹配**: 后续当`DC1`事件到达并将交易置为`pending_counters`状态时，其处理器会执行“反向匹配”逻辑来完成此交易。

### 3.4 边界情况处理 (Edge Cases)

#### 3.4.1 泵码丢失场景
- **问题**: DC1事件已处理，但DC101事件永久丢失
- **解决**: 处于`pending_counters`状态的交易如果超时未收到`DC101`事件，将被定时任务清理，状态置为`cancelled`或`failed_no_counters`

#### 3.4.2 DC1事件丢失场景
- **问题**: DC101事件到达，但DC1事件丢失
- **解决**: 如果`DC101`事件到达，但长时间没有`pending_counters`的交易，可根据业务规则（例如，有一个长时间未更新的`filling`交易）进行推断性完成，并标记为`inferred_completion`。

#### 3.4.3 事件乱序场景
- **问题**: DC101事件先于DC1事件到达
- **解决**: **无需内存暂存**。`DC101`事件由`NozzleCounterService`先行持久化。后到的`DC1`事件处理器将主动查询近期泵码记录完成匹配，此设计从根本上解决了因服务重启导致暂存数据丢失的风险。

#### 3.4.4 数据不匹配场景
- **问题**: 交易记录的体积与泵码增量不一致
- **解决**: 仍然完成交易，但标记为异常，记录详细差异信息供审计

## 4. 开发规划 (Development Plan)

### 4.1 阶段一: 模型与基础准备 (Foundation & Modeling)

**目标**: 为新架构建立数据和接口基础，此阶段不修改核心逻辑

**修改范围**:

#### 4.1.1 数据模型扩展
- **文件**: `pkg/models/transaction.go`
- **修改内容**:
  - 在`TransactionStatus`中添加`pending_counters`状态
  - 在`Transaction`模型中增加`PendingCountersAt *time.Time`字段，用于超时处理
  - 添加数据验证方法和状态转换方法

#### 4.1.2 服务接口评估
- **文件**: `internal/services/nozzle_counters/service.go`
- **评估内容**:
  - 确认`RecordDC101Event`或`GetLatestCounterValue`能够满足"验证新增量"的需求
  - 如有必要，微调其返回值以支持新的验证逻辑
  - 评估性能影响

#### 4.1.3 架构文档更新
- 更新状态机图和流程图
- 编写接口变更说明
- 制定数据库迁移计划（如需要）

**交付物**:
- [ ] 扩展的Transaction模型
- [ ] 更新的接口文档
- [ ] 数据库迁移脚本（如需要）
- [ ] 架构设计文档

### 4.2 阶段二: 核心逻辑重构 (Core Logic Refactoring)

**目标**: 实现新的、由数据驱动的交易完成逻辑

**修改范围**:

#### 4.2.1 交易生命周期服务重构
- **文件**: `internal/services/polling/v2/transaction_lifecycle.go`
- **修改内容**:
  - **重写 `HandleFillingCompleted`**: 实现状态从`filling`到`pending_counters`的转换，并**增加反向查询近期泵码**的逻辑。
  - **重写 `HandleDC101CounterUpdate`**: 实现全新的交易查找、数据验证、和最终完成逻辑。
  - **移除内存暂存机制**: 移除旧有的、不安全的内存缓存逻辑。
  - **添加数据验证逻辑**: 对比交易数据与泵码增量的验证算法。

#### 4.2.2 接口层更新
- **文件**: `internal/services/polling/v2/interfaces.go`
- **修改内容**:
  - 更新接口方法签名（如需要）
  - 添加新的错误类型定义
  - 更新方法文档

**交付物**:
- [ ] 重构的交易生命周期服务
- [ ] 更新的服务接口
- [ ] 内部设计文档

### 4.3 阶段三: 健壮性与容错 (Robustness & Fault Tolerance)

**目标**: 处理边界情况和潜在的故障，确保系统在异常时也能优雅运行

**修改范围**:

#### 4.3.1 超时与清理机制
- **文件**: `internal/services/polling/v2/transaction_lifecycle.go`
- **修改内容**:
  - 实现`pending_counters`状态的交易超时清理逻辑。
    - **实现说明**: 超时阈值设置为一个内部静态变量（如 `5 * time.Minute`），避免初期引入复杂配置。
  - 可通过新的后台任务或集成到现有`CleanupStaleTransactions`中。

#### 4.3.2 监控与观测
- **修改范围**: 日志与监控相关文件
- **修改内容**:
  - 在关键节点添加详细的`Warn`和`Error`级别的日志
  - 添加新的监控指标：
    - `pending_counters`状态的交易数量
    - 泵码不匹配的交易数量
    - DC101事件暂存数量
    - 交易完成成功率

#### 4.3.3 错误处理增强
- 完善异常场景的处理逻辑
- 添加降级策略
- 实现告警机制

**交付物**:
- [ ] 完善的容错机制
- [ ] 监控指标和告警
- [ ] 运维手册

### 4.4 阶段四: 测试与验证 (Testing & Validation)

**目标**: 确保新架构的正确性和稳定性，防止功能退化

**修改范围**:

#### 4.4.1 单元测试
- **文件**: `internal/services/polling/v2/transaction_lifecycle_test.go`
- **测试场景**:
  - 正常流程（DC1 -> DC101 -> 完成）
  - DC101先于DC1到达的流程
  - 数据匹配与不匹配的场景
  - 泵码丢失导致交易超时的场景
  - 暂存机制的超时清理
  - 并发场景测试

#### 4.4.2 集成测试
- 设计并执行端到端的集成测试
- 模拟加油机硬件的行为
- 测试网络异常场景
- 压力测试和性能测试

#### 4.4.3 回归测试
- 确保现有功能不受影响
- 验证向后兼容性
- 数据一致性验证

**交付物**:
- [ ] 完整的测试套件
- [ ] 性能测试报告
- [ ] 测试覆盖率报告

## 5. 风险评估 (Risk Assessment)

### 5.1 技术风险

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| **(已解决)** ~~DC101事件暂存数据丢失~~ | ~~中~~ | ~~内存暂存机制在服务重启时会丢失数据，导致交易无法完成。~~ | **方案已优化**: 采用`NozzleCounterService`的数据库持久化代替内存暂存，彻底规避了此风险。 |
| 状态积压风险 | 中等 | `pending_counters`状态交易积压，可能消耗内存，或导致交易丢失。 | 完善超时清理机制，并添加对`pending_counters`状态交易数量的监控告警。超时时间初期设为静态变量，后续可配置。 |
| 性能风险 | 低 | 新的“反向匹配”逻辑在处理`DC1`时增加了数据库查询，可能增加处理时间。 | 对`NozzleCounterService`的查询进行性能测试和SQL优化，确保查询效率。 |
| 依赖`NozzleCounterService`的健壮性 | 中 | 如果`NozzleCounterService`的记录或查询逻辑有Bug，会直接影响交易的正确完成。 | 必须在阶段一对其进行严格的代码审查和单元测试，覆盖所有边界场景。 |


### 5.2 业务风险

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| 功能回退 | 低 | 新逻辑可能影响现有功能 | 充分的回归测试和灰度发布 |
| 用户体验 | 低 | 交易完成延迟可能增加 | 性能优化和用户沟通 |

## 6. 成功标准 (Success Criteria)

### 6.1 功能标准
- [ ] 生产环境中，因数据不一致导致的坏账交易数量降至零
- [ ] 日志系统能够清晰地追溯每一笔交易从创建到完成（或异常）的完整路径
- [ ] 所有边界情况都有相应的处理逻辑和测试覆盖

### 6.2 性能标准
- [ ] DC101事件处理时间不超过当前基准的120%
- [ ] 监控仪表盘显示`pending_counters`状态的交易数量稳定在合理范围内（接近于零）
- [ ] 系统内存使用量保持稳定，无明显增长趋势

### 6.3 质量标准
- [ ] 所有新增和修改的测试用例100%通过
- [ ] 代码覆盖率不低于85%
- [ ] 通过代码审查和安全审查

## 7. 时间计划 (Timeline)

| 阶段 | 预计时间 | 里程碑 |
|------|----------|--------|
| 阶段一：模型与基础准备 | 1周 | 完成数据模型扩展和接口设计 |
| 阶段二：核心逻辑重构 | 2周 | 完成主要功能重构 |
| 阶段三：健壮性与容错 | 1周 | 完成容错机制和监控 |
| 阶段四：测试与验证 | 1周 | 完成测试和性能验证 |
| **总计** | **5周** | 完成重构并准备发布 |

## 8. 后续优化方向

### 8.1 短期优化
- 基于实际运行数据调优超时参数
- 完善监控指标和告警规则
- 优化数据验证算法

### 8.2 长期演进
- 考虑将暂存机制持久化，提高系统重启后的数据恢复能力
- 探索基于机器学习的异常交易检测
- 实现更智能的数据匹配算法

---

**文档维护**: 本文档将在重构过程中持续更新，确保与实际实现保持一致。 