# 📊 Nozzle 累计数量流水逻辑架构文档 (v3.0 - Final)

## 🎯 **核心设计原则 (v3.0)**

### **有状态分层去重 (State-Aware Layered De-duplication)**
为解决高频写入、长期状态不变和累计值归零三大核心场景的冲突，采用**三层防护**的去重架构：

1.  **L1 - 高性能事件缓存 (The Fast Lane)**: 在应用层使用高速、短时效的事件缓存，以极高性能拦截掉绝大多数由轮询产生的短期、重复事件。
2.  **L2 - 有状态业务逻辑核心 (The Logic Core)**: 这是去重的核心，通过查询数据库获取**上一个已持久化的状态**，进行有状态的比较，从而处理长期状态不变和累计值归零的复杂业务场景。
3.  **L3 - 数据库安全网 (The Safety Net)**: 在数据库层面建立一个最小化的、基于时刻的唯一性约束，作为防止并发和极端情况下数据错乱的最后一道防线。

### **业务逻辑分离 (不变)**
- **DC101流水记录**: 由本架构负责，保证流水的幂等性和准确性。
- **交易生命周期管理**: 业务层负责，处理交易状态变更。

## 🔑 **关键业务场景分析**

本架构设计旨在同时解决以下三个核心业务场景：

1.  **高频短期重复 (Polling an Idle Nozzle)**: 设备在空闲时会高频上报相同的累计值。**L1事件缓存**将拦截这些请求。
2.  **长期状态不变 (The "Two Days No Fuel" Problem)**: 一个喷嘴几天甚至几周不上报数据，其状态（累计值）未变。后续的上报应被识别为状态未变，而非新数据。**L2业务逻辑核心**将通过与数据库旧值比较来处理此问题。
3.  **累计值归零 (The "Counter Rollover" Problem)**: 累计值达到最大后会归零，导致新的值与历史值重复。**L2业务逻辑核心**将通过新旧值的大小比较来识别“归零”事件，并将其作为一次合法的状态变更来处理。

## 📋 **数据表结构 (不变)**

```go
// v3.0: 结构不变，但其承载的业务逻辑已更新
type NozzleCounters struct {
    ID                   int64            // 流水记录主键，自增
    DeviceID             string           // 设备ID
    NozzleID             string           // 喷嘴ID
    CounterType          int16            // DC101计数器类型
    CounterValue         decimal.Decimal  // 当前累计计数器值
    PreviousValue        *decimal.Decimal // 上一次记录的累计值
    IncrementValue       *decimal.Decimal // 本次增量值
    DC101TransactionData []byte           // 原始DC101事务数据
    IsValid              bool             // 数据是否有效
    CreatedAt            time.Time        // 创建时间
    RecordedAt           time.Time        // DC101事务发生时间
}
```

## 🏗️ **系统架构图 (v3.0)**

```mermaid
graph TD
    subgraph "设备层"
        D[Wayne DART设备]
        D -->|DC101事务| P[DevicePoller]
    end
    
    subgraph "协议与服务层"
        P -->|ProcessDC101| NCS[NozzleCountersService]
        
        subgraph "L1: 事件缓存 (Fast Lane)"
            NCS -->|1. 检查事件缓存| L1Cache[Event Cache (SetNX, 30min TTL)]
        end
        
        L1Cache -- 新事件 --> L2Logic
        L1Cache -- 短期重复 --> Success1[✅ 成功 (丢弃)]

        subgraph "L2: 业务逻辑核心 (Logic Core)"
             L2Logic{查询DB获取上一个状态} --> L2Compare{比较新旧状态}
        end

        L2Compare -- 状态未变 --> Success2[✅ 成功 (丢弃)]
        L2Compare -- 状态改变 --> L3DB[L3: 数据库]

    end
    
    subgraph "L3: 数据库 (Safety Net)"
        L3DB -- INSERT --> NC[nozzle_counters 表]
        note top of L3DB: 带有时刻唯一性约束
    end
```

## ⏱️ **时序图 (v3.0)**

```mermaid
sequenceDiagram
    participant App as 应用服务
    participant L1Cache as L1: 事件缓存
    participant L2Logic as L2: 业务逻辑核心
    participant L3DB as L3: 数据库

    App->>L1Cache: 检查事件 (SetNX, 30min TTL)
    alt L1 命中 (短期重复)
        L1Cache-->>App: 是重复
        Note right of App: ✅ 成功 (丢弃)
    else L1 未命中 (新事件)
        L1Cache-->>App: 不是重复
        App->>L2Logic: 处理状态(新值V, 时间T)
        L2Logic->>L3DB: 查询上一个状态 (旧值P, 时间Tp)
        L3DB-->>L2Logic: 返回 (P, Tp)
        
        alt 状态未变 (V == P)
            L2Logic-->>App: ✅ 成功 (丢弃)
        else 状态改变 (V != P)
            Note over L2Logic: 计算增量/处理归零
            L2Logic->>L3DB: INSERT 新记录
            alt 约束冲突 (极端情况)
                 L3DB-->>L2Logic: UniqueConstraintError
                 L2Logic-->>App: ✅ 成功 (丢弃)
            else 插入成功
                 L3DB-->>L2Logic: 成功
                 L2Logic-->>App: ✅ 成功
            end
        end
    end
```

## 🛡️ **全新分层去重机制 (v3.0)**

### **1. 第一层：高性能事件缓存 (The Fast Lane)**
- **目的**: 拦截最高频的、由设备轮询产生的短期重复事件，避免访问数据库。
- **技术**: 利用 `storage.Cache` 接口，调用原子操作 `SetNX`。
- **Key**: `event-dedup:{deviceID}:{nozzleID}:{value}` (不含时间戳)。
- **TTL**: 一个相对较短的业务窗口，例如 **30分钟**。
- **逻辑**: 如果 `SetNX` 失败，意味着30分钟内已有完全相同的事件，直接判定为重复并成功返回。

### **2. 第二层：有状态业务逻辑核心 (The Logic Core)**
- **目的**: 处理通过了第一层缓存的事件，进行真正的状态变更判断。这是解决“长期状态不变”和“累计值归零”场景的核心。
- **技术**: `SELECT-then-INSERT` 模式。
- **逻辑**:
    1.  从数据库查询该喷嘴的**最后一条**记录。
    2.  **值比较**: 如果新值与旧值相同，则判定为状态未变，成功返回。
    3.  **时间比较**: 如果新事件的时间戳早于旧记录，判定为乱序，成功返回。
    4.  **归零判断**: 如果新值小于旧值，判定为“归零”事件，计算正确的增量后，继续执行插入。
    5.  **正常递增**: 计算增量，继续执行插入。

### **3. 第三层：数据库安全网 (The Safety Net)**
- **目的**: 作为数据完整性的最终保障，防止因并发竞争或时钟问题导致的极端情况。
- **技术**: 在数据库表上建立一个最小化的**时刻唯一性约束**。
- **逻辑**: 详见下一节。

## 📊 **性能优化**

### **1. 数据库唯一索引 (时刻唯一性)**
```sql
-- 移除其他可能存在的、基于值的唯一约束
ALTER TABLE nozzle_counters DROP CONSTRAINT IF EXISTS uq_nozzle_counters_business_key;
ALTER TABLE nozzle_counters DROP CONSTRAINT IF EXISTS idx_nozzle_counters_dedup_24hr;

-- 创建基于时刻的唯一约束
CREATE UNIQUE INDEX uq_nozzle_counters_at_time
ON nozzle_counters (device_id, nozzle_id, counter_type, recorded_at);
```
- **工作原理**: 它只保证同一个喷嘴在**完全相同的时间点**（精确到数据库的时间戳精度）只能有一条记录。
- **合理性**:
    - 它不关心 `counter_value`，因此**不会**与“累计值归零”场景冲突。
    - 它不依赖任何时间窗口，因此**不会**与“长期状态不变”场景冲突。
    - 它为并发`INSERT`提供了最终的安全保障。

### **2. 查询性能**
- **必要索引**: 为 `L2业务逻辑核心` 的 `SELECT` 查询，需要一个高效的索引。
```sql
CREATE INDEX idx_nozzle_counters_lookup
ON nozzle_counters (device_id, nozzle_id, counter_type, recorded_at DESC);
```
- 这个索引可以极快地（`O(log N)`）定位到任意喷嘴的最后一条记录。

---

## 📝 **总结**

Nozzle 累计数量流水系统采用最终确定的**有状态分层去重架构**：

1.  **L1 - 事件缓存**: 使用短时效的 `SetNX` 解决**高频短期重复**问题。
2.  **L2 - 业务逻辑核心**: 使用 `SELECT-then-INSERT` 模式，通过与历史状态比较，解决**长期状态不变**和**累计值归零**问题。
3.  **L3 - 数据库安全网**: 使用基于时刻的唯一约束，提供**最终的数据一致性保障**。

这种设计通过分层，用最合适的武器解决对应的问题，确保了系统在**高性能、高可靠性和业务逻辑完备性**之间取得了最佳平衡。 