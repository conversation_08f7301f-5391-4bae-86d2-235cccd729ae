# Wayne DART 协议 CMD 解释文档

## 目录
- [1. 协议概述](#1-协议概述)
- [2. <PERSON> 事务 (Device to Controller)](#2-dc-事务-device-to-controller)
- [3. CD 事务 (Controller to Device)](#3-cd-事务-controller-to-device)
- [4. CD/DC 交互过程](#4-cddc-交互过程)
- [5. 实际应用场景](#5-实际应用场景)

---

## 1. 协议概述

Wayne DART 协议是基于主从式半双工通信的设备控制协议，采用轮询机制进行数据交换。协议分为两类事务：

- **CD 事务 (Controller to Device)**: 站控系统向泵设备发送的命令和配置
- **DC 事务 (Device to Controller)**: 泵设备向站控系统回复的状态和数据

### 通信架构
```
Site Controller (站控系统)
       ↓ CD事务 (命令/配置)
   DART-LINE Protocol
       ↑ DC事务 (状态/数据)
Pump Device (泵设备)
```

### 数据帧格式
```
完整DART帧: [ADR][CTRL][TRANS][LNG][DATA...][CRC-1][CRC-2][ETX][SF]
事务部分:   [TRANS][LNG][DATA...]
```
帧类型对照表：
0x20: POLL (轮询)
0x30: DATA (数据)
0xC0: ACK (确认)
0x50: NAK (否定确认)
0x70: EOT (传输结束)
0xE0: ACKPOLL (确认轮询)
---

## 2. DC 事务 (Device to Controller)

### 2.1 DC 事务类型总览

| DC类型 | 十六进制 | 中文名称 | 英文名称 | 数据长度 | 核心用途 |
|--------|----------|----------|----------|----------|----------|
| **DC1** | 0x01 | 泵状态 | Pump Status | 1字节 | 状态管理和控制 |
| **DC2** | 0x02 | 填充体积和金额 | Filled Volume & Amount | 8字节 | 实时交易数据 |
| **DC3** | 0x03 | 喷嘴状态和价格 | Nozzle Status & Price | 4字节 | 喷嘴和价格管理 |
| **DC5** | 0x05 | 报警代码 | Alarm Code | 1字节 | 故障和报警管理 |
| **DC7** | 0x07 | 泵参数 | Pump Parameters | 33字节 | 参数配置管理 |
| **DC14** | 0x0E | 暂停回复 | Suspend Reply | 1字节 | 暂停控制确认 |
| **DC15** | 0x0F | 恢复回复 | Resume Reply | 1字节 | 恢复控制确认 |
| **DC101** | 0x65 | 总计数器 | Total Counters | 变长 | 计量数据管理 |
| **DC102** | 0x66 | IFSF独立模式 | IFSF Stand Alone | 2字节 | 独立模式管理 |
| **DC103** | 0x67 | 泵单价 | Pump Unit Prices | 变长 | 价格数据管理 |

### 2.2 核心 DC 事务详解

#### DC1 - 泵状态事务
```
格式: [TRANS:0x01][LNG:0x01][STATUS:1byte]
状态值:
- 0x00: PUMP_NOT_PROGRAMMED (泵未编程)
- 0x01: RESET (复位)
- 0x02: AUTHORIZED (授权)
- 0x04: FILLING (加油中)
- 0x05: FILLING_COMPLETED (加油完成)
- 0x06: MAX_AMOUNT_REACHED (达到预设)
- 0x07: SWITCHED_OFF (关闭)
- 0x08: SUSPENDED (暂停)
```

**业务触发时机**：
- 响应 CD1 状态查询命令
- 泵状态自动变化时主动上报
- 定时轮询响应

#### DC2 - 填充体积和金额事务
```
格式: [TRANS:0x02][LNG:0x08][VOLUME:4bytes BCD][AMOUNT:4bytes BCD]
数据说明:
- VOLUME: 4字节BCD格式，3位小数精度，MSB优先
- AMOUNT: 4字节BCD格式，2位小数精度，MSB优先
```

**数据示例**：
```
加油25.460升，金额123.45元
数据帧: [02][08][00 02 54 60][01 23 45 00]
解析: Volume=25.460L, Amount=123.45¥
```

**业务触发时机**：
- 加油过程中体积/金额实时变化
- 响应 CD1 填充信息查询
- 交易复位时发送清零数据

#### DC3 - 喷嘴状态和价格事务
```
格式: [TRANS:0x03][LNG:0x04][PRICE:3bytes BCD][NOZIO:1byte]
NOZIO字节解析:
- bit 0-3: 喷嘴号 (1-15)
- bit 4: 拔出状态 (0=插入, 1=拔出)
```

**NOZIO示例**：
```
0x12: 喷嘴2选中+拔出
0x02: 喷嘴2选中+插入
0x10: 无喷嘴选中+拔出状态
```

**业务触发时机**：
- 客户拔出/插入油枪
- 客户选择油品按钮
- 价格变更时自动上报

#### DC5 - 报警代码事务
```
格式: [TRANS:0x05][LNG:0x01][ALARM:1byte]
主要报警码:
- 0x01: CPU复位
- 0x03: RAM错误
- 0x06: 脉冲器错误
- 0x09: 紧急停止
- 0x0A: 电源故障
- 0x0B: 压力丢失
- 0x0E: 高泄漏错误
- 0x11: VR监控10连续错误
```

### 2.3 高级 DC 事务详解

#### DC101 - 总计数器事务
```
格式: [TRANS:0x65][LNG:变长][COUN:1byte][TOTVAL:5bytes][TOTM1:5bytes][TOTM2:5bytes]
计数器类型 (COUN):
- 0x01-0x08: 喷嘴1-8体积计数器
- 0x09: 总体积计数器
- 0x11-0x18: 喷嘴1-8金额计数器
- 0x19: 总金额计数器
```

#### DC102 - IFSF独立模式事务
```
格式: [TRANS:0x66][LNG:0x02][MODE:1byte][LOCA:1byte]
模式说明:
- MODE: 0x00=禁用独立模式, 0x01=启用独立模式
- LOCA: 0x00=按钮未按下, 0x01=本地授权按钮已按下
```

---

## 3. CD 事务 (Controller to Device)

### 3.1 CD 事务类型总览

| CD类型 | 十六进制 | 中文名称 | 英文名称 | 数据长度 | 核心用途 |
|--------|----------|----------|----------|----------|----------|
| **CD1** | 0x01 | 泵命令 | Command to Pump | 1字节 | 泵控制命令 |
| **CD2** | 0x02 | 允许的喷嘴号 | Allowed Nozzle Numbers | 变长 | 喷嘴授权 |
| **CD3** | 0x03 | 预设体积 | Preset Volume | 4字节 | 体积预设 |
| **CD4** | 0x04 | 预设金额 | Preset Amount | 4字节 | 金额预设 |
| **CD5** | 0x05 | 价格更新 | Price Update | 变长 | 价格设置 |
| **CD7** | 0x07 | 输出命令 | Command to Output | 2字节 | 输出控制 |
| **CD9** | 0x09 | 设置泵参数 | Set Pump Parameters | 33字节 | 参数配置 |
| **CD14** | 0x0E | 暂停请求 | Suspend Request | 1字节 | 暂停控制 |
| **CD15** | 0x0F | 恢复请求 | Resume Request | 1字节 | 恢复控制 |
| **CD101** | 0x65 | 请求总计数器 | Request Total Counters | 1字节 | 计数器查询 |

### 3.2 CD1 - 泵命令事务

#### 命令代码定义
```
格式: [TRANS:0x01][LNG:0x01][COMMAND:1byte]

命令码:
- 0x00: RETURN_STATUS (返回状态)
- 0x02: RETURN_PUMP_PARAMETERS (返回泵参数)
- 0x03: RETURN_PUMP_IDENTITY (返回泵身份)
- 0x04: RETURN_FILLING_INFORMATION (返回填充信息)
- 0x05: RESET (复位)
- 0x06: AUTHORIZE (授权)
- 0x08: STOP (停止)
- 0x0A: SWITCH_OFF (关闭)
- 0x0D: SUSPEND_FUELLING_POINT (暂停加油点)
- 0x0E: RESUME_FUELLING_POINT (恢复加油点)
- 0x0F: RETURN_PRICES_OF_ALL_GRADES (返回所有油品价格)
```

#### 命令业务说明

**RETURN_STATUS (0x00)**
- 用途: 查询泵当前状态
- 响应: DC1 状态事务
- 触发时机: 系统初始化、通信故障恢复、定期状态检查

**AUTHORIZE (0x06)**
- 用途: 授权泵开始加油
- 前置条件: 泵状态为 RESET，已设置价格
- 响应: DC1 状态变为 AUTHORIZED
- 后续: 等待客户拔出油枪

**RESET (0x05)**
- 用途: 复位泵，清除交易数据
- 响应: DC1 状态变为 RESET，DC2 清零数据
- 使用场景: 交易开始前、交易完成后

### 3.3 其他重要 CD 事务

#### CD2 - 允许的喷嘴号
```
格式: [TRANS:0x02][LNG:N][NOZ1][NOZ2]...[NOZN]
说明:
- 设置允许使用的喷嘴号 (1-15)
- 用于多喷嘴泵的权限控制
- 通常在授权前发送
```

#### CD3 - 预设体积
```
格式: [TRANS:0x03][LNG:0x04][VOLUME:4bytes BCD]
说明:
- 设置预设加油体积
- BCD格式，3位小数精度
- 泵达到预设体积时自动停止
```

#### CD4 - 预设金额
```
格式: [TRANS:0x04][LNG:0x04][AMOUNT:4bytes BCD]
说明:
- 设置预设加油金额
- BCD格式，2位小数精度
- 泵达到预设金额时自动停止
```

#### CD5 - 价格更新
```
格式: [TRANS:0x05][LNG:3*N][PRI1:3bytes][PRI2:3bytes]...[PRIN:3bytes]
说明:
- 批量更新油品价格
- 每个价格3字节BCD格式，3位小数精度
- PRI1对应喷嘴1，PRI2对应喷嘴2，以此类推
```

---

## 4. CD/DC 交互过程

### 4.1 基本交互模式

#### 主动查询模式
```
Site Controller -> Pump Device: CD1 RETURN_STATUS
Pump Device -> Site Controller: DC1 CURRENT_STATUS
```

#### 被动接收模式
```
Pump Device -> Site Controller: DC1 STATUS_CHANGE (自动上报)
Site Controller: 处理状态变化事件
```

#### 轮询模式
```
Site Controller -> Pump Device: POLL Frame [ADR][20+TX#][SF]
Pump Device -> Site Controller: DC事务响应 (如有数据)
                              或 ACK Frame (无数据)
```

### 4.2 完整加油流程 CD/DC 交互

#### 阶段1: 系统初始化
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    
    SC->>PD: CD1 RETURN_STATUS
    PD->>SC: DC1 PUMP_NOT_PROGRAMMED
    
    SC->>PD: CD5 Price Update
    PD->>SC: ACK
    
    SC->>PD: CD1 RETURN_STATUS
    PD->>SC: DC1 RESET
```

#### 阶段2: 交易准备
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    
    SC->>PD: CD2 Allowed Nozzles [1,2,3]
    PD->>SC: ACK
    
    SC->>PD: CD4 Preset Amount (50.00¥)
    PD->>SC: ACK
    
    SC->>PD: CD1 AUTHORIZE
    PD->>SC: DC1 AUTHORIZED
```

#### 阶段3: 客户操作
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    participant Customer as Customer
    
    Customer->>PD: 拔出油枪
    PD->>SC: DC3 Nozzle Out Event
    
    Customer->>PD: 选择油品
    PD->>SC: DC3 Grade Selected
    
    Customer->>PD: 开始加油
    PD->>SC: DC1 FILLING
```

#### 阶段4: 加油过程
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    
    loop 加油过程中
        PD->>SC: DC2 Volume/Amount Update
        Note right of SC: 实时显示数据
    end
    
    PD->>SC: DC1 FILLING_COMPLETED
    PD->>SC: DC2 Final Volume/Amount
```

#### 阶段5: 交易完成
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    participant Customer as Customer
    
    Customer->>PD: 插回油枪
    PD->>SC: DC3 Nozzle In Event
    
    SC->>PD: CD1 RESET
    PD->>SC: DC1 RESET
    PD->>SC: DC2 Cleared Data [00000000][00000000]
```

### 4.3 异常处理交互

#### 通信超时处理
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    
    SC->>PD: CD1 Command
    Note right of PD: 25ms内无响应
    SC->>PD: 重发 CD1 Command
    Note right of PD: 再次超时
    SC->>PD: 重发 CD1 Command (第3次)
    Note right of PD: 仍然超时
    Note right of SC: 进入通信重启流程
```

#### 设备报警处理
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    
    PD->>SC: DC5 Alarm Code (0x06 脉冲器错误)
    SC->>SC: 分析报警严重性
    
    alt 严重报警
        SC->>PD: CD1 SWITCH_OFF
        PD->>SC: DC1 SWITCHED_OFF
    else 一般报警
        SC->>SC: 记录报警，继续监控
    end
```

#### 暂停/恢复处理
```mermaid
sequenceDiagram
    participant SC as Site Controller
    participant PD as Pump Device
    participant VID as Vehicle ID System
    
    VID->>SC: Signal Lost
    SC->>PD: CD14 Suspend Request
    PD->>SC: DC14 Suspend Reply
    PD->>SC: DC1 SUSPENDED
    
    Note right of SC: 等待信号恢复...
    
    VID->>SC: Signal Restored
    SC->>PD: CD15 Resume Request
    PD->>SC: DC15 Resume Reply
    PD->>SC: DC1 FILLING
```

---

## 5. 实际应用场景

### 5.1 标准加油场景
**步骤序列**:
1. `CD1 RESET` → `DC1 RESET`
2. `CD5 Price Update` → `ACK`
3. `CD2 Allowed Nozzles` → `ACK`
4. `CD1 AUTHORIZE` → `DC1 AUTHORIZED`
5. 客户拔枪 → `DC3 Nozzle Out`
6. 客户选油品 → `DC3 Grade Selected`
7. 开始加油 → `DC1 FILLING`
8. 实时数据 → `DC2 Volume/Amount Updates`
9. 加油完成 → `DC1 FILLING_COMPLETED`
10. 插回油枪 → `DC3 Nozzle In`

### 5.2 预设金额加油场景
**关键差异**:
- 步骤3.5: `CD4 Preset Amount` → `ACK`
- 步骤8.5: 达到预设 → `DC1 MAX_AMOUNT_REACHED`

### 5.3 IFSF独立模式场景
**独立模式流程**:
1. 通信中断 → `DC102 Stand Alone Enabled`
2. 本地授权 → `DC102 Local Auth Button`
3. 使用本地价格 → `DC103 Pump Prices`
4. 正常加油流程（使用本地控制）
5. 通信恢复 → `DC102 Stand Alone Disabled`

### 5.4 维护和诊断场景
**日常维护**:
- `CD101 Request Counters` → `DC101 Counter Data`
- `CD1 RETURN_PUMP_PARAMETERS` → `DC7 Pump Parameters`
- `CD1 RETURN_PUMP_IDENTITY` → `DC6 Pump Identity`

**故障诊断**:
- 设备报警 → `DC5 Alarm Code`
- 参数验证 → `CD9 Set Parameters` → `DC7 Parameters`
- 计数器检查 → `CD101 各类计数器查询`

---

## 6. 总结

Wayne DART 协议通过 CD/DC 事务的精确配合，实现了完整的加油站设备控制和数据采集功能：

### 核心特点
1. **双向通信**: CD事务(控制) + DC事务(状态/数据)
2. **实时性**: 25ms响应时间要求
3. **可靠性**: CRC校验 + 序列号 + 重传机制
4. **完整性**: 覆盖控制、监控、诊断全流程

### 数据格式统一
- **BCD编码**: 数值数据采用BCD格式
- **MSB优先**: 多字节数据大端序
- **结构化**: 固定格式便于解析和验证

### 业务覆盖完整
- **设备控制**: 授权、复位、停止等
- **状态监控**: 实时状态、报警、事件
- **数据采集**: 交易数据、计数器、参数
- **异常处理**: 暂停、恢复、故障处理

Wayne DART 协议为加油站自动化运营提供了坚实的技术基础，确保了设备控制的精确性和数据传输的可靠性。 