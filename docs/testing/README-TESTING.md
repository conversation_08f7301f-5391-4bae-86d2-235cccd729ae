# FCC Service 测试指南

本文档详细说明如何对FCC Service进行完整的集成测试和端到端测试。

## 🎯 测试概述

FCC Service提供了完整的测试框架，包括：

- **单元测试**: 测试个别组件和函数
- **集成测试**: 测试组件间的交互
- **端到端测试**: 测试完整的业务流程
- **性能测试**: 验证DART协议性能要求
- **Wayne协议测试**: 专门的Wayne DART协议测试

## 🚀 快速开始

### 1. 环境准备

确保已安装以下工具：
- Docker 20.0+
- Docker Compose 2.0+
- Go 1.21+
- Make

### 2. 运行完整测试套件

```bash
# 运行所有测试（推荐）
make test-all

# 或者使用脚本
./scripts/run-tests.sh all --verbose
```

### 3. 运行特定类型测试

```bash
# 单元测试（快速）
make test-unit

# 集成测试（需要Docker）
make test-integration

# 端到端测试（完整流程）
make test-e2e

# Wayne协议测试
make test-wayne

# 性能测试
make test-performance
```

## 📋 测试类型详解

### 单元测试
- **目标**: 测试单个组件的功能
- **范围**: `./internal/...` 和 `./pkg/...`
- **执行时间**: ~30秒
- **命令**: `make test-unit`

```bash
# 运行单元测试并生成覆盖率报告
make test-unit
make test-coverage
```

### 集成测试
- **目标**: 测试组件间交互和Wayne协议集成
- **环境**: Docker容器(PostgreSQL + Redis + Mock设备)
- **执行时间**: ~2-3分钟
- **命令**: `make test-integration`

**测试内容**:
- Wayne适配器与Mock设备通信
- 数据库操作和缓存同步
- 串口协议处理
- DART协议帧解析

### 端到端测试
- **目标**: 测试完整的业务流程
- **环境**: 完整Docker环境
- **执行时间**: ~5-10分钟
- **命令**: `make test-e2e`

**测试流程**:
1. 注册Wayne控制器
2. 发现设备
3. 设置油枪价格
4. 预设加油量
5. 授权加油
6. 监控加油过程
7. 获取交易结果

### Wayne协议测试
- **目标**: 验证Wayne DART协议实现
- **重点**: 协议合规性和性能
- **命令**: `make test-wayne`

**测试内容**:
- DART-LINE协议帧处理
- BCD编码/解码
- 泵状态机
- 事务构建器
- 协议性能(25ms响应要求)

### 性能测试
- **目标**: 验证性能要求
- **基准**: API<200ms, DART<25ms
- **命令**: `make test-performance`

## 🐳 Docker测试环境

### 测试环境架构

```
┌─────────────────────┐
│   测试运行器        │ ── 执行测试脚本
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│   FCC服务容器       │ ── 被测试的服务
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│  Wayne设备模拟器    │ ── 模拟5个Wayne设备
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│ PostgreSQL + Redis  │ ── 数据存储
└─────────────────────┘
```

### 测试环境组件

| 组件 | 用途 | 端口 |
|------|------|------|
| fcc-service-test | 被测FCC服务 | 8080 |
| postgres-test | 测试数据库 | 5432 |
| redis-test | 测试缓存 | 6379 |
| mock-wayne-device | Wayne设备模拟器 | 9600-9604 |
| test-runner | 测试执行器 | - |

### 模拟设备配置

模拟器创建5个Wayne设备：
- 设备地址: 0x50-0x54 (80-84)
- 协议: Wayne DART v1.3
- 波特率: 9600
- 功能: 完整的泵操作流程

## 📊 测试结果和报告

### 覆盖率报告

测试完成后会生成覆盖率报告：
- `coverage.html`: 单元测试覆盖率
- `test-results/integration-coverage.out`: 集成测试覆盖率

```bash
# 查看覆盖率
open coverage.html
```

### 测试结果文件

测试结果保存在 `test-results/` 目录：
- `test-results.json`: 集成测试结果
- `e2e-results.json`: 端到端测试结果
- `integration-coverage.out`: 集成测试覆盖率

## 🔧 高级用法

### 自定义测试脚本

```bash
# 详细输出
./scripts/run-tests.sh all --verbose

# 保留容器用于调试
./scripts/run-tests.sh integration --no-cleanup

# 自定义超时
./scripts/run-tests.sh e2e --timeout 900s

# 强制重新构建镜像
./scripts/run-tests.sh all --build
```

### 手动测试和调试

```bash
# 启动测试环境但不运行测试
docker-compose -f docker-compose.test.yml up -d

# 进入测试容器进行手动测试
docker-compose -f docker-compose.test.yml run --rm test-runner /bin/sh

# 在容器内运行特定测试
go test -v ./tests/e2e/fcc_e2e_test.go -run TestE2E_CompleteWorkflow
```

### CI/CD集成

```bash
# CI环境运行（不清理容器，便于调试）
make test-ci

# 或者
./scripts/run-tests.sh all --no-cleanup --timeout 600s
```

## 🧪 测试功能验证

### 1. 注册设备功能 ✅
```bash
# 验证控制器注册
curl -X POST http://localhost:8080/api/v1/controllers \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Controller","type":"wayne_dart","protocol":"dart"}'
```

### 2. 发现设备功能 ✅
```bash
# 验证设备发现
curl -X POST http://localhost:8080/api/v1/controllers/{id}/discover
```

### 3. 油枪预设功能 ✅
```bash
# 验证价格设置
curl -X POST http://localhost:8080/api/v1/devices/{id}/commands \
  -H "Content-Type: application/json" \
  -d '{"type":"set_price","params":{"nozzle":1,"price":1.259}}'

# 验证体积预设
curl -X POST http://localhost:8080/api/v1/devices/{id}/commands \
  -H "Content-Type: application/json" \
  -d '{"type":"preset_volume","params":{"volume":50.0}}'
```

### 4. 交易接收功能 ✅
```bash
# 验证交易数据
curl -X GET http://localhost:8080/api/v1/devices/{id}/transaction
```

## 📈 性能基准

### DART协议性能要求
- **命令响应时间**: <25ms ✅
- **设备发现时间**: <5秒 ✅
- **API响应时间**: <200ms ✅
- **并发设备支持**: 1000+ ✅

### 测试基准结果
```
BCD转换性能:     平均 <1ms
状态机处理:      平均 <1ms
事务构建:        平均 <1ms
端到端流程:      <30秒
```

## 🐛 故障排除

### 常见问题

1. **Docker容器启动失败**
   ```bash
   # 检查Docker状态
   docker ps -a
   
   # 查看容器日志
   docker-compose -f docker-compose.test.yml logs
   ```

2. **测试超时**
   ```bash
   # 增加超时时间
   ./scripts/run-tests.sh all --timeout 900s
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -an | grep 8080
   
   # 停止现有服务
   docker-compose down
   ```

4. **Wayne设备模拟器无响应**
   ```bash
   # 检查模拟器日志
   docker-compose -f docker-compose.test.yml logs mock-wayne-device
   
   # 重启模拟器
   docker-compose -f docker-compose.test.yml restart mock-wayne-device
   ```

### 调试技巧

1. **保留测试环境**
   ```bash
   ./scripts/run-tests.sh integration --no-cleanup
   ```

2. **查看详细日志**
   ```bash
   ./scripts/run-tests.sh all --verbose
   ```

3. **单独测试模块**
   ```bash
   go test -v ./tests/integration/wayne_pump_test.go -run TestWaynePump_Integration
   ```

## 📝 测试最佳实践

1. **每次修改代码后运行测试**
   ```bash
   make test-unit  # 快速验证
   ```

2. **提交前运行完整测试**
   ```bash
   make test-all
   ```

3. **定期运行性能测试**
   ```bash
   make test-performance
   ```

4. **监控测试覆盖率**
   ```bash
   make test-coverage
   # 目标: >80%覆盖率
   ```

## 🎉 总结

FCC Service的测试框架提供了完整的质量保证：

- ✅ **功能完整性**: 所有核心功能都有对应测试
- ✅ **协议合规性**: 严格遵循Wayne DART v1.3规范
- ✅ **性能保证**: 满足25ms响应时间要求
- ✅ **集成验证**: 端到端业务流程测试
- ✅ **自动化**: 一键运行所有测试

使用本测试框架，您可以：
1. 快速验证代码修改的正确性
2. 确保新功能不破坏现有功能
3. 验证Wayne DART协议的正确实现
4. 保证系统在生产环境的可靠性

开始测试之旅：`make test-all` 🚀 