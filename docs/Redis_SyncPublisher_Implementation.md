# Redis SyncPublisher 实现文档

## 📋 概述

本文档详细记录了将 FCC 交易同步系统从 `memorySyncPublisher` 升级到支持 Redis 的 `syncPublisher` 的完整实现过程。

## 🎯 实现目标

### 主要目标
- ✅ **启用外部ID功能** - 保存外部系统返回的交易ID到数据库
- ✅ **状态持久化** - 使用Redis存储同步状态，重启不丢失
- ✅ **完整回调机制** - 支持数据库更新回调
- ✅ **向后兼容** - 自动检测Redis可用性，支持回退

### 架构对比
| 特性 | memorySyncPublisher | syncPublisher (Redis) |
|------|---------------------|----------------------|
| 状态存储 | 内存 map | Redis 持久化 |
| 外部ID回调 | ❌ 空实现 | ✅ 完整实现 |
| 重试机制 | ❌ 无 | ✅ 完整重试逻辑 |
| 并发控制 | ❌ 简单 | ✅ 完整并发控制 |
| 配置选项 | ❌ 无 | ✅ 丰富配置 |

## 🔧 核心文件改动

### 1. 新增文件

#### `internal/services/transaction/redis_sync_status_store.go`
**Redis 状态存储实现**

```go
// 核心结构
type redisSyncStatusStore struct {
    client    redis.UniversalClient
    logger    *zap.Logger
    keyPrefix string
    ttl       time.Duration
}

// 创建方法
NewRedisSyncStatusStore(client redis.UniversalClient, logger *zap.Logger)
NewRedisSyncStatusStoreFromClient(client *redis.Client, logger *zap.Logger)
```

**主要功能：**
- 实现 `SyncStatusStore` 接口
- 支持 Save/Get/List/Update/Delete 操作
- 自动TTL管理（默认24小时）
- 键命名规范：`fcc:sync_status:transaction_id:system_id`

#### `internal/services/transaction/redis_sync_test.go`
**完整测试套件**

```go
// 测试内容
- TestRedisSyncStatusStore()      // 基础功能测试
- TestSyncPublisherWithRedis()    // 集成测试
- BenchmarkRedisSyncStatusStore() // 性能测试
```

### 2. 修改文件

#### `internal/services/transaction/adapters.go`
**新增Redis工厂方法**

```go
// 新增方法
func (tsf *TransactionServiceFactory) CreateTransactionServiceWithRedis(
    repository TransactionRepository,
    deviceManager api.DeviceManager,
    nozzleService nozzle.ServiceV2,
    redisClient *redis.Client,
    logger *zap.Logger,
) TransactionService

// 配置参数
syncConfig := SyncPublisherConfig{
    MaxConcurrency:    5,
    SyncTimeout:       30 * time.Second,
    EnableRetry:       true,
    DefaultMaxRetries: 3,
    DefaultRetryDelay: 5 * time.Second,
    EnableAsync:       true,
    WorkerCount:       3,
    QueueSize:         100,
}
```

#### `main_v2.go`
**智能检测和自动切换**

```go
// 检查Redis可用性
if redisCache, ok := app.cache.(*storage.RedisCache); ok {
    redisClient := redisCache.GetClient()
    app.transactionService = factory.CreateTransactionServiceWithRedis(
        repository, deviceManager, nozzleService, redisClient, logger)
    app.logger.Info("Transaction service created with Redis sync publisher")
} else {
    // 回退到内存版本
    app.transactionService = factory.CreateTransactionService(
        repository, deviceManager, nozzleService, logger)
    app.logger.Info("Transaction service created with memory sync publisher")
}
```

## 📊 数据库字段扩展

### 新增字段（迁移文件：`migrations/000007_add_external_transaction_sync_fields.sql`）

```sql
-- 外部交易ID字段
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS external_transaction_id VARCHAR(255);

-- 同步状态字段
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS sync_status VARCHAR(50) DEFAULT 'pending';

-- 索引优化
CREATE INDEX IF NOT EXISTS idx_transactions_external_transaction_id 
ON transactions(external_transaction_id);

CREATE INDEX IF NOT EXISTS idx_transactions_sync_status 
ON transactions(sync_status);
```

### 字段说明
- `external_transaction_id`: 存储外部系统返回的交易ID
- `sync_status`: 跟踪同步状态 (`pending`/`synced`/`failed`/`needs_sync`)

## 🔄 完整调用链

### 外部ID更新流程
```
1. HTTPSyncStrategy.extractAndSaveExternalID()
   ↓ (提取外部ID)
2. syncPublisher.UpdateExternalTransactionID()
   ↓ (通过回调)
3. TransactionEventBridge.UpdateExternalTransactionID()
   ↓ (通过Repository)
4. Database.Update() → 保存到 external_transaction_id 字段
```

### pump-readings 更新流程
```
1. HTTPSyncStrategy.syncPumpReadingUpdate()
   ↓ (检查外部ID)
2. 如果有外部ID: 使用外部ID调用 PATCH 接口
3. 如果无外部ID: 标记为 needs_sync 状态
```

## 🎯 Redis 数据结构

### 状态存储键格式
```
# 单个状态
fcc:sync_status:tx-123:external_api_mvp

# 交易状态列表
fcc:sync_status:list:tx-123
```

### 存储数据格式
```json
{
  "system_id": "external_api_mvp",
  "transaction_id": "tx-123",
  "status": "synced",
  "attempt_count": 1,
  "last_attempt": "2024-01-15T10:30:00Z",
  "last_error": "",
  "next_retry": null
}
```

## 🚀 部署和使用

### 自动检测机制
1. **Redis可用** → 使用 `syncPublisher` (Redis版本)
2. **Redis不可用** → 自动回退到 `memorySyncPublisher`
3. **无需手动配置** → 系统自动检测和切换

### 日志变化
```bash
# Redis版本启用后
[INFO] Transaction service created with Redis sync publisher
[INFO] SyncPublisher callbacks configured successfully

# 同步过程日志
[INFO] [SyncPublisher] External API response received
[INFO] External transaction ID extracted from response
[INFO] External transaction ID updated successfully via callback
```

### 配置要求
- Redis 服务可用
- 正确的 Redis 连接配置
- 数据库迁移已执行

## 📈 性能优化

### Redis 配置优化
```go
// 连接池配置
PoolSize:     10,
MinIdleConns: 5,
MaxConnAge:   30 * time.Minute,
IdleTimeout:  5 * time.Minute,

// TTL 配置
ttl: 24 * time.Hour  // 状态保留24小时
```

### 并发控制
```go
MaxConcurrency: 5,     // 最大并发同步数
WorkerCount:    3,     // 异步工作协程数
QueueSize:      100,   // 异步队列大小
```

## 🔍 测试验证

### 运行测试
```bash
# 单元测试
go test ./internal/services/transaction -run TestRedisSyncStatusStore

# 集成测试
go test ./internal/services/transaction -run TestSyncPublisherWithRedis

# 性能测试
go test ./internal/services/transaction -bench BenchmarkRedisSyncStatusStore
```

### 测试要求
- Redis 服务运行在 localhost:6379
- 测试使用 DB 15（避免影响生产数据）

## 🎉 实现效果

### 功能完整性
- ✅ **外部ID保存** - 从API响应中提取并保存到数据库
- ✅ **状态持久化** - Redis存储，重启不丢失
- ✅ **pump-readings更新** - 使用外部ID进行后续更新
- ✅ **错误处理** - 缺失外部ID时标记为needs_sync
- ✅ **向后兼容** - 自动回退机制

### 系统稳定性
- ✅ **重试机制** - 失败自动重试
- ✅ **并发控制** - 安全的并发处理
- ✅ **资源管理** - 自动TTL和清理
- ✅ **监控支持** - 完整的日志记录

## 📝 总结

通过引入 Redis SyncPublisher，系统获得了：

1. **完整的外部ID功能** - 支持外部系统集成
2. **可靠的状态管理** - 持久化存储，不丢失状态
3. **生产级特性** - 重试、并发控制、监控
4. **平滑升级** - 自动检测，向后兼容

这个实现为 FCC 交易同步系统提供了企业级的可靠性和功能完整性。

## 🔧 详细技术实现

### 回调机制设计

#### 接口扩展
```go
// TransactionSyncPublisher 接口新增方法
type TransactionSyncPublisher interface {
    // 原有方法
    PublishTransactionEvent(ctx context.Context, event *TransactionEvent) error
    RegisterSyncStrategy(systemID string, strategy SyncStrategy)
    GetSyncStatus(ctx context.Context, transactionID string) ([]*SyncStatus, error)

    // 🎯 新增回调方法
    SetExternalIDUpdateCallback(callback func(ctx context.Context, transactionID, externalID string) error)
    SetSyncStatusUpdateCallback(callback func(ctx context.Context, transactionID, status string) error)
    UpdateExternalTransactionID(ctx context.Context, transactionID, externalID string) error
    UpdateSyncStatus(ctx context.Context, transactionID, status string) error
}
```

#### 回调设置（bootstrap/services.go）
```go
// 自动配置回调函数
if bridge, ok := eventBridge.(*transaction.TransactionEventBridge); ok {
    transactionSyncPublisher.SetExternalIDUpdateCallback(bridge.UpdateExternalTransactionID)
    transactionSyncPublisher.SetSyncStatusUpdateCallback(func(ctx context.Context, transactionID, status string) error {
        return bridge.MarkTransactionAsNeedsSync(ctx, transactionID)
    })
    logger.Info("SyncPublisher callbacks configured successfully")
}
```

### HTTPSyncStrategy 增强

#### 自动设置SyncPublisher引用
```go
// RegisterSyncStrategy 中自动配置
func (sp *syncPublisher) RegisterSyncStrategy(systemID string, strategy SyncStrategy) {
    sp.mu.Lock()
    defer sp.mu.Unlock()

    sp.strategies[systemID] = strategy

    // 🎯 自动设置HTTPSyncStrategy的SyncPublisher引用
    if httpStrategy, ok := strategy.(*HTTPSyncStrategy); ok {
        httpStrategy.SetSyncPublisher(sp)
        sp.logger.Info("HTTPSyncStrategy configured with SyncPublisher reference",
            zap.String("system_id", systemID))
    }
}
```

#### 外部ID提取和保存
```go
// extractAndSaveExternalID 方法
func (h *HTTPSyncStrategy) extractAndSaveExternalID(ctx context.Context, responseBody []byte, tx *models.Transaction) error {
    // 1. 解析JSON响应
    var response map[string]interface{}
    json.Unmarshal(responseBody, &response)

    // 2. 提取ID字段（支持多种字段名）
    var externalID string
    if id, ok := response["id"]; ok {
        externalID = id.(string)
    } else if txID, ok := response["transaction_id"]; ok {
        externalID = txID.(string)
    } else if extID, ok := response["external_id"]; ok {
        externalID = extID.(string)
    }

    // 3. 通过SyncPublisher回调保存
    if h.syncPublisher != nil {
        return h.syncPublisher.UpdateExternalTransactionID(ctx, tx.ID, externalID)
    }

    return nil
}
```

### TransactionEventBridge 数据库更新

#### 外部ID更新方法
```go
func (b *TransactionEventBridge) UpdateExternalTransactionID(ctx context.Context, transactionID, externalID string) error {
    // 1. 获取现有交易
    tx, err := b.repository.GetByID(ctx, transactionID)
    if err != nil {
        return fmt.Errorf("failed to get transaction: %w", err)
    }

    // 2. 更新字段
    tx.ExternalTransactionID = &externalID
    tx.SyncStatus = "synced"

    // 3. 保存到数据库
    if err := b.repository.Update(ctx, tx); err != nil {
        return fmt.Errorf("failed to update transaction: %w", err)
    }

    return nil
}
```

#### 同步状态标记方法
```go
func (b *TransactionEventBridge) MarkTransactionAsNeedsSync(ctx context.Context, transactionID string) error {
    tx, err := b.repository.GetByID(ctx, transactionID)
    if err != nil {
        return fmt.Errorf("failed to get transaction: %w", err)
    }

    tx.SyncStatus = "needs_sync"

    if err := b.repository.Update(ctx, tx); err != nil {
        return fmt.Errorf("failed to update transaction: %w", err)
    }

    return nil
}
```

## 🔍 故障排除

### 常见问题

#### 1. Redis 连接失败
```bash
# 错误日志
[ERROR] Failed to save sync status to Redis: dial tcp 127.0.0.1:6379: connect: connection refused

# 解决方案
- 检查Redis服务是否运行
- 验证连接配置
- 检查网络连接
```

#### 2. 外部ID未保存
```bash
# 检查日志
[WARN] No SyncPublisher available for external ID update

# 可能原因
- HTTPSyncStrategy未正确注册
- 回调函数未设置
- 使用了memorySyncPublisher而非syncPublisher
```

#### 3. 回调函数未调用
```bash
# 检查日志
[WARN] No external ID update callback registered

# 解决方案
- 确认使用syncPublisher而非memorySyncPublisher
- 检查bootstrap/services.go中的回调设置
- 验证TransactionEventBridge创建成功
```

### 调试技巧

#### 1. 启用详细日志
```go
// 在创建logger时设置Debug级别
logger := zap.NewDevelopment()
```

#### 2. 检查Redis状态
```bash
# 连接Redis CLI
redis-cli

# 查看同步状态
KEYS fcc:sync_status:*
GET fcc:sync_status:tx-123:external_api_mvp
```

#### 3. 验证数据库更新
```sql
-- 检查外部ID字段
SELECT id, external_transaction_id, sync_status
FROM transactions
WHERE id = 'tx-123';
```

## 📊 监控和指标

### 关键指标

#### Redis 性能指标
- 连接数使用率
- 操作延迟
- 内存使用量
- 键过期情况

#### 同步性能指标
- 同步成功率
- 平均响应时间
- 重试次数
- 外部ID保存成功率

### 监控实现
```go
// 在Redis操作中添加指标收集
func (r *redisSyncStatusStore) Save(ctx context.Context, status *SyncStatus) error {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        // 记录操作耗时
        r.logger.Debug("Redis save operation completed",
            zap.Duration("duration", duration))
    }()

    // ... 实际保存逻辑
}
```

## 🚀 未来扩展

### 可能的增强功能

#### 1. 分布式锁
```go
// 防止并发更新同一交易
func (r *redisSyncStatusStore) SaveWithLock(ctx context.Context, status *SyncStatus) error {
    lockKey := fmt.Sprintf("lock:%s", r.getStatusKey(status.TransactionID, status.SystemID))
    // 实现分布式锁逻辑
}
```

#### 2. 批量操作
```go
// 批量保存状态
func (r *redisSyncStatusStore) BatchSave(ctx context.Context, statuses []*SyncStatus) error {
    pipe := r.client.Pipeline()
    // 使用Pipeline批量操作
}
```

#### 3. 状态变更通知
```go
// Redis发布/订阅机制
func (r *redisSyncStatusStore) PublishStatusChange(ctx context.Context, status *SyncStatus) error {
    channel := fmt.Sprintf("status_change:%s", status.TransactionID)
    return r.client.Publish(ctx, channel, status).Err()
}
```

这个完整的实现为系统提供了可扩展、可维护的企业级同步解决方案。
