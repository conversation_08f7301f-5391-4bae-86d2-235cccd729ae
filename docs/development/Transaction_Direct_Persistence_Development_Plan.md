# 交易直接持久化开发计划

## 📋 项目概述

基于您提出的新方案，重构交易组装机制，采用"直接持久化"模式替代当前复杂的内存状态管理，确保交易数据的可靠性和系统的稳定性。

**核心理念**：每个关键步骤都在数据库体现，避免内存丢失风险，优先保证业务连续性。

---

## 🎯 新方案架构

### 核心流程
1. **维护累计泵码表** - 基于DC101事务实时维护设备喷嘴泵码
2. **DC3 Nozzle Out** → 创建Transaction，状态`initiated`
3. **DC2/DC3持续更新** → 实时更新Transaction数据
4. **DC1 FILLING_COMPLETED** → 更新状态为`completed`
5. **DC101异步更新** → 更新结束泵码（如果在时间窗口内）

### 设计原则
- ✅ **数据库为唯一真实来源**
- ✅ **每步操作立即持久化**
- ✅ **幂等性保证**
- ✅ **异常恢复机制**
- ✅ **MVP优先，测试驱动**

---

## 📊 现有服务分析

### 核心服务组件

| 服务名称 | 当前职责 | 新方案中的角色 | 需要改动 |
|---------|----------|---------------|----------|
| **DevicePoller** | DC事务处理、状态管理 | DC事务分发器 | 🔧 中等改动 |
| **TransactionAssembler** | 内存交易组装 | 删除/简化 | 🚨 重大重构 |
| **TransactionService** | 交易持久化、查询 | 核心交易管理器 | 🔧 中等改动 |
| **NozzleService** | 喷嘴状态管理 | 喷嘴状态 + 泵码管理 | 🔧 中等改动 |
| **TransactionRepository** | 数据访问层 | 扩展Transaction操作 | 🔧 少量改动 |

### 新增服务需求

| 新服务名称 | 职责 | 优先级 |
|-----------|------|--------|
| **NozzleCountersService** | 累计泵码表管理 | 🔥 高 |
| **TransactionLifecycleService** | 交易生命周期管理 | 🔥 高 |
| **DC101CounterProcessor** | DC101事务专门处理 | 🔥 高 |

---

## 🗂️ 开发任务分解

### Phase 1: 基础设施建设 (Week 1) - MVP核心

#### Task 1.1: 累计泵码表管理 🔥 ✅ **已完成**
**目标**：建立可靠的设备喷嘴泵码状态管理

**✅ 已实现**：
- ✅ **数据模型**：`pkg/models/nozzle_counters.go` - 完整的NozzleCounters模型
- ✅ **数据库迁移**：`migrations/000001_create_nozzle_counters_table.sql` - 表结构和索引
- ✅ **数据验证**：Wayne协议合规性检查、异常检测机制
- ✅ **数据质量**：单调递增检查、更新频率监控

**核心特性**：
- 复合主键：DeviceID + NozzleID 
- 累计计数器：体积(15,3) + 金额(15,2)
- 数据质量跟踪：更新次数、异常计数、有效性标记
- 时间戳管理：最后更新时间、创建时间
- Wayne协议合规：喷嘴编号1-15、DC101类型验证

**验收标准**：
- ✅ DC101事务能正确解析并更新泵码表
- ✅ 支持多喷嘴并发安全操作  
- ✅ 数据持久化可靠，重启后状态恢复
- ✅ 数据一致性检查和异常检测

#### Task 1.2: Transaction状态管理重构 🔥
**目标**：重新设计Transaction模型支持直接持久化

```go
// 扩展Transaction状态
type TransactionStatus string

const (
    TransactionStatusInitiated  TransactionStatus = "initiated"   // DC3 Nozzle Out
    TransactionStatusFilling    TransactionStatus = "filling"     // DC1 FILLING  
    TransactionStatusCompleted  TransactionStatus = "completed"   // DC1 FILLING_COMPLETED
    TransactionStatusCancelled  TransactionStatus = "cancelled"   // 异常取消
)

// 扩展Transaction模型
type Transaction struct {
    // 现有字段...
    
    // 新增生命周期时间戳
    InitiatedAt  *time.Time `json:"initiated_at"`  // DC3 Nozzle Out时间
    FillingAt    *time.Time `json:"filling_at"`    // DC1 FILLING时间
    CompletedAt  *time.Time `json:"completed_at"`  // DC1 FILLING_COMPLETED时间
    
    // 新增泵码来源跟踪
    CounterUpdateWindow time.Duration `json:"counter_update_window"` // 允许泵码更新的时间窗口
}
```

**测试要求**：
- [x] 数据库迁移测试
- [x] 状态转换逻辑测试
- [x] 并发状态更新测试

#### Task 1.3: TransactionLifecycleService实现 🔥 ✅ **已完成**
**目标**：实现核心交易生命周期管理服务

**✅ 已实现**：
- ✅ **核心服务**：`internal/services/transaction_lifecycle/service.go` - 完整的Service接口和实现
- ✅ **数据访问层**：`internal/services/transaction_lifecycle/repository.go` - GORM Repository实现
- ✅ **DC事务处理**：HandleNozzleOut、HandleVolumeAmountUpdate、HandleFillingCompleted、HandleCounterUpdate
- ✅ **生命周期管理**：状态转换、事件监听、统计查询

**核心特性**：
- DC3 Nozzle Out → 直接创建Transaction（状态：initiated）
- DC2 → 实时更新体积金额数据，转换为filling状态
- DC1 FILLING_COMPLETED → 转换为completed状态
- DC101 → 时间窗口内更新泵码数据
- 交易统计和监控功能

**验收标准**：
- ✅ DC3事务能直接创建Transaction并持久化
- ✅ DC2事务能实时更新交易数据
- ✅ DC1事务能正确完成交易
- ✅ DC101事务能在时间窗口内更新泵码
- ✅ 支持交易生命周期监听和统计

#### Task 1.4: 核心DC事务处理器重构 🔥
**目标**：直接从DC事务创建和更新Transaction

```go
type TransactionLifecycleService interface {
    // DC3 Nozzle Out → 创建Transaction
    HandleNozzleOut(ctx context.Context, deviceID string, nozzleID byte, price decimal.Decimal, timestamp time.Time) (*Transaction, error)
    
    // DC2 → 更新Transaction数据
    HandleVolumeAmountUpdate(ctx context.Context, deviceID string, nozzleID byte, volume, amount decimal.Decimal, timestamp time.Time) error
    
    // DC1 FILLING_COMPLETED → 更新状态
    HandleFillingCompleted(ctx context.Context, deviceID string, timestamp time.Time) error
    
    // DC101 → 更新结束泵码
    HandleCounterUpdate(ctx context.Context, deviceID string, nozzleID byte, counterType byte, value decimal.Decimal, timestamp time.Time) error
}
```

**测试要求**：
- [x] DC事务处理单元测试
- [x] 交易生命周期集成测试
- [x] 异常情况恢复测试

### Phase 2: DevicePoller集成 (Week 2) - 业务逻辑

#### Task 2.1: DevicePoller重构 🔧
**目标**：简化DevicePoller，移除TransactionAssembler依赖

```go
type DevicePoller struct {
    // 移除TransactionAssembler
    // transactionAssembler *TransactionAssembler // 删除
    
    // 新增服务依赖
    nozzleCountersService       NozzleCountersService
    transactionLifecycleService TransactionLifecycleService
    
    // 保留现有服务
    nozzleService      nozzle.ServiceV2
    transactionService TransactionServiceInterface
}

// 重构DC事务处理
func (p *DevicePoller) processDC3PriceNozzle(transaction pump.DCTransaction) error {
    // 解析DC3数据
    nozzleID, price, isOut := parsedc3(transaction.GetData())
    
    if isOut && nozzleID > 0 {
        // 直接创建Transaction
        tx, err := p.transactionLifecycleService.HandleNozzleOut(
            ctx, p.deviceID, nozzleID, price, time.Now())
        return err
    }
    
    // 其他逻辑...
}
```

**测试要求**：
- [x] DC事务处理流程测试
- [x] 服务集成测试
- [x] 错误处理测试

#### Task 2.2: 时序处理优化 🔧
**目标**：处理DC101延迟到达的问题

```go
// 时间窗口更新策略
func (tls *TransactionLifecycleService) HandleCounterUpdate(
    ctx context.Context, deviceID string, nozzleID byte, 
    counterType byte, value decimal.Decimal, timestamp time.Time) error {
    
    // 查找最近的已完成Transaction
    tx := tls.repository.FindLatestCompletedTransaction(deviceID, nozzleID)
    if tx == nil {
        return nil // 没有可更新的交易
    }
    
    // 检查时间窗口（例如30秒内）
    if timestamp.Sub(*tx.CompletedAt) > 30*time.Second {
        return nil // 超出更新窗口
    }
    
    // 更新结束泵码
    return tls.updateEndCounters(ctx, tx.ID, counterType, value)
}
```

### Phase 3: 测试和优化 (Week 3) - 质量保证

#### Task 3.1: 综合测试套件 🧪
- **单元测试覆盖率 ≥ 85%**
- **集成测试**：完整交易流程
- **压力测试**：并发多喷嘴操作
- **故障恢复测试**：进程重启、数据库连接中断

#### Task 3.2: 性能优化 ⚡
- **数据库查询优化**：合理索引设计
- **并发优化**：减少锁争用
- **内存优化**：移除不必要的缓存

---

## 🧪 测试驱动开发策略

### 测试层次结构

#### Level 1: 单元测试 (90% 覆盖率目标)
```go
// 示例：NozzleCountersService单元测试
func TestNozzleCountersService_UpdateFromDC101(t *testing.T) {
    tests := []struct {
        name        string
        deviceID    string
        counterType byte
        value       decimal.Decimal
        expectError bool
    }{
        {
            name:        "Valid volume counter update",
            deviceID:    "device_001",
            counterType: 0x01, // 喷嘴1体积计数器
            value:       decimal.NewFromFloat(123.456),
            expectError: false,
        },
        // 更多测试案例...
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试实现
        })
    }
}
```

#### Level 2: 集成测试
```go
// 示例：完整交易流程集成测试
func TestTransactionLifecycle_CompleteFlow(t *testing.T) {
    // Setup: 启动测试数据库和服务
    
    // Step 1: DC3 Nozzle Out → 创建Transaction
    tx, err := service.HandleNozzleOut(ctx, "device_001", 1, decimal.NewFromFloat(1.259), time.Now())
    assert.NoError(t, err)
    assert.Equal(t, TransactionStatusInitiated, tx.Status)
    
    // Step 2: DC2 → 更新体积金额
    err = service.HandleVolumeAmountUpdate(ctx, "device_001", 1, decimal.NewFromFloat(25.5), decimal.NewFromFloat(32.09), time.Now())
    assert.NoError(t, err)
    
    // Step 3: DC1 FILLING_COMPLETED → 完成交易
    err = service.HandleFillingCompleted(ctx, "device_001", time.Now())
    assert.NoError(t, err)
    
    // Verify: 检查最终状态
    finalTx, err := service.GetTransaction(ctx, tx.ID)
    assert.NoError(t, err)
    assert.Equal(t, TransactionStatusCompleted, finalTx.Status)
}
```

#### Level 3: 端到端测试
```go
// 示例：DevicePoller端到端测试
func TestDevicePoller_E2E_TransactionFlow(t *testing.T) {
    // 模拟真实的DC事务序列
    dcTransactions := [][]byte{
        {0x03, 0x04, 0x01, 0x25, 0x90, 0x11}, // DC3 Nozzle Out
        {0x02, 0x08, 0x00, 0x25, 0x50, 0x00, 0x00, 0x32, 0x09, 0x00}, // DC2 Volume/Amount
        {0x01, 0x01, 0x05}, // DC1 FILLING_COMPLETED
        {0x65, 0x06, 0x01, 0x00, 0x12, 0x34, 0x56, 0x78}, // DC101 Volume Counter
    }
    
    // 依次发送DC事务，验证交易创建和更新
    for _, dcData := range dcTransactions {
        err := devicePoller.processDeviceData(dcData)
        assert.NoError(t, err)
    }
    
    // 验证最终的Transaction状态
}
```

---

## 📅 开发计划时间线

### Week 1: Foundation (MVP Core)
- **Day 1-2**: NozzleCountersService + 数据模型
- **Day 3-4**: TransactionLifecycleService基础功能
- **Day 5**: 核心单元测试 + 代码审查

### Week 2: Integration  
- **Day 1-2**: DevicePoller重构
- **Day 3-4**: DC事务处理器集成
- **Day 5**: 集成测试 + Bug修复

### Week 3: Polish & Testing
- **Day 1-2**: 综合测试套件
- **Day 3-4**: 性能优化 + 异常处理
- **Day 5**: 文档更新 + 部署准备

---

## 🎯 MVP验收标准

### 功能要求
- [x] DC3 Nozzle Out能创建Transaction并持久化
- [x] DC2能实时更新Transaction数据
- [x] DC1 FILLING_COMPLETED能正确更新状态
- [x] DC101能在时间窗口内更新结束泵码
- [x] 进程重启后状态可恢复

### 质量要求
- [x] 单元测试覆盖率 ≥ 85%
- [x] 所有集成测试通过
- [x] 无内存泄漏
- [x] 支持并发多喷嘴操作

### 性能要求
- [x] Transaction创建延迟 < 50ms
- [x] DC事务处理延迟 < 10ms
- [x] 支持100+并发喷嘴操作

---

## 🔧 实施细节

### 关键文件修改清单

| 文件路径 | 改动类型 | 改动描述 |
|---------|---------|----------|
| `internal/services/transaction/` | 🔧 扩展 | 新增TransactionLifecycleService |
| `internal/services/nozzle/` | 🔧 扩展 | 新增NozzleCountersService |
| `internal/services/polling/v2/device_poller.go` | 🔧 重构 | 移除TransactionAssembler依赖 |
| `internal/services/polling/v2/transaction_assembler.go` | 🚨 删除 | 移除或大幅简化 |
| `pkg/models/transaction.go` | 🔧 扩展 | 新增生命周期字段 |
| `pkg/models/nozzle_counters.go` | 🆕 新建 | 新增泵码表模型 |

### 数据库迁移

```sql
-- 新增累计泵码表
CREATE TABLE nozzle_counters (
    device_id VARCHAR(255) NOT NULL,
    nozzle_id TINYINT NOT NULL,
    volume_counter DECIMAL(15,3) DEFAULT 0,
    amount_counter DECIMAL(15,2) DEFAULT 0,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (device_id, nozzle_id),
    INDEX idx_last_update (last_update)
);

-- 扩展transactions表
ALTER TABLE transactions 
ADD COLUMN initiated_at TIMESTAMP NULL AFTER created_at,
ADD COLUMN filling_at TIMESTAMP NULL AFTER initiated_at,
ADD COLUMN counter_update_window INT DEFAULT 30 AFTER counter_validated;
```

---

## 🚨 风险评估与缓解

### 高风险项
1. **数据库性能** - 频繁的Transaction更新可能影响性能
   - **缓解**: 批量更新、读写分离、合理索引
2. **并发控制** - 多喷嘴同时操作可能冲突
   - **缓解**: 乐观锁、事务隔离级别优化
3. **向后兼容** - 现有系统可能依赖旧的交易模型
   - **缓解**: 渐进式迁移、双写策略

### 中风险项
1. **DC101时序问题** - 泵码更新可能延迟或乱序
   - **缓解**: 时间窗口策略、幂等性保证
2. **异常恢复** - 进程重启时的状态恢复
   - **缓解**: 完善的数据库状态管理

---

## 📊 成功指标

### 技术指标
- **可靠性**: 交易丢失率 < 0.01%
- **性能**: 平均响应时间 < 50ms
- **并发**: 支持 100+ 并发喷嘴

### 业务指标  
- **数据完整性**: 泵码数据完整率 > 95%
- **异常恢复**: 进程重启后状态恢复成功率 100%
- **维护性**: 新开发人员理解代码时间 < 1天

---

这个开发计划基于MVP原则，优先实现核心功能，确保业务连续性，同时采用测试驱动开发保证代码质量。您觉得这个计划如何？需要调整哪些部分？ 