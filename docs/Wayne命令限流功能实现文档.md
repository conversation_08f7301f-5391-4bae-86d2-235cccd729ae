# Wayne命令限流功能实现文档

## 概述

为FCC服务实现了API Gateway级别的Wayne DART协议命令限流功能，解决同一nozzle短时间内重复请求preset接口的问题。

**核心特性**：基于`device_id + nozzle_id + operation`的精确限流控制

## 实现架构

### 架构层次
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                        │
│  Echo Middleware → Wayne Routes → Wayne Handlers            │
├─────────────────────────────────────────────────────────────┤
│                  Infrastructure Layer                       │
│  Redis Cache (主) / Memory Cache (降级)                     │
└─────────────────────────────────────────────────────────────┘
```

### 请求处理链路
```
HTTP Request
    ↓
WayneRateLimiterMiddleware
    ├─ 解析JSON请求体 (device_id, nozzle_id)
    ├─ 构建限流Key: throttle:{device_id}:{nozzle_id}:{operation}
    ├─ 检查缓存计数器
    └─ 决策: 通过 / 限流(429)
    ↓
Wayne Command Handler (原有逻辑)
```

## 核心组件

### 1. 限流中间件
**文件**: `internal/server/middleware/rate_limiter.go`

**关键函数**:
- `WayneRateLimiterMiddleware()`: 主中间件函数
- `extractWayneOperation()`: 从路径提取操作类型
- `extractRequestParams()`: 从JSON请求体提取参数
- `checkRateLimit()`: 执行限流检查

### 2. 限流规则配置
```go
var DefaultRateLimitRules = map[string]RateLimitConfig{
    "preset_volume": {
        WindowDuration: 3 * time.Second,  // 3秒窗口
        MaxRequests:    1,                // 最多1次
        Enabled:        true,
    },
    "preset_amount": {
        WindowDuration: 3 * time.Second,
        MaxRequests:    1,
        Enabled:        true,
    },
    "authorize": {
        WindowDuration: 3 * time.Second,
        MaxRequests:    2,                // 允许重试
        Enabled:        true,
    },
}
```

### 3. 路由集成
**文件**: `internal/server/routes_wayne.go`

```go
// Wayne DART 协议专用API路由组
wayne := e.Group("/api/v2/wayne")

// 添加Wayne命令限流中间件
wayne.Use(middleware.WayneRateLimiterMiddleware(cache, logger))
```

### 4. 主程序集成
**文件**: `main_v2.go`

```go
// 注册 Wayne DART 协议路由（带限流中间件）
server.SetupWayneRoutes(app.echoServer, app.dispatchTask, 
    app.deviceManager, app.nozzleService, app.cache, app.logger)
```

## 技术实现细节

### 1. 限流Key设计
```
格式: throttle:{device_id}:{nozzle_id}:{operation}
示例: throttle:device_001:nozzle_001:preset_volume
```

**优势**:
- 设备级隔离：不同设备不互相影响
- 喷嘴级隔离：同设备不同喷嘴独立限流
- 操作级隔离：不同操作类型独立计数

### 2. 请求体解析
```go
// 解析JSON获取基础字段
var baseReq struct {
    DeviceID string  `json:"device_id"`
    NozzleID *string `json:"nozzle_id"`  // 指针类型处理
}

// 重新设置请求体供后续handler使用
c.Request().Body = io.NopCloser(strings.NewReader(string(body)))
```

### 3. 缓存操作
```go
// 原子性递增计数器
count, err := cache.Incr(ctx, key)

// 首次请求设置过期时间
if count == 1 {
    cache.Expire(ctx, key, window)
}

// 判断是否超限
allowed := count <= int64(limit)
```

### 4. 错误处理策略
- **Fail-Open**: 缓存失败时允许请求通过
- **解析失败**: 不阻断请求，交由后续Handler处理
- **详细响应**: 返回结构化错误信息

## 限流响应格式

### 成功请求
正常转发到Wayne Command Handler，返回原有响应格式。

### 限流拒绝 (HTTP 429)
```json
{
    "error": "Rate limit exceeded",
    "message": "Nozzle nozzle_001 can only perform preset_volume 1 time(s) per 3s",
    "device_id": "device_001",
    "nozzle_id": "nozzle_001", 
    "operation": "preset_volume",
    "window_seconds": 3,
    "max_requests": 1,
    "retry_after": 3
}
```

## 日志记录

### Debug日志
```
Rate limit check passed: device_id=device_001 nozzle_id=nozzle_001 operation=preset_volume remaining=0
```

### 警告日志
```
Rate limit exceeded: device_id=device_001 nozzle_id=nozzle_001 operation=preset_volume rate_limit_key=throttle:device_001:nozzle_001:preset_volume
```

## 测试验证

### 测试脚本
**文件**: `test_rate_limit.sh`

**测试场景**:
1. 同一nozzle重复请求被限流 ✅
2. 不同nozzle请求不受影响 ✅  
3. 不同操作类型独立限流 ✅
4. 限流错误响应格式验证 ✅

### 手动测试
```bash
# 第一次请求 - 成功
curl -X POST "http://localhost:8080/api/v2/wayne/preset-volume" \
  -H "Content-Type: application/json" \
  -d '{"device_id":"device_001","nozzle_id":"nozzle_001","volume":50.0}'

# 3秒内重复请求 - 被限流
curl -X POST "http://localhost:8080/api/v2/wayne/preset-volume" \
  -H "Content-Type: application/json" \
  -d '{"device_id":"device_001","nozzle_id":"nozzle_001","volume":60.0}'
```

## 配置管理

### 当前配置方式
硬编码在`DefaultRateLimitRules`变量中

### 扩展建议
- 支持配置文件动态加载
- 支持运行时规则调整
- 支持不同环境差异化配置

## 性能考虑

### 缓存策略
- **Redis**: 分布式环境推荐，支持集群部署
- **Memory**: 单实例环境，性能最优
- **自动降级**: Redis失败时自动切换到内存缓存

### 性能指标
- **延迟**: 增加 < 1ms (内存缓存)
- **吞吐**: 对正常请求无影响
- **内存**: 每个限流Key约占用32字节

## 监控建议

### 关键指标
- 限流触发次数
- 限流Key分布
- 缓存操作延迟
- 限流规则命中率

### 告警规则
```yaml
- alert: HighRateLimitRate
  expr: rate(wayne_rate_limit_blocked_total[5m]) > 0.1
  for: 2m
  annotations:
    summary: "Wayne命令限流频率过高"
```

## 故障排查

### 常见问题

**Q: 限流不生效**
- 检查缓存连接状态
- 确认请求路径匹配规则
- 验证JSON解析是否成功

**Q: 误限流**
- 检查限流Key构造逻辑
- 确认时间窗口配置
- 验证缓存Key过期时间

**Q: 性能影响**
- 监控缓存操作延迟
- 检查内存使用情况
- 考虑调整限流规则

### 调试日志
```go
// 启用Debug级别日志查看详细信息
logger.Debug("Failed to extract request params for rate limiting",
    zap.Error(err), zap.String("path", c.Path()))
```

## 部署说明

### 依赖要求
- Go 1.19+
- Redis (可选，有内存缓存降级)
- 现有FCC服务框架

### 部署步骤
1. 确保代码编译通过
2. 配置Redis连接（可选）
3. 重启FCC服务
4. 验证限流功能

### 回滚方案
如需禁用限流功能，注释掉路由中间件：
```go
// wayne.Use(middleware.WayneRateLimiterMiddleware(cache, logger))
```

## 未来扩展

### 短期优化
- 支持配置文件动态加载
- 增加Prometheus监控指标
- 优化错误响应格式

### 长期规划
- 支持分布式限流算法（滑动窗口）
- 集成熔断器模式
- 支持基于用户/角色的差异化限流

---

**版本**: v1.0  
**更新时间**: 2024-12-19  
**维护者**: FCC开发团队 