# FCC Nozzle Model & Service V2 完善规划文档

## 1. 项目背景与约束

### 1.1 核心约束
- **✅ 专注 services/polling/v2**：不修改 wayne/adapter.go，只兼容轮询系统v2
- **✅ Device-Pump映射**：Device 相当于 Wayne 协议的 Pump，Nozzle 属于 Device
- **✅ 保持pump包状态机**：不修改 internal/adapters/wayne/pump 的状态机实现
- **✅ 服务层集成**：专注于 services/polling/v2 的状态机和nozzle服务集成

### 1.2 现状分析
```
现有架构:
Device (1) ←→ (N) Nozzle
   ↓
DevicePoller (services/polling/v2)
   ↓
Wayne DART协议 (DC1/DC2/DC3事务)
   ↓  
pump.TransactionBuilder (保持不变)
```

## 2. V2架构设计

### 2.1 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                        API层                                │
│    HTTP API (设备和喷嘴管理) + Admin Frontend              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                     服务层                                  │
│  ┌──────────────────┐  ┌──────────────────────────────────┐  │
│  │  Nozzle Service  │  │    Device Manager Service       │  │
│  │   (V2统一实现)    │  │  (与polling/v2集成)              │  │
│  └──────────────────┘  └──────────────────────────────────┘  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  轮询层 (核心)                               │
│  ┌──────────────────────────────────────────────────────┐  │
│  │            services/polling/v2                       │  │
│  │  ┌────────────────┐  ┌──────────────────────────────┐ │  │
│  │  │ DevicePoller   │  │    DeviceStateMachine        │ │  │
│  │  │ (轮询管理)      │  │   (状态管理 + Nozzle状态)    │ │  │
│  │  └────────────────┘  └──────────────────────────────┘ │  │
│  └──────────────────────────────────────────────────────┘  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   协议层                                     │
│  ┌──────────────────────────────────────────────────────┐  │
│  │          Wayne DART Protocol                         │  │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐ │  │
│  │  │    DC1      │ │    DC2      │ │      DC3        │ │  │
│  │  │ (泵状态)     │ │ (体积/金额)  │ │ (价格/喷嘴状态) │ │  │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘ │  │
│  └──────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件关系
```mermaid
graph TB
    A[HTTP API] --> B[Nozzle Service V2]
    A --> C[Device Manager]
    B --> D[DevicePoller V2]
    C --> D
    D --> E[DeviceStateMachine V2]
    D --> F[Wayne DART Protocol]
    E --> G[Nozzle State Management]
    F --> H[DC1/DC2/DC3 Transactions]
    H --> G
    G --> B
```

## 3. 分步骤开发规划

### 第一阶段：统一模型定义 (2天)

#### 任务1.1: 创建统一的Nozzle模型
**目标**: 创建与Wayne DART协议完全对应的Nozzle模型

**文件**: `pkg/models/nozzle.go`
```go
// 统一的Nozzle模型 - 严格对应Wayne DART协议
type Nozzle struct {
    // 基础标识
    ID       string `json:"id" gorm:"primaryKey"`
    Number   byte   `json:"number" gorm:"not null;check:number >= 1 AND number <= 15"`
    Name     string `json:"name"`
    DeviceID string `json:"device_id" gorm:"not null;index"`
    
    // Wayne DART协议状态映射
    Status       NozzleStatus    `json:"status" gorm:"default:idle"`
    IsOut        bool           `json:"is_out" gorm:"default:false"`        // DC3 NOZIO bit 4
    IsSelected   bool           `json:"is_selected" gorm:"default:false"`   // DC3 NOZIO bits 0-3
    
    // 油品和价格 (CD5事务支持)
    FuelGradeID  *string         `json:"fuel_grade_id"`
    CurrentPrice decimal.Decimal `json:"current_price" gorm:"type:decimal(10,4)"`
    
    // Wayne DART交易数据 (DC2事务)
    CurrentVolume decimal.Decimal `json:"current_volume" gorm:"type:decimal(12,4)"`
    CurrentAmount decimal.Decimal `json:"current_amount" gorm:"type:decimal(12,2)"`
    
    // 统计数据
    TotalVolume      decimal.Decimal `json:"total_volume"`
    TotalAmount      decimal.Decimal `json:"total_amount"`
    TransactionCount int64          `json:"transaction_count"`
    
    // 时间戳
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

// Wayne DART协议状态 - 对应pump状态机但独立维护
type NozzleStatus string
const (
    NozzleStatusIdle        NozzleStatus = "idle"        // 对应pump.StatusIdle
    NozzleStatusSelected    NozzleStatus = "selected"    // DC3事务中选择状态
    NozzleStatusOut         NozzleStatus = "out"         // DC3 NOZIO=0x1X
    NozzleStatusFilling     NozzleStatus = "filling"     // DC2事务活跃状态
    NozzleStatusCompleted   NozzleStatus = "completed"   // 加油完成
    NozzleStatusError       NozzleStatus = "error"       // 错误状态
)
```

#### 任务1.2: 扩展Device模型以支持Nozzle关系
**目标**: 在现有Device模型基础上添加Nozzle关联

**文件**: `pkg/models/device.go`（修改）
```go
// 在Device结构体中添加
type Device struct {
    // ... 现有字段 ...
    
    // Nozzle关联 - Device作为Wayne Pump的映射
    Nozzles     []Nozzle `json:"nozzles,omitempty" gorm:"foreignKey:DeviceID"`
    NozzleCount int      `json:"nozzle_count" gorm:"-"` // 计算字段
    
    // Wayne协议特有配置
    MaxNozzles      int  `json:"max_nozzles" gorm:"default:15"`      // 最大喷嘴数(Wayne限制)
    SupportedGrades []string `json:"supported_grades" gorm:"type:jsonb"` // 支持的油品等级
}

// 添加便捷方法
func (d *Device) GetActiveNozzles() []Nozzle
func (d *Device) GetNozzleByNumber(number byte) *Nozzle
func (d *Device) IsNozzleNumberValid(number byte) bool
```

### 第二阶段：NozzleService V2实现 (3天)

#### 任务2.1: 实现完整的NozzleService
**目标**: 基于统一模型创建生产级NozzleService

**文件**: `internal/services/nozzle/service_v2.go`
```go
type ServiceV2 interface {
    // 基础CRUD - 与数据库交互
    CreateNozzle(ctx context.Context, nozzle *models.Nozzle) error
    GetNozzle(ctx context.Context, deviceID string, number byte) (*models.Nozzle, error)
    GetDeviceNozzles(ctx context.Context, deviceID string) ([]*models.Nozzle, error)
    UpdateNozzle(ctx context.Context, nozzle *models.Nozzle) error
    DeleteNozzle(ctx context.Context, deviceID string, number byte) error
    
    // Wayne DART协议专用方法
    GetNozzlePricesForCD5(ctx context.Context, deviceID string) ([][]byte, error)
    UpdateFromDC2Transaction(ctx context.Context, deviceID string, volume, amount decimal.Decimal) error
    UpdateFromDC3Transaction(ctx context.Context, deviceID string, nozzleNum byte, nozio byte, price decimal.Decimal) error
    
    // 批量操作 - 性能优化
    BatchUpdateNozzleStates(ctx context.Context, deviceID string, updates []NozzleStateUpdate) error
    SyncDeviceNozzles(ctx context.Context, deviceID string) error
    
    // 设备初始化支持
    InitializeDeviceNozzles(ctx context.Context, deviceID string, configs []NozzleConfig) error
}
```

#### 任务2.2: 集成BCD转换器
**目标**: 完善Wayne协议的BCD数据处理

**依赖**: 使用现有的 `internal/adapters/wayne/pump/bcd.go`
```go
// 在NozzleService中集成BCD转换
func (s *serviceV2) GetNozzlePricesForCD5(ctx context.Context, deviceID string) ([][]byte, error) {
    // 1. 获取设备所有喷嘴价格
    nozzles, err := s.GetDeviceNozzles(ctx, deviceID)
    if err != nil {
        return nil, err
    }
    
    // 2. 按喷嘴编号(1-15)排序并转换为BCD
    bcdConverter := pump.NewBCDConverter()
    var pricesBCD [][]byte
    
    for nozzleNum := byte(1); nozzleNum <= 15; nozzleNum++ {
        if nozzle := findNozzleByNumber(nozzles, nozzleNum); nozzle != nil {
            // Wayne协议: 3字节BCD，3位小数
            priceBCD := bcdConverter.EncodePrice(nozzle.CurrentPrice.InexactFloat64(), 3, 3)
            pricesBCD = append(pricesBCD, priceBCD)
        } else {
            // 无喷嘴时填充0
            pricesBCD = append(pricesBCD, []byte{0x00, 0x00, 0x00})
        }
    }
    
    return pricesBCD, nil
}
```

### 第三阶段：DevicePoller V2扩展 (3天)

#### 任务3.1: 扩展DevicePoller以支持Nozzle状态管理
**目标**: 在现有DevicePoller基础上添加Nozzle状态同步

**文件**: `internal/services/polling/v2/device_poller.go`（修改）
```go
// 在DevicePoller结构体中添加
type DevicePoller struct {
    // ... 现有字段 ...
    
    // 新增：Nozzle服务集成
    nozzleService nozzle.ServiceV2 // 注入NozzleService
    nozzleStates  map[byte]*NozzleState // 本地喷嘴状态缓存
    nozzleMutex   sync.RWMutex
}

// 新增：Nozzle状态缓存
type NozzleState struct {
    Number        byte
    Status        models.NozzleStatus
    IsOut         bool
    IsSelected    bool
    CurrentPrice  decimal.Decimal
    CurrentVolume decimal.Decimal
    CurrentAmount decimal.Decimal
    LastUpdate    time.Time
}
```

#### 任务3.2: 完善DC事务处理
**目标**: 加强现有的DC2/DC3事务处理以更新Nozzle状态

**修改**: `internal/services/polling/v2/device_poller.go` 中的事务处理方法
```go
// 增强DC2事务处理 - 体积/金额更新
func (p *DevicePoller) processDC2VolumeAmount(transaction pump.DCTransaction) error {
    data := transaction.GetData()
    if len(data) < 8 {
        return fmt.Errorf("DC2 transaction data too short")
    }

    // 解码BCD数据
    bcdConverter := pump.NewBCDConverter()
    volume := decimal.NewFromFloat(bcdConverter.DecodeVolume(data[0:4], 3))
    amount := decimal.NewFromFloat(bcdConverter.DecodeAmount(data[4:8], 2))

    // 获取当前选中的喷嘴
    selectedNozzle := p.getCurrentSelectedNozzle()
    if selectedNozzle == 0 {
        return fmt.Errorf("no nozzle selected for volume/amount update")
    }

    // 更新本地缓存
    p.updateNozzleCache(selectedNozzle, func(state *NozzleState) {
        state.CurrentVolume = volume
        state.CurrentAmount = amount
        state.Status = models.NozzleStatusFilling
        state.LastUpdate = time.Now()
    })

    // 同步到数据库 (异步)
    go func() {
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        
        if err := p.nozzleService.UpdateFromDC2Transaction(ctx, p.config.DeviceInfo.ID, volume, amount); err != nil {
            p.logger.Error("Failed to sync DC2 data to database", zap.Error(err))
        }
    }()

    return nil
}

// 增强DC3事务处理 - 价格/喷嘴状态
func (p *DevicePoller) processDC3PriceNozzle(transaction pump.DCTransaction) error {
    data := transaction.GetData()
    if len(data) < 4 {
        return fmt.Errorf("DC3 transaction data too short")
    }

    // 解析DC3数据
    bcdConverter := pump.NewBCDConverter()
    price := decimal.NewFromFloat(bcdConverter.DecodePrice(data[0:3], 3))
    nozio := data[3]
    
    nozzleNumber := nozio & 0x0F
    isOut := (nozio & 0x10) != 0

    // 更新本地缓存
    p.updateNozzleCache(nozzleNumber, func(state *NozzleState) {
        state.CurrentPrice = price
        state.IsOut = isOut
        state.IsSelected = true // DC3事务表明喷嘴被选中
        state.Status = p.determineNozzleStatus(isOut, state.CurrentVolume)
        state.LastUpdate = time.Now()
    })

    // 同步到数据库 (异步)
    go func() {
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        
        if err := p.nozzleService.UpdateFromDC3Transaction(ctx, p.config.DeviceInfo.ID, nozzleNumber, nozio, price); err != nil {
            p.logger.Error("Failed to sync DC3 data to database", zap.Error(err))
        }
    }()

    return nil
}
```

### 第四阶段：状态机集成 (2天)

#### 任务4.1: 扩展pkg/models/v2的DeviceStateMachine
**目标**: 在v2状态机接口中添加Nozzle状态管理

**文件**: `pkg/models/v2/device_state.go`（修改）
```go
// 扩展DeviceStateMachine接口
type DeviceStateMachine interface {
    // ... 现有方法 ...
    
    // 新增：Nozzle状态管理
    GetNozzleStates() map[byte]NozzleStateData
    UpdateNozzleState(nozzleNum byte, state NozzleStateData) error
    GetSelectedNozzle() byte
    SetSelectedNozzle(nozzleNum byte) error
    
    // 新增：Wayne协议事件处理
    ProcessDC2Transaction(volume, amount decimal.Decimal) error
    ProcessDC3Transaction(nozzleNum byte, nozio byte, price decimal.Decimal) error
    SyncWithNozzleService(nozzleService interface{}) error
}
```

#### 任务4.2: 实现设备级状态机
**目标**: 创建集成Nozzle状态的设备状态机实现

**文件**: `internal/services/polling/v2/device_state_machine.go`
```go
// 设备状态机V2实现 - 集成Nozzle状态
type deviceStateMachineV2 struct {
    deviceInfo    v2models.DeviceInfo
    deviceState   v2models.DeviceStateData
    nozzleStates  map[byte]*v2models.NozzleStateData
    nozzleService nozzle.ServiceV2
    
    mu sync.RWMutex
    logger *zap.Logger
}

// 实现DeviceStateMachine接口
func (dsm *deviceStateMachineV2) ProcessDC2Transaction(volume, amount decimal.Decimal) error {
    dsm.mu.Lock()
    defer dsm.mu.Unlock()
    
    selectedNozzle := dsm.deviceState.ActiveNozzles[0] // 假设单喷嘴选择
    if nozzleState, exists := dsm.nozzleStates[selectedNozzle]; exists {
        nozzleState.CurrentVolume = volume
        nozzleState.CurrentAmount = amount
        nozzleState.State = v2models.NozzleStateFilling
        nozzleState.LastUpdate = time.Now()
        
        // 触发状态变更事件
        return dsm.publishNozzleStateChange(selectedNozzle, nozzleState)
    }
    
    return fmt.Errorf("selected nozzle %d not found", selectedNozzle)
}

func (dsm *deviceStateMachineV2) ProcessDC3Transaction(nozzleNum byte, nozio byte, price decimal.Decimal) error {
    dsm.mu.Lock()
    defer dsm.mu.Unlock()
    
    isOut := (nozio & 0x10) != 0
    
    if nozzleState, exists := dsm.nozzleStates[nozzleNum]; exists {
        nozzleState.CurrentPrice = price
        nozzleState.IsOut = isOut
        nozzleState.IsSelected = true
        nozzleState.State = dsm.computeNozzleState(isOut, nozzleState.CurrentVolume)
        nozzleState.LastUpdate = time.Now()
        
        // 更新设备的活跃喷嘴列表
        dsm.updateActiveNozzles(nozzleNum, true)
        
        return dsm.publishNozzleStateChange(nozzleNum, nozzleState)
    }
    
    return fmt.Errorf("nozzle %d not found", nozzleNum)
}
```

### 第五阶段：测试和文档 (2天)

#### 任务5.1: 单元测试覆盖
**目标**: 确保所有新组件有≥80%测试覆盖率

**文件**: 
- `internal/services/nozzle/service_v2_test.go`
- `internal/services/polling/v2/device_poller_nozzle_test.go`
- `pkg/models/nozzle_test.go`

#### 任务5.2: 集成测试
**目标**: 验证完整的DC事务流程

**文件**: `tests/integration/nozzle_v2_integration_test.go`
```go
func TestNozzleV2Integration(t *testing.T) {
    // 测试场景：
    // 1. 设备初始化 -> 创建喷嘴
    // 2. CD5价格设置 -> 价格更新
    // 3. DC3喷嘴选择 -> 状态变更
    // 4. DC2交易数据 -> 实时更新
    // 5. 交易完成 -> 状态重置
}
```

## 4. 核心技术决策

### 4.1 状态同步策略
```
Wayne DART Event → DevicePoller → Local Cache → Async DB Sync
                                      ↓
                                 State Machine → Event Publish
```

**优势**:
- 响应Wayne协议25ms要求
- 本地缓存提供快速查询
- 异步同步避免阻塞轮询

### 4.2 数据一致性保证
1. **本地缓存为准**: DevicePoller的本地状态是实时状态的权威源
2. **异步同步**: 数据库更新不阻塞协议处理
3. **故障恢复**: 启动时从数据库恢复状态到本地缓存

### 4.3 错误处理策略
```go
// 分层错误处理
type NozzleError struct {
    Type    string `json:"type"`    // "validation", "protocol", "database"
    Code    string `json:"code"`    // 错误代码
    Message string `json:"message"` // 错误描述
    DeviceID string `json:"device_id"`
    NozzleNum byte  `json:"nozzle_num,omitempty"`
}
```

## 5. 性能要求与优化

### 5.1 性能目标
- **DC事务处理**: <5ms 本地处理时间
- **数据库同步**: <50ms 异步同步时间  
- **状态查询**: <10ms 本地缓存响应
- **Wayne协议响应**: ≤25ms (DART要求)

### 5.2 优化策略
- **批量操作**: 支持多喷嘴状态批量更新
- **内存优化**: 限制状态历史记录大小
- **连接池**: 数据库连接复用
- **异步处理**: 非关键路径异步执行

## 6. 验收标准

### 6.1 功能完整性
- ✅ 支持Wayne DART协议所有喷嘴相关事务
- ✅ 本地状态与数据库状态最终一致
- ✅ 支持1-15个喷嘴的完整管理
- ✅ 与services/polling/v2完全集成

### 6.2 性能标准
- ✅ DC事务处理时间符合性能目标
- ✅ 内存使用稳定，无内存泄露
- ✅ 单元测试覆盖率≥80%
- ✅ 集成测试覆盖主要业务流程

### 6.3 兼容性标准
- ✅ 不影响现有pump包状态机
- ✅ 与现有FCC架构兼容
- ✅ 向后兼容现有API
- ✅ 支持平滑升级和回滚

## 7. 风险评估与缓解

### 7.1 主要风险
1. **状态同步复杂性**: 本地缓存与数据库的一致性维护
2. **性能风险**: 频繁的DC事务可能影响系统性能
3. **集成风险**: 与现有polling/v2系统的集成复杂度

### 7.2 缓解措施
1. **状态同步**: 实现完善的故障恢复和冲突解决机制
2. **性能监控**: 添加详细的性能指标和告警
3. **渐进集成**: 分阶段集成，每阶段充分测试

## 8. 实施时间线

| 阶段 | 任务 | 估计工时 | 关键里程碑 |
|------|------|----------|------------|
| 第一阶段 | 模型统一 | 2天 | 统一Nozzle模型完成 |
| 第二阶段 | NozzleService | 3天 | 服务层实现完成 |
| 第三阶段 | DevicePoller扩展 | 3天 | 轮询层集成完成 |
| 第四阶段 | 状态机集成 | 2天 | 状态管理完成 |
| 第五阶段 | 测试文档 | 2天 | 全面测试通过 |

**总计**: 12个工作日

## 9. 下一步行动

1. **确认技术方案**: 验证与现有架构的兼容性
2. **建立开发分支**: 创建 `feature/nozzle-service-v2` 分支
3. **代码评审流程**: 建立每阶段的代码评审检查点
4. **开始第一阶段**: 统一Nozzle模型定义

---

**注**: 本规划基于现有 services/polling/v2 架构，确保与Wayne DART协议完全兼容，同时保持系统的高性能和可维护性。 