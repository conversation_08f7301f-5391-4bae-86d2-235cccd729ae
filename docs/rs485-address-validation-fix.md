# RS485 地址验证修复方案

## 问题确认

通过代码分析确认，当前系统在以下关键位置缺乏设备地址验证：

### 1. SerialTransport.SendFrameAndWait() 无地址检查

```go
// 位置: internal/services/polling/v2/transport_implementations.go:320-334
select {
case frame := <-st.frameReady:
    // ❌ 问题：接收任何帧，不检查地址匹配
    return frame, nil
case <-timeoutCtx.Done():
    return nil, fmt.Errorf("timeout...")
}
```

### 2. frameAssemblerRoutine() 无地址过滤

```go
// 位置: internal/services/polling/v2/transport_implementations.go:518-520
select {
case st.frameReady <- frame:
    // ❌ 问题：所有帧都发送到共享通道，无地址检查
}
```

### 3. DevicePoller.executeCommand() 无响应验证

```go
// 位置: internal/services/polling/v2/device_poller.go:680-690
responseFrame, err := p.communication.SendFrameAndWait(ctx, frameData, timeout)
// ❌ 问题：不验证 responseFrame.Address 是否匹配 p.config.DeviceInfo.Address
```

## 立即修复方案

### 修复1: DevicePoller 层面添加地址验证

**文件**: `internal/services/polling/v2/device_poller.go`

在 `sendFrameAndWait` 方法中添加地址验证：

```go
// sendFrameAndWait 发送帧并等待响应
func (p *DevicePoller) sendFrameAndWait(frame *dartline.Frame, timeout time.Duration, businessType string) (*dartline.Frame, error) {
    frameData := frame.Encode()
    
    // ... 现有代码 ...
    
    responseFrame, err := p.communication.SendFrameAndWait(ctx, frameData, timeout)
    if err != nil {
        return nil, err
    }
    
    // 🚀 新增：验证响应地址匹配
    if responseFrame != nil && responseFrame.Address != p.config.DeviceInfo.Address {
        p.logger.Error("设备地址不匹配，可能存在数据串扰",
            zap.String("device_id", p.config.DeviceInfo.ID),
            zap.Uint8("expected_address", p.config.DeviceInfo.Address),
            zap.Uint8("received_address", responseFrame.Address),
            zap.String("business_type", businessType),
            zap.String("frame_hex", fmt.Sprintf("%X", responseFrame.Encode())))
        
        return nil, fmt.Errorf("address mismatch: expected %d, got %d", 
                              p.config.DeviceInfo.Address, responseFrame.Address)
    }
    
    return responseFrame, nil
}
```

### 修复2: SerialTransport 层面实现地址路由

**文件**: `internal/services/polling/v2/transport_implementations.go`

#### 2.1 修改 SerialTransport 结构

```go
type SerialTransport struct {
    deviceID string
    address  byte
    port     string
    logger   *zap.Logger

    // 串口管理器
    serialManager connection.SerialManagerInterface

    // 🚀 新增：设备地址到响应通道的映射
    waitingDevices map[byte]chan *dartline.Frame
    waitingMutex   sync.RWMutex

    // 无锁数据传输管道
    pipeReader *io.PipeReader
    pipeWriter *io.PipeWriter

    // Frame 组装 goroutine 管理
    assemblerCtx    context.Context
    assemblerCancel context.CancelFunc
    assemblerWg     sync.WaitGroup

    // 🚀 移除：不再使用共享的 frameReady channel
    // frameReady chan *dartline.Frame

    // 状态管理
    connected bool
    mu        sync.RWMutex

    // 传输层日志格式化器
    transportLogFormatter *TransportLogFormatter
}
```

#### 2.2 修改 NewSerialTransport

```go
func NewSerialTransport(deviceID string, address byte, port string, logger *zap.Logger) (*SerialTransport, error) {
    // ... 现有代码 ...
    
    transport := &SerialTransport{
        deviceID:              deviceID,
        address:               address,
        port:                  port,
        logger:                logger,
        serialManager:         serialManager,
        waitingDevices:        make(map[byte]chan *dartline.Frame),
        pipeReader:            pipeReader,
        pipeWriter:            pipeWriter,
        assemblerCtx:          assemblerCtx,
        assemblerCancel:       assemblerCancel,
        assemblerWg:           sync.WaitGroup{},
        // frameReady:            make(chan *dartline.Frame, 10), // 移除
        connected:             false,
        transportLogFormatter: NewTransportLogFormatter(deviceID),
    }
    
    // ... 其余代码 ...
}
```

#### 2.3 修改 SendFrameAndWait 实现地址路由

```go
func (st *SerialTransport) SendFrameAndWait(ctx context.Context, data []byte, timeout time.Duration) (*dartline.Frame, error) {
    if !st.IsConnected() {
        return nil, fmt.Errorf("not connected")
    }

    // 🚀 新增：从发送的数据中提取目标设备地址
    if len(data) < 1 {
        return nil, fmt.Errorf("invalid frame data")
    }
    targetAddress := data[0] // DART协议第一个字节是设备地址

    // 🚀 新增：为目标设备地址创建响应通道
    respChan := make(chan *dartline.Frame, 1)
    
    st.waitingMutex.Lock()
    st.waitingDevices[targetAddress] = respChan
    st.waitingMutex.Unlock()
    
    // 确保清理
    defer func() {
        st.waitingMutex.Lock()
        delete(st.waitingDevices, targetAddress)
        close(respChan)
        st.waitingMutex.Unlock()
    }()

    // 记录发送信息
    logFields := st.transportLogFormatter.FormatTransportTx(data, st.port, targetAddress, timeout)
    logFields = append(logFields, zap.String("method", "SendFrameAndWait"))
    st.logger.Debug("[SerialTransport]传输层数据发送", logFields...)

    startTime := time.Now()

    // 发送数据
    err := st.serialManager.SendFrame(ctx, data)
    if err != nil {
        return nil, fmt.Errorf("failed to send frame: %w", err)
    }

    // 🚀 修改：等待特定设备地址的响应
    timeoutCtx, cancel := context.WithTimeout(ctx, 3*timeout)
    defer cancel()

    select {
    case frame := <-respChan:
        elapsed := time.Since(startTime)
        st.logger.Debug("[SerialTransport]传输层Frame接收",
            zap.String("layer", "传输层"),
            zap.String("direction", "↓RX✓"),
            zap.String("device_id", st.deviceID),
            zap.String("port", st.port),
            zap.Uint8("target_address", targetAddress),
            zap.Uint8("received_address", frame.Address),
            zap.String("frame_type", st.getFrameTypeName(frame)),
            zap.Uint8("tx_num", frame.GetTxNumber()),
            zap.Duration("response_time", elapsed),
            zap.String("result", "success"),
            zap.String("timestamp", time.Now().Format("15:04:05.000")))
        return frame, nil
    case <-timeoutCtx.Done():
        elapsed := time.Since(startTime)
        st.logger.Warn("[SerialTransport]传输层Frame超时",
            zap.String("layer", "传输层"),
            zap.String("direction", "RX✗"),
            zap.String("device_id", st.deviceID),
            zap.String("port", st.port),
            zap.Uint8("target_address", targetAddress),
            zap.Duration("timeout_configured", timeout),
            zap.Duration("elapsed", elapsed),
            zap.String("result", "timeout"),
            zap.String("timestamp", time.Now().Format("15:04:05.000")))
        return nil, fmt.Errorf("timeout waiting for response after %v (configured: %v)", elapsed, timeout)
    case <-ctx.Done():
        return nil, ctx.Err()
    }
}
```

#### 2.4 修改 frameAssemblerRoutine 实现地址分发

```go
func (st *SerialTransport) frameAssemblerRoutine() {
    defer st.assemblerWg.Done()
    defer st.pipeReader.Close()

    buffer := make([]byte, 0, 1024)
    readBuf := make([]byte, 256)

    for {
        select {
        case <-st.assemblerCtx.Done():
            st.logger.Info("[SerialTransport][frameAssemblerRoutine]帧组装器收到停止信号")
            return
        default:
        }

        n, err := st.pipeReader.Read(readBuf)
        if err != nil {
            if err == io.EOF {
                st.logger.Info("[SerialTransport][frameAssemblerRoutine]管道关闭，帧组装器停止")
                return
            }
            st.logger.Error("[SerialTransport][frameAssemblerRoutine]Error reading from pipe",
                zap.String("device_id", st.deviceID),
                zap.Error(err))
            return
        }

        if n > 0 {
            buffer = append(buffer, readBuf[:n]...)
            frames := st.extractFramesFromBuffer(&buffer)

            // 🚀 修改：根据地址路由帧到正确的等待通道
            for _, frame := range frames {
                st.waitingMutex.RLock()
                if respChan, exists := st.waitingDevices[frame.Address]; exists {
                    select {
                    case respChan <- frame:
                        st.logger.Debug("[SerialTransport][frameAssemblerRoutine]帧已路由到设备",
                            zap.String("device_id", st.deviceID),
                            zap.Uint8("frame_address", frame.Address),
                            zap.String("frame_type", st.getFrameTypeName(frame)))
                    default:
                        st.logger.Warn("[SerialTransport][frameAssemblerRoutine]设备响应通道已满",
                            zap.String("device_id", st.deviceID),
                            zap.Uint8("frame_address", frame.Address),
                            zap.String("frame_type", st.getFrameTypeName(frame)))
                    }
                } else {
                    // 🚀 新增：记录无人等待的帧（可能是离线设备的延迟响应）
                    st.logger.Warn("[SerialTransport][frameAssemblerRoutine]收到无人等待的帧",
                        zap.String("device_id", st.deviceID),
                        zap.Uint8("frame_address", frame.Address),
                        zap.String("frame_type", st.getFrameTypeName(frame)),
                        zap.String("possible_cause", "设备离线延迟响应或地址错误"))
                }
                st.waitingMutex.RUnlock()
            }
        }
    }
}
```

## 实施步骤

### 第一阶段：立即保护（1小时内）

1. **在 DevicePoller 层面添加地址验证**
   - 修改 `sendFrameAndWait` 方法
   - 添加地址不匹配的错误日志和返回

### 第二阶段：根本修复（1-2天）

1. **修改 SerialTransport 实现地址路由**
   - 实现 `waitingDevices` 映射
   - 修改 `SendFrameAndWait` 方法
   - 修改 `frameAssemblerRoutine` 方法

2. **测试验证**
   - 单设备测试
   - 多设备并发测试
   - 设备离线场景测试

### 第三阶段：监控和优化（持续）

1. **添加监控指标**
   - 地址不匹配次数
   - 无人等待的帧数量
   - 设备响应时间分布

2. **性能优化**
   - 通道大小调优
   - 超时时间优化
   - 内存使用优化

## 预期效果

1. **消除数据串扰**: 确保每个设备只接收自己地址的响应
2. **提高数据准确性**: 避免设备处理错误的业务数据
3. **增强系统稳定性**: 减少因数据串扰导致的业务逻辑错误
4. **改善调试体验**: 清晰的地址不匹配日志便于问题定位

这个修复方案可以在不破坏现有架构的前提下，有效解决 RS485 串口数据串扰问题。
