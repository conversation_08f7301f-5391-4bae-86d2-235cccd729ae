# RS485 半双工通讯架构设计文档

## 概述

本文档详细描述了 FCC 服务中 RS485 半双工通讯的实现机制，包括设备调度、串口共享、DART 协议处理等核心组件的设计和实现。

## 核心架构组件

### 1. 调度任务管理器 (DispatchTask)

`DispatchTask` 是整个通讯系统的核心调度器，负责管理多个设备的轮询任务和串口资源分配。

```go
// 位置: internal/services/polling/v2/dispatch_task.go:180-201
type DispatchTask struct {
	config DispatchTaskConfig
	logger *zap.Logger

	// 设备管理
	pollers       pollerMap                   // 设备轮询器映射
	stateManager  v2models.DeviceStateManager // 状态管理器
	deviceManager api.DeviceManager           // 设备管理器

	// 🚀 通信锁管理 - 每个串口一个锁，避免RS485冲突
	commLocks map[string]*sync.Mutex // port -> lock
	locksMu   sync.RWMutex           // 锁管理的锁

	// 🚀 通信实例管理 - 每个串口一个通信实例，真正共享
	commInstances map[string]CommunicationInterface // port -> communication
	commMu        sync.RWMutex                      // 通信实例锁

	// 🚀 设备调度管理 - 10ms时间片轮询
	deviceOrder  []string     // 设备ID列表，保持调度顺序
	currentIndex int          // 当前调度索引
	orderMu      sync.RWMutex // 设备顺序锁
}
```

**关键特性:**
- **串口锁管理**: 每个串口维护独立的互斥锁，确保 RS485 半双工通讯的原子性
- **通信实例共享**: 多个设备共享同一个串口的通信实例
- **设备调度**: 维护设备轮询顺序，实现公平的时间片分配

### 2. 时间片调度机制

系统采用 10ms 时间片轮询机制，确保所有设备都能公平地获得通讯机会。

```go
// 位置: internal/services/polling/v2/dispatch_task.go:362-381
func (dt *DispatchTask) communicationSchedulerLoop() {
	defer dt.wg.Done()

	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	dt.logger.Info("通信调度器已启动",
		zap.Duration("time_slice", 10*time.Millisecond))

	for {
		select {
		case <-dt.ctx.Done():
			dt.logger.Info("通信调度器已停止")
			return
		case <-ticker.C:
			dt.processNextDeviceQueue()
		}
	}
}
```

**调度算法:**
```go
// 位置: internal/services/polling/v2/dispatch_task.go:383-406
func (dt *DispatchTask) processNextDeviceQueue() {
	dt.orderMu.RLock()
	if len(dt.deviceOrder) == 0 {
		dt.orderMu.RUnlock()
		return
	}

	deviceID := dt.deviceOrder[dt.currentIndex]
	dt.currentIndex = (dt.currentIndex + 1) % len(dt.deviceOrder)
	dt.orderMu.RUnlock()

	// 获取设备轮询器
	poller, exists := dt.pollers.Load(deviceID)
	if !exists {
		dt.logger.Debug("调度器找不到设备", zap.String("device_id", deviceID))
		return
	}

	// 检查队列是否有数据
	poller.ProcessPendingCommand()
}
```

### 3. RS485 半双工保护机制

#### 3.1 串口通信锁

每个串口维护独立的互斥锁，确保同一时间只有一个设备能进行完整的请求-响应周期。

```go
// 位置: internal/services/polling/v2/dispatch_task.go:335-360
func (dt *DispatchTask) getCommunicationLock(deviceInfo v2models.DeviceInfo) *sync.Mutex {
	port := dt.getDeviceSerialPort(deviceInfo)

	dt.locksMu.RLock()
	if lock, exists := dt.commLocks[port]; exists {
		dt.locksMu.RUnlock()
		return lock
	}
	dt.locksMu.RUnlock()

	// 创建新锁（双重检查）
	dt.locksMu.Lock()
	defer dt.locksMu.Unlock()

	if lock, exists := dt.commLocks[port]; exists {
		return lock
	}

	dt.commLocks[port] = &sync.Mutex{}
	dt.logger.Debug("创建新的通信锁",
		zap.String("port", port),
		zap.String("device_id", deviceInfo.ID))

	return dt.commLocks[port]
}
```

#### 3.2 设备轮询器的锁保护

设备轮询器在执行命令时获取串口锁，保护整个发送-等待-接收周期。

```go
// 位置: internal/services/polling/v2/device_poller.go:334-359
func (p *DevicePoller) processPendingCommand(cmd PollCommand) {
	// 🔧 上锁保护整个发送-等待-接收周期
	// 确保同一通信接口在同一时间只有一个设备在进行完整的请求-响应周期
	p.commLock.Lock()
	defer p.commLock.Unlock()

	startTime := time.Now()

	result := p.executeCommand(context.Background(), cmd, startTime)

	// 复用现有的结果处理
	p.handleResult(result)

	// 只在失败时记录详细信息
	if !result.Success {
		p.logger.Warn("[DevicePoller] 命令执行失败",
			zap.String("device_id", p.config.DeviceInfo.ID),
			zap.String("command_type", cmd.Type),
			zap.String("business_type", cmd.BusinessType),
			zap.Duration("execution_time", time.Since(startTime)),
			zap.Error(result.Error))
	}
}
```

### 4. 串口共享机制

#### 4.1 通信实例共享

多个不同地址的设备共享同一个串口的通信实例，通过设备地址字段区分目标设备。

```go
// 位置: internal/services/polling/v2/dispatch_task.go:1516-1526
// 创建新的串口传输 - 注意：这里不传递deviceInfo.Address，因为会被多个设备共享
dt.logger.Info("为串口创建新的通信实例",
	zap.String("device_id", deviceInfo.ID),
	zap.Uint8("device_address", deviceInfo.Address),
	zap.String("serial_port", serialPort))
transport, err := NewSerialTransport(
	fmt.Sprintf("shared_%s", serialPort), // 使用串口名作为ID
	0,                                    // 地址设为0，因为会被多个设备共享
	serialPort,
	dt.logger,
)
```

#### 4.2 设备注册和调度顺序

设备注册时会被添加到调度顺序中，确保公平轮询。

```go
// 位置: internal/services/polling/v2/dispatch_task.go:565-575
// 🚀 创建设备轮询器（集成新服务）
frameBuilder := NewDefaultFrameBuilder()
commLock := dt.getCommunicationLock(deviceInfo) // 🚀 获取通信锁
poller := NewDevicePoller(config, deviceSM, frameBuilder, ...)
dt.pollers.Store(deviceInfo.ID, poller)

// 🚀 将设备添加到调度顺序
dt.orderMu.Lock()
dt.deviceOrder = append(dt.deviceOrder, deviceInfo.ID)
dt.orderMu.Unlock()
```

### 5. 串口传输层实现

#### 5.1 SerialTransport 结构

```go
// 位置: internal/services/polling/v2/transport_implementations.go:105-133
type SerialTransport struct {
	deviceID string
	address  byte
	port     string
	logger   *zap.Logger

	// 串口管理器
	serialManager connection.SerialManagerInterface

	// 无锁数据传输管道 - 替代原有的 dataBuffer 和 dataBufferMux
	pipeReader *io.PipeReader
	pipeWriter *io.PipeWriter

	// Frame 组装 goroutine 管理
	assemblerCtx    context.Context
	assemblerCancel context.CancelFunc
	assemblerWg     sync.WaitGroup

	// Frame 组装
	frameReady chan *dartline.Frame

	// 状态管理
	connected bool
	mu        sync.RWMutex

	// 传输层日志格式化器
	transportLogFormatter *TransportLogFormatter
}
```

#### 5.2 数据发送和接收

```go
// 位置: internal/services/polling/v2/transport_implementations.go:290-313
func (st *SerialTransport) SendFrameAndWait(ctx context.Context, data []byte, timeout time.Duration) (*dartline.Frame, error) {
	if !st.IsConnected() {
		return nil, fmt.Errorf("not connected")
	}

	// 记录传输层详细信息
	logFields := st.transportLogFormatter.FormatTransportTx(data, st.port, st.address, timeout)
	logFields = append(logFields, zap.String("method", "SendFrameAndWait"))
	st.logger.Debug("[SerialTransport]传输层数据发送", logFields...)

	startTime := time.Now()

	// 清空Frame通道
	for len(st.frameReady) > 0 {
		<-st.frameReady
	}

	// 发送数据
	err := st.serialManager.SendFrame(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("failed to send frame: %w", err)
	}
	// ... 等待响应逻辑
}
```

### 6. DART 协议帧构建

#### 6.1 帧构建器接口

```go
// 位置: internal/services/polling/v2/frame_builder.go:7-12
type FrameBuilder interface {
	BuildPollFrame(address byte) (*dartline.Frame, error)
	BuildDataFrame(address, txNum byte, data []byte) (*dartline.Frame, error)
	ParseResponse(data []byte) (*dartline.Frame, error)
}
```

#### 6.2 DART 协议实现

```go
// 位置: internal/services/polling/v2/frame_builder.go:25-41
// BuildPollFrame 构建轮询帧
// Wayne DART协议：POLL使用纯20H格式，不包含TX#
func (dfb *DefaultFrameBuilder) BuildPollFrame(address byte) (*dartline.Frame, error) {
	return dartline.CreatePollFrame(address, 0)
}

// BuildDataFrame 构建数据帧
// Wayne DART协议：DATA使用30H+TX#格式
func (dfb *DefaultFrameBuilder) BuildDataFrame(address, txNum byte, data []byte) (*dartline.Frame, error) {
	return dartline.CreateDataFrame(address, txNum, data)
}

// ParseResponse 解析响应帧
// 支持所有DART协议帧类型：ACK/NAK/DATA/EOT
func (dfb *DefaultFrameBuilder) ParseResponse(data []byte) (*dartline.Frame, error) {
	return dartline.DecodeFrame(data)
}
```

#### 6.3 DART 协议常量定义

```go
// 位置: internal/adapters/wayne/dartline/frame.go:10-32
const (
	// 协议字符定义
	ETX byte = 0x03 // 文本结束符
	DLE byte = 0x10 // 数据链路转义符
	SF  byte = 0xFA // 停止标志

	// 设备地址范围 - DART协议标准
	MinDARTAddress byte = 0x50 // 最小设备地址 (80 decimal)
	MaxDARTAddress byte = 0x6F // 最大设备地址 (111 decimal)

	// 控制字符定义
	POLL    byte = 0x20 // 轮询命令 (0010 0000)
	DATA    byte = 0x30 // 数据命令 (0011 xxxx) - 低4位为TX#
	ACK     byte = 0xC0 // 应答命令 (1100 xxxx) - 低4位为TX#
	NAK     byte = 0x50 // 否定应答 (0101 xxxx)
	EOT     byte = 0x70 // 传输结束 (0111 xxxx)
	ACKPOLL byte = 0xE0 // 应答+轮询 (1110 xxxx) - 可选

	// 协议限制
	MaxBufferSize int = 256 // 最大缓冲区大小（包含控制字符）
	MaxDataSize   int = 250 // 最大数据大小（预留控制字符空间）
)
```

### 7. 串口调度器 (TimeSliceScheduler)

#### 7.1 访问权限请求

```go
// 位置: internal/services/polling/v2/serial_scheduler.go:205-273
func (ts *TimeSliceScheduler) RequestAccess(ctx context.Context, deviceID string) bool {
	// 如果调度未启用，直接允许访问
	if !ts.config.Enabled {
		return true
	}

	ts.mu.RLock()
	serialPort, exists := ts.devicePort[deviceID]
	if !exists {
		ts.mu.RUnlock()
		ts.logger.Warn("Device not registered for scheduling",
			zap.String("device_id", deviceID))
		return true // 未注册的设备直接允许访问
	}

	group := ts.portGroups[serialPort]
	if group == nil || len(group.Devices) <= 1 {
		ts.mu.RUnlock()
		return true // 单设备直接允许访问
	}

	// 检查是否处于静默期
	if group.InSilencePeriod {
		ts.mu.RUnlock()
		// 静默期内所有设备都不能访问
		ts.logger.Debug("Serial port in silence period, access denied",
			zap.String("device_id", deviceID),
			zap.String("serial_port", serialPort))
	} else {
		// 检查是否是当前活跃设备
		isActive := ts.activeDevices[deviceID]
		ts.mu.RUnlock()

		if isActive {
			ts.logger.Debug("Device marked as in-use",
				zap.String("device_id", deviceID),
				zap.String("serial_port", serialPort))
			return true // 当前活跃设备直接返回
		}
	}

	// 等待轮到该设备 - 50ms检查间隔
	startTime := time.Now()
	timeout := time.Duration(ts.config.MaxWaitTimeoutMs) * time.Millisecond

	for {
		select {
		case <-ctx.Done():
			return false
		case <-time.After(50 * time.Millisecond):
			ts.mu.RLock()
			isActive := ts.activeDevices[deviceID]
			ts.mu.RUnlock()

			if isActive {
				return true
			}

			// 检查超时
			if time.Since(startTime) > timeout {
				ts.logger.Warn("Wait for serial access timeout",
					zap.String("device_id", deviceID),
					zap.Duration("wait_time", time.Since(startTime)))
				return false
			}
		}
	}
}
```

#### 7.2 访问权限释放

```go
// 位置: internal/services/polling/v2/serial_scheduler.go:275-309
func (ts *TimeSliceScheduler) ReleaseAccess(deviceID string) {
	// 如果调度未启用，无需操作
	if !ts.config.Enabled {
		return
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	serialPort, exists := ts.devicePort[deviceID]
	if exists {
		group := ts.portGroups[serialPort]
		if group != nil && group.CurrentDeviceID == deviceID {
			// 显式释放当前设备的访问权限
			delete(ts.activeDevices, deviceID)
			ts.logger.Debug("Device released access explicitly",
				zap.String("device_id", deviceID),
				zap.String("serial_port", serialPort),
				zap.Duration("use_duration", time.Since(group.LastSwitchTime)))

			// 🚀 发送主动释放通知，触发立即切换
			select {
			case ts.releaseChan <- struct{}{}:
				ts.logger.Debug("Active release notification sent",
					zap.String("device_id", deviceID),
					zap.String("serial_port", serialPort))
			default:
				// 通道已满，无所谓，定时器会处理
				ts.logger.Debug("Release notification channel full, using timer fallback",
					zap.String("device_id", deviceID))
			}
		}
	}
}
```

### 8. 串口管理器 (SerialManager)

#### 8.1 串口管理器结构

```go
// 位置: internal/adapters/wayne/connection/serial_manager.go:120-157
type SerialManager struct {
	// 配置信息
	config SerialConfig

	// 连接对象
	port   serial.Port
	mutex  sync.RWMutex
	status ConnectionStatus

	// 统计信息
	stats    SerialStatistics
	statsMux sync.RWMutex

	// 生命周期控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// 日志器
	logger *zap.Logger

	// 数据接收回调
	dataCallback DataReceivedCallback
	callbackMux  sync.RWMutex

	// 序列化回调队列，保证数据顺序且不阻塞接收
	callbackQueue chan []byte
	callbackWg    sync.WaitGroup

	// 持续监听状态
	isListening bool
	listenMux   sync.RWMutex

	// 日志格式化器
	logFormatter *SerialLogFormatter
}
```

#### 8.2 串口配置

```go
// 位置: internal/adapters/wayne/connection/serial_manager.go:46-70
type SerialConfig struct {
	// 基本配置
	Port     string     `json:"port"`     // 串口端口号 (/dev/ttyUSB0, COM1等)
	BaudRate int        `json:"baudrate"` // 波特率 (9600默认, 19200可选)
	DataBits int        `json:"databits"` // 数据位 (DART协议固定8位)
	StopBits int        `json:"stopbits"` // 停止位 (DART协议固定1位)
	Parity   ParityType `json:"parity"`   // 校验位 (DART协议要求奇校验)

	// 超时配置
	Timeout      time.Duration `json:"timeout"`       // 通信超时
	ReadTimeout  time.Duration `json:"read_timeout"`  // 读取超时
	WriteTimeout time.Duration `json:"write_timeout"` // 写入超时

	// 重试配置
	MaxRetries    int           `json:"max_retries"`    // 最大重试次数
	RetryInterval time.Duration `json:"retry_interval"` // 重试间隔

	// 缓冲区配置
	ReadBufferSize  int `json:"read_buffer_size"`  // 读缓冲区大小
	WriteBufferSize int `json:"write_buffer_size"` // 写缓冲区大小

	// 调试配置
	EnableRawDataLogging bool `json:"enable_raw_data_logging"` // 启用原始数据日志记录
}
```

#### 8.3 数据监听机制

```go
// 位置: internal/adapters/wayne/connection/serial_manager.go:440-529
func (sm *SerialManager) dataListenerRoutine() {
	defer sm.wg.Done()

	sm.logger.Info("数据监听goroutine已启动",
		zap.String("串口", sm.config.Port),
		zap.Int("读缓冲区大小", sm.config.ReadBufferSize))

	buffer := make([]byte, sm.config.ReadBufferSize)

	for {
		select {
		case <-sm.ctx.Done():
			sm.logger.Info("数据监听goroutine收到停止信号")
			return
		default:
			// 检查监听状态
			sm.listenMux.RLock()
			isListening := sm.isListening
			sm.listenMux.RUnlock()

			if !isListening {
				sm.logger.Info("监听已停止，数据监听goroutine退出")
				return
			}

			if !sm.IsConnected() {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// 获取端口引用
			sm.mutex.RLock()
			port := sm.port
			sm.mutex.RUnlock()

			if port == nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// 执行读取操作（无超时限制，持续监听）
			n, err := port.Read(buffer)

			if err != nil {
				if err == io.EOF {
					continue
				}
				// 串口读取错误（非严重错误不记录详细信息）
				time.Sleep(50 * time.Millisecond)
				continue
			}

			if n > 0 {
				// ✅ 优化：使用对象池减少内存分配
				receivedData := GetPooledBytes(n)
				copy(receivedData, buffer[:n])

				// 更新对象池统计信息
				sm.safeAddInt64(&sm.stats.PoolAllocations, 1)

				// 更新统计信息 - 使用安全的原子操作
				sm.safeAddInt64(&sm.stats.BytesReceived, int64(n))
				sm.statsMux.Lock()
				sm.stats.LastActivity = time.Now()
				sm.statsMux.Unlock()

				// 数据接收回调 - 使用队列保证顺序且不阻塞
				select {
				case sm.callbackQueue <- receivedData:
					// ✅ 成功加入队列，不阻塞串口接收
					sm.safeAddInt64(&sm.stats.PoolReturns, 1)
				default:
					// ⚠️ 队列满，立即归还对象池并记录警告
					PutPooledBytes(receivedData)
					sm.safeAddInt64(&sm.stats.PoolDropped, 1)
					sm.logger.Error("回调队列已满，丢弃数据",
						zap.String("串口", sm.config.Port),
						zap.Int("丢弃字节数", n),
						zap.String("建议", "检查传输层处理速度"))
				}
			}
		}
	}
}
```

## 核心机制总结

### 1. RS485 半双工通讯保护

**问题**: RS485 是半双工通讯协议，同一时间只能有一个设备发送数据。

**解决方案**:
- **串口级锁**: 每个串口维护独立的 `sync.Mutex`
- **原子操作**: 整个请求-响应周期作为原子操作
- **锁粒度**: 锁的粒度控制在串口级别，不同串口可以并行通讯

```go
// 关键代码路径:
// 1. DispatchTask.getCommunicationLock() - 获取串口锁
// 2. DevicePoller.processPendingCommand() - 使用锁保护通讯周期
// 3. SerialTransport.SendFrameAndWait() - 执行实际通讯
```

### 2. 设备地址管理

**DART 协议地址范围**: 0x50-0x6F (80-111 十进制)

**地址分配策略**:
- 每个设备有唯一的 DART 地址
- 同一串口上的多个设备使用不同地址
- 串口传输层地址设为 0，由协议层处理具体设备地址

### 3. 时间片调度算法

**调度策略**:
- **时间片**: 10ms 固定时间片
- **轮询算法**: Round-Robin 循环调度
- **公平性**: 所有设备获得相等的调度机会

**调度流程**:
1. 每 10ms 触发一次调度
2. 按设备注册顺序循环选择下一个设备
3. 检查设备是否有待发送命令
4. 如有命令，获取串口锁并执行

### 4. 串口资源共享

**共享机制**:
- **物理层共享**: 多个设备共享同一个串口物理连接
- **逻辑层隔离**: 通过设备地址区分不同的逻辑设备
- **实例复用**: 同一串口只创建一个 `SerialTransport` 实例

**资源管理**:
```go
// 关键数据结构:
commInstances map[string]CommunicationInterface // port -> communication
commLocks     map[string]*sync.Mutex            // port -> lock
deviceOrder   []string                          // 设备调度顺序
```

### 5. 错误处理和重试

**错误类型**:
- 串口连接错误
- 通讯超时
- 协议解析错误
- 设备无响应

**重试策略**:
- 串口级别重试 (SerialManager)
- 传输级别重试 (SerialTransport)
- 业务级别重试 (DevicePoller)

### 6. 性能优化

**内存优化**:
- 对象池 (`GetPooledBytes`/`PutPooledBytes`)
- 无锁数据传输 (`io.Pipe`)
- 异步回调处理

**并发优化**:
- 读写分离
- 异步数据处理
- 非阻塞队列

## 架构流程图

```
┌─────────────────┐    10ms时间片    ┌──────────────────┐
│  DispatchTask   │ ──────────────→ │ processNextDevice │
│   (调度器)       │                  │     Queue        │
└─────────────────┘                  └──────────────────┘
         │                                     │
         │ 管理                                │ 轮询
         ▼                                     ▼
┌─────────────────┐                  ┌──────────────────┐
│  DevicePoller   │                  │  DevicePoller    │
│   (设备A)       │                  │   (设备B)        │
└─────────────────┘                  └──────────────────┘
         │                                     │
         │ 获取串口锁                          │ 等待串口锁
         ▼                                     ▼
┌─────────────────────────────────────────────────────────┐
│              SerialTransport                            │
│            (共享串口实例)                                │
└─────────────────────────────────────────────────────────┘
         │
         │ 物理通讯
         ▼
┌─────────────────────────────────────────────────────────┐
│              SerialManager                              │
│            (RS485物理层)                                │
└─────────────────────────────────────────────────────────┘
         │
         │ 硬件接口
         ▼
┌─────────────────────────────────────────────────────────┐
│                RS485 总线                               │
│     设备A(0x50) ←→ 设备B(0x51) ←→ 设备C(0x52)          │
└─────────────────────────────────────────────────────────┘
```

## 关键文件列表

| 文件路径 | 主要功能 | 关键组件 |
|---------|---------|----------|
| `internal/services/polling/v2/dispatch_task.go` | 任务调度器 | DispatchTask, 时间片调度 |
| `internal/services/polling/v2/device_poller.go` | 设备轮询器 | DevicePoller, 命令处理 |
| `internal/services/polling/v2/transport_implementations.go` | 传输层实现 | SerialTransport |
| `internal/services/polling/v2/serial_scheduler.go` | 串口调度器 | TimeSliceScheduler |
| `internal/services/polling/v2/frame_builder.go` | 协议帧构建 | FrameBuilder, DART协议 |
| `internal/adapters/wayne/connection/serial_manager.go` | 串口管理器 | SerialManager, 物理层 |
| `internal/adapters/wayne/dartline/frame.go` | DART协议实现 | Frame, 协议常量 |

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 时间片间隔 | 10ms | 设备轮询调度间隔 |
| 串口波特率 | 9600/19200 | DART协议支持的波特率 |
| 数据位 | 8 | 固定8位数据位 |
| 停止位 | 1 | 固定1位停止位 |
| 校验位 | 奇校验 | DART协议要求奇校验 |
| 设备地址范围 | 0x50-0x6F | 80-111十进制 |
| 读缓冲区大小 | 256字节 | 串口读取缓冲区 |
| 回调队列大小 | 256 | 数据接收回调队列 |

这个架构设计确保了 RS485 半双工通讯的可靠性和效率，同时支持多设备共享串口资源，是一个经过实际验证的工业级通讯解决方案。
```
```
```
