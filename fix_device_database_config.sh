#!/bin/bash

echo "🔧 修复数据库中的设备地址配置"

echo "=== 问题根源 ==="
echo "通过分析发现："
echo "1. pump02设备在配置文件中完全不存在"
echo "2. 数据库中pump01和pump02的device_address配置不正确"
echo "3. 这导致WayneAdapter中地址映射错误"

echo
echo "=== 修复步骤 ===" 

echo "1️⃣ 停止FCC服务"
pkill -f fcc-service 2>/dev/null
sleep 2

echo "2️⃣ 修复数据库配置"

# 设置PostgreSQL连接变量
export PGPASSWORD="fcc123"  # 根据实际密码设置

echo "正在修复设备地址配置..."

# 首先检查是否存在pump02设备，如果不存在则创建
PUMP02_EXISTS=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM devices WHERE id = 'device_com7_pump02';" | tr -d ' ')

if [ "$PUMP02_EXISTS" = "0" ]; then
    echo "pump02设备不存在，正在创建..."
    
    psql -h localhost -U fcc_user -d fcc_db << EOF
-- 创建pump02设备
INSERT INTO devices (
    id, controller_id, name, type, model, manufacturer, device_address, config, 
    station_id, island_id, position, status, health, properties, metadata, 
    created_at, updated_at
) VALUES (
    'device_com7_pump02',
    'controller_com7',
    'COM7 Fuel Pump 02',
    'fuel_pump',
    'Wayne Ovation',
    'Wayne',
    81,  -- DART地址81 (0x51)
    jsonb_build_object(
        'enabled', true,
        'description', 'COM7上的燃油泵设备02',
        'pump_config', jsonb_build_object(
            'nozzle_count', 2,
            'supported_fuels', array['gasoline', 'diesel'],
            'max_flow_rate', 60.0,
            'precision', 3
        )
    ),
    'station_001',
    'island_01', 
    'COM7-Pump-02',
    'offline',
    'unknown',
    jsonb_build_object(
        'protocol', 'DART',
        'address_hex', '0x51',
        'dart_features', array['polling', 'data_transfer', 'command_response']
    ),
    jsonb_build_object(
        'serial_port', 'COM7',
        'dart_address', 81,
        'created_by', 'admin'
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 创建Wayne设备记录
INSERT INTO wayne_devices (device_id, address, status, firmware_version) 
VALUES ('device_com7_pump02', 81, 'offline', 'v2.1.0')
ON CONFLICT (device_id) DO UPDATE SET
    address = EXCLUDED.address,
    updated_at = CURRENT_TIMESTAMP;

-- 初始化设备状态
INSERT INTO device_status (device_id, pump_state, nozzle_state, volume, amount, price) 
VALUES ('device_com7_pump02', 0, 0, 0.000, 0.00, 1.450)
ON CONFLICT DO NOTHING;

-- 初始化泵状态
INSERT INTO pump_states (device_id, state, previous_state, metadata) 
VALUES ('device_com7_pump02', 'PUMP_NOT_PROGRAMMED', NULL, jsonb_build_object('initialized', true, 'com_port', 'COM7'))
ON CONFLICT DO NOTHING;
EOF

    echo "✅ pump02设备创建完成"
else
    echo "pump02设备已存在，正在更新地址..."
fi

# 确保两个设备的地址正确
echo "正在修复设备地址..."

psql -h localhost -U fcc_user -d fcc_db << EOF
-- 修复pump01地址
UPDATE devices SET device_address = 80, metadata = metadata || jsonb_build_object('dart_address', 80, 'address_hex', '0x50') WHERE id = 'device_com7_pump01';
UPDATE wayne_devices SET address = 80 WHERE device_id = 'device_com7_pump01';

-- 修复pump02地址  
UPDATE devices SET device_address = 81, metadata = metadata || jsonb_build_object('dart_address', 81, 'address_hex', '0x51') WHERE id = 'device_com7_pump02';
UPDATE wayne_devices SET address = 81 WHERE device_id = 'device_com7_pump02';
EOF

echo "3️⃣ 验证修复结果"

echo "查询修复后的设备配置："
psql -h localhost -U fcc_user -d fcc_db -c "
SELECT 
    id,
    name,
    device_address,
    metadata->'dart_address' as dart_addr,
    metadata->'address_hex' as addr_hex
FROM devices 
WHERE id IN ('device_com7_pump01', 'device_com7_pump02')
ORDER BY device_address;
"

echo
echo "查询Wayne设备表："
psql -h localhost -U fcc_user -d fcc_db -c "
SELECT device_id, address, status FROM wayne_devices 
WHERE device_id IN ('device_com7_pump01', 'device_com7_pump02')
ORDER BY address;
"

echo
echo "4️⃣ 重启FCC服务"
echo "正在启动FCC服务..."
./fcc-service &
FCC_PID=$!

echo "FCC服务PID: $FCC_PID"
echo "等待服务启动..."
sleep 5

# 检查服务状态
if ps -p $FCC_PID > /dev/null; then
    echo "✅ FCC服务启动成功"
else
    echo "❌ FCC服务启动失败"
    exit 1
fi

echo
echo "5️⃣ 验证修复效果"

echo "等待10秒让服务完全启动..."
sleep 10

LOG_FILE=$(find /home/<USER>"*.log" -type f -exec ls -t {} + | head -1)
echo "监控日志文件: $LOG_FILE"

echo "检查设备地址映射是否正确（监控10秒）..."

timeout 10 tail -f "$LOG_FILE" | while read line; do
    if echo "$line" | grep -q "device_address.*80.*data.*50"; then
        echo "✅ 发现正确的pump01地址映射: device_address=80, data以50开头"
    fi
    
    if echo "$line" | grep -q "device_address.*81.*data.*51"; then
        echo "✅ 发现正确的pump02地址映射: device_address=81, data以51开头"
    fi
    
    if echo "$line" | grep -q "device_address.*80.*data.*51"; then
        echo "❌ 仍然发现错误映射: device_address=80但data以51开头"
    fi
    
    if echo "$line" | grep -q "设备已注册到Wayne适配器"; then
        echo "📋 设备注册: $line" | jq -r '.M' 2>/dev/null || echo "$line"
    fi
done

echo
echo "=== 修复完成 ==="
echo "数据库配置已修复:"
echo "- device_com7_pump01: device_address = 80 (0x50) ✅"
echo "- device_com7_pump02: device_address = 81 (0x51) ✅"
echo
echo "如果仍然出现地址映射错误，问题可能在于代码层面："
echo "1. SharedConnection机制中的设备ID传递"
echo "2. WayneAdapter中设备注册的顺序问题"
echo "3. DeviceTransportWrapper的地址处理逻辑" 