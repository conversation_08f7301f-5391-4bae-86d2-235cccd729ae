#!/bin/bash

echo "=== 硬件连接最终检查 ==="

# 1. 检查设备插入状态
echo "1. USB设备状态:"
lsusb | grep -i serial || lsusb | grep -E "(CH340|CP210|FT232)" || echo "未找到常见串口芯片"
echo ""

# 2. 检查内核消息
echo "2. 最近的USB串口设备消息:"
dmesg | grep -i tty | tail -5
echo ""

# 3. 检查设备真实连接
echo "3. 设备连接测试:"
for port in /dev/ttyUSB{0..3}; do
    if [ -e "$port" ]; then
        echo "测试 $port..."
        # 尝试简单的DTR/RTS信号测试
        python3 -c "
import serial
import time
try:
    ser = serial.Serial('$port', 9600, timeout=1)
    ser.dtr = True
    ser.rts = True  
    time.sleep(0.1)
    ser.dtr = False
    ser.rts = False
    print('  ✅ $port 硬件信号正常')
    ser.close()
except Exception as e:
    print('  ❌ $port 硬件错误:', e)
" 2>/dev/null || echo "  ⚠️ $port 无法测试 (需要python3-serial)"
    fi
done
echo ""

# 4. 给出最终建议
echo "=== 最终建议 ==="
echo "基于检查结果:"
echo ""
echo "如果硬件信号正常但设备无响应，可能是:"
echo "1. 🔌 物理连接问题 - 检查RS485/RS232转换器"
echo "2. ⚡ 设备未上电 - 确认pump设备电源"
echo "3. 📋 协议不匹配 - 确认DART协议版本"
echo "4. 🔧 设备地址错误 - 验证0x50/0x51地址设置"
echo "5. 📡 需要专用驱动 - 某些设备需要厂商驱动"
echo ""
echo "下一步行动:"
echo "1. 联系设备厂商确认通信协议"
echo "2. 使用厂商提供的测试工具验证连接"
echo "3. 检查物理线缆和转换器"
echo "4. 确认设备上电状态和地址配置" 