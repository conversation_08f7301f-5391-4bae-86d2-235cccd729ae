#!/bin/bash

echo "🔍 分析单设备模式 vs SharedConnection模式的串口配置差异"

echo "=== 分析目标 ==="
echo "找出为什么单设备模式通信正常，但SharedConnection模式超时的根本原因"

echo
echo "=== 理论分析 ==="
echo "1. 单设备模式：每个pump有独立的SerialManager"
echo "2. SharedConnection模式：多个pump共享一个SerialTransport"
echo "3. 可能的差异点："
echo "   - 串口配置参数"
echo "   - 设备地址处理"
echo "   - 超时设置"
echo "   - 数据帧格式"
echo "   - 读写模式"

echo
echo "=== 获取最新日志进行分析 ==="

LOG_FILE=$(find /home/<USER>"*.log" -type f -exec ls -t {} + | head -1)
echo "分析日志文件: $LOG_FILE"

echo
echo "1️⃣ 分析串口配置差异"
echo "查找SerialTransport创建配置："
grep -A5 -B5 "Creating.*SerialTransport\|NewSerialTransport" "$LOG_FILE" | tail -15

echo
echo "查找旧模式串口配置（如果有的话）："
grep -A5 -B5 "NewSerialManager\|CreateSerialManager" "$LOG_FILE" | tail -10

echo
echo "2️⃣ 分析设备地址处理差异"
echo "SharedConnection模式的设备地址："
grep "device_address.*8[01]" "$LOG_FILE" | grep -E "shared|wrapper|transport" | tail -5

echo
echo "实际发送的DART帧分析："
grep "frame_data.*50300\|frame_data.*51300" "$LOG_FILE" | tail -5

echo
echo "3️⃣ 分析超时配置差异"
echo "查找超时配置："
grep -E "timeout.*ms|Timeout.*ms" "$LOG_FILE" | grep -v "HTTP" | tail -10

echo
echo "4️⃣ 分析发送时机和频率"
echo "设备调度间隔分析："
grep "Device scheduled for polling" "$LOG_FILE" | tail -5

echo
echo "发送命令时机分析："
grep "Sending.*frame\|SendFrameAndWait" "$LOG_FILE" | tail -5

echo
echo "=== 关键差异点分析 ==="

echo
echo "🔍 检查1: 是否存在端口复用冲突"
echo "查看是否有多个SerialManager同时访问同一端口："
grep -E "Connect.*ttyUSB0|Open.*ttyUSB0" "$LOG_FILE" | tail -5

echo
echo "🔍 检查2: 是否存在设备地址映射错误"
echo "分析设备地址80(0x50)和81(0x51)的帧："
echo "设备80的帧应该以'50'开头："
grep "device_address\":80" "$LOG_FILE" | grep "data.*50" | tail -3
echo "设备81的帧应该以'51'开头："
grep "device_address\":81" "$LOG_FILE" | grep "data.*51" | tail -3

echo
echo "🔍 检查3: 是否存在串口锁定问题"
echo "查找串口忙碌或锁定的迹象："
grep -E "busy|locked|in use|already open" "$LOG_FILE" | tail -5

echo
echo "🔍 检查4: DeviceTransportWrapper的地址处理"
echo "检查wrapper是否正确处理设备地址："
grep "Device wrapper.*device_address" "$LOG_FILE" | tail -5

echo
echo "=== 重要发现总结 ==="

# 检查是否有明显的配置差异
echo
echo "📊 配置对比结果："

# 检查超时设置
timeout_configs=$(grep -E "timeout.*[0-9]+ms" "$LOG_FILE" | grep -v HTTP | sort | uniq -c | sort -nr)
if [ -n "$timeout_configs" ]; then
    echo "超时配置分布:"
    echo "$timeout_configs"
else
    echo "未发现明显的超时配置差异"
fi

echo
echo "📊 设备地址映射验证："
# 验证设备地址映射是否正确
addr80_frames=$(grep "device_address\":80" "$LOG_FILE" | grep -o "data.*50[0-9A-F]*" | wc -l)
addr81_frames=$(grep "device_address\":81" "$LOG_FILE" | grep -o "data.*51[0-9A-F]*" | wc -l)

echo "设备地址80 -> 帧以50开头的数量: $addr80_frames"
echo "设备地址81 -> 帧以51开头的数量: $addr81_frames"

# 检查是否有地址错误的帧
wrong_addr80=$(grep "device_address\":80" "$LOG_FILE" | grep -v "data.*50" | grep "data.*5[1-9A-F]" | wc -l)
wrong_addr81=$(grep "device_address\":81" "$LOG_FILE" | grep -v "data.*51" | grep "data.*5[0,2-9A-F]" | wc -l)

if [ $wrong_addr80 -gt 0 ] || [ $wrong_addr81 -gt 0 ]; then
    echo "❌ 发现地址映射错误:"
    echo "  设备80使用错误地址的帧: $wrong_addr80"
    echo "  设备81使用错误地址的帧: $wrong_addr81"
else
    echo "✅ 设备地址映射正确"
fi

echo
echo "=== 下一步调试建议 ==="
echo "1. 如果发现地址映射错误 -> 修复DeviceTransportWrapper的地址处理"
echo "2. 如果发现端口冲突 -> 检查SharedConnection的串口管理"
echo "3. 如果配置正确但仍超时 -> 需要添加更详细的串口底层调试"
echo "4. 对比单设备模式的成功案例，找出关键配置差异" 