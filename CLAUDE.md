# Wayne Protocol Complete Analysis

## 协议架构详解

### 三层协议结构
- **Level 1 (Electronic Level)**: RS485物理层，包含电气隔离
- **Level 2 (Line Protocol - DART-LINE)**: 数据链路层，负责设备轮询、传输块管理、CRC校验、奇偶校验、块序列号检查
- **Level 3 (Application Level)**: 应用层，泵与站控制器之间的事务处理

### 物理层规格详细说明

#### 数据格式
- **通信方式**: 异步通信
- **波特率**: 9600 (默认) 或 19200 波特
- **数据位**: 1起始位, 8数据位, 1奇偶校验位(奇校验), 1停止位
- **传输方式**: 半双工

#### 物理连接要求
- **标准配线**: 最大300米(使用特殊配线)
- **双线制**: 两线制系统，可选光纤链路
- **电气隔离**: 每个设备可能需要电气隔离

#### 应用数据表
| 波特率 | 配线类型 | 最大距离(米) |
|--------|----------|-------------|
| 19200 | 屏蔽双绞线 | 300 |
| 19200 | 标准配线干燥管道 | 300 |
| 19200 | 标准配线湿润管道 | 50 |
| 9600 | 标准配线湿润管道 | 100 |
| 19200 | 光纤链路 | 300 |
| 19200 | 特殊电缆湿润管道 | 300 |

## 数据链路层详细规格

### 控制字符
- **ETX**: 03H (文本结束)
- **DLE**: 10H (数据链路转义)
- **SF**: FAH (停止标志)

### 地址和控制字段
- **ADR**: 从设备地址 00H-FFH
- **CTRL**: 控制字符和块序列号
- **CRC-1**: CRC-16字的低字节
- **CRC-2**: CRC-16字的高字节

### 控制字符定义
- **POLL**: 20H (轮询)
- **DATA**: 30H-3FH (数据)
- **ACK**: C0H-CFH (确认)
- **NAK**: 50H-5FH (否认)
- **EOT**: 70H-7FH (传输结束)
- **ACKPOLL**: E0H-EFH (确认轮询，可选)

### TX#序列号机制
- **初始化**: 协议重启后TX#初始化为0
- **递增**: 每个成功传输的数据块TX#递增1
- **回卷**: FH后回卷到1
- **用途**: 主站和从站各自维护独立的TX#

### 代码透明性
- **DLE插入**: 当数据字节或CRC值为SF时，发送设备在SF前插入DLE
- **DLE处理**: 接收设备检查SF前是否有DLE，如有则用SF覆盖DLE
- **CRC计算**: 插入的DLE不包含在CRC计算中

### 错误恢复机制
1. **TX# = 上次接收的TX#**: 发送单元未收到我的ACK，跳过数据并应答ACK
2. **TX# = 0**: 发送单元已重启，初始化期望TX#为0，接受数据并发送ACK
3. **TX# ≠ 期望值**: 如果期望TX#=0表示本单元已重启，否则发生错误，用NAK应答

### 时序要求
- **字符间隔**: 必须能够以19200/9600波特率接收字符，字符间无延迟
- **响应时间**: 从站必须在25ms内响应轮询或数据
- **连续传输**: 从站必须能够接收ACK和轮询作为一个连续字节流

## 应用层协议详细规格

### 泵设备地址
- **地址范围**: 50H-6FH (最大32个泵)
- **地址处理**: 由线路协议处理设备地址测试

### 缓冲区大小
- **最大缓冲区**: 256字节(包括控制字符)
- **可变缓冲区**: 不同从设备可有不同缓冲区大小

## 站控制器到泵的完整事务(CD系列)

### CD1 - 泵控制命令
**格式**: TRANS(01H) + LNG(1) + DCC(1)

**命令代码**:
- **00H**: RETURN STATUS (返回状态)
- **02H**: RETURN PUMP PARAMETERS (返回泵参数,*)
- **03H**: RETURN PUMP IDENTITY (返回泵标识)
- **04H**: RETURN FILLING INFORMATION (返回加油信息)
- **05H**: RESET (复位)
- **06H**: AUTHORIZE (授权)
- **08H**: STOP (停止)
- **0AH**: SWITCH OFF (关闭)
- **0DH**: SUSPEND FUELLING POINT (暂停加油点,*)
- **0EH**: RESUME FUELLING POINT (恢复加油点,*)
- **0FH**: RETURN PRICES OF ALL CURRENT GRADES (返回所有当前等级价格,*)

### CD2 - 允许的喷嘴号
**格式**: TRANS(02H) + LNG(1) + NOZ1(1) + ... + NOZn(1)
- **NOZ范围**: 1-0FH
- **用途**: 指定允许加油的逻辑喷嘴号

### CD3 - 预设体积
**格式**: TRANS(03H) + LNG(1) + VOL(4)
- **VOL格式**: 压缩BCD，MSB在前
- **功能**: 泵在加油体积达到VOL时自动停止

### CD4 - 预设金额
**格式**: TRANS(04H) + LNG(1) + AMO(4)
- **AMO格式**: 压缩BCD，MSB在前
- **功能**: 泵在加油金额达到AMO时自动停止

### CD5 - 价格更新
**格式**: TRANS(05H) + LNG(1) + PRI1(3) + ... + PRIn(3)
- **LNG**: 3 × 价格数量
- **PRI格式**: 压缩BCD，MSB在前
- **价格对应**: PRI1对应逻辑喷嘴1，依此类推

### CD7 - 输出命令(*)
**格式**: TRANS(07H) + LNG(1) + NUM(1) + COMD(1)
- **NUM范围**: 0-0FFH (输出功能号)
- **COMD范围**: 0-0FFH (命令)

**输出功能定义**:
- **00H-09H**: 保留
- **0AH**: 主红/绿功能
- **0BH**: 主SB/BT功能
- **0CH**: 背光控制功能
- **0DH**: 对泵语音功能
- **0EH**: 软管泄漏测试功能

**命令定义**:
- **00H**: 关闭
- **01H**: 打开
- **02H-09H**: 保留

### CD8 - 请求总计数器(*)
**格式**: TRANS(08H) + LNG(1) + COUN(1)
- **功能**: 与CD101相同处理

### CD9 - 设置泵参数(*)
**格式**: TRANS(09H) + LNG(1) + RES(22) + DPVOL(1) + DPAMO(1) + DPUNP(1) + RES(5) + MAMO(4) + RES(17)
- **DPVOL**: 体积小数位数(0-8)
- **DPAMO**: 金额小数位数(0-8)
- **DPUNP**: 单价小数位数(0-4)
- **MAMO**: 最大金额(压缩BCD，MSB在前)

### CD10 - 设置加油类型(*)
**格式**: TRANS(0AH) + LNG(1) + FILLTYPE(1)
- **FILLTYPE**: 加油类型代码

### CD14 - 暂停请求(*)
**格式**: TRANS(0EH) + LNG(1) + NOZ(1)
- **NOZ范围**: 0-0FH
- **用途**: 暂停指定逻辑喷嘴的加油

### CD15 - 恢复请求(*)
**格式**: TRANS(0FH) + LNG(1) + NOZ(1)
- **NOZ范围**: 0-0FH
- **用途**: 恢复指定逻辑喷嘴的加油

### CD101 - 请求总计数器(*)
**格式**: TRANS(65H) + LNG(1) + COUN(1)

**计数器类型表**:
| COUN | 01H-08H | 09H | 11H-18H | 19H |
|------|---------|-----|---------|-----|
| 体积 | LN1-8 | ΣLN1-8 | - | - |
| 金额 | - | - | LN1-8 | ΣLN1-8 |
| 加油次数 | - | - | LN1-8 | ΣLN1-8 |

## 泵到站控制器的完整事务(DC系列)

### DC1 - 泵状态
**格式**: TRANS(01H) + LNG(1) + STATUS(1)

**状态码**:
- **0**: PUMP NOT PROGRAMMED (泵未编程)
- **1**: RESET (复位)
- **2**: AUTHORIZED (已授权)
- **4**: FILLING (加油中)
- **5**: FILLING COMPLETED (加油完成)
- **6**: MAX AMOUNT/VOLUME REACHED (达到最大金额/体积)
- **7**: SWITCHED OFF (已关闭)
- **8**: SUSPENDED (已暂停*)

### DC2 - 已加油体积和金额
**格式**: TRANS(02H) + LNG(1) + VOL(4) + AMO(4)
- **VOL**: 已加油体积(压缩BCD，MSB在前)
- **AMO**: 已加油金额(压缩BCD，MSB在前)

### DC3 - 喷嘴状态和加油价格
**格式**: TRANS(03H) + LNG(1) + PRI(3) + NOZIO(1)
- **PRI**: 加油价格(压缩BCD，MSB在前)
- **NOZIO**: 喷嘴信息
  - **位0-3**: 选择的喷嘴号
  - **位4**: 喷嘴进/出信息(0=进，1=出)

**示例**:
- **02H**: 喷嘴2选中，喷嘴在位
- **12H**: 喷嘴2选中，喷嘴拔出
- **10H**: 无喷嘴选中，喷嘴拔出

### DC5 - 报警代码(*)
**格式**: TRANS(05H) + LNG(1) + ALARM(1)

**报警代码**:
- **01H**: CPU复位
- **03H**: RAM错误
- **04H**: PROM校验和错误
- **06H**: 脉冲器错误
- **07H**: 脉冲器电流错误
- **09H**: 紧急停止
- **0AH**: 电源故障
- **0BH**: 压力丢失
- **0CH**: 混合比例错误
- **0DH**: 低泄漏错误
- **0EH**: 高泄漏错误
- **0FH**: 软管泄漏错误
- **10H**: VR监视器，错误复位
- **11H**: VR监视器，10次连续错误
- **12H**: VR监视器，关闭泵
- **13H**: VR监视器，内部错误
- **14H**: 检测到低油罐液位
- **15H**: 低油罐液位正常
- **19H**: VR控制错误
- **20H**: VR控制连续错误

### DC7 - 泵参数(*)
**格式**: TRANS(07H) + LNG(1) + RES(22) + DPVOL(1) + DPAMO(1) + DPUNP(1) + RES(5) + MAMO(4) + RES(2) + GRADE(15)
- **DPVOL**: 体积小数位数(0-8)
- **DPAMO**: 金额小数位数(0-8)
- **DPUNP**: 单价小数位数(0-4)
- **MAMO**: 最大金额(压缩BCD，MSB在前)
- **GRADE**: 每个喷嘴号的现有等级

### DC8 - 泵标识
**格式**: TRANS(08H) + LNG(1) + PID(5)
- **PID**: 泵标识(压缩BCD，MSB在前)

**标识结构**:
- **数字1-4**: 制造商标识(需从Wayne/Dresser获取)
- **数字5-6**: 制造商内的泵类型
- **数字7-10**: 程序版本(制造商自由使用)

### DC14 - 暂停回复(*)
**格式**: TRANS(0EH) + LNG(1) + NOZ(1)
- **NOZ**: 喷嘴号(来自请求)

### DC15 - 恢复回复(*)
**格式**: TRANS(0FH) + LNG(1) + NOZ(1)
- **NOZ**: 喷嘴号(来自请求)

### DC101 - 总计数器(*)
**格式**: TRANS(65H) + LNG(1) + COUN(1) + TOTVAL(5) + TOTM1(5) + TOTM2(5)
- **COUN**: 逻辑喷嘴号01H-09H和11H-19H
- **TOTVAL**: 总计值(压缩BCD格式，MSB在前)
- **TOTM1**: 主计量器总计(压缩BCD格式，MSB在前)
- **TOTM2**: 副计量器总计(压缩BCD格式，MSB在前)

### DC102 - IFSF独立模式(*)
**格式**: TRANS(66H) + LNG(1) + MODE(1) + LOCA(1)
- **MODE**: IFSF模式
  - **00H**: 独立模式禁用
  - **01H**: 独立模式启用
- **LOCA**: 本地授权
  - **00H**: 按钮未被按下
  - **01H**: 按钮被按下

### DC103 - 泵单价(*)
**格式**: TRANS(67H) + LNG(1) + PRI1(3) + ... + PRIn(3)
- **用途**: 在IFSF独立模式下通知转换器当前泵价格

## 泵状态机详细分析

### 泵状态转换表
| 当前状态 | 可接收命令 | 执行动作 | 结果状态 |
|---------|-----------|----------|---------|
| PUMP NOT PROGRAMMED | RETURN STATUS | 返回状态 | 无变化 |
| RESET | AUTHORIZE | 如果等级已选且允许，打开泵电机 | AUTHORIZED |
| AUTHORIZED | SUSPEND | 关闭泵阀 | SUSPENDED |
| FILLING | SUSPEND | 关闭泵阀 | SUSPENDED |
| SUSPENDED | RESUME | 打开泵阀 | 恢复到暂停前状态 |
| FILLING COMPLETED | RESET | 清除金额、体积和报警，打开灯 | RESET |
| MAX AMOUNT/VOLUME REACHED | RESET | 清除金额、体积和报警，打开灯 | RESET |

### 自动状态转换
- **喷嘴拔出**: 可能触发等级选择
- **喷嘴插入**: 可能触发FILLING COMPLETED
- **达到预设值**: FILLING → MAX AMOUNT/VOLUME REACHED
- **加油完成**: FILLING → FILLING COMPLETED

## 业务流程详细示例

### 示例1: 客户付款时清除显示
```
初始状态: FILLING COMPLETED
1. CD1(05H) → 客户已付款，发送RESET命令
2. DC1(1) → 泵状态变为RESET
3. 下一客户到达
4. DC3 → 等级选择
5. CD2 → 允许的喷嘴号(如与上次不同)
6. CD3 → 预设体积(复位命令将预设值设为默认)
7. CD1(06H) → 授权命令
8. DC1(2) → 状态变为AUTHORIZED
9. DC3 → 喷嘴拔出
10. DC1(4) → 状态变为FILLING
11. DC2 → 加油体积和金额
12. DC3 → 喷嘴插入
13. DC1(5) → 状态变为FILLING COMPLETED
```

### 示例2: 加油开始时清除显示
```
初始状态: FILLING COMPLETED
1. 下一客户到达
2. DC3 → 喷嘴拔出
3. CD1(05H) → 泵清除显示，发送RESET命令
4. DC1(1) → 状态变为RESET
5. CD1(06H) → 授权命令
6. DC1(2) → 状态变为AUTHORIZED
7. DC1(4) → 状态变为FILLING
8. DC2 → 加油体积和金额
9. DC3 → 喷嘴插入
10. DC1(5) → 状态变为FILLING COMPLETED
```

### 示例3: 价格变更
```
初始状态: FILLING COMPLETED
1. 下一客户到达
2. DC3 → 喷嘴拔出
3. CD5 → 价格更新
4. CD2 → 允许的喷嘴号
5. CD1(05H) → 复位命令
6. DC1(1) → 状态变为RESET
7. DC3 → 等级选择
8. CD2 → 允许的喷嘴号(如与上次不同)
9. CD3 → 预设体积(复位后必须发送预设值)
10. CD1(06H) → 授权命令
11. DC1(2) → 状态变为AUTHORIZED
12. DC1(4) → 状态变为FILLING
13. DC2 → 加油体积和金额
14. DC3 → 喷嘴插入
15. DC1(5) → 状态变为FILLING COMPLETED
```

## 数据处理详细规格

### 体积和金额处理
- **显示同步**: 站控制器显示必须与泵显示相同值
- **计算原则**: 站控制器不进行计算，直接使用泵发送的体积和金额
- **数据发送**: 仅在值变化时发送，或站控制器请求时发送
- **复位处理**: 复位时发送值为0的事务

### 价格处理
- **填充价格**: 站控制器通过CD5事务发送用于加油的单价
- **价格要求**: 如果泵有多个逻辑喷嘴号，必须为所有喷嘴提供价格
- **价格发送**: 仅在价格变化、泵上电或归零时发送
- **价格拒绝**: 泵在加油开始后不接受新的单价

### 等级和喷嘴号处理
- **逻辑喷嘴**: 泵仅处理喷嘴号，站控制器处理等级-喷嘴号连接
- **混合泵**: 例如一个喷嘴三个等级选择按钮的泵有三个逻辑喷嘴号
- **允许喷嘴**: 通过CD2事务选择允许加油的喷嘴号
- **授权范围**: 授权命令仅授权允许的喷嘴号

### 预设金额/体积处理
- **预设发送**: 站控制器通过CD3或CD4事务发送预设值
- **自动停止**: 泵在达到预设值时停止加油
- **单次使用**: 预设值仅用于一次加油
- **动态更新**: 加油过程中可发送新的预设值
- **优先级**: 泵始终使用最后接收的预设值
- **复位清除**: 复位命令将预设值设回默认值

## 系统限制

### 硬件限制
- **最大泵数**: 32个泵
- **最大体积**: 99999999
- **最大金额**: 99999999
- **最大填充价格**: 999999
- **喷嘴号范围**: 1-0FH
- **混合产品**: 最多6种
- **每泵等级数**: 最多15个

### 协议限制
- **缓冲区大小**: 最大256字节(包括控制字符)
- **传输距离**: 最大300米(取决于配线类型)
- **响应时间**: 25ms内必须响应
- **重传次数**: NAK收到3次后重启通信

### 数据格式限制
- **BCD格式**: 所有数值数据使用压缩BCD格式
- **字节序**: MSB在前(大端序)
- **小数位**: 体积(0-8)，金额(0-8)，单价(0-4)

## 可选功能(标记为*)

### 高级泵管理
- **泵参数管理**: CD9/DC7事务
- **总计数器**: CD101/DC101事务
- **输出控制**: CD7事务
- **报警监控**: DC5事务

### 暂停/恢复功能
- **暂停请求**: CD14事务
- **恢复请求**: CD15事务
- **暂停回复**: DC14事务
- **恢复回复**: DC15事务

### IFSF独立模式
- **独立模式状态**: DC102事务
- **当前价格**: DC103事务
- **本地授权**: 按钮状态监控

## 版本兼容性说明

### 固件版本差异
- **旧版本**: 可能仅支持部分数据/消息
- **标准Dart泵**: 不需要标记*的事务
- **完整Dart实现**: 支持所有事务包括可选功能

### 应用特定消息
- **保留事务**: 在此规格中列为"RESERVED"
- **独立规格**: 在单独的应用规格中描述
- **制造商特定**: 某些功能需要Wayne/Dresser确认

这是Wayne协议的完整详细分析，涵盖了从物理层到应用层的所有细节，包括所有命令、响应、状态机、业务流程和技术规格。