#!/bin/bash

# Wayne命令限流功能测试脚本
# 测试同一nozzle在30秒内只能执行一次preset操作

BASE_URL="http://localhost:8080/api/v2/wayne"
DEVICE_ID="device_001"
NOZZLE_ID="nozzle_001"

echo "=== Wayne命令限流功能测试 ==="
echo "测试场景：同一nozzle在30秒内只能执行一次preset-volume操作"
echo

# 测试数据
PRESET_VOLUME_DATA='{
  "device_id": "'$DEVICE_ID'",
  "nozzle_id": "'$NOZZLE_ID'",
  "command": "preset_volume",
  "volume": 50.0,
  "employee_id": "emp001"
}'

echo "1. 发送第一次preset-volume请求（应该成功）"
echo "请求数据: $PRESET_VOLUME_DATA"
echo

response1=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "$BASE_URL/preset-volume" \
  -H "Content-Type: application/json" \
  -d "$PRESET_VOLUME_DATA")

http_status1=$(echo "$response1" | grep "HTTP_STATUS" | cut -d: -f2)
response_body1=$(echo "$response1" | sed '/HTTP_STATUS/d')

echo "响应状态码: $http_status1"
echo "响应内容: $response_body1"
echo

echo "2. 立即发送第二次preset-volume请求（应该被限流）"
echo "请求数据: $PRESET_VOLUME_DATA"
echo

response2=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "$BASE_URL/preset-volume" \
  -H "Content-Type: application/json" \
  -d "$PRESET_VOLUME_DATA")

http_status2=$(echo "$response2" | grep "HTTP_STATUS" | cut -d: -f2)
response_body2=$(echo "$response2" | sed '/HTTP_STATUS/d')

echo "响应状态码: $http_status2"
echo "响应内容: $response_body2"
echo

# 验证结果
echo "=== 测试结果验证 ==="
if [ "$http_status1" = "200" ] || [ "$http_status1" = "202" ]; then
    echo "✅ 第一次请求成功 (状态码: $http_status1)"
else
    echo "❌ 第一次请求失败 (状态码: $http_status1)"
fi

if [ "$http_status2" = "429" ]; then
    echo "✅ 第二次请求被正确限流 (状态码: $http_status2)"
else
    echo "❌ 第二次请求未被限流 (状态码: $http_status2)"
fi

echo
echo "3. 测试不同nozzle的请求（应该不受限制）"

DIFFERENT_NOZZLE_DATA='{
  "device_id": "'$DEVICE_ID'",
  "nozzle_id": "nozzle_002",
  "command": "preset_volume",
  "volume": 30.0,
  "employee_id": "emp001"
}'

echo "请求数据: $DIFFERENT_NOZZLE_DATA"
echo

response3=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "$BASE_URL/preset-volume" \
  -H "Content-Type: application/json" \
  -d "$DIFFERENT_NOZZLE_DATA")

http_status3=$(echo "$response3" | grep "HTTP_STATUS" | cut -d: -f2)
response_body3=$(echo "$response3" | sed '/HTTP_STATUS/d')

echo "响应状态码: $http_status3"
echo "响应内容: $response_body3"
echo

if [ "$http_status3" = "200" ] || [ "$http_status3" = "202" ]; then
    echo "✅ 不同nozzle请求成功 (状态码: $http_status3)"
else
    echo "❌ 不同nozzle请求失败 (状态码: $http_status3)"
fi

echo
echo "4. 测试preset-amount操作的限流"

PRESET_AMOUNT_DATA='{
  "device_id": "'$DEVICE_ID'",
  "nozzle_id": "'$NOZZLE_ID'",
  "command": "preset_amount",
  "amount": 100.0,
  "employee_id": "emp001"
}'

echo "请求数据: $PRESET_AMOUNT_DATA"
echo

response4=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "$BASE_URL/preset-amount" \
  -H "Content-Type: application/json" \
  -d "$PRESET_AMOUNT_DATA")

http_status4=$(echo "$response4" | grep "HTTP_STATUS" | cut -d: -f2)
response_body4=$(echo "$response4" | sed '/HTTP_STATUS/d')

echo "响应状态码: $http_status4"
echo "响应内容: $response_body4"
echo

# 立即重复preset-amount请求
echo "5. 立即重复preset-amount请求（应该被限流）"

response5=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST "$BASE_URL/preset-amount" \
  -H "Content-Type: application/json" \
  -d "$PRESET_AMOUNT_DATA")

http_status5=$(echo "$response5" | grep "HTTP_STATUS" | cut -d: -f2)
response_body5=$(echo "$response5" | sed '/HTTP_STATUS/d')

echo "响应状态码: $http_status5"
echo "响应内容: $response_body5"
echo

if [ "$http_status5" = "429" ]; then
    echo "✅ preset-amount重复请求被正确限流 (状态码: $http_status5)"
else
    echo "❌ preset-amount重复请求未被限流 (状态码: $http_status5)"
fi

echo
echo "=== 测试总结 ==="
echo "限流规则："
echo "  - preset_volume: 30秒窗口内最多1次"
echo "  - preset_amount: 30秒窗口内最多1次"
echo "  - authorize: 10秒窗口内最多2次"
echo
echo "测试完成。如需测试authorize命令，请手动执行相关请求。" 