# FCC Service Makefile
# 用于管理Go应用程序构建、测试和开发环境

.PHONY: help build test test-unit test-integration test-coverage clean dev lint format install-tools

# 默认目标
help: ## 显示帮助信息
	@echo "FCC Service 开发工具"
	@echo ""
	@echo "可用命令:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 构建相关
build: ## 构建Go应用程序
	@echo "构建FCC Service..."
	go build -o bin/fcc-service main_v2.go

build-linux: ## 构建Linux版本
	@echo "构建Linux版本..."
	GOOS=linux GOARCH=amd64 go build -o bin/fcc-service-linux main_v2.go

build-arm64: ## 构建ARM64版本
	@echo "构建ARM64版本..."
	GOOS=linux GOARCH=arm64 go build -o bin/fcc-service-arm64 main_v2.go

# 测试相关
test: test-unit ## 运行所有测试

test-unit: ## 运行单元测试
	@echo "运行单元测试..."
	go test -v -race -coverprofile=coverage.out ./internal/... ./pkg/...

test-integration: ## 运行集成测试(需要本地数据库)
	@echo "运行集成测试..."
	@echo "注意: 需要本地PostgreSQL和Redis服务运行"
	go test -v -tags=integration -timeout=300s ./tests/integration/...

test-coverage: ## 生成测试覆盖率报告
	@echo "生成测试覆盖率报告..."
	go test -coverprofile=coverage.out ./internal/... ./pkg/...
	go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 开发相关
dev: ## 启动开发模式
	@echo "启动开发模式..."
	go run main_v2.go

dev-watch: ## 启动开发模式(热重载)
	@echo "启动开发模式(热重载)..."
	@echo "注意: 需要先安装air工具: go install github.com/cosmtrek/air@latest"
	air

run: ## 直接运行应用程序
	@echo "运行FCC Service..."
	./bin/fcc-service

run-config: ## 使用指定配置文件运行
	@echo "使用自定义配置运行..."
	./bin/fcc-service -config configs/config.yaml

# 代码质量
lint: ## 运行代码检查
	@echo "运行代码检查..."
	@echo "注意: 需要先安装golangci-lint"
	golangci-lint run

format: ## 格式化代码
	@echo "格式化代码..."
	go fmt ./...
	@if command -v goimports >/dev/null 2>&1; then \
		goimports -w .; \
	else \
		echo "提示: 安装goimports获得更好的格式化: go install golang.org/x/tools/cmd/goimports@latest"; \
	fi

vet: ## 运行go vet检查
	@echo "运行go vet检查..."
	go vet ./...

# 依赖管理
deps: ## 下载依赖
	@echo "下载依赖..."
	go mod download

deps-update: ## 更新依赖
	@echo "更新依赖..."
	go mod tidy

deps-verify: ## 验证依赖
	@echo "验证依赖..."
	go mod verify

# 清理相关
clean: ## 清理构建文件
	@echo "清理构建文件..."
	rm -rf bin/
	rm -f coverage.out coverage.html
	go clean -cache

clean-all: clean ## 清理所有文件

# 工具安装
install-tools: ## 安装开发工具
	@echo "安装开发工具..."
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install golang.org/x/tools/cmd/goimports@latest
	go install github.com/golang/mock/mockgen@latest

# 数据库相关(需要本地PostgreSQL)
setup-db: ## 设置本地数据库
	@echo "设置本地数据库..."
	@echo "请确保PostgreSQL已安装并运行在localhost:5432"
	@echo "创建数据库: createdb -U postgres fcc"
	@echo "创建用户: psql -U postgres -c \"CREATE USER fcc WITH PASSWORD 'fcc_password';\""
	@echo "授权: psql -U postgres -c \"GRANT ALL PRIVILEGES ON DATABASE fcc TO fcc;\""

# 快速开始
quick-start: deps build ## 快速启动开发环境
	@echo "开发环境已就绪!"
	@echo "请确保以下服务已启动:"
	@echo "  - PostgreSQL (localhost:5432)"
	@echo "  - Redis (localhost:6379) [可选]"
	@echo ""
	@echo "运行应用程序: make run"
	@echo "或开发模式: make dev" 