
# 重构规划：适配共享总线架构的DevicePoller (v2)

**版本:** 2.0  
**日期:** 2025年7月3日  
**作者:** Gemini

## 1. 引言与目标

### 1.1. 挑战
现有的 `DevicePoller` 在其架构设计上，隐式地假设了它与单个设备之间拥有一条专用的、低延迟的通信通道。而为了支持在共享物理总线（如RS-485）上运行多个设备而引入的 `BusDispatcher` 模型，从根本上打破了这一假设。

`DevicePoller` 现在将面临：
- **不可预测的通信延迟：** 延迟将从毫秒级增加到潜在的秒级。
- **资源竞争：** 它必须与其他轮询器竞争以获得“通信票证”。
- **实时性保证的丧失：** 发送命令和接收响应之间的紧密耦合关系被切断。

这些挑战使得当前实现的状态管理、事务处理和超时机制变得不再适用，并带来了状态不一致、业务逻辑失败和连锁错误的风险。

### 1.2. 解决方案：统一事件队列架构
为了应对这些挑战，我们将对 `DevicePoller` 进行重构，将其从一个有状态的、基于轮询的实体，改造为一个健壮的、**事件驱动的处理器**。

新架构的核心是一个**统一事件队列**。所有的输入——来自应用的命令、来自设备的数据、以及内部的超时——都被规范化为“事件”，并从一个单一的、带优先级的队列中顺序处理。这一转变将 `DevicePoller` 升级为一个能够在异步和不确定的共享总线环境中保持韧性的代理。

## 2. 核心架构原则

1.  **单一事件队列作为“大脑”：** `DevicePoller` 的主循环将不再由定时器驱动。相反，它将永远地从队列中取出最高优先级的事件并进行处理。这极大地简化了核心逻辑。

2.  **严格的关注点分离：**
    -   **`Receiver` (接收器)：** 一个新的专用组件，将处理所有原始的I/O操作。其唯一职责是监听数据，执行即时的协议层操作（如发送`ACK`帧），并为`DevicePoller`创建待处理的事件并放入队列。
    -   **`DevicePoller` (处理器)：** 其职责被收窄为纯粹的业务逻辑：处理来自队列的事件，并可能因此创建新的事件再放回队列。

3.  **事件驱动的业务逻辑：** 复杂的、多步骤的业务流程（例如 `授权` -> `预设` -> `开始加油`）将不再由复杂的状态函数管理。取而代之，它们将被实现为一个**事件链**。一个事件的成功处理（例如，收到 `DC1: AUTHORIZED`）将触发事件链中下一个事件的创建（例如，发送 `PRESET` 命令）。

4.  **通过显式事件实现韧性：** 错误、超时和恢复操作不再是副作用；它们是系统中的一等公民（`TimeoutEvent`, `NakReceivedEvent`）。这使得错误处理流程清晰、健壮且无阻塞。

## 3. 分阶段实施计划

### 第一阶段：基础结构
*   **位置:** `internal/services/polling/v2/events.go`

1.  **定义 `Event` 结构体:**
    ```go
    type EventType string
    const (
        // 发送的命令
        SendCommandEvent EventType = "SEND_COMMAND"
        // 收到的数据/响应
        ProcessDataEvent EventType = "PROCESS_DATA"
        AckReceivedEvent EventType = "ACK_RECEIVED"
        NakReceivedEvent EventType = "NAK_RECEIVED"
        // 内部系统事件
        TimeoutEvent     EventType = "TIMEOUT"
        RecoveryEvent    EventType = "RECOVERY_ACTION"
    )

    type Event struct {
        Type      EventType
        Priority  int       // 0=严重, 1=状态变更, 2=数据更新, 3=业务逻辑, 4=常规
        Timestamp time.Time
        Payload   interface{}
    }
    ```

2.  **实现 `EventQueue`:**
    -   使用 `container/heap` 创建一个线程安全的、基于优先级的事件队列。它必须支持 `Push(Event)` 和 `Pop() Event` 操作。

### 第二阶段：`Receiver` (接收器) 组件
*   **位置:** `internal/services/polling/v2/receiver.go`

1.  **创建 `Receiver` 结构体:** 它将持有对 `CommunicationInterface` 和 `EventQueue` 的引用。

2.  **实现 `Receiver.Start(ctx)`:** 此方法将启动 `listenLoop` goroutine。

3.  **实现 `listenLoop()`:**
    -   持续调用 `communication.ReceiveFrame()`。
    -   **关键点：如果收到一个 `DATA` 帧，它的首要行动是立即回送一个 `ACK` 帧。** 这满足了DART协议的实时性要求，并且与业务逻辑处理解耦。
    -   然后，它解析 `DATA` 帧的载荷，得到 `DCx` 事务。
    -   为每一个 `DCx` 事务创建一个 `ProcessDataEvent` 并推入 `EventQueue`。
    -   如果收到 `ACK` 或 `NAK`，则将相应的事件推入队列。
    -   如果发生超时，则将 `TimeoutEvent` 推入队列。

### 第三阶段：`DevicePoller` 核心重构
*   **位置:** `internal/services/polling/v2/device_poller.go`

1.  **修改 `DevicePoller` 结构体:**
    -   移除 `commandChan`, `resultChan`。
    -   添加 `eventQueue *EventQueue` 和 `receiver *Receiver`。

2.  **重写 `unifiedPollLoop()`:**
    -   完全移除 `time.Ticker` 及其 `select` 分支。
    -   循环体变为一个简单的 `for { ... }`。
    -   在循环内部，第一个动作是一个阻塞调用：`event := p.eventQueue.Pop()`。
    -   一个 `switch event.Type` 语句将事件委托给特定的处理方法。

### 第四阶段：实现事件处理器
*   **位置:** `internal/services/polling/v2/device_poller.go`

1.  **`handleSendCommand(event)`:**
    -   从事件载荷中获取 `PollCommand`。
    -   从 `deviceSM` 中获取**当前**的 `TX#`。
    -   构建DART帧。
    -   调用 `communication.SendFrame()`。**它在这里不再等待响应。**

2.  **`handleProcessData(event)`:**
    -   这里是处理 `DC1`, `DC2`, `DC3` 等事务的新的 `switch` 语句的所在地。
    -   **它将实现事件链逻辑。** 例如，在处理 `DC1: AUTHORIZED` 的 case 中，当更新完内部状态后，它会创建并 `Push` 一个新的、用于 `PRESET` 命令的 `SendCommandEvent`。

3.  **`handleAck(event)`:**
    -   检查ACK帧中的 `TX#` 是否与最后发送的 `TX#` 匹配。
    -   如果匹配，则在 `deviceSM` 中将 `TX#` 加一。这是**唯一**增加 `TX#` 的地方。

4.  **`handleNak(event)` / `handleTimeout(event)`:**
    -   这些处理器实现恢复逻辑。
    -   它们的主要动作是向队列中 `Push` 一个新的 `RecoveryEvent` 或一个高优先级的 `SendCommandEvent`（包含一个 `RETURN STATUS` 命令），以强制与设备进行状态重新同步。

## 4. 本规划如何解决核心挑战

-   **顺序命令 (`config-nozzle` -> `preset` -> `auth`):** 通过 `handleProcessData` 中的事件链机制优雅地解决。一个步骤的完成会显式地触发下一个步骤，从而保证了顺序。

-   **`TX#` 管理:** 规则不变：“发送时决定，成功后增加”。逻辑现在被清晰地隔离在 `handleSendCommand` (决定) 和 `handleAck` (增加) 中，消除了任何模糊性。

-   **处理无应答:** 这不再是一个阻塞性问题。一个命令被发送后，`DevicePoller` 会立即继续处理其他事件。没有应答的情况最终会被 `Receiver` 的超时机制捕捉到，并生成一个 `TimeoutEvent`。这个事件随后会被 `DevicePoller` 的主循环优雅地处理，并触发恢复操作，整个过程没有任何阻塞。

## 5. 结论

本次重构将 `DevicePoller` 迁移到一个现代的、有韧性的、且高度可测试的事件驱动架构。它通过解耦I/O与逻辑、对事件进行优先级排序、以及将错误和恢复路径作为系统设计的明确部分，系统性地解决了在共享、高延迟通信总线环境下的各种复杂问题。
