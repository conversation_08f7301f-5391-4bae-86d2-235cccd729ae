# TX 序列号管理修复总结

## 🚨 问题描述

### 原始问题：竞态条件和协议违规

之前的 TX 序列号管理存在严重问题：

1. **命令构建时就分配 TX 序列号**：在 `BuildAuthorizeCommand`、`BuildResetCommand` 等方法中，TX 序列号在构建时就被确定
2. **时间差导致重复使用**：命令构建→入队→执行之间的时间差可能导致多个命令使用相同的 TX 序列号
3. **CRC 计算错误**：TX 序列号影响 CRC 计算，但修改时未重新计算 CRC

### 具体影响

- **DART 协议违规**：TX 序列号必须严格递增且不重复
- **设备通信失败**：重复的 TX 序列号可能导致设备拒绝命令
- **CRC 校验失败**：错误的 CRC 可能导致通信失败

## 🔧 修复方案

### 核心原则：延迟分配 (Lazy Allocation)

**TX 序列号应该在实际发送时才分配，而不是在命令构建时。**

### 具体修复

#### 1. 命令构建阶段：使用占位符

```go
// ❌ 修复前：构建时就分配TX序列号
deviceState := p.deviceSM.GetStateData()
cmd, err := p.cdBuilder.BuildAuthorizeCommand(
    p.config.DeviceInfo.ID,
    p.config.DeviceInfo.Address,
    deviceState.TxSequence, // ← 竞态条件！
)

// ✅ 修复后：使用占位符
cmd, err := p.cdBuilder.BuildAuthorizeCommand(
    p.config.DeviceInfo.ID,
    p.config.DeviceInfo.Address,
    0, // ← 占位符，实际执行时分配
)
```

#### 2. 执行阶段：动态分配并重新构建帧

```go
// ✅ 在 executeCommand 中动态分配
func (p *DevicePoller) executeCommand(ctx context.Context, cmd PollCommand, startTime time.Time) PollResult {
    // 获取当前真实的TX序列号
    deviceState := p.deviceSM.GetStateData()
    txNum := deviceState.TxSequence

    // 🔧 关键：重新构建帧以确保CRC正确
    frame, err := p.frameBuilder.BuildDataFrame(p.config.DeviceInfo.Address, txNum, data)
    
    // 发送成功后才更新TX序列号
    if result.Success && txNum != 0 {
        nextTxNum := txNum + 1
        if nextTxNum > 7 {
            nextTxNum = 1
        }
        p.deviceSM.UpdateTxSequence(nextTxNum)
    }
}
```

## 🔍 CRC 计算影响分析

### DART 协议帧结构

```
Address | Command | Data | CRC-Low | CRC-High | ETX | SF
   |        |       |        |        |       |    |
   |        |       |               CRC             |
   +--------+-------+-------------------------------+
            包含在CRC计算中的部分
```

### TX 序列号在 CRC 中的位置

```go
func (f *Frame) CalculateCRC() uint16 {
    var buffer bytes.Buffer
    buffer.WriteByte(f.Address)
    buffer.WriteByte(f.Command)  // ← TX序列号在Command的低4位
    if len(f.Data) > 0 {
        buffer.Write(f.Data)
    }
    return calculateCRC16(buffer.Bytes())
}
```

**关键发现**：TX 序列号直接影响 CRC 计算，因此必须在 TX 序列号确定后重新构建整个帧。

## 📋 修复清单

### 已修复的方法

✅ `executeCommand` - 核心执行逻辑  
✅ `AuthorizeDevice` - 授权命令  
✅ `ResetDevice` - 重置命令  
✅ `PresetVolume` - 预设体积命令  
✅ `PresetAmount` - 预设金额命令  
✅ `RequestCounters` - 计数器查询命令  
✅ `StopDevice` - 停止命令  
✅ `ReturnStatus` - 状态查询命令  
✅ `ReturnFillingInformation` - 加油信息查询命令  
✅ `ConfigureNozzles` - 喷嘴配置命令  
✅ `EnableOneNozzle` - 单喷嘴启用命令  
✅ `UpdatePrices` - 价格更新命令  
✅ `SuspendNozzle` - 暂停喷嘴命令  
✅ `ResumeNozzle` - 恢复喷嘴命令  
✅ `triggerAutoPriceConfiguration` - 自动价格配置  

### 已确认正确的方法

✅ `sendTXStatusQuery` - 直接构建 CD1 数据，不依赖 TX 序列号  
✅ `sendTXResetCommand` - 直接构建 CD1 数据，不依赖 TX 序列号  

## 🧪 验证要点

### 测试场景

1. **并发命令测试**：快速连续发送多个命令，验证 TX 序列号不重复
2. **CRC 验证测试**：确认 CRC 计算正确
3. **协议合规测试**：验证 TX 序列号在 1-7 范围内循环
4. **TX#=0 特殊情况测试**：验证初始化和恢复命令使用 TX#=0

### 监控指标

- TX 序列号递增日志
- CRC 计算成功率
- 设备响应成功率
- 命令执行延迟

## 🎯 协议合规性

### Wayne DART 协议要求

- ✅ TX 序列号范围：1-7 (正常命令)，0 (特殊命令)
- ✅ TX 序列号严格递增，不重复
- ✅ CRC 计算包含完整的 Address + Command + Data
- ✅ 成功发送后才更新 TX 序列号

### 特殊情况处理

- ✅ TX#=0 用于初始化和恢复命令
- ✅ TX#=7 之后循环回到 TX#=1
- ✅ 发送失败时不更新 TX 序列号

## 📈 性能影响

### 轻微增加的开销

- 每次执行时重新构建帧
- 动态获取 TX 序列号

### 显著提升的可靠性

- 消除竞态条件
- 确保协议合规
- 提高设备通信成功率

## 🔮 后续优化建议

1. **命令队列优化**：考虑按业务类型分优先级
2. **TX 序列号池**：为高频命令预分配序列号
3. **CRC 缓存**：对相同数据的 CRC 进行缓存
4. **性能监控**：添加 TX 序列号使用统计

---

**修复完成时间**：[当前时间]  
**影响范围**：所有设备命令执行流程  
**向后兼容性**：完全兼容，仅内部实现优化 