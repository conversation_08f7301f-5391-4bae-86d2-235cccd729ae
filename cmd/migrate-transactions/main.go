package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("FCC Transaction Controller ID Migration Tool")
	fmt.Println("This tool will add the missing controller_id field to the transactions table")

	// 从环境变量或默认值获取数据库配置
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "postgres")
	dbName := getEnv("DB_NAME", "fcc_db")

	// 构建连接字符串
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Jakarta",
		dbHost, dbUser, dbPassword, dbName, dbPort)

	fmt.Printf("📡 Connecting to database: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("❌ Failed to connect to database: %v", err)
	}

	fmt.Println("✅ Database connected successfully")

	// 执行迁移
	if err := migrateTransactions(db); err != nil {
		log.Fatalf("❌ Migration failed: %v", err)
	}

	fmt.Println("🎉 Migration completed successfully!")
}

func migrateTransactions(db *gorm.DB) error {
	// 检查 controller_id 字段是否存在
	hasColumn := db.Migrator().HasColumn(&Transaction{}, "controller_id")
	if hasColumn {
		fmt.Println("✅ controller_id column already exists, skipping migration")
		return nil
	}

	fmt.Println("Adding controller_id column to transactions table...")

	// 添加 controller_id 字段
	if err := db.Migrator().AddColumn(&Transaction{}, "controller_id"); err != nil {
		return fmt.Errorf("failed to add controller_id column: %w", err)
	}

	fmt.Println("✅ controller_id column added successfully")

	// 更新现有数据
	fmt.Println("🔄 Updating existing transactions with controller_id...")

	// 首先从 devices 表更新
	result := db.Exec(`
		UPDATE transactions 
		SET controller_id = d.controller_id
		FROM devices d
		WHERE transactions.device_id = d.id
		AND transactions.controller_id IS NULL
	`)

	if result.Error != nil {
		return fmt.Errorf("failed to update from devices table: %w", result.Error)
	}

	fmt.Printf("✅ Updated %d transactions from devices table\n", result.RowsAffected)

	// 为没有匹配设备的记录设置默认值
	result = db.Exec(`
		UPDATE transactions 
		SET controller_id = CASE 
			WHEN device_id LIKE '%com7%' THEN 'controller_com7'
			WHEN device_id LIKE '%com8%' THEN 'controller_com8'
			WHEN device_id LIKE '%wayne%' THEN 'controller_wayne_dart_001'
			ELSE 'controller_unknown'
		END
		WHERE controller_id IS NULL OR controller_id = ''
	`)

	if result.Error != nil {
		return fmt.Errorf("failed to set default controller_id: %w", result.Error)
	}

	fmt.Printf("✅ Set default controller_id for %d transactions\n", result.RowsAffected)

	// 检查是否还有空的 controller_id
	var countNull int64
	db.Model(&Transaction{}).Where("controller_id IS NULL OR controller_id = ''").Count(&countNull)
	if countNull > 0 {
		return fmt.Errorf("still have %d transactions with null controller_id", countNull)
	}

	// 添加 NOT NULL 约束
	fmt.Println("Adding NOT NULL constraint...")
	if err := db.Exec("ALTER TABLE transactions ALTER COLUMN controller_id SET NOT NULL").Error; err != nil {
		return fmt.Errorf("failed to add NOT NULL constraint: %w", err)
	}

	// 添加索引
	fmt.Println("Adding index on controller_id...")
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_transactions_controller_id ON transactions(controller_id)").Error; err != nil {
		return fmt.Errorf("failed to add index: %w", err)
	}

	// 验证结果
	var total, withController int64
	db.Model(&Transaction{}).Count(&total)
	db.Model(&Transaction{}).Where("controller_id IS NOT NULL AND controller_id != ''").Count(&withController)

	fmt.Printf("📊 Migration verification:\n")
	fmt.Printf("   Total transactions: %d\n", total)
	fmt.Printf("   Transactions with controller_id: %d\n", withController)

	if total == withController {
		fmt.Println("✅ All transactions have controller_id assigned")
	} else {
		return fmt.Errorf("⚠️ %d transactions still missing controller_id", total-withController)
	}

	return nil
}

// Transaction 简化的交易模型（仅用于迁移）
type Transaction struct {
	ID           string `gorm:"primaryKey;type:varchar(255)"`
	DeviceID     string `gorm:"type:varchar(255);not null"`
	ControllerID string `gorm:"type:varchar(255)"`

	// 泵码数据字段（为了保持一致性，虽然迁移工具可能不会直接使用）
	StartPumpVolumeReading *float64 `gorm:"type:decimal(15,3)"`
	EndPumpVolumeReading   *float64 `gorm:"type:decimal(15,3)"`
	StartPumpAmountReading *float64 `gorm:"type:decimal(15,2)"`
	EndPumpAmountReading   *float64 `gorm:"type:decimal(15,2)"`
	PumpReadingSource      string   `gorm:"type:varchar(50);default:DC101"`
	PumpReadingQuality     string   `gorm:"type:varchar(20);default:good"`
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
