
# 重构规划 v3：适配共享总线架构的DevicePoller (调度器-执行器模型)

**版本:** 3.0  
**日期:** 2025年7月3日  
**作者:** Gemini

## 1. 背景与修正

在先前的 v2 规划中，我们确立了“统一事件队列”的核心思想，但经过深入审查，发现其中存在两个关键的设计缺陷：

1.  **ACK 抢占问题:** v2 方案提议由 `Receiver` 在收到 `DATA` 帧后立即回复 `ACK`。这违反了共享总线的设计原则，因为 `ACK` 本身也是一次“写”操作，若不经由 `BusDispatcher` 仲裁，会造成总线冲突。
2.  **通信时机问题:** v2 方案暗示了 `DevicePoller` 会利用每一次获得的通信机会。而一个更合理的设计应该是，`DevicePoller` 拥有自己的内部逻辑来判断何时需要通信，通信票证仅仅是执行通信的“许可”，而非“命令”。

本 v3 规划旨在修正这些缺陷，提出一个更健壮、更精确的**“调度器-执行器 (Scheduler-Executor)”**模型。

## 2. 核心架构：调度器-执行器模型

此模型将 `DevicePoller` 的职责一分为二：

-   **调度器 (Scheduler):** 作为 `DevicePoller` 的“大脑”，它负责决策**“需要做什么”**。它响应内部定时器（用于常规轮询）和外部API调用，将待办任务（如发送命令、回复ACK）作为“事件”推入一个统一的优先级队列。它自身不产生任何I/O操作。

-   **执行器 (Executor):** 作为 `DevicePoller` 的“手臂”，它是完全由 `BusDispatcher` 的**通信票证**驱动的主循环。它的唯一职责是：等待并获取票证，然后从事件队列中取出当前最高优先级的任务并执行它（即，向总线发送数据帧）。如果获得票证时队列为空，它将放弃此次机会。

## 3. 修正后的组件职责与数据流

### 3.1. 组件职责

-   **`Receiver` (纯粹的监听者):**
    -   **只读:** 它的唯一职责是监听总线，从 `CommunicationInterface` 接收数据帧。
    -   **事件化:** 收到任何帧 (`DATA`, `ACK`, `NAK`) 后，它会解析出应用层事务，并创建相应的事件 (`ProcessDataEvent`, `AckReceivedEvent` 等) 推入事件队列。
    -   **禁止写入:** 它绝不向总线写入任何数据，包括 `ACK`。

-   **`DevicePoller` (调度器-执行器):**
    -   **调度能力:**
        1.  **内部定时器 (`routinePollTicker`):** 用于常规轮询。当定时器触发，它会向事件队列推入一个**低优先级**的 `SendCommandEvent(POLL)`，以表达轮询的“意图”。
        2.  **响应外部调用:** 接收到上层业务请求时，创建并推入相应优先级的 `SendCommandEvent`。
    -   **执行能力 (主循环 `unifiedPollLoop`):**
        1.  **票证驱动:** 其循环的唯一触发条件是成功从 `BusDispatcher` 的 `ticketChan` 中获取到票证。
        2.  **按需执行:** 获取票证后，检查事件队列。如果队列不为空，则取出最高优先级的事件并执行；如果队列为空，则放弃此次通信机会。

### 3.2. 关键场景流程

**场景一：处理设备主动上报的 `DATA` 帧 (修正后的ACK流程)**

1.  `Receiver` 收到 `DATA` 帧。
2.  `Receiver` 解析出 `DCx` 事务，创建一个高优先级的 `ProcessDataEvent` 并推入事件队列。
3.  `DevicePoller` 的执行器在其主循环中获得一个通信票证。
4.  假设此时 `ProcessDataEvent` 是最高优先级的事件，执行器将其 `Pop()` 出来。
5.  `handleProcessData` 方法被调用。在处理完所有业务逻辑后，它**必须**创建一个**最高优先级的 `SendCommandEvent(ACK)`**，并将其推回事件队列。
6.  执行器在下一次（极有可能是紧接着的下一次）获得票证时，会发现这个 `ACK` 事件是最高优先级的，于是执行它，向总线发送 `ACK` 帧。

*   **结论：此流程确保了 `ACK` 的发送遵循了共享总线的仲裁机制，解决了抢占问题。**

**场景二：执行一次常规轮询 (修正后的时机判断)**

1.  `DevicePoller` 内部的 `routinePollTicker` 触发，表达了轮询的“意图”，一个低优先级的 `SendCommandEvent(POLL)` 被推入队列。
2.  `DevicePoller` 的执行器主循环获得一个通信票证。
3.  执行器检查事件队列：
    -   **情况A (总线空闲):** 如果队列中没有其他更高优先级的事件（如待办的 `ACK` 或业务命令），这个 `POLL` 事件将被取出并执行。
    -   **情况B (总线繁忙):** 如果队列中有更高优先级的事件，执行器会先处理它们。`POLL` 事件将继续在队列中等待，直到它成为最高优先级的事件并且 `DevicePoller` 再次获得票证。

*   **结论：此流程将“轮询时机”的决策权交还给了 `DevicePoller` 自身，票证只作为执行的先决条件，解决了通信时机的问题。**

## 4. 实施要点

-   **事件队列的优先级设计** 至关重要。`ACK` 命令的事件必须拥有最高的优先级，以确保对设备请求的及时响应。其次是状态恢复命令，然后是业务命令，最后是常规轮询。
-   **主循环 `unifiedPollLoop` 的逻辑** 需要重写，以反映由 `ticketChan` 驱动的执行模型。
-   **`handleProcessData` 方法** 必须增加在处理完成后创建并推入 `ACK` 事件的逻辑。

这份 v3 规划文档基于对共享总线通信模型的更深刻理解，为 `DevicePoller` 的重构提供了正确且健壮的指导方案。
