
# 共享总线轮询架构升级规划方案 (Polling Bus Refactor Plan)

**版本**: 1.0
**日期**: 2025年7月3日

## 1. 核心思路与设计哲学

### 1.1. 背景与痛点

当前的轮询架构在为每个设备创建独立的通信实例，这在物理总线（如RS-485）被多个设备共享的场景下，会因资源独占而导致冲突，使得只有一个设备能正常工作。直接在共享资源上加锁的简单方案，则会引发严重的队头阻塞、性能瓶颈和优先级倒置等问题，无法满足工业场景对高可靠性和实时性的要求。

### 1.2. 核心设计思想：解耦、异步与自主决策

本次架构升级的核心目标，是解决共享总线的资源竞争与调度效率问题。我们将采用**“基于通信票证的异步调度”**模型，其设计哲学包含以下三点：

1.  **通信执行与业务决策的彻底解耦**: 物理I/O操作将被集中到单一的、专用的“总线工作者”中，它不关心任何业务逻辑。而业务决策（何时通信、通信内容、优先级判断）的权力则完全下放到各个设备的`DevicePoller`中。

2.  **从“推”到“拉”的调度模式**: 废除由中央调度器决定轮询顺序的“推”模式。取而代之的是，总线以高频率提供“通信机会”（即票证），由各个`DevicePoller`根据自身需求主动“拉”取，实现对总线资源的竞争性使用。

3.  **状态与错误的隔离**: 单个设备的通信缓慢、超时或失败，不应阻塞或影响总线上其他健康设备的正常运行。架构必须具备自动隔离故障点和在恢复后自动接纳的能力。

### 1.3. 预期目标

*   **功能目标**: 完美支持多设备共享同一物理通信端口（如串口）的场景。
*   **性能目标**: 实现低延迟的命令响应。高优先级业务（如紧急停止）能够“插队”并被立即处理。总线利用率最大化，系统吞吐量显著提升。
*   **可靠性目标**: 架构具备故障隔离和自愈能力，单个设备的异常不会导致整个总线瘫痪。
*   **可维护性目标**: 组件职责清晰，代码逻辑简化，易于理解、扩展和问题排查。

---

## 2. 架构组件与改造规划

### 2.1. 新增核心组件：`BusDispatcher` (总线调度器)

*   **定位**: 每个物理端口（如`COM3`）的唯一管理者和I/O执行者。
*   **内部结构**:
    *   **票证发行器 (`Ticket Issuer`)**: 一个高频`time.Ticker`，周期性地向一个公共的、缓冲大小为1的`ticketChan`中发行“通信票证”。
    *   **请求接收通道 (`requestChan`)**: 一个私有的、无缓冲的通道，用于接收来自`DevicePoller`的最终通信请求。
    *   **I/O工作者 (`IO Worker`)**: 一个专用的goroutine，是唯一被允许执行物理端口读写操作的地方。它循环地从`requestChan`等待请求，执行后通过请求自带的回调通道返回结果。
*   **改造范围**: 这是一个全新的组件，需要完整创建。

### 2.2. `DevicePoller` 的职责重塑

*   **定位**: 从一个独立的轮询单元，转变为一个自主的“业务决策单元”。
*   **改造范围**:
    *   **移除轮询循环**: 移除内部的`time.Ticker`和`unifiedPollLoop`。其主循环将改造为监听`ticketChan`和内部命令队列。
    *   **新增内部命令队列**: 在`DevicePoller`内部实现一个**优先级队列**，用于管理自身的待办事项（高优命令、常规轮询任务等）。
    *   **修改通信逻辑**: `sendFrameAndWait`等方法不再直接执行I/O，而是转变为：
        1.  构建`CommunicationRequest`对象。
        2.  提交给`BusDispatcher`的`requestChan`。
        3.  阻塞等待`BusDispatcher`通过回调通道返回结果。
    *   **新增决策逻辑**: 在抢到“票证”后，需要实现一套决策逻辑，用于判断是否要发起通信，以及发起何种通信。

### 2.3. `DispatchTask` 的适配性改造

*   **定位**: 仍然是系统的总控制器，但需要适配新的通信管理模式。
*   **改造范围**:
    *   **引入`BusDispatcher`管理器**: `DispatchTask`需要维护一个`BusDispatcher`的池（`map[string]*BusDispatcher`），确保每个物理端口只有一个调度器实例。
    *   **修改设备注册流程**: `RegisterDevice`方法不再为`DevicePoller`创建独立的`CommunicationInterface`，而是根据设备的端口配置，为其分配对应的`BusDispatcher`实例的引用。
    *   **适配生命周期管理**: `Start`和`Stop`流程需要确保能正确地启动和优雅地关闭所有的`BusDispatcher`实例。

### 2.4. 错误与超时处理机制的细化

*   **目标**: 建立分层的、职责清晰的错误与超时处理机制。
*   **改造范围**:
    *   **`BusDispatcher`层面**: 实现对单次物理I/O的超时控制 (`CommunicationTimeout`)。
    *   **`DevicePoller`层面**: 实现对跨多步操作的业务流程超时控制 (`BusinessFlowTimeout`)。
    *   **`BusDispatcher`层面**: 实现基于连续失败次数的故障设备自动隔离与恢复心跳机制。

### 2.5. 动态配置与生命周期管理

*   **目标**: 支持服务的平滑变更与稳定启停。
*   **改造范围**:
    *   为`BusDispatcher`和`DispatchTask`提供线程安全的接口，以支持在运行时动态增删设备。
    *   确保`context.Context`能够贯穿整个调用链，从`DispatchTask`的`Stop`方法一直传递到`BusDispatcher`的I/O工作者，实现无资源泄露的优雅关闭。

---

## 3. 实施计划与验证

1.  **阶段一：核心组件开发 (T+0 - T+3天)**
    *   开发`BusDispatcher`组件，包含票证发行、请求队列和I/O工作者。
    *   开发`CommunicationRequest`及`Result`数据结构。

2.  **阶段二：`DevicePoller`改造 (T+2 - T+5天)**
    *   移除`DevicePoller`的轮询逻辑。
    *   引入内部优先级队列，并实现新的“抢票-决策-执行”主循环。

3.  **阶段三：顶层适配与集成 (T+4 - T+6天)**
    *   改造`DispatchTask`以管理`BusDispatcher`池。
    *   调整设备注册与生命周期管理逻辑。

4.  **阶段四：测试与验证 (T+6 - T+8天)**
    *   **单元测试**: 对`BusDispatcher`的调度逻辑、`DevicePoller`的决策逻辑进行单元测试。
    *   **集成测试**: 搭建模拟环境，使用虚拟串口（如`socat`）模拟一个共享总线，挂载多个虚拟设备，验证多设备并发通信、高优先级插队、故障隔离与恢复等核心功能。
    *   **压力测试**: 持续增加虚拟设备数量和命令请求频率，监控系统CPU、内存占用及通信延迟，确保无性能瓶颈和资源泄露。
