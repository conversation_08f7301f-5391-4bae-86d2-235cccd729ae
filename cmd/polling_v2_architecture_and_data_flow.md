
# Polling Service (v2) 架构与数据链路分析

## 1. 架构概述

`polling/v2` 服务旨在提供一个健壮、可扩展且易于维护的轮询框架，用于与加油机等现场设备进行实时通信和业务处理。

本次更新后的核心设计思想是 **集中化的状态管理** 和 **延迟化的事务创建**。系统的所有状态（如油枪的空闲、授权、加油中、完成等）的唯一真实来源 (Single Source of Truth) 是数据库，并通过 `nozzle.Service` 进行统一访问。内存中的状态仅作为临时缓存或过程数据存在。

这种设计将复杂的业务状态同步逻辑从轮询器 (`DevicePoller`) 和事务组装器 (`TransactionAssembler`) 中剥离，极大地降低了组件间的耦合度，并从根本上解决了分布式状态可能引发的一致性问题。

## 2. 核心组件职责

系统主要由以下几个核心组件构成：

*   **`DispatchTask` (调度任务)**
    *   **职责**: 整个轮询系统的总控制器和入口。
    *   管理所有 `DevicePoller` 实例的生命周期（注册、启动、停止）。
    *   从上层应用接收命令，并将其分发到指定的 `DevicePoller`。
    *   汇集所有 `DevicePoller` 的轮询结果，并分发给结果处理器。
    *   管理看门狗 (`Watchdog`)，监控设备健康状况并在超时后触发恢复机制。

*   **`DevicePoller` (设备轮询器)**
    *   **职责**: 与 **单个** 物理设备进行通信的直接代理。
    *   维护一个独立的轮询循环，以固定频率与设备进行交互。
    *   **主要任务是通信**: 将上层业务命令（如授权、预设）封装成 DART 协议帧并发送给设备；接收设备的响应帧。
    *   **状态更新的发起者**: 在收到设备数据后，**直接调用 `nozzle.Service`** 来更新数据库中的状态，而不是在本地维护复杂的状态机。
    *   将从设备收到的原始业务数据 (`DC` 事务) 传递给 `TransactionAssembler` 进行处理。
    *   实现交易回调（`onTransactionComplete` 等），在收到 `TransactionAssembler` 的完成通知后，调用 `transactionService` 将最终的完整交易数据持久化。

*   **`TransactionAssembler` (交易组装器)**
    *   **职责**: 核心的交易数据处理单元，负责将零散的设备原始数据 (`DC` 事务) 组装成一笔完整的、准确的交易。
    *   **延迟化事务创建**: 在交易过程中，它仅在内存中维护一个轻量级的 `PendingTransactionData` 缓存，用于记录临时的体积、金额、单价等信息。
    *   **原子化交易生成**: 只有在交易确认完成的时刻（例如，油枪插回），它才会结合 `PendingTransactionData` 和最终获取的 DC101 泵码数据，**创建** 一个完整的 `Transaction` 对象。
    *   **鲁棒性设计**: 包含交易完成锁 (`completionLocks`) 和标志位 (`completionFlags`)，以防止因设备信号延迟或重复导致的交易重复创建问题。

*   **`nozzle.Service` (油枪服务)**
    *   **职责**: 数据库状态的唯一管理者，是系统的“状态真相源”。
    *   提供了一系列直接的、原子化的数据库操作接口（如 `UpdateNozzleStatusDirect`, `UpdateNozzleDataDirect`）。
    *   `DevicePoller` 通过调用这些接口来更新油枪的状态、实时数据等，确保所有状态变更都直接反映在数据库中。

*   **`transaction.Service` (交易服务)**
    *   **职责**: 负责将 `TransactionAssembler` 构建完成的最终 `Transaction` 对象持久化到数据库中。

## 3. 核心数据链路 (以一次加油授权到完成为例)

以下是系统在工作状态下，处理一笔完整加油交易的数据链路：

1.  **授权命令下发**:
    *   外部请求（如API调用）通过 `DispatchTask.SendCommand` 将一个“授权”命令发送给目标设备的 `DevicePoller`。
    *   `DevicePoller` 收到命令后，调用 `AuthorizeDevice` 方法。

2.  **命令执行与即时状态更新**:
    *   `DevicePoller` 构建一个 DART 协议的授权命令帧 (CD1)，并通过 `communication` 接口发送给物理设备。
    *   **关键变更**: 命令发送后，`DevicePoller` **立即调用 `nozzleService.UpdateAllNozzlesStatusDirect`**，将数据库中该设备的所有油枪状态更新为 `Authorized`。这为上层应用提供了快速的状态反馈。

3.  **加油开始 (物理事件)**:
    *   用户拿起油枪，开始加油。
    *   设备开始上报 DC3 (油枪拔出、价格) 和 DC2 (实时体积、金额) 事务。

4.  **临时数据缓存**:
    *   `DevicePoller` 在其轮询循环中接收到这些 `DC` 事务数据。
    *   它将这些原始数据传递给 `TransactionAssembler` 的 `ProcessDC2Transaction` 和 `ProcessDC3Transaction` 方法。
    *   `TransactionAssembler` **不会立即创建 `Transaction` 对象**。相反，它会为对应的油枪创建或更新一个 `PendingTransactionData` 记录，在内存中缓存实时的体积、金额和单价。

5.  **加油结束 (物理事件)**:
    *   用户加完油，将油枪插回。
    *   设备上报一个 DC3 事务，其中包含“油枪已插入”的状态。

6.  **交易完成流程触发**:
    *   `DevicePoller` 收到该 DC3 事务，并调用 `TransactionAssembler.ProcessDC3Transaction`。
    *   `TransactionAssembler` 检测到“油枪已插入”事件，并且 `PendingTransactionData` 中有有效的加油数据，于是触发核心的 `CompleteTransactionWithDC101Wait` 方法，正式开始交易的最终完成流程。

7.  **获取最终泵码**:
    *   `CompleteTransactionWithDC101Wait` 方法通过回调 (`requestCountersFunc`) 请求 `DevicePoller` 向设备发送 DC101 命令，以获取最准确的结束泵码。
    *   同时，它使用 `dc101Semaphore` 阻塞等待，直到收到 DC101 的响应或超时。

8.  **最终交易对象生成**:
    *   一旦收到 DC101 的泵码数据，`TransactionAssembler` 会执行 `completeTransactionWithFreshPumpReadings` 方法。
    *   **此刻，它才会创建一个完整的 `Transaction` 对象**，并使用 `PendingTransactionData` 中的数据（开始时间、操作员ID、体积、金额等）和刚刚获取的泵码数据来填充这个对象。

9.  **数据持久化**:
    *   `TransactionAssembler` 调用 `onTransactionComplete` 回调，将这个新创建的、完整的 `Transaction` 对象通知给 `DevicePoller`。
    *   `DevicePoller` 在回调中接收到该对象，并立即调用 `transactionService.PersistDARTTransaction`，将其完整地存入数据库。交易流程结束。

## 4. 关键设计与优化

*   **集中化状态管理**:
    *   **设计**: 放弃了在轮询器中维护内存状态副本的模式，将数据库作为状态的唯一真实来源。
    *   **优势**: 彻底解决了多组件、多线程环境下状态同步的复杂性和不一致性问题。简化了轮询器的代码，使其更专注于通信。

*   **延迟化与原子化交易创建**:
    *   **设计**: 引入 `PendingTransactionData` 缓存，将交易的创建推迟到完成的最后一刻。
    *   **优势**: 避免了因用户误操作（如提枪但未加油）而在数据库中产生大量无效和未完成的交易记录。保证了每一条入库的交易都是完整的、数据质量更高的。

*   **交易完成的鲁棒性防护**:
    *   **设计**: 使用 `completionLocks` (冷却锁) 和 `completionFlags` (完成标志) 机制。
    *   **优势**: 有效地防止了因设备状态上报的时序问题或信号重复，而导致的同一笔加油被多次处理、创建重复交易的严重问题。

*   **清晰的职责划分**:
    *   **设计**: `DispatchTask` 负责宏观调度，`DevicePoller` 负责物理通信，`TransactionAssembler` 负责数据规整与交易构建，`nozzle/transaction Service` 负责数据持久化。
    *   **优势**: 实现了高度的“关注点分离”，每个组件职责单一，使得代码更易于理解、测试和维护。

## 5. 总结

更新后的 `polling/v2` 架构是一个高度可靠、状态明确、职责清晰的实时数据处理系统。它通过将状态管理集中化和将交易创建延迟化的核心设计，有效地解决了传统轮询系统中的常见痛点，为业务的稳定运行提供了坚实的技术保障。
