package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("🗑️  FCC Database Reset Tool")
	fmt.Println("This tool will completely reset the database and recreate all tables")

	// 从环境变量或默认值获取数据库配置
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5433")
	dbUser := getEnv("DB_USER", "fcc")
	dbPassword := getEnv("DB_PASSWORD", "fcc_password")
	dbName := getEnv("DB_NAME", "fcc")

	// 构建连接字符串
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Jakarta",
		dbHost, dbUser, dbPassword, dbName, dbPort)

	fmt.Printf("📡 Connecting to database: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("❌ Failed to connect to database: %v", err)
	}

	fmt.Println("✅ Database connected successfully")

	// 确认重置操作
	fmt.Println("⚠️  WARNING: This will delete ALL existing data!")
	fmt.Print("   Type 'YES' to confirm database reset: ")

	var confirmation string
	fmt.Scanln(&confirmation)

	if confirmation != "YES" {
		fmt.Println("❌ Database reset cancelled")
		return
	}

	// 执行数据库重置
	if err := resetDatabase(db); err != nil {
		log.Fatalf("❌ Database reset failed: %v", err)
	}

	// 重新初始化数据库
	if err := initializeDatabase(db); err != nil {
		log.Fatalf("❌ Database initialization failed: %v", err)
	}

	fmt.Println("🎉 Database reset and initialization completed successfully!")
}

func resetDatabase(db *gorm.DB) error {
	fmt.Println("🗑️  Resetting database...")

	// 读取重置脚本
	resetScript, err := os.ReadFile("scripts/reset-database.sql")
	if err != nil {
		return fmt.Errorf("failed to read reset script: %w", err)
	}

	// 执行重置脚本
	if err := db.Exec(string(resetScript)).Error; err != nil {
		return fmt.Errorf("failed to execute reset script: %w", err)
	}

	fmt.Println("✅ Database reset completed")
	return nil
}

func initializeDatabase(db *gorm.DB) error {
	fmt.Println("Initializing database with latest schema...")

	// 读取初始化脚本
	initScript, err := os.ReadFile("scripts/init-fcc-v2-unified.sql")
	if err != nil {
		return fmt.Errorf("failed to read init script: %w", err)
	}

	// 执行初始化脚本
	if err := db.Exec(string(initScript)).Error; err != nil {
		return fmt.Errorf("failed to execute init script: %w", err)
	}

	fmt.Println("✅ Database initialization completed")

	// 验证表结构
	if err := verifyTableStructure(db); err != nil {
		return fmt.Errorf("table structure verification failed: %w", err)
	}

	return nil
}

func verifyTableStructure(db *gorm.DB) error {
	fmt.Println("Verifying table structure...")

	// 检查关键表是否存在
	tables := []string{"controllers", "devices", "fuel_grades", "nozzles", "transactions", "commands"}

	for _, table := range tables {
		var exists bool
		err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", table).Scan(&exists).Error
		if err != nil {
			return fmt.Errorf("failed to check table %s: %w", table, err)
		}

		if !exists {
			return fmt.Errorf("table %s does not exist", table)
		}

		fmt.Printf("   ✅ Table '%s' exists\n", table)
	}

	// 检查 transactions 表的关键字段
	transactionFields := []string{"id", "device_id", "controller_id", "nozzle_id", "extra_data"}

	for _, field := range transactionFields {
		var exists bool
		err := db.Raw(`
			SELECT EXISTS (
				SELECT FROM information_schema.columns 
				WHERE table_name = 'transactions' AND column_name = ?
			)`, field).Scan(&exists).Error

		if err != nil {
			return fmt.Errorf("failed to check field %s: %w", field, err)
		}

		if !exists {
			return fmt.Errorf("field transactions.%s does not exist", field)
		}

		fmt.Printf("   ✅ Field 'transactions.%s' exists\n", field)
	}

	// 显示表统计
	fmt.Println("\n📊 Database Statistics:")
	for _, table := range tables {
		var count int64
		db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count)
		fmt.Printf("   %s: %d records\n", table, count)
	}

	return nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}
