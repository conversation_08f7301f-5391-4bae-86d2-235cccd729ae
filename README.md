# FCC前庭控制系统 (Forecourt Control Center)

[![Go Version](https://img.shields.io/badge/Go-1.23+-blue.svg)](https://golang.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](docs/testing/)

FCC是一个高性能、高可用的前庭设备控制系统，专为加油站、充电站等前庭设备的统一管理和控制而设计。系统采用分层架构，支持多种设备协议，目前已完整实现Wayne DART协议支持。

## 🚀 项目特性

### 核心功能
- **统一设备控制**: 提供统一的设备管理和控制API接口
- **多协议支持**: 支持Wayne DART、Modbus等多种设备通信协议
- **高性能处理**: API响应时间<200ms，设备命令执行<25ms
- **实时监控**: 设备状态实时采集和事件推送
- **企业级特性**: 事务管理、优先级控制、重试机制

### 技术特性
- **分层架构**: 表示层 → 服务层 → 基础设施层
- **高可用性**: 支持99.9%系统可用性
- **可扩展性**: 插件化适配器架构，支持1000+设备并发
- **容器化部署**: 支持Docker容器化和边缘计算部署

## 📋 目录结构

```
fcc-service/
├── cmd/                    # 应用程序入口
├── internal/               # 内部实现
│   ├── server/            # HTTP服务器层
│   ├── services/          # 业务服务层
│   ├── adapters/          # 设备适配器层
│   ├── storage/           # 数据存储层
│   └── config/            # 配置管理
├── pkg/                   # 公共包
│   ├── api/              # API接口定义
│   ├── models/           # 数据模型
│   └── errors/           # 错误定义
├── docs/                  # 文档目录
│   ├── architecture/     # 架构设计文档
│   ├── api/             # API接口文档
│   ├── protocols/       # 协议规范文档
│   ├── business/        # 业务流程文档
│   ├── implementation/  # 实现说明文档
│   ├── testing/         # 测试相关文档
│   └── guides/          # 使用指南
├── configs/              # 配置文件
├── scripts/              # 构建和部署脚本
└── tests/               # 测试文件
```

## 🏗️ 系统架构

### 分层架构设计

```
┌─────────────────────────┐
│      BOS中间件          │  # 业务逻辑层
└──────────┬──────────────┘
           │ HTTP REST API
┌──────────▼──────────────┐
│   表示层 (HTTP服务器)   │  # 请求处理层
├─────────────────────────┤
│ ControllerHandler       │
│ DeviceHandler           │
│ CommandHandler          │
└──────────┬──────────────┘
           │ 服务调用
┌──────────▼──────────────┐
│   服务层 (业务逻辑)     │  # 核心业务层
├─────────────────────────┤
│ DeviceManager           │
│ CommandExecutor         │
│ StatusMonitor           │
└──────────┬──────────────┘
           │ 适配器接口
┌──────────▼──────────────┐
│ 基础设施层 (设备适配器) │  # 设备通信层
├─────────────────────────┤
│ Wayne适配器             │
│ DART协议实现            │
│ 串口连接管理            │
└──────────┬──────────────┘
           │ 设备通信
┌──────────▼──────────────┐
│     物理设备层          │  # 硬件设备
├─────────────────────────┤
│ 油机 | 油枪 | ATG      │
└─────────────────────────┘
```

### 核心组件

- **设备管理器 (DeviceManager)**: 管理设备生命周期、状态监控
- **命令执行器 (CommandExecutor)**: 处理设备控制命令、优先级管理
- **状态监控器 (StatusMonitor)**: 实时状态采集、事件发布
- **Wayne适配器**: Wayne DART协议的专业化实现

## 🛠️ 快速开始

### 系统要求

- Go 1.23+
- PostgreSQL 12+
- Redis 6+
- Linux/macOS/Windows

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd fcc-service
```

2. **安装依赖**
```bash
go mod download
```

3. **配置数据库**
```bash
# 创建PostgreSQL数据库
createdb fcc_service

# 启动Redis服务
redis-server
```

4. **修改配置文件**
```bash
cp configs/config.yaml.example configs/config.yaml
# 编辑配置文件，设置数据库连接等参数
```

5. **编译运行**
```bash
# 编译
make build

# 运行
./bin/fcc-service
```

### Docker部署

```bash
# 构建镜像
docker build -t fcc-service .

# 运行容器
docker-compose up -d
```

## 📡 API接口

### 控制器管理

```http
# 注册Wayne控制器
POST /api/v1/controllers
Content-Type: application/json

{
  "id": "wayne_controller_01",
  "name": "Wayne控制器1",
  "type": "wayne_dart",
  "connection_config": {
    "port": "/dev/ttyUSB0",
    "baud_rate": 9600
  }
}

# 获取控制器列表
GET /api/v1/controllers

# 获取控制器详情
GET /api/v1/controllers/{controllerId}
```

### 设备管理

```http
# 发现设备
POST /api/v1/controllers/{controllerId}/discover

# 获取设备列表
GET /api/v1/devices?controller_id=wayne_controller_01

# 获取设备详情
GET /api/v1/devices/{deviceId}
```

### 命令执行

```http
# 执行设备命令
POST /api/v1/devices/{deviceId}/commands
Content-Type: application/json

{
  "command": "start_pump",
  "parameters": {
    "pump_id": "01",
    "preset_volume": "50.00"
  },
  "priority": "high",
  "timeout": 30
}

# 批量执行命令
POST /api/v1/commands/batch
Content-Type: application/json

{
  "commands": [
    {
      "device_id": "device_01",
      "command": "get_status"
    }
  ]
}
```

### 状态监控

```http
# 获取设备状态
GET /api/v1/devices/{deviceId}/status

# 获取状态历史
GET /api/v1/devices/{deviceId}/status/history?start_time=2024-01-01T00:00:00Z&end_time=2024-01-02T00:00:00Z

# WebSocket实时状态推送
WS /api/v1/devices/{deviceId}/events
```

### 系统管理

```http
# 健康检查
GET /health

# 系统信息
GET /api/v1/system/info

# 监控指标
GET /metrics
```

## 🔧 配置说明

### 主配置文件 (configs/config.yaml)

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s

# 数据库配置
database:
  host: "localhost"
  port: 5432
  user: "fcc_user"
  password: "fcc_password"
  dbname: "fcc_service"
  sslmode: "disable"

# Redis缓存配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

# Wayne DART协议配置
wayne:
  dart:
    baud_rates: [9600, 19200]
    timeout: 25ms
    address_range:
      min: 0x50
      max: 0x6F
  discovery:
    enabled: true
    interval: 30s
    timeout: 5s

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "logs/fcc-service.log"
  max_size: 100
  max_backups: 5
  max_age: 30
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
make test

# 运行单元测试
go test ./...

# 运行集成测试
make test-integration

# 生成测试覆盖率报告
make test-coverage
```

### 测试覆盖率

当前测试覆盖率: **85%+**

- 核心服务层: 90%+
- 适配器层: 85%+
- API处理层: 80%+

## 📚 文档

### 设计文档
- [系统架构设计](docs/architecture/FCC_Architecture_Design.md)
- [Wayne DART协议开发任务](docs/FCC_Wayne_DART协议支持开发任务.md)
- [业务流程设计](docs/business/交易完成业务流程完整分析.md)

### API文档
- [FCC V2 API规范](docs/api/FCC_V2_API_SPECIFICATION.md)
- [服务接口文档](docs/api/FCC_Service_V2_API_Interfaces.md)

### 协议文档
- [Wayne命令解释](docs/protocols/Wayne_CMD_解释.md)
- [DART协议规范](wayne/Protocol_Specification_Dart_Line_V1.3.md)
- [泵接口协议](wayne/Dart_Pump_Interface_1.md)

### 使用指南
- [设备轮询器使用说明](docs/guides/DevicePoller_README.md)
- [日志记录指南](docs/guides/README_LOGGING.md)
- [原始数据调试指南](docs/guides/README-RAW-DATA-DEBUG.md)

## 🔍 故障排查

### 常见问题

1. **设备连接失败**
   - 检查串口权限: `sudo chmod 666 /dev/ttyUSB0`
   - 确认波特率设置: 9600 或 19200
   - 检查设备地址范围: 0x50-0x6F

2. **API响应超时**
   - 检查数据库连接状态
   - 确认Redis服务运行正常
   - 查看日志文件获取详细错误信息

3. **设备发现失败**
   - 确认DART协议配置正确
   - 检查设备物理连接
   - 验证设备地址范围设置

### 日志分析

```bash
# 查看实时日志
tail -f logs/fcc-service.log

# 过滤错误日志
grep "ERROR" logs/fcc-service.log

# 查看设备通信日志
grep "wayne" logs/fcc-service.log
```

## 🤝 贡献指南

### 开发规范

1. **代码规范**: 遵循Go官方代码风格
2. **测试要求**: 新功能必须包含单元测试，覆盖率≥80%
3. **文档更新**: 重要变更需要更新相关文档
4. **架构合规**: 严格遵循FCC分层架构设计

### 提交流程

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交代码: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 📞 联系方式

- 项目维护者: [维护者信息]
- 问题反馈: [Issues页面]
- 技术讨论: [讨论区链接]

---

**FCC前庭控制系统** - 为智能前庭设备管理而生 🚀 