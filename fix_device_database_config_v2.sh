#!/bin/bash

echo "🔧 修复数据库中的设备地址配置 (使用正确密码)"

echo "=== 问题根源 ==="
echo "通过分析发现："
echo "1. pump02设备在配置文件中完全不存在"
echo "2. 数据库中pump01和pump02的device_address配置不正确"
echo "3. 这导致WayneAdapter中地址映射错误"

echo
echo "=== 修复步骤 ===" 

echo "1️⃣ 停止FCC服务"
pkill -f fcc-service 2>/dev/null
sleep 2

echo "2️⃣ 检查当前数据库配置"

# 设置PostgreSQL连接变量
export PGPASSWORD="zB3!lL9+wJ"

echo "查询当前设备配置："
psql -h localhost -U fcc_user -d fcc_db -c "
SELECT 
    id,
    name,
    device_address,
    metadata->'dart_address' as dart_addr,
    metadata->'address_hex' as addr_hex
FROM devices 
WHERE id LIKE '%com7%' OR id LIKE '%COM7%'
ORDER BY device_address;
"

echo
echo "3️⃣ 修复数据库配置"

echo "正在检查pump02设备是否存在..."

# 首先检查是否存在pump02设备，如果不存在则创建
PUMP02_EXISTS=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM devices WHERE id = 'device_com7_pump02';" | tr -d ' ')

echo "pump02设备存在检查结果: $PUMP02_EXISTS"

if [ "$PUMP02_EXISTS" = "0" ]; then
    echo "pump02设备不存在，正在创建..."
    
    psql -h localhost -U fcc_user -d fcc_db << EOF
-- 创建pump02设备
INSERT INTO devices (
    id, controller_id, name, type, model, manufacturer, device_address, config, 
    station_id, island_id, position, status, health, properties, metadata, 
    created_at, updated_at
) VALUES (
    'device_com7_pump02',
    'controller_com7',
    'COM7 Fuel Pump 02',
    'fuel_pump',
    'Wayne Ovation',
    'Wayne',
    81,  -- DART地址81 (0x51)
    jsonb_build_object(
        'enabled', true,
        'description', 'COM7上的燃油泵设备02',
        'pump_config', jsonb_build_object(
            'nozzle_count', 2,
            'supported_fuels', array['gasoline', 'diesel'],
            'max_flow_rate', 60.0,
            'precision', 3
        )
    ),
    'station_001',
    'island_01', 
    'COM7-Pump-02',
    'offline',
    'unknown',
    jsonb_build_object(
        'protocol', 'DART',
        'address_hex', '0x51',
        'dart_features', array['polling', 'data_transfer', 'command_response']
    ),
    jsonb_build_object(
        'serial_port', 'COM7',
        'dart_address', 81,
        'created_by', 'admin'
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)
ON CONFLICT (id) DO UPDATE SET
    device_address = EXCLUDED.device_address,
    metadata = EXCLUDED.metadata,
    updated_at = CURRENT_TIMESTAMP;
EOF

    echo "✅ pump02设备创建/更新完成"
else
    echo "pump02设备已存在，正在更新地址..."
fi

# 确保两个设备的地址正确
echo "正在修复设备地址..."

psql -h localhost -U fcc_user -d fcc_db << EOF
-- 修复pump01地址
UPDATE devices SET 
    device_address = 80, 
    metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object('dart_address', 80, 'address_hex', '0x50') 
WHERE id = 'device_com7_pump01';

-- 修复pump02地址  
UPDATE devices SET 
    device_address = 81, 
    metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object('dart_address', 81, 'address_hex', '0x51') 
WHERE id = 'device_com7_pump02';
EOF

# 检查wayne_devices表是否存在，如果存在则更新
echo "检查并更新wayne_devices表..."
WAYNE_TABLE_EXISTS=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'wayne_devices';" | tr -d ' ')

if [ "$WAYNE_TABLE_EXISTS" = "1" ]; then
    psql -h localhost -U fcc_user -d fcc_db << EOF
INSERT INTO wayne_devices (device_id, address, status, firmware_version) 
VALUES 
    ('device_com7_pump01', 80, 'offline', 'v2.1.0'),
    ('device_com7_pump02', 81, 'offline', 'v2.1.0')
ON CONFLICT (device_id) DO UPDATE SET
    address = EXCLUDED.address,
    updated_at = CURRENT_TIMESTAMP;
EOF
    echo "✅ wayne_devices表已更新"
else
    echo "⚠️ wayne_devices表不存在，跳过"
fi

echo
echo "4️⃣ 验证修复结果"

echo "查询修复后的设备配置："
psql -h localhost -U fcc_user -d fcc_db -c "
SELECT 
    id,
    name,
    device_address,
    metadata->'dart_address' as dart_addr,
    metadata->'address_hex' as addr_hex
FROM devices 
WHERE id IN ('device_com7_pump01', 'device_com7_pump02')
ORDER BY device_address;
"

if [ "$WAYNE_TABLE_EXISTS" = "1" ]; then
    echo
    echo "查询Wayne设备表："
    psql -h localhost -U fcc_user -d fcc_db -c "
    SELECT device_id, address, status FROM wayne_devices 
    WHERE device_id IN ('device_com7_pump01', 'device_com7_pump02')
    ORDER BY address;
    "
fi

echo
echo "5️⃣ 验证地址配置正确性"

# 验证配置是否正确
PUMP01_CORRECT=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM devices WHERE id = 'device_com7_pump01' AND device_address = 80;" | tr -d ' ')
PUMP02_CORRECT=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM devices WHERE id = 'device_com7_pump02' AND device_address = 81;" | tr -d ' ')

echo "配置验证结果："
echo "- pump01地址80配置: $([ "$PUMP01_CORRECT" = "1" ] && echo '✅ 正确' || echo '❌ 错误')"
echo "- pump02地址81配置: $([ "$PUMP02_CORRECT" = "1" ] && echo '✅ 正确' || echo '❌ 错误')"

if [ "$PUMP01_CORRECT" = "1" ] && [ "$PUMP02_CORRECT" = "1" ]; then
    echo "✅ 数据库配置修复成功！"
    
    echo
    echo "6️⃣ 重启FCC服务"
    echo "正在启动FCC服务..."
    ./fcc-service &
    FCC_PID=$!
    
    echo "FCC服务PID: $FCC_PID"
    echo "等待服务启动..."
    sleep 5
    
    # 检查服务状态
    if ps -p $FCC_PID > /dev/null; then
        echo "✅ FCC服务启动成功"
        
        echo
        echo "7️⃣ 验证修复效果"
        
        echo "等待10秒让服务完全启动..."
        sleep 10
        
        LOG_FILE=$(find /home/<USER>"*.log" -type f -exec ls -t {} + | head -1)
        echo "监控日志文件: $LOG_FILE"
        
        echo "检查设备注册情况..."
        grep "设备已注册到Wayne适配器" "$LOG_FILE" | tail -5
        
        echo
        echo "检查设备地址映射（监控15秒）..."
        
        timeout 15 tail -f "$LOG_FILE" | while read line; do
            if echo "$line" | grep -q "device_address\":80"; then
                data=$(echo "$line" | grep -o "data.*[0-9A-F]*" | head -1)
                if echo "$data" | grep -q "data.*50"; then
                    echo "✅ pump01正确映射: device_address=80, data以50开头"
                elif echo "$data" | grep -q "data.*51"; then
                    echo "❌ pump01错误映射: device_address=80但data以51开头"
                fi
            fi
            
            if echo "$line" | grep -q "device_address\":81"; then
                data=$(echo "$line" | grep -o "data.*[0-9A-F]*" | head -1)
                if echo "$data" | grep -q "data.*51"; then
                    echo "✅ pump02正确映射: device_address=81, data以51开头"
                elif echo "$data" | grep -q "data.*50"; then
                    echo "❌ pump02错误映射: device_address=81但data以50开头"
                fi
            fi
        done
        
    else
        echo "❌ FCC服务启动失败"
        exit 1
    fi
    
else
    echo "❌ 数据库配置修复失败，请检查SQL执行结果"
    exit 1
fi

echo
echo "=== 修复完成 ==="
echo "数据库配置已修复:"
echo "- device_com7_pump01: device_address = 80 (0x50) ✅"
echo "- device_com7_pump02: device_address = 81 (0x51) ✅"
echo
echo "如果仍然出现地址映射错误，问题可能在于代码层面："
echo "1. SharedConnection机制中的设备ID传递"
echo "2. WayneAdapter中设备注册的顺序问题"
echo "3. DeviceTransportWrapper的地址处理逻辑" 