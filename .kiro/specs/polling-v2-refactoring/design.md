# Polling V2 重构设计文档

## 概述

本文档描述了 Polling V2 重构的详细设计方案，旨在清理无效的 v1 代码，优化 v2 架构，并简化项目结构。

## 架构分析

### 当前状态分析

#### V1 轮询组件（需要清理）
- `internal/services/polling/scheduler.go` - 复杂的轮询调度器，已被 v2 的 DispatchTask 替代
- `internal/services/polling/strategy.go` - 轮询策略管理，功能已集成到 v2 架构中
- `internal/services/polling/errors.go` - 错误定义，部分可迁移到 v2

#### V2 轮询组件（需要优化）

**核心轮询组件：**
- `internal/services/polling/v2/device_poller.go` - 核心设备轮询器，包含完整的 Wayne DART 协议处理
- `internal/services/polling/v2/dispatch_task.go` - 任务调度器，管理多设备轮询调度
- `internal/services/polling/v2/communication_interface.go` - 通信接口抽象，处理串口通信
- `internal/services/polling/v2/interfaces.go` - 接口定义，解决循环依赖问题

**业务处理组件：**
- `internal/services/polling/v2/transaction_cd_assembler.go` - CD 命令组装器，处理主机到设备的命令
- `internal/services/polling/v2/transaction_dc_assembler.go` - DC 事务处理器，处理设备到主机的数据
- `internal/services/polling/v2/transaction_lifecycle.go` - 交易生命周期管理，核心业务逻辑

**辅助组件：**
- `internal/services/polling/v2/serial_scheduler.go` - 串口调度器，已被标记为禁用但代码仍存在
- `internal/services/polling/v2/frame_builder.go` - DART 帧构建器
- `internal/services/polling/v2/nozzle_id_resolver.go` - 喷嘴 ID 解析器
- `internal/services/polling/v2/operator_id_cache.go` - 操作员 ID 缓存
- `internal/services/polling/v2/poller_stats.go` - 轮询统计收集器
- `internal/services/polling/v2/polling_types.go` - 类型定义

### 依赖关系分析

```mermaid
graph TD
    A[main_v2.go] --> B[DispatchTask]
    B --> C[DevicePoller]
    B --> D[StateManager]
    B --> E[CommunicationInterface]
    
    C --> F[TransactionAssembler]
    C --> G[CDTransactionBuilder]
    C --> H[FrameBuilder]
    C --> I[NozzleIDResolver]
    C --> J[OperatorIDCache]
    
    F --> K[TransactionLifecycleService]
    F --> L[NozzleCountersService]
    F --> M[NozzleService]
    
    K --> N[Repository]
    K --> O[TransactionLifecycleListener]
    
    P[V1 Scheduler] -.-> Q[V1 Strategy]
    P -.-> R[V1 Errors]
    
    style P fill:#ffcccc
    style Q fill:#ffcccc
    style R fill:#ffcccc
```

### 业务功能分析

#### Wayne DART 协议处理
- **CD 命令处理**：主机到设备的命令（授权、预设、价格更新等）
- **DC 事务处理**：设备到主机的数据（状态、体积金额、喷嘴状态、计数器等）
- **协议状态管理**：TX 序列号、帧构建、ACK/NAK 处理

#### 交易生命周期管理
- **交易创建**：基于 DC3 喷嘴拔出事件
- **交易更新**：DC2 体积金额数据更新
- **交易完成**：DC1 加油完成 + DC101 泵码验证
- **状态转换**：initiated → filling → pending_counters → completed

#### 设备状态同步
- **喷嘴状态**：基于 DC3 事务更新数据库
- **设备状态**：基于 DC1 事务更新状态机
- **泵码管理**：DC101 计数器数据记录和验证

## 设计方案

### 1. V1 代码清理策略

#### 1.1 完全删除的组件
- `internal/services/polling/scheduler.go`
  - 理由：功能已被 `DispatchTask` 完全替代
  - 影响：无，没有其他组件依赖
  
- `internal/services/polling/strategy.go`
  - 理由：轮询策略已集成到 v2 的 `DevicePoller` 中
  - 影响：无，策略逻辑已在 v2 中重新实现

#### 1.2 部分迁移的组件
- `internal/services/polling/errors.go`
  - 迁移策略：将通用错误定义移动到 `pkg/errors` 包
  - 删除策略：删除 v1 特定的错误定义

### 2. V2 架构优化

#### 2.1 保留核心业务组件
基于对完整业务功能的分析，以下组件包含重要的业务逻辑，需要保留并优化：

**必须保留的组件：**
- `device_poller.go` - 包含完整的 Wayne DART 协议处理和设备管理逻辑
- `dispatch_task.go` - 多设备调度和资源管理
- `transaction_cd_assembler.go` - CD 命令处理（授权、预设、价格更新等）
- `transaction_dc_assembler.go` - DC 事务处理（状态、数据解析）
- `transaction_lifecycle.go` - 交易生命周期管理核心业务逻辑
- `communication_interface.go` - 串口通信抽象
- `interfaces.go` - 解决循环依赖的接口定义

#### 2.2 简化和合并的组件

**串口调度器简化：**
- 删除 `TimeSliceScheduler` 的复杂时间片调度实现
- 保留 `SerialPortScheduler` 接口以维持兼容性
- 创建简化的 `NoOpScheduler` 实现，因为新架构通过通信锁实现串口管理

**辅助组件整合：**
- `frame_builder.go` - 保留，DART 协议帧构建是核心功能
- `nozzle_id_resolver.go` - 保留，喷嘴 ID 解析是业务必需
- `operator_id_cache.go` - 保留，操作员 ID 缓存用于授权超时管理
- `poller_stats.go` - 简化，移除不必要的统计项
- `polling_types.go` - 整理，合并重复的类型定义

#### 2.3 代码质量优化

**DevicePoller 优化：**
- 保留核心的 Wayne DART 协议处理逻辑
- 简化调试日志，使用条件编译或配置控制
- 优化错误处理，统一错误分类和处理策略
- 保留业务关键的 panic 恢复机制

**TransactionLifecycle 优化：**
- 保留完整的交易状态管理逻辑
- 优化数据库操作，减少不必要的查询
- 保留泵码验证和数据质量检查逻辑

### 3. 项目结构重组

#### 3.1 目录结构优化（保守方案）

考虑到 v2 包含复杂的业务逻辑和大量相互依赖的组件，采用保守的重构策略：

**当前结构：**
```
internal/services/polling/
├── errors.go          # V1 - 删除
├── scheduler.go       # V1 - 删除  
├── strategy.go        # V1 - 删除
└── v2/
    ├── device_poller.go           # 保留 - 核心轮询器
    ├── dispatch_task.go           # 保留 - 任务调度器
    ├── transaction_cd_assembler.go # 保留 - CD命令处理
    ├── transaction_dc_assembler.go # 保留 - DC事务处理
    ├── transaction_lifecycle.go    # 保留 - 交易生命周期
    ├── communication_interface.go  # 保留 - 通信接口
    ├── interfaces.go              # 保留 - 接口定义
    ├── serial_scheduler.go        # 简化 - 串口调度
    ├── frame_builder.go           # 保留 - 帧构建器
    ├── nozzle_id_resolver.go      # 保留 - 喷嘴解析器
    ├── operator_id_cache.go       # 保留 - 操作员缓存
    ├── poller_stats.go            # 简化 - 统计收集
    └── polling_types.go           # 整理 - 类型定义
```

**目标结构（渐进式重构）：**
```
internal/services/polling/
└── v2/  # 保留 v2 目录结构，避免大规模重构风险
    ├── device_poller.go           # 优化 - 简化调试代码
    ├── dispatch_task.go           # 优化 - 提升性能
    ├── transaction_cd_assembler.go # 保留 - 业务核心
    ├── transaction_dc_assembler.go # 保留 - 业务核心
    ├── transaction_lifecycle.go    # 保留 - 业务核心
    ├── communication_interface.go  # 保留 - 通信抽象
    ├── interfaces.go              # 保留 - 循环依赖解决
    ├── serial_scheduler.go        # 简化 - NoOp实现
    ├── frame_builder.go           # 保留 - DART协议必需
    ├── nozzle_id_resolver.go      # 保留 - 业务必需
    ├── operator_id_cache.go       # 保留 - 授权管理
    ├── poller_stats.go            # 简化 - 移除冗余统计
    └── types.go                   # 合并 - polling_types.go重命名
```

#### 3.2 包导入保持不变

为了降低重构风险，保持现有的包导入结构：

```go
// 保持现有导入，避免大规模修改
pollingv2 "fcc-service/internal/services/polling/v2"
```

**重构原则：**
1. **最小化破坏性变更** - 保持现有目录结构和导入路径
2. **渐进式优化** - 先优化代码质量，再考虑结构调整
3. **业务逻辑优先** - 确保 Wayne DART 协议和交易管理功能完整性

### 4. 接口设计

#### 4.1 核心接口保持不变
- `DevicePollerInterface`
- `CommunicationInterface` 
- `TransactionLifecycleServiceInterface`

#### 4.2 简化的调度器接口
```go
// SerialPortScheduler 简化接口
type SerialPortScheduler interface {
    // 保持兼容性的最小接口
    RegisterDevice(deviceID, serialPort string) error
    UnregisterDevice(deviceID string) error
    IsSchedulingEnabled() bool
}

// NoOpScheduler 无操作实现
type NoOpScheduler struct{}

func (n *NoOpScheduler) RegisterDevice(deviceID, serialPort string) error {
    return nil // 无操作
}
```

### 5. 数据流设计

#### 5.1 轮询数据流
```mermaid
sequenceDiagram
    participant DT as DispatchTask
    participant DP as DevicePoller
    participant CI as CommunicationInterface
    participant TLS as TransactionLifecycleService
    
    DT->>DP: Schedule Poll
    DP->>CI: Send DART Frame
    CI->>DP: Response Frame
    DP->>TLS: Process Transaction
    TLS->>DP: Transaction Result
    DP->>DT: Poll Result
```

#### 5.2 错误处理流
```mermaid
graph TD
    A[Communication Error] --> B{Error Type}
    B -->|Timeout| C[Retry with Backoff]
    B -->|Protocol Error| D[Log and Continue]
    B -->|Connection Error| E[Reconnect]
    C --> F[Update Device State]
    D --> F
    E --> F
```

### 6. 性能优化

#### 6.1 内存优化
- 移除不必要的缓存结构
- 优化字符串操作和日志记录
- 减少 goroutine 数量

#### 6.2 CPU 优化
- 简化轮询逻辑
- 减少锁竞争
- 优化序列化/反序列化

#### 6.3 网络优化
- 复用连接
- 优化超时设置
- 减少不必要的网络调用

### 7. 测试策略

#### 7.1 单元测试
- 为简化后的组件编写单元测试
- 确保接口兼容性测试通过
- 添加性能基准测试

#### 7.2 集成测试
- 验证 v2 架构的完整功能
- 测试错误处理和恢复机制
- 验证与 Wayne DART 协议的兼容性

#### 7.3 回归测试
- 确保所有现有功能正常工作
- 验证 API 接口不变
- 检查性能指标

### 8. 迁移计划

#### 阶段 1: 准备阶段
1. 创建代码备份
2. 分析依赖关系
3. 准备测试环境

#### 阶段 2: V1 代码清理
1. 删除 `scheduler.go`
2. 删除 `strategy.go`
3. 迁移 `errors.go` 中的通用错误

#### 阶段 3: V2 架构优化
1. 简化 `serial_scheduler.go`
2. 优化 `device_poller.go`
3. 重构 `dispatch_task.go`

#### 阶段 4: 结构重组
1. 移动文件到新位置
2. 更新导入路径
3. 更新文档

#### 阶段 5: 测试和验证
1. 运行所有测试
2. 性能基准测试
3. 生产环境验证

## 风险评估

### 高风险项
1. **API 兼容性破坏** - 通过保持接口不变来缓解
2. **性能回退** - 通过基准测试来监控
3. **功能缺失** - 通过完整的回归测试来验证

### 中风险项
1. **编译错误** - 通过渐进式重构来减少
2. **运行时错误** - 通过充分的集成测试来发现

### 低风险项
1. **代码风格变化** - 影响较小，可接受
2. **文档更新** - 可以后续补充

## 成功指标

1. **代码质量**
   - 代码行数减少 30%+
   - 循环依赖数量为 0
   - 代码覆盖率保持 80%+

2. **性能指标**
   - 编译时间减少 20%+
   - 内存使用减少 15%+
   - 轮询延迟保持不变

3. **维护性指标**
   - 新功能开发时间减少
   - Bug 修复时间减少
   - 代码审查时间减少