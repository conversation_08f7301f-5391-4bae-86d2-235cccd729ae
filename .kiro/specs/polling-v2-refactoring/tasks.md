# Polling V2 重构实施计划

## 重构任务列表

### 阶段 1: 准备和分析

- [ ] 1.1 创建代码备份和分支
  - 创建重构专用分支 `refactor/polling-v2-cleanup`
  - 备份当前 polling 相关代码
  - 建立回滚机制和检查点
  - _Requirements: 5.1, 5.2_

- [ ] 1.2 分析 V1 组件依赖关系
  - 扫描所有引用 `internal/services/polling/scheduler.go` 的代码
  - 扫描所有引用 `internal/services/polling/strategy.go` 的代码
  - 扫描所有引用 `internal/services/polling/errors.go` 的代码
  - 确认这些组件确实未被使用
  - _Requirements: 1.1, 1.2_

- [ ] 1.3 建立测试基准
  - 运行现有的所有测试用例，记录基准结果
  - 建立性能基准测试（编译时间、内存使用、轮询延迟）
  - 创建回归测试检查清单
  - _Requirements: 5.1, 5.2, 5.3_

### 阶段 2: V1 代码安全清理

- [ ] 2.1 验证 V1 scheduler.go 未被使用
  - 使用 `grep -r "polling.*scheduler" --include="*.go"` 搜索引用
  - 检查 import 语句中的引用
  - 确认没有间接依赖
  - _Requirements: 1.1_

- [ ] 2.2 验证 V1 strategy.go 未被使用
  - 使用 `grep -r "polling.*strategy" --include="*.go"` 搜索引用
  - 检查是否有类型或接口被其他包使用
  - 确认策略逻辑已在 v2 中重新实现
  - _Requirements: 1.2_

- [ ] 2.3 迁移 errors.go 中的通用错误定义
  - 分析 `internal/services/polling/errors.go` 中的错误定义
  - 将通用错误移动到 `pkg/errors/polling.go`
  - 更新 v2 代码中的错误引用
  - 确保错误处理逻辑保持一致
  - _Requirements: 1.3_

- [ ] 2.4 删除 V1 轮询组件文件
  - 删除 `internal/services/polling/scheduler.go`
  - 删除 `internal/services/polling/strategy.go`
  - 删除 `internal/services/polling/errors.go`（在迁移完成后）
  - 运行编译测试确保无破坏性影响
  - _Requirements: 1.1, 1.2, 1.3_

### 阶段 3: V2 架构代码质量优化

- [ ] 3.1 优化 DevicePoller 调试代码
  - 分析 `device_poller.go` 中的调试日志和 panic 恢复代码
  - 使用配置或编译标志控制调试代码的启用
  - 保留业务关键的错误处理和恢复机制
  - 简化生产环境不需要的详细日志
  - _Requirements: 2.1, 2.2_

- [ ] 3.2 简化 SerialScheduler 实现
  - 分析 `serial_scheduler.go` 中的 TimeSliceScheduler 复杂逻辑
  - 创建 NoOpScheduler 实现替代复杂的时间片调度
  - 保留 SerialPortScheduler 接口以维持兼容性
  - 更新 DispatchTask 中的串口调度器使用
  - _Requirements: 2.1, 2.2_

- [ ] 3.3 整理和简化 PollerStats
  - 分析 `poller_stats.go` 中的统计收集逻辑
  - 移除不必要或重复的统计项
  - 优化统计数据的内存使用
  - 保留核心的性能监控指标
  - _Requirements: 2.1, 2.2_

- [ ] 3.4 合并和整理类型定义
  - 分析 `polling_types.go` 中的类型定义
  - 识别重复或未使用的类型定义
  - 将文件重命名为 `types.go` 以简化命名
  - 整理类型定义的组织结构
  - _Requirements: 2.1, 2.2_

### 阶段 4: 性能和内存优化

- [ ] 4.1 优化 DevicePoller 内存使用
  - 分析 DevicePoller 中的内存分配模式
  - 优化字符串操作和日志记录的内存使用
  - 减少不必要的数据结构缓存
  - 优化 goroutine 的创建和管理
  - _Requirements: 2.1, 2.2_

- [ ] 4.2 优化 DispatchTask 调度性能
  - 分析多设备调度的性能瓶颈
  - 优化设备轮询的时间片分配
  - 减少锁竞争和同步开销
  - 优化通信接口的资源管理
  - _Requirements: 2.1, 2.2_

- [ ] 4.3 优化 TransactionLifecycle 数据库操作
  - 分析交易生命周期中的数据库查询模式
  - 减少不必要的数据库查询
  - 优化批量操作和事务处理
  - 保留完整的业务逻辑和数据一致性
  - _Requirements: 2.1, 2.2_

### 阶段 5: 错误处理统一化

- [ ] 5.1 统一错误分类和处理策略
  - 分析 v2 组件中的错误处理模式
  - 创建统一的错误分类体系
  - 实现一致的错误恢复策略
  - 确保关键业务流程的错误处理完整性
  - _Requirements: 2.1, 2.2_

- [ ] 5.2 优化 Wayne DART 协议错误处理
  - 分析 DART 协议处理中的错误场景
  - 优化超时、重试和恢复机制
  - 确保协议状态的一致性
  - 保留所有业务关键的协议处理逻辑
  - _Requirements: 2.1, 2.2_

### 阶段 6: 测试和验证

- [ ] 6.1 运行完整的回归测试套件
  - 执行所有现有的单元测试
  - 执行集成测试验证 Wayne DART 协议兼容性
  - 验证交易生命周期管理功能完整性
  - 确保设备状态同步功能正常
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 6.2 性能基准测试和对比
  - 测量重构前后的编译时间变化
  - 测量内存使用情况的改善
  - 测量轮询延迟和吞吐量
  - 验证性能指标达到预期目标
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 6.3 API 兼容性验证
  - 验证所有公开接口保持不变
  - 测试现有的 HTTP API 端点功能
  - 确保前端应用的兼容性
  - 验证第三方集成的兼容性
  - _Requirements: 5.1, 5.2, 5.3_

### 阶段 7: 文档更新和部署准备

- [ ] 7.1 更新架构文档
  - 更新系统架构图反映重构后的结构
  - 更新组件依赖关系文档
  - 记录重构过程中的重要决策和变更
  - 创建新的开发者指南
  - _Requirements: 4.1, 4.2_

- [ ] 7.2 更新部署和运维文档
  - 更新部署脚本和配置文件
  - 更新监控和日志配置
  - 创建重构后的故障排查指南
  - 准备生产环境部署计划
  - _Requirements: 4.1, 4.2_

- [ ] 7.3 代码审查和最终验证
  - 进行全面的代码审查
  - 验证代码质量和风格一致性
  - 确认所有重构目标已达成
  - 准备合并到主分支的 PR
  - _Requirements: 4.1, 4.2, 5.1, 5.2, 5.3_

## 重要注意事项

### 业务连续性保障
- 所有重构操作必须保持 Wayne DART 协议处理的完整性
- 交易生命周期管理逻辑不得有任何功能缺失
- 设备状态同步和泵码验证功能必须完全保留

### 风险控制措施
- 每个阶段完成后都要进行完整的功能测试
- 建立快速回滚机制，在发现问题时能够立即恢复
- 采用渐进式重构策略，避免大规模破坏性变更

### 成功验收标准
- 编译时间减少 20% 以上
- 内存使用减少 15% 以上
- 所有现有功能测试通过
- API 接口保持完全兼容
- Wayne DART 协议处理无任何功能回退