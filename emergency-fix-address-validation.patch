# 紧急修复：RS485 地址验证补丁
# 解决数据串扰导致的通讯瘫痪问题

## 问题描述
当前系统存在严重的地址串扰问题：
- pump10 发送地址 0x51 的请求
- 接收到地址 0x50 (pump09) 的响应
- pump10 错误处理了 pump09 的数据
- 导致所有设备通讯瘫痪

## 修复方案

### 1. 在 DevicePoller 层面添加地址验证

文件: internal/services/polling/v2/device_poller.go

在 sendFrameAndWait 方法中添加地址验证：

```go
// sendFrameAndWait 发送帧并等待响应
func (p *DevicePoller) sendFrameAndWait(frame *dartline.Frame, timeout time.Duration, businessType string) (*dartline.Frame, error) {
    frameData := frame.Encode()
    
    // 提取目标地址用于验证
    expectedAddress := p.config.DeviceInfo.Address
    
    // 发送并等待响应
    responseFrame, err := p.communication.SendFrameAndWait(ctx, frameData, timeout)
    if err != nil {
        return nil, err
    }
    
    // 🚀 新增：验证响应地址匹配
    if responseFrame != nil && responseFrame.Address != expectedAddress {
        p.logger.Error("设备地址不匹配，检测到数据串扰",
            zap.String("device_id", p.config.DeviceInfo.ID),
            zap.Uint8("expected_address", expectedAddress),
            zap.Uint8("received_address", responseFrame.Address),
            zap.String("business_type", businessType),
            zap.String("sent_frame", fmt.Sprintf("%X", frameData)),
            zap.String("received_frame", fmt.Sprintf("%X", responseFrame.Encode())),
            zap.String("error_type", "address_mismatch"))
        
        return nil, fmt.Errorf("address mismatch: expected %d, got %d (possible data crosstalk)", 
                              expectedAddress, responseFrame.Address)
    }
    
    return responseFrame, nil
}
```

### 2. 在 SerialTransport 层面实现地址路由

文件: internal/services/polling/v2/transport_implementations.go

修改 SerialTransport 结构：

```go
type SerialTransport struct {
    // 现有字段...
    
    // 🚀 新增：设备地址到响应通道的映射
    waitingDevices map[byte]chan *dartline.Frame
    waitingMutex   sync.RWMutex
    
    // 🚀 移除：共享的 frameReady channel
    // frameReady chan *dartline.Frame
}
```

修改 SendFrameAndWait 方法：

```go
func (st *SerialTransport) SendFrameAndWait(ctx context.Context, data []byte, timeout time.Duration) (*dartline.Frame, error) {
    if !st.IsConnected() {
        return nil, fmt.Errorf("not connected")
    }

    // 🚀 提取目标设备地址
    if len(data) < 1 {
        return nil, fmt.Errorf("invalid frame data")
    }
    targetAddress := data[0]

    // 🚀 为目标设备地址创建响应通道
    respChan := make(chan *dartline.Frame, 1)
    
    st.waitingMutex.Lock()
    st.waitingDevices[targetAddress] = respChan
    st.waitingMutex.Unlock()
    
    defer func() {
        st.waitingMutex.Lock()
        delete(st.waitingDevices, targetAddress)
        close(respChan)
        st.waitingMutex.Unlock()
    }()

    // 发送数据
    err := st.serialManager.SendFrame(ctx, data)
    if err != nil {
        return nil, fmt.Errorf("failed to send frame: %w", err)
    }

    // 🚀 等待特定设备地址的响应
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()

    select {
    case frame := <-respChan:
        return frame, nil
    case <-timeoutCtx.Done():
        return nil, fmt.Errorf("timeout waiting for response")
    case <-ctx.Done():
        return nil, ctx.Err()
    }
}
```

修改 frameAssemblerRoutine 方法：

```go
func (st *SerialTransport) frameAssemblerRoutine() {
    // ... 现有代码 ...
    
    for _, frame := range frames {
        st.waitingMutex.RLock()
        if respChan, exists := st.waitingDevices[frame.Address]; exists {
            select {
            case respChan <- frame:
                st.logger.Debug("帧已路由到正确设备",
                    zap.Uint8("frame_address", frame.Address))
            default:
                st.logger.Warn("设备响应通道已满",
                    zap.Uint8("frame_address", frame.Address))
            }
        } else {
            // 🚀 记录无人等待的帧（可能是离线设备的延迟响应）
            st.logger.Warn("收到无人等待的帧",
                zap.Uint8("frame_address", frame.Address),
                zap.String("possible_cause", "设备离线延迟响应或地址错误"))
        }
        st.waitingMutex.RUnlock()
    }
}
```

## 实施步骤

### 立即措施 (5分钟内)
1. 重启 fcc-service 恢复通讯
2. 监控日志确认通讯恢复

### 短期修复 (1小时内)  
1. 实施方案1：DevicePoller 层面地址验证
2. 部署并测试

### 长期解决 (1-2天)
1. 实施方案2：SerialTransport 地址路由
2. 全面测试多设备并发场景

## 监控指标

修复后需要监控：
1. 地址不匹配错误次数
2. 无人等待的帧数量  
3. 设备通讯成功率
4. 响应时间分布

## 预期效果

1. **消除数据串扰**: 确保每个设备只处理自己地址的响应
2. **防止通讯瘫痪**: 避免因地址错误导致的系统阻塞
3. **提高系统稳定性**: 减少设备状态混乱
4. **便于问题诊断**: 清晰的地址不匹配日志
