#!/bin/bash

echo "🔍 检查数据库中的设备地址配置"

echo "=== 问题分析 ==="
echo "根据日志分析，发现了地址映射错误："
echo "- 设备地址80的帧中有20个错误（应该以50开头但以51开头）"
echo "- 设备地址81完全没有正确的帧"
echo "这说明设备注册到WayneAdapter时，地址信息可能有误"

echo
echo "=== 检查数据库设备配置 ==="

# 检查PostgreSQL数据库连接
echo "正在连接数据库查询设备配置..."

# 查询设备配置
echo "1️⃣ 查询所有COM7设备的地址配置："
psql -h localhost -U fcc_user -d fcc_db -c "
SELECT 
    id,
    name,
    device_address,
    metadata->'dart_address' as dart_addr_metadata,
    metadata->'address_hex' as addr_hex_metadata
FROM devices 
WHERE id LIKE '%com7%' OR id LIKE '%COM7%'
ORDER BY device_address;
" 2>/dev/null

echo
echo "2️⃣ 检查配置文件中的设备定义："

if [ -f "scripts/config-com7-device.sql" ]; then
    echo "找到配置文件 scripts/config-com7-device.sql"
    echo "查找pump01和pump02的地址定义："
    
    echo "pump01配置："
    grep -A5 -B5 "device_com7_pump01" scripts/config-com7-device.sql | grep -E "80|0x50|pump01"
    
    echo
    echo "pump02配置："
    grep -A5 -B5 "device_com7_pump02" scripts/config-com7-device.sql | grep -E "81|0x51|pump02"
else
    echo "❌ 未找到配置文件 scripts/config-com7-device.sql"
fi

echo
echo "3️⃣ 检查当前运行中的设备信息："

# 通过API检查设备信息
echo "通过API查询设备信息..."

curl -s "http://localhost:8080/api/v2/devices" | jq '.devices[] | select(.id | test("com7")) | {id: .id, device_address: .device_address, name: .name}' 2>/dev/null || echo "API查询失败或设备服务未运行"

echo
echo "4️⃣ 分析可能的问题："

echo "检查是否存在以下问题："
echo "a) 数据库中pump01和pump02的device_address字段配置错误"
echo "b) 配置脚本中的地址定义有误"
echo "c) 设备注册时地址传递错误"
echo "d) WayneAdapter中设备ID与地址的映射错误"

echo
echo "5️⃣ 验证预期配置："
echo "正确的配置应该是："
echo "- device_com7_pump01: device_address = 80 (0x50)"
echo "- device_com7_pump02: device_address = 81 (0x51)"

echo
echo "6️⃣ 生成修复建议："

# 检查是否需要修复
HAS_PUMP01=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM devices WHERE id = 'device_com7_pump01' AND device_address = 80;" 2>/dev/null | tr -d ' ')
HAS_PUMP02=$(psql -h localhost -U fcc_user -d fcc_db -t -c "SELECT COUNT(*) FROM devices WHERE id = 'device_com7_pump02' AND device_address = 81;" 2>/dev/null | tr -d ' ')

echo "数据库配置检查结果："
echo "- pump01地址80配置正确: $([ \"$HAS_PUMP01\" = \"1\" ] && echo '✅ 是' || echo '❌ 否')"  
echo "- pump02地址81配置正确: $([ \"$HAS_PUMP02\" = \"1\" ] && echo '✅ 是' || echo '❌ 否')"

if [ "$HAS_PUMP01" != "1" ] || [ "$HAS_PUMP02" != "1" ]; then
    echo
    echo "🔧 建议的修复步骤："
    echo "1. 停止FCC服务"
    echo "2. 修复数据库配置："
    
    if [ "$HAS_PUMP01" != "1" ]; then
        echo "   UPDATE devices SET device_address = 80 WHERE id = 'device_com7_pump01';"
    fi
    
    if [ "$HAS_PUMP02" != "1" ]; then
        echo "   UPDATE devices SET device_address = 81 WHERE id = 'device_com7_pump02';"
    fi
    
    echo "3. 重启FCC服务"
    echo "4. 验证地址映射是否正确"
else
    echo
    echo "✅ 数据库配置看起来是正确的"
    echo "问题可能在于："
    echo "1. 设备注册到WayneAdapter的流程中"
    echo "2. SharedConnection机制中的地址传递"
    echo "3. DeviceTransportWrapper的地址处理"
fi

echo
echo "=== 下一步调试建议 ==="
echo "1. 如果数据库配置错误 -> 修复数据库中的device_address字段"
echo "2. 如果数据库配置正确 -> 检查设备注册到WayneAdapter的代码路径"
echo "3. 在WayneAdapter.RegisterDevice方法中添加调试日志"
echo "4. 验证DeviceTransportWrapper创建时的地址参数" 