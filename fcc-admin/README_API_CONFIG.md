# FCC Admin API配置功能

## 概述

FCC Admin项目已经升级，支持动态配置API服务器地址，无需重新编译即可切换不同的后端服务器。

## 主要功能

### 1. 动态API配置
- 支持运行时修改API服务器地址
- 自动添加 `/api/v2` 后缀
- 配置持久化保存到本地存储
- 支持连接测试验证

### 2. 用户界面
- 浮动配置按钮（右下角齿轮图标）
- 快捷键支持：`Ctrl+Shift+A` 打开配置界面
- 实时API地址显示（左下角状态指示器）
- 直观的配置模态框

### 3. 技术特性
- TypeScript支持
- 响应式设计
- 错误处理和验证
- 配置变更监听

## 使用方法

### 方式一：使用浮动按钮
1. 点击页面右下角的齿轮图标
2. 在弹出的配置界面中输入API服务器地址
3. 点击"测试连接"验证服务器可用性
4. 点击"保存配置"应用新设置

### 方式二：使用快捷键
1. 按下 `Ctrl+Shift+A` 打开配置界面
2. 按照上述步骤进行配置

### 配置示例
```
API服务器地址: http://*************:8081
API版本: v2
连接超时: 10000ms

最终API地址: http://*************:8081/api/v2
```

## 代码架构

### 核心文件
- `lib/api-config.ts` - API配置管理核心
- `components/ui/ApiConfigModal.tsx` - 配置界面组件
- `components/ui/ApiConfigProvider.tsx` - 全局配置提供者
- `lib/api-client-v2.ts` - 更新的API客户端

### 配置管理器特性
```typescript
// 获取当前配置
const config = apiConfigManager.getConfig()

// 更新配置
apiConfigManager.updateConfig({
  baseUrl: 'http://*************:8081',
  apiVersion: 'v2',
  timeout: 10000
})

// 监听配置变更
const unsubscribe = apiConfigManager.addListener((newConfig) => {
  console.log('配置已更新:', newConfig)
})

// 验证API连接
const isValid = await apiConfigManager.validateApiUrl('http://*************:8081')
```

## 项目结构整理

```
fcc-admin/
├── app/                    # Next.js 应用目录
│   ├── layout.tsx         # 根布局（已集成API配置）
│   ├── page.tsx           # 首页
│   ├── devices/           # 设备管理页面
│   ├── controllers/       # 控制器管理页面
│   ├── transactions/      # 交易管理页面
│   └── dashboard/         # 仪表板页面
├── components/            # React组件
│   └── ui/               # UI组件
│       ├── ApiConfigModal.tsx      # API配置模态框
│       ├── ApiConfigProvider.tsx   # API配置提供者
│       └── Notifications.tsx       # 通知组件
├── lib/                   # 工具库
│   ├── api-config.ts     # API配置管理（新增）
│   ├── api-client-v2.ts  # API客户端（已更新）
│   └── simple-state.ts   # 状态管理
├── package.json          # 项目依赖
├── next.config.js        # Next.js配置
├── tailwind.config.js    # Tailwind CSS配置
└── tsconfig.json         # TypeScript配置
```

## 默认配置

```typescript
const DEFAULT_API_CONFIG = {
  baseUrl: 'http://localhost:8081',
  apiVersion: 'v2',
  timeout: 10000,
}
```

## 注意事项

1. **URL格式**: 输入的服务器地址会自动标准化，无需手动添加`/api/v2`后缀
2. **协议**: 如果未指定协议，会自动添加`http://`前缀
3. **持久化**: 配置会保存到浏览器的localStorage中
4. **验证**: 建议在保存前使用"测试连接"功能验证服务器可用性
5. **兼容性**: 支持v1和v2两个API版本

## 开发说明

如需扩展API配置功能，可以：

1. 在`APIConfig`接口中添加新的配置项
2. 在`ApiConfigModal`组件中添加对应的输入控件
3. 在`api-config.ts`中实现相关的处理逻辑

## 故障排除

### 连接测试失败
- 检查服务器地址是否正确
- 确认服务器是否运行在指定端口
- 检查防火墙和网络连接
- 验证API服务是否提供`/health`端点

### 配置无法保存
- 检查浏览器是否允许localStorage
- 清除浏览器缓存后重试
- 检查控制台是否有JavaScript错误

### 页面无法加载数据
- 确认API配置是否正确
- 检查服务器API版本是否匹配
- 验证服务器CORS设置是否允许前端域名 