#!/usr/bin/env node

/**
 * PreAuth 功能测试脚本
 * 
 * 这个脚本用于测试 PreAuth 功能的集成是否正常工作
 * 可以在开发环境中运行来验证 API 调用
 */

// 注意：这是一个 Node.js 脚本，用于演示如何测试 PreAuth 功能
// 在实际的 React 应用中，你应该在浏览器控制台中运行测试

console.log('🧪 PreAuth 功能测试脚本')
console.log('=' .repeat(50))

console.log(`
📋 测试说明:
1. 这个脚本演示了如何使用 PreAuth 功能
2. 在实际应用中，请在浏览器控制台中运行以下代码
3. 确保后端 PreAuth API 已经启用并正常运行

🔧 在浏览器控制台中运行的测试代码:
`)

console.log(`
// 1. 导入必要的模块
import { fccV2API, apiV2 } from '@/lib/api-client-v2'
import { testPreauthIntegration, quickPreauthTest, createVolumePreauth, createAmountPreauth } from '@/lib/preauth-test'

// 2. 测试设备和喷嘴 ID（请替换为实际的设备和喷嘴 ID）
const testDeviceId = 'device_001'  // 替换为实际设备 ID
const testNozzleId = 'nozzle_001'  // 替换为实际喷嘴 ID

// 3. 运行完整的集成测试
await testPreauthIntegration(testDeviceId, testNozzleId)

// 4. 快速测试体积预授权
await quickPreauthTest(testDeviceId, testNozzleId, 'preset_volume', '25.50')

// 5. 快速测试金额预授权
await quickPreauthTest(testDeviceId, testNozzleId, 'preset_amount', '100.00')

// 6. 测试不同的 API 调用方式

// 6.1 直接 API 调用
const volumeRequest = createVolumePreauth(testDeviceId, '30.00', {
  nozzleId: testNozzleId,
  employeeId: '001'
})
const response1 = await fccV2API.waynePreauth(volumeRequest)
console.log('waynePreauth 响应:', response1)

// 6.2 便捷方法别名
const amountRequest = createAmountPreauth(testDeviceId, '150.00', {
  nozzleId: testNozzleId,
  employeeId: '002'
})
const response2 = await fccV2API.preauth(amountRequest)
console.log('preauth 响应:', response2)

// 6.3 Wayne 命令对象
const response3 = await apiV2.wayne.preauth(volumeRequest)
console.log('wayne.preauth 响应:', response3)

// 6.4 兼容性命令接口
const response4 = await apiV2.commands.execute(testDeviceId, {
  command_type: 'preauth',
  parameters: {
    nozzle_id: testNozzleId,
    auth_type: 'preset_volume',
    volume: '35.00',
    decimals: 2,
    employee_id: '003'
  }
})
console.log('commands.execute 响应:', response4)
`)

console.log(`
🎯 预期结果:
- 所有 API 调用都应该返回成功响应
- 响应应该包含 success: true, message, expires_in 字段
- 后端应该在缓存中存储预授权记录（30秒过期）
- 喷嘴状态应该更新为 'authorized'

⚠️  注意事项:
1. 确保后端 PreAuth 功能已启用 (PREAUTH_ENABLED=true)
2. 使用真实存在的设备和喷嘴 ID
3. 检查后端日志以确认预授权记录被正确存储
4. 预授权记录会在 30 秒后自动过期

🔍 调试提示:
- 检查浏览器网络面板中的 API 请求
- 查看浏览器控制台中的详细日志
- 确认后端 /api/v2/wayne/preauth 端点可访问
- 验证设备和喷嘴 ID 的正确性
`)

console.log('=' .repeat(50))
console.log('✅ 测试脚本准备完成！请在浏览器控制台中运行上述代码。')
