#!/usr/bin/env node

/**
 * PreAuth 修复验证脚本
 * 
 * 验证修复后的 PreAuth 请求是否包含正确的 command 字段
 */

console.log('🔧 PreAuth 修复验证脚本')
console.log('=' .repeat(50))

console.log(`
📋 修复说明:
1. 添加了必需的 'command' 字段到 PreAuthRequest 接口
2. 在所有 PreAuth 请求中设置 command: 'preauth'
3. 修复了后端验证错误："command is required"

🧪 在浏览器控制台中测试修复后的代码:
`)

console.log(`
// 1. 导入修复后的模块
import { fccV2API } from '@/lib/api-client-v2'
import { createVolumePreauth, createAmountPreauth } from '@/lib/preauth-test'

// 2. 测试设备和喷嘴 ID（请替换为实际的设备和喷嘴 ID）
const testDeviceId = 'device_001'  // 替换为实际设备 ID
const testNozzleId = 'nozzle_001'  // 替换为实际喷嘴 ID

// 3. 验证请求结构 - 体积预授权
const volumeRequest = createVolumePreauth(testDeviceId, '25.50', {
  nozzleId: testNozzleId,
  employeeId: '001'
})
console.log('体积预授权请求结构:', volumeRequest)
// 应该包含: { device_id, nozzle_id, command: 'preauth', auth_type: 'preset_volume', volume, decimals, employee_id }

// 4. 验证请求结构 - 金额预授权
const amountRequest = createAmountPreauth(testDeviceId, '100.00', {
  nozzleId: testNozzleId,
  employeeId: '002'
})
console.log('金额预授权请求结构:', amountRequest)
// 应该包含: { device_id, nozzle_id, command: 'preauth', auth_type: 'preset_amount', amount, decimals, employee_id }

// 5. 测试实际 API 调用
try {
  console.log('🚀 发送体积预授权请求...')
  const response1 = await fccV2API.preauth(volumeRequest)
  console.log('✅ 体积预授权成功:', response1)
} catch (error) {
  console.error('❌ 体积预授权失败:', error)
  console.log('请检查设备ID和喷嘴ID是否正确，以及后端是否启用了PreAuth功能')
}

try {
  console.log('🚀 发送金额预授权请求...')
  const response2 = await fccV2API.preauth(amountRequest)
  console.log('✅ 金额预授权成功:', response2)
} catch (error) {
  console.error('❌ 金额预授权失败:', error)
  console.log('请检查设备ID和喷嘴ID是否正确，以及后端是否启用了PreAuth功能')
}

// 6. 验证兼容性 API
try {
  console.log('🚀 测试兼容性命令接口...')
  const { apiV2 } = await import('@/lib/api-client-v2')
  const response3 = await apiV2.commands.execute(testDeviceId, {
    command_type: 'preauth',
    parameters: {
      nozzle_id: testNozzleId,
      auth_type: 'preset_volume',
      volume: '30.00',
      decimals: 2,
      employee_id: '003'
    }
  })
  console.log('✅ 兼容性命令接口成功:', response3)
} catch (error) {
  console.error('❌ 兼容性命令接口失败:', error)
}
`)

console.log(`
🎯 预期结果:
- 所有请求都应该包含 command: 'preauth' 字段
- 不再出现 "command is required" 错误
- API 调用应该成功返回预授权响应

✅ 修复内容:
1. PreAuthRequest 接口添加了 command?: string 字段
2. waynePreauth 方法自动设置 command: 'preauth'
3. 所有辅助函数都包含正确的 command 字段
4. 兼容性 API 也正确处理 command 字段

🔍 调试提示:
- 检查请求负载是否包含 command: 'preauth'
- 确认后端 PreAuth 功能已启用
- 验证设备和喷嘴 ID 的正确性
- 查看网络面板中的实际请求内容
`)

console.log('=' .repeat(50))
console.log('✅ 修复验证脚本准备完成！请在浏览器控制台中运行上述代码。')
