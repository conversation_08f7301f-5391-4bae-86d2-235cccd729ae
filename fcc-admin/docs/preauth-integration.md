# PreAuth 功能集成文档

## 概述

本文档描述了在 fcc-admin 前端项目中集成的 preauth（预授权）功能。该功能允许通过 API 预先设置设备的授权参数，支持预设体积和预设金额两种授权类型。

## 功能特性

- ✅ 支持预设体积授权 (`preset_volume`)
- ✅ 支持预设金额授权 (`preset_amount`)
- ✅ 完整的参数验证
- ✅ 多种 API 调用方式
- ✅ UI 集成（设备喷嘴操作面板）
- ✅ 错误处理和用户反馈
- ✅ 测试工具和辅助函数

## API 接口

### 后端接口
- **URL**: `POST /api/v2/wayne/preauth`
- **功能**: 创建预授权记录，缓存30秒，用于后续DC3事件匹配

### 前端集成

#### 1. TypeScript 类型定义

```typescript
// PreAuth 授权类型
export type PreAuthType = 'preset_volume' | 'preset_amount'

// PreAuth 请求参数
export interface PreAuthRequest {
  device_id: string
  nozzle_id?: string
  command?: string  // 继承自 WayneCommandRequest，自动设置为 "preauth"
  auth_type: PreAuthType
  volume?: string | number
  amount?: string | number
  decimals?: number
  employee_id?: string
}

// PreAuth 响应
export interface PreAuthResponse {
  success: boolean
  message: string
  expires_in: number
}
```

#### 2. API 客户端方法

##### 主要方法
```typescript
// 直接调用 preauth API
await fccV2API.waynePreauth(request: PreAuthRequest): Promise<PreAuthResponse>

// 便捷方法别名
await fccV2API.preauth(request: PreAuthRequest): Promise<PreAuthResponse>
```

##### Wayne 命令对象
```typescript
// 通过 Wayne 命令对象调用
await apiV2.wayne.preauth(request: PreAuthRequest): Promise<PreAuthResponse>
```

##### 兼容性 API
```typescript
// 通过兼容性命令接口调用
await apiV2.commands.execute(deviceId: string, {
  command_type: 'preauth',
  parameters: {
    nozzle_id?: string,
    auth_type: 'preset_volume' | 'preset_amount',
    volume?: string,
    amount?: string,
    decimals?: number,
    employee_id?: string
  }
})
```

## 使用示例

### 1. 预设体积授权

```typescript
import { fccV2API, PreAuthRequest } from '@/lib/api-client-v2'

const volumeRequest: PreAuthRequest = {
  device_id: 'device_001',
  nozzle_id: 'nozzle_001',
  auth_type: 'preset_volume',
  volume: '20.50',
  decimals: 2,
  employee_id: '001'
}

try {
  const response = await fccV2API.preauth(volumeRequest)
  console.log('预授权成功:', response.message)
  console.log('过期时间:', response.expires_in, '秒')
} catch (error) {
  console.error('预授权失败:', error)
}
```

### 2. 预设金额授权

```typescript
const amountRequest: PreAuthRequest = {
  device_id: 'device_001',
  nozzle_id: 'nozzle_002',
  auth_type: 'preset_amount',
  amount: '100.00',
  decimals: 2,
  employee_id: '002'
}

try {
  const response = await fccV2API.preauth(amountRequest)
  console.log('预授权成功:', response.message)
} catch (error) {
  console.error('预授权失败:', error)
}
```

### 3. 使用辅助函数

```typescript
import { createVolumePreauth, createAmountPreauth } from '@/lib/preauth-test'

// 创建体积预授权请求
const volumeRequest = createVolumePreauth('device_001', '25.00', {
  nozzleId: 'nozzle_001',
  employeeId: '001'
})

// 创建金额预授权请求
const amountRequest = createAmountPreauth('device_001', '150.00', {
  nozzleId: 'nozzle_002',
  employeeId: '002'
})
```

## UI 集成

### DeviceWithNozzles 组件

在设备喷嘴操作面板中添加了预授权功能：

- 🔐⛽ **PreAuth Volume**: 预设体积授权按钮
- 🔐💵 **PreAuth Amount**: 预设金额授权按钮

用户可以通过点击这些按钮来设置预授权参数，系统会弹出输入框要求用户输入相关信息。

## 测试

### 集成测试

使用提供的测试文件验证集成：

```typescript
import { testPreauthIntegration } from '@/lib/preauth-test'

// 运行集成测试
await testPreauthIntegration('device_001', 'nozzle_001')
```

### 快速测试

```typescript
import { quickPreauthTest } from '@/lib/preauth-test'

// 快速测试体积预授权
await quickPreauthTest('device_001', 'nozzle_001', 'preset_volume', '25.50')

// 快速测试金额预授权
await quickPreauthTest('device_001', 'nozzle_001', 'preset_amount', '100.00')
```

## 文件结构

```
fcc-admin/
├── lib/
│   ├── api-client-v2.ts          # API 客户端（包含 PreAuth 类型和方法）
│   └── preauth-test.ts           # PreAuth 测试工具和辅助函数
├── components/ui/
│   └── DeviceWithNozzles.tsx     # 设备喷嘴组件（包含 PreAuth UI）
├── scripts/
│   └── test-preauth.js           # PreAuth 测试脚本
└── docs/
    └── preauth-integration.md    # 本文档
```

## 参数验证

### 必需参数
- `device_id`: 设备ID
- `auth_type`: 授权类型（'preset_volume' 或 'preset_amount'）

### 条件必需参数
- 当 `auth_type` 为 'preset_volume' 时，`volume` 参数必需
- 当 `auth_type` 为 'preset_amount' 时，`amount` 参数必需

### 可选参数
- `nozzle_id`: 喷嘴ID
- `decimals`: 小数位数（默认为2）
- `employee_id`: 员工ID（默认为'001'）

## 错误处理

API 客户端包含完整的参数验证和错误处理：

```typescript
try {
  const response = await fccV2API.preauth(request)
  // 处理成功响应
} catch (error) {
  if (error instanceof FCCV2APIError) {
    console.error('API 错误:', error.message)
    console.error('状态码:', error.status)
  } else {
    console.error('网络错误:', error.message)
  }
}
```

## 注意事项

1. **缓存时间**: 预授权记录在后端缓存30秒，过期后自动清除
2. **参数验证**: 前端会进行完整的参数验证，确保请求格式正确
3. **错误处理**: 所有 API 调用都包含完整的错误处理机制
4. **日志记录**: API 调用会记录详细的调试日志，便于问题排查
5. **兼容性**: 支持多种调用方式，与现有 Wayne 命令体系完全兼容
6. **UI 反馈**: 用户操作会有明确的成功/失败反馈

## 开发和调试

1. 确保后端 PreAuth 功能已启用 (`PREAUTH_ENABLED=true`)
2. 使用浏览器开发者工具查看网络请求和控制台日志
3. 运行测试脚本验证功能正常工作
4. 检查后端日志确认预授权记录被正确存储和消费

## 故障排除

### 常见错误及解决方案

#### 1. "command is required" 错误
**问题**: API 返回 "预授权参数错误: command is required"
**原因**: PreAuthRequest 继承自 WayneCommandRequest，需要 command 字段
**解决**: 确保请求包含 `command: 'preauth'` 字段（已在 API 客户端中自动处理）

#### 2. "nozzle_id is required for preauth" 错误
**问题**: 后端要求必须提供 nozzle_id
**解决**: 在 PreAuth 请求中必须包含有效的 nozzle_id

#### 3. "设备不存在或未启动" 错误
**问题**: 指定的设备 ID 不存在或设备未在线
**解决**: 检查设备 ID 是否正确，确保设备在线

#### 4. "预授权功能未启用" 错误
**问题**: 后端 PreAuth 功能未启用
**解决**: 在后端配置中设置 `PREAUTH_ENABLED=true`

#### 5. "查询喷嘴失败" 错误
**问题**: 指定的喷嘴 ID 不存在
**解决**: 使用正确的喷嘴 ID，可以通过设备喷嘴列表 API 获取

### 调试步骤

1. **检查请求格式**:
   ```javascript
   // 正确的请求应该包含这些字段
   {
     "device_id": "device_001",
     "nozzle_id": "nozzle_001",
     "command": "preauth",
     "auth_type": "preset_volume",
     "volume": "25.50",
     "decimals": 2,
     "employee_id": "001"
   }
   ```

2. **验证后端配置**:
   - 检查 `PREAUTH_ENABLED=true`
   - 确认设备和喷嘴存在
   - 验证数据库连接正常

3. **查看网络请求**:
   - 打开浏览器开发者工具
   - 查看 Network 面板中的 `/api/v2/wayne/preauth` 请求
   - 检查请求头、请求体和响应

4. **检查后端日志**:
   - 查看预授权相关的日志信息
   - 确认缓存操作是否成功
   - 验证喷嘴状态更新

## 下一步

- [ ] 添加更多的 UI 交互改进（如模态框替代 prompt）
- [ ] 实现预授权状态的实时显示
- [ ] 添加预授权历史记录查看功能
- [ ] 集成到更多的设备管理界面
