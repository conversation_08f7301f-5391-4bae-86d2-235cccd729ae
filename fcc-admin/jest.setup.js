// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Mock fetch globally for all tests
global.fetch = jest.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // uncomment to ignore a specific log level
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3003',
    origin: 'http://localhost:3003',
    protocol: 'http:',
    host: 'localhost:3003',
    hostname: 'localhost',
    port: '3003',
    pathname: '/',
    search: '',
    hash: '',
    assign: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn(),
  },
  writable: true,
})

// Mock environment variables
process.env.NEXT_PUBLIC_FCC_API = 'http://localhost:8080/api/v2'

// Setup after each test
afterEach(() => {
  // Reset all mocks after each test
  jest.clearAllMocks()
  
  // Reset fetch mock
  if (global.fetch) {
    (global.fetch as jest.Mock).mockClear()
  }
})

// Global test helpers
global.testHelpers = {
  createMockDevice: (overrides = {}) => ({
    id: 'test-device-001',
    name: 'Test Device',
    type: 'dart_pump',
    device_address: 80,
    controller_id: 'ctrl-001',
    station_id: 'station-001',
    config: {
      timeout: 5000,
      retry_count: 3,
      monitor_interval: 30000,
      protocol_config: {},
      device_params: {},
      extensions: {}
    },
    status: 'online',
    health: 'healthy',
    capabilities: {
      supported_commands: ['authorize', 'stop'],
      supported_data_types: ['fuel_data']
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    ...overrides
  }),
  
  createMockResponse: (data, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    json: async () => data,
    text: async () => JSON.stringify(data)
  }),
  
  createMockWayneResponse: (overrides = {}) => ({
    command_id: 'cmd-123',
    device_id: 'device-001',
    command: 'authorize',
    success: true,
    execution_time_ms: 25,
    submitted_at: '2024-01-01T00:00:00Z',
    completed_at: '2024-01-01T00:00:00.025Z',
    protocol_info: {
      protocol: 'Wayne DART v1.3',
      transaction_type: 'CD1',
      transaction_code: '0x06',
      response_time_ms: 25
    },
    ...overrides
  })
} 