{"name": "fcc-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "dev:3000": "next dev -p 3000", "dev:3001": "next dev -p 3001", "dev:3002": "next dev -p 3002", "dev:4000": "next dev -p 4000", "build": "next build", "start": "next start -p 3003", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "14.0.0", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/jest": "^29", "autoprefixer": "^10", "eslint": "^8", "eslint-config-next": "14.0.0", "jest": "^29", "jest-environment-jsdom": "^29", "postcss": "^8", "tailwindcss": "^3", "typescript": "^5", "ts-jest": "^29"}}