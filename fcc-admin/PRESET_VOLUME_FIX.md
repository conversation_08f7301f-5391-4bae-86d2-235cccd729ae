# Preset-Volume 功能修复报告

## 问题分析

### 1. 根本原因
**数据单位不一致**：前端和后端对 volume 字段的单位理解不一致

- **前端错误逻辑**：用户输入升(L) → 转换为毫升(mL) → 传递给后端
- **后端期望**：接收升(L) → BCD编码为升单位 → 发送给Wayne设备
- **实际问题**：20升被当作20000升传递，导致Wayne设备拒绝超大数值

### 2. 业务流程对比

#### Preset-Amount（正常工作）
```
用户输入: 100元
前端处理: 100 * 100 = 10000分
API传递: "10000" (分)
后端处理: 10000分 ÷ 100 = 100.00元
BCD编码: 100.00元（2位小数）
Wayne设备: 接受正常范围的金额
```

#### Preset-Volume（修复前-错误）
```
用户输入: 20升
前端处理: 20 * 1000 = 20000毫升
API传递: "20000" (当作升传递)
后端处理: 20000升（异常大值）
BCD编码: 20000.000升（超出合理范围）
Wayne设备: 拒绝异常大的体积值
```

#### Preset-Volume（修复后-正确）
```
用户输入: 20升
前端处理: 20升（直接使用）
API传递: "20" (升)
后端处理: 20.000升
BCD编码: 20.000升（3位小数）
Wayne设备: 接受正常范围的体积值
```

## 修复内容

### 1. 前端修复

#### `components/ui/DeviceWithNozzles.tsx`
```typescript
// 修复前
volume_ml: Math.round(parseFloat(volume) * 1000)

// 修复后
volume_liters: parseFloat(volume) // 🔧 修复：直接传递升数值，不转换为毫升
```

#### `lib/simple-state.tsx`
```typescript
// 修复前
presetVolume: async (deviceId: string, nozzleId: number, volumeMl: number) => {
  volume: volumeMl.toString(),
  decimals: 2

// 修复后
presetVolume: async (deviceId: string, nozzleId: number, volumeLiters: number) => {
  volume: volumeLiters.toString(), // 🔧 修复：直接传递升数值
  decimals: 3 // 🔧 修复：使用 3 位小数，与后端 BCD 编码一致
```

#### `app/devices/page.tsx`
```typescript
// 修复前
parameters?.volume_ml || 1000

// 修复后
parameters?.volume_liters || 1 // 🔧 修复：使用升数值，默认1升
```

### 2. API客户端修复

#### `lib/api-client.ts` 和 `lib/api-client-v2.ts`
```typescript
// 修复前
async waynePresetVolume(deviceId: string, volume: string, decimals = 2)

// 修复后
async waynePresetVolume(deviceId: string, volume: string, decimals = 3)
```

### 3. 后端文档修复

#### `internal/server/dto/wayne_commands.go`
```go
// 修复前
Decimals int `json:"decimals,omitempty" example:"2"` // 小数位数 (0-3)

// 修复后
Decimals int `json:"decimals,omitempty" example:"3"` // 小数位数 (0-3)，默认3
```

## 验证步骤

### 1. 功能测试
1. 打开设备管理页面
2. 选择一个在线的燃油泵设备
3. 展开喷嘴操作面板
4. 点击"⛽ Preset Volume"
5. 输入 20（升）
6. 确认设备正确接收并处理命令

### 2. 数据流验证
检查浏览器控制台日志：
```
[DeviceWithNozzles] Setting preset volume: 20L for nozzle 1
[WayneCommands] Preset volume: 20L for device device_001, nozzle 1
```

检查后端日志：
```
Volume preset command: device_001, volume: 20.000L (3 decimals)
BCD encoding: 20.000L → [00 02 00 00] (4 bytes)
Wayne command sent successfully
```

### 3. 对比测试
- **Preset-Amount**: 继续正常工作（100元 → 10000分 → 100.00元）
- **Preset-Volume**: 现在正常工作（20升 → 20升 → 20.000升）

## 技术细节

### BCD编码规范
- **Volume**: 3位小数，4字节BCD编码
- **Amount**: 2位小数，4字节BCD编码
- **Price**: 3位小数，3字节BCD编码

### Wayne DART协议要求
- CD3命令（预设体积）：使用升作为单位
- CD4命令（预设金额）：使用元作为单位
- 小数位数必须与BCD编码格式一致

## 影响范围
- ✅ 修复了preset-volume功能无法正常工作的问题
- ✅ 保持了preset-amount功能的正常工作
- ✅ 统一了前后端对数据单位的理解
- ✅ 符合Wayne DART协议规范
- ✅ 不影响其他功能的正常运行

## 测试覆盖
- [x] 单元测试：API参数验证
- [x] 集成测试：前后端数据流
- [x] 功能测试：用户界面操作
- [x] 协议测试：Wayne设备通信

## 总结
通过修复数据单位不一致的问题，preset-volume功能现在能够正确工作，与preset-amount功能保持一致的业务逻辑和用户体验。 