'use client'

import React from 'react'
import { useDevices, useControllers, usePollingSettings } from '@/lib/simple-state'
import { DeviceStatusBadge, DeviceHealthBadge, DeviceTypeIcon } from '@/components/ui/DeviceStatus'

export default function DashboardPage() {
  const { devices, loading: devicesLoading, error: devicesError } = useDevices()
  const { controllers, loading: controllersLoading } = useControllers()
  const { enabled: pollingEnabled, interval, lastUpdate } = usePollingSettings()

  // 计算统计数据
  const totalDevices = devices.length
  const onlineDevices = devices.filter(d => d.status === 'online').length
  const healthyDevices = devices.filter(d => d.health === 'healthy').length
  const totalControllers = controllers.length
  const onlineControllers = controllers.filter(c => c.status === 'online').length

  // 按类型统计设备
  const devicesByType = devices.reduce((acc, device) => {
    const type = device.type
    if (!acc[type]) {
      acc[type] = { total: 0, online: 0, healthy: 0 }
    }
    acc[type].total++
    if (device.status === 'online') acc[type].online++
    if (device.health === 'healthy') acc[type].healthy++
    return acc
  }, {} as Record<string, { total: number; online: number; healthy: number }>)

  // 最近的设备（按最后更新时间排序）
  const recentDevices = devices
    .filter(d => d.last_seen)
    .sort((a, b) => new Date(b.last_seen!).getTime() - new Date(a.last_seen!).getTime())
    .slice(0, 5)

  if (devicesLoading && devices.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div className="text-lg text-gray-600 mt-4">Loading dashboard...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">System Dashboard</h1>
        <p className="text-gray-600">Wayne DART Device Management System Overview</p>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">📱</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{totalDevices}</div>
              <div className="text-sm text-gray-500">Total Devices</div>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="text-green-600 text-sm font-medium">
              {onlineDevices} online ({totalDevices > 0 ? Math.round((onlineDevices / totalDevices) * 100) : 0}%)
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">📡</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{totalControllers}</div>
              <div className="text-sm text-gray-500">Controllers</div>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="text-green-600 text-sm font-medium">
              {onlineControllers} online ({totalControllers > 0 ? Math.round((onlineControllers / totalControllers) * 100) : 0}%)
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">💚</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{healthyDevices}</div>
              <div className="text-sm text-gray-500">Healthy Devices</div>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="text-green-600 text-sm font-medium">
              {totalDevices > 0 ? Math.round((healthyDevices / totalDevices) * 100) : 0}% healthy
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">🔄</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {pollingEnabled ? 'ON' : 'OFF'}
              </div>
              <div className="text-sm text-gray-500">Real-time Updates</div>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="text-blue-600 text-sm font-medium">
              {pollingEnabled ? `Every ${interval/1000}s` : 'Disabled'}
            </div>
          </div>
        </div>
      </div>

      {/* Device Types Overview */}
      {Object.keys(devicesByType).length > 0 && (
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Device Types Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(devicesByType).map(([type, stats]) => (
              <div key={type} className="flex items-center p-4 bg-gray-50 rounded-lg">
                <DeviceTypeIcon type={type} size="small" />
                <div className="ml-3 flex-1">
                  <div className="font-medium text-gray-900">
                    {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </div>
                  <div className="text-sm text-gray-500">
                    {stats.total} total • {stats.online} online • {stats.healthy} healthy
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">{stats.total}</div>
                  <div className="text-xs text-gray-500">
                    {stats.total > 0 ? Math.round((stats.online / stats.total) * 100) : 0}% up
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Devices */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recently Active Devices</h3>
          {recentDevices.length > 0 ? (
            <div className="space-y-3">
              {recentDevices.map((device) => (
                <div key={device.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <DeviceTypeIcon type={device.type} size="small" />
                    <div>
                      <div className="font-medium text-gray-900">{device.name}</div>
                      <div className="text-sm text-gray-500">
                        {device.station_id} • 0x{device.device_address.toString(16).toUpperCase().padStart(2, '0')}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <DeviceStatusBadge status={device.status} size="small" />
                    <DeviceHealthBadge health={device.health} size="small" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              <div className="text-2xl mb-2">📱</div>
              <div>No device activity yet</div>
            </div>
          )}
        </div>

        {/* System Information */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">Last Update</span>
              <span className="font-medium">
                {lastUpdate ? lastUpdate.toLocaleString() : 'Never'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">Polling Status</span>
              <span className={`font-medium ${pollingEnabled ? 'text-green-600' : 'text-gray-600'}`}>
                {pollingEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">Polling Interval</span>
              <span className="font-medium">{interval/1000}s</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">API Status</span>
              <span className={`font-medium ${devicesError ? 'text-red-600' : 'text-green-600'}`}>
                {devicesError ? 'Error' : 'Connected'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-600">Version</span>
              <span className="font-medium">FCC Admin v1.0.0</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <a
            href="/devices"
            className="flex items-center justify-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">📱</div>
              <div className="text-sm font-medium text-blue-900">Manage Devices</div>
            </div>
          </a>
          <a
            href="/controllers"
            className="flex items-center justify-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">📡</div>
              <div className="text-sm font-medium text-green-900">Controllers</div>
            </div>
          </a>
          <button
            onClick={() => window.location.reload()}
            className="flex items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">🔄</div>
              <div className="text-sm font-medium text-gray-900">Refresh</div>
            </div>
          </button>
          <div className="flex items-center justify-center p-4 bg-purple-50 rounded-lg">
            <div className="text-center">
              <div className="text-2xl mb-2">⚙️</div>
              <div className="text-sm font-medium text-purple-900">Settings</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 