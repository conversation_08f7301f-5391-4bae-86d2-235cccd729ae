import type { Metadata } from 'next'
import './globals.css'
import { SimpleProvider } from '@/lib/simple-state'
import { NotificationList } from '@/components/ui/Notifications'
import { ApiConfigProvider } from '@/components/ui/ApiConfigProvider'

export const metadata: Metadata = {
  title: 'FCC Admin - Device Management System',
  description: 'FCC Wayne DART Device Management and Monitoring System',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        <SimpleProvider>
          <ApiConfigProvider>
            <div className="min-h-screen bg-gray-50">
              <nav className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="flex justify-between h-16">
                    <div className="flex items-center">
                      <h1 className="text-xl font-bold text-gray-900">
                        🏭 FCC Admin
                      </h1>
                      <nav className="ml-8 flex space-x-8">
                        <a
                          href="/"
                          className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                        >
                          📊 Dashboard
                        </a>
                        <a
                          href="/controllers"
                          className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                        >
                          📡 Controllers
                        </a>
                        <a
                          href="/devices"
                          className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                        >
                          📱 Devices
                        </a>
                        <a
                          href="/transactions"
                          className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                        >
                          💳 Transactions
                        </a>
                        <a
                          href="/fuel-grades"
                          className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                        >
                          ⛽ Fuel Grades
                        </a>
                      </nav>
                    </div>
                    <div className="flex items-center space-x-4">
                      <StatusIndicator />
                    </div>
                  </div>
                </div>
              </nav>

              <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                  {children}
                </div>
              </main>
            </div>
            <NotificationList />
          </ApiConfigProvider>
        </SimpleProvider>
      </body>
    </html>
  )
}

// 状态指示器组件
function StatusIndicator() {
  return (
    <div className="flex items-center space-x-2 text-sm">
      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      <span className="text-gray-600">System Active</span>
    </div>
  )
} 