'use client'

import React, { useState, useEffect } from 'react'
import { FuelGradesList } from '@/components/ui/FuelGradesList'
import { FuelSyncPanel } from '@/components/ui/FuelSyncPanel'
import { FuelGradeModal } from '@/components/ui/FuelGradeModal'
import { useFuelGrades, useFuelSync } from '@/lib/simple-state'
import { useToast } from '@/components/ui/Notifications'

export default function FuelGradesPage() {
  const { fuelGrades, loading, error, refresh } = useFuelGrades()
  const { syncStatus, triggerSync, getSyncHistory } = useFuelSync()
  const toast = useToast()
  
  const [showModal, setShowModal] = useState(false)
  const [selectedGrade, setSelectedGrade] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'active' | 'inactive'>('all')

  // 过滤油品数据
  const filteredGrades = React.useMemo(() => {
    let filtered = fuelGrades || []
    
    // 按搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(grade => 
        grade.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        grade.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (grade.description && grade.description.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }
    
    // 按状态过滤
    if (filterType === 'active') {
      filtered = filtered.filter(grade => !grade.deleted_at)
    } else if (filterType === 'inactive') {
      filtered = filtered.filter(grade => grade.deleted_at)
    }
    
    return filtered
  }, [fuelGrades, searchTerm, filterType])

  // 统计数据
  const stats = React.useMemo(() => {
    const total = fuelGrades?.length || 0
    const active = fuelGrades?.filter(g => !g.deleted_at).length || 0
    const inactive = total - active
    
    return { total, active, inactive }
  }, [fuelGrades])

  const handleEdit = (grade: any) => {
    setSelectedGrade(grade)
    setShowModal(true)
  }

  const handleAdd = () => {
    setSelectedGrade(null)
    setShowModal(true)
  }

  const handleDelete = async (gradeId: string) => {
    if (!confirm('确定要删除这个油品吗？')) return
    
    try {
      // 这里应该调用删除API
      toast.success('油品删除成功')
      await refresh()
    } catch (error) {
      toast.error('删除失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  const handleSync = async () => {
    try {
      await triggerSync()
      toast.success('同步已触发')
      await refresh()
    } catch (error) {
      toast.error('同步失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  const handleRefresh = async () => {
    try {
      await refresh()
      toast.success('数据刷新成功')
    } catch (error) {
      toast.error('刷新失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  if (loading && (!fuelGrades || fuelGrades.length === 0)) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div className="text-lg text-gray-600 mt-4">加载油品数据...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">油品管理</h1>
          <p className="text-gray-600">管理加油站油品信息和价格</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            🔄 刷新
          </button>
          <button
            onClick={handleAdd}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
          >
            ➕ 添加油品
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">⛽</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-500">总油品数</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">✅</div>
            <div>
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-sm text-gray-500">活跃油品</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="text-3xl mr-4">❌</div>
            <div>
              <div className="text-2xl font-bold text-red-600">{stats.inactive}</div>
              <div className="text-sm text-gray-500">已停用</div>
            </div>
          </div>
        </div>
      </div>

      {/* 同步面板 */}
      <FuelSyncPanel 
        syncStatus={syncStatus}
        onSync={handleSync}
        onRefresh={handleRefresh}
      />

      {/* 搜索和过滤 */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex-1 max-w-md">
            <input
              type="text"
              placeholder="搜索油品名称、类型或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex space-x-2">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">全部</option>
              <option value="active">活跃</option>
              <option value="inactive">已停用</option>
            </select>
          </div>
        </div>
      </div>

      {/* 油品列表 */}
      <FuelGradesList
        grades={filteredGrades}
        loading={loading}
        error={error}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* 编辑/添加模态框 */}
      {showModal && (
        <FuelGradeModal
          grade={selectedGrade}
          onClose={() => setShowModal(false)}
          onSave={async (gradeData) => {
            try {
              // 这里应该调用保存API
              toast.success(selectedGrade ? '油品更新成功' : '油品添加成功')
              setShowModal(false)
              await refresh()
            } catch (error) {
              toast.error('保存失败: ' + (error instanceof Error ? error.message : '未知错误'))
            }
          }}
        />
      )}
    </div>
  )
} 