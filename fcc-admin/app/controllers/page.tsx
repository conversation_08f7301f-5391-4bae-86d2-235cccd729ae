'use client'

import React, { useState } from 'react'
import { ControllerCreateForm } from '@/components/ui/DeviceStatus'
import { useToast } from '@/components/ui/Notifications'
import { useControllers } from '@/lib/simple-state'

export default function ControllersPage() {
  const { controllers, loading, error, refresh, createController, discoverDevices } = useControllers()
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [creating, setCreating] = useState(false)
  const toast = useToast()

  const handleCreateController = async (controllerData: any) => {
    try {
      setCreating(true)
      await createController(controllerData)
      setShowCreateForm(false)
    } catch (error) {
      // Error notification is already handled in useControllers
      console.error('Failed to create controller:', error)
    } finally {
      setCreating(false)
    }
  }

  const handleDiscoverDevices = async (controllerId: string) => {
    try {
      await discoverDevices(controllerId)
    } catch (error) {
      // Error notification is already handled in useControllers
      console.error('Failed to discover devices:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800'
      case 'offline': return 'bg-gray-100 text-gray-800'
      case 'error': return 'bg-red-100 text-red-800'
      case 'maintenance': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'bg-green-100 text-green-800'
      case 'good': return 'bg-blue-100 text-blue-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'critical': return 'bg-red-100 text-red-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getProtocolBadge = (protocol: string) => {
    const colors = {
      'wayne_dart': 'bg-purple-100 text-purple-800',
      'dart': 'bg-blue-100 text-blue-800',
      'tcp': 'bg-green-100 text-green-800',
      'serial': 'bg-orange-100 text-orange-800',
    }
    return colors[protocol as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="text-lg text-gray-600">加载中...</div>
          <div className="text-sm text-gray-400 mt-2">正在获取控制器数据</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="text-red-800">
          <h3 className="font-semibold">加载控制器失败</h3>
          <p className="mt-2 text-sm">{error}</p>
          <button
            onClick={refresh}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Wayne控制器管理</h1>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowCreateForm(true)}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <span>➕</span>
            <span>创建控制器</span>
          </button>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            🔄 刷新
          </button>
        </div>
      </div>

      {controllers.length === 0 ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <div className="text-gray-600">
            <div className="text-lg mb-2">📡</div>
            <div className="font-medium">暂无控制器</div>
            <div className="text-sm mt-2">请检查FCC服务配置或添加控制器</div>
          </div>
        </div>
      ) : (
        <div className="grid gap-6">
          {controllers.map((controller) => (
            <div key={controller.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {controller.name}
                    </h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(controller.status)}`}>
                      {controller.status}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getHealthColor(controller.health || 'unknown')}`}>
                      {controller.health || 'unknown'}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getProtocolBadge(controller.protocol || 'unknown')}`}>
                      {controller.protocol || 'unknown'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">控制器ID</div>
                      <div className="font-mono text-xs">{controller.id}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">地址</div>
                      <div className="font-mono">{controller.address || '-'}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">站点ID</div>
                      <div>{controller.station_id || '-'}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">最后在线</div>
                      <div className="text-xs">
                        {controller.last_seen
                          ? new Date(controller.last_seen).toLocaleString()
                          : 'Never'}
                      </div>
                    </div>
                  </div>

                  {/* Configuration Details */}
                  {controller.config && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="text-xs text-gray-500 mb-2">Configuration</div>
                      <div className="grid grid-cols-2 gap-2">
                        {controller.config.serial_port && (
                          <div className="text-xs">
                            <span className="text-gray-500">Port:</span>
                            <span className="ml-1 font-mono">{controller.config.serial_port}</span>
                          </div>
                        )}
                        {controller.config.baud_rate && (
                          <div className="text-xs">
                            <span className="text-gray-500">Baud Rate:</span>
                            <span className="ml-1">{controller.config.baud_rate}</span>
                          </div>
                        )}
                        {controller.config.host && (
                          <div className="text-xs">
                            <span className="text-gray-500">Host:</span>
                            <span className="ml-1 font-mono">{controller.config.host}:{controller.config.port}</span>
                          </div>
                        )}
                        {controller.config.timeout && (
                          <div className="text-xs">
                            <span className="text-gray-500">Timeout:</span>
                            <span className="ml-1">{controller.config.timeout}ms</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-2 ml-4">
                  <button
                    onClick={() => handleDiscoverDevices(controller.id)}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                    disabled={controller.status !== 'online'}
                  >
                    🔍 发现设备
                  </button>
                  <button
                    onClick={() => window.location.href = `/devices?controller_id=${controller.id}`}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    📱 查看设备
                  </button>
                </div>
              </div>

              {/* 设备数量信息 */}
              {controller.devices && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600">
                    关联设备: {controller.devices.length} 个
                    {controller.devices.length > 0 && (
                      <span className="ml-2">
                        (在线: {controller.devices.filter(d => d.status === 'online').length})
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 页面底部信息 */}
      <div className="text-center text-sm text-gray-500">
        共 {controllers.length} 个控制器 | 在线: {controllers.filter(c => c.status === 'online').length}
      </div>

      {/* Controller Creation Modal */}
      {showCreateForm && (
        <ControllerCreateForm
          onSubmit={handleCreateController}
          onCancel={() => setShowCreateForm(false)}
          loading={creating}
        />
      )}
    </div>
  )
} 