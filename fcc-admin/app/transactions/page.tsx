'use client'

import React, { useState, useEffect } from 'react'
import { apiV2 } from '@/lib/api-client-v2'
import type { 
  TransactionV2, 
  TransactionFilter, 
  TransactionListResult,
  TransactionStats,
  TransactionStatsFilter,
  PumpReadingsResponse,
  PumpIssuesSummary 
} from '@/lib/api-client-v2'

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<TransactionV2[]>([])
  const [stats, setStats] = useState<TransactionStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<TransactionFilter>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'DESC'
  })
  const [total, setTotal] = useState(0)
  const [hasMore, setHasMore] = useState(false)
  const [showPumpReadings, setShowPumpReadings] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionV2 | null>(null)
  const [pumpReadings, setPumpReadings] = useState<PumpReadingsResponse | null>(null)
  const [pumpIssues, setPumpIssues] = useState<PumpIssuesSummary | null>(null)
  const [showPumpIssues, setShowPumpIssues] = useState(false)

  // 加载交易数据
  const loadTransactions = async (currentFilter?: TransactionFilter) => {
    try {
      setLoading(true)
      setError(null)
      
      const filterToUse = currentFilter || filter
      console.log('[TransactionsPage] Loading transactions with filter:', filterToUse)
      
      const result: TransactionListResult = await apiV2.transactions.list(filterToUse)
      console.log('[TransactionsPage] Transactions result:', result)
      
      setTransactions(result.transactions)
      setTotal(result.total)
      setHasMore(result.has_more)
      
    } catch (err) {
      console.error('[TransactionsPage] Failed to load transactions:', err)
      setError(err instanceof Error ? err.message : 'Failed to load transactions')
    } finally {
      setLoading(false)
    }
  }

  // 加载统计数据
  const loadStats = async (statsFilter?: TransactionStatsFilter) => {
    try {
      const result = await apiV2.transactions.getStats(statsFilter)
      console.log('[TransactionsPage] Stats result:', result)
      setStats(result)
    } catch (err) {
      console.error('[TransactionsPage] Failed to load stats:', err)
    }
  }

  // 页面加载时获取数据
  useEffect(() => {
    // 从 URL 查询参数中读取初始过滤器
    const urlParams = new URLSearchParams(window.location.search)
    const deviceId = urlParams.get('device_id')
    
    if (deviceId) {
      const initialFilter = { ...filter, device_id: deviceId }
      setFilter(initialFilter)
      loadTransactions(initialFilter)
      loadStats({ device_id: deviceId })
    } else {
      loadTransactions()
      loadStats()
    }
  }, [])

  // 处理分页
  const handlePageChange = (newPage: number) => {
    const newFilter = { ...filter, page: newPage }
    setFilter(newFilter)
    loadTransactions(newFilter)
  }

  // 处理过滤器变化
  const handleFilterChange = (newFilter: Partial<TransactionFilter>) => {
    const updatedFilter = { ...filter, ...newFilter, page: 1 }
    setFilter(updatedFilter)
    loadTransactions(updatedFilter)
  }

  // 刷新数据
  const handleRefresh = () => {
    loadTransactions()
    loadStats()
  }

  // 加载泵码详情
  const loadPumpReadings = async (transactionId: string) => {
    try {
      setLoading(true)
      const readings = await apiV2.transactions.getPumpReadings(transactionId)
      setPumpReadings(readings)
    } catch (err) {
      console.error('[TransactionsPage] Failed to load pump readings:', err)
      setError(err instanceof Error ? err.message : 'Failed to load pump readings')
    } finally {
      setLoading(false)
    }
  }

  // 加载泵码问题汇总
  const loadPumpIssues = async () => {
    try {
      setLoading(true)
      const issues = await apiV2.transactions.getPumpIssues({
        device_id: filter.device_id,
        start_time: filter.start_time,
        end_time: filter.end_time
      })
      setPumpIssues(issues)
      setShowPumpIssues(true)
    } catch (err) {
      console.error('[TransactionsPage] Failed to load pump issues:', err)
      setError(err instanceof Error ? err.message : 'Failed to load pump issues')
    } finally {
      setLoading(false)
    }
  }

  // 查看交易泵码详情
  const handleViewPumpReadings = async (transaction: TransactionV2) => {
    setSelectedTransaction(transaction)
    await loadPumpReadings(transaction.id)
    setShowPumpReadings(true)
  }

  // 格式化金额 - 支持字符串和数字类型
  const formatAmount = (amount?: number | string) => {
    if (amount === undefined || amount === null) return '-'
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    if (isNaN(numAmount)) return '-'
    return `Rp ${numAmount.toFixed(0)}`
  }

  // 格式化体积 - 支持字符串和数字类型
  const formatVolume = (volume?: number | string) => {
    if (volume === undefined || volume === null) return '-'
    const numVolume = typeof volume === 'string' ? parseFloat(volume) : volume
    if (isNaN(numVolume)) return '-'
    return `${numVolume.toFixed(3)}L`
  }

  // 格式化时间
  const formatTime = (timeString?: string) => {
    if (!timeString) return '-'
    return new Date(timeString).toLocaleString()
  }

  // 获取状态样式
  const getStatusBadge = (status: string) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full'
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800`
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800`
      case 'failed':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  // 获取交易类型样式
  const getTypeBadge = (type: string) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full'
    switch (type) {
      case 'fuel':
        return `${baseClasses} bg-blue-100 text-blue-800`
      case 'payment':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'refund':
        return `${baseClasses} bg-orange-100 text-orange-800`
      case 'void':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  // 获取泵码质量样式
  const getPumpQualityBadge = (quality?: string) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full'
    switch (quality) {
      case 'good':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'poor':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'bad':
        return `${baseClasses} bg-red-100 text-red-800`
      case 'missing':
        return `${baseClasses} bg-gray-100 text-gray-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-500`
    }
  }

  // 获取数据来源样式
  const getPumpSourceBadge = (source?: string) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full'
    switch (source) {
      case 'DC101':
        return `${baseClasses} bg-blue-100 text-blue-800`
      case 'MANUAL':
        return `${baseClasses} bg-purple-100 text-purple-800`
      case 'CALCULATED':
        return `${baseClasses} bg-orange-100 text-orange-800`
      case 'ESTIMATED':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-500`
    }
  }

  // 检查交易是否有泵码数据
  const hasPumpReadings = (transaction: TransactionV2) => {
    return transaction.start_volume_pump !== undefined || 
           transaction.end_volume_pump !== undefined ||
           transaction.start_amount_pump !== undefined ||
           transaction.end_amount_pump !== undefined
  }

  if (loading && transactions.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div className="text-lg text-gray-600 mt-4">Loading transactions...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transaction Management</h1>
          <p className="text-gray-600">View and manage fuel transactions</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={loadPumpIssues}
            disabled={loading}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 flex items-center space-x-2"
          >
            <span>⚠️</span>
            <span>Pump Issues</span>
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            <span>🔄</span>
            <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="text-3xl mr-4">📊</div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.total_count}</div>
                <div className="text-sm text-gray-500">Total Transactions</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="text-3xl mr-4">⛽</div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{formatVolume(stats.total_volume)}</div>
                <div className="text-sm text-gray-500">Total Volume</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="text-3xl mr-4">💰</div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{formatAmount(stats.total_amount)}</div>
                <div className="text-sm text-gray-500">Total Amount</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="text-3xl mr-4">✅</div>
              <div>
                <div className="text-2xl font-bold text-green-600">{stats.completed_count}</div>
                <div className="text-sm text-gray-500">Completed</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filter Bar */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Device ID
            </label>
            <input
              type="text"
              value={filter.device_id || ''}
              onChange={(e) => handleFilterChange({ device_id: e.target.value || undefined })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Filter by device"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filter.status || ''}
              onChange={(e) => handleFilterChange({ status: e.target.value as any || undefined })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="failed">Failed</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              value={filter.type || ''}
              onChange={(e) => handleFilterChange({ type: e.target.value as any || undefined })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="fuel">Fuel</option>
              <option value="payment">Payment</option>
              <option value="refund">Refund</option>
              <option value="void">Void</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Per Page
            </label>
            <select
              value={filter.limit || 20}
              onChange={(e) => handleFilterChange({ limit: Number(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>

        {/* 泵码过滤器 */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Pump Readings Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pump Quality
              </label>
              <select
                value={filter.pump_reading_quality || ''}
                onChange={(e) => handleFilterChange({ pump_reading_quality: e.target.value || undefined })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Quality</option>
                <option value="good">Good</option>
                <option value="poor">Poor</option>
                <option value="bad">Bad</option>
                <option value="missing">Missing</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Source
              </label>
              <select
                value={filter.pump_reading_source || ''}
                onChange={(e) => handleFilterChange({ pump_reading_source: e.target.value || undefined })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Sources</option>
                <option value="DC101">DC101</option>
                <option value="MANUAL">Manual</option>
                <option value="CALCULATED">Calculated</option>
                <option value="ESTIMATED">Estimated</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Validated
              </label>
              <select
                value={filter.pump_reading_validated === undefined ? '' : filter.pump_reading_validated.toString()}
                onChange={(e) => handleFilterChange({ 
                  pump_reading_validated: e.target.value === '' ? undefined : e.target.value === 'true' 
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All</option>
                <option value="true">Validated</option>
                <option value="false">Not Validated</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Has Pump Data
              </label>
              <select
                value={filter.has_pump_readings === undefined ? '' : filter.has_pump_readings.toString()}
                onChange={(e) => handleFilterChange({ 
                  has_pump_readings: e.target.value === '' ? undefined : e.target.value === 'true' 
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All</option>
                <option value="true">With Pump Data</option>
                <option value="false">Without Pump Data</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">
            <h3 className="font-semibold">Error loading transactions</h3>
            <p className="mt-2 text-sm">{error}</p>
            <button
              onClick={handleRefresh}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Transactions Table */}
      {transactions.length === 0 && !loading ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <div className="text-gray-600">
            <div className="text-4xl mb-4">📊</div>
            <div className="text-lg font-medium">No transactions found</div>
            <div className="text-sm mt-2">
              Try adjusting your filters or check if there are any active pumps
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transaction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Device
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Volume
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pump Readings
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="font-medium text-gray-900">
                          {transaction.id}
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={getTypeBadge(transaction.type)}>
                            {transaction.type}
                          </span>
                          {transaction.nozzle_id && (
                            <span className="text-xs text-gray-500">
                              Nozzle {transaction.nozzle_id}
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{transaction.device_id}</div>
                      {transaction.controller_id && (
                        <div className="text-xs text-gray-500">Controller: {transaction.controller_id}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={getStatusBadge(transaction.status)}>
                        {transaction.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatVolume(transaction.actual_volume)}
                      {transaction.preset_volume && (
                        <div className="text-xs text-gray-500">
                          Preset: {formatVolume(transaction.preset_volume)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatAmount(transaction.actual_amount)}
                      {transaction.unit_price && (
                        <div className="text-xs text-gray-500">
                          @{formatAmount(transaction.unit_price)}/L
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {hasPumpReadings(transaction) ? (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className={getPumpQualityBadge(transaction.pump_reading_quality)}>
                              {transaction.pump_reading_quality || 'unknown'}
                            </span>
                            {transaction.pump_reading_source && (
                              <span className={getPumpSourceBadge(transaction.pump_reading_source)}>
                                {transaction.pump_reading_source}
                              </span>
                            )}
                          </div>
                          {transaction.pump_reading_validated !== undefined && (
                            <div className="text-xs text-gray-600">
                              {transaction.pump_reading_validated ? '✅ Validated' : '❌ Not Validated'}
                            </div>
                          )}
                          {transaction.pump_discrepancy && (
                            <div className="text-xs text-red-600">
                              Discrepancy: {formatVolume(transaction.pump_discrepancy)}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-xs">No pump data</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{formatTime(transaction.created_at)}</div>
                      {transaction.completed_at && (
                        <div className="text-xs">
                          Completed: {formatTime(transaction.completed_at)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        {hasPumpReadings(transaction) && (
                          <button
                            onClick={() => handleViewPumpReadings(transaction)}
                            className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                          >
                            View Pump Data
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange((filter.page || 1) - 1)}
                disabled={!filter.page || filter.page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange((filter.page || 1) + 1)}
                disabled={!hasMore}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">
                    {((filter.page || 1) - 1) * (filter.limit || 20) + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min((filter.page || 1) * (filter.limit || 20), total)}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{total}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => handlePageChange((filter.page || 1) - 1)}
                    disabled={!filter.page || filter.page <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange((filter.page || 1) + 1)}
                    disabled={!hasMore}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 泵码详情模态框 */}
      {showPumpReadings && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900">
                  Pump Readings - Transaction {selectedTransaction.id}
                </h2>
                <button
                  onClick={() => setShowPumpReadings(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              {pumpReadings ? (
                <div className="space-y-6">
                  {/* 基本泵码信息 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800">Volume Pump Readings</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Start Volume:</span>
                          <span className="font-medium">{formatVolume(pumpReadings.pump_readings.start_volume_pump)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">End Volume:</span>
                          <span className="font-medium">{formatVolume(pumpReadings.pump_readings.end_volume_pump)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Difference:</span>
                          <span className="font-medium text-blue-600">{formatVolume(pumpReadings.analysis.volume_difference)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800">Amount Pump Readings</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Start Amount:</span>
                          <span className="font-medium">{formatAmount(pumpReadings.pump_readings.start_amount_pump)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">End Amount:</span>
                          <span className="font-medium">{formatAmount(pumpReadings.pump_readings.end_amount_pump)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Difference:</span>
                          <span className="font-medium text-blue-600">{formatAmount(pumpReadings.analysis.amount_difference)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 数据质量信息 */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Data Quality & Analysis</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <span className="text-gray-600 block mb-1">Data Source:</span>
                        <span className={getPumpSourceBadge(pumpReadings.pump_readings.pump_reading_source)}>
                          {pumpReadings.pump_readings.pump_reading_source || 'Unknown'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600 block mb-1">Quality:</span>
                        <span className={getPumpQualityBadge(pumpReadings.pump_readings.pump_reading_quality)}>
                          {pumpReadings.pump_readings.pump_reading_quality || 'Unknown'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600 block mb-1">Validated:</span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          pumpReadings.pump_readings.pump_reading_validated 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {pumpReadings.pump_readings.pump_reading_validated ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </div>

                    {pumpReadings.analysis.validation_errors && pumpReadings.analysis.validation_errors.length > 0 && (
                      <div className="mt-4">
                        <span className="text-gray-600 block mb-2">Validation Errors:</span>
                        <ul className="list-disc list-inside text-red-600 text-sm space-y-1">
                          {pumpReadings.analysis.validation_errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* 元数据 */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Metadata</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Data Completeness:</span>
                        <div className="font-medium">{
                          pumpReadings.metadata.data_completeness 
                            ? `${(parseFloat(pumpReadings.metadata.data_completeness.toString()) * 100).toFixed(1)}%`
                            : 'N/A'
                        }</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Quality Score:</span>
                        <div className="font-medium">{
                          pumpReadings.metadata.quality_score 
                            ? `${(parseFloat(pumpReadings.metadata.quality_score.toString()) * 100).toFixed(1)}%`
                            : 'N/A'
                        }</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Last Updated:</span>
                        <div className="font-medium">{formatTime(pumpReadings.metadata.last_updated)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <div className="text-lg text-gray-600 mt-4">Loading pump readings...</div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 泵码问题汇总模态框 */}
      {showPumpIssues && pumpIssues && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900">
                  Pump Issues Summary
                </h2>
                <button
                  onClick={() => setShowPumpIssues(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-6">
                {/* 问题统计 */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{pumpIssues.total_issues}</div>
                    <div className="text-sm text-red-800">Total Issues</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">{pumpIssues.issues_by_type.missing_readings || 0}</div>
                    <div className="text-sm text-yellow-800">Missing Readings</div>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{pumpIssues.issues_by_type.poor_quality || 0}</div>
                    <div className="text-sm text-orange-800">Poor Quality</div>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{pumpIssues.issues_by_type.high_discrepancy || 0}</div>
                    <div className="text-sm text-red-800">High Discrepancy</div>
                  </div>
                </div>

                {/* 统计信息 */}
                {pumpIssues.statistics && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Statistics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-gray-600">Average Discrepancy:</div>
                        <div className="text-lg font-medium">{formatVolume(pumpIssues.statistics.avg_discrepancy)}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Maximum Discrepancy:</div>
                        <div className="text-lg font-medium text-red-600">{formatVolume(pumpIssues.statistics.max_discrepancy)}</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 问题交易列表 */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Problem Transactions</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Device</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quality</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Discrepancy</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Created</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {pumpIssues.transactions.map((transaction) => (
                          <tr key={transaction.id} className="hover:bg-gray-50">
                            <td className="px-4 py-2 text-sm font-medium text-gray-900">{transaction.id}</td>
                            <td className="px-4 py-2 text-sm text-gray-500">{transaction.device_id}</td>
                            <td className="px-4 py-2">
                              <span className={getPumpQualityBadge(transaction.pump_reading_quality)}>
                                {transaction.pump_reading_quality || 'unknown'}
                              </span>
                            </td>
                            <td className="px-4 py-2 text-sm text-red-600">
                              {formatVolume(transaction.pump_discrepancy)}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-500">{formatTime(transaction.created_at)}</td>
                            <td className="px-4 py-2">
                              <button
                                onClick={() => {
                                  setShowPumpIssues(false)
                                  handleViewPumpReadings(transaction)
                                }}
                                className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                              >
                                View Details
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 