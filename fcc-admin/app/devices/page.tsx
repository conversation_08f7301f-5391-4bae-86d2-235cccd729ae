'use client'

import React from 'react'
import { useDevicesV2, useControllers, usePollingSettings, useWayneCommands } from '@/lib/simple-state'
import { DeviceCreateForm } from '@/components/ui/DeviceStatus'
import { DeviceWithNozzlesCard } from '@/components/ui/DeviceWithNozzles'
import { useToast } from '@/components/ui/Notifications'
import { DeviceCreateRequestV2, DeviceV2, DeviceStatusV2 } from '@/lib/api-client-v2'

// V2 设备对象的扩展接口（用于兼容旧的组件）
interface DeviceDisplay extends DeviceV2 {
  // 添加兼容性字段
  is_online?: boolean
  last_response?: string
}

// 设备状态信息（简化版本）
interface DeviceStatusInfo {
  device_id: string
  device_type: string
  status: string
  health: string
  last_update: string
  last_seen?: string
  properties?: Record<string, any>
}

export default function DevicesPage() {
  const { devices, deviceStatuses, loading, error, refresh, getDeviceStatus, createDevice, getPumpStatus } = useDevicesV2()
  const { controllers } = useControllers()
  const wayneCommands = useWayneCommands()
  const { enabled: pollingEnabled, interval, lastUpdate, setEnabled, setInterval } = usePollingSettings()
  const toast = useToast()
  const [showCreateForm, setShowCreateForm] = React.useState(false)
  const [creating, setCreating] = React.useState(false)

  // 调试日志
  console.log('[DevicesPage] Render - devices:', devices)
  console.log('[DevicesPage] Render - devices length:', devices?.length || 0)
  console.log('[DevicesPage] Render - loading:', loading)
  console.log('[DevicesPage] Render - error:', error)
  console.log('[DevicesPage] Render - deviceStatuses:', deviceStatuses)

    const handleDeviceCommand = async (deviceId: string, commandType: string, parameters?: Record<string, any>) => {
    try {
      console.log(`[DevicesPage] Executing device command: ${commandType} for device: ${deviceId}`)
      console.log(`[DevicesPage] Command parameters:`, parameters)
      
      // 根据命令类型选择使用 Wayne 协议命令还是通用命令
      let result
      
      switch (commandType) {
        case 'authorize':
          // 支持两种授权方式：设备授权（nozzle_mask）和特定喷嘴授权（nozzle_id）
          if (parameters?.nozzle_id) {
            // 特定喷嘴授权：传递nozzle_id
            const employeeId = parameters?.employee_id || '001'
            console.log(`[DevicesPage] Authorizing specific nozzle - device: ${deviceId}, nozzle_id: ${parameters.nozzle_id}, employee_id: ${employeeId}`)
            result = await wayneCommands.authorize(deviceId, parameters.nozzle_id, employeeId)
          } else {
            // 设备授权：使用nozzle_mask（向后兼容）
            const nozzleMask = parameters?.nozzle_mask || 0xFF // 默认所有喷嘴
            const employeeId = parameters?.employee_id || '001'
            console.log(`[DevicesPage] Authorizing device - device: ${deviceId}, nozzle_mask: ${nozzleMask}, employee_id: ${employeeId}`)
            result = await wayneCommands.authorize(deviceId, nozzleMask, employeeId)
          }
          break
        case 'reset':
          result = await wayneCommands.reset(deviceId)
          break
        case 'stop':
          result = await wayneCommands.stop(deviceId)
          break
        case 'get_status':
          result = await wayneCommands.getStatus(deviceId)
          break
        case 'get_filling_info':
          result = await wayneCommands.getFillingInfo(deviceId)
          break
        case 'resume_fuelling_point':
          const employeeId = parameters?.employee_id || '001'
          console.log(`[DevicesPage] Resuming fuelling point - device: ${deviceId}, employee_id: ${employeeId}`)
          result = await wayneCommands.resumeFuellingPoint(deviceId, false, employeeId)
          break
        default:
          throw new Error(`Unknown device command type: ${commandType}`)
      }

      if (result.success) {
        console.log('Wayne device command executed successfully:', result)
        toast.success(`Device command "${commandType}" executed successfully`)
        // 成功后刷新设备状态
        await refresh()
      } else {
        throw new Error(result.error || 'Command failed')
      }
    } catch (error) {
      console.error('Wayne device command execution failed:', error)
      toast.error(`Device command failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handleNozzleCommand = async (deviceId: string, nozzleNumber: number, commandType: string, parameters?: Record<string, any>) => {
    try {
      console.log(`[DevicesPage] Executing nozzle command: ${commandType} for device: ${deviceId}, nozzle: ${nozzleNumber}`)
      
      let result
      
      switch (commandType) {
        case 'set_price':
          // 处理价格更新命令
          if (parameters?.price !== undefined && parameters?.nozzle_number !== undefined) {
            result = await wayneCommands.updatePrices(deviceId, [{
              nozzle_number: parameters.nozzle_number,
              price: parameters.price,
              decimals: parameters.decimals || 0
            }])
          } else {
            throw new Error('Invalid price parameters. Expected price and nozzle_number.')
          }
          break
        case 'preset_volume':
          result = await wayneCommands.presetVolume(
            deviceId, 
            parameters?.nozzle_number || nozzleNumber, 
            parameters?.volume_liters || 1,
            parameters?.employee_id || '001'
          )
          break
        case 'preset_amount':
          result = await wayneCommands.presetAmount(
            deviceId, 
            parameters?.nozzle_number || nozzleNumber, 
            parameters?.amount_cents || 100000,
            parameters?.employee_id || '001'
          )
          break
        case 'suspend_nozzle':
          result = await wayneCommands.suspendNozzle(deviceId, nozzleNumber)
          break
        case 'resume_nozzle':
          result = await wayneCommands.resumeNozzle(deviceId, nozzleNumber)
          break
        default:
          throw new Error(`Unknown nozzle command type: ${commandType}`)
      }

      if (result.success) {
        console.log('Wayne nozzle command executed successfully:', result)
        toast.success(`Nozzle ${nozzleNumber} command "${commandType}" executed successfully`)
        // 成功后刷新设备状态
        await refresh()
      } else {
        throw new Error(result.error || 'Command failed')
      }
    } catch (error) {
      console.error('Wayne nozzle command execution failed:', error)
      toast.error(`Nozzle command failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handleRefresh = async () => {
    console.log('[DevicesPage] Manual refresh triggered')
    try {
      await refresh()
      console.log('[DevicesPage] Manual refresh completed successfully')
      toast.success('Data refreshed successfully')
    } catch (error) {
      console.error('[DevicesPage] Manual refresh failed:', error)
      toast.error('Failed to refresh data')
    }
  }

  const handleCreateDevice = async (deviceData: any) => {
    try {
      setCreating(true)
      
      // 转换为 v2 设备创建请求格式
      const v2DeviceData: DeviceCreateRequestV2 = {
        id: deviceData.id || `device_${Date.now()}`, // 如果没有提供 id，生成一个
        name: deviceData.name,
        type: deviceData.type,
        device_address: parseInt(deviceData.device_address) || 80, // 默认 0x50
        controller_id: deviceData.controller_id,
        station_id: deviceData.station_id,
        island_id: deviceData.island_id,
        position: deviceData.position,
        config: {
          timeout: deviceData.timeout || 5000,
          retry_count: deviceData.retry_count || 3,
          monitor_interval: deviceData.monitor_interval || 30000,
          protocol_config: {},
          device_params: {},
          extensions: {}
        }
      }
      
      const newDevice = await createDevice(v2DeviceData)
      
      toast.success(`Device "${newDevice.name}" created successfully`)
      setShowCreateForm(false)
      
      // Refresh the devices list
      await refresh()
    } catch (error) {
      console.error('Failed to create device:', error)
      toast.error('Failed to create device: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setCreating(false)
    }
  }

  const getDeviceStatsByType = () => {
    const stats = devices.reduce((acc, device) => {
      const type = device.type
      if (!acc[type]) {
        acc[type] = { total: 0, online: 0 }
      }
      acc[type].total++
      if (device.status === 'online') {  // v2 使用 status 字段
        acc[type].online++
      }
      return acc
    }, {} as Record<string, { total: number; online: number }>)

    return stats
  }

  const deviceStats = getDeviceStatsByType()
  const totalDevices = devices.length
  const onlineDevices = devices.filter(d => d.status === 'online').length  // v2 使用 status 字段
  
  // 对设备进行排序：Pump设备按名称排序，其他设备保持原顺序
  const sortedDevices = React.useMemo(() => {
    console.log('[DevicesPage] Sorting devices, total:', devices.length)
    
    const pumpDevices = devices.filter(device => 
      device.type === 'fuel_pump' || 
      device.type === 'dart_pump' || 
      device.type === 'pump'
    )
    
    const nonPumpDevices = devices.filter(device => 
      device.type !== 'fuel_pump' && 
      device.type !== 'dart_pump' && 
      device.type !== 'pump'
    )
    
    // 对Pump设备按名称进行字母顺序排序
    const sortedPumps = [...pumpDevices].sort((a, b) => {
      return a.name.localeCompare(b.name, undefined, { 
        numeric: true, 
        sensitivity: 'base' 
      })
    })
    
    console.log('[DevicesPage] Sorted pumps:', sortedPumps.map(p => p.name))
    console.log('[DevicesPage] Non-pump devices:', nonPumpDevices.map(d => d.name))
    
    // 将排序后的Pump设备放在前面，其他设备按原顺序排在后面
    return [...sortedPumps, ...nonPumpDevices]
  }, [devices])
  
  // 页面初始化时自动刷新数据
  React.useEffect(() => {
    console.log('[DevicesPage] Component mounted, triggering initial refresh')
    handleRefresh()
  }, [])

  if (loading && devices.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div className="text-lg text-gray-600 mt-4">Loading devices...</div>
          <div className="text-sm text-gray-400 mt-2">Fetching device data from FCC service v2</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Device Management</h1>
          <p className="text-gray-600">Manage Wayne DART protocol devices</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            {lastUpdate && (
              <span>Last update: {lastUpdate.toLocaleTimeString()}</span>
            )}
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <span>➕</span>
            <span>Create Device</span>
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            <span>🔄</span>
            <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{totalDevices}</div>
              <div className="text-sm text-gray-500">Total Devices</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{onlineDevices}</div>
              <div className="text-sm text-gray-500">Online</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-400">{totalDevices - onlineDevices}</div>
              <div className="text-sm text-gray-500">Offline</div>
            </div>
          </div>

          {/* Polling Settings */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="polling"
                checked={pollingEnabled}
                onChange={(e) => setEnabled(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="polling" className="text-sm text-gray-600">
                Real-time updates
              </label>
            </div>
            <select
              value={interval}
              onChange={(e) => setInterval(Number(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value={10000}>10s (推荐)</option>
              <option value={5000}>5s</option>
              <option value={15000}>15s</option>
              <option value={30000}>30s</option>
            </select>
          </div>
        </div>

        {/* Device Type Stats */}
        {Object.keys(deviceStats).length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap gap-4">
              {Object.entries(deviceStats).map(([type, stats]) => (
                <div key={type} className="text-center">
                  <div className="text-sm font-medium text-gray-900">
                    {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </div>
                  <div className="text-xs text-gray-500">
                    {stats.online}/{stats.total}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">
            <h3 className="font-semibold">Error loading devices</h3>
            <p className="mt-2 text-sm">{error}</p>
            <button
              onClick={handleRefresh}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Devices Grid */}
      {devices.length === 0 && !loading ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <div className="text-gray-600">
            <div className="text-4xl mb-4">📱</div>
            <div className="text-lg font-medium">No devices found</div>
            <div className="text-sm mt-2">
              Check controller connections or run device discovery
            </div>
            <button
              onClick={handleRefresh}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Refresh
            </button>
          </div>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2">
          {sortedDevices.map((device) => {
            const deviceStatusV2 = getDeviceStatus(device.id)
            
            return (
              <DeviceWithNozzlesCard
                key={device.id}
                device={device}
                deviceStatus={deviceStatusV2}
                onDeviceCommand={handleDeviceCommand}
                onNozzleCommand={handleNozzleCommand}
                actions={
                  <div className="flex space-x-2">
                    <a
                      href={`/transactions?device_id=${device.id}`}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium px-2 py-1 rounded"
                      title="View transaction history"
                    >
                      📊 Transactions
                    </a>
                    <button
                      onClick={() => handleRefresh()}
                      className="text-gray-400 hover:text-gray-600"
                      title="Refresh device"
                    >
                      🔄
                    </button>
                  </div>
                }
              />
            )
          })}
        </div>
      )}

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 pt-4 border-t border-gray-200">
        <p>
          Total: {totalDevices} devices | Online: {onlineDevices} | 
          Pumps: {deviceStats['fuel_pump']?.total || 0 + deviceStats['dart_pump']?.total || 0} | 
          ATG: {deviceStats['atg']?.total || 0 + deviceStats['dart_tank']?.total || 0} | 
          Displays: {deviceStats['display']?.total || 0 + deviceStats['dart_display']?.total || 0}
        </p>
        {pollingEnabled && (
          <p className="mt-1">
            Real-time updates enabled • Polling every {interval/1000}s
          </p>
        )}
      </div>

      {/* Device Creation Modal */}
      {showCreateForm && (
        <DeviceCreateForm
          controllers={controllers.map(c => ({ id: c.id, name: c.name }))}
          onSubmit={handleCreateDevice}
          onCancel={() => setShowCreateForm(false)}
          loading={creating}
        />
      )}
    </div>
  )
} 