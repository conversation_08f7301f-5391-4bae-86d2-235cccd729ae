# FCC Admin Testing Guide

## 概述

本项目使用 Jest 和 TypeScript 进行单元测试，重点测试 FCC Service V2 API 客户端的功能。

## 测试设置

### 依赖安装

```bash
npm install
```

### 运行测试

```bash
# 运行所有测试
npm test

# 监视模式下运行测试
npm run test:watch

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 测试结构

### 测试文件位置

- 单元测试：`lib/api-client-v2.test.ts`
- 组件测试：`components/**/*.test.tsx`
- 集成测试：`__tests__/**/*.test.ts`

### 测试配置文件

- `jest.config.js` - Jest 主配置
- `jest.setup.js` - 测试环境设置
- `tsconfig.json` - TypeScript 配置

## API 客户端测试

### 测试覆盖范围

✅ **HTTP 客户端基础功能**
- GET/POST/PUT/DELETE 请求
- 错误处理和状态码验证
- JSON 响应解析
- 网络错误处理

✅ **设备管理 API**
- 设备列表获取和过滤
- 设备创建、更新、删除
- DART 协议地址验证 (80-111 范围)
- 设备状态查询

✅ **Wayne DART 协议命令**
- 所有 11 个 Wayne 命令的验证
- 参数验证和范围检查
- 喷嘴编号验证 (1-8)
- 体积和金额的小数位验证
- 价格数据验证

✅ **错误处理**
- FCCV2APIError 类测试
- API 错误响应处理
- 网络错误恢复
- 优雅降级

### 测试示例

```typescript
// 基本 API 调用测试
it('should fetch devices with filters', async () => {
  const mockResponse = { devices: [], total: 0 }
  mockFetch.mockResolvedValueOnce({
    ok: true,
    status: 200,
    json: async () => mockResponse
  })

  const result = await client.getDevices({
    controller_id: 'ctrl-001',
    status: 'online'
  })

  expect(result).toEqual(mockResponse)
})

// Wayne 协议命令验证测试
it('should validate DART address range', async () => {
  const invalidDevice = {
    device_address: 50 // 低于 80
  }

  await expect(client.createDevice(invalidDevice))
    .rejects.toThrow('Device address must be in range 80-111')
})
```

## 覆盖率要求

- **分支覆盖率**: 70%
- **函数覆盖率**: 70%
- **行覆盖率**: 70%
- **语句覆盖率**: 70%

## Mock 和测试助手

### 全局 Mock

- `fetch` - HTTP 请求模拟
- `console` - 控制台输出静音
- `window.location` - 浏览器位置模拟

### 测试助手函数

```typescript
// 创建模拟设备对象
const mockDevice = testHelpers.createMockDevice({
  name: 'Custom Device'
})

// 创建模拟 HTTP 响应
const mockResponse = testHelpers.createMockResponse(data, 200)

// 创建模拟 Wayne 命令响应
const wayneResponse = testHelpers.createMockWayneResponse({
  command: 'authorize'
})
```

## 测试最佳实践

### 1. 测试隔离
- 每个测试独立运行
- 使用 `beforeEach` 重置状态
- 避免测试间的副作用

### 2. 清晰的测试描述
```typescript
describe('Wayne DART Commands', () => {
  describe('wayneConfigureNozzles', () => {
    it('should validate nozzle numbers are between 1-8', async () => {
      // 测试代码
    })
  })
})
```

### 3. 边界条件测试
- 测试有效和无效输入
- 测试边界值 (0, 1, 8, 9)
- 测试错误条件

### 4. 异步测试
```typescript
it('should handle async operations', async () => {
  await expect(asyncFunction()).resolves.toBe(expectedValue)
  await expect(asyncFunction()).rejects.toThrow(ExpectedError)
})
```

## 持续集成

### GitHub Actions 配置示例

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test
      - run: npm run test:coverage
```

## 故障排除

### 常见问题

1. **TypeScript 编译错误**
   ```bash
   # 检查 tsconfig.json 配置
   npx tsc --noEmit
   ```

2. **Mock 不工作**
   ```typescript
   // 确保在 beforeEach 中重置 mock
   beforeEach(() => {
     jest.clearAllMocks()
   })
   ```

3. **异步测试超时**
   ```typescript
   // 增加测试超时时间
   jest.setTimeout(10000)
   ```

4. **模块解析错误**
   ```javascript
   // 检查 jest.config.js 中的 moduleNameMapping
   moduleNameMapping: {
     '^@/(.*)$': '<rootDir>/$1',
   }
   ```

## 测试数据

### DART 协议约束

- **设备地址范围**: 80-111 (0x50-0x6F)
- **喷嘴编号**: 1-8
- **体积小数位**: 0-3
- **金额小数位**: 0-3  
- **价格小数位**: 0-4
- **响应超时**: ≤ 25ms

### Wayne 命令类型

| 命令 | 代码 | 事务类型 | 描述 |
|------|------|----------|------|
| authorize | 0x06 | CD1 | 授权命令 |
| reset | 0x05 | CD1 | 复位命令 |
| stop | 0x08 | CD1 | 停止命令 |
| return_status | 0x00 | CD1 | 状态查询 |
| preset_volume | - | CD3 | 预设体积 |
| preset_amount | - | CD4 | 预设金额 |
| update_prices | - | CD5 | 价格更新 |

## 下一步

1. 添加组件测试
2. 添加端到端测试  
3. 增加覆盖率到 90%
4. 添加性能测试
5. 集成视觉回归测试 