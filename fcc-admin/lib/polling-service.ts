// 简单的轮询服务 - MVP实现
// 定期获取设备状态和系统信息

import { api, Device, DeviceStatusInfo } from './api-client-v2'

export interface PollingConfig {
  interval: number // 轮询间隔（毫秒）
  enabled: boolean
  maxRetries: number
}

export interface PollingCallbacks {
  onDeviceStatusUpdate?: (statuses: DeviceStatusInfo[]) => void
  onDevicesUpdate?: (devices: Device[]) => void
  onError?: (error: Error) => void
  onRetry?: (attempt: number) => void
}

export class PollingService {
  private config: PollingConfig
  private callbacks: PollingCallbacks
  private intervalId: NodeJS.Timeout | null = null
  private retryCount = 0
  private isRunning = false
  private lastDevices: Device[] = []
  private lastStatuses: Map<string, DeviceStatusInfo> = new Map()

  constructor(config: PollingConfig, callbacks: PollingCallbacks = {}) {
    this.config = config
    this.callbacks = callbacks
  }

  // 开始轮询
  start(): void {
    if (this.isRunning || !this.config.enabled) {
      return
    }

    console.log('[PollingService] Starting polling with interval:', this.config.interval)
    this.isRunning = true
    this.retryCount = 0
    
    // 立即执行一次
    this.poll()
    
    // 设置定时器
    this.intervalId = setInterval(() => {
      this.poll()
    }, this.config.interval)
  }

  // 停止轮询
  stop(): void {
    if (!this.isRunning) {
      return
    }

    console.log('[PollingService] Stopping polling')
    this.isRunning = false
    
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  // 更新配置
  updateConfig(newConfig: Partial<PollingConfig>): void {
    const wasRunning = this.isRunning
    
    if (wasRunning) {
      this.stop()
    }
    
    this.config = { ...this.config, ...newConfig }
    
    if (wasRunning && this.config.enabled) {
      this.start()
    }
  }

  // 更新回调
  updateCallbacks(newCallbacks: PollingCallbacks): void {
    this.callbacks = { ...this.callbacks, ...newCallbacks }
  }

  // 手动触发一次轮询
  async triggerPoll(): Promise<void> {
    return this.poll()
  }

  // 执行轮询
  private async poll(): Promise<void> {
    try {
      // 并行获取设备列表和状态
      const [devicesResult, statusResults] = await Promise.allSettled([
        this.fetchDevices(),
        this.fetchDeviceStatuses()
      ])

      // 处理设备列表结果
      if (devicesResult.status === 'fulfilled' && devicesResult.value) {
        const devices = devicesResult.value
        if (this.hasDevicesChanged(devices)) {
          this.lastDevices = devices
          this.callbacks.onDevicesUpdate?.(devices)
        }
      }

      // 处理设备状态结果
      if (statusResults.status === 'fulfilled' && statusResults.value) {
        const statuses = statusResults.value
        if (this.hasStatusesChanged(statuses)) {
          this.updateLastStatuses(statuses)
          this.callbacks.onDeviceStatusUpdate?.(statuses)
        }
      }

      // 重置重试计数
      this.retryCount = 0

    } catch (error) {
      this.handleError(error as Error)
    }
  }

  // 获取设备列表
  private async fetchDevices(): Promise<Device[]> {
    try {
      console.log('[PollingService] Fetching devices...')
      const devices = await api.devices.list()
      console.log('[PollingService] Fetched devices:', devices)
      console.log('[PollingService] Devices length:', devices?.length || 0)
      return devices || []
    } catch (error) {
      console.warn('[PollingService] Failed to fetch devices:', error)
      return []
    }
  }

  // 获取设备状态
  private async fetchDeviceStatuses(): Promise<DeviceStatusInfo[]> {
    try {
      // 获取所有设备的状态
      const devices = this.lastDevices.length > 0 ? this.lastDevices : await this.fetchDevices()
      
      if (devices.length === 0) {
        return []
      }

      // 并行获取每个设备的状态（限制并发数）
      const statusPromises = devices.slice(0, 20).map(async (device) => {
        try {
          return await api.devices.getStatus(device.id)
        } catch (error) {
          // 单个设备状态获取失败不影响其他设备
          console.warn(`[PollingService] Failed to get status for device ${device.id}:`, error)
          return null
        }
      })

      const statusResults = await Promise.all(statusPromises)
      return statusResults.filter((status): status is DeviceStatusInfo => status !== null)

    } catch (error) {
      console.warn('[PollingService] Failed to fetch device statuses:', error)
      return []
    }
  }

  // 检查设备列表是否变化
  private hasDevicesChanged(newDevices: Device[]): boolean {
    if (newDevices.length !== this.lastDevices.length) {
      return true
    }

    // 简单比较设备ID和更新时间
    for (let i = 0; i < newDevices.length; i++) {
      const newDevice = newDevices[i]
      const oldDevice = this.lastDevices[i]
      
      if (!oldDevice || 
          newDevice.id !== oldDevice.id || 
          newDevice.updated_at !== oldDevice.updated_at ||
          newDevice.status !== oldDevice.status) {
        return true
      }
    }

    return false
  }

  // 检查设备状态是否变化
  private hasStatusesChanged(newStatuses: DeviceStatusInfo[]): boolean {
    if (newStatuses.length === 0) {
      return false
    }

    for (const status of newStatuses) {
      const lastStatus = this.lastStatuses.get(status.device_id)
      
      if (!lastStatus ||
          lastStatus.status !== status.status ||
          lastStatus.last_seen !== status.last_seen) {
        return true
      }
    }

    return false
  }

  // 更新最后状态记录
  private updateLastStatuses(statuses: DeviceStatusInfo[]): void {
    statuses.forEach(status => {
      this.lastStatuses.set(status.device_id, status)
    })
  }

  // 处理错误
  private handleError(error: Error): void {
    this.retryCount++
    
    console.error(`[PollingService] Polling error (attempt ${this.retryCount}):`, error)
    
    this.callbacks.onError?.(error)
    
    if (this.retryCount >= this.config.maxRetries) {
      console.error('[PollingService] Max retries reached, stopping polling')
      this.stop()
    } else {
      this.callbacks.onRetry?.(this.retryCount)
    }
  }

  // 获取状态
  get running(): boolean {
    return this.isRunning
  }

  get currentRetryCount(): number {
    return this.retryCount
  }
}

// 创建默认轮询服务实例
export const defaultPollingService = new PollingService({
  interval: 5000, // 5秒轮询一次
  enabled: true,
  maxRetries: 3
})

// 便捷的轮询管理Hook
export function usePolling(callbacks: PollingCallbacks, config?: Partial<PollingConfig>) {
  const service = defaultPollingService

  // 更新回调
  React.useEffect(() => {
    service.updateCallbacks(callbacks)
  }, [callbacks])

  // 更新配置
  React.useEffect(() => {
    if (config) {
      service.updateConfig(config)
    }
  }, [config])

  // 组件卸载时停止轮询
  React.useEffect(() => {
    return () => {
      // 注意：这里不直接停止服务，因为可能有其他组件在使用
      // 实际使用时可能需要引用计数机制
    }
  }, [])

  return {
    start: () => service.start(),
    stop: () => service.stop(),
    triggerPoll: () => service.triggerPoll(),
    isRunning: service.running,
    retryCount: service.currentRetryCount
  }
}

// React import (需要在文件顶部实际导入)
import React from 'react' 