'use client'

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { <PERSON><PERSON>, Controller, DeviceStatusInfo } from './api-client-v2'
import { CommandRequestV2 as Command } from './api-client'
import { fccWebSocket, subscribeToDeviceStatus, subscribeToCommandEvents, subscribeToSystemEvents } from './websocket-client'

// 系统指标接口
export interface SystemMetrics {
  totalDevices: number
  onlineDevices: number
  activeCommands: number
  errorRate: number
  averageResponseTime: number
  systemUptime: number
  lastUpdate: Date
}

// 通知接口
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Date
  read: boolean
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// 连接状态接口
export interface ConnectionInfo {
  connected: boolean
  connecting: boolean
  lastConnectedAt: Date | null
  lastDisconnectedAt: Date | null
  totalReconnects: number
  uptime: number | null
}

// 全局状态接口
export interface FCCState {
  // 核心数据
  devices: Map<string, Device>
  controllers: Map<string, Controller>
  deviceStatuses: Map<string, DeviceStatusInfo>
  commands: Map<string, Command>
  
  // 系统状态
  systemMetrics: SystemMetrics
  connectionInfo: ConnectionInfo
  notifications: Notification[]
  
  // UI状态
  loading: {
    devices: boolean
    controllers: boolean
    commands: boolean
    global: boolean
  }
  errors: {
    devices: string | null
    controllers: string | null
    commands: string | null
    global: string | null
    websocket: string | null
  }
  
  // 配置
  realTimeEnabled: boolean
  autoRefreshInterval: number
}

// Action 类型
export type FCCAction =
  | { type: 'SET_DEVICES'; payload: Device[] }
  | { type: 'ADD_DEVICE'; payload: Device }
  | { type: 'UPDATE_DEVICE'; payload: Device }
  | { type: 'REMOVE_DEVICE'; payload: string }
  | { type: 'SET_CONTROLLERS'; payload: Controller[] }
  | { type: 'ADD_CONTROLLER'; payload: Controller }
  | { type: 'UPDATE_CONTROLLER'; payload: Controller }
  | { type: 'REMOVE_CONTROLLER'; payload: string }
  | { type: 'SET_COMMANDS'; payload: Command[] }
  | { type: 'ADD_COMMAND'; payload: Command }
  | { type: 'UPDATE_COMMAND'; payload: Command }
  | { type: 'REMOVE_COMMAND'; payload: string }
  | { type: 'DEVICE_STATUS_UPDATE'; payload: DeviceStatusInfo }
  | { type: 'BATCH_DEVICE_STATUS_UPDATE'; payload: DeviceStatusInfo[] }
  | { type: 'UPDATE_SYSTEM_METRICS'; payload: Partial<SystemMetrics> }
  | { type: 'UPDATE_CONNECTION_INFO'; payload: Partial<ConnectionInfo> }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'CLEAR_NOTIFICATIONS' }
  | { type: 'SET_LOADING'; payload: { key: keyof FCCState['loading']; value: boolean } }
  | { type: 'SET_ERROR'; payload: { key: keyof FCCState['errors']; value: string | null } }
  | { type: 'CLEAR_ERRORS' }
  | { type: 'SET_REAL_TIME_ENABLED'; payload: boolean }
  | { type: 'SET_AUTO_REFRESH_INTERVAL'; payload: number }

// 初始状态
const initialState: FCCState = {
  devices: new Map(),
  controllers: new Map(),
  deviceStatuses: new Map(),
  commands: new Map(),
  
  systemMetrics: {
    totalDevices: 0,
    onlineDevices: 0,
    activeCommands: 0,
    errorRate: 0,
    averageResponseTime: 0,
    systemUptime: 0,
    lastUpdate: new Date()
  },
  
  connectionInfo: {
    connected: false,
    connecting: false,
    lastConnectedAt: null,
    lastDisconnectedAt: null,
    totalReconnects: 0,
    uptime: null
  },
  
  notifications: [],
  
  loading: {
    devices: false,
    controllers: false,
    commands: false,
    global: false
  },
  
  errors: {
    devices: null,
    controllers: null,
    commands: null,
    global: null,
    websocket: null
  },
  
  realTimeEnabled: true,
  autoRefreshInterval: 30000 // 30秒
}

// Reducer 函数
function fccReducer(state: FCCState, action: FCCAction): FCCState {
  switch (action.type) {
    case 'SET_DEVICES':
      return {
        ...state,
        devices: new Map(action.payload.map(device => [device.id, device]))
      }

    case 'ADD_DEVICE':
      return {
        ...state,
        devices: new Map(state.devices).set(action.payload.id, action.payload)
      }

    case 'UPDATE_DEVICE':
      if (state.devices.has(action.payload.id)) {
        return {
          ...state,
          devices: new Map(state.devices).set(action.payload.id, action.payload)
        }
      }
      return state

    case 'REMOVE_DEVICE':
      const newDevices = new Map(state.devices)
      newDevices.delete(action.payload)
      return {
        ...state,
        devices: newDevices
      }

    case 'SET_CONTROLLERS':
      return {
        ...state,
        controllers: new Map(action.payload.map(controller => [controller.id, controller]))
      }

    case 'ADD_CONTROLLER':
      return {
        ...state,
        controllers: new Map(state.controllers).set(action.payload.id, action.payload)
      }

    case 'UPDATE_CONTROLLER':
      if (state.controllers.has(action.payload.id)) {
        return {
          ...state,
          controllers: new Map(state.controllers).set(action.payload.id, action.payload)
        }
      }
      return state

    case 'REMOVE_CONTROLLER':
      const newControllers = new Map(state.controllers)
      newControllers.delete(action.payload)
      return {
        ...state,
        controllers: newControllers
      }

    case 'SET_COMMANDS':
      return {
        ...state,
        commands: new Map(action.payload.map(command => [command.command_id || '', command]))
      }

    case 'ADD_COMMAND':
      return {
        ...state,
        commands: new Map(state.commands).set(action.payload.command_id || '', action.payload)
      }

    case 'UPDATE_COMMAND':
      const commandId = action.payload.command_id || ''
      if (state.commands.has(commandId)) {
        return {
          ...state,
          commands: new Map(state.commands).set(commandId, action.payload)
        }
      }
      return state

    case 'REMOVE_COMMAND':
      const newCommands = new Map(state.commands)
      newCommands.delete(action.payload)
      return {
        ...state,
        commands: newCommands
      }

    case 'DEVICE_STATUS_UPDATE':
      return {
        ...state,
        deviceStatuses: new Map(state.deviceStatuses).set(
          action.payload.device_id,
          action.payload
        )
      }

    case 'BATCH_DEVICE_STATUS_UPDATE':
      const newStatuses = new Map(state.deviceStatuses)
      action.payload.forEach(status => {
        newStatuses.set(status.device_id, status)
      })
      return {
        ...state,
        deviceStatuses: newStatuses
      }

    case 'UPDATE_SYSTEM_METRICS':
      return {
        ...state,
        systemMetrics: {
          ...state.systemMetrics,
          ...action.payload,
          lastUpdate: new Date()
        }
      }

    case 'UPDATE_CONNECTION_INFO':
      return {
        ...state,
        connectionInfo: {
          ...state.connectionInfo,
          ...action.payload
        }
      }

    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications]
      }

    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload)
      }

    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(n => 
          n.id === action.payload ? { ...n, read: true } : n
        )
      }

    case 'CLEAR_NOTIFICATIONS':
      return {
        ...state,
        notifications: []
      }

    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.value
        }
      }

    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.key]: action.payload.value
        }
      }

    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: {
          devices: null,
          controllers: null,
          commands: null,
          global: null,
          websocket: null
        }
      }

    case 'SET_REAL_TIME_ENABLED':
      return {
        ...state,
        realTimeEnabled: action.payload
      }

    case 'SET_AUTO_REFRESH_INTERVAL':
      return {
        ...state,
        autoRefreshInterval: action.payload
      }

    default:
      return state
  }
}

// Context
const FCCContext = createContext<{
  state: FCCState
  dispatch: React.Dispatch<FCCAction>
} | null>(null)

// Provider 组件
interface FCCProviderProps {
  children: ReactNode
}

export function FCCProvider({ children }: FCCProviderProps) {
  const [state, dispatch] = useReducer(fccReducer, initialState)

  // WebSocket 事件处理
  useEffect(() => {
    if (!state.realTimeEnabled) {
      return
    }

    console.log('[FCCProvider] Setting up WebSocket subscriptions')

    // 订阅设备状态更新
    const unsubscribeDeviceStatus = subscribeToDeviceStatus((data) => {
      console.log('[FCCProvider] Device status update:', data)
      dispatch({ type: 'DEVICE_STATUS_UPDATE', payload: data })
    })

    // 订阅命令状态更新
    const unsubscribeCommandEvents = subscribeToCommandEvents((data) => {
      console.log('[FCCProvider] Command event:', data)
      dispatch({ type: 'UPDATE_COMMAND', payload: data })
    })

    // 订阅系统事件
    const unsubscribeSystemEvents = subscribeToSystemEvents((data) => {
      console.log('[FCCProvider] System event:', data)
      
      if (data.type === 'system_metrics') {
        dispatch({ type: 'UPDATE_SYSTEM_METRICS', payload: data })
      } else if (data.type === 'system_alert') {
        const notification: Notification = {
          id: `alert_${Date.now()}`,
          type: data.level || 'info',
          title: data.title || 'System Alert',
          message: data.message,
          timestamp: new Date(),
          read: false
        }
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
      }
    })

    // WebSocket 连接事件
    const handleWebSocketConnected = () => {
      const stats = fccWebSocket.getStats()
      dispatch({ 
        type: 'UPDATE_CONNECTION_INFO', 
        payload: stats 
      })
      dispatch({ 
        type: 'SET_ERROR', 
        payload: { key: 'websocket', value: null } 
      })
    }

    const handleWebSocketDisconnected = () => {
      const stats = fccWebSocket.getStats()
      dispatch({ 
        type: 'UPDATE_CONNECTION_INFO', 
        payload: stats 
      })
    }

    const handleWebSocketError = (data: any) => {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: { key: 'websocket', value: 'WebSocket connection error' } 
      })
    }

    fccWebSocket.on('connected', handleWebSocketConnected)
    fccWebSocket.on('disconnected', handleWebSocketDisconnected)
    fccWebSocket.on('error', handleWebSocketError)

    // 尝试连接 WebSocket
    fccWebSocket.connect().catch(error => {
      console.error('[FCCProvider] WebSocket connection failed:', error)
      dispatch({ 
        type: 'SET_ERROR', 
        payload: { key: 'websocket', value: 'Failed to connect to real-time service' } 
      })
    })

    return () => {
      console.log('[FCCProvider] Cleaning up WebSocket subscriptions')
      unsubscribeDeviceStatus()
      unsubscribeCommandEvents()
      unsubscribeSystemEvents()
      fccWebSocket.off('connected', handleWebSocketConnected)
      fccWebSocket.off('disconnected', handleWebSocketDisconnected)
      fccWebSocket.off('error', handleWebSocketError)
    }
  }, [state.realTimeEnabled])

  // 定期更新连接信息
  useEffect(() => {
    const interval = setInterval(() => {
      const stats = fccWebSocket.getStats()
      dispatch({ 
        type: 'UPDATE_CONNECTION_INFO', 
        payload: stats 
      })
    }, 5000) // 每5秒更新一次

    return () => clearInterval(interval)
  }, [])

  return (
    <FCCContext.Provider value={{ state, dispatch }}>
      {children}
    </FCCContext.Provider>
  )
}

// Hook 使用 Context
export function useFCCState() {
  const context = useContext(FCCContext)
  if (!context) {
    throw new Error('useFCCState must be used within a FCCProvider')
  }
  return context
}

// 便捷的 Hooks
export function useDevices() {
  const { state, dispatch } = useFCCState()
  return {
    devices: Array.from(state.devices.values()),
    devicesMap: state.devices,
    loading: state.loading.devices,
    error: state.errors.devices,
    addDevice: (device: Device) => dispatch({ type: 'ADD_DEVICE', payload: device }),
    updateDevice: (device: Device) => dispatch({ type: 'UPDATE_DEVICE', payload: device }),
    removeDevice: (deviceId: string) => dispatch({ type: 'REMOVE_DEVICE', payload: deviceId }),
    setDevices: (devices: Device[]) => dispatch({ type: 'SET_DEVICES', payload: devices }),
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: { key: 'devices', value: loading } }),
    setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: { key: 'devices', value: error } })
  }
}

export function useControllers() {
  const { state, dispatch } = useFCCState()
  return {
    controllers: Array.from(state.controllers.values()),
    controllersMap: state.controllers,
    loading: state.loading.controllers,
    error: state.errors.controllers,
    addController: (controller: Controller) => dispatch({ type: 'ADD_CONTROLLER', payload: controller }),
    updateController: (controller: Controller) => dispatch({ type: 'UPDATE_CONTROLLER', payload: controller }),
    removeController: (controllerId: string) => dispatch({ type: 'REMOVE_CONTROLLER', payload: controllerId }),
    setControllers: (controllers: Controller[]) => dispatch({ type: 'SET_CONTROLLERS', payload: controllers }),
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: { key: 'controllers', value: loading } }),
    setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: { key: 'controllers', value: error } })
  }
}

export function useDeviceStatuses() {
  const { state } = useFCCState()
  return {
    deviceStatuses: Array.from(state.deviceStatuses.values()),
    deviceStatusesMap: state.deviceStatuses,
    getDeviceStatus: (deviceId: string) => state.deviceStatuses.get(deviceId)
  }
}

export function useCommands() {
  const { state, dispatch } = useFCCState()
  return {
    commands: Array.from(state.commands.values()),
    commandsMap: state.commands,
    loading: state.loading.commands,
    error: state.errors.commands,
    addCommand: (command: Command) => dispatch({ type: 'ADD_COMMAND', payload: command }),
    updateCommand: (command: Command) => dispatch({ type: 'UPDATE_COMMAND', payload: command }),
    removeCommand: (commandId: string) => dispatch({ type: 'REMOVE_COMMAND', payload: commandId }),
    setCommands: (commands: Command[]) => dispatch({ type: 'SET_COMMANDS', payload: commands }),
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: { key: 'commands', value: loading } }),
    setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: { key: 'commands', value: error } })
  }
}

export function useNotifications() {
  const { state, dispatch } = useFCCState()
  return {
    notifications: state.notifications,
    unreadCount: state.notifications.filter(n => !n.read).length,
    addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => {
      const fullNotification: Notification = {
        ...notification,
        id: `notification_${Date.now()}_${Math.random()}`,
        timestamp: new Date()
      }
      dispatch({ type: 'ADD_NOTIFICATION', payload: fullNotification })
    },
    removeNotification: (id: string) => dispatch({ type: 'REMOVE_NOTIFICATION', payload: id }),
    markAsRead: (id: string) => dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id }),
    clearAll: () => dispatch({ type: 'CLEAR_NOTIFICATIONS' })
  }
}

export function useSystemMetrics() {
  const { state } = useFCCState()
  return {
    metrics: state.systemMetrics,
    connectionInfo: state.connectionInfo
  }
}

export function useRealTimeSettings() {
  const { state, dispatch } = useFCCState()
  return {
    realTimeEnabled: state.realTimeEnabled,
    autoRefreshInterval: state.autoRefreshInterval,
    setRealTimeEnabled: (enabled: boolean) => dispatch({ type: 'SET_REAL_TIME_ENABLED', payload: enabled }),
    setAutoRefreshInterval: (interval: number) => dispatch({ type: 'SET_AUTO_REFRESH_INTERVAL', payload: interval })
  }
} 