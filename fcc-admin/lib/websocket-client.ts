// FCC WebSocket 客户端
// 支持实时设备状态更新、命令执行状态、系统事件等

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
  source?: string
}

export interface WebSocketConfig {
  url: string
  protocols?: string[]
  maxReconnectAttempts?: number
  reconnectInterval?: number
  heartbeatInterval?: number
}

export interface WebSocketEventHandler {
  (event: WebSocketMessage): void
}

export interface WebSocketEventHandlers {
  [eventType: string]: WebSocketEventHandler[]
}

export class FCCWebSocketClient {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private eventHandlers: WebSocketEventHandlers = {}
  private reconnectAttempts = 0
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null
  private isConnecting = false
  private isDestroyed = false

  // 连接状态
  private _connected = false
  private _lastConnectedAt: Date | null = null
  private _lastDisconnectedAt: Date | null = null
  private _totalReconnects = 0

  constructor(config: WebSocketConfig) {
    this.config = {
      maxReconnectAttempts: 5,
      reconnectInterval: 1000,
      heartbeatInterval: 30000,
      ...config
    }
  }

  // 连接到 WebSocket 服务器
  async connect(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('WebSocket client has been destroyed')
    }

    if (this.isConnecting || this._connected) {
      return
    }

    return new Promise((resolve, reject) => {
      try {
        this.isConnecting = true
        console.log(`[WebSocket] Connecting to ${this.config.url}`)

        this.ws = new WebSocket(this.config.url, this.config.protocols)

        const timeout = setTimeout(() => {
          this.ws?.close()
          reject(new Error('Connection timeout'))
        }, 10000) // 10秒连接超时

        this.ws.onopen = (event) => {
          clearTimeout(timeout)
          this.isConnecting = false
          this._connected = true
          this._lastConnectedAt = new Date()
          this.reconnectAttempts = 0

          console.log('[WebSocket] Connected successfully')
          this.startHeartbeat()
          this.emit('connected', { event })
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        this.ws.onclose = (event) => {
          clearTimeout(timeout)
          this.handleClose(event)
        }

        this.ws.onerror = (event) => {
          clearTimeout(timeout)
          this.handleError(event)
          if (this.isConnecting) {
            reject(new Error('Connection failed'))
          }
        }

      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect(): void {
    console.log('[WebSocket] Disconnecting...')
    
    this.stopHeartbeat()
    this.stopReconnect()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    
    this._connected = false
    this._lastDisconnectedAt = new Date()
  }

  // 销毁客户端
  destroy(): void {
    console.log('[WebSocket] Destroying client...')
    
    this.isDestroyed = true
    this.disconnect()
    this.eventHandlers = {}
  }

  // 发送消息
  send(type: string, data: any): boolean {
    if (!this._connected || !this.ws) {
      console.warn('[WebSocket] Cannot send message: not connected')
      return false
    }

    try {
      const message: WebSocketMessage = {
        type,
        data,
        timestamp: new Date().toISOString(),
        source: 'fcc-admin'
      }

      this.ws.send(JSON.stringify(message))
      console.log(`[WebSocket] Sent message: ${type}`)
      return true
    } catch (error) {
      console.error('[WebSocket] Send error:', error)
      return false
    }
  }

  // 订阅事件
  on(eventType: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = []
    }
    this.eventHandlers[eventType].push(handler)
  }

  // 取消订阅事件
  off(eventType: string, handler?: WebSocketEventHandler): void {
    if (!this.eventHandlers[eventType]) {
      return
    }

    if (handler) {
      const index = this.eventHandlers[eventType].indexOf(handler)
      if (index > -1) {
        this.eventHandlers[eventType].splice(index, 1)
      }
    } else {
      this.eventHandlers[eventType] = []
    }
  }

  // 触发事件
  private emit(eventType: string, data: any): void {
    const handlers = this.eventHandlers[eventType]
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler({ type: eventType, data, timestamp: new Date().toISOString() })
        } catch (error) {
          console.error(`[WebSocket] Event handler error for ${eventType}:`, error)
        }
      })
    }
  }

  // 处理收到的消息
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      console.log(`[WebSocket] Received message: ${message.type}`)
      
      // 特殊处理心跳响应
      if (message.type === 'pong') {
        return
      }

      // 触发通用消息事件
      this.emit('message', message)
      
      // 触发特定类型事件
      this.emit(message.type, message.data)

    } catch (error) {
      console.error('[WebSocket] Message parse error:', error)
    }
  }

  // 处理连接关闭
  private handleClose(event: CloseEvent): void {
    console.log(`[WebSocket] Connection closed: ${event.code} ${event.reason}`)
    
    this.isConnecting = false
    this._connected = false
    this._lastDisconnectedAt = new Date()
    this.stopHeartbeat()

    this.emit('disconnected', { 
      code: event.code, 
      reason: event.reason,
      wasClean: event.wasClean 
    })

    // 自动重连（除非是主动断开或已销毁）
    if (!this.isDestroyed && event.code !== 1000) {
      this.scheduleReconnect()
    }
  }

  // 处理连接错误
  private handleError(event: Event): void {
    console.error('[WebSocket] Connection error:', event)
    this.emit('error', { event })
  }

  // 安排重连
  private scheduleReconnect(): void {
    if (this.isDestroyed || this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      console.log('[WebSocket] Max reconnect attempts reached')
      this.emit('reconnect_failed', { attempts: this.reconnectAttempts })
      return
    }

    const delay = this.config.reconnectInterval! * Math.pow(2, this.reconnectAttempts)
    this.reconnectAttempts++
    this._totalReconnects++

    console.log(`[WebSocket] Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`)

    this.reconnectTimer = setTimeout(() => {
      this.emit('reconnecting', { attempt: this.reconnectAttempts })
      this.connect().catch(error => {
        console.error('[WebSocket] Reconnect failed:', error)
      })
    }, delay)
  }

  // 停止重连
  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  // 开始心跳
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this._connected) {
        this.send('ping', { timestamp: Date.now() })
      }
    }, this.config.heartbeatInterval!)
  }

  // 停止心跳
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 获取连接状态
  get connected(): boolean {
    return this._connected
  }

  get connecting(): boolean {
    return this.isConnecting
  }

  get lastConnectedAt(): Date | null {
    return this._lastConnectedAt
  }

  get lastDisconnectedAt(): Date | null {
    return this._lastDisconnectedAt
  }

  get totalReconnects(): number {
    return this._totalReconnects
  }

  get currentReconnectAttempts(): number {
    return this.reconnectAttempts
  }

  // 获取连接统计信息
  getStats(): {
    connected: boolean
    connecting: boolean
    lastConnectedAt: Date | null
    lastDisconnectedAt: Date | null
    totalReconnects: number
    currentReconnectAttempts: number
    uptime: number | null
  } {
    const uptime = this._lastConnectedAt && this._connected 
      ? Date.now() - this._lastConnectedAt.getTime()
      : null

    return {
      connected: this._connected,
      connecting: this.isConnecting,
      lastConnectedAt: this._lastConnectedAt,
      lastDisconnectedAt: this._lastDisconnectedAt,
      totalReconnects: this._totalReconnects,
      currentReconnectAttempts: this.reconnectAttempts,
      uptime
    }
  }
}

// 创建默认 WebSocket 客户端实例
const wsUrl = process.env.NEXT_PUBLIC_FCC_WS_URL || 'ws://localhost:8080/ws'

export const fccWebSocket = new FCCWebSocketClient({
  url: wsUrl,
  maxReconnectAttempts: 5,
  reconnectInterval: 1000,
  heartbeatInterval: 30000
})

// 便捷的事件订阅函数
export const subscribeToDeviceStatus = (handler: (data: any) => void) => {
  fccWebSocket.on('device_status_update', handler)
  return () => fccWebSocket.off('device_status_update', handler)
}

export const subscribeToCommandEvents = (handler: (data: any) => void) => {
  fccWebSocket.on('command_status_update', handler)
  return () => fccWebSocket.off('command_status_update', handler)
}

export const subscribeToSystemEvents = (handler: (data: any) => void) => {
  fccWebSocket.on('system_event', handler)
  return () => fccWebSocket.off('system_event', handler)
}

export const subscribeToConnectionEvents = (handler: (data: any) => void) => {
  fccWebSocket.on('connection_event', handler)
  return () => fccWebSocket.off('connection_event', handler)
} 