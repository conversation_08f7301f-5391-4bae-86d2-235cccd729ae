// FCC API 客户端 V2 版本
// 基于 FCC Service V2 架构和 Wayne DART 协议

import { getApiBaseUrl } from './api-config'

// 动态获取API基础URL，支持运行时配置
const getAPIBaseV2 = () => {
  // 直接使用动态配置，不依赖环境变量
  return getApiBaseUrl()
}

const API_BASE_V2 = getAPIBaseV2()

// ===== V2 类型定义 =====

// ===== 基础类型定义 =====

// 设备类型
export type DeviceType = 
  | 'controller' | 'fuel_controller' | 'payment_controller' | 'monitor_controller'
  | 'dart_line_controller' | 'tcp_device_controller' | 'serial_port_controller'
  | 'fuel_pump' | 'fuel_nozzle' | 'atg' | 'display' | 'pos' | 'edc' | 'pump'
  | 'dart_pump' | 'dart_tank' | 'dart_display'

// 设备状态类型
export type DeviceStatus = 'online' | 'offline' | 'error' | 'maintenance' | 'unknown'
export type DeviceState = 'online' | 'offline' | 'initializing' | 'ready' | 'polling' | 'configuring' | 'error' | 'maintenance'
export type DeviceHealth = 'healthy' | 'good' | 'warning' | 'critical' | 'error' | 'unknown'
export type ProtocolType = 'tcp' | 'udp' | 'serial' | 'modbus' | 'dart' | 'wayne_dart'
export type NozzleState = 'idle' | 'selected' | 'authorized' | 'out' | 'filling' | 'completed' | 'suspended' | 'error' | 'maintenance'
export type CommandPriority = 'high' | 'normal' | 'low'

// 交易相关类型定义
export type TransactionType = 'fuel' | 'payment' | 'refund' | 'void'
export type TransactionStatus = 'pending' | 'started' | 'active' | 'completed' | 'cancelled' | 'failed' | 'refunded'

// Wayne 协议命令类型
export type WayneCommandType =
  | 'authorize' | 'reset' | 'stop' | 'return_status' | 'return_filling_info'
  | 'configure_nozzles' | 'preset_volume' | 'preset_amount' | 'update_prices'
  | 'suspend_nozzle' | 'resume_nozzle' | 'request_counters' | 'preauth'

// PreAuth 授权类型
export type PreAuthType = 'preset_volume' | 'preset_amount'

// 累计数据计数器类型
export type CounterType = 
  | 'volume_nozzle_1' | 'volume_nozzle_2' | 'volume_nozzle_3' | 'volume_nozzle_4'
  | 'volume_nozzle_5' | 'volume_nozzle_6' | 'volume_nozzle_7' | 'volume_nozzle_8' | 'volume_total'
  | 'amount_nozzle_1' | 'amount_nozzle_2' | 'amount_nozzle_3' | 'amount_nozzle_4'
  | 'amount_nozzle_5' | 'amount_nozzle_6' | 'amount_nozzle_7' | 'amount_nozzle_8' | 'amount_total'

// 油品相关类型定义
export type FuelGradeType = 'gasoline' | 'diesel' | 'premium' | 'regular' | 'kerosene' | 'other'
export type FuelSyncStatus = 'idle' | 'syncing' | 'success' | 'failed' | 'partial'

// 油品等级
export interface FuelGrade {
  id: string
  name: string
  type: FuelGradeType
  description?: string
  price: number | string      // 支持decimal格式
  unit: string
  metadata?: Record<string, any>
  deleted_at?: string
  created_at: string
  updated_at: string
}

// 油品同步状态
export interface FuelSyncStatusInfo {
  status: FuelSyncStatus
  last_sync_at?: string
  sync_duration_ms?: number
  synced_count: number
  failed_count: number
  total_count: number
  error_message?: string
  next_sync_at?: string
}

// 油品同步历史记录
export interface FuelSyncHistory {
  id: string
  status: FuelSyncStatus
  started_at: string
  completed_at?: string
  duration_ms?: number
  synced_count: number
  failed_count: number
  total_count: number
  error_message?: string
  details?: Record<string, any>
}

// 外部油品API数据模型
export interface ExternalOilProduct {
  id: number
  code: string
  name: string
  category: string
  grade: string
  description: string
  unit: string
  default_price: number
  current_price: number
  is_active: boolean
  specifications: string
  image_url: string
  created_by: string
  created_at: string
  updated_at: string
}

// ===== V2 接口定义 =====

// 设备配置
export interface DeviceConfigV2 {
  timeout: number
  retry_count: number
  monitor_interval: number
  protocol_config: Record<string, any>
  device_params: Record<string, any>
  extensions: Record<string, any>
}

// 设备能力
export interface DeviceCapabilitiesV2 {
  supported_commands: string[]
  supported_data_types: string[]
  protocol_version?: string
  firmware_version?: string
  hardware_version?: string
  features?: Record<string, boolean>
  performance?: Record<string, any>
}

// 设备对象 (V2版本)
export interface DeviceV2 {
  id: string
  name: string
  type: DeviceType
  controller_id: string
  device_address: number        // V2: 后端字段名为 device_address
  station_id: string
  island_id?: string
  position?: string
  config: DeviceConfigV2
  status: DeviceStatus
  health: DeviceHealth
  capabilities: DeviceCapabilitiesV2
  last_seen?: string
  created_at: string
  updated_at: string
}

// 设备创建请求
export interface DeviceCreateRequestV2 {
  id: string
  name: string
  type: DeviceType
  controller_id: string
  device_address: number        // DART 协议地址 (80-111, 0x50-0x6F)
  station_id: string
  island_id?: string
  position?: string
  config?: Partial<DeviceConfigV2>
}

// 设备状态 (V2版本)
export interface DeviceStatusV2 {
  device_id: string
  status: DeviceState | 'unknown'
  last_seen?: string
}

// 列表响应
export interface DeviceListResponseV2 {
  devices: DeviceV2[]
  total: number
  page?: number
  page_size?: number
}

// Wayne 协议命令响应
export interface WayneCommandResponseV2 {
  command_id: string
  device_id: string
  command: WayneCommandType
  success: boolean
  data?: Record<string, any>
  error?: string
  execution_time_ms: number
  submitted_at: string
  completed_at?: string
  protocol_info?: {
    protocol: string
    transaction_type: string
    transaction_code: string
    response_time_ms: number
  }
}

// PreAuth 请求参数
export interface PreAuthRequest {
  device_id: string
  nozzle_id?: string
  command?: string  // 继承自 WayneCommandRequest，需要设置为 "preauth"
  auth_type: PreAuthType
  volume?: string | number
  amount?: string | number
  decimals?: number
  employee_id?: string
}

// PreAuth 响应
export interface PreAuthResponse {
  success: boolean
  message: string
  expires_in: number
}

// 设备过滤器
export interface DeviceFilterV2 {
  controller_id?: string
  station_id?: string
  status?: DeviceStatus
  type?: DeviceType
  page?: number
  page_size?: number
}

// ===== 交易接口定义 =====

// 交易实体 - 数字字段支持字符串类型（decimal.Decimal JSON序列化）
export interface TransactionV2 {
  id: string
  type: TransactionType
  status: TransactionStatus
  device_id: string
  controller_id: string
  nozzle_id?: string
  fuel_type?: string
  unit_price?: number | string
  preset_amount?: number | string
  preset_volume?: number | string
  actual_amount?: number | string
  actual_volume?: number | string
  total_amount?: number | string
  customer_id?: string
  customer_card?: string
  vehicle_id?: string
  payment_method?: string
  payment_ref?: string
  paid_amount?: number | string
  change_amount?: number | string
  operator_id?: string
  operator_name?: string
  started_at?: string
  completed_at?: string
  cancelled_at?: string
  created_at: string
  updated_at: string
  version: number
  extra_data?: Record<string, any>
  
  // 泵码数据字段 - Wayne DART DC101 支持
  start_volume_pump?: number | string    // 起始体积泵码
  end_volume_pump?: number | string      // 结束体积泵码
  start_amount_pump?: number | string    // 起始金额泵码
  end_amount_pump?: number | string      // 结束金额泵码
  pump_reading_source?: string           // 数据来源: DC101, MANUAL, CALCULATED, ESTIMATED
  pump_reading_quality?: string          // 数据质量: good, poor, bad, missing
  pump_reading_validated?: boolean       // 验证状态
  pump_discrepancy?: number | string     // 差异值（毫升）
}

// 交易查询过滤器
export interface TransactionFilter {
  device_id?: string
  nozzle_id?: string
  status?: TransactionStatus
  type?: TransactionType
  operator_id?: string
  station_id?: string
  start_time?: string
  end_time?: string
  min_amount?: number
  max_amount?: number
  min_volume?: number
  max_volume?: number
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: 'ASC' | 'DESC'
  
  // 泵码过滤器
  pump_reading_source?: string      // 数据来源过滤
  pump_reading_quality?: string     // 数据质量过滤
  pump_reading_validated?: boolean  // 验证状态过滤
  max_pump_discrepancy?: number     // 最大差异值过滤（毫升）
  has_pump_readings?: boolean       // 是否有泵码数据
}

// 交易列表响应
export interface TransactionListResult {
  transactions: TransactionV2[]
  total: number
  page: number
  limit: number
  has_more: boolean
}

// 交易统计过滤器
export interface TransactionStatsFilter {
  device_id?: string
  station_id?: string
  operator_id?: string
  start_time?: string
  end_time?: string
  group_by?: 'day' | 'hour' | 'device' | 'operator'
}

// 时期统计 - 数字字段支持字符串类型
export interface PeriodStat {
  period: string
  count: number
  volume: number | string
  amount: number | string
  avg_unit_price: number | string
}

// 交易统计结果 - 数字字段支持字符串类型
export interface TransactionStats {
  total_count: number
  total_volume: number | string
  total_amount: number | string
  average_volume: number | string
  average_amount: number | string
  average_unit_price: number | string
  completed_count: number
  cancelled_count: number
  failed_count: number
  period_stats?: PeriodStat[]
}

// 喷嘴交易汇总 - 数字字段支持字符串类型
export interface NozzleTransactionSummary {
  nozzle_id: string
  count: number
  volume: number | string
  amount: number | string
}

// 设备交易汇总 - 数字字段支持字符串类型
export interface DeviceTransactionSummary {
  device_id: string
  device_name: string
  total_count: number
  total_volume: number | string
  total_amount: number | string
  last_transaction?: TransactionV2
  nozzle_summaries: NozzleTransactionSummary[]
}

// 时间范围
export interface TimeRange {
  start: string
  end: string
}

// 泵码详情响应
export interface PumpReadingsResponse {
  transaction_id: string
  pump_readings: {
    start_volume_pump?: number | string
    end_volume_pump?: number | string
    start_amount_pump?: number | string
    end_amount_pump?: number | string
    pump_reading_source?: string
    pump_reading_quality?: string
    pump_reading_validated?: boolean
    pump_discrepancy?: number | string
  }
  analysis: {
    volume_difference?: number | string   // 泵码体积差值
    amount_difference?: number | string   // 泵码金额差值
    discrepancy_percentage?: number | string  // 差异百分比
    is_within_tolerance?: boolean         // 是否在容差范围内
    validation_errors?: string[]          // 验证错误
  }
  metadata: {
    data_completeness?: number | string   // 数据完整度 (0-1)
    quality_score?: number | string      // 质量评分 (0-1)
    last_updated?: string                // 最后更新时间
  }
}

// 泵码问题汇总
export interface PumpIssuesSummary {
  total_issues: number
  issues_by_type: {
    missing_readings?: number
    poor_quality?: number
    validation_failed?: number
    high_discrepancy?: number
  }
  transactions: TransactionV2[]
  statistics: {
    avg_discrepancy?: number | string
    max_discrepancy?: number | string
    quality_distribution?: Record<string, number>
    source_distribution?: Record<string, number>
  }
}

// ===== API 错误处理 =====
export class FCCV2APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message)
    this.name = 'FCCV2APIError'
  }
}

// ===== HTTP 客户端 =====
class HTTPClientV2 {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorText = await response.text()
        let errorMessage = `API request failed: ${response.status}`
        
        try {
          const errorData = JSON.parse(errorText)
          errorMessage = errorData.error || errorMessage
        } catch {
          errorMessage = errorText || errorMessage
        }
        
        throw new FCCV2APIError(errorMessage, response.status, errorText)
      }

      // 处理 204 No Content
      if (response.status === 204) {
        return null as any
      }

      const data = await response.json()
      return data
    } catch (error) {
      if (error instanceof FCCV2APIError) {
        throw error
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      throw new FCCV2APIError(`Network error: ${errorMessage}`, 0)
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// ===== FCC V2 API 客户端 =====
export class FCCV2APIClient {
  private http: HTTPClientV2

  constructor(baseURL?: string) {
    // 支持动态获取API URL
    const apiUrl = baseURL || getAPIBaseV2()
    this.http = new HTTPClientV2(apiUrl)
  }

  /**
   * 更新API基础URL
   * 用于运行时动态切换API服务器
   */
  updateBaseURL(baseURL: string): void {
    this.http = new HTTPClientV2(baseURL)
  }

  // ===== 设备管理 API =====
  
  async getDevices(filters?: DeviceFilterV2): Promise<DeviceListResponseV2> {
    try {
      const queryParams = new URLSearchParams()
      if (filters?.controller_id) queryParams.set('controller_id', filters.controller_id)
      if (filters?.station_id) queryParams.set('station_id', filters.station_id)
      if (filters?.status) queryParams.set('status', filters.status)
      if (filters?.type) queryParams.set('type', filters.type)
      if (filters?.page) queryParams.set('page', filters.page.toString())
      if (filters?.page_size) queryParams.set('page_size', filters.page_size.toString())
      
      const endpoint = queryParams.toString() ? `/devices?${queryParams}` : '/devices'
      console.log('[FCCV2API] Calling endpoint:', endpoint)
      const response = await this.http.get<DeviceListResponseV2>(endpoint)
      console.log('[FCCV2API] Raw response:', response)
      
      const result = {
        devices: response?.devices || [],
        total: response?.total || 0,
        page: response?.page,
        page_size: response?.page_size
      }
      console.log('[FCCV2API] Processed result:', result)
      console.log('[FCCV2API] Devices in result:', result.devices)
      
      return result
    } catch (error) {
      console.warn('[FCCV2API] Failed to fetch devices:', error)
      return {
        devices: [],
        total: 0
      }
    }
  }

  async getDevice(deviceId: string): Promise<DeviceV2> {
    return this.http.get<DeviceV2>(`/devices/${deviceId}`)
  }

  async createDevice(device: DeviceCreateRequestV2): Promise<DeviceV2> {
    // 验证 DART 协议地址范围
    if (device.device_address < 80 || device.device_address > 111) {
      throw new FCCV2APIError('Device address must be in range 80-111 (0x50-0x6F) for DART protocol', 400)
    }
    
    return this.http.post<DeviceV2>('/devices', device)
  }

  async updateDevice(deviceId: string, device: Partial<DeviceCreateRequestV2>): Promise<DeviceV2> {
    // 验证地址范围（如果提供了地址）
    if (device.device_address !== undefined && (device.device_address < 80 || device.device_address > 111)) {
      throw new FCCV2APIError('Device address must be in range 80-111 (0x50-0x6F) for DART protocol', 400)
    }
    
    return this.http.put<DeviceV2>(`/devices/${deviceId}`, device)
  }

  async deleteDevice(deviceId: string): Promise<void> {
    return this.http.delete<void>(`/devices/${deviceId}`)
  }

  async getDeviceStatus(deviceId: string): Promise<DeviceStatusV2> {
    try {
      return await this.http.get<DeviceStatusV2>(`/devices/${deviceId}/status`)
    } catch (error) {
      console.warn(`[FCCV2API] Failed to fetch status for device ${deviceId}:`, error)
      // 返回默认状态
      return {
        device_id: deviceId,
        status: 'unknown',
        last_seen: new Date().toISOString()
      }
    }
  }

  // ===== Wayne DART 协议 API =====
  
  async wayneAuthorize(deviceId: string, async = false, employeeId = '001', nozzleId?: string): Promise<WayneCommandResponseV2> {
    console.log(`[FCCV2API] wayneAuthorize called with deviceId: ${deviceId}, async: ${async}, employeeId: ${employeeId}, nozzleId: ${nozzleId}`)
    
    const payload: any = {
      device_id: deviceId,
      command: 'authorize',
      employee_id: employeeId,
      async
    }
    
    // 如果提供了 nozzleId，添加到请求中
    if (nozzleId) {
      payload.nozzle_id = nozzleId
      console.log(`[FCCV2API] Wayne authorize with nozzle ID: ${nozzleId}`)
    } else {
      console.log(`[FCCV2API] Wayne authorize for entire device`)
    }
    
    console.log(`[FCCV2API] Final payload for /wayne/authorize:`, payload)
    console.log(`[FCCV2API] Sending request to: ${this.http.baseURL}/wayne/authorize`)
    
    try {
      const response = await this.http.post<WayneCommandResponseV2>('/wayne/authorize', payload)
      console.log(`[FCCV2API] Wayne authorize response:`, response)
      return response
    } catch (error) {
      console.error(`[FCCV2API] Wayne authorize error:`, error)
      throw error
    }
  }

  async wayneReset(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/reset', {
      device_id: deviceId,
      command: 'reset',
      async
    })
  }

  async wayneStop(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/stop', {
      device_id: deviceId,
      command: 'stop',
      async
    })
  }

  async wayneGetStatus(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/status', {
      device_id: deviceId,
      command: 'return_status'
    })
  }

  async wayneGetFillingInfo(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/filling-info', {
      device_id: deviceId,
      command: 'return_filling_info'
    })
  }

  async waynePresetVolume(deviceId: string, volume: string, decimals = 3, employeeId = '001'): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/preset-volume', {
      device_id: deviceId,
      command: 'preset_volume',
      volume,
      decimals,
      employee_id: employeeId
    })
  }

  async waynePresetAmount(deviceId: string, amount: string, decimals = 0, employeeId = '001'): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/preset-amount', {
      device_id: deviceId,
      command: 'preset_amount',
      amount,
      decimals,
      employee_id: employeeId
    })
  }

  async wayneSuspendNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/suspend-nozzle', {
      device_id: deviceId,
      command: 'suspend_nozzle',
      nozzle_number: nozzleNumber
    })
  }

  async wayneResumeNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/resume-nozzle', {
      device_id: deviceId,
      command: 'resume_nozzle',
      nozzle_number: nozzleNumber
    })
  }

  async wayneResumeFuellingPoint(deviceId: string, async = false, employeeId = '001'): Promise<WayneCommandResponseV2> {
    console.log(`[FCCV2API] wayneResumeFuellingPoint called with deviceId: ${deviceId}, async: ${async}, employeeId: ${employeeId}`)
    
    const payload = {
      device_id: deviceId,
      command: 'resume_fuelling_point',
      employee_id: employeeId,
      async
    }
    
    console.log(`[FCCV2API] Final payload for /wayne/resume-fuelling-point:`, payload)
    console.log(`[FCCV2API] Sending request to: ${this.http.baseURL}/wayne/resume-fuelling-point`)
    
    try {
      const response = await this.http.post<WayneCommandResponseV2>('/wayne/resume-fuelling-point', payload)
      console.log(`[FCCV2API] Wayne resume fuelling point response:`, response)
      return response
    } catch (error) {
      console.error(`[FCCV2API] Wayne resume fuelling point error:`, error)
      throw error
    }
  }

  async wayneUpdatePrices(deviceId: string, prices: Array<{ nozzle_number: number; price: string; decimals?: number }>): Promise<WayneCommandResponseV2> {
    // 验证价格数据
    if (!prices || prices.length === 0) {
      throw new FCCV2APIError('Prices array is required and cannot be empty', 400)
    }
    
    for (const priceInfo of prices) {
      if (priceInfo.nozzle_number < 1 || priceInfo.nozzle_number > 8) {
        throw new FCCV2APIError(`Nozzle number must be between 1 and 8, got ${priceInfo.nozzle_number}`, 400)
      }
      
      const price = parseFloat(priceInfo.price)
      if (isNaN(price) || price <= 0) {
        throw new FCCV2APIError(`Price must be a positive number for nozzle ${priceInfo.nozzle_number}`, 400)
      }
      
      const decimals = priceInfo.decimals || 0
      if (decimals < 0 || decimals > 4) {
        throw new FCCV2APIError(`Decimals must be between 0 and 4 for nozzle ${priceInfo.nozzle_number}`, 400)
      }
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/update-prices', {
      device_id: deviceId,
      command: 'update_prices',
      prices
    })
  }

  async wayneConfigureNozzles(deviceId: string, nozzleNumbers: number[]): Promise<WayneCommandResponseV2> {
    // 验证喷嘴编号数组
    if (!nozzleNumbers || nozzleNumbers.length === 0) {
      throw new FCCV2APIError('nozzle_numbers is required', 400)
    }

    for (const nozzleNumber of nozzleNumbers) {
      if (nozzleNumber < 1 || nozzleNumber > 8) {
        throw new FCCV2APIError('Nozzle number must be between 1 and 8', 400)
      }
    }

    return this.http.post<WayneCommandResponseV2>('/wayne/configure-nozzles', {
      device_id: deviceId,
      command: 'configure_nozzles',
      nozzle_numbers: nozzleNumbers
    })
  }

  // ===== PreAuth API =====

  async waynePreauth(request: PreAuthRequest): Promise<PreAuthResponse> {
    console.log(`[FCCV2API] waynePreauth called with request:`, request)

    // 参数验证
    if (!request.device_id) {
      throw new FCCV2APIError('device_id is required', 400)
    }

    if (!request.auth_type) {
      throw new FCCV2APIError('auth_type is required', 400)
    }

    if (request.auth_type !== 'preset_volume' && request.auth_type !== 'preset_amount') {
      throw new FCCV2APIError('auth_type must be "preset_volume" or "preset_amount"', 400)
    }

    // 根据授权类型验证必需参数
    if (request.auth_type === 'preset_volume') {
      if (!request.volume) {
        throw new FCCV2APIError('volume is required when auth_type is "preset_volume"', 400)
      }
    } else if (request.auth_type === 'preset_amount') {
      if (!request.amount) {
        throw new FCCV2APIError('amount is required when auth_type is "preset_amount"', 400)
      }
    }

    // 构建请求负载
    const payload: any = {
      device_id: request.device_id,
      command: 'preauth',  // 必需字段，继承自 WayneCommandRequest
      auth_type: request.auth_type,
      employee_id: request.employee_id || '001',
      decimals: request.decimals || 2
    }

    // 添加可选的 nozzle_id
    if (request.nozzle_id) {
      payload.nozzle_id = request.nozzle_id
    }

    // 根据授权类型添加相应参数
    if (request.auth_type === 'preset_volume') {
      payload.volume = request.volume
    } else if (request.auth_type === 'preset_amount') {
      payload.amount = request.amount
    }

    console.log(`[FCCV2API] Final payload for /wayne/preauth:`, payload)
    console.log(`[FCCV2API] Sending request to: ${this.http.baseURL}/wayne/preauth`)

    try {
      const response = await this.http.post<PreAuthResponse>('/wayne/preauth', payload)
      console.log(`[FCCV2API] Wayne preauth response:`, response)
      return response
    } catch (error) {
      console.error(`[FCCV2API] Wayne preauth error:`, error)
      throw error
    }
  }

  // ===== 交易管理 API =====
  
  async getTransactions(filter?: TransactionFilter): Promise<TransactionListResult> {
    try {
      const queryParams = new URLSearchParams()
      if (filter?.device_id) queryParams.set('device_id', filter.device_id)
      if (filter?.nozzle_id) queryParams.set('nozzle_id', filter.nozzle_id)
      if (filter?.status) queryParams.set('status', filter.status)
      if (filter?.type) queryParams.set('type', filter.type)
      if (filter?.operator_id) queryParams.set('operator_id', filter.operator_id)
      if (filter?.station_id) queryParams.set('station_id', filter.station_id)
      if (filter?.start_time) queryParams.set('start_time', filter.start_time)
      if (filter?.end_time) queryParams.set('end_time', filter.end_time)
      if (filter?.min_amount) queryParams.set('min_amount', filter.min_amount.toString())
      if (filter?.max_amount) queryParams.set('max_amount', filter.max_amount.toString())
      if (filter?.min_volume) queryParams.set('min_volume', filter.min_volume.toString())
      if (filter?.max_volume) queryParams.set('max_volume', filter.max_volume.toString())
      if (filter?.page) queryParams.set('page', filter.page.toString())
      if (filter?.limit) queryParams.set('limit', filter.limit.toString())
      if (filter?.sort_by) queryParams.set('sort_by', filter.sort_by)
      if (filter?.sort_order) queryParams.set('sort_order', filter.sort_order)
      
      const endpoint = queryParams.toString() ? `/transactions?${queryParams}` : '/transactions'
      console.log('[FCCV2API] Calling transactions endpoint (v2):', API_BASE_V2 + endpoint)
      const response = await this.http.get<TransactionListResult>(endpoint)
      console.log('[FCCV2API] Transactions response:', response)
      
      return response
    } catch (error) {
      console.warn('[FCCV2API] Failed to fetch transactions:', error)
      return {
        transactions: [],
        total: 0,
        page: filter?.page || 1,
        limit: filter?.limit || 20,
        has_more: false
      }
    }
  }

  async getTransaction(transactionId: string): Promise<TransactionV2> {
    console.log('[FCCV2API] Fetching transaction (v2):', transactionId)
    return this.http.get<TransactionV2>(`/transactions/${transactionId}`)
  }

  async getDeviceTransactions(deviceId: string, filter?: TransactionFilter): Promise<TransactionListResult> {
    try {
      const queryParams = new URLSearchParams()
      if (filter?.nozzle_id) queryParams.set('nozzle_id', filter.nozzle_id)
      if (filter?.status) queryParams.set('status', filter.status)
      if (filter?.type) queryParams.set('type', filter.type)
      if (filter?.operator_id) queryParams.set('operator_id', filter.operator_id)
      if (filter?.start_time) queryParams.set('start_time', filter.start_time)
      if (filter?.end_time) queryParams.set('end_time', filter.end_time)
      if (filter?.min_amount) queryParams.set('min_amount', filter.min_amount.toString())
      if (filter?.max_amount) queryParams.set('max_amount', filter.max_amount.toString())
      if (filter?.min_volume) queryParams.set('min_volume', filter.min_volume.toString())
      if (filter?.max_volume) queryParams.set('max_volume', filter.max_volume.toString())
      if (filter?.page) queryParams.set('page', filter.page.toString())
      if (filter?.limit) queryParams.set('limit', filter.limit.toString())
      if (filter?.sort_by) queryParams.set('sort_by', filter.sort_by)
      if (filter?.sort_order) queryParams.set('sort_order', filter.sort_order)
      
      const endpoint = queryParams.toString() 
        ? `/devices/${deviceId}/transactions?${queryParams}` 
        : `/devices/${deviceId}/transactions`
      
      console.log('[FCCV2API] Calling device transactions endpoint (v2):', API_BASE_V2 + endpoint)
      const response = await this.http.get<TransactionListResult>(endpoint)
      console.log('[FCCV2API] Device transactions response:', response)
      
      return response
    } catch (error) {
      console.warn('[FCCV2API] Failed to fetch device transactions:', error)
      return {
        transactions: [],
        total: 0,
        page: filter?.page || 1,
        limit: filter?.limit || 20,
        has_more: false
      }
    }
  }

  async getTransactionStats(filter?: TransactionStatsFilter): Promise<TransactionStats> {
    try {
      const queryParams = new URLSearchParams()
      if (filter?.device_id) queryParams.set('device_id', filter.device_id)
      if (filter?.station_id) queryParams.set('station_id', filter.station_id)
      if (filter?.operator_id) queryParams.set('operator_id', filter.operator_id)
      if (filter?.start_time) queryParams.set('start_time', filter.start_time)
      if (filter?.end_time) queryParams.set('end_time', filter.end_time)
      if (filter?.group_by) queryParams.set('group_by', filter.group_by)
      
      const endpoint = queryParams.toString() ? `/transactions/stats?${queryParams}` : '/transactions/stats'
      console.log('[FCCV2API] Calling transaction stats endpoint (v2):', API_BASE_V2 + endpoint)
      const response = await this.http.get<TransactionStats>(endpoint)
      console.log('[FCCV2API] Transaction stats response:', response)
      
      return response
    } catch (error) {
      console.warn('[FCCV2API] Failed to fetch transaction stats:', error)
      return {
        total_count: 0,
        total_volume: 0,
        total_amount: 0,
        average_volume: 0,
        average_amount: 0,
        average_unit_price: 0,
        completed_count: 0,
        cancelled_count: 0,
        failed_count: 0
      }
    }
  }

  async getDeviceTransactionSummary(deviceId: string, timeRange?: TimeRange): Promise<DeviceTransactionSummary> {
    try {
      const queryParams = new URLSearchParams()
      if (timeRange?.start) queryParams.set('start_time', timeRange.start)
      if (timeRange?.end) queryParams.set('end_time', timeRange.end)
      
      const endpoint = queryParams.toString() 
        ? `/devices/${deviceId}/transactions/summary?${queryParams}` 
        : `/devices/${deviceId}/transactions/summary`
      
      console.log('[FCCV2API] Calling device transaction summary endpoint (v2):', API_BASE_V2 + endpoint)
      const response = await this.http.get<DeviceTransactionSummary>(endpoint)
      console.log('[FCCV2API] Device transaction summary response:', response)
      
      return response
    } catch (error) {
      console.warn('[FCCV2API] Failed to fetch device transaction summary:', error)
      return {
        device_id: deviceId,
        device_name: '',
        total_count: 0,
        total_volume: 0,
        total_amount: 0,
        nozzle_summaries: []
      }
    }
  }

  // ===== 泵码管理 API - Wayne DART 协议支持 =====
  
  async getTransactionPumpReadings(transactionId: string): Promise<PumpReadingsResponse> {
    try {
      console.log('[FCCV2API] Getting transaction pump readings:', transactionId)
      return await this.http.get<PumpReadingsResponse>(`/transactions/${transactionId}/pump-readings`)
    } catch (error) {
      console.warn(`[FCCV2API] Failed to get pump readings for transaction ${transactionId}:`, error)
      throw error
    }
  }
  
  async getTransactionsWithPumpIssues(filter?: TransactionFilter): Promise<PumpIssuesSummary> {
    try {
      const queryParams = new URLSearchParams()
      if (filter?.device_id) queryParams.set('device_id', filter.device_id)
      if (filter?.pump_reading_quality) queryParams.set('pump_reading_quality', filter.pump_reading_quality)
      if (filter?.pump_reading_validated !== undefined) queryParams.set('pump_reading_validated', filter.pump_reading_validated.toString())
      if (filter?.max_pump_discrepancy) queryParams.set('max_pump_discrepancy', filter.max_pump_discrepancy.toString())
      if (filter?.start_time) queryParams.set('start_time', filter.start_time)
      if (filter?.end_time) queryParams.set('end_time', filter.end_time)
      
      const endpoint = queryParams.toString() ? `/transactions/pump-issues?${queryParams}` : '/transactions/pump-issues'
      
      console.log('[FCCV2API] Getting transactions with pump issues:', endpoint)
      return await this.http.get<PumpIssuesSummary>(endpoint)
    } catch (error) {
      console.warn('[FCCV2API] Failed to get transactions with pump issues:', error)
      throw error
    }
  }

  // ===== 便捷方法别名 =====
  
  async authorize(deviceId: string, async = false, employeeId = '001', nozzleId?: string): Promise<WayneCommandResponseV2> {
    return this.wayneAuthorize(deviceId, async, employeeId, nozzleId)
  }

  async reset(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.wayneReset(deviceId, async)
  }

  async stop(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.wayneStop(deviceId, async)
  }

  async getStatus(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.wayneGetStatus(deviceId)
  }

  async getFillingInfo(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.wayneGetFillingInfo(deviceId)
  }

  async presetVolume(deviceId: string, volume: string, decimals = 3, employeeId = '001'): Promise<WayneCommandResponseV2> {
    return this.waynePresetVolume(deviceId, volume, decimals, employeeId)
  }

  async presetAmount(deviceId: string, amount: string, decimals = 0, employeeId = '001'): Promise<WayneCommandResponseV2> {
    return this.waynePresetAmount(deviceId, amount, decimals, employeeId)
  }

  async suspendNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    return this.wayneSuspendNozzle(deviceId, nozzleNumber)
  }

  async resumeNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    return this.wayneResumeNozzle(deviceId, nozzleNumber)
  }

  async resumeFuellingPoint(deviceId: string, async = false, employeeId = '001'): Promise<WayneCommandResponseV2> {
    return this.wayneResumeFuellingPoint(deviceId, async, employeeId)
  }

  async updatePrices(deviceId: string, prices: Array<{ nozzle_number: number; price: string; decimals?: number }>): Promise<WayneCommandResponseV2> {
    return this.wayneUpdatePrices(deviceId, prices)
  }

  async configureNozzles(deviceId: string, nozzleNumbers: number[]): Promise<WayneCommandResponseV2> {
    return this.wayneConfigureNozzles(deviceId, nozzleNumbers)
  }

  async preauth(request: PreAuthRequest): Promise<PreAuthResponse> {
    return this.waynePreauth(request)
  }

  // ===== Nozzle 管理 API =====
  
  async getDeviceNozzles(deviceId: string): Promise<NozzleV2[]> {
    try {
      const response = await this.http.get<DeviceNozzlesResponseV2 | NozzleV2[]>(`/devices/${deviceId}/nozzles`)
      console.log('[FCCV2API] Raw nozzles response:', response)
      
      // 处理不同的响应格式
      if (Array.isArray(response)) {
        // 如果直接返回数组 (标准格式)
        console.log('[FCCV2API] Received nozzles as array:', response.length)
        return response
      } else if (response && Array.isArray(response.nozzles)) {
        // 如果返回包含 nozzles 字段的对象 (当前后端格式)
        console.log('[FCCV2API] Received nozzles in object format, extracting array:', response.nozzles.length)
        return response.nozzles.map((nozzle) => ({
          id: `${deviceId}-nozzle-${nozzle.nozzle_number}`,
          number: nozzle.nozzle_number,
          name: nozzle.fuel_grade_name || `Nozzle ${nozzle.nozzle_number}`,
          device_id: deviceId,
          fuel_grade_id: nozzle.fuel_grade_name || '',
          current_price: parseFloat(nozzle.current_price) || 0,
          position: `Position ${nozzle.nozzle_number}`,
          is_enabled: nozzle.is_enabled || false,
          created_at: nozzle.last_update || new Date().toISOString(),
          updated_at: nozzle.last_update || new Date().toISOString()
        } as NozzleV2))
      } else {
        console.warn('[FCCV2API] Unexpected nozzles response format:', response)
        return []
      }
    } catch (error) {
      console.warn(`[FCCV2API] Failed to fetch nozzles for device ${deviceId}:`, error)
      return []
    }
  }

  async getDeviceNozzle(deviceId: string, nozzleNumber: number): Promise<NozzleV2> {
    return this.http.get<NozzleV2>(`/devices/${deviceId}/nozzles/${nozzleNumber}`)
  }

  async getNozzleTransaction(deviceId: string, nozzleNumber: number): Promise<any> {
    try {
      return await this.http.get(`/devices/${deviceId}/nozzles/${nozzleNumber}/transaction`)
    } catch (error) {
      console.warn(`[FCCV2API] Failed to fetch nozzle transaction for device ${deviceId}, nozzle ${nozzleNumber}:`, error)
      return null
    }
  }

  async getNozzleStats(deviceId: string, nozzleNumber: number): Promise<any> {
    try {
      return await this.http.get(`/devices/${deviceId}/nozzles/${nozzleNumber}/stats`)
    } catch (error) {
      console.warn(`[FCCV2API] Failed to fetch nozzle stats for device ${deviceId}, nozzle ${nozzleNumber}:`, error)
      return null
    }
  }

  // 保留旧的接口作为兼容性别名
  async getNozzle(nozzleId: string): Promise<NozzleV2> {
    // 解析 nozzleId 格式: deviceId-nozzle-number 或直接使用
    console.warn('[FCCV2API] getNozzle with nozzleId is deprecated, use getDeviceNozzle instead')
    throw new FCCV2APIError('getNozzle with nozzleId is deprecated, use getDeviceNozzle(deviceId, nozzleNumber) instead', 400)
  }

  async updateNozzle(nozzleId: string, nozzle: Partial<NozzleUpdateRequestV2>): Promise<NozzleV2> {
    console.warn('[FCCV2API] updateNozzle with nozzleId is not implemented yet')
    throw new FCCV2APIError('updateNozzle is not implemented yet', 501)
  }

  // ===== 🔧 新增：异步调价 API =====
  
  /**
   * 设置喷嘴目标价格（异步调价）
   * @param deviceId 设备ID
   * @param nozzleNumber 喷嘴编号 (1-15)
   * @param targetPrice 目标价格
   */
  async setNozzleTargetPrice(deviceId: string, nozzleNumber: number, targetPrice: number): Promise<any> {
    const payload = {
      target_price: targetPrice
    }
    
    return this.http.post<any>(`/devices/${deviceId}/nozzles/${nozzleNumber}/target-price`, payload)
  }

  /**
   * 获取设备价格同步状态
   * @param deviceId 设备ID
   */
  async getPriceSyncStatus(deviceId: string): Promise<PriceSyncStatus> {
    return this.http.get<PriceSyncStatus>(`/devices/${deviceId}/price-sync-status`)
  }

  // ===== 油品管理接口 =====

  // ===== 油品管理接口 =====
  // 注意：后端实现的是fuel-sync路由，而不是直接的fuel-grades路由

  // 获取油品列表
  async getFuelGrades(): Promise<FuelGrade[]> {
    const response = await this.http.get<FuelGrade[]>('/fuel-sync/fuel-grades')
    return response
  }

  // 获取单个油品信息
  async getFuelGrade(gradeId: string): Promise<FuelGrade> {
    const response = await this.http.get<FuelGrade>(`/fuel-sync/fuel-grades/${gradeId}`)
    return response
  }

  // 创建油品 - 注意：后端可能没有直接的创建接口，需要通过同步实现
  async createFuelGrade(grade: Omit<FuelGrade, 'id' | 'created_at' | 'updated_at'>): Promise<FuelGrade> {
    // 后端可能没有直接创建接口，这里先保留，可能需要调整
    const response = await this.http.post<FuelGrade>('/fuel-sync/fuel-grades', grade)
    return response
  }

  // 更新油品 - 注意：后端可能没有直接的更新接口，需要通过同步实现
  async updateFuelGrade(gradeId: string, grade: Partial<Omit<FuelGrade, 'id' | 'created_at' | 'updated_at'>>): Promise<FuelGrade> {
    // 后端可能没有直接更新接口，这里先保留，可能需要调整
    const response = await this.http.put<FuelGrade>(`/fuel-sync/fuel-grades/${gradeId}`, grade)
    return response
  }

  // 删除油品（软删除） - 注意：后端可能没有直接的删除接口
  async deleteFuelGrade(gradeId: string): Promise<void> {
    // 后端可能没有直接删除接口，这里先保留，可能需要调整
    await this.http.delete(`/fuel-sync/fuel-grades/${gradeId}`)
  }

  // 恢复已删除的油品 - 注意：后端可能没有直接的恢复接口
  async restoreFuelGrade(gradeId: string): Promise<FuelGrade> {
    // 后端可能没有直接恢复接口，这里先保留，可能需要调整
    const response = await this.http.post<FuelGrade>(`/fuel-sync/fuel-grades/${gradeId}/restore`)
    return response
  }

  // ===== 油品同步接口 =====

  // 获取同步状态
  async getFuelSyncStatus(): Promise<FuelSyncStatusInfo> {
    const response = await this.http.get<FuelSyncStatusInfo>('/fuel-sync/status')
    return response
  }

  // 触发手动同步
  async triggerFuelSync(): Promise<{ success: boolean; message: string }> {
    const response = await this.http.post<{ success: boolean; message: string }>('/fuel-sync/trigger')
    return response
  }

  // 获取同步历史
  async getFuelSyncHistory(limit = 10): Promise<FuelSyncHistory[]> {
    const response = await this.http.get<FuelSyncHistory[]>(`/fuel-sync/history?limit=${limit}`)
    return response
  }

  // 获取外部油品数据（用于预览）
  async getExternalOilProducts(): Promise<ExternalOilProduct[]> {
    const response = await this.http.get<ExternalOilProduct[]>('/fuel-sync/external-products')
    return response
  }

  // 测试外部API连接
  async testExternalFuelAPI(): Promise<{ success: boolean; message: string; data?: any }> {
    const response = await this.http.get<{ success: boolean; message: string; data?: any }>('/fuel-sync/test-connection')
    return response
  }

  // 获取同步配置
  async getFuelSyncConfig(): Promise<any> {
    const response = await this.http.get<any>('/fuel-sync/config')
    return response
  }

  // 更新同步配置
  async updateFuelSyncConfig(config: any): Promise<any> {
    const response = await this.http.put<any>('/fuel-sync/config', config)
    return response
  }

  // 启动同步调度器
  async startFuelSyncScheduler(): Promise<{ success: boolean; message: string }> {
    const response = await this.http.post<{ success: boolean; message: string }>('/fuel-sync/start')
    return response
  }

  // 停止同步调度器
  async stopFuelSyncScheduler(): Promise<{ success: boolean; message: string }> {
    const response = await this.http.post<{ success: boolean; message: string }>('/fuel-sync/stop')
    return response
  }

  // 获取同步统计信息
  async getFuelSyncStatistics(): Promise<any> {
    const response = await this.http.get<any>('/fuel-sync/statistics')
    return response
  }

  // 健康检查
  async fuelSyncHealthCheck(): Promise<{ success: boolean; message: string }> {
    const response = await this.http.get<{ success: boolean; message: string }>('/fuel-sync/health')
    return response
  }
}

// ===== 导出默认实例 =====
export const fccV2API = new FCCV2APIClient()

// ===== 向后兼容的接口别名 =====
export type Device = DeviceV2
export type DeviceStatusInfo = DeviceStatusV2

// 控制器类型（简化版，因为V2主要关注设备管理）
export interface Controller {
  id: string
  name: string
  type: string
  status: string
  protocol?: string
  health?: string
  address?: string
  station_id?: string
  last_seen?: string
  config?: Record<string, any>
  devices?: any[]
  created_at: string
  updated_at: string
}

// ===== 兼容性API包装器 =====
export const api = {
  devices: {
    list: async (): Promise<Device[]> => {
      console.log('[API Compatibility] Calling fccV2API.getDevices()')
      const response = await fccV2API.getDevices()
      console.log('[API Compatibility] getDevices response:', response)
      console.log('[API Compatibility] returning devices:', response.devices)
      return response.devices
    },
    
    get: async (deviceId: string): Promise<Device> => {
      return fccV2API.getDevice(deviceId)
    },
    
    create: async (device: Partial<Device>): Promise<Device> => {
      const createRequest: DeviceCreateRequestV2 = {
        id: device.id || '',
        name: device.name || '',
        type: device.type || 'pump',
        controller_id: device.controller_id || '',
        device_address: device.device_address || 80,
        station_id: device.station_id || '',
        island_id: device.island_id,
        position: device.position,
        config: device.config
      }
      return fccV2API.createDevice(createRequest)
    },
    
    update: async (deviceId: string, device: Partial<Device>): Promise<Device> => {
      return fccV2API.updateDevice(deviceId, device)
    },
    
    delete: async (deviceId: string): Promise<void> => {
      return fccV2API.deleteDevice(deviceId)
    },
    
    getStatus: async (deviceId: string): Promise<DeviceStatusInfo> => {
      return fccV2API.getDeviceStatus(deviceId)
    }
  },
  
  controllers: {
    list: async (): Promise<{ controllers: Controller[] }> => {
      // V2 API doesn't have dedicated controller endpoints
      // Return empty array for compatibility
      console.warn('[API Compatibility] Controllers endpoint not implemented in V2')
      return { controllers: [] }
    },
    
    create: async (controller: Partial<Controller>): Promise<Controller> => {
      console.warn('[API Compatibility] Controller creation not implemented in V2')
      throw new Error('Controller management not available in V2 API')
    },
    
    update: async (controllerId: string, controller: Partial<Controller>): Promise<Controller> => {
      console.warn('[API Compatibility] Controller update not implemented in V2')
      throw new Error('Controller management not available in V2 API')
    },
    
    delete: async (controllerId: string): Promise<void> => {
      console.warn('[API Compatibility] Controller deletion not implemented in V2')
      throw new Error('Controller management not available in V2 API')
    },
    
    discoverDevices: async (controllerId: string): Promise<{ total: number }> => {
      console.warn('[API Compatibility] Device discovery not implemented in V2')
      return { total: 0 }
    }
  },
  
  commands: {
    execute: async (deviceId: string, command: any): Promise<any> => {
      // Map generic commands to Wayne protocol commands
      const commandType = command.command_type?.toLowerCase()
      
      try {
        switch (commandType) {
          case 'authorize':
            return fccV2API.wayneAuthorize(deviceId, command.async || false, command.parameters?.employee_id || '001')
          case 'reset':
            return fccV2API.wayneReset(deviceId, command.async || false)
          case 'stop':
            return fccV2API.wayneStop(deviceId, command.async || false)
          case 'status':
          case 'return_status':
            return fccV2API.wayneGetStatus(deviceId)
          case 'filling_info':
          case 'return_filling_info':
            return fccV2API.wayneGetFillingInfo(deviceId)
          case 'preset_volume':
            return fccV2API.waynePresetVolume(deviceId, command.parameters?.volume || '0', command.parameters?.decimals || 3, command.parameters?.employee_id || '001')
          case 'preset_amount':
            return fccV2API.waynePresetAmount(deviceId, command.parameters?.amount || '0', command.parameters?.decimals || 0, command.parameters?.employee_id || '001')
          case 'suspend_nozzle':
            return fccV2API.wayneSuspendNozzle(deviceId, command.parameters?.nozzle_number || 1)
          case 'resume_nozzle':
            return fccV2API.wayneResumeNozzle(deviceId, command.parameters?.nozzle_number || 1)
          case 'resume_fuelling_point':
            return fccV2API.wayneResumeFuellingPoint(deviceId, command.async || false, command.parameters?.employee_id || '001')
          case 'preauth':
            const preauthRequest: PreAuthRequest = {
              device_id: deviceId,
              nozzle_id: command.parameters?.nozzle_id,
              auth_type: command.parameters?.auth_type || 'preset_volume',
              volume: command.parameters?.volume,
              amount: command.parameters?.amount,
              decimals: command.parameters?.decimals || 2,
              employee_id: command.parameters?.employee_id || '001'
            }
            return fccV2API.waynePreauth(preauthRequest)
          default:
            console.warn(`[API Compatibility] Unknown command type: ${commandType}`)
            return {
              command_id: `compat_${Date.now()}`,
              device_id: deviceId,
              command: commandType,
              success: false,
              error: `Command type '${commandType}' not supported`,
              execution_time_ms: 0,
              submitted_at: new Date().toISOString()
            }
        }
      } catch (error) {
        return {
          command_id: `compat_${Date.now()}`,
          device_id: deviceId,
          command: commandType,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          execution_time_ms: 0,
          submitted_at: new Date().toISOString()
        }
      }
    }
  }
}

// ===== V2 API 对象结构 =====
export const apiV2 = {
  devices: {
    list: async (): Promise<DeviceV2[]> => {
      console.log('[API V2] Calling fccV2API.getDevices()')
      const response = await fccV2API.getDevices()
      console.log('[API V2] getDevices response:', response)
      console.log('[API V2] returning devices:', response.devices)
      return response.devices
    },
    
    get: async (deviceId: string): Promise<DeviceV2> => {
      return fccV2API.getDevice(deviceId)
    },
    
    create: async (device: DeviceCreateRequestV2): Promise<DeviceV2> => {
      return fccV2API.createDevice(device)
    },
    
    update: async (deviceId: string, device: Partial<DeviceCreateRequestV2>): Promise<DeviceV2> => {
      return fccV2API.updateDevice(deviceId, device)
    },
    
    delete: async (deviceId: string): Promise<void> => {
      return fccV2API.deleteDevice(deviceId)
    },
    
    getStatus: async (deviceId: string): Promise<DeviceStatusV2> => {
      return fccV2API.getDeviceStatus(deviceId)
    },
    
    // 这些方法在V2中不存在，提供兼容性包装
    getPumpStatus: async (deviceId: string): Promise<any> => {
      console.warn('[API V2] getPumpStatus not implemented, using getStatus instead')
      return fccV2API.getDeviceStatus(deviceId)
    },
    
    getPumpTotals: async (deviceId: string): Promise<any> => {
      console.warn('[API V2] getPumpTotals not implemented')
      return { totals: [] }
    },

    // 喷嘴管理 API
    getDeviceNozzles: async (deviceId: string): Promise<NozzleV2[]> => {
      return fccV2API.getDeviceNozzles(deviceId)
    },

    getNozzle: async (nozzleId: string): Promise<NozzleV2> => {
      return fccV2API.getNozzle(nozzleId)
    },

    updateNozzle: async (nozzleId: string, nozzle: Partial<NozzleUpdateRequestV2>): Promise<NozzleV2> => {
      return fccV2API.updateNozzle(nozzleId, nozzle)
    }
  },
  
  wayne: {
    authorize: async (params: { device_id: string, nozzle_mask?: number, nozzle_id?: string, employee_id?: string }): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneAuthorize(params.device_id, false, params.employee_id || '001', params.nozzle_id)
    },
    
    reset: async (deviceId: string): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneReset(deviceId, false)
    },
    
    stop: async (deviceId: string): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneStop(deviceId, false)
    },
    
    getStatus: async (deviceId: string): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneGetStatus(deviceId)
    },
    
    getFillingInfo: async (deviceId: string): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneGetFillingInfo(deviceId)
    },
    
    presetVolume: async (params: { device_id: string, volume: string, decimals?: number, employee_id?: string }): Promise<WayneCommandResponseV2> => {
      return fccV2API.waynePresetVolume(params.device_id, params.volume, params.decimals || 3, params.employee_id || '001')
    },
    
    presetAmount: async (params: { device_id: string, amount: string, decimals?: number, employee_id?: string }): Promise<WayneCommandResponseV2> => {
      return fccV2API.waynePresetAmount(params.device_id, params.amount, params.decimals || 0, params.employee_id || '001')
    },
    
    suspendNozzle: async (params: { device_id: string, nozzle_id: number }): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneSuspendNozzle(params.device_id, params.nozzle_id)
    },
    
    resumeNozzle: async (params: { device_id: string, nozzle_id: number }): Promise<WayneCommandResponseV2> => {
      return fccV2API.wayneResumeNozzle(params.device_id, params.nozzle_id)
    },

    preauth: async (request: PreAuthRequest): Promise<PreAuthResponse> => {
      return fccV2API.waynePreauth(request)
    }
  },

  // 价格管理
  updatePrices: async (deviceId: string, prices: Array<{ nozzle_number: number; price: string; decimals?: number }>): Promise<WayneCommandResponseV2> => {
    return fccV2API.updatePrices(deviceId, prices)
  },

  nozzles: {
    getDeviceNozzles: async (deviceId: string): Promise<NozzleV2[]> => {
      return fccV2API.getDeviceNozzles(deviceId)
    },

    getDeviceNozzle: async (deviceId: string, nozzleNumber: number): Promise<NozzleV2> => {
      return fccV2API.getDeviceNozzle(deviceId, nozzleNumber)
    },

    getNozzleTransaction: async (deviceId: string, nozzleNumber: number): Promise<NozzleTransactionV2 | null> => {
      return fccV2API.getNozzleTransaction(deviceId, nozzleNumber)
    },

    getNozzleStats: async (deviceId: string, nozzleNumber: number): Promise<NozzleStatsV2 | null> => {
      return fccV2API.getNozzleStats(deviceId, nozzleNumber)
    },

    // 保留旧接口作为兼容性
    getNozzle: async (nozzleId: string): Promise<NozzleV2> => {
      return fccV2API.getNozzle(nozzleId)
    },

    updateNozzle: async (nozzleId: string, nozzle: Partial<NozzleUpdateRequestV2>): Promise<NozzleV2> => {
      return fccV2API.updateNozzle(nozzleId, nozzle)
    }
  },
  
  system: {
    health: async (): Promise<any> => {
      console.warn('[API V2] System health endpoint not implemented')
      return { status: 'unknown' }
    },
    
    status: async (): Promise<any> => {
      console.warn('[API V2] System status endpoint not implemented')
      return { status: 'unknown' }
    },
    
    metrics: async (): Promise<any> => {
      console.warn('[API V2] System metrics endpoint not implemented')
      return { metrics: {} }
    }
  },
  
  // ===== 交易管理 API =====
  transactions: {
    list: async (filter?: TransactionFilter): Promise<TransactionListResult> => {
      return fccV2API.getTransactions(filter)
    },
    
    get: async (transactionId: string): Promise<TransactionV2> => {
      return fccV2API.getTransaction(transactionId)
    },
    
    getByDevice: async (deviceId: string, filter?: TransactionFilter): Promise<TransactionListResult> => {
      return fccV2API.getDeviceTransactions(deviceId, filter)
    },
    
    getStats: async (filter?: TransactionStatsFilter): Promise<TransactionStats> => {
      return fccV2API.getTransactionStats(filter)
    },
    
    getDeviceSummary: async (deviceId: string, timeRange?: TimeRange): Promise<DeviceTransactionSummary> => {
      return fccV2API.getDeviceTransactionSummary(deviceId, timeRange)
    },
    
    // 泵码管理 API
    getPumpReadings: async (transactionId: string): Promise<PumpReadingsResponse> => {
      return fccV2API.getTransactionPumpReadings(transactionId)
    },
    
    getPumpIssues: async (filter?: TransactionFilter): Promise<PumpIssuesSummary> => {
      return fccV2API.getTransactionsWithPumpIssues(filter)
    }
  },
  
  dispatch: {
    getStatus: async (): Promise<any> => {
      console.warn('[API V2] Dispatch status endpoint not implemented')
      return { status: 'unknown' }
    },
    
    start: async (): Promise<any> => {
      console.warn('[API V2] Dispatch start endpoint not implemented')
      return { success: false, message: 'Not implemented' }
    },
    
    stop: async (): Promise<any> => {
      console.warn('[API V2] Dispatch stop endpoint not implemented')
      return { success: false, message: 'Not implemented' }
    },
    
    getDevices: async (): Promise<any[]> => {
      console.warn('[API V2] Dispatch getDevices endpoint not implemented')
      return []
    }
  }
}

// 兼容性导出
export default fccV2API

// ===== Nozzle 相关接口定义 =====

// 喷嘴对象 (V2版本)
export interface NozzleV2 {
  id: string
  number: number               // 1-8 (Wayne 协议限制)
  name: string
  device_id: string
  fuel_grade_id: string
  current_price: number
  position: string
  is_enabled: boolean
  created_at: string
  updated_at: string
}

// Nozzle 更新请求
export interface NozzleUpdateRequestV2 {
  name?: string
  fuel_grade_id?: string
  current_price?: number
  position?: string
  is_enabled?: boolean
}

// Nozzle 状态信息
export interface NozzleStatusV2 {
  id: string
  number: number
  status: NozzleState
  is_out: boolean
  is_selected: boolean
  current_volume: number
  current_amount: number
  current_price: number
  preset_volume?: number
  preset_amount?: number
  transaction_id?: string
  last_update: string
}

// 后端返回的设备喷嘴响应格式
export interface DeviceNozzlesResponseV2 {
  device_id: string
  device_name: string
  total_nozzles: number
  active_count: number
  nozzles: {
    nozzle_number: number
    status: string
    is_out: boolean
    is_selected: boolean
    is_enabled: boolean
    current_price: string
    current_volume: string
    current_amount: string
    total_volume: string
    total_amount: string
    last_update: string
    fuel_grade_name: string
    transaction_count: number
  }[]
  last_update: string
}

// Nozzle 交易信息
export interface NozzleTransactionV2 {
  nozzle_number: number
  device_id: string
  transaction_id?: string
  status: 'idle' | 'selected' | 'authorized' | 'dispensing' | 'completed' | 'suspended'
  is_out: boolean
  preset_volume?: number
  preset_amount?: number
  current_volume: number
  current_amount: number
  current_price: number
  started_at?: string
  completed_at?: string
  last_update: string
}

// Nozzle 统计信息
export interface NozzleStatsV2 {
  nozzle_number: number
  device_id: string
  total_transactions: number
  total_volume: number
  total_amount: number
  average_transaction_volume: number
  average_transaction_amount: number
  last_transaction_at?: string
  uptime_percentage: number
  error_count: number
  maintenance_count: number
  period_stats?: {
    today: {
      transactions: number
      volume: number
      amount: number
    }
    this_week: {
      transactions: number
      volume: number
      amount: number
    }
    this_month: {
      transactions: number
      volume: number
      amount: number
    }
  }
}

// 价格同步状态
export interface PriceSyncStatus {
  device_id: string
  synced: number      // 已同步的喷嘴数量
  pending: number     // 待同步的喷嘴数量
  sending: number     // 发送中的喷嘴数量
  failed: number      // 失败的喷嘴数量
  timeout: number     // 超时的喷嘴数量
  total: number       // 总喷嘴数量
}

// 异步调价响应
export interface AsyncPriceUpdateResponse {
  success: boolean
  device_id: string
  nozzle_number: number
  target_price: number
  message: string
  timestamp: string
} 