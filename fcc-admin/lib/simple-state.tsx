'use client'

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { Device, Controller, DeviceStatusInfo, api, DeviceV2, DeviceCreateRequestV2, DeviceStatusV2, apiV2, NozzleV2, NozzleUpdateRequestV2, NozzleTransactionV2, NozzleStatsV2, FuelGrade, FuelSyncStatusInfo, FuelSyncHistory, ExternalOilProduct, fccV2API } from './api-client-v2'
import { usePolling, PollingCallbacks } from './polling-service'

// 简化的通知接口
export interface SimpleNotification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  timestamp: Date
}

// 扩展简化的全局状态接口，添加 v2 支持
export interface SimpleState {
  // 核心数据
  devices: Device[]
  devicesV2: DeviceV2[]  // v2 设备数据
  controllers: Controller[]
  deviceStatuses: Map<string, DeviceStatusInfo>
  deviceStatusesV2: Map<string, DeviceStatusV2>  // v2 设备状态
  
  // 油品数据
  fuelGrades: FuelGrade[]
  fuelSyncStatus: FuelSyncStatusInfo | null
  fuelSyncHistory: FuelSyncHistory[]
  externalOilProducts: ExternalOilProduct[]
  
  // UI状态
  loading: boolean
  error: string | null
  
  // 系统状态
  notifications: SimpleNotification[]
  lastUpdate: Date | null
  
  // 轮询状态
  pollingEnabled: boolean
  pollingInterval: number
}

// 扩展简化的Action类型，添加 v2 支持
export type SimpleAction =
  | { type: 'SET_DEVICES'; payload: Device[] }
  | { type: 'SET_DEVICES_V2'; payload: DeviceV2[] }  // v2 设备
  | { type: 'SET_CONTROLLERS'; payload: Controller[] }
  | { type: 'UPDATE_DEVICE_STATUSES'; payload: DeviceStatusInfo[] }
  | { type: 'UPDATE_DEVICE_STATUSES_V2'; payload: DeviceStatusV2[] }  // v2 设备状态
  | { type: 'SET_FUEL_GRADES'; payload: FuelGrade[] }
  | { type: 'SET_FUEL_SYNC_STATUS'; payload: FuelSyncStatusInfo }
  | { type: 'SET_FUEL_SYNC_HISTORY'; payload: FuelSyncHistory[] }
  | { type: 'SET_EXTERNAL_OIL_PRODUCTS'; payload: ExternalOilProduct[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_NOTIFICATION'; payload: SimpleNotification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_NOTIFICATIONS' }
  | { type: 'SET_POLLING_ENABLED'; payload: boolean }
  | { type: 'SET_POLLING_INTERVAL'; payload: number }
  | { type: 'UPDATE_TIMESTAMP' }

// 初始状态
const initialState: SimpleState = {
  devices: [],
  devicesV2: [],  // v2 设备
  controllers: [],
  deviceStatuses: new Map(),
  deviceStatusesV2: new Map(),  // v2 设备状态
  fuelGrades: [],
  fuelSyncStatus: null,
  fuelSyncHistory: [],
  externalOilProducts: [],
  loading: false,
  error: null,
  notifications: [],
  lastUpdate: null,
  pollingEnabled: true,
  pollingInterval: 10000 // 10秒，减少轮询频率避免闪烁
}

// 简化的Reducer
function simpleReducer(state: SimpleState, action: SimpleAction): SimpleState {
  switch (action.type) {
    case 'SET_DEVICES':
      return {
        ...state,
        devices: action.payload,
        lastUpdate: new Date()
      }

    case 'SET_DEVICES_V2':
      console.log('[Reducer] SET_DEVICES_V2 payload:', action.payload)
      console.log('[Reducer] SET_DEVICES_V2 payload length:', action.payload?.length || 0)
      return {
        ...state,
        devicesV2: action.payload,
        lastUpdate: new Date()
      }

    case 'SET_CONTROLLERS':
      return {
        ...state,
        controllers: action.payload,
        lastUpdate: new Date()
      }

    case 'UPDATE_DEVICE_STATUSES':
      const newStatusMap = new Map(state.deviceStatuses)
      action.payload.forEach(status => {
        newStatusMap.set(status.device_id, status)
      })
      return {
        ...state,
        deviceStatuses: newStatusMap,
        lastUpdate: new Date()
      }

    case 'UPDATE_DEVICE_STATUSES_V2':
      const newStatusMapV2 = new Map(state.deviceStatusesV2)
      action.payload.forEach(status => {
        newStatusMapV2.set(status.device_id, status)
      })
      return {
        ...state,
        deviceStatusesV2: newStatusMapV2,
        lastUpdate: new Date()
      }

    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      }

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      }

    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications.slice(0, 9)] // 最多保留10个通知
      }

    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload)
      }

    case 'CLEAR_NOTIFICATIONS':
      return {
        ...state,
        notifications: []
      }

    case 'SET_POLLING_ENABLED':
      return {
        ...state,
        pollingEnabled: action.payload
      }

    case 'SET_POLLING_INTERVAL':
      return {
        ...state,
        pollingInterval: action.payload
      }

    case 'SET_FUEL_GRADES':
      return {
        ...state,
        fuelGrades: action.payload,
        lastUpdate: new Date()
      }

    case 'SET_FUEL_SYNC_STATUS':
      return {
        ...state,
        fuelSyncStatus: action.payload,
        lastUpdate: new Date()
      }

    case 'SET_FUEL_SYNC_HISTORY':
      return {
        ...state,
        fuelSyncHistory: action.payload,
        lastUpdate: new Date()
      }

    case 'SET_EXTERNAL_OIL_PRODUCTS':
      return {
        ...state,
        externalOilProducts: action.payload,
        lastUpdate: new Date()
      }

    case 'UPDATE_TIMESTAMP':
      return {
        ...state,
        lastUpdate: new Date()
      }

    default:
      return state
  }
}

// Context
const SimpleContext = createContext<{
  state: SimpleState
  dispatch: React.Dispatch<SimpleAction>
  refreshData: () => Promise<void>
} | null>(null)

// Provider组件
interface SimpleProviderProps {
  children: ReactNode
}

export function SimpleProvider({ children }: SimpleProviderProps) {
  const [state, dispatch] = useReducer(simpleReducer, initialState)

  // 刷新数据的函数
  const refreshData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      // 并行获取控制器和设备数据
      const [controllersResult, devicesResult] = await Promise.allSettled([
        api.controllers.list(),
        api.devices.list()
      ])

      if (controllersResult.status === 'fulfilled') {
        dispatch({ type: 'SET_CONTROLLERS', payload: controllersResult.value.controllers || [] })
      }

      if (devicesResult.status === 'fulfilled') {
        dispatch({ type: 'SET_DEVICES', payload: devicesResult.value || [] })
      }

      dispatch({ type: 'UPDATE_TIMESTAMP' })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      
      // 添加错误通知
      const notification: SimpleNotification = {
        id: `error_${Date.now()}`,
        type: 'error',
        message: `Failed to refresh data: ${errorMessage}`,
        timestamp: new Date()
      }
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  // V2 数据刷新函数 (在Provider中定义用于轮询)
  const refreshDataV2InProvider = async () => {
    console.log('[SimpleProvider] Starting V2 data refresh for polling...')
    try {
      // 获取 v2 设备数据
      const devicesResult = await apiV2.devices.list()
      console.log('[SimpleProvider] V2 devices polling result:', devicesResult?.length || 0)
      
      dispatch({ type: 'SET_DEVICES_V2', payload: devicesResult || [] })

      // 获取每个设备的状态
      const deviceStatuses: DeviceStatusV2[] = []
      for (const device of devicesResult || []) {
        try {
          const status = await apiV2.devices.getStatus(device.id)
          deviceStatuses.push(status)
        } catch (error) {
          console.warn(`[SimpleProvider] Failed to get status for device ${device.id}:`, error)
        }
      }
      dispatch({ type: 'UPDATE_DEVICE_STATUSES_V2', payload: deviceStatuses })

      // 不在Provider中轮询nozzle数据，避免导致UI闪烁
      // nozzle数据由各个组件按需获取和缓存

      dispatch({ type: 'UPDATE_TIMESTAMP' })

    } catch (error) {
      console.warn('[SimpleProvider] V2 polling error:', error)
    }
  }

  // 轮询回调
  const pollingCallbacks: PollingCallbacks = {
    onDevicesUpdate: (devices) => {
      dispatch({ type: 'SET_DEVICES', payload: devices })
    },
    onDeviceStatusUpdate: (statuses) => {
      dispatch({ type: 'UPDATE_DEVICE_STATUSES', payload: statuses })
    },
    onError: (error) => {
      console.warn('[SimpleProvider] Polling error:', error)
      // 不为轮询错误添加通知，避免过多打扰
    },
    onRetry: (attempt) => {
      console.log(`[SimpleProvider] Polling retry attempt: ${attempt}`)
    }
  }

  // 轮询配置
  const pollingConfig = {
    interval: state.pollingInterval,
    enabled: state.pollingEnabled,
    maxRetries: 3
  }

  // 使用轮询
  const polling = usePolling(pollingCallbacks, pollingConfig)

  // 初始化数据加载
  useEffect(() => {
    refreshData()
    // 同时加载 V2 数据
    refreshDataV2InProvider()
  }, [])

  // 启动轮询
  useEffect(() => {
    if (state.pollingEnabled) {
      polling.start()
    } else {
      polling.stop()
    }
  }, [state.pollingEnabled, polling])

  // V2 数据轮询
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (state.pollingEnabled) {
      console.log(`[SimpleProvider] Starting V2 data polling every ${state.pollingInterval}ms`)
      interval = setInterval(() => {
        refreshDataV2InProvider()
      }, state.pollingInterval)
    }

    return () => {
      if (interval) {
        console.log('[SimpleProvider] Stopping V2 data polling')
        clearInterval(interval)
      }
    }
  }, [state.pollingEnabled, state.pollingInterval])

  return (
    <SimpleContext.Provider value={{ state, dispatch, refreshData }}>
      {children}
    </SimpleContext.Provider>
  )
}

// Hook使用Context
export function useSimpleState() {
  const context = useContext(SimpleContext)
  if (!context) {
    throw new Error('useSimpleState must be used within a SimpleProvider')
  }
  return context
}

// 便捷的Hooks
export function useDevices() {
  const { state, dispatch, refreshData } = useSimpleState()
  
  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  return {
    devices: state.devices,
    deviceStatuses: state.deviceStatuses,
    loading: state.loading,
    error: state.error,
    lastUpdate: state.lastUpdate,
    
    // 方法
    refresh: refreshData,
    getDeviceStatus: (deviceId: string) => state.deviceStatuses.get(deviceId),
    
    // 设备操作
    createDevice: async (deviceData: Partial<Device>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        const newDevice = await api.devices.create(deviceData)
        addNotification('success', `Device ${newDevice.name} created successfully`)
        await refreshData()
        return newDevice
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create device'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    updateDevice: async (deviceId: string, deviceData: Partial<Device>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        const updatedDevice = await api.devices.update(deviceId, deviceData)
        addNotification('success', `Device ${updatedDevice.name} updated successfully`)
        await refreshData()
        return updatedDevice
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update device'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    deleteDevice: async (deviceId: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        await api.devices.delete(deviceId)
        addNotification('success', 'Device deleted successfully')
        await refreshData()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete device'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    }
  }
}

export function useControllers() {
  const { state, dispatch, refreshData } = useSimpleState()

  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  return {
    controllers: state.controllers,
    loading: state.loading,
    error: state.error,
    lastUpdate: state.lastUpdate,
    refresh: refreshData,
    
    // 控制器操作
    createController: async (controllerData: Partial<Controller>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        const newController = await api.controllers.create(controllerData)
        addNotification('success', `控制器 ${newController.name} 创建成功`)
        await refreshData()
        return newController
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建控制器失败'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    updateController: async (controllerId: string, controllerData: Partial<Controller>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        const updatedController = await api.controllers.update(controllerId, controllerData)
        addNotification('success', `控制器 ${updatedController.name} 更新成功`)
        await refreshData()
        return updatedController
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '更新控制器失败'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    deleteController: async (controllerId: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        await api.controllers.delete(controllerId)
        addNotification('success', '控制器删除成功')
        await refreshData()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '删除控制器失败'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    discoverDevices: async (controllerId: string) => {
      try {
        const result = await api.controllers.discoverDevices(controllerId)
        addNotification('success', `设备发现成功！发现 ${result.total} 个设备`)
        await refreshData() // 刷新数据以获取新发现的设备
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '设备发现失败'
        addNotification('error', errorMessage)
        throw error
      }
    }
  }
}

export function useCommands() {
  const { dispatch } = useSimpleState()

  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  return {
    executeCommand: async (deviceId: string, commandTypeOrCommand: string | {
      command_type: string
      parameters?: Record<string, any>
      priority?: string
      timeout?: number
    }, parameters?: Record<string, any>) => {
      try {
        // 支持两种调用方式：
        // 1. executeCommand(deviceId, commandType, parameters)
        // 2. executeCommand(deviceId, { command_type, parameters, ... })
        let command: any
        
        if (typeof commandTypeOrCommand === 'string') {
          // 第一种方式：简单的命令类型和参数
          command = {
            command_type: commandTypeOrCommand,
            parameters: parameters || {},
            priority: 'normal',
            timeout: 30
          }
        } else {
          // 第二种方式：完整的命令对象
          command = commandTypeOrCommand
        }
        
        const result = await api.commands.execute(deviceId, command)
        
        if (result.success) {
          addNotification('success', `Command '${command.command_type}' executed successfully`)
        } else {
          addNotification('warning', `Command '${command.command_type}' failed: ${result.error}`)
        }
        
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Command execution failed'
        addNotification('error', errorMessage)
        throw error
      }
    }
  }
}

export function useNotifications() {
  const { state, dispatch } = useSimpleState()

  return {
    notifications: state.notifications,
    
    addNotification: (type: SimpleNotification['type'], message: string) => {
      const notification: SimpleNotification = {
        id: `${type}_${Date.now()}`,
        type,
        message,
        timestamp: new Date()
      }
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
    },
    
    removeNotification: (id: string) => {
      dispatch({ type: 'REMOVE_NOTIFICATION', payload: id })
    },
    
    clearAll: () => {
      dispatch({ type: 'CLEAR_NOTIFICATIONS' })
    }
  }
}

// v2版本的设备管理Hook
export function useDevicesV2() {
  const { state, dispatch, refreshData } = useSimpleState()
  
  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  // v2专用的数据刷新
  const refreshDataV2 = async () => {
    console.log('[RefreshDataV2] Starting data refresh...')
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      // 获取 v2 设备数据
      console.log('[RefreshDataV2] Calling apiV2.devices.list()...')
      const devicesResult = await apiV2.devices.list()
      console.log('[RefreshDataV2] Received devices:', devicesResult)
      console.log('[RefreshDataV2] Devices array length:', devicesResult?.length || 0)
      
      dispatch({ type: 'SET_DEVICES_V2', payload: devicesResult || [] })
      console.log('[RefreshDataV2] Dispatched SET_DEVICES_V2 with payload:', devicesResult || [])

      // 获取每个设备的状态
      const deviceStatuses: DeviceStatusV2[] = []
      for (const device of devicesResult || []) {
        try {
          console.log(`[RefreshDataV2] Getting status for device ${device.id}...`)
          const status = await apiV2.devices.getStatus(device.id)
          deviceStatuses.push(status)
          console.log(`[RefreshDataV2] Status for ${device.id}:`, status)
        } catch (error) {
          console.warn(`Failed to get status for device ${device.id}:`, error)
        }
      }
      dispatch({ type: 'UPDATE_DEVICE_STATUSES_V2', payload: deviceStatuses })
      console.log('[RefreshDataV2] Dispatched UPDATE_DEVICE_STATUSES_V2 with payload:', deviceStatuses)

      // 不在这里轮询nozzle数据，避免导致UI闪烁
      // nozzle数据由各个组件按需获取

      dispatch({ type: 'UPDATE_TIMESTAMP' })
      console.log('[RefreshDataV2] Data refresh completed successfully')

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      
      // 添加错误通知
      const notification: SimpleNotification = {
        id: `error_${Date.now()}`,
        type: 'error',
        message: `Failed to refresh v2 data: ${errorMessage}`,
        timestamp: new Date()
      }
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  console.log('[useDevicesV2] Current state.devicesV2:', state.devicesV2)
  console.log('[useDevicesV2] devicesV2 length:', state.devicesV2?.length || 0)
  console.log('[useDevicesV2] loading:', state.loading)
  console.log('[useDevicesV2] error:', state.error)

  return {
    devices: state.devicesV2,
    deviceStatuses: state.deviceStatusesV2,
    loading: state.loading,
    error: state.error,
    lastUpdate: state.lastUpdate,
    
    // 方法
    refresh: refreshDataV2,
    getDeviceStatus: (deviceId: string) => state.deviceStatusesV2.get(deviceId),
    
    // v2设备操作
    createDevice: async (deviceData: DeviceCreateRequestV2) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        
        // 验证地址范围
        if (deviceData.device_address < 0x50 || deviceData.device_address > 0x6F) {
          throw new Error('Device address must be in range 0x50-0x6F (80-111) for DART protocol')
        }
        
        const newDevice = await apiV2.devices.create(deviceData)
        addNotification('success', `Device ${newDevice.name} created successfully`)
        await refreshDataV2()
        return newDevice
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create device'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    updateDevice: async (deviceId: string, deviceData: Partial<DeviceCreateRequestV2>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        
        // 验证地址范围（如果提供了地址）
        if (deviceData.device_address !== undefined && (deviceData.device_address < 0x50 || deviceData.device_address > 0x6F)) {
          throw new Error('Device address must be in range 0x50-0x6F (80-111) for DART protocol')
        }
        
        const updatedDevice = await apiV2.devices.update(deviceId, deviceData)
        addNotification('success', `Device ${updatedDevice.name} updated successfully`)
        await refreshDataV2()
        return updatedDevice
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update device'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    deleteDevice: async (deviceId: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        await apiV2.devices.delete(deviceId)
        addNotification('success', 'Device deleted successfully')
        await refreshDataV2()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete device'
        addNotification('error', errorMessage)
        throw error
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    
    // v2特有功能
    getPumpStatus: async (deviceId: string) => {
      try {
        return await apiV2.devices.getPumpStatus(deviceId)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get pump status'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    getPumpTotals: async (deviceId: string) => {
      try {
        return await apiV2.devices.getPumpTotals(deviceId)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get pump totals'
        addNotification('error', errorMessage)
        throw error
      }
    }
  }
}

// v2版本的Wayne协议命令Hook
export function useWayneCommands() {
  const { dispatch } = useSimpleState()

  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  return {
    // 基础控制命令
    authorize: async (deviceId: string, nozzleIdOrMask: number | string, employeeId = '001') => {
      try {
        console.log(`[WayneCommands] authorize called with deviceId: ${deviceId}, nozzleIdOrMask: ${nozzleIdOrMask}, employeeId: ${employeeId}`)
        console.log(`[WayneCommands] nozzleIdOrMask type: ${typeof nozzleIdOrMask}`)
        
        let result: any
        
        // 如果传入的是nozzle ID（字符串格式），直接使用新的API参数
        if (typeof nozzleIdOrMask === 'string') {
          console.log(`[WayneCommands] Using nozzle ID directly: ${nozzleIdOrMask}`)
          const requestPayload = { 
            device_id: deviceId, 
            employee_id: employeeId,
            nozzle_id: nozzleIdOrMask 
          }
          console.log(`[WayneCommands] Calling apiV2.wayne.authorize with payload:`, requestPayload)
          
          result = await apiV2.wayne.authorize(requestPayload)
          console.log(`[WayneCommands] API response for nozzle ID:`, result)
        } else {
          // 如果传入的是数字，作为mask使用（向后兼容）
          const nozzleMask = nozzleIdOrMask
          console.log(`[WayneCommands] Using nozzle mask: 0x${nozzleMask.toString(16)}`)
          const requestPayload = { 
            device_id: deviceId, 
            nozzle_mask: nozzleMask, 
            employee_id: employeeId 
          }
          console.log(`[WayneCommands] Calling apiV2.wayne.authorize with payload:`, requestPayload)
          
          result = await apiV2.wayne.authorize(requestPayload)
          console.log(`[WayneCommands] API response for nozzle mask:`, result)
        }
        
        if (result.success) {
          const nozzleInfo = typeof nozzleIdOrMask === 'string' ? `Nozzle ID: ${nozzleIdOrMask}` : `Nozzle Mask: 0x${nozzleIdOrMask.toString(16)}`
          console.log(`[WayneCommands] Authorization successful: ${nozzleInfo}`)
          addNotification('success', `Device ${deviceId} authorized successfully (${nozzleInfo}, Employee: ${employeeId})`)
        } else {
          console.log(`[WayneCommands] Authorization failed:`, result.error)
          addNotification('warning', `Authorization failed: ${result.error}`)
        }
        return result
      } catch (error) {
        console.error(`[WayneCommands] Authorization error:`, error)
        const errorMessage = error instanceof Error ? error.message : 'Authorization failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    reset: async (deviceId: string) => {
      try {
        const result = await apiV2.wayne.reset(deviceId)
        if (result.success) {
          addNotification('success', `Device ${deviceId} reset successfully`)
        } else {
          addNotification('warning', `Reset failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Reset failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    stop: async (deviceId: string) => {
      try {
        const result = await apiV2.wayne.stop(deviceId)
        if (result.success) {
          addNotification('success', `Device ${deviceId} stopped successfully`)
        } else {
          addNotification('warning', `Stop failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Stop failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    // 状态查询命令
    getStatus: async (deviceId: string) => {
      try {
        return await apiV2.wayne.getStatus(deviceId)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get status'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    getFillingInfo: async (deviceId: string) => {
      try {
        return await apiV2.wayne.getFillingInfo(deviceId)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get filling info'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    // 预设命令
    presetVolume: async (deviceId: string, nozzleId: number, volumeLiters: number, employeeId = '001') => {
      try {
        console.log(`[WayneCommands] Preset volume: ${volumeLiters}L for device ${deviceId}, nozzle ${nozzleId}, employee ${employeeId}`)
        const result = await apiV2.wayne.presetVolume({ 
          device_id: deviceId, 
          volume: volumeLiters.toString(),
          decimals: 3,
          employee_id: employeeId
        })
        if (result.success) {
          addNotification('success', `Volume preset set: ${volumeLiters}L for nozzle ${nozzleId} (Employee: ${employeeId})`)
        } else {
          addNotification('warning', `Volume preset failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Volume preset failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    presetAmount: async (deviceId: string, nozzleId: number, amountCents: number, employeeId = '001') => {
      try {
        const result = await apiV2.wayne.presetAmount({ 
          device_id: deviceId, 
          amount: amountCents.toString(), 
          decimals: 2,
          employee_id: employeeId
        })
        if (result.success) {
          addNotification('success', `Amount preset set: ${amountCents}Rp for nozzle ${nozzleId} (Employee: ${employeeId})`)
        } else {
          addNotification('warning', `Amount preset failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Amount preset failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    // 喷嘴控制
    suspendNozzle: async (deviceId: string, nozzleId: number) => {
      try {
        const result = await apiV2.wayne.suspendNozzle({ device_id: deviceId, nozzle_id: nozzleId })
        if (result.success) {
          addNotification('success', `Nozzle ${nozzleId} suspended`)
        } else {
          addNotification('warning', `Nozzle suspend failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Nozzle suspend failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    resumeNozzle: async (deviceId: string, nozzleId: number) => {
      try {
        const result = await apiV2.wayne.resumeNozzle({ device_id: deviceId, nozzle_id: nozzleId })
        if (result.success) {
          addNotification('success', `Nozzle ${nozzleId} resumed`)
        } else {
          addNotification('warning', `Nozzle resume failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Nozzle resume failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    // CD1(0x0E) 恢复加油点命令
    resumeFuellingPoint: async (deviceId: string, async = false, employeeId = '001') => {
      try {
        console.log(`[WayneCommands] Resume fuelling point for device ${deviceId}, employee ${employeeId}`)
        const result = await apiV2.wayne.resumeFuellingPoint(deviceId, async, employeeId)
        if (result.success) {
          addNotification('success', `Fuelling point resumed for device ${deviceId} (Employee: ${employeeId})`)
        } else {
          addNotification('warning', `Resume fuelling point failed: ${result.error}`)
        }
        return result
      } catch (error) {
        console.error(`[WayneCommands] Resume fuelling point error:`, error)
        const errorMessage = error instanceof Error ? error.message : 'Resume fuelling point failed'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    // 价格设置
    updatePrices: async (deviceId: string, prices: Array<{ nozzle_number: number; price: string; decimals?: number }>) => {
      try {
        const result = await apiV2.updatePrices(deviceId, prices)
        if (result.success) {
          const priceList = prices.map(p => `Nozzle ${p.nozzle_number}: Rp${p.price}/L`).join(', ')
          addNotification('success', `Prices updated successfully - ${priceList}`)
        } else {
          addNotification('warning', `Price update failed: ${result.error}`)
        }
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Price update failed'
        addNotification('error', errorMessage)
        throw error
      }
    }
  }
}

// v2版本的系统管理Hook
export function useSystemV2() {
  const { dispatch } = useSimpleState()

  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  return {
    getHealth: async () => {
      try {
        return await apiV2.system.health()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get system health'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    getStatus: async () => {
      try {
        return await apiV2.system.status()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get system status'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    getMetrics: async () => {
      try {
        return await apiV2.system.metrics()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get system metrics'
        addNotification('error', errorMessage)
        throw error
      }
    },
    
    // 调度任务管理
    dispatch: {
      getStatus: async () => {
        try {
          return await apiV2.dispatch.getStatus()
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to get dispatch status'
          addNotification('error', errorMessage)
          throw error
        }
      },
      
      start: async () => {
        try {
          const result = await apiV2.dispatch.start()
          addNotification('success', 'Dispatch started successfully')
          return result
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to start dispatch'
          addNotification('error', errorMessage)
          throw error
        }
      },
      
      stop: async () => {
        try {
          const result = await apiV2.dispatch.stop()
          addNotification('success', 'Dispatch stopped successfully')
          return result
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to stop dispatch'
          addNotification('error', errorMessage)
          throw error
        }
      },
      
      getDevices: async () => {
        try {
          return await apiV2.dispatch.getDevices()
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to get dispatch devices'
          addNotification('error', errorMessage)
          throw error
        }
      }
    }
  }
}

export function usePollingSettings() {
  const { state, dispatch } = useSimpleState()

  return {
    enabled: state.pollingEnabled,
    interval: state.pollingInterval,
    lastUpdate: state.lastUpdate,
    
    setEnabled: (enabled: boolean) => {
      dispatch({ type: 'SET_POLLING_ENABLED', payload: enabled })
    },
    
    setInterval: (interval: number) => {
      dispatch({ type: 'SET_POLLING_INTERVAL', payload: interval })
    }
  }
}

// v2版本的喷嘴管理Hook
export function useNozzles() {
  const { dispatch } = useSimpleState()

  const addNotification = (type: SimpleNotification['type'], message: string) => {
    const notification: SimpleNotification = {
      id: `${type}_${Date.now()}`,
      type,
      message,
      timestamp: new Date()
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }

  return {
    // 获取设备的所有喷嘴 (主要方法)
    getNozzles: async (deviceId: string): Promise<NozzleV2[]> => {
      try {
        console.log(`[useNozzles] Fetching nozzles for device: ${deviceId}`)
        const nozzles = await apiV2.nozzles.getDeviceNozzles(deviceId)
        console.log(`[useNozzles] Retrieved ${nozzles.length} nozzles for device: ${deviceId}`)
        return nozzles
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get device nozzles'
        console.warn(`[useNozzles] Error fetching nozzles for device ${deviceId}:`, error)
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 获取设备的所有喷嘴 (别名，保持兼容性)
    getDeviceNozzles: async (deviceId: string): Promise<NozzleV2[]> => {
      try {
        return await apiV2.nozzles.getDeviceNozzles(deviceId)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get device nozzles'
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 刷新设备喷嘴数据
    refreshNozzles: async (deviceId: string): Promise<NozzleV2[]> => {
      try {
        console.log(`[useNozzles] Refreshing nozzles for device: ${deviceId}`)
        const nozzles = await apiV2.nozzles.getDeviceNozzles(deviceId)
        console.log(`[useNozzles] Refreshed ${nozzles.length} nozzles for device: ${deviceId}`)
        return nozzles
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to refresh device nozzles'
        console.warn(`[useNozzles] Error refreshing nozzles for device ${deviceId}:`, error)
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 获取设备的单个喷嘴（通过喷嘴号）
    getDeviceNozzle: async (deviceId: string, nozzleNumber: number): Promise<NozzleV2> => {
      try {
        return await apiV2.nozzles.getDeviceNozzle(deviceId, nozzleNumber)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : `Failed to get nozzle ${nozzleNumber} for device ${deviceId}`
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 获取喷嘴交易信息
    getNozzleTransaction: async (deviceId: string, nozzleNumber: number): Promise<NozzleTransactionV2 | null> => {
      try {
        return await apiV2.nozzles.getNozzleTransaction(deviceId, nozzleNumber)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : `Failed to get transaction for nozzle ${nozzleNumber}`
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 获取喷嘴统计信息
    getNozzleStats: async (deviceId: string, nozzleNumber: number): Promise<NozzleStatsV2 | null> => {
      try {
        return await apiV2.nozzles.getNozzleStats(deviceId, nozzleNumber)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : `Failed to get stats for nozzle ${nozzleNumber}`
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 兼容性方法（已废弃）
    getNozzle: async (nozzleId: string): Promise<NozzleV2> => {
      try {
        return await apiV2.nozzles.getNozzle(nozzleId)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to get nozzle'
        addNotification('error', errorMessage)
        throw error
      }
    },

    // 更新喷嘴（暂未实现）
    updateNozzle: async (nozzleId: string, nozzle: Partial<NozzleUpdateRequestV2>): Promise<NozzleV2> => {
      try {
        const result = await apiV2.nozzles.updateNozzle(nozzleId, nozzle)
        addNotification('success', `Nozzle ${nozzleId} updated successfully`)
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update nozzle'
        addNotification('error', errorMessage)
        throw error
      }
    }
  }
}

// ===== 油品管理 Hook =====
export function useFuelGrades() {
  const context = useContext(SimpleContext)
  if (!context) {
    throw new Error('useFuelGrades must be used within a SimpleProvider')
  }

  const { state, dispatch } = context
  
  const addNotification = (type: SimpleNotification['type'], message: string) => {
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now().toString(),
        type,
        message,
        timestamp: new Date()
      }
    })
  }

  const refresh = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })
      
      const fuelGrades = await fccV2API.getFuelGrades()
      dispatch({ type: 'SET_FUEL_GRADES', payload: fuelGrades })
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch fuel grades'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      addNotification('error', errorMessage)
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const createFuelGrade = async (grade: Omit<FuelGrade, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newGrade = await fccV2API.createFuelGrade(grade)
      await refresh() // 刷新列表
      addNotification('success', `油品 "${newGrade.name}" 创建成功`)
      return newGrade
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create fuel grade'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const updateFuelGrade = async (gradeId: string, grade: Partial<Omit<FuelGrade, 'id' | 'created_at' | 'updated_at'>>) => {
    try {
      const updatedGrade = await fccV2API.updateFuelGrade(gradeId, grade)
      await refresh() // 刷新列表
      addNotification('success', `油品 "${updatedGrade.name}" 更新成功`)
      return updatedGrade
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update fuel grade'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const deleteFuelGrade = async (gradeId: string) => {
    try {
      await fccV2API.deleteFuelGrade(gradeId)
      await refresh() // 刷新列表
      addNotification('success', '油品删除成功')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete fuel grade'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const restoreFuelGrade = async (gradeId: string) => {
    try {
      const restoredGrade = await fccV2API.restoreFuelGrade(gradeId)
      await refresh() // 刷新列表
      addNotification('success', `油品 "${restoredGrade.name}" 恢复成功`)
      return restoredGrade
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to restore fuel grade'
      addNotification('error', errorMessage)
      throw error
    }
  }

  return {
    fuelGrades: state.fuelGrades,
    loading: state.loading,
    error: state.error,
    refresh,
    createFuelGrade,
    updateFuelGrade,
    deleteFuelGrade,
    restoreFuelGrade
  }
}

// ===== 油品同步 Hook =====
export function useFuelSync() {
  const context = useContext(SimpleContext)
  if (!context) {
    throw new Error('useFuelSync must be used within a SimpleProvider')
  }

  const { state, dispatch } = context
  
  const addNotification = (type: SimpleNotification['type'], message: string) => {
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now().toString(),
        type,
        message,
        timestamp: new Date()
      }
    })
  }

  const getSyncStatus = async () => {
    try {
      const status = await fccV2API.getFuelSyncStatus()
      dispatch({ type: 'SET_FUEL_SYNC_STATUS', payload: status })
      return status
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get sync status'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const triggerSync = async () => {
    try {
      const result = await fccV2API.triggerFuelSync()
      await getSyncStatus() // 刷新状态
      addNotification('success', result.message || '同步已触发')
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to trigger sync'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const getSyncHistory = async (limit = 10) => {
    try {
      const history = await fccV2API.getFuelSyncHistory(limit)
      dispatch({ type: 'SET_FUEL_SYNC_HISTORY', payload: history })
      return history
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get sync history'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const getExternalProducts = async () => {
    try {
      const products = await fccV2API.getExternalOilProducts()
      dispatch({ type: 'SET_EXTERNAL_OIL_PRODUCTS', payload: products })
      return products
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get external products'
      addNotification('error', errorMessage)
      throw error
    }
  }

  const testConnection = async () => {
    try {
      const result = await fccV2API.testExternalFuelAPI()
      addNotification(result.success ? 'success' : 'error', result.message)
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to test connection'
      addNotification('error', errorMessage)
      throw error
    }
  }

  return {
    syncStatus: state.fuelSyncStatus,
    syncHistory: state.fuelSyncHistory,
    externalProducts: state.externalOilProducts,
    loading: state.loading,
    error: state.error,
    getSyncStatus,
    triggerSync,
    getSyncHistory,
    getExternalProducts,
    testConnection
  }
} 