/**
 * FCC API 配置管理
 * 支持动态设置API URL，本地存储配置
 */

// 默认API配置
const DEFAULT_API_CONFIG = {
  baseUrl: 'http://192.168.8.114:8081',
  apiVersion: 'v2',
  timeout: 10000,
} as const

// API配置接口
export interface APIConfig {
  baseUrl: string
  apiVersion: string
  timeout: number
}

// API配置管理类
class APIConfigManager {
  private static instance: APIConfigManager
  private config: APIConfig
  private listeners: Set<(config: APIConfig) => void> = new Set()

  private constructor() {
    this.config = this.loadConfig()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): APIConfigManager {
    if (!APIConfigManager.instance) {
      APIConfigManager.instance = new APIConfigManager()
    }
    return APIConfigManager.instance
  }

  /**
   * 从本地存储加载配置
   */
  private loadConfig(): APIConfig {
    if (typeof window === 'undefined') {
      return { ...DEFAULT_API_CONFIG }
    }

    try {
      const stored = localStorage.getItem('fcc-api-config')
      if (stored) {
        const parsed = JSON.parse(stored)
        return {
          baseUrl: parsed.baseUrl || DEFAULT_API_CONFIG.baseUrl,
          apiVersion: parsed.apiVersion || DEFAULT_API_CONFIG.apiVersion,
          timeout: parsed.timeout || DEFAULT_API_CONFIG.timeout,
        }
      }
    } catch (error) {
      console.warn('Failed to load API config from localStorage:', error)
    }

    return { ...DEFAULT_API_CONFIG }
  }

  /**
   * 保存配置到本地存储
   */
  private saveConfig(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem('fcc-api-config', JSON.stringify(this.config))
    } catch (error) {
      console.warn('Failed to save API config to localStorage:', error)
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): APIConfig {
    return { ...this.config }
  }

  /**
   * 获取完整的API基础URL
   */
  getApiBaseUrl(): string {
    const { baseUrl, apiVersion } = this.config
    return `${baseUrl}/api/${apiVersion}`
  }

  /**
   * 更新API配置
   */
  updateConfig(updates: Partial<APIConfig>): void {
    const oldConfig = { ...this.config }
    this.config = {
      ...this.config,
      ...updates,
    }

    // 确保baseUrl格式正确
    if (updates.baseUrl) {
      this.config.baseUrl = this.normalizeBaseUrl(updates.baseUrl)
    }

    this.saveConfig()
    
    // 通知监听器
    this.listeners.forEach(listener => {
      try {
        listener(this.getConfig())
      } catch (error) {
        console.warn('Error in API config listener:', error)
      }
    })
  }

  /**
   * 标准化基础URL格式
   */
  private normalizeBaseUrl(url: string): string {
    if (!url) return DEFAULT_API_CONFIG.baseUrl
    
    // 移除末尾的斜杠和api路径
    url = url.replace(/\/+$/, '')
    url = url.replace(/\/api\/.*$/, '')
    
    // 确保有协议
    if (!url.match(/^https?:\/\//)) {
      url = `http://${url}`
    }
    
    return url
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    this.updateConfig(DEFAULT_API_CONFIG)
  }

  /**
   * 添加配置变更监听器
   */
  addListener(listener: (config: APIConfig) => void): () => void {
    this.listeners.add(listener)
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * 验证API URL是否可访问
   */
  async validateApiUrl(baseUrl?: string): Promise<boolean> {
    const testUrl = baseUrl ? this.normalizeBaseUrl(baseUrl) : this.config.baseUrl
    const fullUrl = `${testUrl}/api/${this.config.apiVersion}/health`
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)
      
      const response = await fetch(fullUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        },
      })
      
      clearTimeout(timeoutId)
      return response.ok
    } catch (error) {
      console.warn('API validation failed:', error)
      return false
    }
  }
}

// 导出单例实例
export const apiConfigManager = APIConfigManager.getInstance()

// 便捷函数
export const getApiConfig = () => apiConfigManager.getConfig()
export const getApiBaseUrl = () => apiConfigManager.getApiBaseUrl()
export const updateApiConfig = (updates: Partial<APIConfig>) => apiConfigManager.updateConfig(updates)
export const resetApiConfig = () => apiConfigManager.resetToDefault()
export const validateApiUrl = (url?: string) => apiConfigManager.validateApiUrl(url)
export const addApiConfigListener = (listener: (config: APIConfig) => void) => 
  apiConfigManager.addListener(listener) 