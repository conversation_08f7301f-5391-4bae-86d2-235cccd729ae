/**
 * PreAuth 测试工具和辅助函数
 * 
 * 提供用于测试和使用 PreAuth 功能的辅助函数和集成测试
 */

import { fccV2API, apiV2, PreAuthRequest, PreAuthResponse, PreAuthType } from './api-client-v2'

// ===== 辅助函数 =====

/**
 * 创建体积预授权请求
 * @param deviceId 设备ID
 * @param volume 预设体积（升）
 * @param options 可选参数
 * @returns PreAuth 请求对象
 */
export function createVolumePreauth(
  deviceId: string, 
  volume: string, 
  options: {
    nozzleId?: string
    decimals?: number
    employeeId?: string
  } = {}
): PreAuthRequest {
  return {
    device_id: deviceId,
    nozzle_id: options.nozzleId,
    command: 'preauth',
    auth_type: 'preset_volume',
    volume: volume,
    decimals: options.decimals || 2,
    employee_id: options.employeeId || '001'
  }
}

/**
 * 创建金额预授权请求
 * @param deviceId 设备ID
 * @param amount 预设金额
 * @param options 可选参数
 * @returns PreAuth 请求对象
 */
export function createAmountPreauth(
  deviceId: string, 
  amount: string, 
  options: {
    nozzleId?: string
    decimals?: number
    employeeId?: string
  } = {}
): PreAuthRequest {
  return {
    device_id: deviceId,
    nozzle_id: options.nozzleId,
    command: 'preauth',
    auth_type: 'preset_amount',
    amount: amount,
    decimals: options.decimals || 2,
    employee_id: options.employeeId || '001'
  }
}

/**
 * 验证 PreAuth 请求参数
 * @param request PreAuth 请求对象
 * @returns 验证结果
 */
export function validatePreauthRequest(request: PreAuthRequest): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // 必需参数验证
  if (!request.device_id) {
    errors.push('device_id is required')
  }

  if (!request.auth_type) {
    errors.push('auth_type is required')
  }

  if (request.auth_type && !['preset_volume', 'preset_amount'].includes(request.auth_type)) {
    errors.push('auth_type must be "preset_volume" or "preset_amount"')
  }

  // 条件必需参数验证
  if (request.auth_type === 'preset_volume') {
    if (!request.volume) {
      errors.push('volume is required when auth_type is "preset_volume"')
    } else if (isNaN(Number(request.volume)) || Number(request.volume) <= 0) {
      errors.push('volume must be a positive number')
    }
  }

  if (request.auth_type === 'preset_amount') {
    if (!request.amount) {
      errors.push('amount is required when auth_type is "preset_amount"')
    } else if (isNaN(Number(request.amount)) || Number(request.amount) <= 0) {
      errors.push('amount must be a positive number')
    }
  }

  // 可选参数验证
  if (request.decimals !== undefined) {
    if (!Number.isInteger(request.decimals) || request.decimals < 0 || request.decimals > 4) {
      errors.push('decimals must be an integer between 0 and 4')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// ===== 测试函数 =====

/**
 * 测试所有 PreAuth API 调用方式
 * @param deviceId 测试设备ID
 * @param nozzleId 测试喷嘴ID（可选）
 */
export async function testPreauthIntegration(deviceId: string = 'test_device_001', nozzleId?: string): Promise<void> {
  console.log('🧪 开始 PreAuth 功能集成测试...')
  console.log(`📋 测试设备: ${deviceId}`)
  if (nozzleId) {
    console.log(`📋 测试喷嘴: ${nozzleId}`)
  }

  const testResults: { method: string; success: boolean; error?: string }[] = []

  // 测试数据
  const volumeRequest = createVolumePreauth(deviceId, '25.50', {
    nozzleId,
    employeeId: '001'
  })

  const amountRequest = createAmountPreauth(deviceId, '100.00', {
    nozzleId,
    employeeId: '002'
  })

  // 1. 测试直接 API 调用
  console.log('\n1️⃣ 测试直接 API 调用 (fccV2API.waynePreauth)')
  try {
    const response1 = await fccV2API.waynePreauth(volumeRequest)
    console.log('✅ waynePreauth 成功:', response1)
    testResults.push({ method: 'fccV2API.waynePreauth', success: true })
  } catch (error) {
    console.error('❌ waynePreauth 失败:', error)
    testResults.push({ 
      method: 'fccV2API.waynePreauth', 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    })
  }

  // 2. 测试便捷方法别名
  console.log('\n2️⃣ 测试便捷方法别名 (fccV2API.preauth)')
  try {
    const response2 = await fccV2API.preauth(amountRequest)
    console.log('✅ preauth 成功:', response2)
    testResults.push({ method: 'fccV2API.preauth', success: true })
  } catch (error) {
    console.error('❌ preauth 失败:', error)
    testResults.push({ 
      method: 'fccV2API.preauth', 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    })
  }

  // 3. 测试 Wayne 命令对象
  console.log('\n3️⃣ 测试 Wayne 命令对象 (apiV2.wayne.preauth)')
  try {
    const response3 = await apiV2.wayne.preauth(volumeRequest)
    console.log('✅ wayne.preauth 成功:', response3)
    testResults.push({ method: 'apiV2.wayne.preauth', success: true })
  } catch (error) {
    console.error('❌ wayne.preauth 失败:', error)
    testResults.push({ 
      method: 'apiV2.wayne.preauth', 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    })
  }

  // 4. 测试兼容性命令接口 - 体积预授权
  console.log('\n4️⃣ 测试兼容性命令接口 - 体积预授权 (apiV2.commands.execute)')
  try {
    const response4 = await apiV2.commands.execute(deviceId, {
      command_type: 'preauth',
      parameters: {
        nozzle_id: nozzleId,
        auth_type: 'preset_volume',
        volume: '30.00',
        decimals: 2,
        employee_id: '003'
      }
    })
    console.log('✅ commands.execute (volume) 成功:', response4)
    testResults.push({ method: 'apiV2.commands.execute (volume)', success: true })
  } catch (error) {
    console.error('❌ commands.execute (volume) 失败:', error)
    testResults.push({ 
      method: 'apiV2.commands.execute (volume)', 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    })
  }

  // 5. 测试兼容性命令接口 - 金额预授权
  console.log('\n5️⃣ 测试兼容性命令接口 - 金额预授权 (apiV2.commands.execute)')
  try {
    const response5 = await apiV2.commands.execute(deviceId, {
      command_type: 'preauth',
      parameters: {
        nozzle_id: nozzleId,
        auth_type: 'preset_amount',
        amount: '150.00',
        decimals: 2,
        employee_id: '004'
      }
    })
    console.log('✅ commands.execute (amount) 成功:', response5)
    testResults.push({ method: 'apiV2.commands.execute (amount)', success: true })
  } catch (error) {
    console.error('❌ commands.execute (amount) 失败:', error)
    testResults.push({ 
      method: 'apiV2.commands.execute (amount)', 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    })
  }

  // 输出测试结果摘要
  console.log('\n📊 测试结果摘要:')
  console.log('=' .repeat(60))
  
  const successCount = testResults.filter(r => r.success).length
  const totalCount = testResults.length
  
  testResults.forEach(result => {
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.method}`)
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`)
    }
  })
  
  console.log('=' .repeat(60))
  console.log(`🎯 成功率: ${successCount}/${totalCount} (${Math.round(successCount / totalCount * 100)}%)`)
  
  if (successCount === totalCount) {
    console.log('🎉 所有 PreAuth API 调用方式测试通过！')
  } else {
    console.log('⚠️  部分测试失败，请检查错误信息')
  }
}

/**
 * 快速测试 PreAuth 功能
 * @param deviceId 设备ID
 * @param nozzleId 喷嘴ID（可选）
 * @param authType 授权类型
 * @param value 预设值（体积或金额）
 */
export async function quickPreauthTest(
  deviceId: string, 
  nozzleId: string | undefined, 
  authType: PreAuthType, 
  value: string
): Promise<PreAuthResponse> {
  console.log(`🚀 快速 PreAuth 测试: ${authType} = ${value}`)
  
  const request: PreAuthRequest = {
    device_id: deviceId,
    nozzle_id: nozzleId,
    command: 'preauth',
    auth_type: authType,
    ...(authType === 'preset_volume' ? { volume: value } : { amount: value }),
    decimals: 2,
    employee_id: '001'
  }
  
  // 验证请求
  const validation = validatePreauthRequest(request)
  if (!validation.valid) {
    throw new Error(`请求验证失败: ${validation.errors.join(', ')}`)
  }
  
  console.log('📤 发送请求:', request)
  
  try {
    const response = await fccV2API.preauth(request)
    console.log('📥 响应:', response)
    return response
  } catch (error) {
    console.error('❌ 请求失败:', error)
    throw error
  }
}
