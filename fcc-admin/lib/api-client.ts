// FCC API 客户端 V2 版本
// 基于 FCC Service V2 架构和 Wayne DART 协议

const API_BASE = process.env.NEXT_PUBLIC_FCC_API || 'http://localhost:8080/api/v2'

// ===== V2 类型定义 =====

// 设备类型
export type DeviceType = 
  | 'controller' | 'fuel_controller' | 'payment_controller' | 'monitor_controller'
  | 'dart_line_controller' | 'tcp_device_controller' | 'serial_port_controller'
  | 'fuel_pump' | 'fuel_nozzle' | 'atg' | 'display' | 'pos' | 'edc' | 'pump'
  | 'dart_pump' | 'dart_tank' | 'dart_display'

export type DeviceStatus = 'online' | 'offline' | 'error' | 'maintenance' | 'unknown'
export type DeviceState = 'online' | 'offline' | 'initializing' | 'ready' | 'polling' | 'configuring' | 'error' | 'maintenance'
export type DeviceHealth = 'healthy' | 'good' | 'warning' | 'critical' | 'error' | 'unknown'
export type ProtocolType = 'tcp' | 'udp' | 'serial' | 'modbus' | 'dart' | 'wayne_dart'
export type NozzleState = 'idle' | 'selected' | 'authorized' | 'out' | 'filling' | 'completed' | 'suspended' | 'error' | 'maintenance'
export type CommandPriority = 'high' | 'normal' | 'low'

// Wayne 协议类型
export type WayneCommandType = 
  | 'authorize' | 'reset' | 'stop' | 'return_status' | 'return_filling_info'
  | 'configure_nozzles' | 'preset_volume' | 'preset_amount' | 'update_prices'
  | 'suspend_nozzle' | 'resume_nozzle' | 'request_counters'

export type CounterType = 
  | 'volume_nozzle_1' | 'volume_nozzle_2' | 'volume_nozzle_3' | 'volume_nozzle_4'
  | 'volume_nozzle_5' | 'volume_nozzle_6' | 'volume_nozzle_7' | 'volume_nozzle_8' | 'volume_total'
  | 'amount_nozzle_1' | 'amount_nozzle_2' | 'amount_nozzle_3' | 'amount_nozzle_4'
  | 'amount_nozzle_5' | 'amount_nozzle_6' | 'amount_nozzle_7' | 'amount_nozzle_8' | 'amount_total'

// ===== V2 接口定义 =====

// 设备配置
export interface DeviceConfigV2 {
  timeout: number
  retry_count: number
  monitor_interval: number
  protocol_config: Record<string, any>
  device_params: Record<string, any>
  extensions: Record<string, any>
}

// 设备能力
export interface DeviceCapabilitiesV2 {
  supported_commands: string[]
  supported_data_types: string[]
  protocol_version?: string
  firmware_version?: string
  hardware_version?: string
  features?: Record<string, boolean>
  performance?: Record<string, any>
}

// 设备对象 (V2版本)
export interface DeviceV2 {
  id: string
  name: string
  type: DeviceType
  controller_id: string
  device_address: number        // V2: 字段名为 device_address
  station_id: string
  island_id?: string
  position?: string
  config: DeviceConfigV2
  status: DeviceStatus
  health: DeviceHealth
  capabilities: DeviceCapabilitiesV2
  last_seen?: string
  created_at: string
  updated_at: string
}

// 设备创建请求
export interface DeviceCreateRequestV2 {
  id: string
  name: string
  type: DeviceType
  controller_id: string
  device_address: number        // DART 协议地址 (80-111, 0x50-0x6F)
  station_id: string
  island_id?: string
  position?: string
  config?: Partial<DeviceConfigV2>
}

// 设备状态 (V2版本)
export interface DeviceStatusV2 {
  device_id: string
  status: DeviceState
  last_seen?: string
}

// 设备状态详细信息 (V2版本)
export interface DeviceStateDetailV2 {
  device_id: string
  state: DeviceState
  is_online: boolean
  last_poll: string
  last_response: string
  pump_status: number
  tx_sequence: number
  poll_count: number
  error_count: number
  response_time: number        // 纳秒
  last_error?: string
  stats?: {
    total_polls: number
    successful_polls: number
    failed_polls: number
    average_response_time: number
  }
}

// 泵状态 (V2版本)
export interface PumpStatusV2 {
  device_id: string
  pump_status: number
  device_state: DeviceState
  is_online: boolean
  tx_sequence: number
  last_poll: string
  last_response: string
  poll_count: number
  error_count: number
  response_time: number
  nozzles: Record<string, {
    state: NozzleState
    is_out: boolean
    is_selected: boolean
    current_volume: number
    current_amount: number
    current_price: number
    transaction_id?: string
  }>
}

// 泵累计数据 (V2版本)
export interface PumpTotalsV2 {
  device_id: string
  totals: Record<string, {
    nozzle_id: string
    total_volume: number
    total_amount: number
    transaction_count: number
    last_transaction_time?: string
  }>
}

// 命令请求 (V2版本)
export interface CommandRequestV2 {
  command_id?: string
  device_id: string
  command_type: string
  parameters?: Record<string, any>
  priority?: CommandPriority
  timeout?: number
  async?: boolean
}

// 命令响应 (V2版本)
export interface CommandResponseV2 {
  command_id: string
  device_id: string
  command_type: string
  success: boolean
  data?: Record<string, any>
  error?: string
  execution_time_ms: number
  submitted_at: string
  completed_at?: string
}

// Wayne 协议命令基础请求
export interface WayneCommandRequestV2 {
  command_id?: string
  device_id: string
  command: WayneCommandType
  priority?: CommandPriority
  timeout?: number
  async?: boolean
}

// Wayne 协议命令响应
export interface WayneCommandResponseV2 {
  command_id: string
  device_id: string
  command: WayneCommandType
  success: boolean
  data?: Record<string, any>
  error?: string
  execution_time_ms: number
  submitted_at: string
  completed_at?: string
  protocol_info?: {
    protocol: string
    transaction_type: string
    transaction_code: string
    response_time_ms: number
  }
}

// 喷嘴对象 (V2版本)
export interface NozzleV2 {
  id: string
  number: number               // 1-8 (Wayne 协议限制)
  name: string
  device_id: string
  fuel_grade_id: string
  current_price: number
  position: string
  is_enabled: boolean
  created_at: string
  updated_at: string
}

// 列表响应
export interface DeviceListResponseV2 {
  devices: DeviceV2[]
  total: number
  page?: number
  page_size?: number
}

// 系统状态 (V2版本)
export interface SystemStatusV2 {
  is_running: boolean
  total_devices: number
  active_devices: number
  start_time: string
  uptime: number               // 纳秒
  device_statuses: Record<string, {
    device_id: string
    is_running: boolean
    last_activity: string
    state: DeviceState
  }>
}

// 系统指标 (V2版本)
export interface SystemMetricsV2 {
  total_devices: number
  active_devices: number
  total_polls: number
  successful_polls: number
  failed_polls: number
  average_latency: number      // 纳秒
  start_time: string
  last_update_time: string
}

// 调度状态 (V2版本)
export interface DispatchStatusV2 {
  is_running: boolean
  total_devices: number
  active_devices: number
  start_time: string
  uptime: number
}

// 调度设备信息 (V2版本)
export interface DispatchDeviceV2 {
  device_id: string
  is_running: boolean
  last_activity: string
  stats: {
    total_polls: number
    successful_polls: number
    failed_polls: number
    average_response_time: number
    last_error?: string
  }
}

// 设备过滤器
export interface DeviceFilterV2 {
  controller_id?: string
  station_id?: string
  status?: DeviceStatus
  type?: DeviceType
  page?: number
  page_size?: number
}

// 健康检查响应
export interface HealthResponseV2 {
  status: string
  version: string
  timestamp: string
  uptime: string
}

// ===== API 错误处理 =====
export class FCCV2APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message)
    this.name = 'FCCV2APIError'
  }
}

// ===== HTTP 客户端 =====
class HTTPClientV2 {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorText = await response.text()
        let errorMessage = `API request failed: ${response.status}`
        
        try {
          const errorData = JSON.parse(errorText)
          errorMessage = errorData.error || errorMessage
        } catch {
          errorMessage = errorText || errorMessage
        }
        
        throw new FCCV2APIError(errorMessage, response.status, errorText)
      }

      // 处理 204 No Content
      if (response.status === 204) {
        return null as any
      }

      const data = await response.json()
      return data
    } catch (error) {
      if (error instanceof FCCV2APIError) {
        throw error
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      throw new FCCV2APIError(`Network error: ${errorMessage}`, 0)
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// ===== FCC V2 API 客户端 =====
export class FCCV2APIClient {
  private http: HTTPClientV2

  constructor(baseURL = API_BASE) {
    this.http = new HTTPClientV2(baseURL)
  }

  // ===== 基础系统 API =====
  
  async getHealth(): Promise<HealthResponseV2> {
    return this.http.get<HealthResponseV2>('/health')
  }

  async getSystemStatus(): Promise<SystemStatusV2> {
    return this.http.get<SystemStatusV2>('/status')
  }

  async getSystemMetrics(): Promise<SystemMetricsV2> {
    return this.http.get<SystemMetricsV2>('/metrics')
  }

  // ===== 设备管理 API =====
  
  async getDevices(filters?: DeviceFilterV2): Promise<DeviceListResponseV2> {
    try {
      const queryParams = new URLSearchParams()
      if (filters?.controller_id) queryParams.set('controller_id', filters.controller_id)
      if (filters?.station_id) queryParams.set('station_id', filters.station_id)
      if (filters?.status) queryParams.set('status', filters.status)
      if (filters?.type) queryParams.set('type', filters.type)
      if (filters?.page) queryParams.set('page', filters.page.toString())
      if (filters?.page_size) queryParams.set('page_size', filters.page_size.toString())
      
      const endpoint = queryParams.toString() ? `/devices?${queryParams}` : '/devices'
      const response = await this.http.get<DeviceListResponseV2>(endpoint)
      
      return {
        devices: response?.devices || [],
        total: response?.total || 0,
        page: response?.page,
        page_size: response?.page_size
      }
    } catch (error) {
      console.warn('[FCCV2API] Failed to fetch devices:', error)
      return {
        devices: [],
        total: 0
      }
    }
  }

  async getDevice(deviceId: string): Promise<DeviceV2> {
    return this.http.get<DeviceV2>(`/devices/${deviceId}`)
  }

  async createDevice(device: DeviceCreateRequestV2): Promise<DeviceV2> {
    // 验证 DART 协议地址范围
    if (device.device_address < 80 || device.device_address > 111) {
      throw new FCCV2APIError('Device address must be in range 80-111 (0x50-0x6F) for DART protocol', 400)
    }
    
    return this.http.post<DeviceV2>('/devices', device)
  }

  async updateDevice(deviceId: string, device: Partial<DeviceCreateRequestV2>): Promise<DeviceV2> {
    // 验证地址范围（如果提供了地址）
    if (device.device_address !== undefined && (device.device_address < 80 || device.device_address > 111)) {
      throw new FCCV2APIError('Device address must be in range 80-111 (0x50-0x6F) for DART protocol', 400)
    }
    
    return this.http.put<DeviceV2>(`/devices/${deviceId}`, device)
  }

  async deleteDevice(deviceId: string): Promise<void> {
    return this.http.delete<void>(`/devices/${deviceId}`)
  }

  async getDeviceStatus(deviceId: string): Promise<DeviceStatusV2> {
    try {
      return await this.http.get<DeviceStatusV2>(`/devices/${deviceId}/status`)
    } catch (error) {
      console.warn(`[FCCV2API] Failed to fetch status for device ${deviceId}:`, error)
      // 返回默认状态
      return {
        device_id: deviceId,
        status: 'error' as DeviceState,
        last_seen: new Date().toISOString()
      }
    }
  }

  async sendCommand(deviceId: string, command: Omit<CommandRequestV2, 'device_id'>): Promise<CommandResponseV2> {
    const commandRequest: CommandRequestV2 = {
      ...command,
      device_id: deviceId
    }
    return this.http.post<CommandResponseV2>(`/devices/${deviceId}/commands`, commandRequest)
  }

  async getPumpStatus(deviceId: string): Promise<PumpStatusV2> {
    return this.http.get<PumpStatusV2>(`/devices/${deviceId}/pump/status`)
  }

  async getPumpTotals(deviceId: string): Promise<PumpTotalsV2> {
    return this.http.get<PumpTotalsV2>(`/devices/${deviceId}/pump/totals`)
  }

  // ===== 调度任务管理 API =====
  
  async getDispatchStatus(): Promise<DispatchStatusV2> {
    return this.http.get<DispatchStatusV2>('/dispatch/status')
  }

  async startDispatch(): Promise<{ status: string }> {
    return this.http.post<{ status: string }>('/dispatch/start')
  }

  async stopDispatch(): Promise<{ status: string }> {
    return this.http.post<{ status: string }>('/dispatch/stop')
  }

  async getDispatchDevices(): Promise<DispatchDeviceV2[]> {
    return this.http.get<DispatchDeviceV2[]>('/dispatch/devices')
  }

  // ===== 喷嘴管理 API =====
  
  async getDeviceNozzles(deviceId: string): Promise<NozzleV2[]> {
    return this.http.get<NozzleV2[]>(`/nozzles/device/${deviceId}`)
  }

  async getNozzle(nozzleId: string): Promise<NozzleV2> {
    return this.http.get<NozzleV2>(`/nozzles/${nozzleId}`)
  }

  async updateNozzle(nozzleId: string, nozzle: Partial<NozzleV2>): Promise<NozzleV2> {
    return this.http.put<NozzleV2>(`/nozzles/${nozzleId}`, nozzle)
  }

  // ===== Wayne DART 协议 API =====
  
  async wayneAuthorize(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/authorize', {
      device_id: deviceId,
      command: 'authorize',
      async
    })
  }

  async wayneReset(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/reset', {
      device_id: deviceId,
      command: 'reset',
      async
    })
  }

  async wayneStop(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/stop', {
      device_id: deviceId,
      command: 'stop',
      async
    })
  }

  async wayneGetStatus(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/status', {
      device_id: deviceId,
      command: 'return_status'
    })
  }

  async wayneGetFillingInfo(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/filling-info', {
      device_id: deviceId,
      command: 'return_filling_info'
    })
  }

  async wayneConfigureNozzles(deviceId: string, nozzleNumbers: number[]): Promise<WayneCommandResponseV2> {
    // 验证喷嘴编号
    if (!nozzleNumbers || nozzleNumbers.length === 0) {
      throw new FCCV2APIError('nozzle_numbers is required and cannot be empty', 400)
    }
    
    for (const nozzle of nozzleNumbers) {
      if (nozzle < 1 || nozzle > 8) {
        throw new FCCV2APIError(`Nozzle number must be between 1 and 8, got ${nozzle}`, 400)
      }
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/configure-nozzles', {
      device_id: deviceId,
      command: 'configure_nozzles',
      nozzle_numbers: nozzleNumbers
    })
  }

  async waynePresetVolume(deviceId: string, volume: string, decimals = 3): Promise<WayneCommandResponseV2> {
    // 验证体积
    const volumeNum = parseFloat(volume)
    if (isNaN(volumeNum) || volumeNum <= 0) {
      throw new FCCV2APIError('Volume must be a positive number', 400)
    }
    
    if (decimals < 0 || decimals > 3) {
      throw new FCCV2APIError('Decimals must be between 0 and 3', 400)
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/preset-volume', {
      device_id: deviceId,
      command: 'preset_volume',
      volume,
      decimals
    })
  }

  async waynePresetAmount(deviceId: string, amount: string, decimals = 2): Promise<WayneCommandResponseV2> {
    // 验证金额
    const amountNum = parseFloat(amount)
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new FCCV2APIError('Amount must be a positive number', 400)
    }
    
    if (decimals < 0 || decimals > 3) {
      throw new FCCV2APIError('Decimals must be between 0 and 3', 400)
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/preset-amount', {
      device_id: deviceId,
      command: 'preset_amount',
      amount,
      decimals
    })
  }

  async wayneUpdatePrices(deviceId: string, prices: Array<{ nozzle_number: number; price: string; decimals?: number }>): Promise<WayneCommandResponseV2> {
    // 验证价格数据
    if (!prices || prices.length === 0) {
      throw new FCCV2APIError('Prices array is required and cannot be empty', 400)
    }
    
    for (const priceInfo of prices) {
      if (priceInfo.nozzle_number < 1 || priceInfo.nozzle_number > 8) {
        throw new FCCV2APIError(`Nozzle number must be between 1 and 8, got ${priceInfo.nozzle_number}`, 400)
      }
      
      const price = parseFloat(priceInfo.price)
      if (isNaN(price) || price <= 0) {
        throw new FCCV2APIError(`Price must be a positive number for nozzle ${priceInfo.nozzle_number}`, 400)
      }
      
      const decimals = priceInfo.decimals || 2
      if (decimals < 0 || decimals > 4) {
        throw new FCCV2APIError(`Decimals must be between 0 and 4 for nozzle ${priceInfo.nozzle_number}`, 400)
      }
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/update-prices', {
      device_id: deviceId,
      command: 'update_prices',
      prices
    })
  }

  async wayneSuspendNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    // 验证喷嘴编号
    if (nozzleNumber < 1 || nozzleNumber > 8) {
      throw new FCCV2APIError(`Nozzle number must be between 1 and 8, got ${nozzleNumber}`, 400)
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/suspend-nozzle', {
      device_id: deviceId,
      command: 'suspend_nozzle',
      nozzle_number: nozzleNumber
    })
  }

  async wayneResumeNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    // 验证喷嘴编号
    if (nozzleNumber < 1 || nozzleNumber > 8) {
      throw new FCCV2APIError(`Nozzle number must be between 1 and 8, got ${nozzleNumber}`, 400)
    }
    
    return this.http.post<WayneCommandResponseV2>('/wayne/resume-nozzle', {
      device_id: deviceId,
      command: 'resume_nozzle',
      nozzle_number: nozzleNumber
    })
  }

  async wayneRequestCounters(deviceId: string, counterType: CounterType): Promise<WayneCommandResponseV2> {
    return this.http.post<WayneCommandResponseV2>('/wayne/request-counters', {
      device_id: deviceId,
      command: 'request_counters',
      counter_type: counterType
    })
  }

  // ===== 便捷方法别名 =====
  
  async authorize(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.wayneAuthorize(deviceId, async)
  }

  async reset(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.wayneReset(deviceId, async)
  }

  async stop(deviceId: string, async = false): Promise<WayneCommandResponseV2> {
    return this.wayneStop(deviceId, async)
  }

  async getStatus(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.wayneGetStatus(deviceId)
  }

  async getFillingInfo(deviceId: string): Promise<WayneCommandResponseV2> {
    return this.wayneGetFillingInfo(deviceId)
  }

  async configureNozzles(deviceId: string, nozzleNumbers: number[]): Promise<WayneCommandResponseV2> {
    return this.wayneConfigureNozzles(deviceId, nozzleNumbers)
  }

  async presetVolume(deviceId: string, volume: string, decimals = 3): Promise<WayneCommandResponseV2> {
    return this.waynePresetVolume(deviceId, volume, decimals)
  }

  async presetAmount(deviceId: string, amount: string, decimals = 2): Promise<WayneCommandResponseV2> {
    return this.waynePresetAmount(deviceId, amount, decimals)
  }

  async updatePrices(deviceId: string, prices: Array<{ nozzle_number: number; price: string; decimals?: number }>): Promise<WayneCommandResponseV2> {
    return this.wayneUpdatePrices(deviceId, prices)
  }

  async suspendNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    return this.wayneSuspendNozzle(deviceId, nozzleNumber)
  }

  async resumeNozzle(deviceId: string, nozzleNumber: number): Promise<WayneCommandResponseV2> {
    return this.wayneResumeNozzle(deviceId, nozzleNumber)
  }

  async requestCounters(deviceId: string, counterType: CounterType): Promise<WayneCommandResponseV2> {
    return this.wayneRequestCounters(deviceId, counterType)
  }
}

// ===== 导出默认实例 =====
export const fccV2API = new FCCV2APIClient()

// ===== 向后兼容的接口别名 =====
export type Device = DeviceV2
export type DeviceCreateRequest = DeviceCreateRequestV2
export type DeviceListResponse = DeviceListResponseV2
export type DeviceStatusInfo = DeviceStatusV2
export type CommandResponse = CommandResponseV2
export type PumpStatus = PumpStatusV2
export type PumpTotals = PumpTotalsV2 