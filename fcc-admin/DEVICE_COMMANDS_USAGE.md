# Device Commands Usage Guide

This document describes how to use the device command functionality in the FCC Admin interface.

## Overview

The FCC Admin interface now provides comprehensive manual command capabilities for Wayne DART protocol devices. You can send various commands including reset, price setting, authorization, and preset operations.

## Accessing Device Commands

### Quick Commands (on Device Card)
Each device card in the devices page shows quick action buttons for online devices:

- **📊 Status** - Quick status query
- **✅ Authorize** - Authorize device for fueling (pump devices only)
- **🔄 Reset** - Reset device to initial state (pump devices only)
- **⛔ Stop** - Emergency stop current operation (pump devices only)

### Command Center (Full Command Panel)
Click the **⚙️ Command Center** button on any device card to open the comprehensive command panel.

## Available Commands

### 1. Status Query (📊)
- **Purpose**: Query current device status
- **Parameters**: None
- **Use Case**: Check device health, current state, pump status

### 2. Reset (🔄)
- **Purpose**: Reset device to initial state
- **Parameters**: None
- **Use Case**: Clear error states, reset after completed transactions

### 3. Authorize (✅)
- **Purpose**: Authorize device to begin fueling operation
- **Parameters**: None
- **Use Case**: Allow customer to start fueling

### 4. Stop (⛔)
- **Purpose**: Stop current operation immediately
- **Parameters**: None
- **Use Case**: Emergency stop, cancel ongoing transaction

### 5. Set Price (💰)
- **Purpose**: Configure fuel price
- **Parameters**: 
  - `Price (Yuan/Liter)`: Enter price (e.g., 3.333)
  - Range: 0.001 - 999.999
- **Use Case**: Update fuel pricing

### 6. Preset Volume (🛢️)
- **Purpose**: Set predetermined volume for fueling
- **Parameters**:
  - `Volume (Liters)`: Required - amount to dispense (e.g., 20.00)
  - `Nozzle Number`: Optional - specific nozzle (1-8)
- **Use Case**: Pre-authorize specific fuel amount

### 7. Preset Amount (💵)
- **Purpose**: Set predetermined monetary amount for fueling
- **Parameters**:
  - `Amount (Yuan)`: Required - monetary value (e.g., 100.00)
  - `Nozzle Number`: Optional - specific nozzle (1-8)
- **Use Case**: Pre-authorize specific payment amount

### 8. Request Total Counters (📈)
- **Purpose**: Retrieve cumulative counter data
- **Parameters**: None
- **Use Case**: Get total volume/amount dispensed

### 9. Request Filling Info (📋)
- **Purpose**: Get current transaction information
- **Parameters**: None
- **Use Case**: Check ongoing transaction details

## Command Center Interface

### Command Selection
1. Open Command Center from device card
2. Select desired command from the grid
3. Commands with parameters will show ⚙️ icon

### Parameter Input
- Required parameters are marked with red asterisk (*)
- Input validation ensures proper formats
- Helpful descriptions guide proper values

### Command Summary
- Review command details before execution
- Shows target device and parameters
- Displays device address in hex format

### Quick Actions
At the bottom of Command Center, find quick buttons for:
- 📊 Quick Status
- 🔄 Quick Reset  
- ✅ Quick Auth
- ⛔ Emergency Stop

## Best Practices

### Price Setting
- Use standard fuel pricing format (e.g., 3.333)
- Verify price before execution
- Price changes affect immediate transactions

### Preset Operations
- Verify volume/amount values
- Use appropriate nozzle numbers if specified
- Consider pump capacity limits

### Emergency Procedures
- Use Stop command for immediate halt
- Reset after error conditions
- Check status after recovery operations

### Device States
- Authorize only when pump is in RESET/IDLE state
- Reset devices in COMPLETED state before new transactions
- Monitor device responses for state changes

## Error Handling

The interface provides automatic error handling:
- Success notifications for completed commands
- Warning notifications for command failures
- Error details in notification messages
- Command history in browser console

## Device Address Display

Device addresses are shown in hexadecimal format (e.g., 0x50, 0x51) for easy identification with DART protocol specifications.

## Troubleshooting

### Device Not Responding
1. Check device connection status
2. Verify device is online
3. Try Reset command
4. Check serial communication

### Command Failures
1. Verify device supports the command
2. Check parameter formats
3. Ensure device is in correct state
4. Review error messages in notifications

### Price Setting Issues
1. Use decimal format (not percentage)
2. Stay within range limits
3. Verify BCD conversion if needed
4. Check device price display

## Wayne DART Protocol Notes

- Device addresses range from 0x50 to 0x6F
- Commands follow Wayne DART v1.3 specification
- Response timeout is 25ms per protocol
- Supports both 9600 and 19200 baud rates

## Support

For technical issues:
1. Check browser console for detailed logs
2. Verify FCC service connectivity
3. Review device documentation
4. Contact system administrator

---

*Last Updated: December 2024*
*FCC Service Version: 1.0* 