'use client'

import React, { useState } from 'react'
import { fccV2API, PreAuthRequest, PreAuthResponse, PreAuthType } from '@/lib/api-client-v2'
import { createVolumePreauth, createAmountPreauth, validatePreauthRequest } from '@/lib/preauth-test'

/**
 * PreAuth 功能演示组件
 * 
 * 这个组件展示了如何使用 PreAuth 功能的各种 API 调用方式
 */
export default function PreauthDemo() {
  const [deviceId, setDeviceId] = useState('device_001')
  const [nozzleId, setNozzleId] = useState('nozzle_001')
  const [authType, setAuthType] = useState<PreAuthType>('preset_volume')
  const [volume, setVolume] = useState('25.50')
  const [amount, setAmount] = useState('100.00')
  const [decimals, setDecimals] = useState(2)
  const [employeeId, setEmployeeId] = useState('001')
  const [isLoading, setIsLoading] = useState(false)
  const [lastResponse, setLastResponse] = useState<PreAuthResponse | null>(null)
  const [lastError, setLastError] = useState<string | null>(null)

  const handlePreauth = async (method: string) => {
    setIsLoading(true)
    setLastResponse(null)
    setLastError(null)

    try {
      let request: PreAuthRequest

      if (authType === 'preset_volume') {
        request = createVolumePreauth(deviceId, volume, {
          nozzleId: nozzleId || undefined,
          decimals,
          employeeId
        })
      } else {
        request = createAmountPreauth(deviceId, amount, {
          nozzleId: nozzleId || undefined,
          decimals,
          employeeId
        })
      }

      // 验证请求
      const validation = validatePreauthRequest(request)
      if (!validation.valid) {
        throw new Error(`请求验证失败: ${validation.errors.join(', ')}`)
      }

      let response: PreAuthResponse

      switch (method) {
        case 'waynePreauth':
          response = await fccV2API.waynePreauth(request)
          break
        case 'preauth':
          response = await fccV2API.preauth(request)
          break
        case 'wayne.preauth':
          const { apiV2 } = await import('@/lib/api-client-v2')
          response = await apiV2.wayne.preauth(request)
          break
        case 'commands.execute':
          const { apiV2: apiV2Commands } = await import('@/lib/api-client-v2')
          response = await apiV2Commands.commands.execute(deviceId, {
            command_type: 'preauth',
            parameters: {
              nozzle_id: nozzleId || undefined,
              auth_type: authType,
              volume: authType === 'preset_volume' ? volume : undefined,
              amount: authType === 'preset_amount' ? amount : undefined,
              decimals,
              employee_id: employeeId
            }
          })
          break
        default:
          throw new Error(`未知的方法: ${method}`)
      }

      setLastResponse(response)
      console.log(`✅ ${method} 成功:`, response)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      setLastError(errorMessage)
      console.error(`❌ ${method} 失败:`, error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">🔐 PreAuth 功能演示</h1>
      
      {/* 参数配置区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-700">📋 参数配置</h2>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">设备 ID</label>
            <input
              type="text"
              value={deviceId}
              onChange={(e) => setDeviceId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="device_001"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">喷嘴 ID（可选）</label>
            <input
              type="text"
              value={nozzleId}
              onChange={(e) => setNozzleId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="nozzle_001"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">授权类型</label>
            <select
              value={authType}
              onChange={(e) => setAuthType(e.target.value as PreAuthType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="preset_volume">预设体积</option>
              <option value="preset_amount">预设金额</option>
            </select>
          </div>

          {authType === 'preset_volume' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">体积（升）</label>
              <input
                type="text"
                value={volume}
                onChange={(e) => setVolume(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="25.50"
              />
            </div>
          )}

          {authType === 'preset_amount' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">金额</label>
              <input
                type="text"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="100.00"
              />
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">小数位数</label>
              <input
                type="number"
                value={decimals}
                onChange={(e) => setDecimals(parseInt(e.target.value) || 0)}
                min="0"
                max="4"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">员工 ID</label>
              <input
                type="text"
                value={employeeId}
                onChange={(e) => setEmployeeId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="001"
              />
            </div>
          </div>
        </div>

        {/* API 调用按钮区域 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-700">🚀 API 调用方式</h2>
          
          <div className="space-y-3">
            <button
              onClick={() => handlePreauth('waynePreauth')}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              fccV2API.waynePreauth()
            </button>

            <button
              onClick={() => handlePreauth('preauth')}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              fccV2API.preauth()
            </button>

            <button
              onClick={() => handlePreauth('wayne.preauth')}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              apiV2.wayne.preauth()
            </button>

            <button
              onClick={() => handlePreauth('commands.execute')}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              apiV2.commands.execute()
            </button>
          </div>

          {isLoading && (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">正在发送请求...</span>
            </div>
          )}
        </div>
      </div>

      {/* 响应结果区域 */}
      {(lastResponse || lastError) && (
        <div className="mt-6 p-4 border rounded-lg">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">📥 响应结果</h3>
          
          {lastResponse && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex items-center mb-2">
                <span className="text-green-600 font-semibold">✅ 成功</span>
              </div>
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(lastResponse, null, 2)}
              </pre>
            </div>
          )}

          {lastError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-center mb-2">
                <span className="text-red-600 font-semibold">❌ 错误</span>
              </div>
              <p className="text-sm text-red-700">{lastError}</p>
            </div>
          )}
        </div>
      )}

      {/* 说明文档 */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">📖 使用说明</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• 设置设备 ID 和喷嘴 ID（使用真实存在的 ID）</li>
          <li>• 选择授权类型：预设体积或预设金额</li>
          <li>• 输入相应的体积或金额值</li>
          <li>• 点击不同的 API 调用按钮测试各种调用方式</li>
          <li>• 查看响应结果和错误信息</li>
          <li>• 预授权记录会在后端缓存 30 秒</li>
        </ul>
      </div>
    </div>
  )
}
