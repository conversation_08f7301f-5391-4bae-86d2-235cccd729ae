'use client'

import React from 'react'
import { useNotifications, SimpleNotification } from '@/lib/simple-state'

interface NotificationItemProps {
  notification: SimpleNotification
  onRemove: (id: string) => void
}

function NotificationItem({ notification, onRemove }: NotificationItemProps) {
  const getIcon = (type: SimpleNotification['type']) => {
    switch (type) {
      case 'success': return '✅'
      case 'error': return '❌'
      case 'warning': return '⚠️'
      case 'info': return 'ℹ️'
      default: return 'ℹ️'
    }
  }

  const getColorClass = (type: SimpleNotification['type']) => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 text-green-800'
      case 'error': return 'bg-red-50 border-red-200 text-red-800'
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'info': return 'bg-blue-50 border-blue-200 text-blue-800'
      default: return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  return (
    <div className={`p-3 border rounded-lg flex items-start justify-between ${getColorClass(notification.type)}`}>
      <div className="flex items-start space-x-2 flex-1">
        <span className="text-lg">{getIcon(notification.type)}</span>
        <div className="flex-1">
          <p className="text-sm font-medium">{notification.message}</p>
          <p className="text-xs opacity-70 mt-1">
            {notification.timestamp.toLocaleTimeString()}
          </p>
        </div>
      </div>
      <button
        onClick={() => onRemove(notification.id)}
        className="ml-2 text-lg hover:opacity-70"
        title="Close"
      >
        ×
      </button>
    </div>
  )
}

export function NotificationList() {
  const { notifications, removeNotification, clearAll } = useNotifications()

  if (notifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-600">
          {notifications.length} notification{notifications.length > 1 ? 's' : ''}
        </span>
        {notifications.length > 1 && (
          <button
            onClick={clearAll}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Clear all
          </button>
        )}
      </div>
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onRemove={removeNotification}
        />
      ))}
    </div>
  )
}

// 简单的Toast通知Hook
export function useToast() {
  const { addNotification } = useNotifications()

  return {
    success: (message: string) => addNotification('success', message),
    error: (message: string) => addNotification('error', message),
    warning: (message: string) => addNotification('warning', message),
    info: (message: string) => addNotification('info', message)
  }
} 