'use client'

import React, { useState, useEffect } from 'react'
import { Device } from '@/lib/api-client'
import { useNozzles } from '@/lib/simple-state'
import { NozzleV2 } from '@/lib/api-client-v2'
import CustomInput from './CustomInput'
import { useToast } from '@/components/ui/Notifications'

interface ParameterField {
  name: string
  label: string
  type: string
  step?: string
  min?: string
  max?: string
  placeholder?: string
  required?: boolean
  description?: string
}

interface CommandConfig {
  id: string
  name: string
  description: string
  icon: string
  color: string
  hasParameters: boolean
  parameterFields?: ParameterField[]
}

interface DeviceCommandPanelProps {
  device: Device
  isOpen: boolean
  onClose: () => void
  onCommand: (deviceId: string, commandType: string, parameters?: Record<string, any>) => Promise<void>
}

export function DeviceCommandPanel({ device, isOpen, onClose, onCommand }: DeviceCommandPanelProps) {
  const { getNozzles } = useNozzles()
  const toast = useToast()
  const [activeTab, setActiveTab] = useState<'pump' | 'nozzles'>('pump')
  const [selectedNozzle, setSelectedNozzle] = useState<number | null>(null)
  const [selectedCommand, setSelectedCommand] = useState('')
  const [parameters, setParameters] = useState<Record<string, any>>({})
  const [isExecuting, setIsExecuting] = useState(false)
  const [nozzles, setNozzles] = useState<NozzleV2[]>([])
  const [loadingNozzles, setLoadingNozzles] = useState(false)

  // 加载设备喷嘴
  useEffect(() => {
    if (isOpen && activeTab === 'nozzles') {
      loadNozzles()
    }
  }, [isOpen, activeTab, device.id])

  const loadNozzles = async () => {
    try {
      setLoadingNozzles(true)
      const deviceNozzles = await getNozzles(device.id)
      setNozzles(deviceNozzles)
    } catch (error) {
      console.error('Failed to load nozzles:', error)
      toast.error('Failed to load nozzles')
    } finally {
      setLoadingNozzles(false)
    }
  }

  // Pump 层级的命令配置
  const pumpCommands: CommandConfig[] = [
    {
      id: 'status_query',
      name: 'Device Status',
      description: '查询整个设备状态',
      icon: '📊',
      color: 'bg-blue-600 hover:bg-blue-700',
      hasParameters: false
    },
    {
      id: 'authorize',
      name: 'Authorize Device',
      description: '授权整个设备开始操作',
      icon: '✅',
      color: 'bg-green-600 hover:bg-green-700',
      hasParameters: true,
      parameterFields: [
        {
          name: 'employee_id',
          label: 'Employee Card ID (员工卡号)',
          type: 'text',
          placeholder: '001',
          required: true,
          description: 'Enter employee card ID (e.g., 001)'
        }
      ]
    },
    {
      id: 'reset',
      name: 'Reset Device',
      description: '重置整个设备到初始状态',
      icon: '🔄',
      color: 'bg-orange-600 hover:bg-orange-700',
      hasParameters: false
    },
    {
      id: 'stop',
      name: 'Emergency Stop',
      description: '紧急停止整个设备',
      icon: '⛔',
      color: 'bg-red-600 hover:bg-red-700',
      hasParameters: false
    },
    {
      id: 'request_total_counters',
      name: 'Total Counters',
      description: '请求设备总计数器数据',
      icon: '📈',
      color: 'bg-gray-600 hover:bg-gray-700',
      hasParameters: false
    },
    {
      id: 'request_filling_info',
      name: 'Device Info',
      description: '请求设备填充信息',
      icon: '📋',
      color: 'bg-cyan-600 hover:bg-cyan-700',
      hasParameters: false
    }
  ]

  // Nozzle 层级的命令配置
  const nozzleCommands: CommandConfig[] = [
    {
      id: 'set_price',
      name: 'Set Price',
      description: '设置指定喷嘴的燃油价格',
      icon: '💰',
      color: 'bg-purple-600 hover:bg-purple-700',
      hasParameters: true,
      parameterFields: [
        {
          name: 'price',
          label: 'Price (Rupiah/Liter)',
          type: 'number',
                      step: '1',
            min: '1',
            max: '999999',
          placeholder: '15000',
          required: true,
          description: 'Enter price in Rupiah per liter (e.g., 15000)'
        },
        {
          name: 'decimals',
          label: 'Decimal Places',
          type: 'number',
          min: '0',
          max: '4',
          placeholder: '3',
          description: 'Optional: Number of decimal places (0-4, default: 3)'
        }
      ]
    },
    {
      id: 'preset_volume',
      name: 'Preset Volume',
      description: '为指定喷嘴预设加油体积',
      icon: '🛢️',
      color: 'bg-indigo-600 hover:bg-indigo-700',
      hasParameters: true,
      parameterFields: [
        {
          name: 'volume',
          label: 'Volume (Liters)',
          type: 'number',
          step: '0.01',
          min: '0.01',
          max: '999.99',
          placeholder: '20.00',
          required: true,
          description: 'Enter volume in liters (e.g., 20.00)'
        },
        {
          name: 'employee_id',
          label: 'Employee Card ID (员工卡号)',
          type: 'text',
          placeholder: '001',
          required: true,
          description: 'Enter employee card ID (e.g., 001)'
        }
      ]
    },
    {
      id: 'preset_amount',
      name: 'Preset Amount',
      description: '为指定喷嘴预设加油金额',
      icon: '💵',
      color: 'bg-teal-600 hover:bg-teal-700',
      hasParameters: true,
      parameterFields: [
        {
          name: 'amount',
          label: 'Amount (Rupiah)',
          type: 'number',
                      step: '1',
            min: '1',
            max: '9999999',
          placeholder: '100000',
          required: true,
          description: 'Enter amount in Rupiah (e.g., 100000)'
        },
        {
          name: 'employee_id',
          label: 'Employee Card ID (员工卡号)',
          type: 'text',
          placeholder: '001',
          required: true,
          description: 'Enter employee card ID (e.g., 001)'
        }
      ]
    },
    {
      id: 'suspend_nozzle',
      name: 'Suspend Nozzle',
      description: '暂停指定喷嘴',
      icon: '⏸️',
      color: 'bg-yellow-600 hover:bg-yellow-700',
      hasParameters: false
    },
    {
      id: 'resume_nozzle',
      name: 'Resume Nozzle',
      description: '恢复指定喷嘴',
      icon: '▶️',
      color: 'bg-emerald-600 hover:bg-emerald-700',
      hasParameters: false
    }
  ]

  const getCurrentCommands = () => {
    return activeTab === 'pump' ? pumpCommands : nozzleCommands
  }

  const selectedCommandConfig = getCurrentCommands().find(cmd => cmd.id === selectedCommand)

  const handleParameterChange = (fieldName: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  const handleExecuteCommand = async () => {
    if (!selectedCommand) {
      toast.error('Please select a command')
      return
    }

    // Nozzle 层级命令需要选择喷嘴
    if (activeTab === 'nozzles' && selectedNozzle === null) {
      toast.error('Please select a nozzle for nozzle-level commands')
      return
    }

    try {
      setIsExecuting(true)

      // 准备命令参数
      let commandParameters: Record<string, any> = {
        device_address: device.device_address,
        ...parameters
      }

      // 确保员工ID参数正确传递
      if (parameters.employee_id) {
        commandParameters.employee_id = parameters.employee_id
      }

      // Nozzle 层级命令的特殊处理
      if (activeTab === 'nozzles' && selectedNozzle !== null) {
        commandParameters.nozzle = selectedNozzle

        // 特殊处理价格参数
        if (selectedCommand === 'set_price' && parameters.price) {
          commandParameters.prices = [{
            nozzle_number: selectedNozzle,
            price: parameters.price.toString(),
            decimals: parameters.decimals ? parseInt(parameters.decimals) : 3
          }]
          // 清理单独的参数
          delete commandParameters.price
          delete commandParameters.decimals
        }

        // 特殊处理预设参数
        if (selectedCommand === 'preset_volume' && parameters.volume) {
          commandParameters.volume = parseFloat(parameters.volume)
        }

        if (selectedCommand === 'preset_amount' && parameters.amount) {
          commandParameters.amount = parseFloat(parameters.amount)
        }
      }

      await onCommand(device.id, selectedCommand, commandParameters)

      // 成功后清空参数并关闭面板
      setParameters({})
      setSelectedCommand('')
      setSelectedNozzle(null)
      onClose()

      toast.success(`Command '${selectedCommandConfig?.name}' executed successfully`)
    } catch (error) {
      console.error('Command execution failed:', error)
      toast.error(`Failed to execute command: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsExecuting(false)
    }
  }

  const handleCancel = () => {
    setSelectedCommand('')
    setParameters({})
    setSelectedNozzle(null)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-5xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Device Command Center</h2>
            <p className="text-sm text-gray-600">
              {device.name} • Type: {device.type} • Address: 0x{device.device_address.toString(16).toUpperCase().padStart(2, '0')}
            </p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ✕
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => {
                  setActiveTab('pump')
                  setSelectedCommand('')
                  setSelectedNozzle(null)
                  setParameters({})
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'pump'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                🔧 Pump Commands
              </button>
              <button
                onClick={() => {
                  setActiveTab('nozzles')
                  setSelectedCommand('')
                  setSelectedNozzle(null)
                  setParameters({})
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'nozzles'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                ⛽ Nozzle Commands
              </button>
            </nav>
          </div>
        </div>

        {/* Nozzle Selection (only for nozzle commands) */}
        {activeTab === 'nozzles' && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Select Nozzle</h3>
            {loadingNozzles ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-500 mt-2">Loading nozzles...</p>
              </div>
            ) : nozzles.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {nozzles.map((nozzle) => (
                  <button
                    key={nozzle.number}
                    onClick={() => setSelectedNozzle(nozzle.number)}
                    className={`p-3 rounded-lg border-2 text-center transition-all ${
                      selectedNozzle === nozzle.number
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium">Nozzle {nozzle.number}</div>
                    <div className="text-xs text-gray-500 mt-1">{nozzle.name || `Position ${nozzle.position}`}</div>
                    <div className="text-xs text-gray-600 mt-1">Rp {nozzle.current_price}/L</div>
                    <div className={`text-xs mt-1 px-2 py-1 rounded ${
                      nozzle.is_enabled ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                    }`}>
                      {nozzle.is_enabled ? 'Enabled' : 'Disabled'}
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No nozzles found for this device</p>
                <button
                  onClick={loadNozzles}
                  className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
                >
                  🔄 Retry
                </button>
              </div>
            )}
          </div>
        )}

        {/* Command Selection */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Select {activeTab === 'pump' ? 'Pump' : 'Nozzle'} Command
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {getCurrentCommands().map((command) => (
              <button
                key={command.id}
                onClick={() => {
                  setSelectedCommand(command.id)
                  // 为特定命令设置默认参数
                  const defaultParams: Record<string, any> = {}
                  if (command.id === 'authorize' || command.id === 'preset_volume' || command.id === 'preset_amount') {
                    defaultParams.employee_id = '001'
                  }
                  setParameters(defaultParams)
                }}
                disabled={activeTab === 'nozzles' && selectedNozzle === null}
                className={`p-4 rounded-lg border-2 text-left transition-all ${
                  selectedCommand === command.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${activeTab === 'nozzles' && selectedNozzle === null ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">{command.icon}</span>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{command.name}</div>
                    <div className="text-sm text-gray-600 mt-1">{command.description}</div>
                    {command.hasParameters && (
                      <div className="text-xs text-blue-600 mt-1">⚙️ Requires parameters</div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Parameter Input */}
        {selectedCommandConfig?.hasParameters && selectedCommandConfig.parameterFields && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Command Parameters</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedCommandConfig.parameterFields.map((field) => (
                  <CustomInput
                    key={field.name}
                    type={field.type === 'number' ? 'decimal' : 'text'}
                    label={field.label}
                    value={parameters[field.name] || ''}
                    onChange={(value) => handleParameterChange(field.name, value)}
                      placeholder={field.placeholder}
                    min={field.min ? parseFloat(field.min) : undefined}
                    max={field.max ? parseFloat(field.max) : undefined}
                    step={field.step ? parseFloat(field.step) : undefined}
                    decimals={field.name === 'price' ? 3 : 2}
                      required={field.required}
                    error={field.required && !parameters[field.name] ? `${field.label} is required` : undefined}
                    />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Selected Command Summary */}
        {selectedCommandConfig && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Command Summary</h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{selectedCommandConfig.icon}</span>
                <div className="flex-1">
                  <div className="font-medium text-blue-900">{selectedCommandConfig.name}</div>
                  <div className="text-sm text-blue-700 mt-1">{selectedCommandConfig.description}</div>
                  <div className="text-sm text-blue-600 mt-2">
                    Target: {device.name} (0x{device.device_address.toString(16).toUpperCase().padStart(2, '0')})
                    {activeTab === 'nozzles' && selectedNozzle !== null && (
                      <span> → Nozzle {selectedNozzle}</span>
                    )}
                  </div>
                  {Object.keys(parameters).length > 0 && (
                    <div className="mt-3">
                      <div className="text-sm font-medium text-blue-900">Parameters:</div>
                      <div className="text-sm text-blue-700 mt-1">
                        {Object.entries(parameters).map(([key, value]) => (
                          <div key={key} className="inline-block mr-4">
                            <span className="font-medium">{key}:</span> {value}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4">
          <button
            onClick={handleCancel}
            disabled={isExecuting}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleExecuteCommand}
            disabled={!selectedCommand || isExecuting || (activeTab === 'nozzles' && selectedNozzle === null)}
            className={`px-6 py-2 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed ${
              selectedCommandConfig?.color || 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isExecuting ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Executing...</span>
              </div>
            ) : (
              `Execute ${selectedCommandConfig?.name || 'Command'}`
            )}
          </button>
        </div>

        {/* Quick Actions for Pump Commands */}
        {activeTab === 'pump' && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Pump Actions</h4>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => onCommand(device.id, 'status_query')}
                disabled={isExecuting}
                className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
              >
                📊 Quick Status
              </button>
              <button
                onClick={() => onCommand(device.id, 'reset')}
                disabled={isExecuting}
                className="px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200 disabled:opacity-50"
              >
                🔄 Quick Reset
              </button>
              <button
                onClick={() => onCommand(device.id, 'authorize', { employee_id: '001' })}
                disabled={isExecuting}
                className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50"
              >
                ✅ Quick Auth (001)
              </button>
              <button
                onClick={() => onCommand(device.id, 'stop')}
                disabled={isExecuting}
                className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
              >
                ⛔ Emergency Stop
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 