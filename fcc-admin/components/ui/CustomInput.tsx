'use client'

import React, { useState, useRef, useEffect, forwardRef } from 'react'

interface CustomInputProps {
  type?: 'text' | 'number' | 'decimal'
  value: string | number
  onChange: (value: string) => void
  placeholder?: string
  min?: number
  max?: number
  step?: number
  decimals?: number
  required?: boolean
  disabled?: boolean
  className?: string
  label?: string
  error?: string
  prefix?: string
  suffix?: string
  autoFocus?: boolean
  onEnter?: () => void
  onEscape?: () => void
}

const CustomInput = forwardRef<HTMLDivElement, CustomInputProps>(
  function CustomInput({
    type = 'text',
    value,
    onChange,
    placeholder,
    min,
    max,
    step = 0.001,
    decimals = 3,
    required = false,
    disabled = false,
    className = '',
    label,
    error,
    prefix,
    suffix,
    autoFocus = false,
    onEnter,
    onEscape
  }, ref) {
    const [isFocused, setIsFocused] = useState(false)
    const [inputValue, setInputValue] = useState(String(value || ''))
    const inputRef = useRef<HTMLInputElement>(null)

    useEffect(() => {
      setInputValue(String(value || ''))
    }, [value])

    useEffect(() => {
      if (autoFocus && inputRef.current) {
        inputRef.current.focus()
      }
    }, [autoFocus])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let newValue = e.target.value

      if (type === 'number' || type === 'decimal') {
        // 只允许数字、小数点和负号
        newValue = newValue.replace(/[^0-9.-]/g, '')
        
        // 防止多个小数点
        const parts = newValue.split('.')
        if (parts.length > 2) {
          newValue = parts[0] + '.' + parts.slice(1).join('')
        }
        
        // 限制小数位数
        if (type === 'decimal' && parts[1] && parts[1].length > decimals) {
          newValue = parts[0] + '.' + parts[1].substring(0, decimals)
        }
        
        // 检查范围
        const numValue = parseFloat(newValue)
        if (!isNaN(numValue)) {
          if (min !== undefined && numValue < min) {
            newValue = String(min)
          }
          if (max !== undefined && numValue > max) {
            newValue = String(max)
          }
        }
      }

      setInputValue(newValue)
      onChange(newValue)
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && onEnter) {
        onEnter()
      }
      if (e.key === 'Escape' && onEscape) {
        onEscape()
      }
      
      // 数字输入特殊处理
      if (type === 'number' || type === 'decimal') {
        if (e.key === 'ArrowUp') {
          e.preventDefault()
          const currentValue = parseFloat(inputValue) || 0
          const newValue = currentValue + (step || 1)
          const finalValue = max !== undefined ? Math.min(newValue, max) : newValue
          const formatted = type === 'decimal' ? finalValue.toFixed(decimals) : String(finalValue)
          setInputValue(formatted)
          onChange(formatted)
        }
        if (e.key === 'ArrowDown') {
          e.preventDefault()
          const currentValue = parseFloat(inputValue) || 0
          const newValue = currentValue - (step || 1)
          const finalValue = min !== undefined ? Math.max(newValue, min) : newValue
          const formatted = type === 'decimal' ? finalValue.toFixed(decimals) : String(finalValue)
          setInputValue(formatted)
          onChange(formatted)
        }
      }
    }

    const handleIncrement = () => {
      const currentValue = parseFloat(inputValue) || 0
      const newValue = currentValue + (step || 1)
      const finalValue = max !== undefined ? Math.min(newValue, max) : newValue
      const formatted = type === 'decimal' ? finalValue.toFixed(decimals) : String(finalValue)
      setInputValue(formatted)
      onChange(formatted)
    }

    const handleDecrement = () => {
      const currentValue = parseFloat(inputValue) || 0
      const newValue = currentValue - (step || 1)
      const finalValue = min !== undefined ? Math.max(newValue, min) : newValue
      const formatted = type === 'decimal' ? finalValue.toFixed(decimals) : String(finalValue)
      setInputValue(formatted)
      onChange(formatted)
    }

    const baseClasses = `
      relative w-full px-3 py-2 
      border rounded-lg 
      transition-all duration-200 
      focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500
      ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
      ${error ? 'border-red-500 focus-within:ring-red-500 focus-within:border-red-500' : 'border-gray-300'}
      ${isFocused ? 'shadow-md' : 'shadow-sm'}
    `

    return (
      <div ref={ref} className={`${className}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <div className={baseClasses}>
          <div className="flex items-center">
            {prefix && (
              <span className="text-gray-500 text-sm mr-2">{prefix}</span>
            )}
            
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              disabled={disabled}
              className="flex-1 outline-none bg-transparent text-gray-900 placeholder-gray-400"
            />
            
            {suffix && (
              <span className="text-gray-500 text-sm ml-2">{suffix}</span>
            )}
            
            {(type === 'number' || type === 'decimal') && !disabled && (
              <div className="flex flex-col ml-2">
                <button
                  type="button"
                  onClick={handleIncrement}
                  className="w-4 h-3 flex items-center justify-center text-gray-400 hover:text-gray-600 text-xs leading-none"
                >
                  ▲
                </button>
                <button
                  type="button"
                  onClick={handleDecrement}
                  className="w-4 h-3 flex items-center justify-center text-gray-400 hover:text-gray-600 text-xs leading-none"
                >
                  ▼
                </button>
              </div>
            )}
          </div>
        </div>
        
        {error && (
          <p className="text-red-500 text-xs mt-1">{error}</p>
        )}
        
        {/* 显示范围提示 */}
        {(type === 'number' || type === 'decimal') && (min !== undefined || max !== undefined) && (
          <p className="text-gray-400 text-xs mt-1">
            Range: {min !== undefined ? min : '∞'} - {max !== undefined ? max : '∞'}
            {step && ` (step: ${step})`}
          </p>
        )}
      </div>
    )
  }
)

export default CustomInput 