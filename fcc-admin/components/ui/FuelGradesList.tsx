'use client'

import React from 'react'
import { FuelGrade } from '@/lib/api-client-v2'

interface FuelGradesListProps {
  grades: FuelGrade[]
  loading: boolean
  error: string | null
  onEdit: (grade: FuelGrade) => void
  onDelete: (gradeId: string) => void
}

export function FuelGradesList({ grades, loading, error, onEdit, onDelete }: FuelGradesListProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">加载油品数据...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border border-red-200 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-red-600">
            <div className="text-lg font-medium">加载失败</div>
            <div className="text-sm mt-1">{error}</div>
          </div>
        </div>
      </div>
    )
  }

  if (!grades || grades.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center py-8">
          <div className="text-4xl mb-4">⛽</div>
          <div className="text-lg font-medium text-gray-900">暂无油品数据</div>
          <div className="text-sm text-gray-500 mt-1">请先添加油品或触发同步</div>
        </div>
      </div>
    )
  }

  const formatPrice = (price: number | string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numPrice)
  }

  const getFuelTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'gasoline':
        return 'bg-green-100 text-green-800'
      case 'diesel':
        return 'bg-blue-100 text-blue-800'
      case 'premium':
        return 'bg-purple-100 text-purple-800'
      case 'regular':
        return 'bg-gray-100 text-gray-800'
      case 'kerosene':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">油品列表</h3>
        <p className="text-sm text-gray-500 mt-1">共 {grades.length} 种油品</p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                油品信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                价格
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {grades.map((grade) => (
              <tr key={grade.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{grade.name}</div>
                    {grade.description && (
                      <div className="text-sm text-gray-500">{grade.description}</div>
                    )}
                    <div className="text-xs text-gray-400 mt-1">ID: {grade.id}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getFuelTypeColor(grade.type)}`}>
                    {grade.type}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {formatPrice(grade.price)}
                  </div>
                  <div className="text-xs text-gray-500">per {grade.unit}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {grade.deleted_at ? (
                    <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                      已停用
                    </span>
                  ) : (
                    <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                      活跃
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(grade.updated_at).toLocaleString('zh-CN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onEdit(grade)}
                      className="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => onDelete(grade.id)}
                      className="text-red-600 hover:text-red-900 text-sm"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
} 