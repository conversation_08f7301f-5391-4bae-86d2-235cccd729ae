/**
 * API配置模态框组件
 * 允许用户动态设置和测试API URL
 */

'use client'

import { useState, useEffect } from 'react'
import { apiConfigManager, type APIConfig, validateApiUrl } from '@/lib/api-config'

interface ApiConfigModalProps {
  isOpen: boolean
  onClose: () => void
  onSave?: (config: APIConfig) => void
}

export function ApiConfigModal({ isOpen, onClose, onSave }: ApiConfigModalProps) {
  const [config, setConfig] = useState<APIConfig>({
    baseUrl: '',
    apiVersion: 'v2',
    timeout: 10000,
  })
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    message: string
  } | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  // 加载当前配置
  useEffect(() => {
    if (isOpen) {
      const currentConfig = apiConfigManager.getConfig()
      setConfig(currentConfig)
      setValidationResult(null)
    }
  }, [isOpen])

  /**
   * 处理输入变更
   */
  const handleInputChange = (field: keyof APIConfig, value: string | number) => {
    setConfig(prev => ({
      ...prev,
      [field]: value,
    }))
    setValidationResult(null)
  }

  /**
   * 测试API连接
   */
  const handleTestConnection = async () => {
    if (!config.baseUrl.trim()) {
      setValidationResult({
        isValid: false,
        message: '请输入API服务器地址',
      })
      return
    }

    setIsValidating(true)
    setValidationResult(null)

    try {
      const isValid = await validateApiUrl(config.baseUrl)
      setValidationResult({
        isValid,
        message: isValid 
          ? '✅ 连接成功！API服务器响应正常' 
          : '❌ 连接失败，请检查服务器地址和网络连接',
      })
    } catch (error) {
      setValidationResult({
        isValid: false,
        message: `❌ 连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
      })
    } finally {
      setIsValidating(false)
    }
  }

  /**
   * 保存配置
   */
  const handleSave = async () => {
    if (!config.baseUrl.trim()) {
      setValidationResult({
        isValid: false,
        message: '请输入API服务器地址',
      })
      return
    }

    setIsSaving(true)

    try {
      // 更新配置
      apiConfigManager.updateConfig(config)
      
      // 调用回调
      onSave?.(config)
      
      // 关闭模态框
      onClose()
    } catch (error) {
      setValidationResult({
        isValid: false,
        message: `保存失败: ${error instanceof Error ? error.message : '未知错误'}`,
      })
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * 重置为默认配置
   */
  const handleReset = () => {
    setConfig({
      baseUrl: 'http://localhost:8081',
      apiVersion: 'v2',
      timeout: 10000,
    })
    setValidationResult(null)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            🔧 API服务器配置
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            配置FCC API服务器连接参数
          </p>
        </div>

        <div className="px-6 py-4 space-y-4">
          {/* API服务器地址 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API服务器地址 *
            </label>
            <input
              type="text"
              value={config.baseUrl}
              onChange={(e) => handleInputChange('baseUrl', e.target.value)}
              placeholder="http://192.168.1.100:8081"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              自动添加 /api/v2 后缀，无需手动输入
            </p>
          </div>

          {/* API版本 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API版本
            </label>
            <select
              value={config.apiVersion}
              onChange={(e) => handleInputChange('apiVersion', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="v1">v1</option>
              <option value="v2">v2</option>
            </select>
          </div>

          {/* 连接超时 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              连接超时 (毫秒)
            </label>
            <input
              type="number"
              value={config.timeout}
              onChange={(e) => handleInputChange('timeout', parseInt(e.target.value) || 10000)}
              min="1000"
              max="60000"
              step="1000"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 测试连接按钮 */}
          <div>
            <button
              onClick={handleTestConnection}
              disabled={isValidating || !config.baseUrl.trim()}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isValidating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  测试连接中...
                </>
              ) : (
                '🔍 测试连接'
              )}
            </button>
          </div>

          {/* 验证结果 */}
          {validationResult && (
            <div className={`p-3 rounded-md text-sm ${
              validationResult.isValid 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {validationResult.message}
            </div>
          )}

          {/* 当前完整URL预览 */}
          <div className="bg-gray-50 p-3 rounded-md">
            <p className="text-xs text-gray-600 mb-1">完整API地址预览:</p>
            <p className="text-sm font-mono text-gray-800 break-all">
              {config.baseUrl || 'http://localhost:8081'}/api/{config.apiVersion}
            </p>
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
          <button
            onClick={handleReset}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            🔄 重置默认
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving || !config.baseUrl.trim()}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </>
              ) : (
                '💾 保存配置'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 