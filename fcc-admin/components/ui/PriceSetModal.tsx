'use client'

import React, { useState, useEffect } from 'react'
import CustomInput from './CustomInput'
import { FCCV2APIClient, PriceSyncStatus } from '@/lib/api-client-v2'

interface PriceSetModalProps {
  isOpen: boolean
  onClose: () => void
  deviceId: string
  nozzleNumber: number
  nozzleName: string
  currentPrice: number
  onSuccess: () => void
}

export default function PriceSetModal({
  isOpen,
  onClose,
  deviceId,
  nozzleNumber,
  nozzleName,
  currentPrice,
  onSuccess
}: PriceSetModalProps) {
  const [newPrice, setNewPrice] = useState('')
  const [priceMode, setPriceMode] = useState<'sync' | 'async'>('async')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [syncStatus, setSyncStatus] = useState<PriceSyncStatus | null>(null)
  const [isCheckingStatus, setIsCheckingStatus] = useState(false)

  const apiClient = new FCCV2APIClient()

  useEffect(() => {
    if (isOpen) {
      setNewPrice(currentPrice.toFixed(0))
      setError('')
      setSuccess('')
      // 检查当前价格同步状态
      checkPriceSyncStatus()
    }
  }, [isOpen, currentPrice])

  const checkPriceSyncStatus = async () => {
    try {
      setIsCheckingStatus(true)
      const status = await apiClient.getPriceSyncStatus(deviceId)
      setSyncStatus(status)
    } catch (error) {
      console.warn('Failed to get price sync status:', error)
    } finally {
      setIsCheckingStatus(false)
    }
  }

  const handlePriceChange = (value: string) => {
    setNewPrice(value)
    setError('')
  }

  const validatePrice = (price: string): boolean => {
    const priceNum = parseFloat(price)
    if (isNaN(priceNum)) {
      setError('请输入有效的价格')
      return false
    }
    if (priceNum <= 0) {
      setError('价格必须大于0')
      return false
    }
    if (priceNum > 999999) {
      setError('价格不能超过999999')
      return false
    }
    if (priceNum < 1) {
      setError('价格不能小于1')
      return false
    }
    return true
  }

  const handleSubmit = async () => {
    if (!validatePrice(newPrice)) {
      return
    }

    const priceNum = parseFloat(newPrice)
    setIsSubmitting(true)
    setError('')
    setSuccess('')

    try {
      if (priceMode === 'async') {
        // 异步调价
        const response = await apiClient.setNozzleTargetPrice(deviceId, nozzleNumber, priceNum)
        setSuccess('目标价格设置成功！系统将自动同步价格到设备。')
        
        // 刷新同步状态
        setTimeout(() => {
          checkPriceSyncStatus()
        }, 1000)
      } else {
        // 同步调价（原有方式）
        const response = await apiClient.wayneUpdatePrices(deviceId, [{
          nozzle_number: nozzleNumber,
          price: priceNum.toString(),
          decimals: 0
        }])
        setSuccess('价格设置成功！')
      }

      onSuccess()
      
      // 2秒后自动关闭
      setTimeout(() => {
        onClose()
      }, 2000)
    } catch (error) {
      console.error('Failed to set price:', error)
      setError(error instanceof Error ? error.message : '设置价格失败')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    setNewPrice('')
    setError('')
    setSuccess('')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">设置喷嘴价格</h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ✕
          </button>
        </div>

        {/* 喷嘴信息 */}
        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <div className="text-sm text-gray-600">
            <div>设备: {deviceId}</div>
            <div>喷嘴: #{nozzleNumber} - {nozzleName}</div>
            <div>当前价格: <span className="font-semibold">Rp {currentPrice.toFixed(0)}/L</span></div>
          </div>
        </div>

        {/* 价格同步状态 */}
        {syncStatus && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div className="text-sm text-blue-800">
              <div className="font-medium mb-1">当前同步状态</div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div>已同步: {syncStatus.synced}</div>
                <div>待同步: {syncStatus.pending}</div>
                <div>发送中: {syncStatus.sending}</div>
                <div>失败: {syncStatus.failed}</div>
                <div>超时: {syncStatus.timeout}</div>
                <div>总计: {syncStatus.total}</div>
              </div>
            </div>
          </div>
        )}

        {/* 调价模式选择 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">调价模式</label>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => setPriceMode('async')}
              className={`p-2 rounded-lg border text-sm ${
                priceMode === 'async'
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium">异步调价</div>
              <div className="text-xs text-gray-500">立即返回，后台同步</div>
            </button>
            <button
              onClick={() => setPriceMode('sync')}
              className={`p-2 rounded-lg border text-sm ${
                priceMode === 'sync'
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium">同步调价</div>
              <div className="text-xs text-gray-500">等待设备响应</div>
            </button>
          </div>
        </div>

        {/* 价格输入 */}
        <div className="mb-4">
          <CustomInput
            type="decimal"
            label="新价格"
            value={newPrice}
            onChange={handlePriceChange}
            placeholder="输入新价格"
            min={1}
            max={999999}
            decimals={0}
            step={1}
            suffix="Rupiah/升"
            required
            autoFocus
            error={error}
            onEnter={handleSubmit}
            onEscape={handleCancel}
          />
        </div>

        {/* 价格对比 */}
        {newPrice && !error && parseFloat(newPrice) !== currentPrice && (
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <div className="text-sm">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">当前价格:</span>
                <span className="font-medium">Rp {currentPrice.toFixed(0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">新价格:</span>
                <span className="font-medium text-blue-600">Rp {parseFloat(newPrice).toFixed(0)}</span>
              </div>
              <div className="flex justify-between items-center border-t border-gray-200 pt-1 mt-1">
                <span className="text-gray-600">价格变化:</span>
                <span className={`font-medium ${
                  parseFloat(newPrice) > currentPrice ? 'text-red-600' : 'text-green-600'
                }`}>
                  {parseFloat(newPrice) > currentPrice ? '+' : ''}
                  Rp {(parseFloat(newPrice) - currentPrice).toFixed(0)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 成功消息 */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
            <div className="text-green-800 text-sm">{success}</div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-3">
          <button
            onClick={handleCancel}
            disabled={isSubmitting}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !newPrice || !!error}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>设置中...</span>
              </div>
            ) : (
              `${priceMode === 'async' ? '异步' : '同步'}设置价格`
            )}
          </button>
        </div>

        {/* 刷新状态按钮 */}
        {priceMode === 'async' && (
          <div className="mt-3 text-center">
            <button
              onClick={checkPriceSyncStatus}
              disabled={isCheckingStatus}
              className="text-blue-600 hover:text-blue-800 text-sm disabled:opacity-50"
            >
              {isCheckingStatus ? (
                <div className="flex items-center justify-center space-x-1">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                  <span>检查状态中...</span>
                </div>
              ) : (
                '🔄 刷新同步状态'
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  )
} 