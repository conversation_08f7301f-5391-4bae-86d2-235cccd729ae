'use client'

import React, { useState, useEffect } from 'react'
import { FuelGrade, FuelGradeType } from '@/lib/api-client-v2'

interface FuelGradeModalProps {
  grade: FuelGrade | null
  onClose: () => void
  onSave: (gradeData: Partial<FuelGrade>) => void
}

export function FuelGradeModal({ grade, onClose, onSave }: FuelGradeModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    type: 'gasoline' as FuelGradeType,
    description: '',
    price: '',
    unit: 'Litre',
    metadata: {}
  })

  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (grade) {
      setFormData({
        name: grade.name,
        type: grade.type,
        description: grade.description || '',
        price: grade.price.toString(),
        unit: grade.unit,
        metadata: grade.metadata || {}
      })
    } else {
      setFormData({
        name: '',
        type: 'gasoline',
        description: '',
        price: '',
        unit: 'Litre',
        metadata: {}
      })
    }
  }, [grade])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const gradeData = {
        name: formData.name,
        type: formData.type,
        description: formData.description,
        price: parseFloat(formData.price),
        unit: formData.unit,
        metadata: formData.metadata
      }

      await onSave(gradeData)
    } catch (error) {
      console.error('Save failed:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {grade ? '编辑油品' : '添加油品'}
          </h3>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* 油品名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              油品名称 *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如：BP 92"
            />
          </div>

          {/* 油品类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              油品类型 *
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleChange('type', e.target.value as FuelGradeType)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="gasoline">汽油 (Gasoline)</option>
              <option value="diesel">柴油 (Diesel)</option>
              <option value="premium">高级汽油 (Premium)</option>
              <option value="regular">普通汽油 (Regular)</option>
              <option value="kerosene">煤油 (Kerosene)</option>
              <option value="other">其他 (Other)</option>
            </select>
          </div>

          {/* 价格 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              价格 (印尼盾) *
            </label>
            <input
              type="number"
              value={formData.price}
              onChange={(e) => handleChange('price', e.target.value)}
              required
              min="0"
              step="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如：12360"
            />
          </div>

          {/* 单位 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              单位
            </label>
            <select
              value={formData.unit}
              onChange={(e) => handleChange('unit', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Litre">升 (Litre)</option>
              <option value="Gallon">加仑 (Gallon)</option>
            </select>
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="油品描述信息..."
            />
          </div>
        </form>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="submit"
            form="fuel-grade-form"
            onClick={handleSubmit}
            disabled={saving}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? '保存中...' : '保存'}
          </button>
        </div>
      </div>
    </div>
  )
} 