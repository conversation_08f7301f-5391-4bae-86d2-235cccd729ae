/**
 * API配置提供者组件
 * 提供全局API配置管理和状态同步
 */

'use client'

import { useState, useEffect, ReactNode } from 'react'
import { ApiConfigModal } from './ApiConfigModal'
import { apiConfigManager, type APIConfig } from '@/lib/api-config'

interface ApiConfigProviderProps {
  children: ReactNode
}

export function ApiConfigProvider({ children }: ApiConfigProviderProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentConfig, setCurrentConfig] = useState<APIConfig | null>(null)

  useEffect(() => {
    // 加载初始配置
    const config = apiConfigManager.getConfig()
    setCurrentConfig(config)

    // 监听配置变更
    const unsubscribe = apiConfigManager.addListener((newConfig) => {
      setCurrentConfig(newConfig)
    })

    // 添加全局快捷键 (Ctrl+Shift+A 打开API配置)
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'A') {
        event.preventDefault()
        setIsModalOpen(true)
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      unsubscribe()
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleSaveConfig = (config: APIConfig) => {
    console.log('API配置已更新:', config)
    // 这里可以添加额外的处理逻辑，如刷新页面数据等
  }

  return (
    <>
      {children}
      
      {/* 浮动配置按钮 */}
      <div className="fixed bottom-4 right-4 z-40">
        <button
          onClick={handleOpenModal}
          className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors duration-200"
          title="API配置 (Ctrl+Shift+A)"
        >
          <svg 
            className="w-5 h-5" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" 
            />
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
        </button>
      </div>

      {/* API配置状态指示器 */}
      {currentConfig && (
        <div className="fixed bottom-4 left-4 z-40">
          <div className="bg-white rounded-lg shadow-lg p-3 text-xs border">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-gray-600">
                API: {currentConfig.baseUrl}/api/{currentConfig.apiVersion}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* API配置模态框 */}
      <ApiConfigModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveConfig}
      />
    </>
  )
} 