'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { DeviceV2, DeviceStatusV2, NozzleV2, fccV2API } from '@/lib/api-client-v2'
import { useNozzles } from '@/lib/simple-state'
import { DeviceStatusBadge, DeviceHealthBadge, DeviceTypeIcon } from './DeviceStatus'
import PriceSetModal from './PriceSetModal'
import { getApiBaseUrl } from '@/lib/api-config'
import { createVolumePreauth, createAmountPreauth } from '@/lib/preauth-test'

interface NozzleItemProps {
  nozzle: NozzleV2 & { status?: string; is_out?: boolean; is_selected?: boolean }  // 扩展nozzle类型以包含状态
  deviceId: string
  onNozzleCommand: (deviceId: string, nozzleNumber: number, command: string, parameters?: Record<string, any>) => Promise<void>
}

const NozzleItem = React.memo(function NozzleItem({ nozzle, deviceId, onNozzleCommand }: NozzleItemProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showActions, setShowActions] = useState(false)
  const [showPriceModal, setShowPriceModal] = useState(false)

  const handleCommand = useCallback(async (command: string, parameters?: Record<string, any>) => {
    try {
      setIsLoading(true)
      await onNozzleCommand(deviceId, nozzle.number, command, parameters)
    } finally {
      setIsLoading(false)
    }
  }, [deviceId, nozzle.number, onNozzleCommand])

  const handlePresetVolume = useCallback(async () => {
    const volume = prompt(`Set preset volume for ${nozzle.name} (liters):`)
    const employeeId = prompt(`Enter employee ID:`, '001')
    if (volume && !isNaN(parseFloat(volume)) && employeeId) {
      console.log(`[DeviceWithNozzles] Setting preset volume: ${volume}L for nozzle ${nozzle.number}, employee: ${employeeId}`)
      await handleCommand('preset_volume', {
        nozzle_number: nozzle.number,
        volume_liters: parseFloat(volume),
        employee_id: employeeId
      })
    }
  }, [nozzle.name, nozzle.number, handleCommand])

  const handlePresetAmount = useCallback(async () => {
    const amount = prompt(`Set preset amount for ${nozzle.name} (Rupiah):`)
    const employeeId = prompt(`Enter employee ID:`, '001')
    if (amount && !isNaN(parseFloat(amount)) && employeeId) {
      await handleCommand('preset_amount', {
        nozzle_number: nozzle.number,
        amount_cents: parseFloat(amount),
        employee_id: employeeId
      })
    }
  }, [nozzle.name, nozzle.number, handleCommand])

  const handlePreauthVolume = useCallback(async () => {
    const volume = prompt(`Set preauth volume for ${nozzle.name} (liters):`)
    const employeeId = prompt(`Enter employee ID:`, '001')
    if (volume && !isNaN(parseFloat(volume)) && employeeId) {
      console.log(`[DeviceWithNozzles] Setting preauth volume: ${volume}L for nozzle ${nozzle.id}, employee: ${employeeId}`)
      try {
        const request = createVolumePreauth(deviceId, volume, {
          nozzleId: nozzle.id,
          employeeId: employeeId
        })
        const response = await fccV2API.preauth(request)
        console.log('✅ PreAuth volume success:', response)
        alert(`预授权成功！体积: ${volume}L, 过期时间: ${response.expires_in}秒`)
      } catch (error) {
        console.error('❌ PreAuth volume failed:', error)
        alert(`预授权失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
  }, [nozzle.name, nozzle.id, deviceId])

  const handlePreauthAmount = useCallback(async () => {
    const amount = prompt(`Set preauth amount for ${nozzle.name} (Rupiah):`)
    const employeeId = prompt(`Enter employee ID:`, '001')
    if (amount && !isNaN(parseFloat(amount)) && employeeId) {
      console.log(`[DeviceWithNozzles] Setting preauth amount: ${amount} for nozzle ${nozzle.id}, employee: ${employeeId}`)
      try {
        const request = createAmountPreauth(deviceId, amount, {
          nozzleId: nozzle.id,
          employeeId: employeeId
        })
        const response = await fccV2API.preauth(request)
        console.log('✅ PreAuth amount success:', response)
        alert(`预授权成功！金额: ${amount}, 过期时间: ${response.expires_in}秒`)
      } catch (error) {
        console.error('❌ PreAuth amount failed:', error)
        alert(`预授权失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
  }, [nozzle.name, nozzle.id, deviceId])

  // 获取状态颜色和文本
  const getStatusInfo = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'idle':
        return { color: 'bg-gray-400', text: 'Idle', textColor: 'text-gray-600' }
      case 'selected':
        return { color: 'bg-blue-400', text: 'Selected', textColor: 'text-blue-600' }
      case 'authorized':
        return { color: 'bg-green-400', text: 'Authorized', textColor: 'text-green-600' }
      case 'out':
        return { color: 'bg-yellow-400', text: 'Out', textColor: 'text-yellow-600' }
      case 'filling':
      case 'dispensing':
        return { color: 'bg-blue-500', text: 'Filling', textColor: 'text-blue-700' }
      case 'completed':
        return { color: 'bg-green-500', text: 'Completed', textColor: 'text-green-700' }
      case 'suspended':
        return { color: 'bg-orange-400', text: 'Suspended', textColor: 'text-orange-600' }
      case 'error':
        return { color: 'bg-red-400', text: 'Error', textColor: 'text-red-600' }
      case 'maintenance':
        return { color: 'bg-purple-400', text: 'Maintenance', textColor: 'text-purple-600' }
      default:
        return { color: 'bg-gray-300', text: 'Unknown', textColor: 'text-gray-500' }
    }
  }

  const statusInfo = getStatusInfo(nozzle.status)

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 hover:bg-gray-100 transition-colors">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-blue-600">#{nozzle.number}</span>
            <span className="text-sm font-medium">{nozzle.name}</span>
          </div>
          <div className="flex items-center space-x-2">
            {/* 启用状态指示器 */}
            <div className="flex items-center space-x-1">
              <span className={`w-2 h-2 rounded-full ${nozzle.is_enabled ? 'bg-green-400' : 'bg-gray-400'}`}></span>
              <span className="text-xs text-gray-500">{nozzle.is_enabled ? 'Enabled' : 'Disabled'}</span>
            </div>
            {/* 状态指示器 */}
            {nozzle.status && (
              <div className="flex items-center space-x-1">
                <span className={`w-2 h-2 rounded-full ${statusInfo.color}`}></span>
                <span className={`text-xs font-medium ${statusInfo.textColor}`}>{statusInfo.text}</span>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm font-semibold text-green-600">Rp {nozzle.current_price.toFixed(0)}</span>
          <button
            onClick={() => setShowActions(!showActions)}
            className="text-gray-400 hover:text-gray-600 text-sm"
            disabled={isLoading}
          >
            {showActions ? '▼' : '▶'}
          </button>
        </div>
      </div>

      {/* 额外状态信息 */}
      {(nozzle.is_out || nozzle.is_selected) && (
        <div className="mb-2 flex items-center space-x-2 text-xs">
          {nozzle.is_out && (
            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full">枪已取出</span>
          )}
          {nozzle.is_selected && (
            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full">已选中</span>
          )}
        </div>
      )}

      {showActions && (
        <div className="space-y-2 pt-2 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => setShowPriceModal(true)}
              disabled={isLoading}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              💰 Set Price
            </button>
            <button
              onClick={handlePresetVolume}
              disabled={isLoading}
              className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              ⛽ Preset Volume
            </button>
            <button
              onClick={handlePresetAmount}
              disabled={isLoading}
              className="px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              💵 Preset Amount
            </button>
            <button
              onClick={() => handleCommand(nozzle.is_enabled ? 'suspend_nozzle' : 'resume_nozzle')}
              disabled={isLoading}
              className={`px-2 py-1 text-xs text-white rounded disabled:opacity-50 ${
                nozzle.is_enabled
                  ? 'bg-orange-600 hover:bg-orange-700'
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {nozzle.is_enabled ? '⏸️ Suspend' : '▶️ Resume'}
            </button>
          </div>

          {/* PreAuth 功能区域 */}
          <div className="pt-2 border-t border-gray-300">
            <div className="text-xs font-medium text-gray-600 mb-2">🔐 预授权功能</div>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={handlePreauthVolume}
                disabled={isLoading}
                className="px-2 py-1 text-xs bg-teal-600 text-white rounded hover:bg-teal-700 disabled:opacity-50"
              >
                🔐⛽ PreAuth Volume
              </button>
              <button
                onClick={handlePreauthAmount}
                disabled={isLoading}
                className="px-2 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50"
              >
                🔐💵 PreAuth Amount
              </button>
            </div>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="flex justify-center pt-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {showPriceModal && (
        <PriceSetModal
          isOpen={showPriceModal}
          onClose={() => setShowPriceModal(false)}
          deviceId={deviceId}
          nozzleNumber={nozzle.number}
          nozzleName={nozzle.name}
          currentPrice={nozzle.current_price}
          onSuccess={() => {
            setShowPriceModal(false)
            // Optionally refresh nozzle data here
          }}
        />
      )}
    </div>
  )
}, (prevProps, nextProps) => {
  // 只有当nozzle数据真正改变时才重新渲染 - 添加状态字段比较
  return prevProps.nozzle.id === nextProps.nozzle.id &&
         prevProps.nozzle.current_price === nextProps.nozzle.current_price &&
         prevProps.nozzle.is_enabled === nextProps.nozzle.is_enabled &&
         prevProps.nozzle.status === nextProps.nozzle.status &&
         prevProps.nozzle.is_out === nextProps.nozzle.is_out &&
         prevProps.nozzle.is_selected === nextProps.nozzle.is_selected &&
         prevProps.deviceId === nextProps.deviceId
})

interface DeviceWithNozzlesCardProps {
  device: DeviceV2
  deviceStatus?: DeviceStatusV2
  onDeviceCommand?: (deviceId: string, command: string, parameters?: Record<string, any>) => Promise<void>
  onNozzleCommand?: (deviceId: string, nozzleNumber: number, command: string, parameters?: Record<string, any>) => Promise<void>
  actions?: React.ReactNode
}

export const DeviceWithNozzlesCard = React.memo(function DeviceWithNozzlesCard({ 
  device, 
  deviceStatus, 
  onDeviceCommand,
  onNozzleCommand,
  actions 
}: DeviceWithNozzlesCardProps) {
  const { getNozzles } = useNozzles()
  const [nozzles, setNozzles] = useState<(NozzleV2 & { status?: string; is_out?: boolean; is_selected?: boolean })[]>([])
  const [loadingNozzles, setLoadingNozzles] = useState(false)
  const [showNozzles, setShowNozzles] = useState(true)
  const [employeeId, setEmployeeId] = useState('001') // 添加员工卡号状态
  const lastUpdateRef = useRef<number>(0)
  const nozzleCacheRef = useRef<Map<string, { data: (NozzleV2 & { status?: string; is_out?: boolean; is_selected?: boolean })[], timestamp: number }>>(new Map())

  const currentStatus = deviceStatus?.status || device.status
  const currentHealth = device.health
  const isPumpDevice = device.type === 'fuel_pump' || device.type === 'dart_pump'

  // 使用ref来跟踪是否已经加载过，避免重复请求
  const hasLoadedRef = useRef<Set<string>>(new Set())
  const loadingStateRef = useRef<Set<string>>(new Set())

  // 智能获取喷嘴数据，严格防止重复请求
  const loadNozzles = useCallback(async (forceRefresh = false) => {
    if (!isPumpDevice) return

    const cacheKey = device.id
    
    // 如果正在加载中，直接返回
    if (loadingStateRef.current.has(cacheKey) && !forceRefresh) {
      console.log(`[DeviceWithNozzles] Already loading nozzles for device ${device.id}, skipping`)
      return
    }

    const now = Date.now()
    const cache = nozzleCacheRef.current.get(cacheKey)
    
    // 如果缓存存在且未过期（60秒内），且不是强制刷新，则使用缓存
    if (!forceRefresh && cache && (now - cache.timestamp < 60000)) {
      console.log(`[DeviceWithNozzles] Using cached nozzles for device ${device.id}`)
      setNozzles(cache.data)
      return
    }

    // 严格防抖：如果距离上次更新不到5秒，且不是强制刷新，则跳过
    if (!forceRefresh && (now - lastUpdateRef.current < 5000)) {
      console.log(`[DeviceWithNozzles] Skipping nozzle update due to debounce for device ${device.id}`)
      return
    }

    // 如果已经加载过且不是强制刷新，则跳过
    if (!forceRefresh && hasLoadedRef.current.has(cacheKey)) {
      console.log(`[DeviceWithNozzles] Already loaded nozzles for device ${device.id}, using cache`)
      if (cache) {
        setNozzles(cache.data)
      }
      return
    }

    // 标记为正在加载
    loadingStateRef.current.add(cacheKey)
    if (!forceRefresh) {
      setLoadingNozzles(true) // 只在非强制刷新时显示加载状态，避免闪烁
    }
    
    try {
      console.log(`[DeviceWithNozzles] Loading nozzles for device ${device.id}`)
      
      // 使用 getDeviceNozzles API 获取带状态的nozzle数据
      console.log(`[DeviceWithNozzles] Calling getNozzles API for device ${device.id}`)
      const nozzlesResult = await getNozzles(device.id)
      console.log(`[DeviceWithNozzles] Basic nozzles result for ${device.id}:`, nozzlesResult)
      console.log(`[DeviceWithNozzles] Basic nozzles count: ${nozzlesResult.length}`)
      nozzlesResult.forEach((nozzle, index) => {
        console.log(`[DeviceWithNozzles] Basic nozzle ${index + 1}:`, {
          id: nozzle.id,
          number: nozzle.number,
          name: nozzle.name,
          device_id: nozzle.device_id,
          is_enabled: nozzle.is_enabled
        })
      })
      
      // 尝试获取增强的nozzle状态信息（包含status等字段）
      let enhancedNozzles = nozzlesResult
      
      try {
        // 如果API支持，尝试获取更详细的状态信息
        const apiUrl = `${getApiBaseUrl()}/devices/${device.id}/nozzles`
        console.log(`[DeviceWithNozzles] Fetching detailed nozzle status from: ${apiUrl}`)
        
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        
        console.log(`[DeviceWithNozzles] Response status: ${response.status}`)
        
        if (response.ok) {
          const detailedResponse = await response.json()
          console.log(`[DeviceWithNozzles] Detailed nozzles response for ${device.id}:`, detailedResponse)
          console.log(`[DeviceWithNozzles] Detailed response type:`, typeof detailedResponse)
          console.log(`[DeviceWithNozzles] Has nozzles array:`, Array.isArray(detailedResponse.nozzles))
          
          // 如果返回的是 DeviceNozzlesResponseV2 格式，提取状态信息
          if (detailedResponse && Array.isArray(detailedResponse.nozzles)) {
            console.log(`[DeviceWithNozzles] Processing ${detailedResponse.nozzles.length} detailed nozzles`)
            
            enhancedNozzles = nozzlesResult.map(nozzle => {
              const detailedNozzle = detailedResponse.nozzles.find((n: any) => n.nozzle_number === nozzle.number)
              console.log(`[DeviceWithNozzles] Matching detailed nozzle for #${nozzle.number}:`, detailedNozzle)
              
              if (detailedNozzle) {
                // 🔧 修复：优先使用详细状态API中的正确ID，而不是基础API的ID
                const enhanced = {
                  ...nozzle,
                  id: detailedNozzle.id, // ✅ 使用详细状态API中的正确ID
                  status: detailedNozzle.status || 'idle',
                  is_out: detailedNozzle.is_out || false,
                  is_selected: detailedNozzle.is_selected || false,
                  current_price: parseFloat(detailedNozzle.current_price) || nozzle.current_price,
                  is_enabled: detailedNozzle.is_enabled !== undefined ? detailedNozzle.is_enabled : nozzle.is_enabled
                }
                console.log(`[DeviceWithNozzles] Enhanced nozzle #${nozzle.number}:`, enhanced)
                console.log(`[DeviceWithNozzles] ✅ Fixed ID: ${nozzle.id} → ${detailedNozzle.id}`)
                return enhanced
              }
              const defaultEnhanced = { ...nozzle, status: 'idle' }
              console.log(`[DeviceWithNozzles] Default enhanced nozzle #${nozzle.number}:`, defaultEnhanced)
              return defaultEnhanced
            })
            
            console.log(`[DeviceWithNozzles] Final enhanced nozzles:`, enhancedNozzles)
          }
        }
      } catch (statusError) {
        console.warn(`[DeviceWithNozzles] Failed to get detailed nozzle status for ${device.id}:`, statusError)
        // 使用基本的nozzle数据，添加默认状态
        enhancedNozzles = nozzlesResult.map(nozzle => ({ ...nozzle, status: 'idle' }))
      }
      
      console.log(`[DeviceWithNozzles] Loaded ${enhancedNozzles.length} nozzles for device ${device.id}`)
      console.log(`[DeviceWithNozzles] Final enhanced nozzles list:`, enhancedNozzles)
      
      // 检查数据是否真的有变化（静默更新）
      const hasChanges = !cache || JSON.stringify(enhancedNozzles) !== JSON.stringify(cache.data)
      
      if (hasChanges || forceRefresh) {
        // 更新缓存
        nozzleCacheRef.current.set(cacheKey, {
          data: enhancedNozzles,
          timestamp: now
        })
        
        setNozzles(enhancedNozzles)
        console.log(`[DeviceWithNozzles] Updated nozzles for device ${device.id} (${forceRefresh ? 'forced' : 'changes detected'})`)
      } else {
        console.log(`[DeviceWithNozzles] No changes detected for device ${device.id}, keeping current state`)
      }
      
      lastUpdateRef.current = now
      hasLoadedRef.current.add(cacheKey)
    } catch (error) {
      console.warn(`[DeviceWithNozzles] Failed to load nozzles for device ${device.id}:`, error)
      // 错误时保持现有状态，避免清空数据
      if (nozzles.length === 0) {
        setNozzles([])
      }
    } finally {
      setLoadingNozzles(false)
      loadingStateRef.current.delete(cacheKey)
    }
  }, [device.id, isPumpDevice, getNozzles, nozzles.length])

  // 自动轮询nozzle状态（静默更新）
  useEffect(() => {
    if (!isPumpDevice || !device.id) return

    // 设置轮询间隔（15秒，比设备轮询间隔稍长）
    const pollInterval = setInterval(() => {
      if (document.visibilityState === 'visible') { // 只在页面可见时轮询
        loadNozzles(true) // 强制刷新以获取最新状态
      }
    }, 15000)

    return () => clearInterval(pollInterval)
  }, [isPumpDevice, device.id, loadNozzles])

  // 仅在组件挂载时加载一次，不使用useEffect监听变化
  useEffect(() => {
    if (isPumpDevice) {
      loadNozzles()
    }
  }, []) // 空依赖数组，只在挂载时执行一次

  const getLastSeenText = () => {
    const lastSeen = deviceStatus?.last_seen || device.last_seen
    if (!lastSeen) return 'Never'
    
    const date = new Date(lastSeen)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return date.toLocaleDateString()
  }

  const handleDeviceCommand = useCallback(async (command: string, parameters?: Record<string, any>) => {
    if (onDeviceCommand) {
      await onDeviceCommand(device.id, command, parameters)
      // 强制刷新喷嘴数据
      if (isPumpDevice) {
        loadNozzles(true) // 强制刷新
      }
    }
  }, [device.id, isPumpDevice, onDeviceCommand, loadNozzles])

  const handleNozzleCommand = useCallback(async (deviceId: string, nozzleNumber: number, command: string, parameters?: Record<string, any>) => {
    if (onNozzleCommand) {
      await onNozzleCommand(deviceId, nozzleNumber, command, parameters)
      // 强制刷新喷嘴数据
      loadNozzles(true) // 强制刷新
    }
  }, [onNozzleCommand, loadNozzles])

  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
      {/* Device Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <DeviceTypeIcon type={device.type} />
            <div>
              <h3 className="font-semibold text-gray-900">{device.name}</h3>
              <p className="text-sm text-gray-500">
                {device.type} • 0x{device.device_address.toString(16).toUpperCase().padStart(2, '0')}
              </p>
            </div>
          </div>
          {actions}
        </div>

        <div className="flex flex-wrap gap-2 mb-3">
          <DeviceStatusBadge status={currentStatus} size="small" />
          <DeviceHealthBadge health={currentHealth} size="small" />
          {isPumpDevice && nozzles.length > 0 && (
            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 border border-blue-200 rounded-full">
              {nozzles.length} Nozzles
            </span>
          )}
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm mb-3">
          <div>
            <span className="text-gray-500">Station:</span>
            <span className="ml-1 font-medium">{device.station_id}</span>
          </div>
          <div>
            <span className="text-gray-500">Island:</span>
            <span className="ml-1 font-medium">{device.island_id || 'N/A'}</span>
          </div>
          <div>
            <span className="text-gray-500">Position:</span>
            <span className="ml-1 font-medium">{device.position || 'N/A'}</span>
          </div>
          <div>
            <span className="text-gray-500">Last Seen:</span>
            <span className="ml-1 font-medium">{getLastSeenText()}</span>
          </div>
        </div>

        {/* Device Commands */}
        {onDeviceCommand && currentStatus === 'online' && (
          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleDeviceCommand('get_status')}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                📊 Status
              </button>
              {isPumpDevice && (
                <>
                  <button
                    onClick={() => handleDeviceCommand('reset')}
                    className="px-2 py-1 text-xs bg-orange-600 text-white rounded hover:bg-orange-700"
                  >
                    🔄 Reset
                  </button>
                  <button
                    onClick={() => handleDeviceCommand('stop')}
                    className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    ⛔ Stop
                  </button>
                  {nozzles.length > 0 && (
                    <button
                      onClick={() => setShowNozzles(!showNozzles)}
                      className="px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700"
                    >
                      {showNozzles ? '▼' : '▶'} Nozzles ({nozzles.length})
                    </button>
                  )}
                </>
              )}
            </div>
            
            {/* 员工授权区域 */}
            {isPumpDevice && (
              <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded">
                <label className="text-xs font-medium text-green-700">员工卡号:</label>
                <input
                  type="text"
                  value={employeeId}
                  onChange={(e) => setEmployeeId(e.target.value)}
                  placeholder="001"
                  className="w-16 px-2 py-1 text-xs border border-green-300 rounded focus:outline-none focus:border-green-500"
                />
                <button
                  onClick={() => handleDeviceCommand('authorize', { employee_id: employeeId })}
                  className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 font-medium"
                >
                  ✅ 授权设备
                </button>
                {nozzles.length > 0 && (
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-green-700">或选择喷嘴:</span>
                    <select
                      onChange={(e) => {
                        const selectedNozzleId = e.target.value
                        if (selectedNozzleId) {
                          handleDeviceCommand('authorize', { 
                            nozzle_id: selectedNozzleId, 
                            employee_id: employeeId 
                          })
                          e.target.value = '' // 重置选择
                        }
                      }}
                      className="text-xs border border-green-300 rounded px-1 py-1"
                      defaultValue=""
                    >
                      <option value="" disabled>选择喷嘴授权</option>
                      {nozzles.map((nozzle) => (
                        <option key={nozzle.id} value={nozzle.id}>
                          #{nozzle.number} {nozzle.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Nozzles Section */}
      {isPumpDevice && showNozzles && (
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-700">Nozzles</h4>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => loadNozzles(true)}
                disabled={loadingNozzles}
                className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
                title="Refresh nozzle data"
              >
                🔄
              </button>
              {loadingNozzles && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
            </div>
          </div>
          
          {loadingNozzles ? (
            <div className="text-center py-4 text-gray-500">
              Loading nozzles...
            </div>
          ) : nozzles.length > 0 ? (
            <div className="space-y-2">
              {nozzles.map((nozzle) => (
                <NozzleItem
                  key={nozzle.id}
                  nozzle={nozzle}
                  deviceId={device.id}
                  onNozzleCommand={handleNozzleCommand}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              No nozzles found for this device
            </div>
          )}
        </div>
      )}
    </div>
  )
}, (prevProps, nextProps) => {
  // 只有当设备关键信息改变时才重新渲染
  return prevProps.device.id === nextProps.device.id &&
         prevProps.device.status === nextProps.device.status &&
         prevProps.device.health === nextProps.device.health
})

export default DeviceWithNozzlesCard