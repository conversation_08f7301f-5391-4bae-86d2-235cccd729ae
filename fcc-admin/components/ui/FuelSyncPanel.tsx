'use client'

import React from 'react'
import { FuelSyncStatusInfo } from '@/lib/api-client-v2'

interface FuelSyncPanelProps {
  syncStatus: FuelSyncStatusInfo | null
  onSync: () => void
  onRefresh: () => void
}

export function FuelSyncPanel({ syncStatus, onSync, onRefresh }: FuelSyncPanelProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800'
      case 'syncing':
        return 'bg-blue-100 text-blue-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'partial':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return '同步成功'
      case 'syncing':
        return '同步中...'
      case 'failed':
        return '同步失败'
      case 'partial':
        return '部分同步'
      case 'idle':
        return '空闲'
      default:
        return '未知状态'
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">油品同步</h3>
        <div className="flex space-x-2">
          <button
            onClick={onRefresh}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900"
          >
            🔄 刷新状态
          </button>
          <button
            onClick={onSync}
            disabled={syncStatus?.status === 'syncing'}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {syncStatus?.status === 'syncing' ? '同步中...' : '🔄 触发同步'}
          </button>
        </div>
      </div>

      {syncStatus ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 同步状态 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-gray-500">同步状态</div>
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(syncStatus.status)}`}>
                {getStatusText(syncStatus.status)}
              </span>
            </div>
          </div>

          {/* 同步统计 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500 mb-2">同步统计</div>
            <div className="text-lg font-bold text-gray-900">
              {syncStatus.synced_count}/{syncStatus.total_count}
            </div>
            <div className="text-xs text-gray-500">
              成功: {syncStatus.synced_count} | 失败: {syncStatus.failed_count}
            </div>
          </div>

          {/* 最后同步时间 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500 mb-2">最后同步</div>
            <div className="text-sm text-gray-900">
              {syncStatus.last_sync_at 
                ? new Date(syncStatus.last_sync_at).toLocaleString('zh-CN')
                : '从未同步'
              }
            </div>
            {syncStatus.sync_duration_ms && (
              <div className="text-xs text-gray-500">
                耗时: {syncStatus.sync_duration_ms}ms
              </div>
            )}
          </div>

          {/* 下次同步 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500 mb-2">下次同步</div>
            <div className="text-sm text-gray-900">
              {syncStatus.next_sync_at 
                ? new Date(syncStatus.next_sync_at).toLocaleString('zh-CN')
                : '手动触发'
              }
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <div className="text-2xl mb-2">🔄</div>
          <div>暂无同步状态信息</div>
          <div className="text-sm mt-1">点击"触发同步"开始同步</div>
        </div>
      )}

      {/* 错误信息 */}
      {syncStatus?.error_message && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <div className="text-red-400 text-sm mr-2">⚠️</div>
            <div className="text-sm text-red-800">
              <div className="font-medium">同步错误</div>
              <div className="mt-1">{syncStatus.error_message}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 