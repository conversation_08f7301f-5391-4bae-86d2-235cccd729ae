'use client'

import React, { useState } from 'react'
import { Device, DeviceStatusInfo } from '@/lib/api-client-v2'
import { DeviceCommandPanel } from './DeviceCommandPanel'

interface DeviceStatusBadgeProps {
  status: Device['status'] | string
  size?: 'small' | 'medium' | 'large'
}

export function DeviceStatusBadge({ status, size = 'medium' }: DeviceStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'online':
        return { 
          color: 'bg-green-100 text-green-800 border-green-200', 
          icon: '🟢',
          label: 'Online' 
        }
      case 'offline':
        return { 
          color: 'bg-gray-100 text-gray-800 border-gray-200', 
          icon: '⚫',
          label: 'Offline' 
        }
      case 'error':
        return { 
          color: 'bg-red-100 text-red-800 border-red-200', 
          icon: '🔴',
          label: 'Error' 
        }
      case 'maintenance':
        return { 
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
          icon: '🟡',
          label: 'Maintenance' 
        }
      default:
        return { 
          color: 'bg-gray-100 text-gray-800 border-gray-200', 
          icon: '⚪',
          label: 'Unknown' 
        }
    }
  }

  const getSizeClass = (size: string) => {
    switch (size) {
      case 'small':
        return 'px-2 py-1 text-xs'
      case 'large':
        return 'px-4 py-2 text-base'
      default:
        return 'px-3 py-1 text-sm'
    }
  }

  const config = getStatusConfig(status)

  return (
    <span className={`inline-flex items-center rounded-full border font-medium ${config.color} ${getSizeClass(size)}`}>
      <span className="mr-1">{config.icon}</span>
      {config.label}
    </span>
  )
}

interface DeviceHealthBadgeProps {
  health: Device['health'] | string
  size?: 'small' | 'medium' | 'large'
}

export function DeviceHealthBadge({ health, size = 'medium' }: DeviceHealthBadgeProps) {
  const getHealthConfig = (health: string) => {
    switch (health) {
      case 'healthy':
        return { 
          color: 'bg-green-100 text-green-800 border-green-200', 
          icon: '💚',
          label: 'Healthy' 
        }
      case 'good':
        return { 
          color: 'bg-blue-100 text-blue-800 border-blue-200', 
          icon: '💙',
          label: 'Good' 
        }
      case 'warning':
        return { 
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
          icon: '💛',
          label: 'Warning' 
        }
      case 'critical':
        return { 
          color: 'bg-red-100 text-red-800 border-red-200', 
          icon: '💔',
          label: 'Critical' 
        }
      case 'error':
        return { 
          color: 'bg-red-100 text-red-800 border-red-200', 
          icon: '❤️‍🩹',
          label: 'Error' 
        }
      default:
        return { 
          color: 'bg-gray-100 text-gray-800 border-gray-200', 
          icon: '🤍',
          label: 'Unknown' 
        }
    }
  }

  const getSizeClass = (size: string) => {
    switch (size) {
      case 'small':
        return 'px-2 py-1 text-xs'
      case 'large':
        return 'px-4 py-2 text-base'
      default:
        return 'px-3 py-1 text-sm'
    }
  }

  const config = getHealthConfig(health)

  return (
    <span className={`inline-flex items-center rounded-full border font-medium ${config.color} ${getSizeClass(size)}`}>
      <span className="mr-1">{config.icon}</span>
      {config.label}
    </span>
  )
}

interface DeviceTypeIconProps {
  type: Device['type'] | string
  size?: 'small' | 'medium' | 'large'
}

export function DeviceTypeIcon({ type, size = 'medium' }: DeviceTypeIconProps) {
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'fuel_pump':
      case 'dart_pump':
        return { icon: '⛽', label: 'Fuel Pump' }
      case 'fuel_nozzle':
        return { icon: '🔧', label: 'Fuel Nozzle' }
      case 'atg':
      case 'dart_tank':
        return { icon: '🛢️', label: 'ATG Tank' }
      case 'display':
      case 'dart_display':
        return { icon: '📺', label: 'Display' }
      case 'pos':
        return { icon: '💳', label: 'POS Terminal' }
      default:
        return { icon: '📱', label: 'Device' }
    }
  }

  const getSizeClass = (size: string) => {
    switch (size) {
      case 'small':
        return 'text-lg'
      case 'large':
        return 'text-4xl'
      default:
        return 'text-2xl'
    }
  }

  const config = getTypeConfig(type)

  return (
    <div className="flex items-center space-x-2">
      <span className={getSizeClass(size)} title={config.label}>
        {config.icon}
      </span>
    </div>
  )
}

interface DeviceCardProps {
  device: Device
  deviceStatus?: DeviceStatusInfo
  onCommand?: (deviceId: string, command: string, parameters?: Record<string, any>) => Promise<void>
  actions?: React.ReactNode
}

export function DeviceCard({ device, deviceStatus, onCommand, actions }: DeviceCardProps) {
  const [showCommandPanel, setShowCommandPanel] = useState(false)
  const currentStatus = deviceStatus?.status || device.status
  const currentHealth = device.health

  const getLastSeenText = () => {
    const lastSeen = deviceStatus?.last_seen || device.last_seen
    if (!lastSeen) return 'Never'
    
    const date = new Date(lastSeen)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <DeviceTypeIcon type={device.type} />
          <div>
            <h3 className="font-semibold text-gray-900">{device.name}</h3>
            <p className="text-sm text-gray-500">
              {device.type} • 0x{device.device_address.toString(16).toUpperCase().padStart(2, '0')}
            </p>
          </div>
        </div>
        {actions}
      </div>

      <div className="flex flex-wrap gap-2 mb-3">
        <DeviceStatusBadge status={currentStatus} size="small" />
        <DeviceHealthBadge health={currentHealth} size="small" />
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm mb-3">
        <div>
          <span className="text-gray-500">Station:</span>
          <span className="ml-1 font-medium">{device.station_id}</span>
        </div>
        <div>
          <span className="text-gray-500">Island:</span>
          <span className="ml-1 font-medium">{device.island_id || 'N/A'}</span>
        </div>
        <div>
          <span className="text-gray-500">Position:</span>
          <span className="ml-1 font-medium">{device.position || 'N/A'}</span>
        </div>
        <div>
          <span className="text-gray-500">Last Seen:</span>
          <span className="ml-1 font-medium">{getLastSeenText()}</span>
        </div>
      </div>

      {onCommand && currentStatus === 'online' && (
        <div className="border-t pt-3">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => onCommand(device.id, 'status_query')}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              📊 Status
            </button>
            {(device.type === 'fuel_pump' || device.type === 'dart_pump') && (
              <>
                <button
                  onClick={() => onCommand(device.id, 'authorize')}
                  className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                >
                  ✅ Authorize
                </button>
                <button
                  onClick={() => onCommand(device.id, 'reset')}
                  className="px-2 py-1 text-xs bg-orange-600 text-white rounded hover:bg-orange-700"
                >
                  🔄 Reset
                </button>
                <button
                  onClick={() => onCommand(device.id, 'stop')}
                  className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                >
                  ⛔ Stop
                </button>
              </>
            )}
            <button
              onClick={() => setShowCommandPanel(true)}
              className="px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 font-medium"
            >
              ⚙️ Command Center
            </button>
          </div>
        </div>
      )}

      {/* Command Panel Modal */}
      <DeviceCommandPanel
        device={device}
        isOpen={showCommandPanel}
        onClose={() => setShowCommandPanel(false)}
        onCommand={onCommand || (async () => {})}
      />
    </div>
  )
}

// 设备创建表单组件
export function DeviceCreateForm({ 
  controllers, 
  onSubmit, 
  onCancel,
  loading = false 
}: {
  controllers: { id: string; name: string }[]
  onSubmit: (device: any) => Promise<void>
  onCancel: () => void
  loading?: boolean
}) {
  const [formData, setFormData] = React.useState({
    name: '',
    type: 'fuel_pump' as const,
    controller_id: '',
    device_address: 50,
    station_id: 'STATION-001',
    island_id: '',
    position: '',
    model: '',
    vendor: 'Wayne',
    serial_number: '',
    timeout: 5000,
    retry_count: 3,
    monitor_interval: 30000
  })

  const deviceTypes = [
    { value: 'fuel_pump', label: 'Fuel Pump' },
    { value: 'fuel_nozzle', label: 'Fuel Nozzle' },
    { value: 'atg', label: 'ATG Tank' },
    { value: 'display', label: 'Display' },
    { value: 'pos', label: 'POS Terminal' },
    { value: 'dart_pump', label: 'DART Pump' },
    { value: 'dart_tank', label: 'DART Tank' },
    { value: 'dart_display', label: 'DART Display' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.controller_id) {
      alert('Please fill in required fields: Name and Controller')
      return
    }

    const deviceData = {
      name: formData.name,
      type: formData.type,
      controller_id: formData.controller_id,
      device_address: formData.device_address,
      station_id: formData.station_id,
      island_id: formData.island_id || undefined,
      position: formData.position || undefined,
      model: formData.model || undefined,
      vendor: formData.vendor,
      serial_number: formData.serial_number || undefined,
      config: {
        timeout: formData.timeout,
        retry_count: formData.retry_count,
        monitor_interval: formData.monitor_interval,
        protocol_config: {},
        device_params: {},
        extensions: {}
      },
      capabilities: {
        supported_commands: ['status', 'authorize', 'stop', 'diagnostic'],
        supported_data_types: ['fuel_data', 'transaction_data'],
        protocol_version: '1.3',
        features: {
          transaction_support: true,
          status_monitoring: true,
          remote_authorization: true
        }
      },
      metadata: {
        created_by: 'admin',
        environment: 'production'
      }
    }

    await onSubmit(deviceData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">Create New Device</h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Device Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Pump 01"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Device Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                {deviceTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Controller and Address */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Controller *
              </label>
              <select
                value={formData.controller_id}
                onChange={(e) => setFormData({ ...formData, controller_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select Controller</option>
                {controllers.map(controller => (
                  <option key={controller.id} value={controller.id}>
                    {controller.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Device Address (DART)
              </label>
              <input
                type="number"
                min="50"
                max="111"
                value={formData.device_address}
                onChange={(e) => setFormData({ ...formData, device_address: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Location Information */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Station ID
              </label>
              <input
                type="text"
                value={formData.station_id}
                onChange={(e) => setFormData({ ...formData, station_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="STATION-001"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Island ID
              </label>
              <input
                type="text"
                value={formData.island_id}
                onChange={(e) => setFormData({ ...formData, island_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="ISLAND-01"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Position
              </label>
              <input
                type="text"
                value={formData.position}
                onChange={(e) => setFormData({ ...formData, position: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="A1, B2, etc."
              />
            </div>
          </div>

          {/* Device Details */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vendor
              </label>
              <input
                type="text"
                value={formData.vendor}
                onChange={(e) => setFormData({ ...formData, vendor: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Wayne"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Model
              </label>
              <input
                type="text"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="WP-2000"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Serial Number
              </label>
              <input
                type="text"
                value={formData.serial_number}
                onChange={(e) => setFormData({ ...formData, serial_number: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="WP123456789"
              />
            </div>
          </div>

          {/* Configuration */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Timeout (ms)
              </label>
              <input
                type="number"
                min="1000"
                max="30000"
                value={formData.timeout}
                onChange={(e) => setFormData({ ...formData, timeout: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Retry Count
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.retry_count}
                onChange={(e) => setFormData({ ...formData, retry_count: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Monitor Interval (ms)
              </label>
              <input
                type="number"
                min="5000"
                max="300000"
                value={formData.monitor_interval}
                onChange={(e) => setFormData({ ...formData, monitor_interval: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Device'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

// 控制器创建表单组件
export function ControllerCreateForm({ 
  onSubmit, 
  onCancel,
  loading = false 
}: {
  onSubmit: (controller: any) => Promise<void>
  onCancel: () => void
  loading?: boolean
}) {
  const [formData, setFormData] = React.useState({
    name: '',
    type: 'wayne_dart',
    protocol: 'wayne_dart',
    address: '',
    station_id: 'STATION-001',
    location: '',
    model: '',
    vendor: 'Wayne',
    // 连接配置
    connection_type: 'serial' as 'serial' | 'tcp',
    // 串口配置
    serial_port: 'COM1',
    baud_rate: 19200,
    data_bits: 8,
    stop_bits: 1,
    parity: 'none',
    // TCP配置  
    host: '',
    port: 502,
    // 通用配置
    timeout: 5000,
    read_timeout: 3000,
    write_timeout: 3000,
    max_retries: 3,
    // DART配置
    address_range_min: 80,
    address_range_max: 111,
    dle_enabled: true,
    crc_enabled: true,
    tx_sequence_start: 1
  })

  const controllerTypes = [
    { value: 'wayne_dart', label: 'Wayne DART' },
    { value: 'dart', label: 'Generic DART' },
    { value: 'modbus', label: 'Modbus RTU' },
    { value: 'tcp', label: 'TCP/IP' }
  ]

  const baudRates = [9600, 19200, 38400, 57600, 115200]
  const parityOptions = [
    { value: 'none', label: 'None' },
    { value: 'odd', label: 'Odd' },
    { value: 'even', label: 'Even' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.station_id) {
      alert('Please fill in required fields: Name and Station ID')
      return
    }

    if (formData.connection_type === 'serial' && !formData.serial_port) {
      alert('Please specify serial port for serial connection')
      return
    }

    if (formData.connection_type === 'tcp' && !formData.host) {
      alert('Please specify host for TCP connection')
      return
    }

    const controllerData = {
      name: formData.name,
      type: formData.type,
      protocol: formData.protocol,
      address: formData.connection_type === 'tcp' ? `${formData.host}:${formData.port}` : formData.serial_port,
      station_id: formData.station_id,
      location: formData.location || undefined,
      model: formData.model || undefined,
      vendor: formData.vendor,
      config: {
        // 连接配置
        ...(formData.connection_type === 'serial' ? {
          serial_port: formData.serial_port,
          baud_rate: formData.baud_rate,
          data_bits: formData.data_bits,
          stop_bits: formData.stop_bits,
          parity: formData.parity
        } : {
          host: formData.host,
          port: formData.port
        }),
        // 通用配置
        timeout: formData.timeout,
        read_timeout: formData.read_timeout,
        write_timeout: formData.write_timeout,
        max_retries: formData.max_retries,
        // DART特定配置
        address_range: {
          min: formData.address_range_min,
          max: formData.address_range_max
        },
        dle_enabled: formData.dle_enabled,
        crc_enabled: formData.crc_enabled,
        tx_sequence_start: formData.tx_sequence_start,
        // 扩展配置
        extra_config: {
          connection_type: formData.connection_type
        }
      },
      metadata: {
        created_by: 'admin',
        environment: 'production'
      }
    }

    await onSubmit(controllerData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">Create New Controller</h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="border-b pb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Controller Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Wayne Controller 01"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Controller Type *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value, protocol: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {controllerTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Station ID *
                </label>
                <input
                  type="text"
                  value={formData.station_id}
                  onChange={(e) => setFormData({ ...formData, station_id: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="STATION-001"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Island A"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Vendor
                </label>
                <input
                  type="text"
                  value={formData.vendor}
                  onChange={(e) => setFormData({ ...formData, vendor: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Wayne"
                />
              </div>
            </div>
          </div>

          {/* Connection Configuration */}
          <div className="border-b pb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Connection Configuration</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Connection Type *
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="serial"
                    checked={formData.connection_type === 'serial'}
                    onChange={(e) => setFormData({ ...formData, connection_type: e.target.value as any })}
                    className="mr-2"
                  />
                  Serial (RS-485/RS-232)
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="tcp"
                    checked={formData.connection_type === 'tcp'}
                    onChange={(e) => setFormData({ ...formData, connection_type: e.target.value as any })}
                    className="mr-2"
                  />
                  TCP/IP Network
                </label>
              </div>
            </div>

            {formData.connection_type === 'serial' ? (
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Serial Port *
                  </label>
                  <input
                    type="text"
                    value={formData.serial_port}
                    onChange={(e) => setFormData({ ...formData, serial_port: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="COM1, /dev/ttyUSB0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Baud Rate
                  </label>
                  <select
                    value={formData.baud_rate}
                    onChange={(e) => setFormData({ ...formData, baud_rate: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {baudRates.map(rate => (
                      <option key={rate} value={rate}>{rate}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Parity
                  </label>
                  <select
                    value={formData.parity}
                    onChange={(e) => setFormData({ ...formData, parity: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {parityOptions.map(parity => (
                      <option key={parity.value} value={parity.value}>{parity.label}</option>
                    ))}
                  </select>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Host/IP Address *
                  </label>
                  <input
                    type="text"
                    value={formData.host}
                    onChange={(e) => setFormData({ ...formData, host: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="*************"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Port
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="65535"
                    value={formData.port}
                    onChange={(e) => setFormData({ ...formData, port: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Protocol Configuration */}
          <div className="border-b pb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Protocol Configuration</h3>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Timeout (ms)
                </label>
                <input
                  type="number"
                  min="1000"
                  max="30000"
                  value={formData.timeout}
                  onChange={(e) => setFormData({ ...formData, timeout: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Retries
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.max_retries}
                  onChange={(e) => setFormData({ ...formData, max_retries: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Read Timeout (ms)
                </label>
                <input
                  type="number"
                  min="500"
                  max="10000"
                  value={formData.read_timeout}
                  onChange={(e) => setFormData({ ...formData, read_timeout: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* DART Specific Configuration */}
            {(formData.type === 'wayne_dart' || formData.type === 'dart') && (
              <div className="mt-4">
                <h4 className="text-md font-medium text-gray-800 mb-3">DART Protocol Settings</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address Range Min
                    </label>
                    <input
                      type="number"
                      min="80"
                      max="111"
                      value={formData.address_range_min}
                      onChange={(e) => setFormData({ ...formData, address_range_min: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address Range Max
                    </label>
                    <input
                      type="number"
                      min="80"
                      max="111"
                      value={formData.address_range_max}
                      onChange={(e) => setFormData({ ...formData, address_range_max: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                
                <div className="flex space-x-6 mt-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.dle_enabled}
                      onChange={(e) => setFormData({ ...formData, dle_enabled: e.target.checked })}
                      className="mr-2"
                    />
                    Enable DLE Encoding
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.crc_enabled}
                      onChange={(e) => setFormData({ ...formData, crc_enabled: e.target.checked })}
                      className="mr-2"
                    />
                    Enable CRC Checksum
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Controller'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 