# FCC Admin 使用示例

## 快速开始

### 1. 启动项目
```bash
cd fcc-admin
npm install
npm run dev
```

### 2. 配置API服务器

#### 方法一：使用界面配置
1. 打开浏览器访问 `http://localhost:8083`
2. 点击右下角的齿轮图标 🔧
3. 输入API服务器地址，例如：`http://*************:8081`
4. 点击"测试连接"验证
5. 点击"保存配置"

#### 方法二：使用快捷键
1. 按下 `Ctrl+Shift+A`
2. 按照上述步骤配置

### 3. 验证配置
- 左下角会显示当前API地址
- 绿色圆点表示连接正常

## 常见配置示例

### 本地开发环境
```
API服务器地址: http://localhost:8081
API版本: v2
连接超时: 10000ms
```

### 测试环境
```
API服务器地址: http://*************:8081
API版本: v2
连接超时: 15000ms
```

### 生产环境
```
API服务器地址: http://fcc-api.company.com:8081
API版本: v2
连接超时: 10000ms
```

## 编程方式配置

如果需要在代码中动态配置：

```typescript
import { updateApiConfig, getApiConfig } from '@/lib/api-config'

// 更新配置
updateApiConfig({
  baseUrl: 'http://*************:8081',
  apiVersion: 'v2',
  timeout: 10000
})

// 获取当前配置
const config = getApiConfig()
console.log('当前API地址:', config.baseUrl)
```

## 注意事项

1. **地址格式**: 只需输入服务器地址和端口，系统会自动添加`/api/v2`
2. **协议**: 如果不输入`http://`，系统会自动添加
3. **持久化**: 配置会保存在浏览器中，下次访问时自动加载
4. **测试**: 建议每次配置后都进行连接测试

## 故障排除

### 连接失败
- 检查服务器地址是否正确
- 确认服务器是否运行
- 检查网络连接

### 数据无法加载
- 确认API配置是否正确
- 检查服务器是否支持CORS
- 查看浏览器控制台错误信息 