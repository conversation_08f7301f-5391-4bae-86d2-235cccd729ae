# FCC Service v2 后端集成指南

## 📋 后端路由配置

### 当前路由结构

```go
// internal/server/routes.go
func (r *Router) SetupRoutes(e *echo.Echo) {
    // 健康检查路由
    e.GET("/health", r.healthHandler)
    e.GET("/ready", r.readyHandler)

    // API v1 路由组 (原有接口)
    v1 := e.Group("/api/v1")
    
    // 现有设备相关路由
    devices := v1.Group("/devices")
    devices.GET("", deviceHandler.ListDevices)
    devices.POST("", deviceHandler.CreateDevice)
    devices.GET("/:id", deviceHandler.GetDevice)
    devices.PUT("/:id", deviceHandler.UpdateDevice)
    devices.DELETE("/:id", deviceHandler.DeleteDevice)
    devices.GET("/:id/status", deviceHandler.GetDeviceStatus)
    devices.POST("/:id/commands", deviceHandler.SendCommand)
}
```

### 建议的v2集成方案

```go
// internal/server/routes.go - 更新版本
package server

import (
    "net/http"
    "github.com/labstack/echo/v4"
    "go.uber.org/zap"
    
    "fcc-service/internal/server/handlers"
    handlersV2 "fcc-service/internal/server/handlers/v2" // v2 handlers
    "fcc-service/internal/services/device"
    v2 "fcc-service/internal/services/polling/v2"
    v2models "fcc-service/pkg/models/v2"
)

type Router struct {
    logger         *zap.Logger
    deviceService  *device.Manager
    // v2 组件
    dispatchTask   *v2.DispatchTask
    stateManager   v2models.DeviceStateManager
}

func NewRouter(
    logger *zap.Logger, 
    deviceService *device.Manager,
    dispatchTask *v2.DispatchTask,
    stateManager v2models.DeviceStateManager,
) *Router {
    return &Router{
        logger:         logger,
        deviceService:  deviceService,
        dispatchTask:   dispatchTask,
        stateManager:   stateManager,
    }
}

func (r *Router) SetupRoutes(e *echo.Echo) {
    // 健康检查路由
    e.GET("/health", r.healthHandler)
    e.GET("/ready", r.readyHandler)

    // 创建处理器实例
    deviceHandler := handlers.NewDeviceEchoHandler(r.deviceService, r.logger)
    controllerHandler := handlers.NewControllerEchoHandler(r.deviceService, r.logger)
    
    // v2处理器实例
    handlerV2 := handlersV2.NewHandlerV2(r.dispatchTask, r.stateManager, r.logger)

    // API v1 路由组 (保持现有接口不变)
    v1 := e.Group("/api/v1")
    
    // 设备相关路由 - 支持两种方式
    devices := v1.Group("/devices")
    
    // 方案1: 根据环境变量选择处理器
    useV2 := os.Getenv("FCC_USE_V2_HANDLERS") == "true"
    
    if useV2 {
        // 使用v2处理器
        devices.GET("", handlerV2.ListDevices)
        devices.POST("", handlerV2.CreateDevice)
        devices.GET("/:id", handlerV2.GetDevice)
        devices.PUT("/:id", handlerV2.UpdateDevice)
        devices.DELETE("/:id", handlerV2.DeleteDevice)
        devices.GET("/:id/status", handlerV2.GetDeviceStatus)
        devices.POST("/:id/commands", handlerV2.ExecuteCommand)
        
        // v2新增的系统管理接口
        system := v1.Group("/system")
        system.GET("/status", handlerV2.GetSystemStatus)
        system.GET("/metrics", handlerV2.GetSystemMetrics)
        
        // v2增强的命令管理接口
        commands := v1.Group("/commands")
        commands.GET("/:id", handlerV2.GetCommand)
        commands.GET("", handlerV2.ListCommands)
    } else {
        // 使用原有处理器
        devices.GET("", deviceHandler.ListDevices)
        devices.POST("", deviceHandler.CreateDevice)
        devices.GET("/:id", deviceHandler.GetDevice)
        devices.PUT("/:id", deviceHandler.UpdateDevice)
        devices.DELETE("/:id", deviceHandler.DeleteDevice)
        devices.GET("/:id/status", deviceHandler.GetDeviceStatus)
        devices.POST("/:id/commands", deviceHandler.SendCommand)
    }

    // 控制器相关路由 (保持不变)
    controllers := v1.Group("/controllers")
    controllers.GET("", controllerHandler.ListControllers)
    controllers.POST("", controllerHandler.CreateController)
    controllers.GET("/:id", controllerHandler.GetController)
    controllers.PUT("/:id", controllerHandler.UpdateController)
    controllers.DELETE("/:id", controllerHandler.DeleteController)
    controllers.GET("/:id/status", controllerHandler.GetControllerStatus)
    controllers.POST("/:id/devices", controllerHandler.AddDevice)
    controllers.DELETE("/:id/devices/:deviceId", controllerHandler.RemoveDevice)

    // Wayne设备相关路由 (保持不变)
    wayne := v1.Group("/wayne")
    wayne.GET("/devices", deviceHandler.ListWayneDevices)
    wayne.GET("/devices/:id", deviceHandler.GetWayneDevice)
    wayne.POST("/devices/:id/pump-status", deviceHandler.GetPumpStatus)
    wayne.POST("/devices/:id/pump-totals", deviceHandler.GetPumpTotals)
}
```

## 🔧 服务启动配置

### main.go 更新示例

```go
// main.go 或 cmd/fcc-server/main.go
package main

import (
    "log"
    "os"
    
    "fcc-service/internal/core"
    "fcc-service/internal/server"
    v2 "fcc-service/internal/services/polling/v2"
    v2models "fcc-service/pkg/models/v2"
)

func main() {
    // 初始化核心组件
    container, err := core.NewContainer()
    if err != nil {
        log.Fatal("Failed to initialize container:", err)
    }

    // 初始化v2组件
    var dispatchTask *v2.DispatchTask
    var stateManager v2models.DeviceStateManager
    
    useV2 := os.Getenv("FCC_USE_V2_HANDLERS") == "true"
    if useV2 {
        // 创建v2状态管理器
        stateManager = v2models.NewStateManager()
        
        // 创建v2调度任务
        config := v2.DispatchTaskConfig{
            Name:                "wayne_dispatch_task",
            MaxDevices:          32,
            DefaultPollInterval: 1 * time.Second,
            DefaultPollTimeout:  25 * time.Millisecond,
            WatchdogTimeout:     10 * time.Second,
            ResultBufferSize:    1000,
            EnableMetrics:       true,
        }
        
        dispatchTask = v2.NewDispatchTask(config, stateManager, container.Logger)
        
        // 启动v2调度任务
        if err := dispatchTask.Start(); err != nil {
            log.Fatal("Failed to start dispatch task:", err)
        }
    }

    // 创建路由器
    router := server.NewRouter(
        container.Logger,
        container.DeviceService,
        dispatchTask,
        stateManager,
    )

    // 启动服务器
    e := echo.New()
    router.SetupRoutes(e)
    
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }
    
    log.Printf("Starting FCC service on port %s (v2: %v)", port, useV2)
    log.Fatal(e.Start(":" + port))
}
```

## 🌍 环境变量配置

```bash
# .env 或环境变量
FCC_USE_V2_HANDLERS=true  # 启用v2处理器
FCC_V2_MAX_DEVICES=32     # v2最大设备数
FCC_V2_POLL_INTERVAL=1s   # v2默认轮询间隔
FCC_V2_WATCHDOG_TIMEOUT=10s # v2看门狗超时
```

## 📊 部署策略

### 1. 蓝绿部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  fcc-service-v1:
    build: .
    environment:
      - FCC_USE_V2_HANDLERS=false
    ports:
      - "8080:8080"
    
  fcc-service-v2:
    build: .
    environment:
      - FCC_USE_V2_HANDLERS=true
    ports:
      - "8081:8080"
    
  nginx:
    image: nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### 2. 渐进式切换

```bash
# 脚本示例
#!/bin/bash
# switch-to-v2.sh

echo "Switching to v2 handlers..."
export FCC_USE_V2_HANDLERS=true

# 重启服务
systemctl restart fcc-service

echo "Service switched to v2"
```

## 🔍 监控和日志

### v2特定的监控指标

```go
// 在v2处理器中添加指标收集
func (h *HandlerV2) GetSystemMetrics(c echo.Context) error {
    metrics := h.dispatchTask.GetSystemMetrics()
    
    // 记录指标到日志
    h.logger.Info("v2 system metrics",
        zap.Int64("total_devices", metrics.TotalDevices),
        zap.Int64("active_devices", metrics.ActiveDevices),
        zap.Int64("total_polls", metrics.TotalPolls),
        zap.Float64("success_rate", float64(metrics.SuccessfulPolls)/float64(metrics.TotalPolls)*100),
    )
    
    return c.JSON(http.StatusOK, convertToResponse(metrics))
}
```

## ⚠️ 注意事项

### 兼容性检查清单

- [ ] 确保v2处理器已正确初始化
- [ ] 验证环境变量配置
- [ ] 测试所有API端点的响应格式
- [ ] 检查日志输出是否正常
- [ ] 验证设备地址范围限制 (0x50-0x6F)
- [ ] 测试系统监控接口
- [ ] 验证命令执行的business_type字段

### 回滚计划

如果v2出现问题，快速回滚到v1：

```bash
# 回滚脚本
export FCC_USE_V2_HANDLERS=false
systemctl restart fcc-service
echo "Rolled back to v1 handlers"
``` 