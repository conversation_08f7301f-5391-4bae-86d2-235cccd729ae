# FCC管理后台 - 简化MVP版本

一个基于 Next.js 的FCC设备管理系统，专注于核心功能的MVP实现。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发环境启动
```bash
npm run dev
```

浏览器访问 http://localhost:8083

### 构建生产版本
```bash
npm run build
npm start
```

## 📱 功能特性

- **系统概览**: 控制器和设备状态统计
- **控制器管理**: Wayne DART控制器监控和配置
- **设备管理**: 泵、ATG、显示屏等设备的状态监控和控制
- **命令执行**: 简单的设备命令操作界面

## 🏗️ 技术栈

- **Next.js 14**: React全栈框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **React Hooks**: 状态管理

## 📁 项目结构

```
fcc-admin/
├── app/                    # Next.js App Router
│   ├── dashboard/         # 仪表板页面
│   ├── controllers/       # 控制器管理页面  
│   ├── devices/           # 设备管理页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 共享组件(预留)
├── lib/                   # 工具函数(预留)
└── styles/               # 样式文件(预留)
```

## 🔧 配置

### 环境变量
创建 `.env.local` 文件：

```bash
# FCC服务API地址
NEXT_PUBLIC_FCC_API=http://localhost:8080/api/v1
```

### Next.js配置
`next.config.js` 已配置API代理，将 `/api/fcc/*` 代理到FCC服务。

## 🎯 MVP特点

1. **单一应用**: 不使用复杂的monorepo结构
2. **最少依赖**: 只使用必要的npm包
3. **核心功能**: 专注于设备管理的基本需求
4. **简单UI**: 使用Tailwind CSS快速构建界面
5. **模拟数据**: 内置模拟数据，便于开发和演示

## 📋 页面说明

### 概览页面 (`/dashboard`)
- 显示控制器和设备的统计信息
- 提供快捷操作入口

### 控制器页面 (`/controllers`)
- 显示Wayne DART控制器列表
- 支持发现设备和配置操作

### 设备页面 (`/devices`)
- 显示所有设备的状态
- 支持对泵设备进行授权和停止操作
- 支持ATG设备的查询操作
- 支持显示屏的更新操作

## 🔗 与FCC服务集成

该前端应用通过HTTP API与FCC服务通信：

- 控制器接口: `/api/v1/controllers`
- 设备接口: `/api/v1/devices`  
- 命令接口: `/api/v1/devices/{id}/commands`

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t fcc-admin .

# 运行容器
docker run -p 8083:8083 fcc-admin
```

### 手动部署
```bash
npm run build
npm start
```

## 🛠️ 开发说明

- 当前使用模拟数据，实际部署时需要连接真实的FCC服务API
- 可以通过修改 `NEXT_PUBLIC_FCC_API` 环境变量来指定FCC服务地址
- 所有页面都使用客户端渲染，便于状态管理

## 📝 TODO

- [ ] 集成真实的FCC API
- [ ] 添加实时WebSocket连接
- [ ] 增加错误处理和重试机制
- [ ] 添加用户认证
- [ ] 优化移动端体验

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。 