# FCC Service v2 前端迁移指南

## 📋 概述

FCC Service 已升级到 v2 架构，提供了更强大的设备管理和实时监控功能。本指南将帮助您将前端代码从旧版本迁移到 v2 版本。

## 🔄 兼容性总结

### ✅ 完全兼容（无需修改）
- 设备列表查询 `GET /api/v1/devices`
- 设备详情查询 `GET /api/v1/devices/{id}`
- 设备删除 `DELETE /api/v1/devices/{id}`
- 基本命令执行接口

### ⚠️ 需要修改（字段变化）
- 设备创建 `POST /api/v1/devices` - 增加了 v2 专用字段
- 设备更新 `PUT /api/v1/devices/{id}` - 地址字段类型变化
- 设备状态查询 - 响应字段大幅增加

### ➕ 新增功能
- 系统状态监控 `GET /api/v1/system/status`
- 系统指标 `GET /api/v1/system/metrics`
- 增强的命令响应（包含 business_type）

## 📝 迁移步骤

### 步骤 1: 更新类型定义

API 客户端已更新，包含了 v2 版本的类型定义：

```typescript
// 新的 v2 设备类型
import { DeviceV2, DeviceCreateRequestV2, DeviceStatusV2, apiV2 } from '@/lib/api-client'

// v2 设备具有更丰富的状态信息
interface DeviceV2 {
  id: string
  name: string
  type: string
  address: number          // 注意：类型从 int 改为 byte，但 TS 中仍为 number
  controller_id: string
  station_id: string
  state: string           // 新增：设备状态
  is_online: boolean      // 新增：在线状态
  pump_status: number     // 新增：泵状态
  last_poll: string       // 新增：最后轮询时间
  last_response: string   // 新增：最后响应时间
  stats?: PollerStats     // 新增：统计信息
  // ... 其他字段
}
```

### 步骤 2: 设备创建表单更新

**旧版本：**
```typescript
// 旧版本的设备创建
const createDevice = async (deviceData: Partial<Device>) => {
  return await api.devices.create({
    id: deviceData.id,
    name: deviceData.name,
    type: deviceData.type,
    device_address: deviceData.device_address, // 旧字段名
    controller_id: deviceData.controller_id,
    station_id: deviceData.station_id,
  })
}
```

**v2 版本：**
```typescript
// v2 版本的设备创建 - 支持更多配置选项
const createDeviceV2 = async (deviceData: DeviceCreateRequestV2) => {
  return await apiV2.devices.create({
    id: deviceData.id,
    name: deviceData.name,
    type: deviceData.type,
    address: deviceData.address,          // 新字段名，必须在 0x50-0x6F 范围
    controller_id: deviceData.controller_id,
    station_id: deviceData.station_id,
    // v2 新增的可选字段
    poll_interval: 1000,                  // 轮询间隔（毫秒）
    max_retries: 3,                       // 最大重试次数
    watchdog_timeout: 10000,              // 看门狗超时（毫秒）
    buffer_size: 100,                     // 缓冲区大小
  })
}
```

### 步骤 3: 设备状态监控更新

**旧版本：**
```typescript
// 旧版本 - 基础状态信息
const [deviceStatus, setDeviceStatus] = useState<DeviceStatusInfo>()

useEffect(() => {
  const fetchStatus = async () => {
    const status = await api.devices.getStatus(deviceId)
    setDeviceStatus(status)
  }
  fetchStatus()
}, [deviceId])

// 渲染基础状态
<div>
  <p>状态: {deviceStatus?.status}</p>
  <p>健康: {deviceStatus?.health}</p>
</div>
```

**v2 版本：**
```typescript
// v2 版本 - 丰富的状态信息
const [deviceStatusV2, setDeviceStatusV2] = useState<DeviceStatusV2>()

useEffect(() => {
  const fetchStatusV2 = async () => {
    const status = await apiV2.devices.getStatus(deviceId)
    setDeviceStatusV2(status)
  }
  fetchStatusV2()
}, [deviceId])

// 渲染详细状态信息
<div className="device-status-v2">
  <div className="status-grid">
    <div>状态: {deviceStatusV2?.state}</div>
    <div>在线: {deviceStatusV2?.is_online ? '是' : '否'}</div>
    <div>泵状态: {deviceStatusV2?.pump_status}</div>
    <div>轮询次数: {deviceStatusV2?.poll_count}</div>
    <div>错误次数: {deviceStatusV2?.error_count}</div>
    <div>响应时间: {deviceStatusV2?.response_time / 1000000}ms</div>
  </div>
  
  {/* 统计信息 */}
  {deviceStatusV2?.stats && (
    <div className="stats-section">
      <h4>统计信息</h4>
      <p>成功率: {(deviceStatusV2.stats.successful_polls / deviceStatusV2.stats.total_polls * 100).toFixed(2)}%</p>
      <p>平均响应时间: {deviceStatusV2.stats.average_response_time / 1000000}ms</p>
    </div>
  )}
</div>
```

### 步骤 4: 命令执行增强

**旧版本：**
```typescript
// 旧版本命令执行
const executeCommand = async (deviceId: string, commandType: string) => {
  const result = await api.commands.execute(deviceId, {
    command_type: commandType,
    parameters: { /* 参数 */ }
  })
  
  console.log('命令结果:', result.success)
}
```

**v2 版本：**
```typescript
// v2 版本命令执行 - 增加了业务类型
const executeCommandV2 = async (deviceId: string, commandType: string) => {
  const result = await apiV2.commands.execute(deviceId, {
    command_type: commandType,
    parameters: { /* 参数 */ },
    priority: 'high',  // 新增：优先级控制
    timeout: 30,       // 新增：超时控制
    async: false       // 新增：异步执行选项
  })
  
  console.log('命令结果:', result.success)
  console.log('业务类型:', result.business_type)  // v2 新增字段
}
```

### 步骤 5: 新增系统监控功能

```typescript
// v2 新增：系统状态监控
const SystemDashboard = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatusV2>()
  const [systemMetrics, setSystemMetrics] = useState<SystemMetricsV2>()

  useEffect(() => {
    const fetchSystemInfo = async () => {
      try {
        const [status, metrics] = await Promise.all([
          apiV2.system.status(),
          apiV2.system.metrics()
        ])
        setSystemStatus(status)
        setSystemMetrics(metrics)
      } catch (error) {
        console.error('Failed to fetch system info:', error)
      }
    }
    
    fetchSystemInfo()
    const interval = setInterval(fetchSystemInfo, 5000) // 每5秒更新
    
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="system-dashboard">
      <div className="status-cards">
        <div className="card">
          <h3>系统状态</h3>
          <p>运行状态: {systemStatus?.is_running ? '运行中' : '已停止'}</p>
          <p>设备总数: {systemStatus?.total_devices}</p>
          <p>活跃设备: {systemStatus?.active_devices}</p>
        </div>
        
        <div className="card">
          <h3>系统指标</h3>
          <p>总轮询次数: {systemMetrics?.total_polls}</p>
          <p>成功率: {systemMetrics && (systemMetrics.successful_polls / systemMetrics.total_polls * 100).toFixed(2)}%</p>
          <p>平均延迟: {systemMetrics && (systemMetrics.average_latency / 1000000).toFixed(2)}ms</p>
        </div>
      </div>
    </div>
  )
}
```

## 🔧 实用组件示例

### v2 设备表单组件

```typescript
import { useState } from 'react'
import { DeviceCreateRequestV2, apiV2 } from '@/lib/api-client'

const DeviceFormV2 = ({ onSuccess }: { onSuccess: () => void }) => {
  const [formData, setFormData] = useState<DeviceCreateRequestV2>({
    id: '',
    name: '',
    type: 'dart_pump',
    address: 0x50,
    controller_id: '',
    station_id: '',
    poll_interval: 1000,
    max_retries: 3,
    watchdog_timeout: 10000,
    buffer_size: 100,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await apiV2.devices.create(formData)
      onSuccess()
    } catch (error) {
      console.error('创建设备失败:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="device-form-v2">
      <div className="form-group">
        <label>设备地址 (0x50-0x6F)</label>
        <input
          type="number"
          min={0x50}
          max={0x6F}
          value={formData.address}
          onChange={(e) => setFormData({...formData, address: parseInt(e.target.value)})}
        />
      </div>
      
      <div className="form-group">
        <label>轮询间隔 (毫秒)</label>
        <input
          type="number"
          min={100}
          value={formData.poll_interval}
          onChange={(e) => setFormData({...formData, poll_interval: parseInt(e.target.value)})}
        />
      </div>
      
      <div className="form-group">
        <label>看门狗超时 (毫秒)</label>
        <input
          type="number"
          min={1000}
          value={formData.watchdog_timeout}
          onChange={(e) => setFormData({...formData, watchdog_timeout: parseInt(e.target.value)})}
        />
      </div>
      
      {/* 其他字段... */}
      
      <button type="submit">创建设备</button>
    </form>
  )
}
```

## 🚀 迁移策略建议

### 渐进式迁移

1. **阶段 1**: 保持现有功能正常运行
   - 继续使用现有的 `api` 对象
   - 逐步添加 v2 功能到新页面

2. **阶段 2**: 增强现有功能
   - 将设备状态监控升级到 v2 版本
   - 添加系统监控仪表板

3. **阶段 3**: 完全迁移
   - 将设备创建/更新迁移到 v2 版本
   - 利用 v2 的高级功能

### 兼容性代码示例

```typescript
// 创建一个包装器，支持两个版本
const useDeviceAPI = (useV2: boolean = false) => {
  if (useV2) {
    return {
      devices: apiV2.devices,
      system: apiV2.system,
    }
  } else {
    return {
      devices: api.devices,
      system: api.system,
    }
  }
}

// 在组件中使用
const DeviceList = () => {
  const USE_V2 = process.env.NEXT_PUBLIC_USE_V2 === 'true'
  const deviceAPI = useDeviceAPI(USE_V2)
  
  // 统一的API调用方式
  const fetchDevices = () => deviceAPI.devices.list()
}
```

## ❗ 注意事项

### 重要变更
1. **地址字段**: `device_address` → `address`，类型保持 number
2. **地址范围**: 必须在 0x50-0x6F (80-111) 范围内
3. **新增字段**: v2 设备创建需要更多配置参数
4. **响应格式**: v2 响应包含更多状态和统计信息

### 最佳实践
1. 始终验证设备地址范围
2. 使用 TypeScript 类型检查确保类型安全
3. 利用 v2 的实时状态信息改善用户体验
4. 监控系统指标以优化性能

### 故障排除
- 如果遇到地址验证错误，确保地址在 0x50-0x6F 范围内
- v2 API 错误响应格式可能不同，注意错误处理
- 新增字段可能需要后端支持，确保 API 版本匹配

## 📚 相关资源

- [v2 Architecture Design](../docs/New_Architecture_v2_Design.md)
- [Wayne DART Protocol](../docs/FCC_Wayne_DART协议支持开发任务.md)
- [API Documentation](./DEVICE_COMMANDS_USAGE.md) 