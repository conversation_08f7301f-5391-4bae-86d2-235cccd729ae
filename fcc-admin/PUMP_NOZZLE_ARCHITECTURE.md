# Pump vs Nozzle 层级架构设计

## 概述

FCC Admin 现在正确区分了 **Pump 层级** 和 **Nozzle 层级** 的操作，这更符合 Wayne DART 协议的实际工作方式和加油站的业务逻辑。

## 架构层级

```
加油站
├── 设备/泵 (Pump Level)
│   ├── 设备级命令 (Device Commands)
│   └── 喷嘴 (Nozzle Level)
│       ├── 喷嘴 1
│       ├── 喷嘴 2
│       ├── ...
│       └── 喷嘴 N
```

## Pump 层级操作

### 设备级命令 (影响整个设备)
- **设备状态查询** (`status_query`) - 查询整个设备的状态
- **设备授权** (`authorize`) - 授权整个设备开始操作
- **设备重置** (`reset`) - 重置整个设备到初始状态  
- **紧急停止** (`stop`) - 紧急停止整个设备
- **总计数器** (`request_total_counters`) - 获取设备总计数器
- **设备信息** (`request_filling_info`) - 获取设备填充信息

### 特点
- 影响整个泵设备
- 不需要指定具体喷嘴
- 通常用于设备初始化、故障处理、系统管理

## Nozzle 层级操作

### 喷嘴级命令 (影响单个喷嘴)
- **价格设置** (`set_price`) - 设置指定喷嘴的燃油价格
- **体积预设** (`preset_volume`) - 为指定喷嘴预设加油体积
- **金额预设** (`preset_amount`) - 为指定喷嘴预设加油金额
- **暂停喷嘴** (`suspend_nozzle`) - 暂停指定喷嘴
- **恢复喷嘴** (`resume_nozzle`) - 恢复指定喷嘴

### 特点
- 需要先选择目标喷嘴
- 只影响选定的喷嘴
- 用于具体的业务操作和客户服务

## API 接口设计

### 新增的 Nozzle API
```typescript
// 获取设备所有喷嘴
GET /api/v2/devices/:id/nozzles -> NozzleV2[]

// 获取单个喷嘴
GET /api/v2/devices/:id/nozzles/:number -> NozzleV2

// 获取喷嘴交易信息  
GET /api/v2/devices/:id/nozzles/:number/transaction -> NozzleTransactionV2

// 获取喷嘴统计信息
GET /api/v2/devices/:id/nozzles/:number/stats -> NozzleStatsV2
```

### 前端 API 客户端
```typescript
// 使用新的 API
const nozzles = await apiV2.nozzles.getDeviceNozzles(deviceId)
const nozzle = await apiV2.nozzles.getDeviceNozzle(deviceId, nozzleNumber)
const transaction = await apiV2.nozzles.getNozzleTransaction(deviceId, nozzleNumber)
const stats = await apiV2.nozzles.getNozzleStats(deviceId, nozzleNumber)
```

## UI 界面设计

### 1. 设备命令中心 (DeviceCommandPanel)
- **标签页切换**: "🔧 Pump Commands" 和 "⛽ Nozzle Commands"
- **Pump 标签**: 显示设备级命令，无需选择喷嘴
- **Nozzle 标签**: 先选择喷嘴，再选择喷嘴级命令

### 2. 喷嘴选择界面
显示设备的所有喷嘴：
- 喷嘴号 (1-8)
- 喷嘴名称/位置
- 当前价格
- 启用状态

### 3. 命令执行流程
1. 选择命令层级 (Pump/Nozzle)
2. 如果是 Nozzle 命令，选择目标喷嘴
3. 选择具体命令
4. 输入命令参数（如果需要）
5. 确认执行

## 数据类型定义

### NozzleV2 接口
```typescript
interface NozzleV2 {
  id: string
  number: number               // 1-8 (Wayne 协议限制)
  name: string
  device_id: string
  fuel_grade_id: string
  current_price: number
  position: string
  is_enabled: boolean
  created_at: string
  updated_at: string
}
```

### NozzleStatusResponse 接口
```typescript
interface NozzleStatusResponse {
  id: string                           // 喷嘴唯一标识符
  nozzle_number: number                // 喷嘴编号 (1-15)
  status: 'idle' | 'selected' | 'authorized' | 'out' | 'filling' | 'completed' | 'suspended' | 'error' | 'maintenance'
  is_out: boolean                      // 油枪是否拔出
  is_selected: boolean                 // 喷嘴是否被选中
  is_enabled: boolean                  // 是否启用
  current_price: number                // 当前单价
  current_volume: number               // 当前体积
  current_amount: number               // 当前金额
  total_volume: number                 // 累计体积
  total_amount: number                 // 累计金额
  transaction_count: number            // 交易次数
  fuel_grade_name?: string             // 油品名称
  preset_volume?: number               // 预设体积
  preset_amount?: number               // 预设金额
  last_update: string                  // 最后更新时间
}
```

### DeviceNozzlesResponse 接口
```typescript
interface DeviceNozzlesResponse {
  device_id: string                    // 设备ID
  device_name: string                  // 设备名称
  total_nozzles: number                // 总喷嘴数
  active_count: number                 // 活跃喷嘴数
  nozzles: NozzleStatusResponse[]      // 喷嘴列表
  last_update: string                  // 最后更新时间
}
```

### NozzleTransactionResponse 接口
```typescript
interface NozzleTransactionResponse {
  id: string                           // 喷嘴唯一标识符
  nozzle_number: number                // 喷嘴编号
  transaction_id?: string              // 交易ID
  status: 'idle' | 'selected' | 'authorized' | 'out' | 'filling' | 'completed' | 'suspended' | 'error' | 'maintenance'
  start_time?: string                  // 开始时间
  current_volume: number               // 当前体积
  current_amount: number               // 当前金额
  unit_price: number                   // 单价
  preset_volume?: number               // 预设体积
  preset_amount?: number               // 预设金额
  duration?: string                    // 交易持续时间
  is_active: boolean                   // 是否活跃交易
}
```

### NozzleStatsResponse 接口
```typescript
interface NozzleStatsResponse {
  id: string                           // 喷嘴唯一标识符
  nozzle_number: number                // 喷嘴编号
  total_volume: number                 // 累计体积
  total_amount: number                 // 累计金额
  transaction_count: number            // 交易次数
  average_volume: number               // 平均体积
  average_amount: number               // 平均金额
  last_transaction?: string            // 最后交易时间
  utilization: number                  // 使用率 (0-1)
}
```

## Wayne DART 协议映射

### Pump 层级命令对应的协议
- `authorize` → Wayne CD1 (授权命令)
- `reset` → Wayne CD0 (重置命令)  
- `stop` → Wayne CD命令 (停止命令)
- `status_query` → Wayne DC1 (状态查询)

### Nozzle 层级命令对应的协议
- `set_price` → Wayne CD5 (价格设置) + 喷嘴号
- `preset_volume` → Wayne CD3 (体积预设) + 喷嘴号
- `preset_amount` → Wayne CD4 (金额预设) + 喷嘴号
- `suspend_nozzle` → Wayne CD14 (暂停喷嘴)
- `resume_nozzle` → Wayne CD15 (恢复喷嘴)

## 实际使用场景

### 设备管理员场景
1. **设备维护**: 使用 Pump 层级命令进行设备重置、状态检查
2. **价格管理**: 使用 Nozzle 层级命令为每个喷嘴设置不同价格
3. **故障处理**: 暂停/恢复特定喷嘴，而不影响其他喷嘴

### 客户服务场景
1. **预设服务**: 为客户在特定喷嘴预设体积或金额
2. **故障隔离**: 当某个喷嘴故障时，只暂停该喷嘴
3. **业务统计**: 查看各喷嘴的交易统计和性能数据

## 优势

1. **业务逻辑清晰**: 符合加油站实际运营模式
2. **操作精确**: 能够精确控制单个喷嘴，避免误操作
3. **协议符合**: 正确映射 Wayne DART 协议的命令结构
4. **扩展性好**: 便于添加更多喷嘴级功能和统计
5. **用户体验**: 界面层级清晰，操作流程符合直觉

## 下一步规划

1. **喷嘴状态面板**: 创建专门的喷嘴状态监控界面
2. **批量操作**: 支持同时操作多个喷嘴
3. **喷嘴配置**: 支持喷嘴的详细配置管理
4. **实时监控**: 实时显示各喷嘴的状态和交易进度
5. **报告分析**: 基于喷嘴级数据生成更详细的业务报告 