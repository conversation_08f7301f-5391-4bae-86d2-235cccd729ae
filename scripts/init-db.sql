-- FCC Service 开发环境数据库初始化脚本
-- 创建必要的表和索引

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Wayne设备表
CREATE TABLE IF NOT EXISTS wayne_devices (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(50) UNIQUE NOT NULL,
    address INTEGER NOT NULL CHECK (address >= 80 AND address <= 111), -- 0x50-0x6F
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'error', 'maintenance')),
    firmware_version VARCHAR(20),
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Wayne事务表
CREATE TABLE IF NOT EXISTS wayne_transactions (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(10) NOT NULL CHECK (transaction_type IN ('CD', 'DC')),
    transaction_number INTEGER NOT NULL CHECK (transaction_number >= 1 AND transaction_number <= 101),
    command_code INTEGER,
    data BYTEA,
    response BYTEA,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'completed', 'failed', 'timeout')),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES wayne_devices(device_id) ON DELETE CASCADE
);

-- 设备状态表
CREATE TABLE IF NOT EXISTS device_status (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    pump_state INTEGER DEFAULT 0,
    nozzle_state INTEGER DEFAULT 0,
    volume DECIMAL(10,3) DEFAULT 0 CHECK (volume >= 0),
    amount DECIMAL(10,2) DEFAULT 0 CHECK (amount >= 0),
    price DECIMAL(6,3) DEFAULT 0 CHECK (price >= 0),
    preset_volume DECIMAL(10,3),
    preset_amount DECIMAL(10,2),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES wayne_devices(device_id) ON DELETE CASCADE
);

-- 泵状态表
CREATE TABLE IF NOT EXISTS pump_states (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    state VARCHAR(30) NOT NULL,
    previous_state VARCHAR(30),
    transition_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB,
    FOREIGN KEY (device_id) REFERENCES wayne_devices(device_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_wayne_devices_address ON wayne_devices(address);
CREATE INDEX IF NOT EXISTS idx_wayne_devices_status ON wayne_devices(status);
CREATE INDEX IF NOT EXISTS idx_wayne_devices_last_seen ON wayne_devices(last_seen);

CREATE INDEX IF NOT EXISTS idx_wayne_transactions_device_id ON wayne_transactions(device_id);
CREATE INDEX IF NOT EXISTS idx_wayne_transactions_type ON wayne_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_wayne_transactions_status ON wayne_transactions(status);
CREATE INDEX IF NOT EXISTS idx_wayne_transactions_created_at ON wayne_transactions(created_at);

CREATE INDEX IF NOT EXISTS idx_device_status_device_id ON device_status(device_id);
CREATE INDEX IF NOT EXISTS idx_device_status_updated_at ON device_status(updated_at);

CREATE INDEX IF NOT EXISTS idx_pump_states_device_id ON pump_states(device_id);
CREATE INDEX IF NOT EXISTS idx_pump_states_state ON pump_states(state);
CREATE INDEX IF NOT EXISTS idx_pump_states_transition_time ON pump_states(transition_time);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
DROP TRIGGER IF EXISTS update_wayne_devices_updated_at ON wayne_devices;
CREATE TRIGGER update_wayne_devices_updated_at
    BEFORE UPDATE ON wayne_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_device_status_updated_at ON device_status;
CREATE TRIGGER update_device_status_updated_at
    BEFORE UPDATE ON device_status
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据
INSERT INTO wayne_devices (device_id, address, status, firmware_version) VALUES
    ('pump001', 80, 'offline', 'v2.1.0'),
    ('pump002', 81, 'offline', 'v2.1.0'),
    ('pump003', 82, 'offline', 'v2.1.0')
ON CONFLICT (device_id) DO NOTHING;

-- 插入初始设备状态
INSERT INTO device_status (device_id, pump_state, nozzle_state, volume, amount, price) VALUES
    ('pump001', 0, 0, 0.000, 0.00, 1.259),
    ('pump002', 0, 0, 0.000, 0.00, 1.329),
    ('pump003', 0, 0, 0.000, 0.00, 1.199)
ON CONFLICT DO NOTHING;

-- 插入初始泵状态
INSERT INTO pump_states (device_id, state, previous_state, metadata) VALUES
    ('pump001', 'PUMP_NOT_PROGRAMMED', NULL, '{"initialized": true}'),
    ('pump002', 'PUMP_NOT_PROGRAMMED', NULL, '{"initialized": true}'),
    ('pump003', 'PUMP_NOT_PROGRAMMED', NULL, '{"initialized": true}')
ON CONFLICT DO NOTHING; 