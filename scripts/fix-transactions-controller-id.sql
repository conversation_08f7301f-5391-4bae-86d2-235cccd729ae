-- 修复 transactions 表缺失的 controller_id 字段
-- 此脚本将为现有的 transactions 表添加 controller_id 字段，并更新现有数据

BEGIN;

-- 检查 controller_id 字段是否已存在
DO $$
BEGIN
    -- 如果字段不存在，则添加它
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' 
        AND column_name = 'controller_id'
    ) THEN
        -- 添加 controller_id 字段（暂时允许 NULL）
        ALTER TABLE transactions 
        ADD COLUMN controller_id VARCHAR(255);
        
        RAISE NOTICE 'Added controller_id column to transactions table';
        
        -- 更新现有记录的 controller_id
        -- 基于 device_id 从 devices 表中获取对应的 controller_id
        UPDATE transactions 
        SET controller_id = d.controller_id
        FROM devices d
        WHERE transactions.device_id = d.id
        AND transactions.controller_id IS NULL;
        
        RAISE NOTICE 'Updated existing transactions with controller_id from devices table';
        
        -- 为那些没有找到对应设备的记录设置默认值
        UPDATE transactions 
        SET controller_id = CASE 
            WHEN device_id LIKE '%com7%' THEN 'controller_com7'
            WHEN device_id LIKE '%com8%' THEN 'controller_com8'
            WHEN device_id LIKE '%wayne%' THEN 'controller_wayne_dart_001'
            ELSE 'controller_unknown'
        END
        WHERE controller_id IS NULL;
        
        RAISE NOTICE 'Set default controller_id for transactions without matching devices';
        
        -- 现在将字段设为 NOT NULL（如果所有记录都有值）
        ALTER TABLE transactions 
        ALTER COLUMN controller_id SET NOT NULL;
        
        RAISE NOTICE 'Set controller_id column to NOT NULL';
        
        -- 添加外键约束（如果 controllers 表存在）
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'controllers') THEN
            -- 首先检查是否所有 controller_id 都在 controllers 表中存在
            INSERT INTO controllers (id, name, type, protocol, address, station_id, status, health, created_at, updated_at)
            SELECT DISTINCT 
                t.controller_id,
                'Controller ' || t.controller_id,
                'dart_line_controller',
                'dart',
                CASE 
                    WHEN t.controller_id LIKE '%com7%' THEN 'COM7'
                    WHEN t.controller_id LIKE '%com8%' THEN 'COM8'
                    ELSE 'UNKNOWN'
                END,
                'station_001',
                'online',
                'healthy',
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP
            FROM transactions t
            WHERE NOT EXISTS (
                SELECT 1 FROM controllers c WHERE c.id = t.controller_id
            )
            ON CONFLICT (id) DO NOTHING;
            
            RAISE NOTICE 'Created missing controller records';
            
            -- 添加外键约束
            ALTER TABLE transactions 
            ADD CONSTRAINT fk_transactions_controller 
            FOREIGN KEY (controller_id) REFERENCES controllers(id);
            
            RAISE NOTICE 'Added foreign key constraint for controller_id';
        END IF;
        
        -- 添加索引
        CREATE INDEX IF NOT EXISTS idx_transactions_controller_id ON transactions(controller_id);
        
        RAISE NOTICE 'Added index on controller_id';
        
    ELSE
        RAISE NOTICE 'controller_id column already exists in transactions table';
    END IF;
END $$;

-- 验证修复结果
DO $$
DECLARE
    total_transactions INTEGER;
    transactions_with_controller INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_transactions FROM transactions;
    SELECT COUNT(*) INTO transactions_with_controller FROM transactions WHERE controller_id IS NOT NULL;
    
    RAISE NOTICE 'Verification Results:';
    RAISE NOTICE '  Total transactions: %', total_transactions;
    RAISE NOTICE '  Transactions with controller_id: %', transactions_with_controller;
    
    IF total_transactions = transactions_with_controller THEN
        RAISE NOTICE '  ✅ All transactions have controller_id assigned';
    ELSE
        RAISE WARNING '  ⚠️  Some transactions still missing controller_id';
    END IF;
END $$;

COMMIT;

-- 显示更新后的表结构
\d transactions;

-- 显示更新结果示例
SELECT 
    id,
    device_id,
    controller_id,
    type,
    status,
    created_at
FROM transactions 
ORDER BY created_at DESC 
LIMIT 5; 