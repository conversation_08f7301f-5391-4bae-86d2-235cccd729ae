-- ===================================================
-- FCC Service - 修复fuel_grades表price字段溢出问题（简化版）
-- 问题：DECIMAL(8,4)无法容纳印尼盾价格(12360, 12780等)
-- 解决方案：扩展为DECIMAL(12,4)支持更大的价格值
-- 作者：FCC开发团队
-- 日期：2025-07-12
-- ===================================================

-- 开始修复
SELECT '开始修复fuel_grades表price字段溢出问题（简化版）' AS message;

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS fuel_grades_backup AS 
SELECT * FROM fuel_grades;

-- 2. 直接删除已知的视图（如果存在）
DROP VIEW IF EXISTS active_fuel_grades CASCADE;
DROP VIEW IF EXISTS nozzle_prices_cache CASCADE;
DROP VIEW IF EXISTS device_nozzle_stats CASCADE;
DROP VIEW IF EXISTS nozzle_status_overview CASCADE;

-- 3. 修改fuel_grades表的price字段
-- 从 DECIMAL(8,4) 改为 DECIMAL(12,4)
ALTER TABLE fuel_grades 
ALTER COLUMN price TYPE DECIMAL(12,4);

-- 4. 更新fuel_grades表的检查约束
ALTER TABLE fuel_grades 
DROP CONSTRAINT IF EXISTS fuel_grades_price_check;

ALTER TABLE fuel_grades 
ADD CONSTRAINT fuel_grades_price_check 
CHECK (price >= 0 AND price <= 99999999.9999);

-- 5. 修复nozzles表的current_price字段
ALTER TABLE nozzles 
ALTER COLUMN current_price TYPE DECIMAL(12,4);

ALTER TABLE nozzles 
DROP CONSTRAINT IF EXISTS nozzles_current_price_check;

ALTER TABLE nozzles 
ADD CONSTRAINT nozzles_current_price_check 
CHECK (current_price >= 0 AND current_price <= 99999999.9999);

-- 6. 重新创建标准视图
-- 创建active_fuel_grades视图
CREATE OR REPLACE VIEW active_fuel_grades AS
SELECT 
    id,
    name,
    type,
    octane,
    description,
    color,
    price,
    currency,
    metadata,
    created_at,
    updated_at,
    version
FROM fuel_grades
WHERE deleted_at IS NULL;

-- 创建nozzle_prices_cache视图
CREATE OR REPLACE VIEW nozzle_prices_cache AS
SELECT 
    n.device_id,
    n.number as nozzle_number,
    n.current_price,
    n.is_enabled,
    fg.name as fuel_grade_name,
    fg.type as fuel_type,
    n.updated_at as price_updated_at
FROM nozzles n
LEFT JOIN fuel_grades fg ON n.fuel_grade_id = fg.id
WHERE n.is_enabled = TRUE
ORDER BY n.device_id, n.number;

-- 创建device_nozzle_stats视图
CREATE OR REPLACE VIEW device_nozzle_stats AS
SELECT 
    device_id,
    COUNT(*) as total_nozzles,
    COUNT(CASE WHEN is_enabled THEN 1 END) as enabled_nozzles,
    COUNT(CASE WHEN status = 'filling' THEN 1 END) as filling_nozzles,
    COUNT(CASE WHEN is_out THEN 1 END) as out_nozzles,
    AVG(current_price) as avg_price,
    SUM(total_volume) as total_volume,
    SUM(total_amount) as total_amount,
    SUM(transaction_count) as total_transactions
FROM nozzles
GROUP BY device_id;

-- 创建nozzle_status_overview视图
CREATE OR REPLACE VIEW nozzle_status_overview AS
SELECT 
    n.id as nozzle_id,
    n.number,
    n.name as nozzle_name,
    n.status,
    n.is_out,
    n.is_selected,
    n.current_price,
    n.current_volume,
    n.current_amount,
    d.id as device_id,
    d.name as device_name,
    fg.id as fuel_grade_id,
    fg.name as fuel_grade_name,
    fg.type as fuel_type,
    n.position,
    n.is_enabled
FROM nozzles n
LEFT JOIN devices d ON n.device_id = d.id
LEFT JOIN fuel_grades fg ON n.fuel_grade_id = fg.id;

-- 7. 验证修复结果
SELECT 
    column_name,
    data_type,
    numeric_precision,
    numeric_scale,
    CASE 
        WHEN numeric_precision = 12 AND numeric_scale = 4 THEN '✅ 已修复'
        ELSE '❌ 需要修复'
    END AS status
FROM information_schema.columns 
WHERE table_name = 'fuel_grades' AND column_name = 'price';

SELECT 
    column_name,
    data_type,
    numeric_precision,
    numeric_scale,
    CASE 
        WHEN numeric_precision = 12 AND numeric_scale = 4 THEN '✅ 已修复'
        ELSE '❌ 需要修复'
    END AS status
FROM information_schema.columns 
WHERE table_name = 'nozzles' AND column_name = 'current_price';

-- 8. 测试插入印尼盾价格
DO $$
BEGIN
    -- 测试插入一个印尼盾价格
    INSERT INTO fuel_grades (
        id, name, type, price, currency, 
        created_at, updated_at
    ) VALUES (
        'test_idr_price', 'Test IDR Price', 'gasoline', 
        12360.0000, 'IDR', 
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );
    
    -- 立即删除测试数据
    DELETE FROM fuel_grades WHERE id = 'test_idr_price';
    
    RAISE NOTICE '✅ 印尼盾价格测试成功';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ 印尼盾价格测试失败: %', SQLERRM;
END $$;

-- 9. 显示当前视图状态
SELECT 
    viewname,
    CASE 
        WHEN definition LIKE '%fuel_grades%' THEN '✅ 包含fuel_grades'
        ELSE '⚪ 不包含fuel_grades'
    END AS fuel_grades_dependency
FROM pg_views 
WHERE schemaname = 'public'
AND viewname IN ('active_fuel_grades', 'nozzle_prices_cache', 'device_nozzle_stats', 'nozzle_status_overview')
ORDER BY viewname;

-- 完成修复
SELECT '✅ fuel_grades表price字段修复完成（简化版）' AS message;
SELECT '📋 现在支持最大价格：99,999,999.9999' AS info;
SELECT '🇮🇩 印尼盾价格范围：0 - 99,999,999' AS idr_support;
SELECT '🔄 标准视图已重新创建' AS view_status; 