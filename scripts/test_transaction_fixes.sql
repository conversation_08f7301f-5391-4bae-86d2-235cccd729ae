-- ===================================================
-- FCC Service 交易修复验证测试脚本
-- 验证重复交易插入和泵码异常问题的修复效果
-- ===================================================

BEGIN;

-- 1. 检查交易表的幂等性约束
DO $$
DECLARE
    dart_constraint_exists BOOLEAN;
    business_constraint_exists BOOLEAN;
BEGIN
    -- 检查 dart_transaction_id 唯一约束
    SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'transactions' 
        AND indexname = 'uq_transactions_dart_transaction_id'
    ) INTO dart_constraint_exists;
    
    -- 检查业务唯一约束
    SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'transactions' 
        AND indexname = 'uq_transactions_business_key'
    ) INTO business_constraint_exists;
    
    RAISE NOTICE '📋 幂等性约束检查:';
    RAISE NOTICE '  - DART交易ID唯一约束: %', CASE WHEN dart_constraint_exists THEN '✅ 存在' ELSE '❌ 缺失' END;
    RAISE NOTICE '  - 业务唯一约束: %', CASE WHEN business_constraint_exists THEN '✅ 存在' ELSE '❌ 缺失' END;
END $$;

-- 2. 检查泵码相关字段
DO $$
DECLARE
    pump_fields_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO pump_fields_count
    FROM information_schema.columns 
    WHERE table_name = 'transactions' 
    AND column_name IN (
        'start_pump_volume_reading',
        'end_pump_volume_reading', 
        'start_pump_amount_reading',
        'end_pump_amount_reading',
        'pump_reading_source',
        'pump_reading_quality',
        'pump_reading_validated'
    );
    
    RAISE NOTICE '📊 泵码字段检查: % / 7 个字段存在', pump_fields_count;
    
    IF pump_fields_count = 7 THEN
        RAISE NOTICE '✅ 所有泵码字段已正确创建';
    ELSE
        RAISE NOTICE '❌ 部分泵码字段缺失，需要执行迁移脚本';
    END IF;
END $$;

-- 3. 检查员工ID字段
DO $$
DECLARE
    operator_field_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' 
        AND column_name = 'operator_id'
    ) INTO operator_field_exists;
    
    RAISE NOTICE '👤 员工ID字段检查: %', CASE WHEN operator_field_exists THEN '✅ 存在' ELSE '❌ 缺失' END;
END $$;

-- 4. 模拟重复交易插入测试
DO $$
DECLARE
    test_dart_id VARCHAR(255) := 'test-dart-tx-' || extract(epoch from now())::text;
    first_insert_success BOOLEAN := FALSE;
    second_insert_failed BOOLEAN := FALSE;
    error_message TEXT;
BEGIN
    RAISE NOTICE '🧪 开始重复交易插入测试...';
    RAISE NOTICE '测试DART交易ID: %', test_dart_id;
    
    -- 第一次插入（应该成功）
    BEGIN
        INSERT INTO transactions (
            id, dart_transaction_id, device_id, controller_id,
            type, status, actual_volume, actual_amount, unit_price,
            operator_id, pump_reading_source, pump_reading_quality,
            created_at
        ) VALUES (
            'test-tx-1-' || extract(epoch from now())::text,
            test_dart_id,
            'test-device-001',
            'test-controller-001',
            'fuel',
            'completed',
            25.50,
            200.00,
            7.84,
            'emp001',
            'DC101',
            'good',
            NOW()
        );
        first_insert_success := TRUE;
        RAISE NOTICE '✅ 第一次插入成功';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ 第一次插入失败: %', SQLERRM;
    END;
    
    -- 第二次插入相同DART交易ID（应该失败）
    BEGIN
        INSERT INTO transactions (
            id, dart_transaction_id, device_id, controller_id,
            type, status, actual_volume, actual_amount, unit_price,
            operator_id, pump_reading_source, pump_reading_quality,
            created_at
        ) VALUES (
            'test-tx-2-' || extract(epoch from now())::text,
            test_dart_id, -- 相同的DART交易ID
            'test-device-001',
            'test-controller-001',
            'fuel',
            'completed',
            25.50,
            200.00,
            7.84,
            'emp001',
            'DC101',
            'good',
            NOW()
        );
        RAISE NOTICE '❌ 第二次插入成功（不应该发生）';
    EXCEPTION WHEN unique_violation THEN
        second_insert_failed := TRUE;
        RAISE NOTICE '✅ 第二次插入被唯一约束正确阻止';
    EXCEPTION WHEN OTHERS THEN
        error_message := SQLERRM;
        RAISE NOTICE '⚠️ 第二次插入失败，但不是预期的唯一约束错误: %', error_message;
    END;
    
    -- 清理测试数据
    DELETE FROM transactions WHERE dart_transaction_id = test_dart_id;
    
    -- 测试结果
    IF first_insert_success AND second_insert_failed THEN
        RAISE NOTICE '🎉 重复交易插入防护测试通过！';
    ELSE
        RAISE NOTICE '❌ 重复交易插入防护测试失败！';
    END IF;
END $$;

-- 5. 检查现有交易的泵码数据质量
DO $$
DECLARE
    total_transactions INTEGER;
    transactions_with_pump_readings INTEGER;
    transactions_with_operator_id INTEGER;
    invalid_pump_readings INTEGER;
BEGIN
    -- 统计总交易数
    SELECT COUNT(*) INTO total_transactions FROM transactions;
    
    -- 统计有泵码数据的交易
    SELECT COUNT(*) INTO transactions_with_pump_readings 
    FROM transactions 
    WHERE start_pump_volume_reading IS NOT NULL 
    AND end_pump_volume_reading IS NOT NULL;
    
    -- 统计有员工ID的交易
    SELECT COUNT(*) INTO transactions_with_operator_id 
    FROM transactions 
    WHERE operator_id IS NOT NULL AND operator_id != '';
    
    -- 统计无效泵码数据（结束值小于开始值）
    SELECT COUNT(*) INTO invalid_pump_readings 
    FROM transactions 
    WHERE start_pump_volume_reading IS NOT NULL 
    AND end_pump_volume_reading IS NOT NULL 
    AND end_pump_volume_reading < start_pump_volume_reading;
    
    RAISE NOTICE '📈 现有交易数据质量分析:';
    RAISE NOTICE '  - 总交易数: %', total_transactions;
    RAISE NOTICE '  - 有完整泵码数据: % (%.1f%%)', 
        transactions_with_pump_readings, 
        CASE WHEN total_transactions > 0 THEN (transactions_with_pump_readings::FLOAT / total_transactions * 100) ELSE 0 END;
    RAISE NOTICE '  - 有员工ID: % (%.1f%%)', 
        transactions_with_operator_id,
        CASE WHEN total_transactions > 0 THEN (transactions_with_operator_id::FLOAT / total_transactions * 100) ELSE 0 END;
    RAISE NOTICE '  - 无效泵码数据: % (%.1f%%)', 
        invalid_pump_readings,
        CASE WHEN total_transactions > 0 THEN (invalid_pump_readings::FLOAT / total_transactions * 100) ELSE 0 END;
        
    IF invalid_pump_readings > 0 THEN
        RAISE NOTICE '⚠️ 发现 % 笔交易有无效泵码数据，需要修复', invalid_pump_readings;
    ELSE
        RAISE NOTICE '✅ 所有交易的泵码数据都是有效的';
    END IF;
END $$;

-- 6. 检查最近的交易趋势
DO $$
DECLARE
    recent_transactions INTEGER;
    recent_duplicates INTEGER;
BEGIN
    -- 检查最近24小时的交易
    SELECT COUNT(*) INTO recent_transactions 
    FROM transactions 
    WHERE created_at >= NOW() - INTERVAL '24 hours';
    
    -- 检查最近24小时可能的重复交易（相同设备、喷嘴、时间相近）
    SELECT COUNT(*) INTO recent_duplicates
    FROM (
        SELECT device_id, nozzle_id, actual_volume, actual_amount, 
               COUNT(*) as duplicate_count
        FROM transactions 
        WHERE created_at >= NOW() - INTERVAL '24 hours'
        GROUP BY device_id, nozzle_id, actual_volume, actual_amount, 
                 DATE_TRUNC('minute', created_at)
        HAVING COUNT(*) > 1
    ) duplicates;
    
    RAISE NOTICE '📅 最近24小时交易分析:';
    RAISE NOTICE '  - 总交易数: %', recent_transactions;
    RAISE NOTICE '  - 可能的重复交易组: %', recent_duplicates;
    
    IF recent_duplicates > 0 THEN
        RAISE NOTICE '⚠️ 发现可能的重复交易，建议检查应用层逻辑';
    ELSE
        RAISE NOTICE '✅ 最近无重复交易';
    END IF;
END $$;

-- 7. 生成修复建议
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔧 修复建议总结:';
    RAISE NOTICE '1. 如果幂等性约束缺失，请执行: scripts/add-transaction-idempotency-constraint.sql';
    RAISE NOTICE '2. 如果泵码字段缺失，请执行: scripts/add-pump-readings-to-transactions.sql';
    RAISE NOTICE '3. 应用层修复已完成:';
    RAISE NOTICE '   - ✅ 移除重复持久化逻辑';
    RAISE NOTICE '   - ✅ 改进员工ID缓存机制';
    RAISE NOTICE '   - ✅ 修复泵码数据一致性检查';
    RAISE NOTICE '   - ✅ 增强交易完成时的验证';
    RAISE NOTICE '4. 监控要点:';
    RAISE NOTICE '   - 观察重复交易插入错误日志';
    RAISE NOTICE '   - 检查员工ID字段填充率';
    RAISE NOTICE '   - 监控泵码数据质量';
    RAISE NOTICE '';
END $$;

COMMIT;

-- 输出完成信息
SELECT 
    '🎉 交易修复验证测试完成!' as status,
    '数据库层面: 幂等性约束 + 泵码字段' as database_fixes,
    '应用层面: 去重逻辑 + 员工ID + 泵码修复' as application_fixes,
    '建议: 部署后观察日志，确认修复效果' as recommendation,
    CURRENT_TIMESTAMP as test_completed_at; 