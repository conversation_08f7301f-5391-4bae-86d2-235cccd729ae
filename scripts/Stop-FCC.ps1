# FCC Service Stop Script for Windows
# Windows PowerShell版本的FCC服务停止脚本

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 颜色函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Blue "[INFO] $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

# 停止FCC服务
function Stop-FCCService {
    Write-Info "停止FCC服务..."
    
    $stopped = $false
    
    # 方法1: 通过PID文件停止
    if (Test-Path "fcc-server.pid") {
        try {
            $pid = Get-Content "fcc-server.pid" -ErrorAction Stop
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
            
            if ($process) {
                Write-Info "通过PID文件找到进程: $pid"
                $process | Stop-Process -Force
                Start-Sleep -Seconds 2
                
                # 验证进程是否已停止
                $checkProcess = Get-Process -Id $pid -ErrorAction SilentlyContinue
                if (-not $checkProcess) {
                    Write-Success "FCC服务已停止 (PID: $pid)"
                    $stopped = $true
                }
            } else {
                Write-Warning "PID文件存在但进程不存在"
            }
        } catch {
            Write-Warning "通过PID文件停止失败: $($_.Exception.Message)"
        }
    }
    
    # 方法2: 通过进程名停止
    if (-not $stopped) {
        try {
            $processes = Get-Process -Name "fcc-server" -ErrorAction SilentlyContinue
            if ($processes) {
                Write-Info "通过进程名找到 $($processes.Count) 个FCC服务进程"
                $processes | ForEach-Object {
                    Write-Info "停止进程 PID: $($_.Id)"
                    $_ | Stop-Process -Force
                }
                Start-Sleep -Seconds 2
                Write-Success "FCC服务已停止"
                $stopped = $true
            }
        } catch {
            Write-Warning "通过进程名停止失败: $($_.Exception.Message)"
        }
    }
    
    # 方法3: 通过端口查找并停止
    if (-not $stopped) {
        try {
            Write-Info "尝试通过端口8080查找进程..."
            $netstat = netstat -ano | Select-String ":8080.*LISTENING"
            if ($netstat) {
                $pids = $netstat | ForEach-Object {
                    ($_ -split '\s+')[-1]
                } | Sort-Object -Unique
                
                foreach ($pid in $pids) {
                    if ($pid -match '^\d+$') {
                        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                        if ($process) {
                            Write-Info "停止占用端口8080的进程 PID: $pid ($($process.ProcessName))"
                            $process | Stop-Process -Force
                            $stopped = $true
                        }
                    }
                }
            }
        } catch {
            Write-Warning "通过端口查找停止失败: $($_.Exception.Message)"
        }
    }
    
    if (-not $stopped) {
        Write-Info "未发现运行中的FCC服务"
    }
    
    return $stopped
}

# 清理临时文件
function Clear-TempFiles {
    Write-Info "清理临时文件..."
    
    try {
        # 清理PID文件
        if (Test-Path "fcc-server.pid") {
            Remove-Item "fcc-server.pid" -Force
            Write-Info "已删除PID文件"
        }
        
        # 清理日志文件（可选，注释掉保留日志）
        # if (Test-Path "fcc-server.log") {
        #     Remove-Item "fcc-server.log" -Force
        #     Write-Info "已删除日志文件"
        # }
        
        Write-Success "临时文件清理完成"
    } catch {
        Write-Warning "清理临时文件时出错: $($_.Exception.Message)"
    }
}

# 验证服务已停止
function Test-ServiceStopped {
    Write-Info "验证服务状态..."
    
    try {
        # 检查端口是否释放
        $portCheck = netstat -ano | Select-String ":8080.*LISTENING"
        if ($portCheck) {
            Write-Warning "端口8080仍被占用"
            return $false
        }
        
        # 尝试访问健康检查接口
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/health" -Method Get -TimeoutSec 3
            Write-Warning "服务仍在响应"
            return $false
        } catch {
            # 访问失败说明服务已停止
            Write-Success "服务已完全停止"
            return $true
        }
    } catch {
        Write-Warning "验证服务状态时出错: $($_.Exception.Message)"
        return $false
    }
}

# 显示进程信息
function Show-ProcessInfo {
    Write-Info "当前系统进程信息："
    
    try {
        $processes = Get-Process | Where-Object { $_.ProcessName -like "*fcc*" -or $_.ProcessName -like "*go*" }
        if ($processes) {
            $processes | Format-Table -Property Id, ProcessName, CPU, WorkingSet -AutoSize
        } else {
            Write-Info "未发现相关进程"
        }
    } catch {
        Write-Warning "获取进程信息失败: $($_.Exception.Message)"
    }
}

# 主函数
function Main {
    Write-Host "======================================" -ForegroundColor Magenta
    Write-Host "🛑 FCC Service Stop Script (Windows)" -ForegroundColor Magenta
    Write-Host "======================================" -ForegroundColor Magenta
    Write-Host ""
    
    # 停止FCC服务
    $serviceStopped = Stop-FCCService
    
    Write-Host ""
    
    # 清理临时文件
    Clear-TempFiles
    
    Write-Host ""
    
    # 验证服务已停止
    $verified = Test-ServiceStopped
    
    Write-Host ""
    
    # 显示进程信息
    Show-ProcessInfo
    
    Write-Host ""
    
    if ($serviceStopped -and $verified) {
        Write-Success "🏁 FCC服务已完全停止"
    } elseif ($serviceStopped) {
        Write-Warning "⚠️ FCC服务已停止，但验证时发现异常"
    } else {
        Write-Info "💡 未发现运行中的FCC服务"
    }
    
    Write-Host ""
    Write-Host "如需重新启动服务，请运行: .\scripts\Start-CM7.ps1" -ForegroundColor Yellow
}

# 执行主函数
Main 