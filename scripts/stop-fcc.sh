#!/bin/bash

# FCC Service Stop Script

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止FCC服务
stop_fcc_service() {
    log_info "停止FCC服务..."
    
    if [ -f "fcc-server.pid" ]; then
        PID=$(cat fcc-server.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            sleep 2
            
            # 检查是否已停止
            if kill -0 $PID 2>/dev/null; then
                log_warning "强制停止FCC服务..."
                kill -9 $PID
            fi
            
            rm -f fcc-server.pid
            log_success "FCC服务已停止"
        else
            log_warning "FCC服务进程不存在"
            rm -f fcc-server.pid
        fi
    else
        log_warning "未找到FCC服务PID文件"
        
        # 尝试通过进程名停止
        FCC_PIDS=$(pgrep -f "fcc-server" || true)
        if [ -n "$FCC_PIDS" ]; then
            log_info "发现FCC服务进程: $FCC_PIDS"
            echo "$FCC_PIDS" | xargs kill
            log_success "FCC服务已停止"
        else
            log_info "未发现运行中的FCC服务"
        fi
    fi
}

# 清理临时文件
cleanup_files() {
    log_info "清理临时文件..."
    
    # 清理日志文件（可选）
    # rm -f fcc-server.log
    
    # 清理其他临时文件
    rm -f fcc-server.pid
    
    log_success "清理完成"
}

main() {
    echo "======================================"
    echo "🛑 FCC Service Stop Script"
    echo "======================================"
    echo ""
    
    stop_fcc_service
    cleanup_files
    
    echo ""
    log_success "🏁 FCC服务已完全停止"
}

main "$@" 