-- FCC Service 通用架构表初始化脚本
-- 补充创建通用的设备和控制器表

-- 控制器表
CREATE TABLE IF NOT EXISTS controllers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address INET,
    port INTEGER,
    protocol VARCHAR(20) DEFAULT 'HTTP',
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'error', 'maintenance')),
    capabilities JSONB DEFAULT '{}',
    configuration JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 通用设备表
CREATE TABLE IF NOT EXISTS devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    controller_id UUID REFERENCES controllers(id),
    external_id VARCHAR(100), -- 映射到wayne_devices.device_id
    description TEXT,
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'error', 'maintenance')),
    capabilities JSONB DEFAULT '{}',
    configuration JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    last_seen TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_controllers_name ON controllers(name);
CREATE INDEX IF NOT EXISTS idx_controllers_type ON controllers(type);
CREATE INDEX IF NOT EXISTS idx_controllers_status ON controllers(status);
CREATE INDEX IF NOT EXISTS idx_controllers_created_at ON controllers(created_at);

CREATE INDEX IF NOT EXISTS idx_devices_name ON devices(name);
CREATE INDEX IF NOT EXISTS idx_devices_type ON devices(type);
CREATE INDEX IF NOT EXISTS idx_devices_controller_id ON devices(controller_id);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_external_id ON devices(external_id);
CREATE INDEX IF NOT EXISTS idx_devices_created_at ON devices(created_at);

-- 为通用表添加更新时间触发器
DROP TRIGGER IF EXISTS update_controllers_updated_at ON controllers;
CREATE TRIGGER update_controllers_updated_at
    BEFORE UPDATE ON controllers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_devices_updated_at ON devices;
CREATE TRIGGER update_devices_updated_at
    BEFORE UPDATE ON devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入默认控制器（Wayne DART控制器）
INSERT INTO controllers (name, type, description, protocol, status, capabilities, configuration) VALUES
    ('Wayne DART Controller', 'DART', 'Wayne DART Protocol Controller for fuel dispensers', 'SERIAL', 'online', 
     '{"protocols": ["DART"], "max_devices": 32, "baud_rates": [9600, 19200]}',
     '{"port": "/dev/ttyUSB0", "baud_rate": 9600, "timeout": 1000}')
ON CONFLICT DO NOTHING;

-- 将现有Wayne设备映射到通用设备表
INSERT INTO devices (name, type, controller_id, external_id, description, status, last_seen, capabilities, metadata)
SELECT 
    'Fuel Dispenser ' || wd.device_id,
    'FUEL_DISPENSER',
    (SELECT id FROM controllers WHERE type = 'DART' LIMIT 1),
    wd.device_id,
    'Wayne fuel dispenser with DART protocol support',
    wd.status,
    wd.last_seen,
    ('{"protocol": "DART", "address": ' || wd.address || ', "firmware": "' || COALESCE(wd.firmware_version, 'unknown') || '"}')::jsonb,
    ('{"wayne_device_id": ' || wd.id || ', "dart_address": ' || wd.address || '}')::jsonb
FROM wayne_devices wd
ON CONFLICT DO NOTHING; 