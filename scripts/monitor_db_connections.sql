-- PostgreSQL 连接监控脚本
-- 用于分析数据库连接使用情况和性能问题

-- 1. 当前活跃连接统计
SELECT 
    datname,
    usename,
    client_addr,
    client_hostname,
    application_name,
    state,
    COUNT(*) as connection_count,
    MAX(now() - backend_start) as max_connection_age,
    MAX(now() - state_change) as max_idle_time
FROM pg_stat_activity 
WHERE pid <> pg_backend_pid()
GROUP BY datname, usename, client_addr, client_hostname, application_name, state
ORDER BY connection_count DESC;

-- 2. 连接状态分布
SELECT 
    state,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM pg_stat_activity 
WHERE pid <> pg_backend_pid()
GROUP BY state
ORDER BY count DESC;

-- 3. 长时间运行的查询
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state,
    client_addr,
    application_name
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '30 seconds'
    AND state = 'active'
ORDER BY duration DESC;

-- 4. 空闲连接分析
SELECT 
    pid,
    usename,
    client_addr,
    application_name,
    state,
    now() - state_change as idle_duration,
    query
FROM pg_stat_activity 
WHERE state = 'idle' 
    AND (now() - state_change) > interval '5 minutes'
ORDER BY idle_duration DESC;

-- 5. 数据库连接限制信息
SELECT 
    setting as max_connections,
    current_setting('shared_preload_libraries') as preload_libraries
FROM pg_settings 
WHERE name = 'max_connections';

-- 6. 锁等待情况
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS blocking_statement,
    blocked_activity.application_name AS blocked_application,
    blocking_activity.application_name AS blocking_application
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;

-- 7. 频繁查询的fuel_grades分析
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query LIKE '%fuel_grades%'
ORDER BY calls DESC
LIMIT 10;

-- 8. 连接池建议
WITH connection_stats AS (
    SELECT 
        COUNT(*) as total_connections,
        COUNT(*) FILTER (WHERE state = 'active') as active_connections,
        COUNT(*) FILTER (WHERE state = 'idle') as idle_connections,
        COUNT(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
    FROM pg_stat_activity 
    WHERE pid <> pg_backend_pid()
)
SELECT 
    total_connections,
    active_connections,
    idle_connections,
    idle_in_transaction,
    CASE 
        WHEN idle_connections > active_connections * 2 THEN 'TOO_MANY_IDLE_CONNECTIONS'
        WHEN active_connections > total_connections * 0.8 THEN 'CONNECTION_POOL_TOO_SMALL'
        ELSE 'OPTIMAL'
    END as pool_status,
    CASE 
        WHEN idle_connections > active_connections * 2 THEN 'Reduce max_idle_conns'
        WHEN active_connections > total_connections * 0.8 THEN 'Increase max_open_conns'
        ELSE 'Current settings are good'
    END as recommendation
FROM connection_stats; 