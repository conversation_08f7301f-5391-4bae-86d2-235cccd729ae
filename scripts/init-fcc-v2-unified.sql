-- ===================================================
-- FCC Service V2 统一数据库初始化脚本
-- 基于代码中的 GORM 模型定义生成
-- 包含所有核心表结构、约束、索引和初始数据
-- ===================================================

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. 控制器表 (controllers) - 基于 pkg/models/device.go Controller 结构
CREATE TABLE IF NOT EXISTS controllers (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    model VARCHAR(255),
    vendor VARCHAR(255),
    protocol VARCHAR(50) NOT NULL,
    address VARCHAR(255) NOT NULL,
    config JSONB,
    station_id VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'offline',
    health VARCHAR(50) NOT NULL DEFAULT 'unknown',
    last_seen TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);

-- 控制器表索引
CREATE INDEX IF NOT EXISTS idx_controllers_station_id ON controllers(station_id);
CREATE INDEX IF NOT EXISTS idx_controllers_status ON controllers(status);
CREATE INDEX IF NOT EXISTS idx_controllers_type ON controllers(type);
CREATE INDEX IF NOT EXISTS idx_controllers_protocol ON controllers(protocol);

-- 2. 设备表 (devices) - 基于 pkg/models/device.go Device 结构
CREATE TABLE IF NOT EXISTS devices (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    model VARCHAR(255),
    vendor VARCHAR(255),
    serial_number VARCHAR(255),
    controller_id VARCHAR(255) NOT NULL REFERENCES controllers(id),
    device_address INTEGER NOT NULL,
    config JSONB,
    station_id VARCHAR(255) NOT NULL,
    island_id VARCHAR(255),
    position VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'offline',
    health VARCHAR(50) NOT NULL DEFAULT 'unknown',
    last_seen TIMESTAMP,
    capabilities JSONB,
    max_nozzles INTEGER NOT NULL DEFAULT 15,
    supported_grades JSONB,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);

-- 设备表索引
CREATE INDEX IF NOT EXISTS idx_devices_controller_id ON devices(controller_id);
CREATE INDEX IF NOT EXISTS idx_devices_station_id ON devices(station_id);
CREATE INDEX IF NOT EXISTS idx_devices_island_id ON devices(island_id);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_type ON devices(type);
CREATE UNIQUE INDEX IF NOT EXISTS idx_devices_controller_address ON devices(controller_id, device_address);

-- 3. 油品等级表 (fuel_grades) - 基于 pkg/models/nozzle.go FuelGrade 结构
CREATE TABLE IF NOT EXISTS fuel_grades (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    octane INTEGER DEFAULT 0,
    description TEXT,
    color VARCHAR(50),
    price DECIMAL(8,4) NOT NULL DEFAULT 0 CHECK (price >= 0),
    currency VARCHAR(10) DEFAULT 'CNY',
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);

-- 油品等级表索引
CREATE INDEX IF NOT EXISTS idx_fuel_grades_type ON fuel_grades(type);
CREATE INDEX IF NOT EXISTS idx_fuel_grades_octane ON fuel_grades(octane);

-- 4. 喷嘴表 (nozzles) - 基于 pkg/models/nozzle.go Nozzle 结构
CREATE TABLE IF NOT EXISTS nozzles (
    id VARCHAR(255) PRIMARY KEY,
    number SMALLINT NOT NULL CHECK (number >= 1 AND number <= 15),
    name VARCHAR(255),
    device_id VARCHAR(255) NOT NULL REFERENCES devices(id),
    status VARCHAR(50) NOT NULL DEFAULT 'idle' CHECK (status IN ('idle', 'selected', 'authorized', 'out', 'filling', 'completed', 'suspended', 'error', 'maintenance')),
    is_out BOOLEAN DEFAULT FALSE,
    is_selected BOOLEAN DEFAULT FALSE,
    fuel_grade_id VARCHAR(255) REFERENCES fuel_grades(id),
    current_price DECIMAL(8,4) DEFAULT 0 CHECK (current_price >= 0),
    current_volume DECIMAL(10,3) DEFAULT 0 CHECK (current_volume >= 0),
    current_amount DECIMAL(10,2) DEFAULT 0 CHECK (current_amount >= 0),
    preset_volume DECIMAL(10,3) CHECK (preset_volume IS NULL OR preset_volume >= 0),
    preset_amount DECIMAL(10,2) CHECK (preset_amount IS NULL OR preset_amount >= 0),
    total_volume DECIMAL(12,3) DEFAULT 0 CHECK (total_volume >= 0),
    total_amount DECIMAL(12,2) DEFAULT 0 CHECK (total_amount >= 0),
    transaction_count BIGINT DEFAULT 0,
    position VARCHAR(100),
    hose_length INTEGER DEFAULT 0,
    is_enabled BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);

-- 喷嘴表索引
CREATE INDEX IF NOT EXISTS idx_nozzles_device_id ON nozzles(device_id);
CREATE INDEX IF NOT EXISTS idx_nozzles_fuel_grade_id ON nozzles(fuel_grade_id);
CREATE INDEX IF NOT EXISTS idx_nozzles_status ON nozzles(status);
CREATE UNIQUE INDEX IF NOT EXISTS idx_nozzles_device_number ON nozzles(device_id, number);

-- 5. 交易表 (transactions) - 基于 pkg/models/command.go Transaction 结构
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(255) PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    device_id VARCHAR(255) NOT NULL REFERENCES devices(id),
    controller_id VARCHAR(255) NOT NULL REFERENCES controllers(id),
    nozzle_id VARCHAR(255) REFERENCES nozzles(id),
    fuel_type VARCHAR(100),
    unit_price DECIMAL(10,3),
    preset_amount DECIMAL(10,2),
    preset_volume DECIMAL(10,3),
    actual_amount DECIMAL(10,2),
    actual_volume DECIMAL(10,3),
    total_amount DECIMAL(10,2),
    customer_id VARCHAR(255),
    customer_card VARCHAR(255),
    vehicle_id VARCHAR(255),
    payment_method VARCHAR(100),
    payment_ref VARCHAR(255),
    paid_amount DECIMAL(10,2),
    change_amount DECIMAL(10,2),
    operator_id VARCHAR(255),
    operator_name VARCHAR(255),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    extra_data JSONB
);

-- 交易表索引
CREATE INDEX IF NOT EXISTS idx_transactions_device_id ON transactions(device_id);
CREATE INDEX IF NOT EXISTS idx_transactions_controller_id ON transactions(controller_id);
CREATE INDEX IF NOT EXISTS idx_transactions_nozzle_id ON transactions(nozzle_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_operator_id ON transactions(operator_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_completed_at ON transactions(completed_at);

-- 6. 命令表 (commands) - 基于 pkg/models/command.go Command 结构
CREATE TABLE IF NOT EXISTS commands (
    id VARCHAR(255) PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    controller_id VARCHAR(255) NOT NULL,
    parameters JSONB,
    payload JSONB,
    priority INTEGER NOT NULL DEFAULT 5,
    scheduled_at TIMESTAMP,
    timeout_secs INTEGER,
    max_retries INTEGER,
    retry_count INTEGER,
    status VARCHAR(50) NOT NULL,
    result JSONB,
    error_message TEXT,
    transaction_id VARCHAR(255),
    user_id VARCHAR(255),
    user_agent VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    
    -- 外键约束
    CONSTRAINT fk_commands_device FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    CONSTRAINT fk_commands_controller FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE CASCADE,
    CONSTRAINT fk_commands_transaction FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL
);

-- 命令表索引
CREATE INDEX IF NOT EXISTS idx_commands_device_id ON commands(device_id);
CREATE INDEX IF NOT EXISTS idx_commands_controller_id ON commands(controller_id);
CREATE INDEX IF NOT EXISTS idx_commands_transaction_id ON commands(transaction_id);
CREATE INDEX IF NOT EXISTS idx_commands_status ON commands(status);
CREATE INDEX IF NOT EXISTS idx_commands_type ON commands(type);
CREATE INDEX IF NOT EXISTS idx_commands_created_at ON commands(created_at);
CREATE INDEX IF NOT EXISTS idx_commands_scheduled_at ON commands(scheduled_at);

-- 7. 设备状态条目表 (device_status_entries) - 基于 pkg/models/command.go DeviceStatusEntry 结构
CREATE TABLE IF NOT EXISTS device_status_entries (
    id VARCHAR(255) PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    status_type VARCHAR(100) NOT NULL,
    status_data JSONB,
    timestamp TIMESTAMP NOT NULL,
    sequence_num BIGINT NOT NULL,
    data_quality VARCHAR(50),
    source VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_device_status_entries_device FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
);

-- 设备状态条目表索引
CREATE INDEX IF NOT EXISTS idx_device_status_entries_device_id ON device_status_entries(device_id);
CREATE INDEX IF NOT EXISTS idx_device_status_entries_status_type ON device_status_entries(status_type);
CREATE INDEX IF NOT EXISTS idx_device_status_entries_timestamp ON device_status_entries(timestamp);

-- ===================================================
-- 初始数据插入
-- ===================================================

-- 插入默认油品等级
INSERT INTO fuel_grades (id, name, type, octane, description, price, currency, metadata, created_at, updated_at) VALUES
('grade_92', '92号汽油', 'gasoline', 92, '普通汽油', 7.50, 'CNY', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('grade_95', '95号汽油', 'gasoline', 95, '优质汽油', 8.10, 'CNY', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('grade_98', '98号汽油', 'gasoline', 98, '高级汽油', 8.60, 'CNY', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('grade_diesel', '0号柴油', 'diesel', 0, '普通柴油', 6.80, 'CNY', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('grade_diesel_plus', '柴油-10', 'diesel', -10, '低凝点柴油', 7.20, 'CNY', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- 插入测试控制器
INSERT INTO controllers (id, name, type, model, vendor, protocol, address, station_id, status, health, config, created_at, updated_at) VALUES
('controller_wayne_dart_001', 'Wayne DART Controller #1', 'dart_line_controller', 'WM-7000', 'Wayne', 'dart', 'COM7', 'station_001', 'online', 'healthy', 
'{"serial_port": "COM7", "baud_rate": 9600, "timeout": 5000, "address_range": {"min": 80, "max": 111}, "dle_enabled": true, "crc_enabled": true}', 
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- 插入测试设备
INSERT INTO devices (id, name, type, model, vendor, controller_id, device_address, station_id, island_id, position, status, health, max_nozzles, supported_grades, config, capabilities, created_at, updated_at) VALUES
('device_wayne_pump_001', 'Wayne Pump #1', 'dart_pump', 'WM-7100', 'Wayne', 'controller_wayne_dart_001', 81, 'station_001', 'island_01', 'A区1号', 'online', 'healthy', 8, 
'["gasoline", "diesel"]',
'{"pump_config": {"nozzle_count": 8, "supported_fuels": ["gasoline", "diesel"], "max_flow_rate": 60, "precision": 3}}',
'{"supported_commands": ["CD1", "CD2", "CD3", "CD5", "CD101"], "protocol_version": "v1.3", "firmware_version": "2.1.0"}',
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- 插入测试喷嘴
INSERT INTO nozzles (id, number, name, device_id, fuel_grade_id, current_price, position, is_enabled, created_at, updated_at) VALUES
('nozzle_001_01', 1, '1号喷嘴-92汽油', 'device_wayne_pump_001', 'grade_92', 7.50, '左侧A', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_02', 2, '2号喷嘴-95汽油', 'device_wayne_pump_001', 'grade_95', 8.10, '左侧B', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_03', 3, '3号喷嘴-98汽油', 'device_wayne_pump_001', 'grade_98', 8.60, '左侧C', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_04', 4, '4号喷嘴-柴油', 'device_wayne_pump_001', 'grade_diesel', 6.80, '左侧D', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_05', 5, '5号喷嘴-92汽油', 'device_wayne_pump_001', 'grade_92', 7.50, '右侧A', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_06', 6, '6号喷嘴-95汽油', 'device_wayne_pump_001', 'grade_95', 8.10, '右侧B', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_07', 7, '7号喷嘴-98汽油', 'device_wayne_pump_001', 'grade_98', 8.60, '右侧C', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('nozzle_001_08', 8, '8号喷嘴-柴油', 'device_wayne_pump_001', 'grade_diesel', 6.80, '右侧D', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- ===================================================
-- 数据库函数和触发器
-- ===================================================

-- 更新 updated_at 字段的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表添加自动更新时间戳的触发器
DROP TRIGGER IF EXISTS trigger_controllers_updated_at ON controllers;
CREATE TRIGGER trigger_controllers_updated_at
    BEFORE UPDATE ON controllers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_devices_updated_at ON devices;
CREATE TRIGGER trigger_devices_updated_at
    BEFORE UPDATE ON devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_fuel_grades_updated_at ON fuel_grades;
CREATE TRIGGER trigger_fuel_grades_updated_at
    BEFORE UPDATE ON fuel_grades
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_nozzles_updated_at ON nozzles;
CREATE TRIGGER trigger_nozzles_updated_at
    BEFORE UPDATE ON nozzles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_transactions_updated_at ON transactions;
CREATE TRIGGER trigger_transactions_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ===================================================
-- 视图定义 (可选)
-- ===================================================

-- 设备完整信息视图
CREATE OR REPLACE VIEW device_full_info AS
SELECT 
    d.id as device_id,
    d.name as device_name,
    d.type as device_type,
    d.device_address,
    d.status as device_status,
    d.health as device_health,
    c.id as controller_id,
    c.name as controller_name,
    c.protocol,
    c.address as controller_address,
    d.station_id,
    d.island_id,
    d.position,
    COUNT(n.id) as nozzle_count,
    d.max_nozzles,
    d.created_at,
    d.updated_at
FROM devices d
LEFT JOIN controllers c ON d.controller_id = c.id
LEFT JOIN nozzles n ON d.id = n.device_id AND n.is_enabled = true
GROUP BY d.id, c.id;

-- 喷嘴状态概览视图
CREATE OR REPLACE VIEW nozzle_status_overview AS
SELECT 
    n.id as nozzle_id,
    n.number,
    n.name as nozzle_name,
    n.status,
    n.is_out,
    n.is_selected,
    n.current_price,
    n.current_volume,
    n.current_amount,
    d.id as device_id,
    d.name as device_name,
    fg.id as fuel_grade_id,
    fg.name as fuel_grade_name,
    fg.type as fuel_type,
    n.position,
    n.is_enabled
FROM nozzles n
LEFT JOIN devices d ON n.device_id = d.id
LEFT JOIN fuel_grades fg ON n.fuel_grade_id = fg.id;

-- 交易统计视图
CREATE OR REPLACE VIEW transaction_daily_stats AS
SELECT 
    DATE(created_at) as transaction_date,
    device_id,
    COUNT(*) as transaction_count,
    SUM(actual_volume) as total_volume,
    SUM(actual_amount) as total_amount,
    AVG(actual_amount) as avg_amount,
    MIN(created_at) as first_transaction,
    MAX(created_at) as last_transaction
FROM transactions 
WHERE status = 'completed'
GROUP BY DATE(created_at), device_id;

-- ===================================================
-- 权限设置
-- ===================================================

-- 为 fcc_user 用户授权（如果存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'fcc_user') THEN
        GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO fcc_user;
        GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO fcc_user;
        GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO fcc_user;
    END IF;
END
$$;

-- ===================================================
-- 完成信息
-- ===================================================
SELECT 'FCC Service V2 数据库初始化完成!' as status,
       'Tables: controllers, devices, fuel_grades, nozzles, transactions, commands, device_status_entries' as tables_created,
       CURRENT_TIMESTAMP as completed_at; 