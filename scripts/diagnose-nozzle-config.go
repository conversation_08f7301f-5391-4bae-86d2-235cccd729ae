package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"fcc-service/pkg/models"
)

// DiagnosticResult 诊断结果
type DiagnosticResult struct {
	DeviceID         string
	ExpectedNozzles  []byte
	ConfiguredNozzles []byte
	MissingNozzles   []byte
	ExtraNozzles     []byte
	Issues           []string
}

// main 主函数
func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run diagnose-nozzle-config.go <device_id>")
	}

	deviceID := os.Args[1]
	
	// 连接数据库（根据实际配置修改）
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "host=localhost user=fcc password=fcc dbname=fcc port=5432 sslmode=disable"
	}
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 执行诊断
	result, err := diagnoseNozzleConfig(db, deviceID)
	if err != nil {
		log.Fatalf("Diagnostic failed: %v", err)
	}

	// 输出结果
	printDiagnosticResult(result)
	
	// 生成修复建议
	if len(result.MissingNozzles) > 0 {
		generateFixScript(result)
	}
}

// diagnoseNozzleConfig 诊断喷嘴配置
func diagnoseNozzleConfig(db *gorm.DB, deviceID string) (*DiagnosticResult, error) {
	ctx := context.Background()
	
	result := &DiagnosticResult{
		DeviceID: deviceID,
		Issues:   []string{},
	}

	// 1. 检查设备是否存在
	var device models.Device
	if err := db.WithContext(ctx).First(&device, "id = ?", deviceID).Error; err != nil {
		return nil, fmt.Errorf("device not found: %s", deviceID)
	}

	// 2. 根据设备类型确定预期的喷嘴编号
	result.ExpectedNozzles = getExpectedNozzles(device)

	// 3. 查询已配置的喷嘴
	var nozzles []models.Nozzle
	if err := db.WithContext(ctx).Where("device_id = ?", deviceID).Find(&nozzles).Error; err != nil {
		return nil, fmt.Errorf("failed to query nozzles: %w", err)
	}

	// 4. 分析配置情况
	configuredMap := make(map[byte]bool)
	for _, nozzle := range nozzles {
		result.ConfiguredNozzles = append(result.ConfiguredNozzles, nozzle.Number)
		configuredMap[nozzle.Number] = true
	}

	// 5. 找出缺失和多余的喷嘴
	expectedMap := make(map[byte]bool)
	for _, num := range result.ExpectedNozzles {
		expectedMap[num] = true
		if !configuredMap[num] {
			result.MissingNozzles = append(result.MissingNozzles, num)
		}
	}

	for _, num := range result.ConfiguredNozzles {
		if !expectedMap[num] {
			result.ExtraNozzles = append(result.ExtraNozzles, num)
		}
	}

	// 6. 生成问题报告
	if len(result.MissingNozzles) > 0 {
		result.Issues = append(result.Issues, fmt.Sprintf("Missing nozzles: %v", result.MissingNozzles))
	}
	if len(result.ExtraNozzles) > 0 {
		result.Issues = append(result.Issues, fmt.Sprintf("Extra nozzles: %v", result.ExtraNozzles))
	}

	// 7. 检查喷嘴配置的完整性
	for _, nozzle := range nozzles {
		if !nozzle.IsEnabled {
			result.Issues = append(result.Issues, fmt.Sprintf("Nozzle %d is disabled", nozzle.Number))
		}
		if nozzle.FuelGradeID == nil {
			result.Issues = append(result.Issues, fmt.Sprintf("Nozzle %d has no fuel grade", nozzle.Number))
		}
	}

	return result, nil
}

// getExpectedNozzles 根据设备类型返回预期的喷嘴编号
func getExpectedNozzles(device models.Device) []byte {
	// 根据设备类型或配置确定预期的喷嘴编号
	// 这里简化为根据设备名称推测
	switch device.Type {
	case "wayne_dart_4_nozzle":
		return []byte{1, 2, 3, 4}
	case "wayne_dart_8_nozzle":
		return []byte{1, 2, 3, 4, 5, 6, 7, 8}
	default:
		// 默认假设是4个喷嘴
		return []byte{1, 2, 3, 4}
	}
}

// printDiagnosticResult 打印诊断结果
func printDiagnosticResult(result *DiagnosticResult) {
	fmt.Printf("=== Nozzle Configuration Diagnostic Report ===\n")
	fmt.Printf("Device ID: %s\n", result.DeviceID)
	fmt.Printf("Expected Nozzles: %v\n", result.ExpectedNozzles)
	fmt.Printf("Configured Nozzles: %v\n", result.ConfiguredNozzles)
	fmt.Printf("Missing Nozzles: %v\n", result.MissingNozzles)
	fmt.Printf("Extra Nozzles: %v\n", result.ExtraNozzles)
	fmt.Printf("\n=== Issues Found ===\n")
	
	if len(result.Issues) == 0 {
		fmt.Printf("✅ No issues found. Nozzle configuration is complete.\n")
	} else {
		for i, issue := range result.Issues {
			fmt.Printf("❌ Issue %d: %s\n", i+1, issue)
		}
	}
}

// generateFixScript 生成修复脚本
func generateFixScript(result *DiagnosticResult) {
	filename := fmt.Sprintf("fix-nozzles-%s.sql", result.DeviceID)
	
	file, err := os.Create(filename)
	if err != nil {
		log.Printf("Failed to create fix script: %v", err)
		return
	}
	defer file.Close()

	fmt.Fprintf(file, "-- Fix script for device %s\n", result.DeviceID)
	fmt.Fprintf(file, "-- Generated at: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	for _, nozzleNum := range result.MissingNozzles {
		fmt.Fprintf(file, `INSERT INTO nozzles (
    id, device_id, number, name, status, current_price, current_volume, 
    current_amount, is_enabled, is_out, is_selected, fuel_grade_id, 
    position, hose_length, created_at, updated_at
) VALUES (
    '%s-nozzle-%d',
    '%s',
    %d,
    '%d号油枪',
    'idle',
    6.33,
    0,
    0,
    true,
    false,
    false,
    'fuel_grade_92',
    'LEFT',
    5.0,
    NOW(),
    NOW()
);

`, result.DeviceID, nozzleNum, result.DeviceID, nozzleNum, nozzleNum)
	}

	fmt.Fprintf(file, "-- Verification query\n")
	fmt.Fprintf(file, "SELECT device_id, number, name, is_enabled FROM nozzles WHERE device_id = '%s' ORDER BY number;\n", result.DeviceID)

	fmt.Printf("\n✅ Fix script generated: %s\n", filename)
	fmt.Printf("Please review and execute the SQL script to fix missing nozzles.\n")
} 