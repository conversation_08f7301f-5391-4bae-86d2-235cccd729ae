#!/bin/bash

# FCC交易快速检索脚本 - 简化版本
# 
# 使用方法:
#   ./quick_trace.sh <transaction_id>     # 快速追踪交易
#   ./quick_trace.sh -l                   # 显示最近交易
#   ./quick_trace.sh -e                   # 显示错误日志
#   ./quick_trace.sh -s                   # 显示统计信息

LOG_FILE="logs/fcc-service.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 快速追踪交易
quick_trace() {
    local tx_id="$1"
    echo -e "${CYAN}🔍 快速追踪交易: $tx_id${NC}\n"

    echo -e "${GREEN}📋 交易创建:${NC}"
    grep -E "(主动创建|被动创建|HandleAuth)" "$LOG_FILE" | grep "$tx_id" | head -2

    echo -e "\n${GREEN}⛽ 加油过程:${NC}"
    grep -E "(filling|DC2.*体积)" "$LOG_FILE" | grep "$tx_id" | head -3

    echo -e "\n${GREEN}🧠 智能完成:${NC}"
    grep -E "(smart_waiting|CD1.*04H)" "$LOG_FILE" | grep "$tx_id" | head -3

    echo -e "\n${GREEN}✅ 交易完成:${NC}"
    grep -E "(completed|交易已完成)" "$LOG_FILE" | grep "$tx_id" | head -2

    echo -e "\n${YELLOW}📊 质量信息:${NC}"
    grep -E "(quality|elapsed)" "$LOG_FILE" | grep "$tx_id" | head -2

    echo -e "\n${BLUE}🔧 DC101分析:${NC}"
    analyze_dc101_issue "$tx_id"

    echo -e "\n${RED}⚠️  错误警告:${NC}"
    grep -E "(ERROR|WARN|Failed)" "$LOG_FILE" | grep "$tx_id" | head -3
}

# DC101问题分析
analyze_dc101_issue() {
    local tx_id="$1"

    echo -e "${CYAN}🔍 DC101问题分析: $tx_id${NC}"

    # 1. 检查是否发送了DC101请求
    echo -e "\n${YELLOW}1. DC101请求发送:${NC}"
    local dc101_requests=$(grep -E "(request.*counter|sendRequestCountersCommand|DC101.*request)" "$LOG_FILE" | grep "$tx_id" | wc -l)
    if [ "$dc101_requests" -gt 0 ]; then
        echo -e "${GREEN}✅ 发现 $dc101_requests 次DC101请求${NC}"
        grep -E "(request.*counter|sendRequestCountersCommand|DC101.*request)" "$LOG_FILE" | grep "$tx_id" | head -3
    else
        echo -e "${RED}❌ 未发现DC101请求${NC}"
    fi

    # 2. 检查是否收到了DC101响应
    echo -e "\n${YELLOW}2. DC101响应接收:${NC}"
    local dc101_responses=$(grep -E "(DC101.*received|HandleDC101|ProcessDC101)" "$LOG_FILE" | grep "$tx_id" | wc -l)
    if [ "$dc101_responses" -gt 0 ]; then
        echo -e "${GREEN}✅ 发现 $dc101_responses 次DC101响应${NC}"
        grep -E "(DC101.*received|HandleDC101|ProcessDC101)" "$LOG_FILE" | grep "$tx_id" | head -3
    else
        echo -e "${RED}❌ 未发现DC101响应${NC}"
    fi

    # 3. 检查后台DC101请求
    echo -e "\n${YELLOW}3. 后台DC101请求:${NC}"
    local background_requests=$(grep -E "(后台.*DC101|background.*DC101|OnTransactionPending)" "$LOG_FILE" | grep "$tx_id" | wc -l)
    if [ "$background_requests" -gt 0 ]; then
        echo -e "${GREEN}✅ 发现 $background_requests 次后台DC101请求${NC}"
        grep -E "(后台.*DC101|background.*DC101|OnTransactionPending)" "$LOG_FILE" | grep "$tx_id" | head -3
    else
        echo -e "${RED}❌ 未发现后台DC101请求${NC}"
    fi

    # 4. 检查设备通信状态
    echo -e "\n${YELLOW}4. 设备通信状态:${NC}"
    local device_id=$(grep "$tx_id" "$LOG_FILE" | grep -o '"device_id":"[^"]*"' | head -1 | cut -d'"' -f4)
    if [ -n "$device_id" ]; then
        echo -e "${BLUE}设备ID: $device_id${NC}"
        local comm_errors=$(grep -E "(通信.*失败|communication.*error|connection.*lost)" "$LOG_FILE" | grep "$device_id" | tail -3 | wc -l)
        if [ "$comm_errors" -gt 0 ]; then
            echo -e "${RED}⚠️ 发现通信问题:${NC}"
            grep -E "(通信.*失败|communication.*error|connection.*lost)" "$LOG_FILE" | grep "$device_id" | tail -3
        else
            echo -e "${GREEN}✅ 无明显通信问题${NC}"
        fi
    fi

    # 5. 时间分析
    echo -e "\n${YELLOW}5. 时间分析:${NC}"
    local smart_waiting_time=$(grep "smart_waiting" "$LOG_FILE" | grep "$tx_id" | head -1 | grep -o '[0-9-]*T[0-9:]*\.[0-9]*')
    local completion_time=$(grep "completed" "$LOG_FILE" | grep "$tx_id" | head -1 | grep -o '[0-9-]*T[0-9:]*\.[0-9]*')

    if [ -n "$smart_waiting_time" ] && [ -n "$completion_time" ]; then
        echo -e "${BLUE}Smart waiting开始: $smart_waiting_time${NC}"
        echo -e "${BLUE}交易完成时间: $completion_time${NC}"
        echo -e "${YELLOW}分析: 如果间隔超过200ms，说明触发了超时机制${NC}"
    fi

    # 6. 修复建议
    # 6. 重复发送机制检查
    echo -e "\n${YELLOW}6. 重复发送机制检查:${NC}"

    # CD1 04H状态驱动重试
    local cd1_retries=$(grep -E "(CD1.*04H.*重试|status_driven_retry)" "$LOG_FILE" | grep "$tx_id" | wc -l)
    if [ "$cd1_retries" -gt 0 ]; then
        echo -e "${GREEN}✅ CD1 04H状态驱动重试: $cd1_retries 次${NC}"
        grep -E "(CD1.*04H.*重试|status_driven_retry)" "$LOG_FILE" | grep "$tx_id" | head -3
    else
        echo -e "${RED}❌ CD1 04H状态驱动重试: 未发现${NC}"
    fi

    # DC101状态驱动重试
    local dc101_retries=$(grep -E "(DC101.*重试|status_driven_retry)" "$LOG_FILE" | grep "$tx_id" | wc -l)
    if [ "$dc101_retries" -gt 0 ]; then
        echo -e "${GREEN}✅ DC101状态驱动重试: $dc101_retries 次${NC}"
        grep -E "(DC101.*重试|status_driven_retry)" "$LOG_FILE" | grep "$tx_id" | head -3
    else
        echo -e "${RED}❌ DC101状态驱动重试: 未发现${NC}"
    fi

    echo -e "\n${YELLOW}7. 修复建议:${NC}"
    if [ "$dc101_requests" -eq 0 ]; then
        echo -e "${RED}❌ 问题: 未发送DC101请求${NC}"
        echo -e "${YELLOW}建议: 检查smart_waiting状态下的DC101请求逻辑${NC}"
    fi

    if [ "$dc101_responses" -eq 0 ] && [ "$dc101_requests" -gt 0 ]; then
        echo -e "${RED}❌ 问题: DC101请求已发送但无响应${NC}"
        echo -e "${YELLOW}建议: 检查设备通信状态和DC101命令格式${NC}"
    fi

    if [ "$background_requests" -eq 0 ]; then
        echo -e "${RED}❌ 问题: 未启动后台DC101请求${NC}"
        echo -e "${YELLOW}建议: 检查OnTransactionPending监听器是否正常工作${NC}"
    fi

    if [ "$cd1_retries" -eq 0 ] && [ "$dc101_retries" -eq 0 ]; then
        echo -e "${RED}❌ 问题: MVP状态驱动重试机制未工作${NC}"
        echo -e "${YELLOW}建议: 检查状态驱动重试逻辑和时间戳计算${NC}"
    fi
}

# 检查MVP状态驱动重试机制
check_retry_mechanism() {
    local tx_id="$1"
    echo -e "${CYAN}🔄 MVP状态驱动重试机制分析: $tx_id${NC}\n"

    # CD1 04H状态驱动重试分析
    echo -e "${YELLOW}CD1 04H状态驱动重试分析:${NC}"
    local cd1_retries=$(grep -E "(CD1.*04H.*重试|status_driven_retry)" "$LOG_FILE" | grep "$tx_id")
    if [ -n "$cd1_retries" ]; then
        echo -e "${GREEN}✅ 发现CD1 04H状态驱动重试:${NC}"
        echo "$cd1_retries"

        # 分析重试频率
        local retry_count=$(echo "$cd1_retries" | wc -l)
        echo -e "${BLUE}重试频率: $retry_count 次 (每500ms一次)${NC}"
    else
        echo -e "${RED}❌ 未发现CD1 04H状态驱动重试${NC}"
    fi

    # DC101状态驱动重试分析
    echo -e "\n${YELLOW}DC101状态驱动重试分析:${NC}"
    local dc101_retries=$(grep -E "(DC101.*重试|status_driven_retry)" "$LOG_FILE" | grep "$tx_id")
    if [ -n "$dc101_retries" ]; then
        echo -e "${GREEN}✅ 发现DC101状态驱动重试:${NC}"
        echo "$dc101_retries"

        # 分析重试频率
        local retry_count=$(echo "$dc101_retries" | wc -l)
        echo -e "${BLUE}重试频率: $retry_count 次 (每50ms一次)${NC}"
    else
        echo -e "${RED}❌ 未发现DC101状态驱动重试${NC}"
    fi

    # 状态驱动重试效果分析
    echo -e "\n${YELLOW}状态驱动重试效果分析:${NC}"
    local final_response=$(grep -E "(DC2.*final|DC101.*received)" "$LOG_FILE" | grep "$tx_id" | head -1)
    if [ -n "$final_response" ]; then
        echo -e "${GREEN}✅ 最终收到响应:${NC}"
        echo "$final_response"
    else
        echo -e "${RED}❌ 未收到最终响应，可能需要检查网络或设备状态${NC}"
    fi

    # MVP设计验证
    echo -e "\n${YELLOW}MVP设计验证:${NC}"
    echo -e "${BLUE}✅ 基于状态: 重试仅在目标状态下进行${NC}"
    echo -e "${BLUE}✅ 速率限制: CD1 04H每500ms，DC101每50ms${NC}"
    echo -e "${BLUE}✅ 自然终止: 状态变化或超时自动停止${NC}"
    echo -e "${BLUE}✅ 简洁设计: 无复杂计数和数组逻辑${NC}"
}

# 检测重复交易问题
check_duplicate_transactions() {
    echo -e "${CYAN}🚨 重复交易检测${NC}\n"

    # 检查最近1小时内的重复交易
    echo -e "${YELLOW}检查最近1小时内的重复交易:${NC}"

    # 按设备和喷嘴分组，查找同一时间段内的多个交易
    local duplicate_groups=$(grep -E "(创建|created)" "$LOG_FILE" | \
        grep "$(date +%Y-%m-%d)" | \
        grep -o '"device_id":"[^"]*".*"nozzle_id":"[^"]*"' | \
        sort | uniq -c | awk '$1 > 1')

    if [ -n "$duplicate_groups" ]; then
        echo -e "${RED}❌ 发现重复交易:${NC}"
        echo "$duplicate_groups"

        # 分析具体的重复交易
        echo -e "\n${YELLOW}重复交易详细分析:${NC}"
        echo "$duplicate_groups" | while read count device_nozzle; do
            device_id=$(echo "$device_nozzle" | grep -o '"device_id":"[^"]*"' | cut -d'"' -f4)
            nozzle_id=$(echo "$device_nozzle" | grep -o '"nozzle_id":"[^"]*"' | cut -d'"' -f4)

            echo -e "${BLUE}设备: $device_id, 喷嘴: $nozzle_id, 重复次数: $count${NC}"

            # 显示这些重复交易的ID和时间
            grep -E "(创建|created)" "$LOG_FILE" | \
                grep "$device_id" | grep "$nozzle_id" | \
                grep "$(date +%Y-%m-%d)" | \
                grep -o '"transaction_id":"[^"]*"' | \
                cut -d'"' -f4 | head -5
        done
    else
        echo -e "${GREEN}✅ 未发现明显的重复交易${NC}"
    fi

    # 检查nozzle_in触发的重复创建
    echo -e "\n${YELLOW}检查nozzle_in触发的重复创建:${NC}"
    local nozzle_in_duplicates=$(grep "nozzle_in" "$LOG_FILE" | \
        grep "$(date +%Y-%m-%d)" | \
        grep -E "(创建|created)" | wc -l)

    if [ "$nozzle_in_duplicates" -gt 0 ]; then
        echo -e "${RED}⚠️ 发现 $nozzle_in_duplicates 个由nozzle_in触发的交易创建${NC}"
        grep "nozzle_in" "$LOG_FILE" | \
            grep "$(date +%Y-%m-%d)" | \
            grep -E "(创建|created)" | \
            head -5
    else
        echo -e "${GREEN}✅ 未发现nozzle_in重复创建问题${NC}"
    fi

    # 修复建议
    echo -e "\n${YELLOW}修复建议:${NC}"
    echo -e "${BLUE}1. 检查EnsureSingleActiveTransaction逻辑${NC}"
    echo -e "${BLUE}2. 确保查询包含最近完成的交易${NC}"
    echo -e "${BLUE}3. 添加时间窗口防护机制${NC}"
    echo -e "${BLUE}4. 检查nozzle_in事件处理逻辑${NC}"
}

# 检测DC101性能问题
check_dc101_performance() {
    echo -e "${CYAN}🔍 DC101性能问题检测${NC}\n"

    # 检查DC101更新的交易数量
    echo -e "${YELLOW}检查DC101更新交易数量:${NC}"
    local dc101_updates=$(grep "找到相关交易处理DC101" "$LOG_FILE" | \
        grep "$(date +%Y-%m-%d)" | \
        grep -o '"transaction_count":[0-9]*' | \
        cut -d':' -f2)

    if [ -n "$dc101_updates" ]; then
        echo "$dc101_updates" | while read count; do
            if [ "$count" -gt 5 ]; then
                echo -e "${RED}❌ 发现DC101更新过多交易: $count 个${NC}"
            elif [ "$count" -gt 1 ]; then
                echo -e "${YELLOW}⚠️ DC101更新多个交易: $count 个${NC}"
            else
                echo -e "${GREEN}✅ DC101更新交易数量正常: $count 个${NC}"
            fi
        done
    else
        echo -e "${BLUE}ℹ️ 今日未发现DC101更新记录${NC}"
    fi

    # 检查重复的喷嘴查询
    echo -e "\n${YELLOW}检查重复喷嘴查询:${NC}"
    local nozzle_queries=$(grep "GetNozzleByID called" "$LOG_FILE" | \
        grep "$(date +%Y-%m-%d)" | \
        grep -o '"nozzle_id":"[^"]*"' | \
        sort | uniq -c | awk '$1 > 10')

    if [ -n "$nozzle_queries" ]; then
        echo -e "${RED}❌ 发现频繁的喷嘴查询:${NC}"
        echo "$nozzle_queries"
    else
        echo -e "${GREEN}✅ 喷嘴查询频率正常${NC}"
    fi

    # 检查同一时间戳的多个交易更新
    echo -e "\n${YELLOW}检查同一时间戳的多个交易更新:${NC}"
    local timestamp_duplicates=$(grep "Updated.*pump reading" "$LOG_FILE" | \
        grep "$(date +%Y-%m-%d)" | \
        cut -d',' -f1 | sort | uniq -c | awk '$1 > 5')

    if [ -n "$timestamp_duplicates" ]; then
        echo -e "${RED}❌ 发现同一时间戳的大量交易更新:${NC}"
        echo "$timestamp_duplicates"
    else
        echo -e "${GREEN}✅ 交易更新时间分布正常${NC}"
    fi

    # 修复建议
    echo -e "\n${YELLOW}性能优化建议:${NC}"
    echo -e "${BLUE}1. 限制DC101查询的交易范围（只查smart_waiting状态）${NC}"
    echo -e "${BLUE}2. 缓存喷嘴查询结果，避免重复数据库访问${NC}"
    echo -e "${BLUE}3. 添加时间窗口限制，避免更新历史交易${NC}"
    echo -e "${BLUE}4. 监控transaction_count，确保不超过合理范围${NC}"
}

# 显示最近交易
show_recent() {
    echo -e "${CYAN}📈 最近交易活动${NC}\n"
    
    echo -e "${GREEN}最近创建的交易:${NC}"
    grep -E "(主动创建交易|被动创建交易)" "$LOG_FILE" | tail -5
    
    echo -e "\n${GREEN}最近完成的交易:${NC}"
    grep -E "交易已完成" "$LOG_FILE" | tail -5
    
    echo -e "\n${YELLOW}当前智能等待:${NC}"
    grep -E "smart_waiting.*超时检查" "$LOG_FILE" | tail -3
}

# 显示错误日志
show_errors() {
    echo -e "${RED}🚨 错误和警告日志${NC}\n"
    
    echo -e "${RED}最近错误:${NC}"
    grep -E "ERROR" "$LOG_FILE" | tail -10
    
    echo -e "\n${YELLOW}最近警告:${NC}"
    grep -E "WARN" "$LOG_FILE" | tail -10
    
    echo -e "\n${RED}超时问题:${NC}"
    grep -E "(超时|timeout)" "$LOG_FILE" | tail -5
}

# 显示统计信息
show_stats() {
    echo -e "${CYAN}📊 FCC系统统计${NC}\n"
    
    echo -e "${GREEN}交易统计:${NC}"
    echo "今日创建交易: $(grep -c "$(date +%Y-%m-%d).*主动创建交易\|$(date +%Y-%m-%d).*被动创建交易" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "今日完成交易: $(grep -c "$(date +%Y-%m-%d).*交易已完成" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "当前smart_waiting: $(grep -c "smart_waiting.*超时检查" "$LOG_FILE" | tail -1 2>/dev/null || echo 0)"
    
    echo -e "\n${YELLOW}质量统计:${NC}"
    echo "good质量: $(grep -c "quality.*good" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "pending质量: $(grep -c "quality.*pending" "$LOG_FILE" 2>/dev/null || echo 0)"
    
    echo -e "\n${RED}问题统计:${NC}"
    echo "今日错误: $(grep -c "$(date +%Y-%m-%d).*ERROR" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "今日警告: $(grep -c "$(date +%Y-%m-%d).*WARN" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "超时事件: $(grep -c "超时\|timeout" "$LOG_FILE" 2>/dev/null || echo 0)"
}

# 实时监控
monitor_live() {
    echo -e "${CYAN}📡 实时监控FCC交易${NC}"
    echo "按 Ctrl+C 停止监控"
    echo "=" | tr '=' '=' | head -c 50; echo
    
    tail -f "$LOG_FILE" | grep --line-buffered -E "(交易|Transaction|DC[0-9]|CD[0-9]|ERROR|WARN)" | \
    while read line; do
        if echo "$line" | grep -q "ERROR"; then
            echo -e "${RED}$line${NC}"
        elif echo "$line" | grep -q "WARN"; then
            echo -e "${YELLOW}$line${NC}"
        elif echo "$line" | grep -q "completed\|交易已完成"; then
            echo -e "${GREEN}$line${NC}"
        else
            echo "$line"
        fi
    done
}

# 帮助信息
show_help() {
    cat << EOF
${CYAN}FCC交易快速检索脚本${NC}

${YELLOW}使用方法:${NC}
  $0 <transaction_id>    快速追踪特定交易
  $0 -l|--latest         显示最近交易活动
  $0 -e|--errors         显示错误和警告日志
  $0 -s|--stats          显示系统统计信息
  $0 -m|--monitor        实时监控交易活动
  $0 -d|--dc101 <tx_id>  专门分析DC101问题
  $0 -r|--retry <tx_id>  分析MVP状态驱动重试机制
  $0 --duplicate         检测重复交易问题
  $0 --dc101-perf        检测DC101性能问题
  $0 -h|--help           显示帮助信息

${YELLOW}示例:${NC}
  $0 cd406f5f-7349-48f4-8bfa-565edd1cd20c
  $0 -l
  $0 -e
  $0 -s
  $0 -m

${YELLOW}快捷命令:${NC}
  # 查看最近5个完成的交易
  grep "交易已完成" logs/fcc-service.log | tail -5
  
  # 查看当前smart_waiting状态
  grep "smart_waiting.*超时检查" logs/fcc-service.log | tail -3
  
  # 查看今日错误
  grep "$(date +%Y-%m-%d).*ERROR" logs/fcc-service.log
EOF
}

# 检查日志文件
if [[ ! -f "$LOG_FILE" ]]; then
    echo -e "${RED}错误: 日志文件不存在: $LOG_FILE${NC}" >&2
    exit 1
fi

# 解析参数
case "${1:-}" in
    -h|--help)
        show_help
        ;;
    -l|--latest)
        show_recent
        ;;
    -e|--errors)
        show_errors
        ;;
    -s|--stats)
        show_stats
        ;;
    -m|--monitor)
        monitor_live
        ;;
    -d|--dc101)
        if [ -n "$2" ]; then
            analyze_dc101_issue "$2"
        else
            echo -e "${RED}错误: 请指定交易ID${NC}" >&2
            exit 1
        fi
        ;;
    -r|--retry)
        if [ -n "$2" ]; then
            check_retry_mechanism "$2"
        else
            echo -e "${RED}错误: 请指定交易ID${NC}" >&2
            exit 1
        fi
        ;;
    "")
        echo -e "${RED}错误: 请指定交易ID或选项${NC}" >&2
        show_help
        exit 1
        ;;
    *)
        if [[ "$1" =~ ^[a-f0-9-]{36}$ ]]; then
            quick_trace "$1"
        else
            echo -e "${RED}错误: 无效的交易ID格式${NC}" >&2
            echo "交易ID应该是36位UUID格式，例如: cd406f5f-7349-48f4-8bfa-565edd1cd20c"
            exit 1
        fi
        ;;
esac
