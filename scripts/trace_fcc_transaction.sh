#!/bin/bash

# FCC交易完成流程日志检索脚本
# 基于 transaction_dc_assembler.go 和 MVP统一交易完成机制
# 
# 使用方法:
#   ./trace_fcc_transaction.sh <transaction_id>                    # 追踪特定交易
#   ./trace_fcc_transaction.sh -d <device_id>                     # 追踪设备所有交易
#   ./trace_fcc_transaction.sh -n <nozzle_id>                     # 追踪喷嘴所有交易
#   ./trace_fcc_transaction.sh -t <time_range>                    # 追踪时间段交易
#   ./trace_fcc_transaction.sh --smart-waiting                    # 查看智能等待状态
#   ./trace_fcc_transaction.sh --timeout                          # 查看超时问题
#   ./trace_fcc_transaction.sh --dc101                            # 查看DC101处理

set -e

# 默认配置
LOG_FILE="logs/fcc-service.log"
CONTEXT_LINES=3
SHOW_TIMESTAMPS=true
COLOR_OUTPUT=true

# 颜色定义
if [[ "$COLOR_OUTPUT" == "true" && -t 1 ]]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    PURPLE='\033[0;35m'
    CYAN='\033[0;36m'
    NC='\033[0m' # No Color
else
    RED='' GREEN='' YELLOW='' BLUE='' PURPLE='' CYAN='' NC=''
fi

# 显示帮助信息
show_help() {
    cat << EOF
${CYAN}FCC交易完成流程日志检索脚本${NC}

${YELLOW}使用方法:${NC}
  $0 <transaction_id>                    追踪特定交易完整流程
  $0 -d|--device <device_id>            追踪设备所有交易
  $0 -n|--nozzle <nozzle_id>            追踪喷嘴所有交易
  $0 -t|--time <start_time> [end_time]  追踪时间段内交易
  $0 --smart-waiting                     查看智能等待状态问题
  $0 --timeout                           查看超时相关问题
  $0 --dc101                             查看DC101处理问题
  $0 --cd1-04h                           查看CD1 04H命令问题
  $0 --quality                           查看数据质量问题
  $0 --stats                             显示交易统计信息

${YELLOW}选项:${NC}
  -f|--file <log_file>                   指定日志文件 (默认: logs/fcc-service.log)
  -c|--context <lines>                   显示上下文行数 (默认: 3)
  --no-color                             禁用颜色输出
  --no-timestamp                         不显示时间戳
  -h|--help                              显示此帮助信息

${YELLOW}示例:${NC}
  $0 cd406f5f-7349-48f4-8bfa-565edd1cd20c
  $0 -d device_com7_pump10
  $0 -t "2025-07-19 13:30:00" "2025-07-19 13:45:00"
  $0 --smart-waiting --file logs/fcc-service-2025-07-19.log
EOF
}

# 打印带颜色的标题
print_section() {
    local title="$1"
    local color="$2"
    echo -e "\n${color}=== $title ===${NC}"
}

# 格式化输出日志行
format_log_line() {
    local line="$1"
    if [[ "$SHOW_TIMESTAMPS" == "true" ]]; then
        echo "$line"
    else
        echo "$line" | sed 's/^[0-9-]* [0-9:.]* //'
    fi
}

# 检查日志文件是否存在
check_log_file() {
    if [[ ! -f "$LOG_FILE" ]]; then
        echo -e "${RED}错误: 日志文件不存在: $LOG_FILE${NC}" >&2
        exit 1
    fi
}

# 追踪特定交易的完整流程
trace_transaction() {
    local transaction_id="$1"
    
    print_section "追踪交易: $transaction_id" "$CYAN"
    
    # 阶段1: 交易创建 (DC3 → HandleAuth)
    print_section "阶段1: 交易创建" "$GREEN"
    grep -E "(DC3.*喷嘴|主动创建|被动创建|HandleAuth|Create Transaction|EnsureActiveTransaction)" "$LOG_FILE" | \
    grep "$transaction_id" | head -5 | while read line; do
        format_log_line "$line"
    done
    
    # 阶段2: 加油开始 (DC2 → filling)
    print_section "阶段2: 加油开始" "$GREEN"
    grep -E "(DC2.*体积金额|filling|HandleVolumeAmountUpdate|ProcessDC2Transaction)" "$LOG_FILE" | \
    grep "$transaction_id" | head -5 | while read line; do
        format_log_line "$line"
    done
    
    # 阶段3: 加油完成触发 (DC1 FILLING_COMPLETED → awaiting_final_dc2)
    print_section "阶段3: 加油完成触发" "$GREEN"
    grep -E "(FILLING_COMPLETED|awaiting_final_dc2|HandleFillingCompleted|智能完成机制)" "$LOG_FILE" | \
    grep "$transaction_id" | head -5 | while read line; do
        format_log_line "$line"
    done
    
    # 阶段4: 智能完成机制 (CD1 04H → smart_waiting)
    print_section "阶段4: 智能完成机制" "$GREEN"
    grep -E "(CD1.*04H|smart_waiting|sendCD1_04H_Command|OnTransactionUpdated)" "$LOG_FILE" | \
    grep "$transaction_id" | head -5 | while read line; do
        format_log_line "$line"
    done
    
    # 阶段5: 交易完成 (DC101/timeout → completed)
    print_section "阶段5: 交易完成" "$GREEN"
    grep -E "(DC101.*smart_waiting|completed|交易已完成|CompleteTransaction|forceCompleteSmartWaitingTransaction)" "$LOG_FILE" | \
    grep "$transaction_id" | head -5 | while read line; do
        format_log_line "$line"
    done
    
    # 数据质量和性能信息
    print_section "数据质量和性能" "$YELLOW"
    grep -E "(quality.*good|quality.*pending|elapsed.*[0-9]+.*ms|EndPumpVolumeReading|EndPumpAmountReading)" "$LOG_FILE" | \
    grep "$transaction_id" | while read line; do
        format_log_line "$line"
    done
    
    # 错误和警告
    print_section "错误和警告" "$RED"
    grep -E "(ERROR|WARN|Failed|failed|Error|error)" "$LOG_FILE" | \
    grep "$transaction_id" | while read line; do
        format_log_line "$line"
    done
}

# 追踪设备所有交易
trace_device() {
    local device_id="$1"

    print_section "追踪设备: $device_id" "$CYAN"

    # 最近的交易创建
    print_section "最近交易创建" "$GREEN"
    grep -E "(主动创建交易|被动创建交易|EnsureActiveTransaction)" "$LOG_FILE" | \
    grep "$device_id" | tail -10 | while read line; do
        format_log_line "$line"
    done

    # 最近的交易完成
    print_section "最近交易完成" "$GREEN"
    grep -E "(交易已完成|CompleteTransaction)" "$LOG_FILE" | \
    grep "$device_id" | tail -10 | while read line; do
        format_log_line "$line"
    done

    # 智能完成机制活动
    print_section "智能完成机制活动" "$YELLOW"
    grep -E "(smart_waiting|CD1.*04H|智能完成)" "$LOG_FILE" | \
    grep "$device_id" | tail -10 | while read line; do
        format_log_line "$line"
    done
}

# 追踪喷嘴所有交易
trace_nozzle() {
    local nozzle_id="$1"

    print_section "追踪喷嘴: $nozzle_id" "$CYAN"

    # 喷嘴相关交易
    print_section "喷嘴交易活动" "$GREEN"
    grep "$nozzle_id" "$LOG_FILE" | \
    grep -E "(交易|Transaction)" | tail -15 | while read line; do
        format_log_line "$line"
    done

    # DC3喷嘴事件
    print_section "DC3喷嘴事件" "$YELLOW"
    grep "$nozzle_id" "$LOG_FILE" | \
    grep -E "(DC3|喷嘴)" | tail -10 | while read line; do
        format_log_line "$line"
    done
}

# 追踪时间范围内的交易
trace_time_range() {
    local start_time="$1"
    local end_time="$2"

    print_section "追踪时间范围: $start_time - ${end_time:-现在}" "$CYAN"

    if [[ -n "$end_time" ]]; then
        awk -v start="$start_time" -v end="$end_time" '
        $1" "$2 >= start && $1" "$2 <= end && /交易|Transaction|DC[0-9]|CD[0-9]/
        ' "$LOG_FILE" | tail -20 | while read line; do
            format_log_line "$line"
        done
    else
        awk -v start="$start_time" '
        $1" "$2 >= start && /交易|Transaction|DC[0-9]|CD[0-9]/
        ' "$LOG_FILE" | tail -20 | while read line; do
            format_log_line "$line"
        done
    fi
}

# 查看数据质量问题
check_quality() {
    print_section "数据质量分析" "$CYAN"

    # 数据质量分布
    print_section "数据质量分布" "$GREEN"
    grep -E "(quality.*good|quality.*pending)" "$LOG_FILE" | tail -15 | while read line; do
        format_log_line "$line"
    done

    # 泵码数据问题
    print_section "泵码数据问题" "$YELLOW"
    grep -E "(EndPumpVolumeReading.*nil|EndPumpAmountReading.*nil|泵码.*缺失)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done

    # 数据不一致问题
    print_section "数据不一致" "$RED"
    grep -E "(不一致|mismatch|anomaly)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
}

# 查看智能等待状态问题
check_smart_waiting() {
    print_section "智能等待状态分析" "$CYAN"
    
    # 当前处于smart_waiting状态的交易
    print_section "当前smart_waiting交易" "$YELLOW"
    grep -E "smart_waiting.*超时检查" "$LOG_FILE" | tail -20 | while read line; do
        format_log_line "$line"
    done
    
    # smart_waiting超时情况
    print_section "smart_waiting超时" "$RED"
    grep -E "(smart_waiting.*超时|forceCompleteSmartWaitingTransaction)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
    
    # 时间戳问题
    print_section "时间戳异常" "$RED"
    grep -E "(elapsed.*-|negative|时区)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
}

# 查看超时问题
check_timeout() {
    print_section "超时问题分析" "$CYAN"
    
    # awaiting_final_dc2超时
    print_section "awaiting_final_dc2超时" "$RED"
    grep -E "(awaiting_final_dc2.*超时|2s.*timeout)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
    
    # smart_waiting超时
    print_section "smart_waiting超时" "$RED"
    grep -E "(smart_waiting.*超时|200ms.*timeout)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
    
    # 超时统计
    print_section "超时统计" "$YELLOW"
    echo "awaiting_final_dc2超时次数: $(grep -c "awaiting_final_dc2.*超时" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "smart_waiting超时次数: $(grep -c "smart_waiting.*超时" "$LOG_FILE" 2>/dev/null || echo 0)"
}

# 查看DC101处理问题
check_dc101() {
    print_section "DC101处理分析" "$CYAN"
    
    # DC101接收情况
    print_section "DC101接收" "$GREEN"
    grep -E "(DC101.*received|HandleDC101CounterUpdate|ProcessDC101Transaction)" "$LOG_FILE" | tail -15 | while read line; do
        format_log_line "$line"
    done
    
    # DC101完成交易
    print_section "DC101完成交易" "$GREEN"
    grep -E "(DC101.*smart_waiting.*完成|DC101_smart)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
    
    # DC101统计
    print_section "DC101统计" "$YELLOW"
    echo "DC101事务总数: $(grep -c "DC101.*received" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "DC101完成交易数: $(grep -c "DC101.*smart_waiting.*完成" "$LOG_FILE" 2>/dev/null || echo 0)"
}

# 查看CD1 04H命令问题
check_cd1_04h() {
    print_section "CD1 04H命令分析" "$CYAN"
    
    # CD1 04H发送
    print_section "CD1 04H发送" "$GREEN"
    grep -E "(CD1.*04H|sendCD1_04H_Command|ReturnFillingInformation)" "$LOG_FILE" | tail -15 | while read line; do
        format_log_line "$line"
    done
    
    # 重复发送检查
    print_section "重复发送检查" "$YELLOW"
    grep -E "(重复发送|防重复)" "$LOG_FILE" | tail -10 | while read line; do
        format_log_line "$line"
    done
}

# 显示交易统计信息
show_stats() {
    print_section "FCC交易统计信息" "$CYAN"
    
    echo -e "${YELLOW}交易状态统计:${NC}"
    echo "创建交易数: $(grep -c "主动创建交易\|被动创建交易" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "完成交易数: $(grep -c "交易已完成" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "smart_waiting状态: $(grep -c "smart_waiting" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "awaiting_final_dc2状态: $(grep -c "awaiting_final_dc2" "$LOG_FILE" 2>/dev/null || echo 0)"
    
    echo -e "\n${YELLOW}数据质量统计:${NC}"
    echo "good质量: $(grep -c "quality.*good" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "pending质量: $(grep -c "quality.*pending" "$LOG_FILE" 2>/dev/null || echo 0)"
    
    echo -e "\n${YELLOW}超时统计:${NC}"
    echo "smart_waiting超时: $(grep -c "smart_waiting.*超时" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "awaiting_final_dc2超时: $(grep -c "awaiting_final_dc2.*超时" "$LOG_FILE" 2>/dev/null || echo 0)"
    
    echo -e "\n${YELLOW}DC事务统计:${NC}"
    echo "DC101事务: $(grep -c "DC101.*received" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "DC2事务: $(grep -c "ProcessDC2Transaction" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "DC3事务: $(grep -c "ProcessDC3Transaction" "$LOG_FILE" 2>/dev/null || echo 0)"
    echo "CD1 04H命令: $(grep -c "CD1.*04H" "$LOG_FILE" 2>/dev/null || echo 0)"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--file)
                LOG_FILE="$2"
                shift 2
                ;;
            -c|--context)
                CONTEXT_LINES="$2"
                shift 2
                ;;
            -d|--device)
                DEVICE_ID="$2"
                shift 2
                ;;
            -n|--nozzle)
                NOZZLE_ID="$2"
                shift 2
                ;;
            -t|--time)
                START_TIME="$2"
                END_TIME="$3"
                shift 2
                if [[ "$END_TIME" =~ ^[0-9-]+ ]]; then
                    shift 1
                else
                    END_TIME=""
                fi
                ;;
            --smart-waiting)
                ACTION="smart_waiting"
                shift
                ;;
            --timeout)
                ACTION="timeout"
                shift
                ;;
            --dc101)
                ACTION="dc101"
                shift
                ;;
            --cd1-04h)
                ACTION="cd1_04h"
                shift
                ;;
            --quality)
                ACTION="quality"
                shift
                ;;
            --stats)
                ACTION="stats"
                shift
                ;;
            --no-color)
                COLOR_OUTPUT=false
                shift
                ;;
            --no-timestamp)
                SHOW_TIMESTAMPS=false
                shift
                ;;
            -*)
                echo -e "${RED}错误: 未知选项 $1${NC}" >&2
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$TRANSACTION_ID" ]]; then
                    TRANSACTION_ID="$1"
                fi
                shift
                ;;
        esac
    done
}

# 主函数
main() {
    parse_args "$@"
    check_log_file
    
    case "$ACTION" in
        smart_waiting)
            check_smart_waiting
            ;;
        timeout)
            check_timeout
            ;;
        dc101)
            check_dc101
            ;;
        cd1_04h)
            check_cd1_04h
            ;;
        quality)
            check_quality
            ;;
        stats)
            show_stats
            ;;
        *)
            if [[ -n "$TRANSACTION_ID" ]]; then
                trace_transaction "$TRANSACTION_ID"
            elif [[ -n "$DEVICE_ID" ]]; then
                trace_device "$DEVICE_ID"
            elif [[ -n "$NOZZLE_ID" ]]; then
                trace_nozzle "$NOZZLE_ID"
            elif [[ -n "$START_TIME" ]]; then
                trace_time_range "$START_TIME" "$END_TIME"
            else
                echo -e "${RED}错误: 请指定交易ID、设备ID或其他选项${NC}" >&2
                show_help
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
