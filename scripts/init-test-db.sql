-- FCC Service Test Database Initialization Script
-- 用于初始化测试环境数据库

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建controllers表
CREATE TABLE IF NOT EXISTS controllers (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    model VARCHAR(255),
    vendor VARCHAR(255),
    protocol VARCHAR(50) NOT NULL,
    address VARCHAR(255) NOT NULL,
    config JSONB NOT NULL,
    station_id VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'offline',
    health VARCHAR(50) NOT NULL DEFAULT 'unknown',
    last_seen TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);

-- 创建controllers表索引
CREATE INDEX IF NOT EXISTS idx_controllers_station_id ON controllers(station_id);
CREATE INDEX IF NOT EXISTS idx_controllers_status ON controllers(status);
CREATE INDEX IF NOT EXISTS idx_controllers_protocol ON controllers(protocol);
CREATE INDEX IF NOT EXISTS idx_controllers_type ON controllers(type);

-- 创建devices表
CREATE TABLE IF NOT EXISTS devices (
    id VARCHAR(255) PRIMARY KEY,
    controller_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    model VARCHAR(255),
    vendor VARCHAR(255),
    serial_number VARCHAR(255),
    device_address INTEGER NOT NULL,
    config JSONB NOT NULL DEFAULT '{}',
    station_id VARCHAR(255) NOT NULL,
    island_id VARCHAR(255),
    position VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'offline',
    health VARCHAR(50) NOT NULL DEFAULT 'unknown',
    last_seen TIMESTAMP,
    capabilities JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE CASCADE
);

-- 创建devices表索引
CREATE INDEX IF NOT EXISTS idx_devices_controller_id ON devices(controller_id);
CREATE INDEX IF NOT EXISTS idx_devices_station_island ON devices(station_id, island_id);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_type ON devices(type);
CREATE INDEX IF NOT EXISTS idx_devices_address ON devices(device_address);

-- 创建commands表
CREATE TABLE IF NOT EXISTS commands (
    id VARCHAR(255) PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    controller_id VARCHAR(255) NOT NULL,
    parameters JSONB DEFAULT '{}',
    payload JSONB DEFAULT '{}',
    priority INTEGER NOT NULL DEFAULT 5,
    scheduled_at TIMESTAMP,
    timeout_secs INTEGER DEFAULT 30,
    max_retries INTEGER DEFAULT 3,
    retry_count INTEGER DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    result JSONB DEFAULT '{}',
    error_message TEXT,
    transaction_id VARCHAR(255),
    user_id VARCHAR(255),
    user_agent VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE CASCADE
);

-- 创建commands表索引
CREATE INDEX IF NOT EXISTS idx_commands_device_status ON commands(device_id, status);
CREATE INDEX IF NOT EXISTS idx_commands_created_at ON commands(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_commands_status ON commands(status);
CREATE INDEX IF NOT EXISTS idx_commands_priority ON commands(priority DESC);
CREATE INDEX IF NOT EXISTS idx_commands_transaction ON commands(transaction_id);

-- 创建device_status表
CREATE TABLE IF NOT EXISTS device_status (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    health VARCHAR(50) NOT NULL,
    mode VARCHAR(50),
    cpu DECIMAL(5,2),
    memory DECIMAL(5,2),
    temperature DECIMAL(5,2),
    business_status JSONB DEFAULT '{}',
    connected BOOLEAN DEFAULT false,
    signal_strength DECIMAL(5,2),
    latency BIGINT,
    errors JSONB DEFAULT '[]',
    warnings JSONB DEFAULT '[]',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    collected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
);

-- 创建device_status表索引
CREATE INDEX IF NOT EXISTS idx_device_status_device_timestamp ON device_status(device_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_device_status_timestamp ON device_status(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_device_status_status ON device_status(status);

-- 创建transactions表（用于交易数据）
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(255) PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    command_id VARCHAR(255),
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    preset_volume DECIMAL(10,3),
    preset_amount DECIMAL(12,2),
    actual_volume DECIMAL(10,3),
    actual_amount DECIMAL(12,2),
    unit_price DECIMAL(8,3),
    nozzle_position INTEGER,
    payment_method VARCHAR(100),
    customer_info JSONB DEFAULT '{}',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (command_id) REFERENCES commands(id) ON DELETE SET NULL
);

-- 创建transactions表索引
CREATE INDEX IF NOT EXISTS idx_transactions_device_id ON transactions(device_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建更新时间触发器
DROP TRIGGER IF EXISTS update_controllers_updated_at ON controllers;
CREATE TRIGGER update_controllers_updated_at 
    BEFORE UPDATE ON controllers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_devices_updated_at ON devices;
CREATE TRIGGER update_devices_updated_at 
    BEFORE UPDATE ON devices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_old_device_status()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 清理30天以前的设备状态记录，但保留每个设备的最新记录
    WITH latest_status AS (
        SELECT device_id, MAX(timestamp) as latest_timestamp
        FROM device_status
        GROUP BY device_id
    )
    DELETE FROM device_status ds
    WHERE ds.timestamp < (CURRENT_TIMESTAMP - INTERVAL '30 days')
    AND NOT EXISTS (
        SELECT 1 FROM latest_status ls 
        WHERE ls.device_id = ds.device_id 
        AND ls.latest_timestamp = ds.timestamp
    );
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建测试用户权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO fcc_test;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO fcc_test;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO fcc_test;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'FCC Test Database initialized successfully!';
    RAISE NOTICE 'Tables created: controllers, devices, commands, device_status, transactions';
    RAISE NOTICE 'Indexes and triggers created successfully';
    RAISE NOTICE 'Ready for testing!';
END $$; 