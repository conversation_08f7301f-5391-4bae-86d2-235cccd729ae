# FCC CM7 Device Startup Script for Windows
# Windows PowerShell版本的FCC服务启动脚本

param(
    [string]$ComPort = "COM3",  # 默认COM端口，可通过参数修改
    [string]$ConfigFile = "configs\config-local.yaml"
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 颜色函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Blue "[INFO] $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

# 检查COM端口
function Test-ComPort {
    param([string]$Port)
    
    Write-Info "检查COM端口: $Port"
    
    try {
        $ports = [System.IO.Ports.SerialPort]::GetPortNames()
        if ($ports -contains $Port) {
            Write-Success "找到COM端口: $Port"
            return $true
        } else {
            Write-Error "未找到COM端口: $Port"
            Write-Info "可用的COM端口:"
            $ports | ForEach-Object { Write-Host "  - $_" }
            return $false
        }
    } catch {
        Write-Error "检查COM端口时出错: $($_.Exception.Message)"
        return $false
    }
}

# 更新配置文件中的COM端口
function Update-ConfigFile {
    param(
        [string]$ConfigPath,
        [string]$ComPort
    )
    
    Write-Info "更新配置文件中的串口设置..."
    
    if (-not (Test-Path $ConfigPath)) {
        Write-Error "配置文件不存在: $ConfigPath"
        return $false
    }
    
    try {
        $content = Get-Content $ConfigPath -Raw
        $content = $content -replace 'serial_port:\s*"[^"]*"', "serial_port: `"$ComPort`""
        $content | Set-Content $ConfigPath -Encoding UTF8
        Write-Success "配置文件已更新"
        return $true
    } catch {
        Write-Error "更新配置文件失败: $($_.Exception.Message)"
        return $false
    }
}

# 检查Go环境
function Test-GoEnvironment {
    Write-Info "检查Go环境..."
    
    try {
        $goVersion = go version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Go环境正常: $goVersion"
            return $true
        } else {
            Write-Error "未找到Go环境，请先安装Go"
            return $false
        }
    } catch {
        Write-Error "检查Go环境失败: $($_.Exception.Message)"
        return $false
    }
}

# 编译FCC服务
function Build-FCCService {
    Write-Info "编译FCC服务..."
    
    try {
        # 清理mod缓存
        go mod tidy
        if ($LASTEXITCODE -ne 0) {
            Write-Error "go mod tidy 失败"
            return $false
        }
        
        # 编译服务
        go build -o bin\fcc-server.exe cmd\fcc-server\main.go
        if ($LASTEXITCODE -eq 0) {
            Write-Success "FCC服务编译完成"
            return $true
        } else {
            Write-Error "FCC服务编译失败"
            return $false
        }
    } catch {
        Write-Error "编译过程出错: $($_.Exception.Message)"
        return $false
    }
}

# 启动FCC服务
function Start-FCCService {
    param([string]$ConfigPath)
    
    Write-Info "启动FCC服务..."
    
    # 设置环境变量
    $env:FCC_CONFIG_PATH = $ConfigPath
    $env:FCC_LOG_LEVEL = "info"
    
    try {
        # 启动服务进程
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = ".\bin\fcc-server.exe"
        $processInfo.WorkingDirectory = Get-Location
        $processInfo.UseShellExecute = $false
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        
        $process = [System.Diagnostics.Process]::Start($processInfo)
        
        # 保存进程ID
        $process.Id | Out-File -FilePath "fcc-server.pid" -Encoding UTF8
        
        Write-Success "FCC服务已启动，PID: $($process.Id)"
        
        # 等待服务启动
        Start-Sleep -Seconds 5
        
        # 检查进程是否还在运行
        if (-not $process.HasExited) {
            Write-Success "FCC服务启动成功"
            return $true
        } else {
            Write-Error "FCC服务启动失败"
            return $false
        }
    } catch {
        Write-Error "启动FCC服务失败: $($_.Exception.Message)"
        return $false
    }
}

# 验证FCC服务
function Test-FCCService {
    Write-Info "验证FCC服务..."
    
    Start-Sleep -Seconds 3
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/health" -Method Get -TimeoutSec 10
        Write-Success "FCC服务响应正常"
        return $true
    } catch {
        Write-Warning "FCC服务健康检查失败: $($_.Exception.Message)"
        return $false
    }
}

# 触发设备发现
function Start-DeviceDiscovery {
    Write-Info "触发设备发现..."
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/devices/discover" -Method Post -TimeoutSec 30
        Write-Success "设备发现请求已发送"
        
        # 等待发现完成
        Start-Sleep -Seconds 10
        
        # 获取设备列表
        $devices = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/devices" -Method Get -TimeoutSec 10
        $deviceCount = $devices.data.Count
        
        if ($deviceCount -gt 0) {
            Write-Success "发现 $deviceCount 个设备"
            $devices.data | ForEach-Object {
                Write-Host "  - 设备ID: $($_.id), 名称: $($_.name), 状态: $($_.status)"
            }
            return $true
        } else {
            Write-Warning "未发现任何设备，请检查CM7连接"
            return $false
        }
    } catch {
        Write-Warning "设备发现失败: $($_.Exception.Message)"
        return $false
    }
}

# 显示监控信息
function Show-MonitoringInfo {
    Write-Info "=== FCC系统监控信息 ==="
    Write-Host ""
    Write-Host "📊 服务状态监控:" -ForegroundColor Cyan
    Write-Host "   - 服务状态: http://localhost:8080/api/v1/health"
    Write-Host "   - 设备列表: http://localhost:8080/api/v1/devices"
    Write-Host "   - 轮询状态: http://localhost:8080/api/v1/polling/status"
    Write-Host ""
    Write-Host "📈 实时统计:" -ForegroundColor Cyan
    Write-Host "   - 监听器统计: http://localhost:8080/api/v1/adapters/statistics"
    Write-Host "   - 业务引擎: http://localhost:8080/api/v1/statemachine/statistics"
    Write-Host ""
    Write-Host "🔧 PowerShell管理命令:" -ForegroundColor Cyan
    Write-Host "   - 查看设备: Invoke-RestMethod http://localhost:8080/api/v1/devices"
    Write-Host "   - 停止服务: .\scripts\Stop-FCC.ps1"
    Write-Host "   - 验证系统: .\scripts\Verify-CM7.ps1"
    Write-Host ""
    Write-Host "📋 日志和调试:" -ForegroundColor Cyan
    Write-Host "   - 查看日志: Get-Content fcc-server.log -Tail 50 -Wait"
    Write-Host "   - 进程状态: Get-Process fcc-server"
}

# 主函数
function Main {
    Write-Host "======================================" -ForegroundColor Magenta
    Write-Host "🚀 FCC CM7 Device Startup Script (Windows)" -ForegroundColor Magenta
    Write-Host "======================================" -ForegroundColor Magenta
    Write-Host ""
    
    # 检查COM端口
    if (-not (Test-ComPort -Port $ComPort)) {
        Write-Host ""
        Write-Host "请检查CM7设备连接，或使用正确的COM端口参数:" -ForegroundColor Yellow
        Write-Host ".\scripts\Start-CM7.ps1 -ComPort COM4" -ForegroundColor Yellow
        return
    }
    
    # 检查Go环境
    if (-not (Test-GoEnvironment)) {
        return
    }
    
    # 更新配置文件
    if (-not (Update-ConfigFile -ConfigPath $ConfigFile -ComPort $ComPort)) {
        return
    }
    
    # 编译服务
    if (-not (Build-FCCService)) {
        return
    }
    
    # 启动服务
    if (-not (Start-FCCService -ConfigPath $ConfigFile)) {
        return
    }
    
    # 验证服务
    if (-not (Test-FCCService)) {
        Write-Warning "服务启动但健康检查失败，请检查日志"
    }
    
    # 设备发现
    Start-DeviceDiscovery | Out-Null
    
    # 显示监控信息
    Show-MonitoringInfo
    
    Write-Host ""
    Write-Success "🎉 FCC系统启动完成！CM7设备已连接"
    Write-Host ""
    Write-Host "请访问 http://localhost:8080 开始使用FCC管理系统" -ForegroundColor Green
    Write-Host ""
    Write-Host "按 Ctrl+C 停止服务，或运行 .\scripts\Stop-FCC.ps1" -ForegroundColor Yellow
}

# 执行主函数
Main 