-- ===================================================
-- FCC Service - 交易表添加泵码字段迁移脚本
-- 目标：为transactions表添加起始和结束泵码支持
-- 作者：FCC开发团队
-- 日期：2024-12-19
-- 版本：v1.0
-- ===================================================

-- 检查transactions表是否存在
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        RAISE EXCEPTION 'transactions表不存在，请先执行init-fcc-v2-unified.sql初始化脚本';
    END IF;
END $$;

-- 为transactions表添加泵码相关字段
BEGIN;

-- 添加起始泵码字段（体积）
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS start_pump_volume_reading DECIMAL(15,3) 
COMMENT '起始泵码-体积读数(升)，来源于DC101累计计数器';

-- 添加结束泵码字段（体积）
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS end_pump_volume_reading DECIMAL(15,3) 
COMMENT '结束泵码-体积读数(升)，来源于DC101累计计数器';

-- 添加起始泵码字段（金额）
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS start_pump_amount_reading DECIMAL(15,2) 
COMMENT '起始泵码-金额读数(元)，来源于DC101累计计数器';

-- 添加结束泵码字段（金额）
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS end_pump_amount_reading DECIMAL(15,2) 
COMMENT '结束泵码-金额读数(元)，来源于DC101累计计数器';

-- 添加泵码数据源字段
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS pump_reading_source VARCHAR(50) DEFAULT 'DC101' 
COMMENT '泵码数据来源(DC101/MANUAL/ESTIMATE)';

-- 添加泵码数据质量字段
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS pump_reading_quality VARCHAR(20) DEFAULT 'good' 
COMMENT '泵码数据质量(good/poor/bad/missing)';

-- 添加泵码验证状态字段
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS pump_reading_validated BOOLEAN DEFAULT FALSE 
COMMENT '泵码数据是否已验证';

-- 添加泵码差异字段（用于存储计算差异）
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS pump_reading_discrepancy DECIMAL(10,3) 
COMMENT '泵码计算与实际的差异值(升)';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_transactions_pump_reading_source 
ON transactions(pump_reading_source);

CREATE INDEX IF NOT EXISTS idx_transactions_pump_reading_quality 
ON transactions(pump_reading_quality);

CREATE INDEX IF NOT EXISTS idx_transactions_pump_reading_validated 
ON transactions(pump_reading_validated);

-- 创建约束确保数据一致性
ALTER TABLE transactions 
ADD CONSTRAINT check_pump_volume_readings 
CHECK (
    (start_pump_volume_reading IS NULL AND end_pump_volume_reading IS NULL) 
    OR 
    (start_pump_volume_reading IS NOT NULL AND end_pump_volume_reading IS NOT NULL 
     AND end_pump_volume_reading >= start_pump_volume_reading)
);

ALTER TABLE transactions 
ADD CONSTRAINT check_pump_amount_readings 
CHECK (
    (start_pump_amount_reading IS NULL AND end_pump_amount_reading IS NULL) 
    OR 
    (start_pump_amount_reading IS NOT NULL AND end_pump_amount_reading IS NOT NULL 
     AND end_pump_amount_reading >= start_pump_amount_reading)
);

-- 添加泵码数据源的检查约束
ALTER TABLE transactions 
ADD CONSTRAINT check_pump_reading_source 
CHECK (pump_reading_source IN ('DC101', 'MANUAL', 'ESTIMATE', 'FALLBACK'));

-- 添加泵码数据质量的检查约束
ALTER TABLE transactions 
ADD CONSTRAINT check_pump_reading_quality 
CHECK (pump_reading_quality IN ('good', 'poor', 'bad', 'missing', 'estimated'));

-- 创建计算列的函数（计算交易体积）
CREATE OR REPLACE FUNCTION calculate_pump_volume_difference(
    start_reading DECIMAL(15,3), 
    end_reading DECIMAL(15,3)
) RETURNS DECIMAL(15,3) AS $$
BEGIN
    IF start_reading IS NULL OR end_reading IS NULL THEN
        RETURN NULL;
    END IF;
    RETURN end_reading - start_reading;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 创建计算列的函数（计算交易金额）
CREATE OR REPLACE FUNCTION calculate_pump_amount_difference(
    start_reading DECIMAL(15,2), 
    end_reading DECIMAL(15,2)
) RETURNS DECIMAL(15,2) AS $$
BEGIN
    IF start_reading IS NULL OR end_reading IS NULL THEN
        RETURN NULL;
    END IF;
    RETURN end_reading - start_reading;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 提交事务
COMMIT;

-- 验证迁移结果
DO $$
DECLARE
    column_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'transactions' 
    AND column_name IN (
        'start_pump_volume_reading', 
        'end_pump_volume_reading',
        'start_pump_amount_reading', 
        'end_pump_amount_reading',
        'pump_reading_source', 
        'pump_reading_quality',
        'pump_reading_validated',
        'pump_reading_discrepancy'
    );
    
    IF column_count = 8 THEN
        RAISE NOTICE '✅ 泵码字段迁移成功！已添加8个新字段到transactions表';
    ELSE
        RAISE EXCEPTION '❌ 泵码字段迁移失败！只添加了%个字段，期望8个', column_count;
    END IF;
END $$;

-- 输出迁移完成信息
SELECT 
    '🎉 Transactions表泵码字段迁移完成!' as status,
    '新增字段: start_pump_volume_reading, end_pump_volume_reading, start_pump_amount_reading, end_pump_amount_reading, pump_reading_source, pump_reading_quality, pump_reading_validated, pump_reading_discrepancy' as added_fields,
    '新增索引: 3个' as added_indexes,
    '新增约束: 4个' as added_constraints,
    '新增函数: 2个' as added_functions,
    CURRENT_TIMESTAMP as completed_at; 