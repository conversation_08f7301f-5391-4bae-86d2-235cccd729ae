#!/bin/bash

# FCC CM7 Device Startup Script
# 用于启动FCC服务并连接CM7设备

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查CM7串口连接
check_cm7_connection() {
    log_info "检查CM7设备连接..."
    
    # 检查串口设备
    if [ ! -e "/dev/ttyUSB0" ]; then
        log_warning "未找到 /dev/ttyUSB0，尝试查找其他串口设备..."
        
        # 列出可用的串口设备
        ports=$(ls /dev/tty{USB,ACM}* 2>/dev/null || true)
        if [ -z "$ports" ]; then
            log_error "未找到任何串口设备，请检查CM7连接"
            log_info "尝试运行: lsusb 查看USB设备"
            exit 1
        else
            log_info "发现可用串口设备:"
            echo "$ports"
            log_warning "请更新配置文件中的 serial_port 设置"
        fi
    else
        log_success "找到CM7串口设备: /dev/ttyUSB0"
    fi
    
    # 检查串口权限
    if [ ! -w "/dev/ttyUSB0" ]; then
        log_warning "当前用户无法写入串口设备，请添加用户到 dialout 组:"
        echo "sudo usermod -a -G dialout \$USER"
        echo "然后重新登录"
    fi
}

# 启动数据库和缓存
start_dependencies() {
    log_info "启动依赖服务..."
    
    # 检查并启动PostgreSQL
    if ! pgrep -x "postgres" > /dev/null; then
        log_info "启动PostgreSQL数据库..."
        if command -v systemctl &> /dev/null; then
            sudo systemctl start postgresql
        elif command -v brew &> /dev/null; then
            brew services start postgresql
        else
            log_warning "请手动启动PostgreSQL数据库"
        fi
    else
        log_success "PostgreSQL已在运行"
    fi
    
    # 检查并启动Redis
    if ! pgrep -x "redis-server" > /dev/null; then
        log_info "启动Redis缓存..."
        if command -v systemctl &> /dev/null; then
            sudo systemctl start redis
        elif command -v brew &> /dev/null; then
            brew services start redis
        else
            log_warning "请手动启动Redis服务"
        fi
    else
        log_success "Redis已在运行"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化FCC数据库..."
    
    # 运行数据库初始化脚本
    if [ -f "scripts/init-fcc-tables.sql" ]; then
        psql -h localhost -U fcc_user -d fcc_db -f scripts/init-fcc-tables.sql
        log_success "数据库初始化完成"
    else
        log_warning "未找到数据库初始化脚本"
    fi
}

# 编译FCC服务
build_fcc_service() {
    log_info "编译FCC服务..."
    
    go mod tidy
    go build -o bin/fcc-server cmd/fcc-server/main.go
    
    if [ $? -eq 0 ]; then
        log_success "FCC服务编译完成"
    else
        log_error "FCC服务编译失败"
        exit 1
    fi
}

# 启动FCC服务
start_fcc_service() {
    log_info "启动FCC服务..."
    
    # 设置环境变量
    export FCC_CONFIG_PATH="configs/config-local.yaml"
    export FCC_LOG_LEVEL="info"
    
    # 启动服务
    ./bin/fcc-server &
    FCC_PID=$!
    
    log_info "FCC服务已启动，PID: $FCC_PID"
    echo $FCC_PID > fcc-server.pid
    
    # 等待服务启动
    log_info "等待FCC服务启动..."
    sleep 5
    
    # 检查服务是否正常运行
    if kill -0 $FCC_PID 2>/dev/null; then
        log_success "FCC服务启动成功"
    else
        log_error "FCC服务启动失败"
        exit 1
    fi
}

# 验证设备连接
verify_device_connection() {
    log_info "验证CM7设备连接..."
    
    # 等待设备发现
    sleep 10
    
    # 调用设备发现API
    response=$(curl -s -w "%{http_code}" "http://localhost:8080/api/v1/devices/discover" || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "设备发现API调用成功"
        
        # 获取设备列表
        devices=$(curl -s "http://localhost:8080/api/v1/devices" | jq '.data | length' 2>/dev/null || echo "0")
        log_info "发现 $devices 个设备"
        
        if [ "$devices" -gt "0" ]; then
            log_success "CM7设备连接验证成功！"
        else
            log_warning "未发现任何设备，请检查CM7连接和配置"
        fi
    else
        log_warning "设备发现API调用失败，HTTP状态码: $http_code"
    fi
}

# 显示监控信息
show_monitoring_info() {
    log_info "=== FCC系统监控信息 ==="
    echo ""
    echo "📊 服务状态监控:"
    echo "   - 服务状态: http://localhost:8080/api/v1/health"
    echo "   - 设备列表: http://localhost:8080/api/v1/devices"
    echo "   - 轮询状态: http://localhost:8080/api/v1/polling/status"
    echo ""
    echo "📈 实时统计:"
    echo "   - 监听器统计: http://localhost:8080/api/v1/adapters/statistics"
    echo "   - 业务引擎: http://localhost:8080/api/v1/statemachine/statistics"
    echo ""
    echo "🔧 管理操作:"
    echo "   - 手动设备发现: curl -X POST http://localhost:8080/api/v1/devices/discover"
    echo "   - 查看设备状态: curl http://localhost:8080/api/v1/devices/{device_id}/status"
    echo ""
    echo "📋 日志查看:"
    echo "   - 查看FCC日志: tail -f fcc-server.log"
    echo "   - 停止服务: ./scripts/stop-fcc.sh"
}

# 主函数
main() {
    echo "======================================"
    echo "🚀 FCC CM7 Device Startup Script"
    echo "======================================"
    echo ""
    
    # 检查系统要求
    log_info "检查系统要求..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "未找到Go环境，请先安装Go"
        exit 1
    fi
    
    # 检查jq工具
    if ! command -v jq &> /dev/null; then
        log_warning "建议安装jq工具用于JSON处理: sudo apt-get install jq"
    fi
    
    # 执行启动流程
    check_cm7_connection
    start_dependencies
    init_database
    build_fcc_service
    start_fcc_service
    verify_device_connection
    show_monitoring_info
    
    echo ""
    log_success "🎉 FCC系统启动完成！CM7设备已连接"
    echo ""
    echo "请访问 http://localhost:8080 开始使用FCC管理系统"
    echo ""
}

# 信号处理
cleanup() {
    log_info "正在停止FCC服务..."
    if [ -f "fcc-server.pid" ]; then
        kill $(cat fcc-server.pid) 2>/dev/null || true
        rm -f fcc-server.pid
    fi
    exit 0
}

trap cleanup SIGINT SIGTERM

# 执行主函数
main "$@" 