-- 数据库迁移脚本：将金额字段从2位小数改为3位小数
-- 创建时间: 2024-12-19
-- 目的: 统一体积和金额都使用3位小数精度

-- 开始事务
BEGIN;

-- 1. 修改 transactions 表中的金额字段
ALTER TABLE transactions 
  ALTER COLUMN preset_amount TYPE DECIMAL(10,3),
  ALTER COLUMN actual_amount TYPE DECIMAL(10,3),
  ALTER COLUMN total_amount TYPE DECIMAL(10,3),
  ALTER COLUMN paid_amount TYPE DECIMAL(10,3),
  ALTER COLUMN change_amount TYPE DECIMAL(10,3),
  ALTER COLUMN start_pump_amount_reading TYPE DECIMAL(15,3),
  ALTER COLUMN end_pump_amount_reading TYPE DECIMAL(15,3);

-- 2. 修改 nozzles 表中的金额字段
ALTER TABLE nozzles 
  ALTER COLUMN current_amount TYPE DECIMAL(10,3),
  ALTER COLUMN preset_amount TYPE DECIMAL(10,3),
  ALTER COLUMN total_amount TYPE DECIMAL(12,3);

-- 3. 如果存在其他相关表，也需要修改
-- 检查是否存在 device_status 表中的金额字段
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns 
             WHERE table_name = 'device_status' AND column_name = 'amount') THEN
    ALTER TABLE device_status ALTER COLUMN amount TYPE DECIMAL(10,3);
  END IF;
END
$$;

-- 4. 更新注释说明新的精度
COMMENT ON COLUMN transactions.preset_amount IS '预设金额(元) - 3位小数精度';
COMMENT ON COLUMN transactions.actual_amount IS '实际金额(元) - 3位小数精度';
COMMENT ON COLUMN transactions.total_amount IS '总金额(元) - 3位小数精度';
COMMENT ON COLUMN transactions.paid_amount IS '已付金额(元) - 3位小数精度';
COMMENT ON COLUMN transactions.change_amount IS '找零金额(元) - 3位小数精度';
COMMENT ON COLUMN transactions.start_pump_amount_reading IS '起始泵码-金额读数(元) - 3位小数精度';
COMMENT ON COLUMN transactions.end_pump_amount_reading IS '结束泵码-金额读数(元) - 3位小数精度';

COMMENT ON COLUMN nozzles.current_amount IS '当前金额(元) - 3位小数精度';
COMMENT ON COLUMN nozzles.preset_amount IS '预设金额(元) - 3位小数精度';
COMMENT ON COLUMN nozzles.total_amount IS '累计总金额(元) - 3位小数精度';

-- 提交事务
COMMIT;

-- 验证修改结果
SELECT 
  table_name,
  column_name,
  data_type,
  numeric_precision,
  numeric_scale
FROM information_schema.columns 
WHERE table_name IN ('transactions', 'nozzles') 
  AND column_name LIKE '%amount%'
ORDER BY table_name, column_name; 