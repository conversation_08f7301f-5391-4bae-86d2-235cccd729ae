-- ===================================================
-- FCC Service - 智能完成机制数据库修复脚本
-- 目标：修复 awaiting_final_dc2_at 列缺失错误
-- 作者：FCC开发团队
-- 日期：2025年1月19日
-- 版本：v1.0
-- ===================================================

-- 检查transactions表是否存在
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        RAISE EXCEPTION 'transactions表不存在，请先执行数据库初始化脚本';
    END IF;
END $$;

-- 开始事务
BEGIN;

-- 添加awaiting_final_dc2_at字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'awaiting_final_dc2_at'
    ) THEN
        ALTER TABLE transactions ADD COLUMN awaiting_final_dc2_at TIMESTAMP;
        RAISE NOTICE '✅ 已添加 awaiting_final_dc2_at 字段';
    ELSE
        RAISE NOTICE 'ℹ️ awaiting_final_dc2_at 字段已存在，跳过添加';
    END IF;
END $$;

-- 添加smart_waiting_at字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'smart_waiting_at'
    ) THEN
        ALTER TABLE transactions ADD COLUMN smart_waiting_at TIMESTAMP;
        RAISE NOTICE '✅ 已添加 smart_waiting_at 字段';
    ELSE
        RAISE NOTICE 'ℹ️ smart_waiting_at 字段已存在，跳过添加';
    END IF;
END $$;

-- 添加字段注释
COMMENT ON COLUMN transactions.awaiting_final_dc2_at IS 'DC1 FILLING_COMPLETED进入awaiting_final_dc2状态时间，用于500ms超时检查';
COMMENT ON COLUMN transactions.smart_waiting_at IS '收到最终DC2数据进入smart_waiting状态时间，用于200ms超时检查';

-- 创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_transactions_awaiting_final_dc2_at ON transactions(awaiting_final_dc2_at);
CREATE INDEX IF NOT EXISTS idx_transactions_smart_waiting_at ON transactions(smart_waiting_at);

-- 创建复合索引用于超时查询
CREATE INDEX IF NOT EXISTS idx_transactions_awaiting_final_dc2_timeout ON transactions(status, awaiting_final_dc2_at) 
WHERE awaiting_final_dc2_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_transactions_smart_waiting_timeout ON transactions(status, smart_waiting_at) 
WHERE smart_waiting_at IS NOT NULL;

-- 更新status字段约束，添加新状态
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_transaction_status;
ALTER TABLE transactions 
ADD CONSTRAINT chk_transaction_status CHECK (
    status::text = ANY (ARRAY[
        'pending'::character varying,
        'started'::character varying, 
        'active'::character varying,
        'completed'::character varying,
        'cancelled'::character varying,
        'failed'::character varying,
        'refunded'::character varying,
        'initiated'::character varying,
        'filling'::character varying,
        'pending_counters'::character varying,
        'awaiting_final_dc2'::character varying,  -- 新增：智能完成机制状态
        'smart_waiting'::character varying        -- 新增：智能完成机制状态
    ]::text[])
);

-- 为新状态创建专用索引
CREATE INDEX IF NOT EXISTS idx_transactions_smart_completion_states ON transactions(status) 
WHERE status IN ('awaiting_final_dc2', 'smart_waiting');

-- 验证迁移结果
DO $$
DECLARE
    awaiting_col_exists BOOLEAN;
    smart_col_exists BOOLEAN;
    constraint_exists BOOLEAN;
BEGIN
    -- 检查字段是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'awaiting_final_dc2_at'
    ) INTO awaiting_col_exists;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'smart_waiting_at'
    ) INTO smart_col_exists;
    
    -- 检查约束是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_transaction_status'
    ) INTO constraint_exists;
    
    -- 输出验证结果
    RAISE NOTICE '📊 迁移验证结果:';
    RAISE NOTICE '   awaiting_final_dc2_at字段: %', CASE WHEN awaiting_col_exists THEN '✅ 存在' ELSE '❌ 缺失' END;
    RAISE NOTICE '   smart_waiting_at字段: %', CASE WHEN smart_col_exists THEN '✅ 存在' ELSE '❌ 缺失' END;
    RAISE NOTICE '   状态约束: %', CASE WHEN constraint_exists THEN '✅ 已更新' ELSE '❌ 未更新' END;
    
    -- 如果有任何检查失败，回滚事务
    IF NOT (awaiting_col_exists AND smart_col_exists AND constraint_exists) THEN
        RAISE EXCEPTION '❌ 迁移验证失败，事务将回滚';
    ELSE
        RAISE NOTICE '🎉 智能完成机制数据库迁移成功完成！';
    END IF;
END $$;

-- 提交事务
COMMIT;

-- 显示完成信息
\echo '✅ 数据库迁移脚本执行完成'
\echo 'ℹ️ 现在可以重启FCC服务以应用新的数据库结构' 