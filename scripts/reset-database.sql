-- ===================================================
-- FCC Service 数据库重置脚本
-- 删除旧表结构并重新创建最新版本
-- ===================================================

BEGIN;

-- 显示开始信息
SELECT 'Starting FCC database reset...' as status, CURRENT_TIMESTAMP as timestamp;

-- 1. 删除所有外键约束（避免删除表时的依赖问题）
DO $$
DECLARE
    r RECORD;
BEGIN
    -- 删除所有外键约束
    FOR r IN (
        SELECT 
            tc.table_name, 
            tc.constraint_name
        FROM information_schema.table_constraints tc
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
    ) LOOP
        EXECUTE 'ALTER TABLE ' || r.table_name || ' DROP CONSTRAINT IF EXISTS ' || r.constraint_name || ' CASCADE';
        RAISE NOTICE 'Dropped foreign key constraint: %.%', r.table_name, r.constraint_name;
    END LOOP;
END $$;

-- 2. 删除所有视图
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT viewname 
        FROM pg_views 
        WHERE schemaname = 'public'
    ) LOOP
        EXECUTE 'DROP VIEW IF EXISTS ' || r.viewname || ' CASCADE';
        RAISE NOTICE 'Dropped view: %', r.viewname;
    END LOOP;
END $$;

-- 3. 删除所有表
DO $$
DECLARE
    r RECORD;
BEGIN
    -- 按特定顺序删除表以避免依赖问题
    FOR r IN (
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY tablename
    ) LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || r.tablename || ' CASCADE';
        RAISE NOTICE 'Dropped table: %', r.tablename;
    END LOOP;
END $$;

-- 4. 删除自定义函数（跳过扩展函数）
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT proname, pg_get_function_identity_arguments(oid) as args
        FROM pg_proc 
        WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        AND prokind = 'f'
        -- 跳过扩展相关的函数
        AND proname NOT LIKE 'uuid_%'
        AND proname NOT IN ('update_updated_at_column', 'cleanup_old_device_status')
    ) LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS ' || r.proname || '(' || r.args || ') CASCADE';
        RAISE NOTICE 'Dropped function: %(%)', r.proname, r.args;
    END LOOP;
END $$;

-- 5. 删除所有序列
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'public'
    ) LOOP
        EXECUTE 'DROP SEQUENCE IF EXISTS ' || r.sequencename || ' CASCADE';
        RAISE NOTICE 'Dropped sequence: %', r.sequencename;
    END LOOP;
END $$;

-- 6. 删除所有自定义类型
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT typname 
        FROM pg_type 
        WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        AND typtype = 'e'  -- 枚举类型
    ) LOOP
        EXECUTE 'DROP TYPE IF EXISTS ' || r.typname || ' CASCADE';
        RAISE NOTICE 'Dropped type: %', r.typname;
    END LOOP;
END $$;

COMMIT;

-- 显示清理完成信息
SELECT 'Database cleanup completed!' as status, CURRENT_TIMESTAMP as timestamp;
SELECT 'Ready to run init-fcc-v2-unified.sql' as next_step; 