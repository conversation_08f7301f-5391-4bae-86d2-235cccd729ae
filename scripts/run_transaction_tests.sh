#!/bin/bash

# FCC交易完成机制测试运行脚本
# 
# 使用方法:
#   ./run_transaction_tests.sh                    # 运行所有测试
#   ./run_transaction_tests.sh unit               # 只运行单元测试
#   ./run_transaction_tests.sh integration        # 只运行集成测试
#   ./run_transaction_tests.sh benchmark          # 只运行性能测试
#   ./run_transaction_tests.sh coverage           # 运行测试并生成覆盖率报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目根目录
PROJECT_ROOT="/home/<USER>"
TEST_PACKAGE="./internal/services/polling/v2"

# 打印带颜色的标题
print_section() {
    local title="$1"
    local color="$2"
    echo -e "\n${color}=== $title ===${NC}"
}

# 检查Go环境
check_go_env() {
    if ! command -v go &> /dev/null; then
        echo -e "${RED}错误: Go未安装或不在PATH中${NC}" >&2
        exit 1
    fi
    
    echo -e "${GREEN}Go版本: $(go version)${NC}"
}

# 运行单元测试
run_unit_tests() {
    print_section "运行单元测试" "$CYAN"
    
    echo -e "${YELLOW}测试文件:${NC}"
    echo "- transaction_completion_test.go"
    echo "- device_poller_timeout_test.go"
    
    cd "$PROJECT_ROOT"
    
    # 运行单元测试
    go test -v -run "^Test.*" "$TEST_PACKAGE" \
        -timeout 30s \
        -count=1 \
        -race
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 单元测试通过${NC}"
    else
        echo -e "${RED}❌ 单元测试失败${NC}"
        exit 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_section "运行集成测试" "$CYAN"
    
    echo -e "${YELLOW}测试文件:${NC}"
    echo "- transaction_flow_integration_test.go"
    
    cd "$PROJECT_ROOT"
    
    # 运行集成测试
    go test -v -run "^TestCompleteTransactionFlow.*" "$TEST_PACKAGE" \
        -timeout 60s \
        -count=1 \
        -race
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 集成测试通过${NC}"
    else
        echo -e "${RED}❌ 集成测试失败${NC}"
        exit 1
    fi
}

# 运行性能测试
run_benchmark_tests() {
    print_section "运行性能测试" "$CYAN"
    
    cd "$PROJECT_ROOT"
    
    # 运行性能测试
    go test -v -run "^Benchmark.*" "$TEST_PACKAGE" \
        -bench=. \
        -benchmem \
        -timeout 120s \
        -count=3
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 性能测试完成${NC}"
    else
        echo -e "${RED}❌ 性能测试失败${NC}"
        exit 1
    fi
}

# 运行覆盖率测试
run_coverage_tests() {
    print_section "运行覆盖率测试" "$CYAN"
    
    cd "$PROJECT_ROOT"
    
    # 创建覆盖率输出目录
    mkdir -p coverage
    
    # 运行测试并生成覆盖率
    go test -v "$TEST_PACKAGE" \
        -coverprofile=coverage/coverage.out \
        -covermode=atomic \
        -timeout 60s \
        -race
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 覆盖率测试完成${NC}"
        
        # 生成HTML覆盖率报告
        go tool cover -html=coverage/coverage.out -o coverage/coverage.html
        echo -e "${BLUE}📊 覆盖率报告已生成: coverage/coverage.html${NC}"
        
        # 显示覆盖率统计
        go tool cover -func=coverage/coverage.out | tail -1
    else
        echo -e "${RED}❌ 覆盖率测试失败${NC}"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    print_section "运行所有测试" "$CYAN"
    
    run_unit_tests
    run_integration_tests
    
    print_section "测试总结" "$GREEN"
    echo -e "${GREEN}✅ 所有测试通过${NC}"
}

# 验证测试场景覆盖
verify_test_scenarios() {
    print_section "验证测试场景覆盖" "$YELLOW"
    
    echo -e "${BLUE}核心场景覆盖检查:${NC}"
    
    # 检查关键测试场景
    scenarios=(
        "TestCompleteTransaction_WithGoodQuality"
        "TestCompleteTransaction_WithPendingQuality" 
        "TestCompleteTransaction_Idempotent"
        "TestHandleDC101_SmartWaiting"
        "TestHandleDC101_CompletedPending"
        "TestSmartWaitingTimeout_200ms"
        "TestAwaitingFinalDC2Timeout_2s"
        "TestBackgroundDC101Request_Success"
        "TestCompleteTransactionFlow_HappyPath"
        "TestCompleteTransactionFlow_TimeoutPath"
        "TestCompleteTransactionFlow_BackgroundImprovement"
    )
    
    cd "$PROJECT_ROOT"
    
    for scenario in "${scenarios[@]}"; do
        if grep -q "$scenario" "$TEST_PACKAGE"/*_test.go; then
            echo -e "${GREEN}✅ $scenario${NC}"
        else
            echo -e "${RED}❌ $scenario${NC}"
        fi
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
${CYAN}FCC交易完成机制测试运行脚本${NC}

${YELLOW}使用方法:${NC}
  $0                    运行所有测试
  $0 unit               只运行单元测试
  $0 integration        只运行集成测试
  $0 benchmark          只运行性能测试
  $0 coverage           运行测试并生成覆盖率报告
  $0 verify             验证测试场景覆盖
  $0 help               显示此帮助信息

${YELLOW}测试覆盖的核心场景:${NC}
  ✅ 交易完成机制（good/pending质量）
  ✅ 幂等性保护
  ✅ smart_waiting超时（200ms）
  ✅ awaiting_final_dc2超时（2s）
  ✅ DC101处理（smart_waiting和completed状态）
  ✅ 后台数据完善机制
  ✅ 并发访问保护
  ✅ 错误处理
  ✅ 状态历史记录
  ✅ 性能基准测试

${YELLOW}输出文件:${NC}
  coverage/coverage.out     覆盖率数据
  coverage/coverage.html    覆盖率HTML报告

${YELLOW}示例:${NC}
  $0 unit                   # 快速单元测试
  $0 coverage               # 生成覆盖率报告
  $0 benchmark              # 性能测试
EOF
}

# 主函数
main() {
    check_go_env
    
    case "${1:-all}" in
        unit)
            run_unit_tests
            ;;
        integration)
            run_integration_tests
            ;;
        benchmark)
            run_benchmark_tests
            ;;
        coverage)
            run_coverage_tests
            ;;
        verify)
            verify_test_scenarios
            ;;
        all)
            run_all_tests
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知选项 '$1'${NC}" >&2
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
