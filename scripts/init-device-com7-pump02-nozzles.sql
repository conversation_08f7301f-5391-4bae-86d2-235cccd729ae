-- 为device_com7_pump02初始化喷嘴
-- 修复"nozzle not found"错误

-- 检查设备是否存在
SELECT id, name, type FROM devices WHERE id = 'device_com7_pump02';

-- 为device_com7_pump02创建喷嘴记录
INSERT INTO nozzles (
    id, number, name, device_id, fuel_grade_id, current_price, position, is_enabled
) VALUES 
('device_com7_pump02-nozzle-1', 1, '1号油枪', 'device_com7_pump02', 'grade_92', 7.50, 'left', TRUE),
('device_com7_pump02-nozzle-2', 2, '2号油枪', 'device_com7_pump02', 'grade_95', 8.00, 'right', TRUE);

-- 验证喷嘴创建成功
SELECT id, device_id, number, name, fuel_grade_id, current_price, is_enabled 
FROM nozzles 
WHERE device_id = 'device_com7_pump02' 
ORDER BY number;

-- 同时检查所有COM7设备的喷嘴状态
SELECT d.id as device_id, d.name as device_name, n.id as nozzle_id, n.number, n.name as nozzle_name, n.is_enabled
FROM devices d
LEFT JOIN nozzles n ON d.id = n.device_id
WHERE d.id LIKE 'device_com7%'
ORDER BY d.id, n.number; 