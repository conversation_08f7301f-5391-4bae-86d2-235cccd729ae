#!/bin/bash

# ===================================================
# FCC Service 交易持久化修复验证脚本
# 用于验证交易重复插入和数据库写入问题的修复效果
# ===================================================

set -e

echo "🔍 FCC Service 交易持久化修复验证"
echo "=================================="

# 检查数据库连接
echo "📊 1. 检查数据库连接..."
if command -v psql >/dev/null 2>&1; then
    echo "✅ PostgreSQL 客户端可用"
else
    echo "❌ PostgreSQL 客户端不可用，请安装 postgresql-client"
    exit 1
fi

# 检查最近的交易记录
echo ""
echo "📋 2. 检查最近的交易记录..."
cat << 'EOF' > /tmp/check_recent_transactions.sql
-- 检查最近10分钟内的交易记录
SELECT 
    id,
    pump_id,
    nozzle_id,
    operator_id,
    status,
    volume,
    amount,
    unit_price,
    start_total_counter,
    end_total_counter,
    counter_quality,
    counter_source,
    created_at,
    updated_at
FROM transactions 
WHERE created_at >= NOW() - INTERVAL '10 minutes'
ORDER BY created_at DESC
LIMIT 10;
EOF

echo "最近10分钟的交易记录："
# 这里需要根据实际的数据库连接信息进行调整
# psql -h localhost -U fcc_user -d fcc_db -f /tmp/check_recent_transactions.sql

echo ""
echo "🔄 3. 检查重复交易..."
cat << 'EOF' > /tmp/check_duplicate_transactions.sql
-- 检查可能的重复交易
SELECT 
    pump_id,
    nozzle_id,
    volume,
    amount,
    DATE_TRUNC('minute', created_at) as minute_group,
    COUNT(*) as transaction_count
FROM transactions 
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY pump_id, nozzle_id, volume, amount, DATE_TRUNC('minute', created_at)
HAVING COUNT(*) > 1
ORDER BY transaction_count DESC, minute_group DESC;
EOF

echo "重复交易检查（相同设备、喷嘴、体积、金额在同一分钟内的记录）："
# psql -h localhost -U fcc_user -d fcc_db -f /tmp/check_duplicate_transactions.sql

echo ""
echo "👥 4. 检查员工ID写入情况..."
cat << 'EOF' > /tmp/check_operator_id_coverage.sql
-- 检查员工ID的覆盖率
SELECT 
    CASE 
        WHEN operator_id IS NULL OR operator_id = '' THEN '缺失员工ID'
        ELSE '有员工ID'
    END as operator_id_status,
    COUNT(*) as transaction_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM transactions 
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY (operator_id IS NULL OR operator_id = '')
ORDER BY transaction_count DESC;
EOF

echo "员工ID覆盖率统计："
# psql -h localhost -U fcc_user -d fcc_db -f /tmp/check_operator_id_coverage.sql

echo ""
echo "🏷️ 5. 检查泵码数据质量..."
cat << 'EOF' > /tmp/check_counter_quality.sql
-- 检查泵码数据质量分布
SELECT 
    counter_quality,
    counter_source,
    COUNT(*) as transaction_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM transactions 
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY counter_quality, counter_source
ORDER BY transaction_count DESC;
EOF

echo "泵码数据质量分布："
# psql -h localhost -U fcc_user -d fcc_db -f /tmp/check_counter_quality.sql

echo ""
echo "⚠️ 6. 检查异常泵码数据..."
cat << 'EOF' > /tmp/check_abnormal_counters.sql
-- 检查异常的泵码数据
SELECT 
    id,
    pump_id,
    nozzle_id,
    volume,
    amount,
    start_total_counter,
    end_total_counter,
    start_amount_counter,
    end_amount_counter,
    counter_quality,
    created_at
FROM transactions 
WHERE created_at >= NOW() - INTERVAL '1 hour'
AND (
    -- 结束泵码小于起始泵码
    (end_total_counter < start_total_counter AND end_total_counter > 0) OR
    (end_amount_counter < start_amount_counter AND end_amount_counter > 0) OR
    -- 泵码为零但交易有数据
    (start_total_counter = 0 AND end_total_counter = 0 AND volume > 0) OR
    (start_amount_counter = 0 AND end_amount_counter = 0 AND amount > 0)
)
ORDER BY created_at DESC;
EOF

echo "异常泵码数据："
# psql -h localhost -U fcc_user -d fcc_db -f /tmp/check_abnormal_counters.sql

echo ""
echo "📈 7. 生成修复效果报告..."
cat << 'EOF' > /tmp/generate_fix_report.sql
-- 生成修复效果报告
WITH transaction_stats AS (
    SELECT 
        DATE_TRUNC('hour', created_at) as hour_group,
        COUNT(*) as total_transactions,
        COUNT(CASE WHEN operator_id IS NOT NULL AND operator_id != '' THEN 1 END) as transactions_with_operator,
        COUNT(CASE WHEN counter_quality IN ('good', 'active') THEN 1 END) as good_quality_transactions,
        COUNT(CASE WHEN start_total_counter > 0 AND end_total_counter > start_total_counter THEN 1 END) as valid_pump_readings
    FROM transactions 
    WHERE created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY DATE_TRUNC('hour', created_at)
)
SELECT 
    hour_group,
    total_transactions,
    transactions_with_operator,
    ROUND(transactions_with_operator * 100.0 / total_transactions, 2) as operator_coverage_pct,
    good_quality_transactions,
    ROUND(good_quality_transactions * 100.0 / total_transactions, 2) as quality_pct,
    valid_pump_readings,
    ROUND(valid_pump_readings * 100.0 / total_transactions, 2) as valid_readings_pct
FROM transaction_stats
ORDER BY hour_group DESC
LIMIT 24;
EOF

echo "24小时修复效果报告："
# psql -h localhost -U fcc_user -d fcc_db -f /tmp/generate_fix_report.sql

echo ""
echo "🧹 8. 清理临时文件..."
rm -f /tmp/check_recent_transactions.sql
rm -f /tmp/check_duplicate_transactions.sql
rm -f /tmp/check_operator_id_coverage.sql
rm -f /tmp/check_counter_quality.sql
rm -f /tmp/check_abnormal_counters.sql
rm -f /tmp/generate_fix_report.sql

echo ""
echo "✅ 验证脚本执行完成"
echo ""
echo "📋 使用说明："
echo "1. 请根据实际数据库连接信息，取消注释并修改 psql 命令"
echo "2. 运行脚本前确保数据库连接正常"
echo "3. 观察日志中的持久化相关信息，查看是否有 '✅ Transaction persisted successfully' 消息"
echo "4. 如果仍有问题，请检查 TransactionService 和 NozzleService 的配置"
echo ""
echo "🔧 常见问题排查："
echo "- 如果看到 'No transaction service available'，检查服务初始化"
echo "- 如果看到持久化错误，检查数据库连接和权限"
echo "- 如果员工ID仍然缺失，检查授权命令的执行时机" 