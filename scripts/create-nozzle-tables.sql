-- FCC Service Nozzle 层级支持 - 数据库迁移脚本
-- 创建 Nozzle 和 FuelGrade 表结构

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 油品等级表
CREATE TABLE IF NOT EXISTS fuel_grades (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL, -- gasoline, diesel, etc.
    octane INTEGER DEFAULT 0,
    description TEXT,
    color VARCHAR(50),
    price DECIMAL(8,4) NOT NULL DEFAULT 0 CHECK (price >= 0), 
    currency VARCHAR(10) DEFAULT 'CNY',
    
    -- 元数据
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1
);

-- 喷嘴表 - 对应Wayne协议的Logical Nozzle
CREATE TABLE IF NOT EXISTS nozzles (
    id VARCHAR(255) PRIMARY KEY,
    number SMALLINT NOT NULL CHECK (number >= 1 AND number <= 15), -- Wayne协议喷嘴编号 1-15
    name VARCHAR(255),
    device_id VARCHAR(255) NOT NULL,
    
    -- 状态信息
    status VARCHAR(50) NOT NULL DEFAULT 'idle' CHECK (status IN ('idle', 'selected', 'out', 'filling', 'suspended', 'maintenance', 'error')),
    is_out BOOLEAN DEFAULT FALSE,
    is_selected BOOLEAN DEFAULT FALSE,
    
    -- 油品和价格配置
    fuel_grade_id VARCHAR(255),
    current_price DECIMAL(8,4) DEFAULT 0 CHECK (current_price >= 0),
    
    -- 交易统计
    total_volume DECIMAL(12,3) DEFAULT 0 CHECK (total_volume >= 0),
    total_amount DECIMAL(12,2) DEFAULT 0 CHECK (total_amount >= 0),
    transaction_count BIGINT DEFAULT 0,
    
    -- 当前交易数据
    current_volume DECIMAL(10,3) DEFAULT 0 CHECK (current_volume >= 0),
    current_amount DECIMAL(10,2) DEFAULT 0 CHECK (current_amount >= 0),
    
    -- 预设值
    preset_volume DECIMAL(10,3) CHECK (preset_volume IS NULL OR preset_volume >= 0),
    preset_amount DECIMAL(10,2) CHECK (preset_amount IS NULL OR preset_amount >= 0),
    
    -- 物理配置
    position VARCHAR(100),
    hose_length INTEGER DEFAULT 0,
    is_enabled BOOLEAN DEFAULT TRUE,
    
    -- 元数据
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    
    -- 外键约束
    FOREIGN KEY (fuel_grade_id) REFERENCES fuel_grades(id) ON DELETE SET NULL,
    
    -- 唯一约束：同一设备的喷嘴编号唯一
    UNIQUE(device_id, number)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_fuel_grades_type ON fuel_grades(type);
CREATE INDEX IF NOT EXISTS idx_fuel_grades_price ON fuel_grades(price);
CREATE INDEX IF NOT EXISTS idx_fuel_grades_created_at ON fuel_grades(created_at);

CREATE INDEX IF NOT EXISTS idx_nozzles_device_id ON nozzles(device_id);
CREATE INDEX IF NOT EXISTS idx_nozzles_number ON nozzles(number);
CREATE INDEX IF NOT EXISTS idx_nozzles_status ON nozzles(status);
CREATE INDEX IF NOT EXISTS idx_nozzles_fuel_grade_id ON nozzles(fuel_grade_id);
CREATE INDEX IF NOT EXISTS idx_nozzles_is_enabled ON nozzles(is_enabled);
CREATE INDEX IF NOT EXISTS idx_nozzles_device_number ON nozzles(device_id, number);
CREATE INDEX IF NOT EXISTS idx_nozzles_updated_at ON nozzles(updated_at);

-- 创建更新时间触发器
DROP TRIGGER IF EXISTS update_fuel_grades_updated_at ON fuel_grades;
CREATE TRIGGER update_fuel_grades_updated_at
    BEFORE UPDATE ON fuel_grades
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_nozzles_updated_at ON nozzles;
CREATE TRIGGER update_nozzles_updated_at
    BEFORE UPDATE ON nozzles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入示例油品等级数据
INSERT INTO fuel_grades (id, name, type, octane, description, color, price, currency) VALUES
    ('grade_92', '92号汽油', 'gasoline', 92, '标准92号无铅汽油', '#ff6b6b', 7.50, 'CNY'),
    ('grade_95', '95号汽油', 'gasoline', 95, '高标号95号无铅汽油', '#4ecdc4', 8.00, 'CNY'),
    ('grade_98', '98号汽油', 'gasoline', 98, '超高标号98号无铅汽油', '#45b7d1', 8.50, 'CNY'),
    ('grade_0_diesel', '0号柴油', 'diesel', 0, '标准0号柴油', '#96ceb4', 6.80, 'CNY'),
    ('grade_minus10_diesel', '-10号柴油', 'diesel', 0, '低温-10号柴油', '#feca57', 7.00, 'CNY')
ON CONFLICT (id) DO NOTHING;

-- 为示例设备创建喷嘴数据
-- 注意：这里假设已有devices表中的pump设备
DO $$
BEGIN
    -- 为pump001创建2个喷嘴 (假设COM7测试设备)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'devices') THEN
        INSERT INTO nozzles (id, number, name, device_id, fuel_grade_id, current_price, position, is_enabled) VALUES
            ('device_com7_pump01-nozzle-1', 1, '1号油枪', 'device_com7_pump01', 'grade_92', 7.50, 'left', TRUE),
            ('device_com7_pump01-nozzle-2', 2, '2号油枪', 'device_com7_pump01', 'grade_95', 8.00, 'right', TRUE)
        ON CONFLICT (device_id, number) DO NOTHING;
        
        -- 为pump002创建2个喷嘴
        INSERT INTO nozzles (id, number, name, device_id, fuel_grade_id, current_price, position, is_enabled) VALUES
            ('nozzle_pump002_1', 1, '1号油枪', 'pump002', 'grade_92', 7.50, 'left', TRUE),
            ('nozzle_pump002_2', 2, '2号油枪', 'pump002', 'grade_0_diesel', 6.80, 'right', TRUE)
        ON CONFLICT (device_id, number) DO NOTHING;
        
        -- 为pump003创建3个喷嘴
        INSERT INTO nozzles (id, number, name, device_id, fuel_grade_id, current_price, position, is_enabled) VALUES
            ('nozzle_pump003_1', 1, '1号油枪', 'pump003', 'grade_92', 7.50, 'left', TRUE),
            ('nozzle_pump003_2', 2, '2号油枪', 'pump003', 'grade_95', 8.00, 'center', TRUE),
            ('nozzle_pump003_3', 3, '3号油枪', 'pump003', 'grade_98', 8.50, 'right', TRUE)
        ON CONFLICT (device_id, number) DO NOTHING;
    END IF;
END $$;

-- 创建喷嘴价格缓存视图 (便于Wayne适配器快速查询)
CREATE OR REPLACE VIEW nozzle_prices_cache AS
SELECT 
    n.device_id,
    n.number as nozzle_number,
    n.current_price,
    n.is_enabled,
    fg.name as fuel_grade_name,
    fg.type as fuel_type,
    n.updated_at as price_updated_at
FROM nozzles n
LEFT JOIN fuel_grades fg ON n.fuel_grade_id = fg.id
WHERE n.is_enabled = TRUE
ORDER BY n.device_id, n.number;

-- 创建设备喷嘴统计视图
CREATE OR REPLACE VIEW device_nozzle_stats AS
SELECT 
    device_id,
    COUNT(*) as total_nozzles,
    COUNT(CASE WHEN is_enabled THEN 1 END) as enabled_nozzles,
    COUNT(CASE WHEN status = 'filling' THEN 1 END) as filling_nozzles,
    COUNT(CASE WHEN is_out THEN 1 END) as out_nozzles,
    AVG(current_price) as avg_price,
    SUM(total_volume) as total_volume,
    SUM(total_amount) as total_amount,
    SUM(transaction_count) as total_transactions
FROM nozzles
GROUP BY device_id;

-- 注释说明
COMMENT ON TABLE fuel_grades IS '油品等级表 - 定义不同类型和标号的燃油';
COMMENT ON TABLE nozzles IS '喷嘴表 - 对应Wayne协议的Logical Nozzle，支持1-15编号';

COMMENT ON COLUMN nozzles.number IS 'Wayne协议喷嘴编号 (1-15)';
COMMENT ON COLUMN nozzles.current_price IS '当前价格，用于CD5价格更新命令';
COMMENT ON COLUMN nozzles.is_out IS 'DC3事务中的NOZIO bit 4 - 喷嘴拔出状态';
COMMENT ON COLUMN nozzles.is_selected IS 'DC3事务中的NOZIO bits 0-3 - 喷嘴选中状态';

COMMENT ON VIEW nozzle_prices_cache IS '喷嘴价格缓存视图 - 用于Wayne适配器CD5命令快速查询所有喷嘴价格';
COMMENT ON VIEW device_nozzle_stats IS '设备喷嘴统计视图 - 提供设备级别的喷嘴汇总信息'; 