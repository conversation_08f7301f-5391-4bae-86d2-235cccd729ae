# FCC COM7 Wayne Device Simulator
# Windows PowerShell脚本，为COM7端口创建Wayne DART设备模拟器

param(
    [string]$ComPort = "COM7",
    [string]$BaudRate = "9600",
    [int]$DeviceAddress = 80  # 0x50
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 颜色函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Blue "[INFO] $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

# 检查Go环境
function Test-GoEnvironment {
    Write-Info "检查Go环境..."
    
    try {
        $goVersion = go version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Go环境正常: $goVersion"
            return $true
        } else {
            Write-Error "未找到Go环境，请先安装Go"
            return $false
        }
    } catch {
        Write-Error "检查Go环境失败: $($_.Exception.Message)"
        return $false
    }
}

# 检查COM端口可用性
function Test-ComPortAvailable {
    param([string]$Port)
    
    Write-Info "检查COM端口可用性: $Port"
    
    try {
        $ports = [System.IO.Ports.SerialPort]::GetPortNames()
        if ($ports -contains $Port) {
            Write-Warning "端口 $Port 已存在，将尝试使用"
            return $true
        } else {
            Write-Info "端口 $Port 不存在，将创建虚拟端口"
            return $false
        }
    } catch {
        Write-Error "检查COM端口时出错: $($_.Exception.Message)"
        return $false
    }
}

# 创建Wayne设备模拟器
function Start-WayneSimulator {
    param(
        [string]$Port,
        [string]$BaudRate,
        [int]$Address
    )
    
    Write-Info "启动Wayne DART设备模拟器..."
    Write-Info "端口: $Port, 波特率: $BaudRate, 设备地址: $Address (0x$('{0:X2}' -f $Address))"
    
    # 创建模拟器Go代码
    $simulatorCode = @"
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    "os"
    "os/signal"
    "syscall"

    "go.bug.st/serial"
)

// DART协议常量
const (
    DLE = 0x10
    ACK = 0x06
    NAK = 0x15
    STX = 0x02
    ETX = 0x03
)

// 设备状态
type DeviceStatus byte
const (
    StatusPumpNotProgrammed DeviceStatus = 0x00
    StatusReset            DeviceStatus = 0x01
    StatusAuthorized       DeviceStatus = 0x02
    StatusFilling          DeviceStatus = 0x03
    StatusFillingCompleted DeviceStatus = 0x04
)

// Mock设备
type MockDevice struct {
    Address   byte
    Status    DeviceStatus
    Volume    float64
    Amount    float64
    Price     float64
    NozzlePos byte
}

func NewMockDevice(address byte) *MockDevice {
    return &MockDevice{
        Address:   address,
        Status:    StatusPumpNotProgrammed,
        Volume:    0.0,
        Amount:    0.0,
        Price:     1.459, // 默认油价
        NozzlePos: 1,
    }
}

// CRC计算
func calculateCRC(data []byte) byte {
    crc := byte(0x00)
    for _, b := range data {
        crc ^= b
        for i := 0; i < 8; i++ {
            if crc&0x80 != 0 {
                crc = (crc << 1) ^ 0x07
            } else {
                crc <<= 1
            }
        }
    }
    return crc
}

// 创建ACK响应
func createACK() []byte {
    return []byte{DLE, ACK}
}

// 创建NAK响应
func createNAK() []byte {
    return []byte{DLE, NAK}
}

// 创建DC1响应（状态数据）
func (d *MockDevice) createDC1Response() []byte {
    // 构建DC1响应数据
    responseData := []byte{
        byte(d.Status),    // 泵状态
        d.NozzlePos,       // 喷嘴位置
        0x00, 0x00,        // 错误代码
    }
    
    // 构建完整帧
    frame := []byte{DLE, STX, d.Address, 0xD1} // DC1响应
    frame = append(frame, responseData...)
    frame = append(frame, DLE, ETX)
    
    // 计算并添加CRC
    crcData := frame[2:len(frame)-2] // 地址到ETX之前的数据
    crc := calculateCRC(crcData)
    frame = append(frame, crc)
    
    return frame
}

// 处理DART帧
func (d *MockDevice) processDARTFrame(data []byte) []byte {
    log.Printf("收到数据: %X", data)
    
    // 基本长度检查
    if len(data) < 5 {
        log.Printf("数据长度不足: %d", len(data))
        return createNAK()
    }
    
    // 检查DLE STX
    if data[0] != DLE || data[1] != STX {
        log.Printf("无效的帧头: %02X %02X", data[0], data[1])
        return createNAK()
    }
    
    // 提取地址和命令
    address := data[2]
    command := data[3]
    
    // 检查地址匹配
    if address != d.Address {
        log.Printf("地址不匹配: 收到 %02X, 期望 %02X", address, d.Address)
        return nil // 忽略不匹配的地址
    }
    
    log.Printf("处理命令: 地址=%02X, 命令=%02X", address, command)
    
    // 根据命令处理
    switch command {
    case 0xC1: // CD1 - 状态查询/控制命令
        if len(data) >= 6 {
            subCommand := data[4]
            log.Printf("CD1子命令: %02X", subCommand)
            
            switch subCommand {
            case 0x00: // 状态查询
                log.Printf("设备 %02X 状态查询", address)
                return d.createDC1Response()
            case 0x01: // 重置
                log.Printf("设备 %02X 重置", address)
                d.Status = StatusReset
                return createACK()
            case 0x02: // 授权
                log.Printf("设备 %02X 授权", address)
                if d.Status == StatusReset {
                    d.Status = StatusAuthorized
                }
                return createACK()
            case 0x03: // 停止
                log.Printf("设备 %02X 停止", address)
                if d.Status == StatusFilling {
                    d.Status = StatusFillingCompleted
                }
                return createACK()
            default:
                log.Printf("未知CD1子命令: %02X", subCommand)
                return createNAK()
            }
        }
        return createNAK()
        
    default:
        log.Printf("未知命令: %02X", command)
        return createNAK()
    }
}

func main() {
    port := "$Port"
    baudRate := $BaudRate
    deviceAddr := byte($Address)
    
    log.Printf("启动Wayne DART设备模拟器")
    log.Printf("端口: %s, 波特率: %d, 设备地址: 0x%02X", port, baudRate, deviceAddr)
    
    // 创建模拟设备
    device := NewMockDevice(deviceAddr)
    
    // 配置串口
    mode := &serial.Mode{
        BaudRate: baudRate,
        DataBits: 8,
        StopBits: serial.OneStopBit,
        Parity:   serial.OddParity, // DART协议要求奇校验
    }
    
    // 打开串口
    serialPort, err := serial.Open(port, mode)
    if err != nil {
        log.Fatalf("无法打开串口 %s: %v", port, err)
    }
    defer serialPort.Close()
    
    log.Printf("串口 %s 已打开，设备就绪", port)
    
    // 创建信号处理
    ctx, cancel := context.WithCancel(context.Background())
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
    
    go func() {
        <-sigChan
        log.Println("收到停止信号")
        cancel()
    }()
    
    // 主循环
    buffer := make([]byte, 256)
    
    log.Println("Wayne DART设备模拟器运行中，等待命令...")
    
    for {
        select {
        case <-ctx.Done():
            log.Println("设备模拟器停止")
            return
        default:
            // 设置读取超时
            serialPort.SetReadTimeout(100 * time.Millisecond)
            
            // 读取数据
            n, err := serialPort.Read(buffer)
            if err != nil {
                // 超时或其他错误，继续循环
                continue
            }
            
            if n > 0 {
                log.Printf("收到 %d 字节数据", n)
                
                // 处理DART帧
                response := device.processDARTFrame(buffer[:n])
                if response != nil {
                    log.Printf("发送响应: %X", response)
                    
                    // 发送响应
                    _, err := serialPort.Write(response)
                    if err != nil {
                        log.Printf("发送响应失败: %v", err)
                    } else {
                        log.Printf("响应已发送")
                    }
                }
            }
        }
    }
}
"@

    # 保存模拟器代码到临时文件
    $tempFile = "wayne_simulator_com7.go"
    $simulatorCode | Out-File -FilePath $tempFile -Encoding UTF8
    
    # 初始化Go模块（如果需要）
    if (-not (Test-Path "go.mod")) {
        Write-Info "初始化Go模块..."
        go mod init wayne-simulator 2>$null
        go mod tidy 2>$null
    }
    
    # 运行模拟器
    Write-Success "启动Wayne DART设备模拟器"
    Write-Info "设备地址: 0x$('{0:X2}' -f $Address) ($Address)"
    Write-Info "按 Ctrl+C 停止模拟器"
    Write-Host ""
    
    try {
        go run $tempFile
    } catch {
        Write-Error "模拟器运行失败: $($_.Exception.Message)"
    } finally {
        # 清理临时文件
        if (Test-Path $tempFile) {
            Remove-Item $tempFile -Force
        }
    }
}

# 主函数
function Main {
    Write-Host "======================================" -ForegroundColor Magenta
    Write-Host "🎯 Wayne DART Device Simulator (COM7)" -ForegroundColor Magenta
    Write-Host "======================================" -ForegroundColor Magenta
    Write-Host ""
    
    # 检查Go环境
    if (-not (Test-GoEnvironment)) {
        return
    }
    
    # 检查端口
    Test-ComPortAvailable -Port $ComPort
    
    # 启动模拟器
    Start-WayneSimulator -Port $ComPort -BaudRate $BaudRate -Address $DeviceAddress
    
    Write-Host ""
    Write-Success "Wayne设备模拟器已停止"
}

# 执行主函数
Main 