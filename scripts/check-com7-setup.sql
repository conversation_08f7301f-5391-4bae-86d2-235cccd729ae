-- COM7设备配置检查脚本
-- 使用方法: psql -h localhost -p 5433 -U fcc -d fcc -f scripts/check-com7-setup.sql

\echo '=== COM7设备配置检查 ==='
\echo ''

-- 1. 检查控制器是否存在
\echo '1. 检查控制器配置:'
SELECT 
    id as "控制器ID",
    name as "控制器名称", 
    protocol as "协议类型",
    address as "地址",
    (config->>'serial_port') as "串口",
    (config->>'baud_rate')::int as "波特率",
    status as "状态",
    health as "健康状态"
FROM controllers 
WHERE id = 'controller_com7';

-- 2. 检查设备是否存在
\echo ''
\echo '2. 检查设备配置:'
SELECT 
    id as "设备ID",
    name as "设备名称",
    type as "设备类型", 
    device_address as "DART地址",
    status as "状态",
    health as "健康状态"
FROM devices 
WHERE controller_id = 'controller_com7';

-- 3. 检查控制器和设备关联
\echo ''
\echo '3. 检查控制器-设备关联:'
SELECT 
    c.id as "控制器ID",
    c.name as "控制器名称",
    c.protocol as "协议",
    d.id as "设备ID", 
    d.name as "设备名称",
    d.device_address as "DART地址",
    d.status as "设备状态"
FROM controllers c
LEFT JOIN devices d ON c.id = d.controller_id
WHERE c.id = 'controller_com7'
ORDER BY d.device_address;

-- 4. 检查是否缺少数据
\echo ''
\echo '4. 配置状态检查:'

DO $$ 
DECLARE
    controller_count int;
    device_count int;
BEGIN
    -- 检查控制器
    SELECT count(*) INTO controller_count FROM controllers WHERE id = 'controller_com7';
    
    IF controller_count = 0 THEN
        RAISE NOTICE '❌ 控制器 controller_com7 不存在！';
        RAISE NOTICE '请运行: psql -h localhost -p 5433 -U fcc -d fcc -f scripts/config-com7-device.sql';
    ELSE
        RAISE NOTICE '✅ 控制器 controller_com7 存在';
    END IF;
    
    -- 检查设备
    SELECT count(*) INTO device_count FROM devices WHERE controller_id = 'controller_com7';
    
    IF device_count = 0 THEN
        RAISE NOTICE '❌ 控制器 controller_com7 下没有设备！';
    ELSE
        RAISE NOTICE '✅ 控制器 controller_com7 下有 % 个设备', device_count;
    END IF;
    
    -- 总结
    IF controller_count > 0 AND device_count > 0 THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 COM7配置检查通过！可以启动FCC服务';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️  COM7配置不完整，请先配置数据库';
    END IF;
END $$;

\echo ''
\echo '=== 检查完成 ===' 