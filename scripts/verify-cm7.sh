#!/bin/bash

# CM7 Device Verification Script
# 用于验证CM7设备连接和FCC系统功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# FCC服务基础URL
FCC_BASE_URL="http://localhost:8080/api/v1"

# 检查FCC服务状态
check_fcc_service() {
    log_info "检查FCC服务状态..."
    
    response=$(curl -s -w "%{http_code}" "$FCC_BASE_URL/health" 2>/dev/null || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "FCC服务运行正常"
        return 0
    else
        log_error "FCC服务无响应 (HTTP: $http_code)"
        log_info "请先运行: ./scripts/start-cm7.sh"
        return 1
    fi
}

# 检查设备发现
check_device_discovery() {
    log_info "检查设备发现功能..."
    
    # 触发设备发现
    response=$(curl -s -w "%{http_code}" -X POST "$FCC_BASE_URL/devices/discover" 2>/dev/null || echo "000")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "设备发现API调用成功"
        
        # 等待设备发现完成
        sleep 5
        
        # 获取设备列表
        devices_response=$(curl -s "$FCC_BASE_URL/devices" 2>/dev/null)
        device_count=$(echo "$devices_response" | jq '.data | length' 2>/dev/null || echo "0")
        
        if [ "$device_count" -gt "0" ]; then
            log_success "发现 $device_count 个设备"
            
            # 显示设备详情
            echo "$devices_response" | jq '.data[] | {id: .id, name: .name, type: .type, status: .status}' 2>/dev/null || echo "设备信息解析失败"
            return 0
        else
            log_warning "未发现任何设备"
            return 1
        fi
    else
        log_error "设备发现失败 (HTTP: $http_code)"
        return 1
    fi
}

# 检查轮询状态
check_polling_status() {
    log_info "检查轮询调度器状态..."
    
    response=$(curl -s "$FCC_BASE_URL/polling/status" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        is_running=$(echo "$response" | jq '.data.is_running' 2>/dev/null)
        total_devices=$(echo "$response" | jq '.data.total_devices' 2>/dev/null)
        total_polls=$(echo "$response" | jq '.data.total_polls' 2>/dev/null)
        successful_polls=$(echo "$response" | jq '.data.successful_polls' 2>/dev/null)
        
        if [ "$is_running" = "true" ]; then
            log_success "轮询调度器正在运行"
            echo "  - 总设备数: $total_devices"
            echo "  - 总轮询次数: $total_polls"
            echo "  - 成功轮询: $successful_polls"
            return 0
        else
            log_warning "轮询调度器未运行"
            return 1
        fi
    else
        log_error "无法获取轮询状态"
        return 1
    fi
}

# 检查适配器统计
check_adapter_statistics() {
    log_info "检查适配器统计信息..."
    
    response=$(curl -s "$FCC_BASE_URL/adapters/statistics" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        frames_received=$(echo "$response" | jq '.data.frames_received' 2>/dev/null || echo "N/A")
        frames_processed=$(echo "$response" | jq '.data.frames_processed' 2>/dev/null || echo "N/A")
        frame_errors=$(echo "$response" | jq '.data.frame_errors' 2>/dev/null || echo "N/A")
        is_listening=$(echo "$response" | jq '.data.is_listening' 2>/dev/null || echo "false")
        
        if [ "$is_listening" = "true" ]; then
            log_success "连接监听器工作正常"
            echo "  - 接收帧数: $frames_received"
            echo "  - 处理帧数: $frames_processed"
            echo "  - 错误帧数: $frame_errors"
            return 0
        else
            log_warning "连接监听器未工作"
            return 1
        fi
    else
        log_error "无法获取适配器统计"
        return 1
    fi
}

# 检查业务引擎状态
check_business_engine() {
    log_info "检查业务引擎状态..."
    
    response=$(curl -s "$FCC_BASE_URL/statemachine/statistics" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        active_devices=$(echo "$response" | jq '.data.active_devices' 2>/dev/null || echo "0")
        total_events=$(echo "$response" | jq '.data.total_events' 2>/dev/null || echo "0")
        processed_events=$(echo "$response" | jq '.data.processed_events' 2>/dev/null || echo "0")
        
        log_success "业务引擎运行正常"
        echo "  - 活跃设备: $active_devices"
        echo "  - 总事件数: $total_events"
        echo "  - 已处理事件: $processed_events"
        return 0
    else
        log_error "无法获取业务引擎状态"
        return 1
    fi
}

# 测试设备命令
test_device_commands() {
    log_info "测试设备命令功能..."
    
    # 获取第一个设备ID
    devices_response=$(curl -s "$FCC_BASE_URL/devices" 2>/dev/null)
    device_id=$(echo "$devices_response" | jq -r '.data[0].id' 2>/dev/null)
    
    if [ "$device_id" != "null" ] && [ -n "$device_id" ]; then
        log_info "测试设备: $device_id"
        
        # 测试设备状态查询
        status_response=$(curl -s "$FCC_BASE_URL/devices/$device_id/status" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            device_status=$(echo "$status_response" | jq -r '.data.status' 2>/dev/null)
            log_success "设备状态查询成功: $device_status"
            return 0
        else
            log_error "设备状态查询失败"
            return 1
        fi
    else
        log_warning "未找到可用设备进行测试"
        return 1
    fi
}

# 检查串口连接
check_serial_connection() {
    log_info "检查串口连接..."
    
    # 检查串口设备
    if [ -e "/dev/ttyUSB0" ]; then
        log_success "串口设备 /dev/ttyUSB0 存在"
        
        # 检查权限
        if [ -w "/dev/ttyUSB0" ]; then
            log_success "串口设备权限正常"
        else
            log_warning "串口设备权限不足，可能需要添加用户到dialout组"
        fi
        return 0
    else
        log_error "未找到串口设备 /dev/ttyUSB0"
        
        # 查找其他串口设备
        ports=$(ls /dev/tty{USB,ACM}* 2>/dev/null || true)
        if [ -n "$ports" ]; then
            log_info "发现其他串口设备:"
            echo "$ports"
        fi
        return 1
    fi
}

# 生成验证报告
generate_report() {
    echo ""
    echo "======================================"
    echo "📋 CM7设备验证报告"
    echo "======================================"
    echo ""
    
    # 统计结果
    local total_tests=7
    local passed_tests=$1
    local failed_tests=$((total_tests - passed_tests))
    
    echo "总测试项: $total_tests"
    echo "✅ 通过: $passed_tests"
    echo "❌ 失败: $failed_tests"
    echo ""
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "🎉 所有验证项目都通过了！CM7设备工作正常"
        echo ""
        echo "建议后续操作:"
        echo "1. 访问 http://localhost:8080 使用Web管理界面"
        echo "2. 使用API进行设备管理和监控"
        echo "3. 查看实时日志: tail -f fcc-server.log"
    elif [ $passed_tests -gt $((total_tests / 2)) ]; then
        log_warning "⚠️  部分验证项目失败，请检查相关配置"
        echo ""
        echo "建议排查步骤:"
        echo "1. 检查CM7设备连接"
        echo "2. 验证配置文件设置"
        echo "3. 查看系统日志排查问题"
    else
        log_error "💥 多数验证项目失败，系统可能存在严重问题"
        echo ""
        echo "紧急排查步骤:"
        echo "1. 重启FCC服务: ./scripts/stop-fcc.sh && ./scripts/start-cm7.sh"
        echo "2. 检查系统依赖: PostgreSQL, Redis"
        echo "3. 验证CM7硬件连接"
    fi
    
    echo ""
}

# 主函数
main() {
    echo "======================================"
    echo "🔍 CM7 Device Verification Script"
    echo "======================================"
    echo ""
    
    passed_tests=0
    
    # 执行所有验证项目
    if check_serial_connection; then ((passed_tests++)); fi
    echo ""
    
    if check_fcc_service; then ((passed_tests++)); fi
    echo ""
    
    if check_device_discovery; then ((passed_tests++)); fi
    echo ""
    
    if check_polling_status; then ((passed_tests++)); fi
    echo ""
    
    if check_adapter_statistics; then ((passed_tests++)); fi
    echo ""
    
    if check_business_engine; then ((passed_tests++)); fi
    echo ""
    
    if test_device_commands; then ((passed_tests++)); fi
    echo ""
    
    # 生成报告
    generate_report $passed_tests
}

# 检查jq工具
if ! command -v jq &> /dev/null; then
    log_warning "建议安装jq工具以获得更好的输出格式:"
    echo "Ubuntu/Debian: sudo apt-get install jq"
    echo "CentOS/RHEL: sudo yum install jq"
    echo "macOS: brew install jq"
    echo ""
fi

main "$@" 