-- FCC Service Nozzle Status Constraint Verification
-- 验证所有喷嘴状态约束是否正确工作

-- 显示当前约束定义
\echo '=== Current Nozzle Status Constraint ==='
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conname = 'nozzles_status_check';

\echo ''
\echo '=== Testing All Valid Statuses ==='

-- 清理可能存在的测试数据
DELETE FROM nozzles WHERE device_id = 'constraint_test_device';

-- 测试所有有效状态
DO $$
DECLARE
    statuses text[] := ARRAY['idle', 'selected', 'authorized', 'out', 'filling', 'completed', 'suspended', 'maintenance', 'error'];
    status_name text;
    test_count integer := 0;
    success_count integer := 0;
BEGIN
    RAISE NOTICE 'Testing all valid nozzle statuses...';
    RAISE NOTICE '';
    
    FOREACH status_name IN ARRAY statuses
    LOOP
        test_count := test_count + 1;
        
        BEGIN
            -- 尝试插入每个状态
            INSERT INTO nozzles (id, number, device_id, status, current_price) 
            VALUES (
                'test_' || status_name, 
                test_count, 
                'constraint_test_device', 
                status_name::varchar(50), 
                0
            );
            
            success_count := success_count + 1;
            RAISE NOTICE '✓ Status ''%'' - SUCCESS', status_name;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '✗ Status ''%'' - FAILED: %', status_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Test Results: % of % statuses passed', success_count, test_count;
    
    IF success_count = test_count THEN
        RAISE NOTICE '🎉 ALL TESTS PASSED - Constraint fix is working correctly!';
    ELSE
        RAISE NOTICE '❌ Some tests failed - constraint may need further adjustment';
    END IF;
END $$;

\echo ''
\echo '=== Verification: Check Inserted Records ==='
SELECT number, status, 'SUCCESS' as result 
FROM nozzles 
WHERE device_id = 'constraint_test_device' 
ORDER BY number;

\echo ''
\echo '=== Testing Invalid Status (should fail) ==='
DO $$
BEGIN
    BEGIN
        INSERT INTO nozzles (id, number, device_id, status, current_price) 
        VALUES ('test_invalid', 10, 'constraint_test_device', 'invalid_status', 0);
        
        RAISE NOTICE '❌ UNEXPECTED: Invalid status was accepted!';
        
    EXCEPTION WHEN check_violation THEN
        RAISE NOTICE '✓ EXPECTED: Invalid status correctly rejected: %', SQLERRM;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '? UNEXPECTED ERROR: %', SQLERRM;
    END;
END $$;

\echo ''
\echo '=== DC3 Transaction Scenario Test ==='
DO $$
DECLARE
    nozzle_record record;
BEGIN
    -- 模拟真实的DC3事务场景
    RAISE NOTICE 'Simulating DC3 transaction status changes...';
    
    -- 1. 喷嘴从 idle -> selected
    UPDATE nozzles SET status = 'selected' WHERE device_id = 'constraint_test_device' AND number = 1;
    RAISE NOTICE '✓ Step 1: idle -> selected';
    
    -- 2. 喷嘴从 selected -> out (油枪拔出)
    UPDATE nozzles SET status = 'out' WHERE device_id = 'constraint_test_device' AND number = 1;
    RAISE NOTICE '✓ Step 2: selected -> out';
    
    -- 3. 喷嘴从 out -> filling (开始加油)
    UPDATE nozzles SET status = 'filling' WHERE device_id = 'constraint_test_device' AND number = 1;
    RAISE NOTICE '✓ Step 3: out -> filling';
    
    -- 4. 喷嘴从 filling -> completed (插回油枪，这是触发原始错误的状态)
    UPDATE nozzles SET status = 'completed' WHERE device_id = 'constraint_test_device' AND number = 1;
    RAISE NOTICE '✓ Step 4: filling -> completed (This was causing the original error!)';
    
    -- 5. 喷嘴从 completed -> idle (重置)
    UPDATE nozzles SET status = 'idle' WHERE device_id = 'constraint_test_device' AND number = 1;
    RAISE NOTICE '✓ Step 5: completed -> idle';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 DC3 Transaction Scenario Test PASSED!';
    RAISE NOTICE 'The original error should no longer occur in production.';
    
END $$;

\echo ''
\echo '=== Cleanup Test Data ==='
DELETE FROM nozzles WHERE device_id = 'constraint_test_device';
SELECT 'Test data cleaned up' as cleanup_status;

\echo ''
\echo '=== Summary ==='
\echo 'If all tests above passed, the nozzle status constraint fix is successful.'
\echo 'The following error should no longer occur:'
\echo '  "pq: new row for relation \"nozzles\" violates check constraint \"nozzles_status_check\""'
\echo ''
\echo 'Next steps:'
\echo '1. Monitor FCC service logs for any remaining constraint violations'  
\echo '2. The DC3 transaction processing should now work correctly'
\echo '3. Both \"completed\" and \"authorized\" statuses are now allowed' 