-- 修复 transactions 表缺少的字段
-- 此脚本只添加缺少的字段，不删除现有数据

BEGIN;

-- 检查并添加 controller_id 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'controller_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN controller_id VARCHAR(255);
        RAISE NOTICE 'Added controller_id column';
    ELSE
        RAISE NOTICE 'controller_id column already exists';
    END IF;
END $$;

-- 检查并添加 nozzle_id 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'nozzle_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN nozzle_id VARCHAR(255);
        RAISE NOTICE 'Added nozzle_id column';
    ELSE
        RAISE NOTICE 'nozzle_id column already exists';
    END IF;
END $$;

-- 检查并添加其他可能缺少的字段
DO $$
BEGIN
    -- extra_data 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'extra_data'
    ) THEN
        ALTER TABLE transactions ADD COLUMN extra_data JSONB;
        RAISE NOTICE 'Added extra_data column';
    END IF;
    
    -- fuel_type 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'fuel_type'
    ) THEN
        ALTER TABLE transactions ADD COLUMN fuel_type VARCHAR(100);
        RAISE NOTICE 'Added fuel_type column';
    END IF;
    
    -- operator_id 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'operator_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN operator_id VARCHAR(255);
        RAISE NOTICE 'Added operator_id column';
    END IF;
    
    -- operator_name 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'operator_name'
    ) THEN
        ALTER TABLE transactions ADD COLUMN operator_name VARCHAR(255);
        RAISE NOTICE 'Added operator_name column';
    END IF;
END $$;

-- 更新现有数据的 controller_id
UPDATE transactions 
SET controller_id = CASE 
    WHEN device_id LIKE '%com7%' THEN 'controller_com7'
    WHEN device_id LIKE '%com8%' THEN 'controller_com8'
    WHEN device_id LIKE '%wayne%' THEN 'controller_wayne_dart_001'
    ELSE 'controller_unknown'
END
WHERE controller_id IS NULL;

-- 设置 NOT NULL 约束（如果所有记录都有值）
DO $$
DECLARE
    null_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO null_count FROM transactions WHERE controller_id IS NULL;
    
    IF null_count = 0 THEN
        ALTER TABLE transactions ALTER COLUMN controller_id SET NOT NULL;
        RAISE NOTICE 'Set controller_id NOT NULL constraint';
    ELSE
        RAISE WARNING 'Cannot set NOT NULL constraint: % records have null controller_id', null_count;
    END IF;
END $$;

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_transactions_controller_id ON transactions(controller_id);
CREATE INDEX IF NOT EXISTS idx_transactions_nozzle_id ON transactions(nozzle_id);

-- 验证修复结果
SELECT 
    'transactions' as table_name,
    COUNT(*) as total_records,
    COUNT(controller_id) as has_controller_id,
    COUNT(nozzle_id) as has_nozzle_id,
    COUNT(extra_data) as has_extra_data
FROM transactions;

COMMIT;

SELECT 'Transactions table fix completed!' as status; 