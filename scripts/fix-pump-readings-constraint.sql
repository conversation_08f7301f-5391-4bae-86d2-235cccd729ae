-- ===================================================
-- 移除泵码约束脚本 - 改为业务逻辑验证
-- 问题：数据库约束过于严格，阻止交易入库
-- 解决：完全移除约束，通过业务逻辑保证数据合理性
-- ===================================================

BEGIN;

-- 移除所有pump reading相关的约束
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_volume_readings;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_amount_readings;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_volume_logical;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_amount_logical;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_reading_quality;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_reading_source;

-- 移除其他可能的相关约束
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS check_pump_readings;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_pump_volume_check;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_pump_amount_check;

COMMIT;

-- 验证移除结果
SELECT 
    '✅ 所有泵码约束已移除!' as status,
    '数据验证将通过业务逻辑实现' as approach,
    '支持更灵活的数据处理策略' as benefit,
    CURRENT_TIMESTAMP as removed_at; 