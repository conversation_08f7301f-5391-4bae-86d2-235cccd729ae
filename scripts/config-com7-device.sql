-- 配置COM7设备的SQL脚本
-- 使用方法: psql -h localhost -p 5433 -U fcc -d fcc -f scripts/config-com7-device.sql

-- 1. 配置COM7控制器
INSERT INTO controllers (
    id, 
    name, 
    type, 
    protocol, 
    address, 
    config, 
    station_id, 
    location, 
    status, 
    health,
    metadata,
    created_at,
    updated_at
) VALUES (
    'controller_com7',
    'COM7 DART Controller',
    'dart_controller',
    'wayne_dart',
    'COM7',
    jsonb_build_object(
        'serial_port', 'COM7',
        'baud_rate', 9600,
        'data_bits', 8,
        'stop_bits', 1,
        'parity', 'odd',
        'timeout', 25,
        'read_timeout', 50,
        'write_timeout', 50,
        'max_retries', 3,
        'address_range', jsonb_build_object(
            'min', 80,
            'max', 111
        ),
        'dle_enabled', true,
        'crc_enabled', true
    ),
    'station_001',
    'COM7串口控制器',
    'offline',
    'unknown',
    jsonb_build_object(
        'created_by', 'admin', 
        'purpose', 'COM7设备管理',
        'serial_config', jsonb_build_object(
            'port', 'COM7',
            'description', 'Windows COM7串口'
        )
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)
ON CONFLICT (id) DO UPDATE SET
    config = EXCLUDED.config,
    metadata = EXCLUDED.metadata,
    updated_at = CURRENT_TIMESTAMP;

-- 2. 配置COM7上的第一个设备（地址80，避免与现有设备冲突）
INSERT INTO devices (
    id,
    controller_id,
    name,
    type,
    model,
    vendor,
    device_address,
    config,
    station_id,
    island_id,
    position,
    status,
    health,
    capabilities,
    metadata,
    created_at,
    updated_at
) VALUES (
    'device_com7_pump01',
    'controller_com7',
    'COM7 Fuel Pump 01',
    'fuel_pump',
    'Wayne Ovation',
    'Wayne',
    80,  -- DART地址80 (0x50)
    jsonb_build_object(
        'enabled', true,
        'description', 'COM7上的燃油泵设备',
        'pump_config', jsonb_build_object(
            'nozzle_count', 2,
            'supported_fuels', array['gasoline', 'diesel'],
            'max_flow_rate', 60.0,
            'precision', 3
        )
    ),
    'station_001',
    'island_01',
    'COM7-Pump-01',
    'offline',
    'unknown',
    jsonb_build_object(
        'protocol', 'DART',
        'address_hex', '0x50',
        'dart_features', array['polling', 'data_transfer', 'command_response']
    ),
    jsonb_build_object(
        'serial_port', 'COM7',
        'dart_address', 80,
        'created_by', 'admin'
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)
ON CONFLICT (id) DO UPDATE SET
    config = EXCLUDED.config,
    metadata = EXCLUDED.metadata,
    updated_at = CURRENT_TIMESTAMP;

-- 3. 为wayne_devices表添加设备记录（如果使用Wayne专用表）
INSERT INTO wayne_devices (device_id, address, status, firmware_version) 
VALUES ('device_com7_pump01', 80, 'offline', 'v2.1.0')
ON CONFLICT (device_id) DO UPDATE SET
    address = EXCLUDED.address,
    updated_at = CURRENT_TIMESTAMP;

-- 4. 初始化设备状态
INSERT INTO device_status (device_id, pump_state, nozzle_state, volume, amount, price) 
VALUES ('device_com7_pump01', 0, 0, 0.000, 0.00, 1.450)
ON CONFLICT DO NOTHING;

-- 5. 初始化泵状态
INSERT INTO pump_states (device_id, state, previous_state, metadata) 
VALUES ('device_com7_pump01', 'PUMP_NOT_PROGRAMMED', NULL, jsonb_build_object('initialized', true, 'com_port', 'COM7'))
ON CONFLICT DO NOTHING;

-- 查询配置结果
SELECT 
    c.id as controller_id,
    c.name as controller_name,
    c.address as com_port,
    d.id as device_id,
    d.name as device_name,
    d.device_address as dart_address,
    d.status as device_status
FROM controllers c
LEFT JOIN devices d ON c.id = d.controller_id
WHERE c.id = 'controller_com7'
ORDER BY d.device_address;

-- 显示配置摘要
\echo ''
\echo '=== COM7设备配置完成 ==='
\echo '控制器ID: controller_com7'
\echo '串口: COM7'
\echo '设备ID: device_com7_pump01'
\echo 'DART地址: 80 (0x50)'
\echo '设备类型: 燃油泵'
\echo ''
\echo '使用以下命令检查配置:'
\echo 'SELECT * FROM controllers WHERE id = '"'"'controller_com7'"'"';'
\echo 'SELECT * FROM devices WHERE controller_id = '"'"'controller_com7'"'"';' 