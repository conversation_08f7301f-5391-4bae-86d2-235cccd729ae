-- ===================================================
-- FCC Service - FuelGrade软删除支持迁移脚本
-- 目标：为fuel_grades表添加deleted_at字段，支持软删除功能
-- 作者：FCC开发团队
-- 日期：2024-12-19
-- 版本：v1.0
-- ===================================================

-- 开始迁移
SELECT '开始执行FuelGrade软删除字段迁移' AS message;

-- 检查表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fuel_grades') THEN
        RAISE EXCEPTION 'fuel_grades表不存在，请先执行基础表创建脚本';
    END IF;
END $$;

-- 添加 deleted_at 字段
ALTER TABLE fuel_grades ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP DEFAULT NULL;

-- 添加deleted_at字段的注释
COMMENT ON COLUMN fuel_grades.deleted_at IS '软删除时间戳，NULL表示未删除';

-- 创建deleted_at索引
CREATE INDEX IF NOT EXISTS idx_fuel_grades_deleted_at ON fuel_grades(deleted_at);

-- 创建复合索引：支持查询活跃油品
CREATE INDEX IF NOT EXISTS idx_fuel_grades_active ON fuel_grades(type, deleted_at) WHERE deleted_at IS NULL;

-- 创建复合索引：支持按价格查询活跃油品
CREATE INDEX IF NOT EXISTS idx_fuel_grades_price_active ON fuel_grades(price, deleted_at) WHERE deleted_at IS NULL;

-- 创建复合索引：支持按创建时间查询活跃油品
CREATE INDEX IF NOT EXISTS idx_fuel_grades_created_active ON fuel_grades(created_at DESC, deleted_at) WHERE deleted_at IS NULL;

-- 创建视图：只显示活跃的油品
CREATE OR REPLACE VIEW active_fuel_grades AS
SELECT 
    id,
    name,
    type,
    octane,
    description,
    color,
    price,
    currency,
    metadata,
    created_at,
    updated_at,
    version
FROM fuel_grades
WHERE deleted_at IS NULL;

-- 创建函数：检查油品是否活跃
CREATE OR REPLACE FUNCTION is_fuel_grade_active(grade_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    is_active BOOLEAN;
BEGIN
    SELECT (deleted_at IS NULL) INTO is_active
    FROM fuel_grades
    WHERE id = grade_id;
    
    RETURN COALESCE(is_active, FALSE);
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN FALSE;
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE;

-- 创建函数：软删除油品
CREATE OR REPLACE FUNCTION soft_delete_fuel_grade(grade_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE fuel_grades
    SET deleted_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = grade_id AND deleted_at IS NULL;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows > 0;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：恢复软删除的油品
CREATE OR REPLACE FUNCTION restore_fuel_grade(grade_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE fuel_grades
    SET deleted_at = NULL,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = grade_id AND deleted_at IS NOT NULL;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows > 0;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取油品统计信息
CREATE OR REPLACE FUNCTION get_fuel_grade_stats()
RETURNS TABLE(
    total_count BIGINT,
    active_count BIGINT,
    deleted_count BIGINT,
    last_sync_time TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_count,
        COUNT(*) FILTER (WHERE deleted_at IS NULL) as active_count,
        COUNT(*) FILTER (WHERE deleted_at IS NOT NULL) as deleted_count,
        MAX(updated_at) as last_sync_time
    FROM fuel_grades;
END;
$$ LANGUAGE plpgsql STABLE;

-- 验证迁移结果
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'fuel_grades' AND column_name = 'deleted_at'
        ) THEN '✅ deleted_at字段已添加'
        ELSE '❌ deleted_at字段添加失败'
    END AS deleted_at_status;

-- 验证索引创建
SELECT 
    COUNT(*) AS index_count,
    CASE 
        WHEN COUNT(*) >= 4 THEN '✅ 索引创建成功'
        ELSE '⚠️ 部分索引创建失败'
    END AS index_status
FROM pg_indexes 
WHERE tablename = 'fuel_grades' 
AND indexname IN (
    'idx_fuel_grades_deleted_at',
    'idx_fuel_grades_active', 
    'idx_fuel_grades_price_active',
    'idx_fuel_grades_created_active'
);

-- 验证视图创建
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.views 
            WHERE table_name = 'active_fuel_grades'
        ) THEN '✅ active_fuel_grades视图已创建'
        ELSE '❌ active_fuel_grades视图创建失败'
    END AS view_status;

-- 验证函数创建
SELECT 
    COUNT(*) AS function_count,
    CASE 
        WHEN COUNT(*) >= 4 THEN '✅ 函数创建成功'
        ELSE '⚠️ 部分函数创建失败'
    END AS function_status
FROM information_schema.routines 
WHERE routine_name IN (
    'is_fuel_grade_active',
    'soft_delete_fuel_grade',
    'restore_fuel_grade',
    'get_fuel_grade_stats'
);

-- 显示当前油品统计
SELECT 
    '当前油品统计:' AS info,
    total_count,
    active_count,
    deleted_count,
    last_sync_time
FROM get_fuel_grade_stats();

SELECT '✅ FuelGrade软删除支持迁移完成' AS message; 