-- ===================================================
-- FCC Service - 交易表添加油品字段迁移脚本
-- 目标：为transactions表添加fuel_grade_id和fuel_grade_name字段
-- 作者：FCC开发团队
-- 日期：2024-12-19
-- 版本：v1.4 (最简化版本)
-- ===================================================

-- 开始迁移
SELECT '开始执行油品字段迁移' AS message;

-- 添加 fuel_grade_id 字段
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS fuel_grade_id VARCHAR(255);

-- 添加 fuel_grade_name 字段
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS fuel_grade_name VARCHAR(255);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_transactions_fuel_grade_id ON transactions(fuel_grade_id);
CREATE INDEX IF NOT EXISTS idx_transactions_fuel_grade_name ON transactions(fuel_grade_name);
CREATE INDEX IF NOT EXISTS idx_transactions_fuel_grade_device_time ON transactions(fuel_grade_id, device_id, created_at DESC);

-- 创建更新函数
CREATE OR REPLACE FUNCTION update_fuel_grade_name_from_id(grade_id VARCHAR(255))
RETURNS VARCHAR(255) AS $$
DECLARE
    grade_name VARCHAR(255);
BEGIN
    IF grade_id IS NULL THEN
        RETURN NULL;
    END IF;
    
    SELECT name INTO grade_name FROM fuel_grades WHERE id = grade_id;
    RETURN COALESCE(grade_name, 'Unknown Fuel Grade');
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Unknown Fuel Grade';
END;
$$ LANGUAGE plpgsql STABLE;

-- 创建触发器函数
CREATE OR REPLACE FUNCTION sync_fuel_grade_name()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.fuel_grade_id IS NOT NULL AND (
        OLD.fuel_grade_id IS NULL OR 
        OLD.fuel_grade_id != NEW.fuel_grade_id
    ) THEN
        NEW.fuel_grade_name := update_fuel_grade_name_from_id(NEW.fuel_grade_id);
    END IF;
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 删除旧触发器（如果存在）
DROP TRIGGER IF EXISTS trigger_sync_fuel_grade_name ON transactions;

-- 创建新触发器
CREATE TRIGGER trigger_sync_fuel_grade_name
    BEFORE INSERT OR UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION sync_fuel_grade_name();

-- 添加外键约束（如果fuel_grades表存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fuel_grades') THEN
        BEGIN
            ALTER TABLE transactions 
            ADD CONSTRAINT fk_transactions_fuel_grade_id 
            FOREIGN KEY (fuel_grade_id) REFERENCES fuel_grades(id) 
            ON DELETE SET NULL ON UPDATE CASCADE;
        EXCEPTION
            WHEN duplicate_object THEN
                -- 约束已存在，忽略
                NULL;
        END;
    END IF;
END $$;

-- 验证结果
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'transactions' AND column_name = 'fuel_grade_id'
        ) THEN '✅ fuel_grade_id字段已添加'
        ELSE '❌ fuel_grade_id字段添加失败'
    END AS fuel_grade_id_status;

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'transactions' AND column_name = 'fuel_grade_name'
        ) THEN '✅ fuel_grade_name字段已添加'
        ELSE '❌ fuel_grade_name字段添加失败'
    END AS fuel_grade_name_status;

SELECT 
    COUNT(*) AS index_count,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ 索引创建成功'
        ELSE '⚠️ 部分索引创建失败'
    END AS index_status
FROM pg_indexes 
WHERE tablename = 'transactions' 
AND indexname IN (
    'idx_transactions_fuel_grade_id',
    'idx_transactions_fuel_grade_name', 
    'idx_transactions_fuel_grade_device_time'
);

SELECT 
    COUNT(*) AS function_count,
    CASE 
        WHEN COUNT(*) >= 2 THEN '✅ 函数创建成功'
        ELSE '⚠️ 函数创建失败'
    END AS function_status
FROM information_schema.routines 
WHERE routine_name IN ('update_fuel_grade_name_from_id', 'sync_fuel_grade_name');

SELECT 
    COUNT(*) AS trigger_count,
    CASE 
        WHEN COUNT(*) >= 1 THEN '✅ 触发器创建成功'
        ELSE '⚠️ 触发器创建失败'
    END AS trigger_status
FROM information_schema.triggers 
WHERE trigger_name = 'trigger_sync_fuel_grade_name' AND event_object_table = 'transactions';

-- 完成信息
SELECT '🎉 油品字段迁移完成！' AS final_message; 