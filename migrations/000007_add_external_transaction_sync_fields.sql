-- +goose Up
-- 添加外部系统同步字段到transactions表
-- 用于存储外部API返回的交易ID和同步状态跟踪

-- 添加外部交易ID字段，用于存储外部系统返回的ID
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS external_transaction_id VARCHAR(255);

-- 添加同步状态字段，用于跟踪交易的同步状态
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS sync_status VARCHAR(50) DEFAULT 'pending';

-- 为字段添加注释（PostgreSQL语法）
COMMENT ON COLUMN transactions.external_transaction_id IS '外部系统返回的交易ID，用于后续pump-readings更新操作';
COMMENT ON COLUMN transactions.sync_status IS '同步状态：pending(待同步)/synced(已同步)/failed(同步失败)/needs_sync(需要重新同步)';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_transactions_external_transaction_id ON transactions(external_transaction_id);
CREATE INDEX IF NOT EXISTS idx_transactions_sync_status ON transactions(sync_status);

-- 创建复合索引用于同步查询优化
CREATE INDEX IF NOT EXISTS idx_transactions_sync_lookup ON transactions(sync_status, external_transaction_id) 
WHERE external_transaction_id IS NOT NULL;

-- 创建部分索引用于快速查找需要同步的交易
CREATE INDEX IF NOT EXISTS idx_transactions_needs_sync ON transactions(created_at) 
WHERE sync_status IN ('pending', 'failed', 'needs_sync');

-- +goose Down
-- 回滚操作：删除添加的字段和索引

-- 删除索引
DROP INDEX IF EXISTS idx_transactions_needs_sync;
DROP INDEX IF EXISTS idx_transactions_sync_lookup;
DROP INDEX IF EXISTS idx_transactions_sync_status;
DROP INDEX IF EXISTS idx_transactions_external_transaction_id;

-- 删除添加的字段
ALTER TABLE transactions 
DROP COLUMN IF EXISTS sync_status,
DROP COLUMN IF EXISTS external_transaction_id;
