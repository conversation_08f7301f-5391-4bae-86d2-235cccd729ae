-- +goose Up
-- 创建喷嘴累计泵码表
CREATE TABLE nozzle_counters (
    device_id VARCHAR(255) NOT NULL COMMENT '设备ID',
    nozzle_id TINYINT NOT NULL COMMENT '喷嘴编号(1-15)',
    
    -- 累计计数器数据
    volume_counter DECIMAL(15,3) DEFAULT 0 NOT NULL COMMENT '累计体积计数器(升)',
    amount_counter DECIMAL(15,2) DEFAULT 0 NOT NULL COMMENT '累计金额计数器(元)',
    
    -- 数据质量和来源跟踪
    last_dc101_volume_type TINYINT DEFAULT 0 COMMENT '最后收到的体积计数器类型(0x01-0x08)',
    last_dc101_amount_type TINYINT DEFAULT 0 COMMENT '最后收到的金额计数器类型(0x11-0x18)',
    volume_update_count BIGINT DEFAULT 0 COMMENT '体积计数器更新次数',
    amount_update_count BIGINT DEFAULT 0 COMMENT '金额计数器更新次数',
    last_volume_update TIMESTAMP NULL COMMENT '最后体积更新时间',
    last_amount_update TIMESTAMP NULL COMMENT '最后金额更新时间',
    
    -- 数据一致性检查
    is_volume_valid BOOLEAN DEFAULT TRUE COMMENT '体积数据是否有效',
    is_amount_valid BOOLEAN DEFAULT TRUE COMMENT '金额数据是否有效',
    
    -- 异常检测
    volume_anomaly_count INT DEFAULT 0 COMMENT '体积异常次数',
    amount_anomaly_count INT DEFAULT 0 COMMENT '金额异常次数',
    
    -- 时间戳
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    -- 主键和约束
    PRIMARY KEY (device_id, nozzle_id),
    
    -- 检查约束
    CONSTRAINT chk_nozzle_id_range CHECK (nozzle_id >= 1 AND nozzle_id <= 15),
    CONSTRAINT chk_volume_counter_positive CHECK (volume_counter >= 0),
    CONSTRAINT chk_amount_counter_positive CHECK (amount_counter >= 0),
    CONSTRAINT chk_volume_type_range CHECK (last_dc101_volume_type = 0 OR (last_dc101_volume_type >= 0x01 AND last_dc101_volume_type <= 0x08)),
    CONSTRAINT chk_amount_type_range CHECK (last_dc101_amount_type = 0 OR (last_dc101_amount_type >= 0x11 AND last_dc101_amount_type <= 0x18))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='喷嘴累计泵码表';

-- 创建索引
CREATE INDEX idx_nozzle_counters_device_id ON nozzle_counters(device_id);
CREATE INDEX idx_nozzle_counters_last_update ON nozzle_counters(last_update);
CREATE INDEX idx_nozzle_counters_volume_update ON nozzle_counters(last_volume_update);
CREATE INDEX idx_nozzle_counters_amount_update ON nozzle_counters(last_amount_update);
CREATE INDEX idx_nozzle_counters_data_quality ON nozzle_counters(is_volume_valid, is_amount_valid);

-- 添加外键约束（如果devices表存在）
-- ALTER TABLE nozzle_counters 
-- ADD CONSTRAINT fk_nozzle_counters_device 
-- FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE;

-- +goose Down
-- 删除表
DROP TABLE IF EXISTS nozzle_counters; 