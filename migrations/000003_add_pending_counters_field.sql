-- +goose Up
-- 添加缺失的pending_counters_at字段到transactions表

-- 添加pending_counters_at字段，用于处理交易等待计数器更新的状态
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS pending_counters_at TIMESTAMP;

-- 为字段添加注释（PostgreSQL语法）
COMMENT ON COLUMN transactions.pending_counters_at IS 'DC1 FILLING_COMPLETED进入pending_counters状态时间，用于超时处理';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_transactions_pending_counters_at ON transactions(pending_counters_at);

-- 创建复合索引用于超时查询（PostgreSQL中的条件索引）
CREATE INDEX IF NOT EXISTS idx_transactions_pending_timeout ON transactions(status, pending_counters_at) 
WHERE pending_counters_at IS NOT NULL;

-- +goose Down
-- 回滚操作

-- 删除索引
DROP INDEX IF EXISTS idx_transactions_pending_timeout;
DROP INDEX IF EXISTS idx_transactions_pending_counters_at;

-- 删除字段注释
COMMENT ON COLUMN transactions.pending_counters_at IS NULL;

-- 删除字段
ALTER TABLE transactions 
DROP COLUMN IF EXISTS pending_counters_at; 