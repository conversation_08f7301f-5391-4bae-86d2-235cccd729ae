-- +goose Up
-- 删除 nozzle_counters 表的增量检查约束
-- 这个约束阻止了首次记录时的正常插入操作

-- 删除约束
ALTER TABLE nozzle_counters 
DROP CONSTRAINT IF EXISTS chk_increment_reasonable;

-- 验证约束已删除
-- 可以通过以下查询验证：
-- SELECT constraint_name FROM information_schema.table_constraints 
-- WHERE table_name = 'nozzle_counters' AND constraint_name = 'chk_increment_reasonable';

-- +goose Down
-- 重新添加约束（如果需要回滚）
-- ALTER TABLE nozzle_counters 
-- ADD CONSTRAINT chk_increment_reasonable 
-- CHECK (increment_value > 0 OR increment_value IS NULL); 