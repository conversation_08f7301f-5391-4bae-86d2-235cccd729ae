-- +goose Up
-- 更新transactions表的status字段约束，添加missing pending_counters状态

-- 删除现有的约束（使用正确的约束名称）
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_transaction_status;

-- 添加新的状态约束，包含所有状态（包括缺失的pending_counters）
ALTER TABLE transactions 
ADD CONSTRAINT chk_transaction_status CHECK (
    status::text = ANY (ARRAY[
        'pending'::character varying,
        'started'::character varying, 
        'active'::character varying,
        'completed'::character varying,
        'cancelled'::character varying,
        'failed'::character varying,
        'refunded'::character varying,
        'initiated'::character varying,
        'filling'::character varying,
        'pending_counters'::character varying  -- 新增：这是缺失的状态
    ]::text[])
);

-- 为新状态创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_transactions_status_lifecycle ON transactions(status) 
WHERE status IN ('initiated', 'filling', 'pending_counters');

-- +goose Down
-- 回滚操作

-- 删除新增的索引
DROP INDEX IF EXISTS idx_transactions_status_lifecycle;

-- 恢复原始状态约束（原来的约束，不包含pending_counters）
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_transaction_status;
ALTER TABLE transactions 
ADD CONSTRAINT chk_transaction_status CHECK (
    status::text = ANY (ARRAY[
        'pending'::character varying,
        'started'::character varying, 
        'active'::character varying,
        'completed'::character varying,
        'cancelled'::character varying,
        'failed'::character varying,
        'refunded'::character varying,
        'initiated'::character varying,
        'filling'::character varying
    ]::text[])
); 