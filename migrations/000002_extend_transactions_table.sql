-- +goose Up
-- 扩展transactions表以支持直接持久化方案

-- 添加生命周期时间戳字段
ALTER TABLE transactions 
ADD COLUMN initiated_at TIMESTAMP NULL COMMENT 'DC3 Nozzle Out创建时间',
ADD COLUMN filling_at TIMESTAMP NULL COMMENT 'DC1 FILLING开始时间';

-- 添加泵码来源跟踪字段
ALTER TABLE transactions 
ADD COLUMN counter_update_window BIGINT DEFAULT 30000000000 COMMENT '允许泵码更新的时间窗口(纳秒，默认30秒)',
ADD COLUMN data_source VARCHAR(50) DEFAULT 'direct_persistence' COMMENT '数据来源(direct_persistence/legacy_assembler)',
ADD COLUMN created_by VARCHAR(100) COMMENT '创建者(DC3_nozzle_out/manual/import)';

-- 添加状态转换历史字段
ALTER TABLE transactions 
ADD COLUMN state_history JSON COMMENT '状态转换历史';

-- 添加新的交易状态支持
ALTER TABLE transactions 
MODIFY COLUMN status VARCHAR(50) NOT NULL CHECK (
    status IN (
        'pending', 'started', 'active', 'completed', 'cancelled', 'failed', 'refunded',
        'initiated', 'filling'  -- 新增状态
    )
);

-- 创建索引以提高查询性能
CREATE INDEX idx_transactions_initiated_at ON transactions(initiated_at);
CREATE INDEX idx_transactions_filling_at ON transactions(filling_at);
CREATE INDEX idx_transactions_data_source ON transactions(data_source);
CREATE INDEX idx_transactions_created_by ON transactions(created_by);

-- 创建复合索引用于生命周期查询
CREATE INDEX idx_transactions_lifecycle ON transactions(device_id, status, initiated_at, completed_at);

-- +goose Down
-- 回滚操作

-- 删除索引
DROP INDEX IF EXISTS idx_transactions_initiated_at;
DROP INDEX IF EXISTS idx_transactions_filling_at;
DROP INDEX IF EXISTS idx_transactions_data_source;
DROP INDEX IF EXISTS idx_transactions_created_by;
DROP INDEX IF EXISTS idx_transactions_lifecycle;

-- 恢复原始状态检查约束
ALTER TABLE transactions 
MODIFY COLUMN status VARCHAR(50) NOT NULL CHECK (
    status IN (
        'pending', 'started', 'active', 'completed', 'cancelled', 'failed', 'refunded'
    )
);

-- 删除添加的字段
ALTER TABLE transactions 
DROP COLUMN state_history,
DROP COLUMN created_by,
DROP COLUMN data_source,
DROP COLUMN counter_update_window,
DROP COLUMN filling_at,
DROP COLUMN initiated_at; 