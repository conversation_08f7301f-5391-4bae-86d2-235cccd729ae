-- +goose Up
-- 智能完成机制：添加awaiting_final_dc2_at和smart_waiting_at字段到transactions表

-- 添加awaiting_final_dc2_at字段，用于处理交易等待最终DC2数据的状态
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS awaiting_final_dc2_at TIMESTAMP;

-- 添加smart_waiting_at字段，用于处理交易智能等待DC101的状态
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS smart_waiting_at TIMESTAMP;

-- 为字段添加注释（PostgreSQL语法）
COMMENT ON COLUMN transactions.awaiting_final_dc2_at IS 'DC1 FILLING_COMPLETED进入awaiting_final_dc2状态时间，用于500ms超时检查';
COMMENT ON COLUMN transactions.smart_waiting_at IS '收到最终DC2数据进入smart_waiting状态时间，用于200ms超时检查';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_transactions_awaiting_final_dc2_at ON transactions(awaiting_final_dc2_at);
CREATE INDEX IF NOT EXISTS idx_transactions_smart_waiting_at ON transactions(smart_waiting_at);

-- 创建复合索引用于超时查询（PostgreSQL中的条件索引）
CREATE INDEX IF NOT EXISTS idx_transactions_awaiting_final_dc2_timeout ON transactions(status, awaiting_final_dc2_at) 
WHERE awaiting_final_dc2_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_transactions_smart_waiting_timeout ON transactions(status, smart_waiting_at) 
WHERE smart_waiting_at IS NOT NULL;

-- 更新status字段约束，添加智能完成机制的新状态
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_transaction_status;
ALTER TABLE transactions 
ADD CONSTRAINT chk_transaction_status CHECK (
    status::text = ANY (ARRAY[
        'pending'::character varying,
        'started'::character varying, 
        'active'::character varying,
        'completed'::character varying,
        'cancelled'::character varying,
        'failed'::character varying,
        'refunded'::character varying,
        'initiated'::character varying,
        'filling'::character varying,
        'pending_counters'::character varying,
        'awaiting_final_dc2'::character varying,  -- 新增：智能完成机制状态
        'smart_waiting'::character varying        -- 新增：智能完成机制状态
    ]::text[])
);

-- 为新状态创建专用索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_transactions_smart_completion_states ON transactions(status) 
WHERE status IN ('awaiting_final_dc2', 'smart_waiting');

-- +goose Down
-- 回滚操作

-- 删除新增的索引
DROP INDEX IF EXISTS idx_transactions_smart_completion_states;
DROP INDEX IF EXISTS idx_transactions_smart_waiting_timeout;
DROP INDEX IF EXISTS idx_transactions_awaiting_final_dc2_timeout;
DROP INDEX IF EXISTS idx_transactions_smart_waiting_at;
DROP INDEX IF EXISTS idx_transactions_awaiting_final_dc2_at;

-- 恢复原始状态约束（不包含智能完成机制状态）
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_transaction_status;
ALTER TABLE transactions 
ADD CONSTRAINT chk_transaction_status CHECK (
    status::text = ANY (ARRAY[
        'pending'::character varying,
        'started'::character varying, 
        'active'::character varying,
        'completed'::character varying,
        'cancelled'::character varying,
        'failed'::character varying,
        'refunded'::character varying,
        'initiated'::character varying,
        'filling'::character varying,
        'pending_counters'::character varying
    ]::text[])
);

-- 删除字段注释
COMMENT ON COLUMN transactions.smart_waiting_at IS NULL;
COMMENT ON COLUMN transactions.awaiting_final_dc2_at IS NULL;

-- 删除字段
ALTER TABLE transactions 
DROP COLUMN IF EXISTS smart_waiting_at;

ALTER TABLE transactions 
DROP COLUMN IF EXISTS awaiting_final_dc2_at; 