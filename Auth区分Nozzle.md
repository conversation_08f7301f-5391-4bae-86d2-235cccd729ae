# CD2配置喷嘴与DC1授权状态喷嘴获取解决方案

## 🔍 问题分析

### 原始问题
根据Wayne DART协议文档分析，存在以下核心问题：

**时序问题**：
```
1. CD2 [nozzle_3] → 配置只允许喷嘴3
2. CD1 AUTHORIZE → 授权设备  
3. DC1 AUTHORIZED → 设备响应已授权 (状态码0x02)
4. ❌ 此时还没有DC3事务（客户未拔枪）
5. ✅ 但我们需要知道是喷嘴3被授权了
```

### Wayne DART协议限制
基于 `/wayne` 目录的官方协议文档：

1. **DC1只返回设备级状态**：`DC1 [TRANS:01H][LNG:01H][STATUS:02H]`
2. **DC3包含喷嘴信息**：`DC3 [TRANS:03H][LNG:04H][PRI:3bytes][NOZIO:1byte]`
3. **但DC3只在客户拔枪时发送**：`NOZIO bit 4: 拔出状态 (0=插入, 1=拔出)`

### CD2命令的作用
```
CD2 - Allowed nozzle numbers
格式: [TRANS:02H][LNG:N][NOZ1][NOZ2]...[NOZN] 
用途: 设置允许使用的喷嘴号 (1-0FH)
说明: 用于多喷嘴泵的权限控制，通常在授权前发送
```

## 💡 解决方案设计

### 🎯 核心思路：主机端缓存CD2配置信息

既然是我们主机发送的CD2命令配置了允许的喷嘴，我们就应该在主机端记录这个信息。

### 📋 实现步骤

#### 步骤1：扩展DevicePoller结构体
```go
type DevicePoller struct {
    // ... 现有字段 ...
    
    // 🎯 CD2喷嘴配置缓存 - 解决DC1 AUTHORIZED状态无法获取喷嘴信息的问题
    lastConfiguredNozzles []byte       // 最后配置的喷嘴列表（CD2命令）
    configuredAt          time.Time    // 配置时间
    configuredMutex       sync.RWMutex // 并发保护
}
```

#### 步骤2：在CD2发送时缓存配置信息
```go
func (p *DevicePoller) ConfigureNozzles(nozzles []byte) error {
    // ... 构建和发送CD2命令 ...
    
    // 🎯 解决方案：缓存CD2配置的喷嘴信息
    p.cacheConfiguredNozzles(nozzles)
    
    // ... 发送命令 ...
}

func (p *DevicePoller) cacheConfiguredNozzles(nozzles []byte) {
    // 验证喷嘴编号 (1-15)
    validNozzles := filterValidNozzles(nozzles)
    
    // 缓存配置信息
    p.configuredMutex.Lock()
    p.lastConfiguredNozzles = make([]byte, len(validNozzles))
    copy(p.lastConfiguredNozzles, validNozzles)
    p.configuredAt = time.Now()
    p.configuredMutex.Unlock()
}
```

#### 步骤3：提供获取授权喷嘴的接口
```go
func (p *DevicePoller) GetAuthorizedNozzles() ([]byte, error) {
    // 检查设备是否处于授权状态
    deviceState := p.deviceSM.GetStateData()
    if deviceState.PumpStatus != 0x02 { // AUTHORIZED状态
        return nil, fmt.Errorf("设备不在授权状态")
    }
    
    // 🎯 从缓存中获取最后配置的喷嘴
    p.configuredMutex.RLock()
    defer p.configuredMutex.RUnlock()
    
    if len(p.lastConfiguredNozzles) == 0 {
        return nil, fmt.Errorf("未找到CD2配置的喷嘴信息")
    }
    
    // 返回配置喷嘴的副本
    result := make([]byte, len(p.lastConfiguredNozzles))
    copy(result, p.lastConfiguredNozzles)
    return result, nil
}

func (p *DevicePoller) GetSingleAuthorizedNozzle() (byte, error) {
    nozzles, err := p.GetAuthorizedNozzles()
    if err != nil {
        return 0, err
    }
    
    if len(nozzles) == 1 {
        return nozzles[0], nil // 最常见情况：单喷嘴授权
    }
    
    // 多喷嘴配置时返回第一个
    return nozzles[0], nil
}
```

## 🚀 实际应用场景

### 场景1：单喷嘴精确授权
```go
// 前端请求授权喷嘴3
devicePoller.EnableOneNozzle(3)     // CD2 [03H]
devicePoller.AuthorizeDevice(empID) // CD1 AUTHORIZE
// 设备响应: DC1 AUTHORIZED

// 🎯 现在可以立即获取授权喷嘴信息
authorizedNozzle, err := devicePoller.GetSingleAuthorizedNozzle()
// 返回: 3
```

### 场景2：多喷嘴选择限制
```go
// 配置允许喷嘴1,3,5
devicePoller.ConfigureNozzles([]byte{1, 3, 5}) // CD2 [01H, 03H, 05H]
devicePoller.AuthorizeDevice(empID)             // CD1 AUTHORIZE
// 设备响应: DC1 AUTHORIZED

// 🎯 获取所有授权喷嘴
authorizedNozzles, err := devicePoller.GetAuthorizedNozzles()
// 返回: [1, 3, 5]
```

### 场景3：配合现有授权流程
```go
func (p *DevicePoller) AuthorizeDevice(employeeID string, nozzleNumber ...byte) error {
    // 如果指定了喷嘴编号，先发送喷嘴配置命令
    if len(nozzleNumber) > 0 && nozzleNumber[0] > 0 {
        nozzleConfig := []byte{nozzleNumber[0]}
        p.ConfigureNozzles(nozzleConfig) // 🎯 自动缓存配置
    }
    
    // 发送授权命令
    return p.SendAuthorizeCommand()
}
```

## ✅ 解决方案验证

### 时序验证
```
主机 → 设备: CD2 [03H]           (配置喷嘴3)
设备 → 主机: ACK               (确认)
主机缓存: lastConfiguredNozzles = [3]

主机 → 设备: CD1 AUTHORIZE      (授权)
设备 → 主机: DC1 AUTHORIZED     (授权成功)

主机查询: GetAuthorizedNozzles()
返回结果: [3] ✅ 问题解决！
```

### 协议合规性
✅ **遵循Wayne DART协议**：不改变协议本身的行为
✅ **主机端解决**：通过缓存自己发送的CD2信息来解决
✅ **时序正确**：在DC3事务发送前就能获取喷嘴信息
✅ **线程安全**：使用sync.RWMutex保护并发访问

## 🎯 核心优势

1. **解决时序问题**：在客户拔枪前就知道哪个喷嘴被授权
2. **协议兼容**：不违反Wayne DART协议规范
3. **简单可靠**：基于主机自己的配置记录，无需猜测
4. **向后兼容**：不影响现有的DC3事务处理逻辑
5. **多场景支持**：同时支持单喷嘴和多喷嘴授权

## 📊 应用效果

| 时间点 | 原方案 | 新方案 |
|--------|--------|--------|
| CD2发送后 | ❌ 不知道配置了什么 | ✅ 缓存配置信息 |
| DC1 AUTHORIZED | ❌ 无法获取喷嘴信息 | ✅ 从缓存获取喷嘴 |
| 客户拔枪前 | ❌ 必须等DC3事务 | ✅ 立即知道授权喷嘴 |
| 客户拔枪后 | ✅ DC3提供喷嘴信息 | ✅ 双重确认机制 |

**结论**：完美解决了"DC1 auth状态无法获取到是哪个nozzle"的问题！ 